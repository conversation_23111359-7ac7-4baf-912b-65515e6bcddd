package com.tencent.wework;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.tencent.wework.model.WeComChatMessageDTO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.RsaUtil;
import net.summerfarm.common.util.StringUtils;

import java.security.ProviderException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 企业微信会话sdk, 用于获取企业微信聊天记录
 */
@Slf4j
public class FinanceClient {

    private static final int TIMEOUT = 1000;

    private long sdk;
    private String privateKey;


    /**
     * 创建一个新的会话sdk
     *
     * @param corpid     企业id
     * @param secret     企业密钥
     * @param privateKey 私钥
     * @return 会话sdk
     */
    public static FinanceClient newInstance(String corpid, String secret, String privateKey) {
        FinanceClient client = new FinanceClient();
        client.sdk = Finance.NewSdk();
        client.privateKey = privateKey;

        int ret = Finance.Init(client.sdk, corpid, secret);
        if (ret != 0) {
            throw new ProviderException("初始化企业微信会话sdk失败");
        }
        return client;
    }


    /**
     * 获取聊天记录列表
     *
     * @param seq   查询偏移量
     * @param limit 查询条数, 最大值1000, 超过1000条会返回错误
     * @return 聊天记录列表
     */
    public List<WeComChatMessageDTO> getChatMessageList(long seq, long limit) {
        List<ChatData> chatDataList = getChatDataList(seq, limit);

        return chatDataList.stream().map(chatData -> {
            log.info("chatData:【{}】", chatData);
            String chatMsg = decryptChatData(chatData.getEncryptRandomKey(), chatData.getEncryptChatMsg());
            if (StringUtils.isBlank(chatMsg)) {
                return null;
            }

            WeComChatMessageDTO weComChatMessageDTO = JSONObject.parseObject(chatMsg, WeComChatMessageDTO.class);
            weComChatMessageDTO.setSeq(chatData.getSeq());
            return weComChatMessageDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 使用sdk获取加密的聊天数据
     *
     * @param seq   从指定的seq开始拉取消息，注意的是返回的消息从seq+1开始返回，seq为之前接口返回的最大seq值。首次使用请使用seq:0
     * @param limit 一次拉取的消息条数，最大值1000条，超过1000条会返回错误
     * @return 聊天数据列表
     */
    private List<ChatData> getChatDataList(long seq, long limit) {
        long slice = Finance.NewSlice();
        int ret = Finance.GetChatData(sdk, seq, limit, null, null, TIMEOUT, slice);
        if (ret != 0) {
            log.error("获取企业微信聊天记录失败，ret：【{}】", ret);
            Finance.FreeSlice(slice);
            return Collections.emptyList();
        }

        String contentFromSlice = Finance.GetContentFromSlice(slice);
        Finance.FreeSlice(slice);
        ChatDataResult chatDataResult = JSON.parseObject(contentFromSlice, ChatDataResult.class);

        if (chatDataResult.isSuccess()) {
            return chatDataResult.getChatDataList();
        } else {
            log.error("获取企业微信聊天记录失败，errCode：【{}】, errMsg：【{}】", chatDataResult.getErrCode(), chatDataResult.getErrMsg());
            return Collections.emptyList();
        }
    }


    private String decryptChatData(String encryptRandomKey, String encryptChatMsg) {
        try {
            String encryptKey = RsaUtil.decrypt(encryptRandomKey, privateKey);
            long slice = Finance.NewSlice();
            int ret = Finance.DecryptData(sdk, encryptKey, encryptChatMsg, slice);
            if (ret != 0) {
                log.info("解析企业微信聊天记录失败,ret:【{}】", ret);
                Finance.FreeSlice(slice);
                return "";
            }
            String text = Finance.GetContentFromSlice(slice);
            Finance.FreeSlice(slice);

            return text;
        } catch (Exception e) {
            log.error("解析企业微信聊天记录失败", e);
            return "";
        }
    }


    @Data
    static class ChatData {
        private Long seq;

        @JSONField(name = "msgid")
        private String msgId;

        @JSONField(name = "publickey_ver")
        private String publicKeyVer;

        @JSONField(name = "encrypt_random_key")
        private String encryptRandomKey;

        @JSONField(name = "encrypt_chat_msg")
        private String encryptChatMsg;
    }

    @Setter
    @Getter
    static class ChatDataResult {

        private final Integer SUCCESS_CODE = 0;

        @JSONField(name = "errcode")
        private Integer errCode;

        @JSONField(name = "errmsg")
        private String errMsg;

        public boolean isSuccess() {
            return SUCCESS_CODE.equals(errCode);
        }

        @JSONField(name = "chatdata")
        private List<ChatData> chatDataList;
    }
}
