package com.tencent.wework.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 媒体文件扩展名枚举
 *
 * @link <a href="https://developer.work.weixin.qq.com/document/path/91774#%E8%AF%AD%E9%9F%B3">...</a>
 */
@Getter
@AllArgsConstructor
public enum MediaExtEnum {

    GIF("emotion", 1, "gif"),
    PNG("emotion", 2, "png"),
    JPG("image", null, "jpg"),
    AMR("voice", null, "amr"),
    MP4("video", null, "mp4"),

    ;

    private final String msgType;
    private final Integer extType;
    private final String fileExt;

    public static String getFileExt(String msgType, Integer extType) {
        for (MediaExtEnum value : MediaExtEnum.values()) {
            if (value.getMsgType().equals(msgType) && Objects.equals(value.getExtType(), extType)) {
                return value.getFileExt();
            }
        }
        return null;
    }
}
