package com.tencent.wework.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.tencent.wework.model.body.MixedTypeBody;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

@Data
@Slf4j
public class WeComChatMessageDTO implements Serializable {

    private Long seq;

    /**
     * 消息动作，目前有send(发送消息)/recall(撤回消息)/switch(切换企业日志)三种类型。String类型
     */
    @JSONField(ordinal = 1, name = "action")
    private String action;

    /**
     * 消息id，消息的唯一标识，企业可以使用此字段进行消息去重。String类型
     */
    @JSONField(ordinal = 2, name = "msgid")
    private String msgId;

    /**
     * 消息类型。String类型
     */
    @JSONField(ordinal = 3, name = "msgtype")
    private String msgType;

    /**
     * 消息发送时间戳，utc时间，ms单位。
     */
    @JSONField(ordinal = 4, name = "msgtime")
    private Long msgTime;

    /**
     * 消息发送方id。同一企业内容为userid，非相同企业为external_userid。消息如果是机器人发出，也为external_userid。String类型
     */
    @JSONField(ordinal = 20, name = "from")
    private String from;

    /**
     * 消息接收方列表，可能是多个，同一个企业内容为userid，非相同企业为external_userid。数组，内容为string类型
     */
    @JSONField(ordinal = 20, name = "tolist")
    private List<String> toList;

    /**
     * 群聊消息的群id。如果是单聊则为空。String类型
     */
    @JSONField(ordinal = 20, name = "roomid")
    private String roomId;

    /**
     * 媒体文件上传至oss后的链接
     * 混合消息可能有多个媒体文件
     */
    private List<String> mediaUrlsOnOss;


    /**
     * ============ 消息类型 ============
     *
     * @link <a href="https://developer.work.weixin.qq.com/document/path/91774#%E8%AF%AD%E9%9F%B3">...</a>
     */
    @JSONField(ordinal = 10, name = "body",
            alternateNames = {
                    "text", "image", "revoke", "agree", "disagree", "voice", "video", "card",
                    "location", "emotion", "file", "link", "weapp", "chatrecord", "todo", "vote",
                    "collect", "redpacket", "meeting", "doc", "info", "calendar", "mixed", "voiptext",
                    "sphfeed", "external_redpacket", "content", "meeting_voice_call", "voip_doc_share"})
    private JSONObject body;

    @JSONField(ordinal = 10, name = "mixed")
    private MixedTypeBody mixedBody;

}
