package com.tencent.wework.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class MediaFileDTO {

    @JSONField(name = "type")
    private Integer type;

    @JSONField(name = "msgid")
    private String msgId;

    @J<PERSON><PERSON>ield(name = "sdkfileid")
    private String sdkFileId;

    @JSONField(name = "md5sum")
    private String md5sum;

    @JSONField(name = "fileext")
    private String fileExt;

    @JSONField(name = "filesize", alternateNames = {"imagesize", "voice_size"})
    private Long fileSize;
}
