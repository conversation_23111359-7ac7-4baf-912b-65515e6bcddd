package com.tencent.wework;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.ProviderException;

/**
 * 企微会话内容里的文件处理, 使用sdk下载文件并上传至oss
 */
@Slf4j
public class FinanceFileClient {

    private long sdk;


    /**
     * 获取FinanceFileClient对象
     *
     * @param corpId 企业id
     * @param secret 企业secret
     * @return FinanceFileClient对象
     */
    public static FinanceFileClient newInstance(String corpId, String secret) {
        long sdk = Finance.NewSdk();
        int ret = Finance.Init(sdk, corpId, secret);
        if (ret != 0) {
            throw new ProviderException("初始化企业微信会话sdk失败");
        }

        FinanceFileClient client = new FinanceFileClient();
        client.sdk = sdk;

        return client;
    }


    /**
     * 下载媒体文件
     *
     * @param sdkField sdkfileid
     * @param fileSize 文件大小
     * @param file     文件路径
     */
    public boolean downloadMediaFile(String sdkField, long fileSize, String file) {
        log.info("下载媒体文件 size: {}, {}, {}", fileSize, sdkField, file);

        File fs = new File(file);

        // 确保父目录存在
        File parentDir = fs.getParentFile();
        if (!parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                log.error("创建父目录失败, {}", file);
                return false;
            }
        }

        // 判断本地是否存在文件并且文件大小与远程一致,防止多次下载
        if (fs.exists() && fs.length() == fileSize) {
            log.debug("媒体文件本地已存在 {},{}", file, sdkField);
            return true;
        }

        // 下载
        String buf = "";
        long index = 0;
        int retry = 0;
        while (retry < 3) {
            long mediaData = Finance.NewMediaData();
            int ret = Finance.GetMediaData(sdk, buf, sdkField, "", "", 60, mediaData);
            if (ret != 0) {
                log.error("获取媒体文件异常, {}", ret);
                Finance.FreeMediaData(mediaData);
                retry++;
                continue;
            }
            log.info("media_data_long: {}, len: {}, data_len: {}, is_finish: {}", index,
                    Finance.GetIndexLen(mediaData), Finance.GetDataLen(mediaData), Finance.IsMediaDataFinish(mediaData));

            try (FileOutputStream outputStream = new FileOutputStream(file, true)) {
                outputStream.write(Finance.GetData(mediaData));
            } catch (IOException e) {
                log.error("写本地文件失败", e);
                Finance.FreeMediaData(mediaData);
                retry++;
                continue;
            }

            if (Finance.IsMediaDataFinish(mediaData) == 1) {
                Finance.FreeMediaData(mediaData);
                break;
            }
            index++;
            buf = Finance.GetOutIndexBuf(mediaData);
            Finance.FreeMediaData(mediaData);
        }
        log.info("[流程]媒体文件 size: {}, {}, {}", file.length(), file, sdkField);
        return true;
    }
}
