package net.summerfarm.crm.provider.converter;

import net.summerfarm.crm.client.dto.TaskDTO;
import net.summerfarm.crm.model.domain.CrmTask;

/**
 * <AUTHOR>
 * @date 2023/10/9 16:39
 */
public class CrmTaskConverter {
    public static CrmTask toTask(TaskDTO task){
        if (task == null) {
            return null;
        }
        CrmTask crmTask = new CrmTask();
        crmTask.setTaskName(task.getTaskName());
        crmTask.setType(task.getType());
        crmTask.setStartTime(task.getStartTime());
        crmTask.setEndTime(task.getEndTime());
        crmTask.setSourceId(task.getSourceId());
        crmTask.setCreator(task.getCreator());
        return crmTask;
    }
}
