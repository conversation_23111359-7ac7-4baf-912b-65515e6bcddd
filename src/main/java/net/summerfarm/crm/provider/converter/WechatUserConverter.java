package net.summerfarm.crm.provider.converter;

import lombok.Data;
import net.summerfarm.crm.client.dto.WechatUserInfoDTO;
import net.summerfarm.crm.model.domain.WechatUserInfo;

/**
 * <AUTHOR>
 * @date 2023/10/26 14:20
 */
public class WechatUserConverter {
    public static WechatUserInfoDTO wechatUserConverter(WechatUserInfo wechatUserInfo){
        if (wechatUserInfo == null) {
            return null;
        }
        WechatUserInfoDTO wechatUserInfoDTO = new WechatUserInfoDTO();
        wechatUserInfoDTO.setId(wechatUserInfo.getId());
        wechatUserInfoDTO.setUserId(wechatUserInfo.getUserId());
        wechatUserInfoDTO.setUnionid(wechatUserInfo.getUnionid());
        wechatUserInfoDTO.setAddTime(wechatUserInfo.getAddTime());
        wechatUserInfoDTO.setStatus(wechatUserInfo.getStatus());
        wechatUserInfoDTO.setExternalUserid(wechatUserInfo.getExternalUserid());
        wechatUserInfoDTO.setAdminId(wechatUserInfo.getAdminId());
        wechatUserInfoDTO.setCreateTime(wechatUserInfo.getCreateTime());
        wechatUserInfoDTO.setUpdateTime(wechatUserInfo.getUpdateTime());
        return wechatUserInfoDTO;
    }
}
