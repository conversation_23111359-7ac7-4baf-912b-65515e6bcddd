package net.summerfarm.crm.provider.impl;

import net.summerfarm.crm.client.input.AdminTurningConfigInput;
import net.summerfarm.crm.client.provider.AdminTurningOperationProvider;
import net.summerfarm.crm.service.impl.FollowUpRelationServiceImpl;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DubboService
@Component
public class AdminTurningOperationProviderImpl implements AdminTurningOperationProvider {
    @Resource
    FollowUpRelationServiceImpl followUpRelationService;
    @Override
    public DubboResponse<Boolean> createOrUpdateAdminTurning(AdminTurningConfigInput adminTurningConfigInput) {
        return DubboResponse.getOK(followUpRelationService.updateAdminTurning(adminTurningConfigInput.getAdminId(), adminTurningConfigInput.getBdId()));
    }

}
