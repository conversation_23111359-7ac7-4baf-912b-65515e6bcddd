package net.summerfarm.crm.provider.impl;

import net.summerfarm.crm.client.dto.CrmBdOrgDTO;
import net.summerfarm.crm.client.provider.BdOrgQueryProvider;
import net.summerfarm.crm.model.convert.CrmBdOrgConvert;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/12 13:47
 */
@DubboService
@Component
public class BdOrgQueryProviderImpl implements BdOrgQueryProvider {
    @Resource
    private BdAreaConfigService bdAreaConfigService;

    @Override
    public DubboResponse<List<CrmBdOrgDTO>> selectBdOrgCity(String province, String city, String area, Boolean notContainChildren) {
        List<CrmBdOrg> crmBdOrgList = bdAreaConfigService.selectOrgByCity(province, city, area, notContainChildren);
        List<CrmBdOrgDTO> crmBdOrgDTOList = CrmBdOrgConvert.convertToDTO(crmBdOrgList);
        return DubboResponse.getOK(crmBdOrgDTOList);
    }

    @Override
    public DubboResponse<List<CrmBdOrgDTO>> selectBdOrgByBdId(Integer bdId, Boolean notContainChildren) {
        List<CrmBdOrg> crmBdOrgList = bdAreaConfigService.listParentOrg(bdId, notContainChildren);
        List<CrmBdOrgDTO> crmBdOrgDTOList = CrmBdOrgConvert.convertToDTO(crmBdOrgList);
        return DubboResponse.getOK(crmBdOrgDTOList);
    }
}
