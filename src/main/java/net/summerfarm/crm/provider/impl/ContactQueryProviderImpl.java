package net.summerfarm.crm.provider.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.client.dto.ContactBdDTO;
import net.summerfarm.crm.client.dto.ContactDTO;
import net.summerfarm.crm.client.input.ContactQueryInput;
import net.summerfarm.crm.client.provider.ContactQueryProvider;
import net.summerfarm.crm.mapper.manage.ContactMapper;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.dto.ContactDto;
import net.summerfarm.crm.service.ContactService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DubboService
@Component
public class ContactQueryProviderImpl implements ContactQueryProvider {
    @Resource
    ContactMapper contactMapper;
    @Resource
    ContactService contactService;
    @Override
    public DubboResponse<ContactDTO> queryStorageCenterName(ContactQueryInput contactQueryInput) {
        Long contactId = contactQueryInput.getContactId();
        if (contactId == null) {
            return DubboResponse.getDefaultError("contactId is not null");
        }
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        if (contact != null) {
            ContactDTO contactDTO = new ContactDTO();
            BeanUtils.copyProperties(contact, contactDTO);
            return DubboResponse.getOK(contactDTO);
        }
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<ContactBdDTO> queryContactBd(ContactQueryInput contactQueryInput) {
        AjaxResult<ContactDto> contactDto = contactService.getContactDto(contactQueryInput.getContactId());
        ContactDto data = contactDto.getData();
        ContactBdDTO contactBdDTO = new ContactBdDTO();
        if (data!=null){
            contactBdDTO.setBdName(data.getBdName());
            contactBdDTO.setBdPhone(data.getBdPhone());
        }
        return DubboResponse.getOK(contactBdDTO);
    }
}
