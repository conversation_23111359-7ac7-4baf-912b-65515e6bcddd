package net.summerfarm.crm.provider.impl;

import cn.hutool.core.util.StrUtil;
import net.summerfarm.crm.client.dto.WechatUserInfoDTO;
import net.summerfarm.crm.client.enums.BdQrCodeQueryChannelEnum;
import net.summerfarm.crm.client.provider.WechatUserQueryProvider;
import net.summerfarm.crm.mapper.manage.ConfigMapper;
import net.summerfarm.crm.mapper.manage.WechatUserInfoMapper;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.WechatUserInfo;
import net.summerfarm.crm.provider.converter.WechatUserConverter;
import net.summerfarm.crm.service.wecom.WeComContactWayQueryService;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.ConfigValueEnum.OFFICIAL_WECHAT_USER_ID;

/**
 * <AUTHOR>
 * @date 2023/10/26 14:01
 */
@Component
@DubboService
public class WechatUserProviderImpl implements WechatUserQueryProvider {
    @Resource
    private WechatUserInfoMapper wechatUserInfoMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private WeComContactWayQueryService weComContactWayQueryService;

    @Override
    public DubboResponse<WechatUserInfoDTO> selectOfficialUserByUnionId(String unionId) {
        Config config = configMapper.selectOne(OFFICIAL_WECHAT_USER_ID.getKey());
        List<String> userIdList = StrUtil.split(config.getValue(), StrUtil.COMMA);
        List<WechatUserInfo> wechatUserInfos = wechatUserInfoMapper.selectByUserIdsAndUnionId(userIdList, unionId);
        if (wechatUserInfos.isEmpty()) {
            return DubboResponse.getOK();
        }
        WechatUserInfoDTO dto = WechatUserConverter.wechatUserConverter(wechatUserInfos.get(0));
        return DubboResponse.getOK(dto);
    }

    @Override
    public DubboResponse<List<WechatUserInfoDTO>> selectWechatUserInfoByUnionId(String unionId) {
        if (StrUtil.isEmpty(unionId)) {
            throw new ParamsException("unionId为空");
        }
        List<WechatUserInfo> wechatUserInfos = wechatUserInfoMapper.selectByUnionid(unionId);
        List<WechatUserInfoDTO> dtos = wechatUserInfos.stream()
                .map(WechatUserConverter::wechatUserConverter).collect(Collectors.toList());
        return DubboResponse.getOK(dtos);
    }

    @Override
    @Deprecated
    public DubboResponse<String> queryBdQrCodeByMid(Long mid) {
        return DubboResponse.getOK(weComContactWayQueryService.getQrCodeForMerchant(mid, BdQrCodeQueryChannelEnum.REGISTER));
    }

    @Override
    public DubboResponse<String> queryBdQrCodeByMidAndChannel(Long mid, BdQrCodeQueryChannelEnum channel) {
        return DubboResponse.getOK(weComContactWayQueryService.getQrCodeForMerchant(mid, channel));
    }
}
