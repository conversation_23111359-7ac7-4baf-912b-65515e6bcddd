package net.summerfarm.crm.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.client.dto.TaskDTO;
import net.summerfarm.crm.client.dto.TaskDetailDTO;
import net.summerfarm.crm.client.provider.CrmTaskProvider;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.mapper.manage.CrmJobCompletionCriteriaMapper;
import net.summerfarm.crm.mapper.manage.CrmJobMapper;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobCompletionCriteria;
import net.summerfarm.crm.model.dto.crmjob.UpdateJobDTO;
import net.summerfarm.crm.service.crmjob.CrmJobService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/9 16:27
 */
@DubboService
@Component
@Slf4j
public class CrmTaskProviderImpl implements CrmTaskProvider {
    //    @Resource
//    private CrmTaskService taskService;
    @Resource
    private CrmJobService crmJobService;
    @Resource
    private CrmJobMapper crmJobMapper;
    @Resource
    private CrmJobCompletionCriteriaMapper crmJobCompletionCriteriaMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DubboResponse<Void> createTask(TaskDTO task) {
        // ======== 旧版任务中心代码 ========
//        CrmTask crmTask = CrmTaskConverter.toTask(task);
//        taskService.createTask(crmTask);
        // ================================

        log.info("接收到创建发券任务请求, task: {}", task);

        CrmJob crmJob = CrmJob.builder()
                .businessType(CrmJobEnum.BusinessType.CRM.getCode())
                .jobName(task.getTaskName())
                .type(CrmJobEnum.Type.COUPON.getCode())
                .couponId(Integer.valueOf(task.getSourceId()))
                .startTime(task.getStartTime())
                .endTime(task.getEndTime())
                .creator(Long.valueOf(task.getCreator()))
                .merchantSelectionType(CrmJobEnum.MerchantSelectionType.MERCHANT_LIST.getCode())
                .build();
        crmJobMapper.insertSelective(crmJob);


        // 发券任务的任务完成条件为券被使用,值为卡券id
        CrmJobCompletionCriteria completionCriteria = new CrmJobCompletionCriteria();
        completionCriteria.setJobId(crmJob.getId());
        completionCriteria.setCompletionType(CrmJobEnum.CompletionCriteriaType.COUPON_USED.getCode());
        completionCriteria.setCompletionValue(task.getSourceId());

        crmJobCompletionCriteriaMapper.insertList(Collections.singletonList(completionCriteria));

        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Void> createTaskDetail(TaskDetailDTO taskDetail) {
        // ======== 旧版任务中心代码 ========
//        CommonResult<String> task = taskService.createTaskDetail(taskDetail);
//        if (task.getStatus().equals(ResultStatusEnum.SERVER_ERROR.getStatus())){
//            return DubboResponse.getDefaultError(task.getMsg());
//        }
        // ================================

        log.info("接收到创建发券任务明细请求, taskDetail: {}", taskDetail);

        CrmJob crmJob = crmJobMapper.selectOneByCouponId(Integer.valueOf(taskDetail.getSourceId()));
        if (crmJob == null) {
            return DubboResponse.getDefaultError(String.format("发券任务不存在.优惠券id: %s", taskDetail.getSourceId()));
        }

        UpdateJobDTO updateJobDTO = new UpdateJobDTO();
        updateJobDTO.setJobId(crmJob.getId());
        updateJobDTO.setMIdList(taskDetail.getMIds().stream().map(Long::valueOf).collect(Collectors.toList()));

        crmJobService.updateJob(updateJobDTO);

        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<Void> cancelTask(String sourceId) {

        log.info("接收到取消发券任务请求, sourceId: {}", sourceId);

        CrmJob crmJob = crmJobMapper.selectOneByCouponId(Integer.valueOf(sourceId));
        if (crmJob == null) {
            return DubboResponse.getOK();
        }
        crmJobService.batchCancelJobs(Collections.singletonList(crmJob.getId()));

        return DubboResponse.getOK();
    }
}
