package net.summerfarm.crm.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.crm.client.dto.CrmBdInfoDTO;
import net.summerfarm.crm.client.dto.CrmBdOrgDTO;
import net.summerfarm.crm.client.input.BdInfoQueryInput;
import net.summerfarm.crm.client.provider.BdInfoQueryProvider;
import net.summerfarm.crm.client.provider.BdOrgQueryProvider;
import net.summerfarm.crm.model.convert.CrmBdOrgConvert;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.FollowUpRelationService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/12 13:47
 */
@DubboService
@Component
public class BdInfoQueryProviderImpl implements BdInfoQueryProvider {

    @Resource
    private FollowUpRelationService followUpRelationService;

    @Override
    public DubboResponse<List<CrmBdInfoDTO>> selectBdInfoByMidAndAreaNos(@Valid List<BdInfoQueryInput> list) {
        if(CollectionUtil.isEmpty(list)) {
            return DubboResponse.getError("PARAMS-DEFAULT_ERROR", "请求参数为空");
        }
        if(list.size() > 200) {
            return DubboResponse.getError("PARAMS-DEFAULT_ERROR", "请求数据量过大，批次数量最大为200");
        }
        return DubboResponse.getOK(followUpRelationService.selectBdInfoByMidAndAreaNos(list));
    }
}
