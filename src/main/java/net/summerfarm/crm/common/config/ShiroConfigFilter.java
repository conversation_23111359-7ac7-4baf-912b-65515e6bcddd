package net.summerfarm.crm.common.config;

import net.xianmu.authentication.shiro.filter.PermissionFilter;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年05月09日
 */
@Configuration
public class ShiroConfigFilter {

    /**
     * ShiroFilter是整个Shiro的入口点，用于拦截需要安全控制的请求进行处理
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);
        shiroFilter.setLoginUrl("/summerfarm/home.html#/login");
        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/ok", "anon");
        //心跳检测
        filterMap.put("/crm-service/ok", "anon");

        //七鱼回调
        filterMap.put("/qiyu/**", "anon");
        filterMap.put("/crm-service/qiyu/**", "anon");

        filterMap.put("/static-resources/**", "anon");
        filterMap.put("/pages/managelogin/**", "anon");
        filterMap.put("/pages/management/**", "authc");
        filterMap.put("/admin/dingTalkSync", "anon");
        filterMap.put("/plugins/**", "anon");
        filterMap.put("/bundle/**", "anon");
        filterMap.put("/static/**", "anon");
        filterMap.put("/themes/**", "anon");
        filterMap.put("/common/**", "anon");
        filterMap.put("/v2/api-docs", "anon");
        filterMap.put("/index.html", "anon");
        filterMap.put("/admin/send/code", "anon");
        filterMap.put("/admin/update/password", "anon");
        filterMap.put("/stock-task/transfer/download", "anon");
        filterMap.put("/messageReminder/completeDelivery/download", "anon");
        // 后门接口放过拦截
        filterMap.put("/inccddaa/**", "anon");
        filterMap.put("/swagger*", "anon");
        filterMap.put("/dingding/event/**","anon");
        //外部对接，下单服务
        filterMap.put("/api/orderService/placeOrder","anon");
        filterMap.put("/api/outerQuery/**","anon");

        filterMap.put("/crm-service/contact/query**", "anon");
        filterMap.put("/crm-service/follow-up-record/export/*/*", "anon");
        filterMap.put("/crm-service/follow-up-record/feedback/**", "anon");
        filterMap.put("/crm-service/follow-up-record/feedback", "anon");


        // 企微
        filterMap.put("/crm-service/wechat/callback/customer", "anon");

        //省心送订单导出
        filterMap.put("/crm-service/crm-task/query/timing-task-download/**", "anon");


       // filterMap.put("/crm-service/merchant/query/pool/detail", "anon");

        //扫码界面
        filterMap.put("/skuBatchCod/code/**","anon");
        filterMap.put("/**", "authc");
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        Map<String, Filter> filterWonMap = new LinkedHashMap<>();
        filterWonMap.put("authc",new PermissionFilter());
        shiroFilter.setFilters(filterWonMap);
        return shiroFilter;
    }

}
