/*
package net.summerfarm.crm.common.config;

import com.alibaba.schedulerx.worker.SchedulerxWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

*/
/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 *//*

@Configuration
@Slf4j
public class SchedulerX2Config {

    @Value(value = "${spring.schedulerx2.endpoint}")
    private String endpoint;
    @Value(value = "${spring.schedulerx2.namespace}")
    private String namespace;
    @Value(value = "${spring.schedulerx2.groupId}")
    private String groupId;
    @Value(value = "${spring.schedulerx2.appKey}")
    private String appKey;

    */
/**
     * 初始化schedulerX
     * @throws Exception e
     *//*

    @Bean
    public void initSchedulerXWorker() throws Exception {
        SchedulerxWorker schedulerxWorker = new SchedulerxWorker();
        schedulerxWorker.setEndpoint(endpoint);
        schedulerxWorker.setNamespace(namespace);
        schedulerxWorker.setGroupId(groupId);
        schedulerxWorker.setAppKey(appKey);
        schedulerxWorker.init();
    }

}
*/
