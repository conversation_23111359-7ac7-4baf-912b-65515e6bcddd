package net.summerfarm.crm.common.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 客户标签配置类
 *
 * CRM配置类，使用方法如下：
 * 1. 属性名与Nacos配置文件里key保持一致
 * 2. 属性类型支持List和Map，对应Nacos里的配置类似于:
 * list[0]=item1
 * list[1]=item2
 * map.key1=value1
 * map.key2=value2
 * 3. 可以在属性对应的setter、getter方法里做类型或者结构的转换
 * 4. 建议给属性设置默认值，避免应用先于Nacos发布时获取到的属性为null，Nacos有对应配置时会覆盖掉默认值
 *
 * <AUTHOR>
 */
@Data
@Configuration
@NacosConfigurationProperties(prefix = "merchant.label", dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class MerchantLabelNacosConfig {

    /**
     * 需要展示出来的标签
     */
    private List<Integer> labelToDisplay;

    /**
     * 校区商户
     */
    private Integer school = 557;

    /**
     * 非校区商户
     */
    private Integer notSchool = 556;

    /**
     * 过年营业
     */
    private Integer openInNewYear = 559;

    /**
     * 过年不营业
     */
    private Integer closeInNewYear = 558;

    /**
     * 开业时间标签
     * key: 日期,格式为"MMdd", value: 标签id
     */
    private Map<String, Integer> openingTimeMap;

    public List<Integer> getSchoolLabels() {
        return Arrays.asList(school, notSchool);
    }

    public List<Integer> getIsOpenInNewYearLabels() {
        return Arrays.asList(openInNewYear, closeInNewYear);
    }
}
