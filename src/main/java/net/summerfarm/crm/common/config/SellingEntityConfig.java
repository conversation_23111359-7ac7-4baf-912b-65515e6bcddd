package net.summerfarm.crm.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * 销售主体配置
 * @author: zach
 * @date: 2025-03-15
 **/
@Configuration
@Data
public class SellingEntityConfig {

    @NacosValue(value = "${sellingEntity.default:杭州鲜沐科技有限公司}", autoRefreshed = true)
    private String defaultSellingEntityName;


}
