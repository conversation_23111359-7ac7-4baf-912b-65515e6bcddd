package net.summerfarm.crm.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * POP业务线配置
 */
@Configuration
@Data
@Slf4j
public class POPBLineConfig {

    /**
     * 券使用限制区域，多个区域号用逗号分隔
     */
    @NacosValue(value = "${pop.bline.restricted.areaNo.forMerchantSituationCoupon:}", autoRefreshed = true)
    private String restrictedAreaNosForMerchantSituationCoupon;

    @NacosValue(value = "${pop.bline.restricted.autoAgree.amountRatio:}", autoRefreshed = true)
    private String restrictedAutoAgreeAmountRatio;


    public List<Integer> getRestrictedAreaNosForMerchantSituationCoupon() {
        if (StringUtils.isBlank(restrictedAreaNosForMerchantSituationCoupon)) {
            return new ArrayList<>();
        }

        try {
            return Arrays.stream(restrictedAreaNosForMerchantSituationCoupon.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } catch (Throwable e) {
            log.error("获取券使用限制区域配置失败", e);
            return new ArrayList<>();
        }
    }

    public Integer getRestrictedAutoAgreeAmountRatio() {
        if (StringUtils.isBlank(restrictedAutoAgreeAmountRatio)) {
            return null;
        }

        try {
            return Integer.parseInt(restrictedAutoAgreeAmountRatio);
        } catch (Throwable e) {
            log.error("获取自动审核费用比例配置失败", e);
            return null;
        }
    }
}
