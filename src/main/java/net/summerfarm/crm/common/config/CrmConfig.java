package net.summerfarm.crm.common.config;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.dto.config.BdGrayConfigDTO;
import net.summerfarm.crm.model.dto.notice.CrmNoticeDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;

/**
 * CRM配置类，使用方法如下：
 * 1. 属性名与Nacos配置文件里key保持一致
 * 2. 属性类型支持List和Map，对应Nacos里的配置类似于:
 * list[0]=item1
 * list[1]=item2
 * map.key1=value1
 * map.key2=value2
 * 3. 可以在属性对应的setter、getter方法里做类型或者结构的转换
 * 4. 建议给属性设置默认值，避免应用先于Nacos发布时获取到的属性为null，Nacos有对应配置时会覆盖掉默认值
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Configuration
@NacosConfigurationProperties(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class CrmConfig {
    /**
     * 微信客户联系token
     */
    private String wechatToken;

    /**
     * 微信客户联系encodingAesKey
     */
    private String wechatEncodingAesKey;

    /**
     * 企业id
     */
    private String wechatReceiveId;

    /**
     * 微信标签组id
     */
    private String wechatTagGroupId;

    /**
     * 创建企微账号开关
     */
    private Boolean wecomCreateSwitch = false;

    /**
     * 已拆分城市列表
     */
    private List<String> splitCityList;

    /**
     * 新年开业状态标志
     */
    private List<Integer> newYearOperatingStatusLabels;

    /**
     * 校区客户标志
     */
    private List<Integer> campusStoreLabels;

    /**
     * 开业时间标签
     */
    private List<Integer> openingTimeLabels;

    private List<Integer> newYearBusinessTag;

    /**
     * 企微用户创建默认部门
     */
    private String department;


    /**
     * pop 默认挂属大客户id
     */
    private Integer popDefaultAdminId;

    /**
     * pop 默认运营区域
     */
    private Integer popDefaultAreaNo;

    /**
     * pop 默认运营区域名称
     */
    private String popDefaultAreaName;

    /**
     * pop销售列表
     */
    private List<Integer> popBdList = Collections.emptyList();

    /**
     * 鲜沐和pop都有的销售列表
     */
    private List<Integer> xianmuAndPopBdList = Collections.emptyList();

    /**
     * 私海客户掉落时间（时分秒）
     */
    private String privateSeaReassignTime;

    /**
     * 微信小程序通知
     */
    private String wechatNotice;

    /**
     * 允许导入拜访记录的管理员id列表
     */
    private String allowImportFollowUpRecordAdminIds = "11496,178,1050962,172,1923,1047929";

    /**
     * 客户行业属性打标的白名单（品牌维度）
     */
    private String merchantBusinessAdminIdWhiteList;

    /**
     * 销售灰度配置
     */
    private String bdGrayConfig;

    public void setNewYearOperatingStatusLabels(String newYearOperatingStatusLabels) {
        this.newYearOperatingStatusLabels = StrUtil.split(newYearOperatingStatusLabels, CharUtil.COMMA, -1, true, Integer::parseInt);
    }

    public void setCampusStoreLabels(String campusStoreLabels) {
        this.campusStoreLabels = StrUtil.split(campusStoreLabels, CharUtil.COMMA, -1, true, Integer::parseInt);
    }

    public void setOpeningTimeLabels(String openingTimeLabels) {
        this.openingTimeLabels = StrUtil.split(openingTimeLabels, CharUtil.COMMA, -1, true, Integer::parseInt);
    }

    public void setNewYearBusinessTag(String newYearBusinessTag) {
        this.newYearBusinessTag = StrUtil.split(newYearBusinessTag, CharUtil.COMMA, -1, true, Integer::parseInt);
    }

    public LocalTime getPrivateSeaReassignTime() {
        LocalTime defaultReassignTime = LocalTime.parse("09:30:00");
        try {
            if (StringUtils.isEmpty(privateSeaReassignTime)) {
                log.info("私海客户掉落时间（时分秒）为空，默认使用9点半做为掉落时间");
                return defaultReassignTime;
            }
            return LocalTime.parse(privateSeaReassignTime);
        } catch (Exception ex) {
            log.error("解析私海客户掉落时间（时分秒）失败, privateSeaReassignTime:{}", privateSeaReassignTime, ex);
            return defaultReassignTime;
        }
    }

    public List<CrmNoticeDTO> getWechatNotice() {
        try {
            if (StringUtils.isEmpty(wechatNotice)) {
                return Collections.emptyList();
            }
            return JSON.parseArray(wechatNotice, CrmNoticeDTO.class);
        } catch (Exception ex) {
            log.error("解析微信小程序通知失败, wechatNotice:{}", wechatNotice, ex);
            return Collections.emptyList();
        }
    }

    public List<Integer> getAllowImportFollowUpRecordAdminIds() {
        try {
            if (StringUtils.isEmpty(allowImportFollowUpRecordAdminIds)) {
                return Collections.emptyList();
            }
            return StrUtil.split(allowImportFollowUpRecordAdminIds, CharUtil.COMMA, -1, true, Integer::parseInt);
        } catch (Exception ex) {
            log.error("解析允许导入拜访记录的管理员id列表失败, allowImportFollowUpRecordAdminIds:{}", allowImportFollowUpRecordAdminIds, ex);
            return Collections.emptyList();
        }
    }

    public List<Integer> getMerchantBusinessAdminIdWhiteList() {
        try {
            if (StringUtils.isEmpty(merchantBusinessAdminIdWhiteList)) {
                return Collections.emptyList();
            }
            return StrUtil.split(merchantBusinessAdminIdWhiteList, CharUtil.COMMA, -1, true, Integer::parseInt);
        } catch (Exception ex) {
            log.error("解析客户行业属性打标白名单列表失败, merchantBusinessAdminIdWhiteList:{}", merchantBusinessAdminIdWhiteList, ex);
            return Collections.emptyList();
        }
    }

    public List<BdGrayConfigDTO> getBdGrayConfig() {
        try {
            if (StringUtils.isEmpty(bdGrayConfig)) {
                return Collections.emptyList();
            }
            return JSON.parseArray(bdGrayConfig, BdGrayConfigDTO.class);
        } catch (Exception ex) {
            log.error("解析销售灰度配置失败, bdGrayConfig:{}", bdGrayConfig, ex);
            return Collections.emptyList();
        }
    }

}