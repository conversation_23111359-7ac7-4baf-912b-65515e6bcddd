package net.summerfarm.crm.common.config;


import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class UpdateESConfig implements InitializingBean {
    @Value("${es.url}")
    private String url;
    @Value("${es.port}")
    private Integer port;
    @Value("${es.user-name}")
    private String name;
    @Value("${es.user-pwd}")
    private String pwd;

    @Override
    public void afterPropertiesSet() {

    }

    @Bean("updatePoolConfig")
    public GenericObjectPoolConfig updatePoolConfig() {
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMinIdle(20);
        poolConfig.setMaxTotal(20);
        poolConfig.setMaxIdle(20);
        poolConfig.setMaxWaitMillis(3000);
        poolConfig.setJmxEnabled(false);
        return poolConfig;
    }

    @Bean("updatePool")
    public GenericObjectPool<RestHighLevelClient> updatePool(
            PooledObjectFactory<RestHighLevelClient> updatePoolFactory,
            GenericObjectPoolConfig updatePoolConfig) {
        return new GenericObjectPool<>(updatePoolFactory, updatePoolConfig);
    }

    @Bean("updatePoolFactory")
    public PooledObjectFactory<RestHighLevelClient> updatePoolFactory() {
        return new BasePooledObjectFactory<RestHighLevelClient>() {

            @Override
            public void destroyObject(PooledObject<RestHighLevelClient> pooledObject) throws Exception {
                RestHighLevelClient highLevelClient = pooledObject.getObject();
                highLevelClient.close();
            }

            @Override
            public RestHighLevelClient create() throws Exception {
                RestClientBuilder restClientBuilder = RestClient.builder(new HttpHost(url, port, "http"));
                //统一设置请求头
                Header[] defaultHeaders = new Header[]{
                        new BasicHeader("header", "value")
                };
                restClientBuilder.setDefaultHeaders(defaultHeaders);

                restClientBuilder.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder.setSocketTimeout(10000));
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(name, pwd));

                restClientBuilder.setHttpClientConfigCallback(httpClientBuilder -> {
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);

                    //线程设置
                    httpClientBuilder.setDefaultIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(10).build());
                    return httpClientBuilder;
                });

                //超时时间设置
                restClientBuilder.setRequestConfigCallback(requestConfigBuilder ->
                        requestConfigBuilder
                                .setConnectTimeout(3 * 1000)
                                .setConnectionRequestTimeout(3 * 1000)
                                .setSocketTimeout(60 * 1000));
                return new RestHighLevelClient(restClientBuilder);
            }

            @Override
            public PooledObject<RestHighLevelClient> wrap(RestHighLevelClient restHighLevelClient) {
                return new DefaultPooledObject<>(restHighLevelClient);
            }
        };
    }
}
