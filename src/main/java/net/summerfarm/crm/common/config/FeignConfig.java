package net.summerfarm.crm.common.config;


import feign.Client;
import feign.Contract;
import feign.Feign;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import net.summerfarm.crm.common.interceptor.FeignCallInterceptor;
import net.summerfarm.crm.feign.decoder.FeignResultDecoder;
import okhttp3.ConnectionPool;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.support.FeignHttpClientProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description feign配置
 * @date 2022/6/17 13:11
 */
@Configuration
@ConditionalOnClass(Feign.class)
@AutoConfigureBefore(FeignAutoConfiguration.class)
public class FeignConfig {

    @Bean("feignCallInterceptor")
    public RequestInterceptor feignCallInterceptor(){
        return new FeignCallInterceptor();
    }

    @Bean
    public Decoder feignDecoder() {
        return new FeignResultDecoder();
    }

}
