package net.summerfarm.crm.common.config;

import com.github.pagehelper.PageHelper;
import net.summerfarm.crm.common.interceptor.DataPermissionInterceptor;
import net.summerfarm.crm.common.interceptor.PrepareInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @Package: net.summerfarm.common.config
 * @Description:
 * @author: <EMAIL>
 * @Date: 2019-12-24
 */
@ComponentScan
@EnableTransactionManagement
@Configuration
public class MybatisConfig {
    @Bean
    public DataPermissionInterceptor dataPermissionInterceptor() {
        return  new DataPermissionInterceptor();
    }

    @Bean
    public PageHelper pageHelper() {
        return  new PageHelper();
    }

    @Bean
    public PrepareInterceptor prepareInterceptor() {
        return  new PrepareInterceptor();
    }


}
