package net.summerfarm.crm.common.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.scripting.support.ResourceScriptSource;

/**
 * @Package: net.summerfarm.config
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/9/17
 */
@Configuration
public class RedisConfig {


    @Value("${spring.redis.host}")
    private String host;
    @Value("${spring.redis.port}")
    private String port;
    @Value("${spring.redis.password}")
    private String password;
    @Value("${spring.redis.database}")
    private Integer database;

    /**
     * redis分布式客户端，实现分布式锁，分布式读写锁，信号量等工具
     * 详见{@see https://github.com/redisson/redisson/wiki/%E7%9B%AE%E5%BD%95}
     * 注意，RedissonClient和RedisTemplate的序列化方式不一致，两个客户端的数据不能直接使用
     * @return
     */
    @Bean
    public RedissonClient redissonClient(){
        Config config = new Config();
        config.useSingleServer()
              .setAddress("redis://" + host + ":" + port)
              .setPassword(password)
              .setDatabase(database);
        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }
//
    @Bean(name = "delLockScript")
    public RedisScript<Boolean> delLockScript() {
        DefaultRedisScript<Boolean> script = new DefaultRedisScript<>();
        script.setResultType(Boolean.class);
        script.setScriptSource(new ResourceScriptSource(new ClassPathResource("redis/dellock.lua")));
        return script;
    }

    @Bean(name = "delKeysScript")
    public RedisScript<Boolean> delKeysScript() {
        DefaultRedisScript<Boolean> script = new DefaultRedisScript<>();
        script.setResultType(Boolean.class);
        script.setScriptSource(new ResourceScriptSource(new ClassPathResource("redis/delkeys.lua")));
        return script;
    }
    @Bean(name = "delKeyScript")
    public RedisScript<Boolean> delKeyScript() {
        DefaultRedisScript<Boolean> script = new DefaultRedisScript<>();
        script.setResultType(Boolean.class);
        script.setScriptSource(new ResourceScriptSource(new ClassPathResource("redis/delkey.lua")));
        return script;
    }
    /**
     * 配置RedisTemplate
     * 设置添加序列化器
     * key 使用string序列化器
     * value 使用Json序列化器
     * 还有一种简答的设置方式，改变defaultSerializer对象的实现。
     * @return
     */
    @Bean
    @Qualifier("redisTemplate")
    public RedisTemplate redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        //StringRedisTemplate的构造方法中默认设置了stringSerializer
        RedisTemplate template = new RedisTemplate<>();
        //set key serializer
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        template.setConnectionFactory(redisConnectionFactory);
        template.afterPropertiesSet();
        return template;
    }

}
