package net.summerfarm.crm.common.interceptor;

import com.github.pagehelper.sqlsource.PageDynamicSqlSource;
import net.summerfarm.common.util.ReflectUtils;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.scripting.xmltags.DynamicSqlSource;
import org.apache.ibatis.scripting.xmltags.SqlNode;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.util.*;

/**
 * @Package: net.summerfarm.common.interceptor
 * @Description: mybatis拦截器,在sql执行前生效 ，当升级到spring5.x的时候可以用AOP做,拦截Mapper/
 * @author: <EMAIL>
 * @Date: 2018/9/28
 */
@Intercepts(@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}))
public class DataPermissionInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //通过java反射获得mappedStatement属性值
        final Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        SqlSource sqlSource = mappedStatement.getSqlSource();
        if (sqlSource instanceof DynamicSqlSource || sqlSource instanceof PageDynamicSqlSource){
            List buff = new ArrayList();
            Configuration configuration = (Configuration) ReflectUtils.getFieldValue(sqlSource,"configuration");
            buff.add(configuration);
            SqlNode rootSqlNode = (SqlNode) ReflectUtils.getFieldValue(sqlSource,"rootSqlNode");
            buff.add(rootSqlNode);
            Object parameterObject = args[1];
            buff.add(parameterObject);
            Map<String,Object> map = new HashMap<>();
            map.put(mappedStatement.getId(),buff);
            CrmGlobalConstant.threadLocal.set(map);
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
