package net.summerfarm.crm.common.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description feign调用拦截器,统一添加token
 * @date 2022/6/16 21:58
 */
@Slf4j
public class FeignCallInterceptor implements RequestInterceptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(FeignCallInterceptor.class);

    @Override
    public void apply(RequestTemplate requestTemplate) {
        LOGGER.info("恭喜你,被我劫持了,让我看看你是谁:{}",requestTemplate.url());

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        //添加token
        requestTemplate.header("token", request.getHeader("token"));

    }
}
