package net.summerfarm.crm.common.base;


import com.alibaba.nacos.api.config.annotation.NacosValue;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.mapper.manage.AdminDataPermissionMapper;
import net.summerfarm.crm.mapper.manage.AdminMapper;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.pojo.DO.Admin;
import net.summerfarm.pojo.DO.AdminDataPermission;
import net.summerfarm.tms.client.dist.resp.DistOrderResp;
import net.xianmu.authentication.client.dto.ShiroUser;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.common.base
 * @Description: 基础Service
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
@Service
public class BaseService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private ApplicationContext context;
    @Resource
    private UserRoleIdConfig userRoleConfig;
    @NacosValue(value = "${updateExpensePoolRoleIds:10821}",autoRefreshed = true)
    private List<Integer> updateExpensePoolRoleIds;
    @Resource
    private AdminDataPermissionMapper adminDataPermissionMapper;
    /**
     * 获取当前登录用户对象
     *
     * @return
     */
    public Admin getCurrentUser() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return null;
        }

        Admin currentAdmin = adminMapper.select(user.getUsername());
        return currentAdmin;
    }

    /**
     * 获取当前登录用户名
     * @return
     */
    public String getLoginName() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (user == null) {
            return "";
        }
        return user.getUsername();
    }

    public Set<Integer> getAdminRoleVOs() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (user != null) {
            return new HashSet<>(user.getRoleIds());
        }
        return null;
    }

    /**
     * 获取当前登录用户id
     *
     * @return
     */
    public Integer getAdminId() {
        if (getCurrentUser() == null) {
            return null;
        }
        return this.getCurrentUser().getAdminId();
    }

    /**
     * 获取当前登录用户名
     *
     * @return
     */
    public String getAdminName() {
        ShiroUser subject = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        if (subject == null) {
            return "系统默认";
        } else {
            return getCurrentUser().getRealname();
        }

    }

    /**
     * 是否超级管理员
     *
     * @return
     */
    public boolean isSA() {
        Set<Integer> roleIds = getAdminRoleVOs();
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.SA_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否超级管理员
     *
     * @return
     */
    public boolean updateExpensePoolRoleId() {
        Set<Integer> roleIds = getAdminRoleVOs();
        for (Integer roleId : roleIds) {
            if (updateExpensePoolRoleIds.contains(roleId)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 是否是BD
     *
     * @return
     */
    public boolean isBD() {
        Set<Integer> roleIds = getAdminRoleVOs();
        if (ObjectUtils.isEmpty(roleIds)) {
            return false;
        }
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.BD_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是采购
     */
    public boolean isPurchase() {
        Set<Integer> roleIds = getAdminRoleVOs();
        if (ObjectUtils.isEmpty(roleIds)) {
            return false;
        }
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.PURCHASE_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 是否是运营
     */
    public boolean isOperate() {
        Set<Integer> roleIds = getAdminRoleVOs();
        if (ObjectUtils.isEmpty(roleIds)) {
            return false;
        }
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.OPERATE_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是大客户
     *
     * @return
     */
    public boolean isMajor() {
        Set<Integer> roleIds = getAdminRoleVOs();
        if (!CollectionUtils.isEmpty(roleIds)) {
            for (Integer roleId : roleIds) {
                if (CrmGlobalConstant.MAJOR_ROLE_ID.contains(roleId)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否是区域超管
     *
     * @return
     */
    public boolean isAreaSA() {
        Set<Integer> roleIds = getAdminRoleVOs();
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.AREASA_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 是否是城市合伙人
     *
     * @return
     */
    public boolean isParterSA() {
        Set<Integer> roleIds = getAdminRoleVOs();
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.PARTERSA_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    public boolean isM3() {
        Set<Integer> roleIds = getAdminRoleVOs();
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.M3.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否是销售主管
     *
     * @return
     */
    public boolean isSaleSA() {
        Set<Integer> roleIds = getAdminRoleVOs();
        for (Integer roleId : roleIds) {
            if (CrmGlobalConstant.SALESA_ROLE_ID.contains(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 登录用户的区域数据权限
     * @return
     */
    public Set<Integer> getDataPermission() {
        Admin currentUser = getCurrentUser();
        List<AdminDataPermission> adminDataPermissions = adminDataPermissionMapper.selectByAdminId(currentUser.getAdminId());
        Set<Integer> dataPermissions = new TreeSet();
        if (!CollectionUtils.isEmpty(adminDataPermissions)) {
            for (AdminDataPermission permission : adminDataPermissions) {
                dataPermissions.add(Integer.valueOf(permission.getPermissionValue()));
            }
        }
        return dataPermissions;
    }

    public ApplicationContext getContext() {
        return context;
    }

    public Map<Integer,String>  getBdRealNames(List<Integer> bdIds){
        if (CollectionUtils.isEmpty(bdIds)){
            return new HashMap<>();
        }
        List<Integer> queryBdIds = bdIds.stream().distinct().collect(Collectors.toList());
        List<Admin> admins = adminMapper.selectByIds(queryBdIds);
        return admins.stream().collect(Collectors.toMap(Admin::getAdminId,Admin::getRealname));
    }

    /**
     * 登录用户的区域数据权限-兼容超管和全部权限逻辑
     * @return
     */
    public List<Integer> getCompatibleDataPermission() {
        Set<Integer> dataPermission = this.getDataPermission();
        boolean isAll = dataPermission.contains(NumberUtils.INTEGER_ZERO) || isSA();
        if(isAll){
            return null;
        }else {
            return  new ArrayList<>(dataPermission);
        }
    }

    /**
     * 登录用户的区域数据权限-兼容超管和全部权限逻辑
     * @param isTask 定时任务标识, 定时任务不需要区域数据权限
     * @return
     */
    public List<Integer> getCompatibleDataPermission(boolean isTask) {
        if (isTask) {
            return null;
        }
        return this.getCompatibleDataPermission();
    }
}
