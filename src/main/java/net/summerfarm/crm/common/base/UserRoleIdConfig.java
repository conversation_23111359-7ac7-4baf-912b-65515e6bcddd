package net.summerfarm.crm.common.base;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Component
@ConfigurationProperties(prefix = "user.roleid")
@Data
public class UserRoleIdConfig {
    private Integer saRoleId;
    private Integer bdRoleId;
    private Integer purchaseRoleId;
    private Integer areasaRoleId;
    private Integer partersaRoleId;
    private Integer majorRoleId;
    private Integer operateRoleId;
    private Integer salesaRoleId;
}
