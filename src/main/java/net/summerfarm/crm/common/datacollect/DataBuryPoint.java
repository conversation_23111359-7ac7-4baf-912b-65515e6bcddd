package net.summerfarm.crm.common.datacollect;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019-11-07
 * @description
 */
public class DataBuryPoint {
    private static final Logger dbpLogger = LoggerFactory.getLogger("dbpLog");

    public static void logIntoDBP(String tab, String part, String url, String method, Long adminId, String parameter, String result) {
        //16kb以上不记录日志
        if (parameter.getBytes(StandardCharsets.UTF_8).length > 16 * 1024){
            parameter = "dataTooLarge...";
        }
        if (result.getBytes(StandardCharsets.UTF_8).length > 16 * 1024){
            result = "dataTooLarge...";
        }

        dbpLogger.info("{}●{}●{}●{}●{}●{}●{}", tab, part, url, method, adminId, parameter, result);
    }
}
