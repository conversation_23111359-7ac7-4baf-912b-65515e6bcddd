package net.summerfarm.crm.common.constant;

public interface CommonRedisKey {
    String PREFIX = "marketing-center:";

    /**
     * 订单相关prefixKey
     */
    interface BannerLock{
        String BANNER_INSERT = "lock:banner_insert:";
    }

    /**
     * 订单相关prefixKey
     */
    interface MerchantPoolLock{
        String MERCHANT_POOL_INSERT = "lock:merchant_pool_insert:";
    }


    /**
     * 分布式锁相关prefixKey
     */
    interface Lock{
        String  BATCH_SAMPLE_APPLY_INSERT = "lock:sample_apply_insert:";
    }

    /**
     * 缓存相关prefixKey
     */
    interface Cache {

        /**
         * 查询缓存版本运费规则接口缓存key
         */
        String QUERY_DELIVERY_FENCE_FOR_CACHE = PREFIX + "queryDeliveryFenceForCache:cache";

        String SHORT_TERM_LOST_SPU_KEY = PREFIX + "shortTermLostSpuCache:cache:";
    }
}
