package net.summerfarm.crm.common.constant;

import com.google.common.collect.Sets;
import net.summerfarm.common.util.PropertiesUtils;
import net.summerfarm.common.util.URLUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description 系统常用参数
 * @date 2022/6/14 2:00
 */
public class CrmGlobalConstant {
    /**
     * 超级管理员
     */
    public static final String SA = "SUPER_ADMIN";

    /**
     * 超管角色id
     */
    public static final Set<Integer> SA_ROLE_ID = Sets.newHashSet(1);

    /**
     * 茶百道大客户adminId
     */
    public static final String CBD_ADMIN_ID = "CBD_admin_id";

    /**
     * BD角色id
     */
    public static final Set<Integer> BD_ROLE_ID = Sets.newHashSet(5);

    /**
     * 采购角色id
     */
    public static final Set<Integer> PURCHASE_ROLE_ID = Sets.newHashSet(9,70,71,72,73);

    /**
     * 区域超管
     */
    public static final Set<Integer> AREASA_ROLE_ID = Sets.newHashSet(13,89,92);

    /**
     * m3
     */
    public static final Set<Integer> M3 = Sets.newHashSet(92);


    /**
     * 城市合伙人
     */
    public static Integer PARTER_SA = 12;

    /**
     * 城市合伙人角色id
     */
    public static final Set<Integer> PARTERSA_ROLE_ID = Sets.newHashSet(12);

    /**
     * 分隔符号
     */
    public static final String SEPARATING_SYMBOL = ",";

    /**
     * 大客户角色id
     */
    public static Integer BIG_ROLEID = 14;

    /**
     * 大客户角色id
     */
    public static final Set<Integer> MAJOR_ROLE_ID = Sets.newHashSet(14);

    /**
     * 运营角色id
     */
    public static final Set<Integer> OPERATE_ROLE_ID = Sets.newHashSet(4, 54, 65, 66, 67, 68, 69, 80, 81, 82, 83);

    /**
     * 销售主管角色id
     */
    public static final Set<Integer> SALESA_ROLE_ID = Sets.newHashSet(20,74);

    /**
     * 每日截单时间-1 <= orderTIme < 每日截单时间
     */
    public static final LocalTime CLOSING_ORDER_TIME = LocalTime.of(22, 00, 00);

    /**
     * 当前时间的截单开始时间
     * 若当前时间>=截单时间，return: {当前日期 }
     * 当前时间<截单时间，return：{(当前日期-1) }
     *
     * @return
     */
    public static LocalDateTime getStartTime() {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), CLOSING_ORDER_TIME);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }

    public static LocalDateTime getStartTime(LocalTime closeTime) {
        LocalDateTime closingTime = LocalDateTime.of(LocalDate.now(), closeTime);
        return LocalDateTime.now().isBefore(closingTime) ? closingTime.minusDays(1) : closingTime;
    }


    /**
     * threadLocal
     */
    public static final ThreadLocal<Map<String,Object>> threadLocal = new ThreadLocal<>();

    /**
     * 收集异常堆栈信息
     */
    public static String collectExceptionStackMsg(Exception e) {
        return e.getMessage();
    }

    /**
     * M1提成系数
     */
    public static final String COMMISSION_COEFFICIENT = "commissionCoefficient";

    /**
     * 月活目标
     */
    public static final String TARGET_MONTH_LIVING = "targetOfMonthLiving";

    /**
     * gmv目标
     */
    public static final String TARGET_GMV = "targetOfGmv";

    public static final String OLD_MONTH_MERCHANT = "月活老客户";

    public static final String NEW_MONTH_MERCHANT = "月活新客户";

    /**
     * 下载中心待上传
     */
    public static final Integer WAIT_UPLOAD = 0;

    /**
     * 七牛云成功标识
     */
    public static final String SUCCESS_FLAG = "SUCCESS";
    /**
     * 顶级域名
     */
    public static String TOP_DOMAIN_NAME;
    /**
     * 商城域名
     */
    public static String DOMAIN_NAME;
    /**
     * 初始化api
     */
    public static void apiInit() {
        DOMAIN_NAME = PropertiesUtils.getProperty("xianmu.mall.domain");

        TOP_DOMAIN_NAME = URLUtils.getDomainName(CrmGlobalConstant.DOMAIN_NAME);
    }

    /**
     * 查询线索池数据上限
     */
    public static final Integer QUERY_CLUE_POOL_SIZE = 10;

    public static final String SYSTEM_NAME = "系统";

    public static final String SYSTEM_AUDIT = "系统审核";

    /**
     * 系统adminId
     */
    public static final Integer SYSTEM_ID = 0;

    public static final String TIMEOUT_SHUTDOWN = "超时关闭";

    public static final String ADD_CITY = "请添加城市";

    /**
     * 大括号
     */
    public static final String BRACE = "{}";

    public static final String REGISTER_NO_ORDER_30_DAY = "30天注册未下单";
    public static final String REGISTER_FIRST_ORDER_30_DAY = "30天注册首单";

    public static final String CRM_COUPON_EXPENSE_POOL_PRE ="crm_coupon_expense_pool_";

    public static final String CRM_COUPON_EXPENSE_ADMIN_POOL_PRE ="crm_coupon_expense_admin_pool_";

    public static final String  BIZ_DOMAIN ="crm";

    public static final String   CRM_COUPON_EXPENSE_POOL_ENTITY_TYPE ="crm卡券申请余额池";

    public static final String   CRM_COUPON_EXPENSE_POOL_OPERATION_NAME ="余额变动";

    public static final String   CRM_COUPON_EXPENSE_POOL_BIZ_KEY_TIP ="余额变动";


}
