package net.summerfarm.crm.common.exception;

import com.alibaba.fastjson.JSON;
import feign.RetryableException;
import io.micrometer.core.instrument.util.StringUtils;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.common.datacollect.DataBuryPoint;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.common.exception.*;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.session.UnknownSessionException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * @Package: net.summerfarm.common.exceptions
 * @Description: 自定义异常处理类
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
@RestControllerAdvice
@Component
public class CustomExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomExceptionHandler.class);


    @ExceptionHandler(Exception.class)
    public ModelAndView resolveException(@NotNull HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                         Object o, @NotNull Exception e) {
        ModelAndView modelAndView = new ModelAndView();

        AjaxResult result;

        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setHeader("Content-Type", "application/json;charset=UTF-8");
        if (e instanceof ClientAbortException) {
            logger.info("【警告】message=[{}]", e.getMessage(), e);
            result = AjaxResult.getErrorWithMsg("网络不给力");
        } else if (e instanceof ParamsException) {
            ParamsException exception = (ParamsException) e;
            logger.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof BizException) {
            BizException exception = (BizException) e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof CallerException) {
            CallerException exception = (CallerException) e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        }else if (e instanceof MethodArgumentNotValidException){
            FieldError fieldError = ((MethodArgumentNotValidException) e).getBindingResult().getFieldError();
            String message = fieldError==null ? "参数校验不通过，请确认后重试" : fieldError.getDefaultMessage();
            result = AjaxResult.getError(ResultConstant.PARAM_FAULT, message);
        }
        else if (e instanceof ProviderException) {
            ProviderException exception = (ProviderException) e;
            logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof AuthorizationException) {
            //跨域设置
            httpServletResponse.addHeader("Access-Control-Allow-Origin", "*");
            httpServletResponse.addHeader("Access-Control-Allow-Methods", "POST, GET");
            httpServletResponse.addHeader("Access-Control-Allow-Headers", "*");
            httpServletResponse.addHeader("Access-Control-Allow-Credentials", "true");
            //无访问权限设置http请求status为403
            httpServletResponse.setStatus(403);
            result = AjaxResult.getError(ResultConstant.UNAUTHORIZED, "暂无该功能权限,请联系主管操作");
        } else if (e instanceof DefaultServiceException) {
            Object[] params = ((DefaultServiceException) e).getParams();
            if (((DefaultServiceException) e).getLevel() == 0) {
                logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            } else {
                logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            }
            if (Objects.isNull(params)) {
                //兼容模式如果e.getMessage()包含中文判断使用哪种构造函数
                //若e.getMessage()字节码长度等于本身长度则不包含中文
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().getBytes().length != e.getMessage().length()) {
                    result = AjaxResult.getErrorWithMsg(e.getMessage());
                } else {
                    result = AjaxResult.getError(e.getMessage());
                }
            } else {
                result = AjaxResult.getErrorWithParam(e.getMessage(), params);
            }
        } else if (e instanceof DuplicateKeyException) {
            logger.info("异常:{}", e.getMessage());
            result = AjaxResult.getErrorWithMsg("该信息已存在,请确认!");
        } else if (e instanceof RetryableException) {
            logger.info("异常:{}", e.getMessage());
            result = AjaxResult.getErrorWithMsg("管理后台升级中!该功能暂不可用");
        }else if (e instanceof UnknownSessionException){
            logger.info("登录超时,msg:{}",e.getMessage(),e);
            result = AjaxResult.getError(ResultConstant.UNAUTHORIZED,"登录超时，请重新登录");
        }
        else {
            logger.error("异常:{}", e.getMessage(),e);
            result = AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "网络不给力");
        }
        try {
            httpServletResponse.getWriter().write(JSON.toJSONString(result));
        } catch (IOException ex) {
            logger.info("与客户端通信异常，{}", ex.getMessage());
        }

        try {
            String tab = httpServletRequest.getHeader("tab");
            String part = httpServletRequest.getHeader("part");
            Long adminId = null;
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            if (user != null) {
                adminId = user.getBaseUserId();
            }

            String paraStr = "";
            //RequestBody参数暂不能解析
            if (httpServletRequest.getParameterMap().size() != 0) {
                paraStr = JSON.toJSONString(httpServletRequest.getParameterMap());
            }

            DataBuryPoint.logIntoDBP(tab, part, httpServletRequest.getRequestURI(), httpServletRequest.getMethod(), adminId, paraStr, JSON.toJSONString(result));
        } catch (UnknownSessionException ee) {
            logger.info("登录超时,msg:{}", ee.getMessage(), ee);
        } catch (Exception ee) {
            logger.error("数据埋点日志处理异常 {}", ee.getMessage(), ee);
        }

        return modelAndView;
    }

    @Override
    public DubboResponse processError(Throwable throwable, ProceedingJoinPoint joinPoint) {
        if (throwable instanceof ConsumerException) {
            ConsumerException exception = (ConsumerException) throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof ParamsException) {
            ParamsException exception = (ParamsException) throwable;
            logger.warn("调用方参数异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof BizException) {
            BizException exception = (BizException) throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof CallerException) {
            CallerException exception = (CallerException) throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (throwable instanceof DefaultServiceException) {
            DefaultServiceException exception = (DefaultServiceException) throwable;
            if (exception.getLevel() == 0) {
                logger.info("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            } else {
                logger.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            }
            return DubboResponse.getError(exception.getCode(), exception.getMessage());
        } else if (throwable instanceof ProviderException) {
            ProviderException exception = (ProviderException) throwable;
            logger.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return DubboResponse.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else {
            logger.error("提供方未知异常, 异常信息:{}", throwable.getMessage(), throwable);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return DubboResponse.getError(providerErrorCode.getCode(), throwable.getMessage());
        }
    }
}
