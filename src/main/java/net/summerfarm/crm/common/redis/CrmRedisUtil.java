package net.summerfarm.crm.common.redis;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * redis工具类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CrmRedisUtil {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    public Long increment(String key) {
        return redisTemplate.opsForValue().increment(key, 1L);
    }

    public void setNx(String key, String value, Long expireTime, TimeUnit timeUnit) {
        stringRedisTemplate.opsForValue().set(key, value, expireTime, timeUnit);
    }

    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    public void setExpire(String key, String value, Long expireTime, TimeUnit timeUnit) {
        stringRedisTemplate.opsForValue().set(key, value);
        stringRedisTemplate.expire(key, expireTime, timeUnit);
    }

    public Boolean setIfAbsent(String key, String value, Long expireTime, TimeUnit timeUnit) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value, expireTime, timeUnit);
    }

    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    public void del(String key) {
        stringRedisTemplate.delete(key);
    }

    public boolean tryLock(String key, Long expireTime, TimeUnit timeUnit, Long waitTime) {
        RLock lock = redissonClient.getLock(key);
        try {
            return lock.tryLock(waitTime, expireTime, timeUnit);
        } catch (InterruptedException e) {
            log.warn("加锁失败,key:{}", key);
            return false;
        } catch (Exception e) {
            log.warn("加锁异常,key:{}", key);
            return false;
        }
    }

    public boolean tryLockV2(String key, Long expireTime, TimeUnit timeUnit, Long waitTime) {
        RLock lock = redissonClient.getLock(key);
        try {
            boolean doLock = lock.tryLock(waitTime, expireTime, timeUnit);
            log.info("获取redission锁，key：{}，时间戳：{}，结果：{}", key, System.currentTimeMillis(), doLock);
            return doLock;
        } catch (InterruptedException e) {
            log.warn("加锁失败,key:{}", key);
            return false;
        } catch (Exception e) {
            log.warn("加锁异常,key:{}", key);
            return false;
        }
    }

    public boolean tryLockV2(String key, Long expireTime, TimeUnit timeUnit, Long waitTime, RLock lock, String uid) {
        if (null == lock) {
            return false;
        }
        try {
            boolean doLock = lock.tryLock(waitTime, expireTime, timeUnit);
            log.info("获取redission锁，key：{}，线程：{}，时间戳：{}，结果：{}，uid：{}", key, Thread.currentThread().getName(), System.currentTimeMillis(), doLock, uid);
            return doLock;
        } catch (InterruptedException e) {
            log.warn("加锁失败,key:{}", key);
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.warn("加锁异常,key:{}", key);
            return false;
        }
    }

    public void doLock(String key, Long expireTime, TimeUnit timeUnit, Long waitTime, Runnable runnable) {
        RLock lock = redissonClient.getLock(key);
        String uid = UUID.randomUUID().toString();
        boolean doLock = tryLockV2(key, expireTime, timeUnit, waitTime, lock, uid);
        try {
            if (doLock) {
                log.info("redis锁定成功:{} 线程:{}", key, Thread.currentThread().getName());
                runnable.run();
            } else {
                log.warn("redis锁定失败:{} 线程:{}", key, Thread.currentThread().getName());
            }
        } catch (net.xianmu.common.exception.BizException e) {
            log.warn("redis锁定业务方法出现业务异常", e);
            throw e;
        } catch (Exception e) {
            log.error("redis锁定业务方法出现异常", e);
            throw e;
        } finally {
            if (doLock) {
                unlock(key, lock, uid);
            }
        }
    }

    public void unlock(String key, RLock lock, String uid) {
        if (null == lock) {
            return;
        }
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放redission锁，key：{}，线程：{}，时间戳：{}，uid：{}", key, Thread.currentThread().getName(), System.currentTimeMillis(), uid);
            }
        } catch (Exception e) {
            log.warn("当前线程释放锁失败,key:{} 线程:{}", key, Thread.currentThread().getName(), e);
        }
    }

    public void unlock(String key) {
        RLock lock = redissonClient.getLock(key);
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放redission锁，key：{}，时间戳：{}", key, System.currentTimeMillis());
            }
        } catch (Exception e) {
            log.warn("当前线程释放锁失败,key:{}", key);
        }
    }


    public void execute(String key, Long expireTime, TimeUnit timeUnit, Long waitTime, Runnable runnable, net.xianmu.common.exception.error.code.ErrorCode errorCode) {
        if (!tryLock(key, expireTime, timeUnit, waitTime)) {
            throw new net.xianmu.common.exception.BizException(errorCode);
        }
        executeTask(key, runnable);
    }

    private void executeTask(String key, Runnable runnable) {
        try {
            runnable.run();
        } finally {
            unlock(key);
        }
    }

    /**
     * 只尝试一次
     *
     * @param key
     * @param expireTime
     * @param timeUnit
     * @param callable
     * @param <T>
     * @return
     */
    public <T> T executeCallableByJustTryOneTime(String key, Long expireTime, TimeUnit timeUnit, Callable callable) {
        try {
            if (!Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                    .setIfAbsent(key, "1", expireTime, timeUnit))) {
                log.info("获取锁失败，key：{}，时间戳：{}", key, System.currentTimeMillis());
                return null;
            }

            log.info("获取锁成功，key：{}，时间戳：{}", key, System.currentTimeMillis());
            return (T) callable.call();
        } catch (BizException e) {
            log.warn("executeTaskCallable BizException", e);
            throw e;
        } catch (Throwable e) {
            log.error("executeTaskCallable Exception", e);
            throw new RuntimeException(e);
        } finally {
            stringRedisTemplate.delete(key);
            log.info("删除锁成功，key：{}，时间戳：{}", key, System.currentTimeMillis());
        }
    }
}
