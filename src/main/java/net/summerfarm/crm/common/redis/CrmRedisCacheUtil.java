package net.summerfarm.crm.common.redis;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class CrmRedisCacheUtil {

    private static final Logger logger = LoggerFactory.getLogger(CrmRedisCacheUtil.class);

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CrmRedisUtil crmRedisUtil;

    /**
     * 查询对象缓存封装
     *
     * @param key
     * @param liveTime
     * @param callable
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> String getCacheStringValueRefreshByHalfTime(String key, long liveTime, Callable<T> callable) {
        String strLockKey = key + "_lock";

        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(value)) {
            // 查询结果
            try {
                T result = callable.call();
                if (Objects.isNull(result)) {
                    return null;
                }
                value = result.toString();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            // 并发控制设置结果到缓存
            if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                    .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))){
                stringRedisTemplate.opsForValue().set(key, value);
                stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                stringRedisTemplate.delete(strLockKey);
            }
        } else {
            // 过半续约
            Long restTime = stringRedisTemplate.getExpire(key);
            if (restTime != null &&
                    restTime > 0 && (liveTime / 2) > restTime &&
                    Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                            .setIfAbsent(strLockKey, "1", liveTime, TimeUnit.SECONDS))) {
                stringRedisTemplate.opsForValue().set(key, value);
                stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                stringRedisTemplate.delete(strLockKey);
            }
        }

        return value;
    }

    /**
     * 查询对象缓存封装
     *
     * @param key
     * @param liveTime
     * @param callable
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T getCacheObjectValueRefreshByHalfTime(String key, long liveTime, Callable<T> callable, Class<T> clazz) {
        String strLockKey = key + "_lock";

        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(value)) {
            // 查询结果
            try {
                T result = callable.call();
                if (Objects.isNull(result)) {
                    return null;
                }
                value = JSONObject.toJSONString(result);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            // 并发控制设置结果到缓存
            if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                    .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))){
                stringRedisTemplate.opsForValue().set(key, value);
                stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                stringRedisTemplate.delete(strLockKey);
            }
        } else {
            // 过半续约
            Long restTime = stringRedisTemplate.getExpire(key);
            if (restTime != null &&
                    restTime > 0 && (liveTime / 2) > restTime &&
                    Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                            .setIfAbsent(strLockKey, "1", liveTime, TimeUnit.SECONDS))) {
                stringRedisTemplate.opsForValue().set(key, value);
                stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                stringRedisTemplate.delete(strLockKey);
            }
        }

        return JSONObject.parseObject(value, clazz);
    }

    /**
     * 查询缓存列表封装
     * 过半续约
     *
     * @param key
     * @param liveTime
     * @param callable
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> List<T> getCacheListValueRefreshByHalfTime(String key, long liveTime, Callable<List<T>> callable, Class<T> clazz) {
        String strLockKey = key + "_lock";

        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(value)) {
            // 查询结果
            try {
                List<T> result = callable.call();
                if (Objects.isNull(result)) {
                    return null;
                }
                value = JSONObject.toJSONString(result);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            // 并发控制设置结果到缓存
            if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                    .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))) {
                stringRedisTemplate.opsForValue().set(key, value);
                stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                stringRedisTemplate.delete(strLockKey);
            }
        } else {
            // 过半续约
            Long restTime = stringRedisTemplate.getExpire(key);
            if (restTime > 0 && (liveTime / 2) > restTime &&
                    Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                            .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))) {
                try {
                    List<T> cacheList = callable.call();
                    if (Objects.isNull(cacheList)) {
                        return null;
                    }
                    stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(cacheList));
                    stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                } catch (Exception e) {
                    throw new RuntimeException("Refresh Cache Error. Key: " + key, e);
                }
                stringRedisTemplate.delete(strLockKey);
            }
        }

        return JSONObject.parseArray(value, clazz);
    }

    /**
     * 查询缓存列表封装
     * 过半续约
     *
     * @param key
     * @param liveTime
     * @param callable
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T getCacheValueRefreshByHalfTime(String key, long liveTime, Callable<T> callable, Class<T> clazz) {
        String strLockKey = key + "_lock";

        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(value)) {
            // 查询结果
            try {
                T result = callable.call();
                if (Objects.isNull(result)) {
                    return null;
                }
                value = JSONObject.toJSONString(result);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            // 并发控制设置结果到缓存
            if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                    .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))) {
                stringRedisTemplate.opsForValue().set(key, value);
                stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                stringRedisTemplate.delete(strLockKey);
            }
        } else {
            // 过半续约
            Long restTime = stringRedisTemplate.getExpire(key);
            if (restTime > 0 && (liveTime / 2) > restTime &&
                    Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                            .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))) {
                try {
                    T cacheObject = callable.call();
                    if (Objects.isNull(cacheObject)) {
                        return null;
                    }
                    stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(cacheObject));
                    stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                } catch (Exception e) {
                    throw new RuntimeException("Refresh Cache Error. Key: " + key, e);
                }
                stringRedisTemplate.delete(strLockKey);
            }
        }

        return JSONObject.parseObject(value, clazz);
    }

    /**
     * 包装值缓存，每次调用刷新
     *
     * @param key
     * @param liveTime
     * @param callable
     * @param clazz
     * @param executor
     * @return
     * @param <T>
     */
    public <T> List<T> getCacheListValueRefreshEveryTime(String key,
                                                         long liveTime,
                                                         Callable<List<T>> callable,
                                                         Class<T> clazz,
                                                         ThreadPoolTaskExecutor executor) {
        String strLockKey = key + "_lock";
        String cacheKey = key;

        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(value)) {
            // 查询结果
            try {
                List<T> result = callable.call();
                if (Objects.isNull(result)) {
                    return null;
                }
                value = JSONObject.toJSONString(result);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            // 并发控制设置结果到缓存
            if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                    .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))) {
                stringRedisTemplate.opsForValue().set(key, value);
                stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                stringRedisTemplate.delete(strLockKey);
            }
        } else {
            // 异步线程处理
            try {
                executor.execute(() -> {
                    // 先异步查询并将返回值设置到缓存中
                    crmRedisUtil.executeCallableByJustTryOneTime(
                            strLockKey,
                            liveTime,
                            TimeUnit.SECONDS,
                            () -> {// 查询结果
                                String value1 = null;
                                try {
                                    List<T> result = callable.call();
                                    if (Objects.isNull(result)) {
                                        return null;
                                    }
                                    value1 = JSONObject.toJSONString(result);
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }

                                // 并发控制设置结果到缓存
                                if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                                        .setIfAbsent(strLockKey, "1", 1, TimeUnit.SECONDS))) {
                                    stringRedisTemplate.opsForValue().set(key, value1);
                                    stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
                                    stringRedisTemplate.delete(strLockKey);
                                }

                                return null;
                            }
                    );
                });

            } catch (Throwable e) {
                logger.error("异步缓存list异常", e);
            }
        }

        return JSONObject.parseArray(value, clazz);
    }

    public <T> T updateCacheObjectValue(String key,
                                        long liveTime,
                                        Callable<T> callable,
                                        Class<T> clazz) {
        // 读取结果
        String value;
        // 查询结果
        try {
            T result = callable.call();
            if (Objects.isNull(result)) {
                return null;
            }
            value = JSONObject.toJSONString(result);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        // 更新缓存
        String strLockKey = key + "_lock";
        if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue()
                        .setIfAbsent(strLockKey, "1", liveTime, TimeUnit.SECONDS))) {
            stringRedisTemplate.opsForValue().set(key, value);
            stringRedisTemplate.expire(key, liveTime, TimeUnit.SECONDS);
            stringRedisTemplate.delete(strLockKey);
        }

        return JSONObject.parseObject(value, clazz);
    }
}
