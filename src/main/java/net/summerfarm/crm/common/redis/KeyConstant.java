package net.summerfarm.crm.common.redis;

/**
 * @Package: net.summerfarm.common.redis
 * @Description: redis key collection
 * @author: <EMAIL>
 * @Date: 2018/1/22
 */
public class KeyConstant {

    /**
     * 数据采集key
     */
    public static final String SCADA = "SCADA_";

    /**
     * 微信access_token
     */
    public static final String ACCESS_TOKEN = "ACCESS_TOKEN";

    public static final String JS_API_TICKET = "JS_API_TICKET";

    /**
     * dms的Redis前缀
     */
    public static final String DMS_REDIS_PRE = "DMS_";

    /**
     * 验证码key
     */
    public static final String VERIFICATION_CODE = "VERIFICATION_CODE";


    public static final String CLOSE_EXCEL_EXCEL="CLOSE_EXCEL_EXCEL";

}
