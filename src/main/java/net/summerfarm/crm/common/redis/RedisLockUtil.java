/*
package net.summerfarm.crm.common.redis;

import io.lettuce.core.SetArgs;
import io.lettuce.core.api.async.RedisAsyncCommands;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class RedisLockUtil {

    private static final Logger logger = LoggerFactory.getLogger(RedisLockUtil.class);

    @Resource
    private RedisTemplate<String,String> redisTemplate;

    @Resource
    public StringRedisTemplate stringRedisTemplate;

    public Boolean tryLock(String key, String value, Long timeout, TimeUnit unit, Integer expireTime, Long sleepTime) {

        String execute = redisTemplate.execute((RedisCallback<String>) connection -> {
            try {
                do{
                    Object conn = connection.getNativeConnection();
                    //序列化
                    RedisSerializer<String> stringRedisSerializer = (RedisSerializer<String>) stringRedisTemplate.getKeySerializer();

                    byte[] keyByte = stringRedisSerializer.serialize(key);
                    byte[] valueByte = stringRedisSerializer.serialize(value);
                    //redis单机模式
                    if (conn instanceof RedisAsyncCommands) {
                        RedisAsyncCommands redisAsyncCommands = (RedisAsyncCommands) conn;
                        String result = redisAsyncCommands.getStatefulConnection()
                                .sync()
                                .set(keyByte, valueByte, SetArgs.Builder.nx().ex(expireTime));
                        if(Objects.equals(result,"OK")){
                            return result;
                        }
                        try {
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            return "";
                        }
                    }
                } while (true);

            } catch (Exception e) {
                logger.info("tryLock is Error"+e.getMessage());
            }
            return "";
        });
        if(Objects.equals(execute,"OK")){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    public Boolean lock(String key, String value, Long sleepTime) {
        return redisTemplate.execute(new RedisCallback<Boolean>() {
            @Override
            public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                Object conn = connection.getNativeConnection();
                if (conn instanceof RedisAsyncCommands) {
                    do {
                        RedisAsyncCommands redisAsyncCommands = (RedisAsyncCommands) conn;
                        //lettuce连接包下序列化键值
                        RedisSerializer<String> stringRedisSerializer = (RedisSerializer<String>) stringRedisTemplate.getKeySerializer();

                        byte[] keyByte = stringRedisSerializer.serialize(key);
                        byte[] valueByte = stringRedisSerializer.serialize(value);

                        String result = redisAsyncCommands.getStatefulConnection()
                                .sync()
                                .set(keyByte, valueByte);

                        if(Objects.equals("OK",result)){
                            return Boolean.TRUE;
                        }

                        try {
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            return Boolean.FALSE;
                        }
                    } while (true);
                }
                return Boolean.FALSE;
            }
        }, true);
    }

}
*/
