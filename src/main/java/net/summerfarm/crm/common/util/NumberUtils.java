package net.summerfarm.crm.common.util;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public class NumberUtils  {

    /**
     * Double类型 0.25
     */
    public static final Double DOUBLE_ZERO_POINT_TWO_FIVE = Double.valueOf(0.25d);

    /**
     * Double类型 0.6
     */
    public static final Double DOUBLE_ZERO_POINT_SIX = Double.valueOf(0.6d);

    /**
     * Double类型的0.67
     */
    public static final Double DOUBLE_ZERO_POINT_SIX_SEVEN = Double.valueOf(0.67d);

    /**
     * Double类型0.9
     */
    public static final Double DOUBLE_ZERO_POINT_NINE = Double.valueOf(0.9d);

    /**
     * Double类型1.2
     */
    public static final Double DOUBLE_ONE_POINT_TWO = Double.valueOf(1.2d);

    /**
     * Double类型1
     */
    public static final Double DOUBLE_ONE = Double.valueOf(1.0d);

    /**
     * Double类型9
     */
    public static final Double DOUBLE_NINE = Double.valueOf(9.0d);

    /**
     * Integer类型0
     */
    public static Integer INTEGER_ZERO = Integer.valueOf(0);

    /**
     * Integer类型1
     */
    public static Integer INTEGER_ONE = Integer.valueOf(1);

    /**
     * Integer类型2
     */
    public static Integer INTEGER_TWO = Integer.valueOf(2);

    /**
     * Integer类型3
     */
    public static Integer INTEGER_THREE = Integer.valueOf(3);

    /**
     * Integer类型4
     */
    public static Integer INTEGER_FOUR = Integer.valueOf(4);

    /**
     * Integer类型10
     */
    public static Integer INTEGER_TEN = Integer.valueOf(10);

    /**
     * Integer类型15
     */
    public static Integer INTEGER_FIFTEEN = Integer.valueOf(15);

    /**
     * Integer类型30
     */
    public static Integer INTEGER_THIRTY = Integer.valueOf(30);

    /**
     * Integer类型60
     */
    public static Integer INTEGER_SIXTY = Integer.valueOf(60);

    /**
     * Integer类型100
     */
    public static Integer INTEGER_ONE_HUNDRED = Integer.valueOf(100);

}
