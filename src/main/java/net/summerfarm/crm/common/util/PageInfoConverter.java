package net.summerfarm.crm.common.util;

import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

public class PageInfoConverter {
    public static <T, R> PageInfo<R> toPageResp(PageInfo<T> page, Function<? super T, ? extends R> function) {
        PageInfo resp = new PageInfo();
        resp.setPageNum(page.getPageNum());
        resp.setPageSize(page.getPageSize());
        resp.setTotal(page.getTotal());
        resp.setSize(page.getSize());
        resp.setStartRow(page.getStartRow());
        resp.setEndRow(page.getEndRow());
        resp.setPages(page.getPages());
        resp.setPrePage(page.getPrePage());
        resp.setNextPage(page.getNextPage());
        resp.setIsFirstPage(page.isIsFirstPage());
        resp.setIsLastPage(page.isIsLastPage());
        resp.setHasNextPage(page.isHasNextPage());
        resp.setHasPreviousPage(page.isHasPreviousPage());
        resp.setNavigatePages(page.getNavigatePages());
        resp.setNavigatepageNums(page.getNavigatepageNums());
        List<R> collect = page.getList().stream().map(function).collect(toList());
        resp.setList(collect);
        return resp;
    }

    public static <T, R> PageInfo<R> copyPageInfo(PageInfo<T> pageInfo) {
        if (pageInfo == null) {
            return null;
        }
        PageInfo<R> newPageInfo = new PageInfo<>();
        newPageInfo.setPageNum(pageInfo.getPageNum());
        newPageInfo.setPageSize(pageInfo.getPageSize());
        newPageInfo.setSize(pageInfo.getSize());
        newPageInfo.setTotal(pageInfo.getTotal());
        newPageInfo.setPages(pageInfo.getPages());
        return newPageInfo;
    }

}
