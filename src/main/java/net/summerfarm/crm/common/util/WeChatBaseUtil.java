package net.summerfarm.crm.common.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2023/7/31 14:41
 */
@Slf4j
public class WeChatBaseUtil {
    /**
     * 默认地址
     */
    private static String DEFAULT_BASE_URL = "https://qyapi.weixin.qq.com";
    /**
     * 获取用户详情
     */
    public static String GET_BY_USER = "/cgi-bin/externalcontact/get?access_token=";
    /**
     * 批量获取用户
     */
    public static String BATCH_GET_BY_USER = "/cgi-bin/externalcontact/batch/get_by_user?access_token=";
    /**
     * 编辑客户标签
     */
    public static String EDIT_MARK_TAG = "/cgi-bin/externalcontact/mark_tag?access_token=";

    /**
     * 获取公司标签列表
     */
    public static String GET_CORP_TAG_LIST = "/cgi-bin/externalcontact/get_corp_tag_list?access_token=";

    /**
     * 新增标签
     */
    public static String ADD_MARK_TAG = "/cgi-bin/externalcontact/add_corp_tag?access_token=";

    /**
     * 修改备注和手机号
     */
    public static String UPDATE_REMARK_PHONE = "/cgi-bin/externalcontact/remark?access_token=";

    /**
     * 企业微信推送
     */
    public static String SEND_MESSAGE = "/cgi-bin/externalcontact/add_msg_template?access_token=";

    //删除企业客户标签
    public static String DELETE_CORP_TAG = "/cgi-bin/externalcontact/del_corp_tag?access_token=";

    /**
     * 企微用户信息
     */
    public static String GET_USER="/cgi-bin/user/get?access_token=";

    /**
     * 群发记录列表
     */
    public static String GET_GROUP_MSG_LIST="/cgi-bin/externalcontact/get_groupmsg_list_v2?access_token=";

    /**
     * 获取群发任务
     */
    public static String GET_GROUP_MSG_TASK="/cgi-bin/externalcontact/get_groupmsg_task?access_token=";

    /**
     * 获取群发消息发送结果
     */
    public static String GET_GROUP_MSG_SEND_RESULT="/cgi-bin/externalcontact/get_groupmsg_send_result?access_token=";

    /**
     * 客户联系统计
     */
    public static String USER_BEHAVIOR_DATA="/cgi-bin/externalcontact/get_user_behavior_data?access_token=";

    /**
     * 用户创建
     */
    public static String USER_CREATE="/cgi-bin/user/create?access_token=";

    public static String TRANSFER_CUSTOMER="/cgi-bin/externalcontact/transfer_customer?access_token=";

    public static String RESIGNED_TRANSFER_CUSTOMER="/cgi-bin/externalcontact/resigned/transfer_customer?access_token=";

    /**
     * 配置客户联系「联系我」方式
     * <a href="https://developer.work.weixin.qq.com/document/path/92228#%E6%9D%83%E9%99%90%E8%AF%B4%E6%98%8E">...</a>
     */
    public static String ADD_CONTACT_WAY = "/cgi-bin/externalcontact/add_contact_way?access_token=";

    /**
     * 删除企业已配置的「联系我」方式
     */
    public static String DELETE_CONTACT_WAY = "/cgi-bin/externalcontact/del_contact_way?access_token=";

    public static String GET_CONTACT_WAY = "/cgi-bin/externalcontact/get_contact_way?access_token=";

    /**
     * 上传临时素材
     */
    public static String UPLOAD_MEDIA = "/cgi-bin/media/upload?access_token=";

    public static String getApiUrl(String url) {
        return DEFAULT_BASE_URL + url;
    }

    public static String post(String uri, String token, String data) {
        log.info("\n【企微请求地址】: {}\n【请求参数】：{}, token {} ,data {}", uri, data, token, data);
        String result = HttpRequest.post(uri + token).timeout(6000).body(data).execute().body();
        log.info("\n【企微请求地址】: {}\n【响应参数】：{}, token {} ,data {}", uri, result, token, data);
        return result;
    }

    public static String get(String uri, String token, Map<String,Object> data) {
        log.info("\n【企微请求地址】: {}\n【请求参数】：{},token:{}", uri, JSONUtil.toJsonStr(data),token);
        String result = HttpRequest.get(uri + token).timeout(6000).form(data).execute().body();
        log.info("\n【企微请求地址】: {}\n【响应参数】：{}", uri, result);
        return result;
    }

    public static String uploadMedia(String url, String token, String type, File file) {
        log.info("\n【企微请求地址】: {}\n【请求参数】：{},token:{}", url, JSONUtil.toJsonStr(file), token);
        String result = HttpRequest
                .post(url + token + "&type=" + type)
                .form("media", file).execute().body();
        log.info("\n【企微请求地址】: {}\n【响应参数】：{}", url, result);
        return result;
    }

}
