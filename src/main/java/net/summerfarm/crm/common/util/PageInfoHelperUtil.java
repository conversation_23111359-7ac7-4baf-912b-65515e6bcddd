package net.summerfarm.crm.common.util;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/9 10:55
 */
public class PageInfoHelperUtil {
    /**
     * pageInfo类型转换
     *
     * @param pageInfoPO 当前pageInfo对象
     * @return 转换之后的pageInfo对象
     */
    public static <P, V> PageInfo<V> pageInfoConvert(PageInfo<P> pageInfoPO, Class<V> targetClazz) {
        PageInfo<V> pageInfo = new PageInfo();
        pageInfo.setEndRow(pageInfoPO.getEndRow());
        pageInfo.setPageSize(pageInfoPO.getPageSize());
        pageInfo.setSize(pageInfoPO.getSize());
        pageInfo.setOrderBy(pageInfoPO.getOrderBy());
        pageInfo.setStartRow(pageInfoPO.getStartRow());
        pageInfo.setPageNum(pageInfoPO.getPageNum());
        pageInfo.setTotal(pageInfoPO.getTotal());
        pageInfo.setPages(pageInfoPO.getPages());
        pageInfo.setFirstPage(pageInfoPO.getFirstPage());
        pageInfo.setPrePage(pageInfoPO.getPrePage());
        pageInfo.setNextPage(pageInfoPO.getNextPage());
        pageInfo.setLastPage(pageInfoPO.getLastPage());
        pageInfo.setIsFirstPage(pageInfoPO.isIsFirstPage());
        pageInfo.setIsLastPage(pageInfoPO.isIsLastPage());
        pageInfo.setHasPreviousPage(pageInfoPO.isHasPreviousPage());
        pageInfo.setHasNextPage(pageInfoPO.isHasNextPage());
        pageInfo.setNavigatePages(pageInfoPO.getNavigatePages());
        pageInfo.setNavigatepageNums(pageInfoPO.getNavigatepageNums());

        List<V> targetList = pageInfoPO.getList().stream().map(e -> {
            try {
                V v = targetClazz.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(e, v);
                return v;
            } catch (Exception exception) {
                throw new RuntimeException("convertPageInfo cause exception", exception);
            }
        }).collect(Collectors.toList());
        pageInfo.setList(targetList);
        return pageInfo;
    }
}
