package net.summerfarm.crm.common.util;

import java.util.concurrent.*;

public class ThreadUtils {
    /**
     * 构建一个线程池  主要用于快速的查询
     * 获取服务器CPU的核数：Runtime.getRuntime().availableProcessors()
     * 线程池定义大小：CPU * 2 + 1
     * 拒绝策略是让当前main线程去处理
     */
     public static final ExecutorService executor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2 + 1,
            Runtime.getRuntime().availableProcessors() * 2 + 1,
            0L, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(200), new ThreadPoolExecutor.CallerRunsPolicy());


}
