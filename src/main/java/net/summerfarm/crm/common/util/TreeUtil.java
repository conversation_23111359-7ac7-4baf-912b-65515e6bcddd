package net.summerfarm.crm.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import net.summerfarm.crm.enums.NodeValue;
import net.summerfarm.crm.model.domain.CrmBdCity;
import net.summerfarm.crm.model.vo.AdministrativeRegionVo;
import net.summerfarm.crm.model.vo.TableArea;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 树结构组装工具
 *
 * <AUTHOR>
 */
@ApiModel
public class TreeUtil<T, ID> {
    private final List<T> topList = new ArrayList<>();
    private static final float DEFAULT_LOAD_FACTOR = 0.75f;

    /**
     * 适合单一对象集合组装成树结构，主要方法就是找到顶层的集合
     *
     * @param list        需要进行树型结构的对象集合，需要有对象有符合节点特征的属性和方法
     * @param getId       获取对象ID的方法
     * @param getParentID 对象获取父对象的ID的方法
     * @param hasParent   对象是否有父对象的方法，方法返回true表示有父对象，false则表示对象为最上层，没有时默认用pid判断，建议自己指定此判断方法
     * @param addChild    父对象添加单个对象到子集的sub集合，默认从getSub方法获取子集后用add添加，建议子集指定实现添加子对象的方法
     * @param comparator  同级对象排序的方法，可以为空不排序
     * @param getSub      可以为null，对象获取子集的方法，如果提供了，在返回结果前将对所有子集进行排序，使用方法入参提供的排序器
     * @return never {@literal null}
     */
    public List<T> toTree(@NotNull List<T> list, @NotNull Function<T, ID> getId, @NotNull Function<T, ID> getParentID, @Nullable Function<T, Boolean> hasParent, @Nullable TreeUtil.AddChild<T> addChild, @Nullable Comparator<T> comparator, @Nullable Function<T, List<T>> getSub) {
        int mapSize = (int) (list.size() / DEFAULT_LOAD_FACTOR) + 1;
        Map<ID, T> map = new HashMap<>(mapSize);
        list.forEach(t -> {
            if (null == hasParent) {
                ID pid = getParentID.apply(t);
                if (null != pid) {
                    if (pid instanceof String) {
                        if (((String) pid).isEmpty()) {
                            topList.add(t);
                        }
                    }
                } else {
                    topList.add(t);
                }
            } else {
                if (!hasParent.apply(t)) {
                    topList.add(t);
                }
            }
            map.put(getId.apply(t), t);
        });

        // 只需要找到每个对象的父，然后父把子加到父的子集中
        map.entrySet().stream().filter(entry -> hasParent.apply(entry.getValue())).forEach(entry -> {
            T t = entry.getValue();
            T parent = map.get(getParentID.apply(t));
            if (null != parent) {
                if (null != addChild) addChild.addChild(parent, t);
                else {
                    getSub.apply(parent).add(t);
                }
            }
        });

        // 如果有获取子集的方法，则对所有子集进行排序
        if (null != comparator && null != getSub) sort(topList, getSub, comparator);
        return topList;
    }

    /**
     * 自包含对象给子集中添加要素的接口
     *
     * @param <T>
     */
    @FunctionalInterface
    public interface AddChild<T> {
        /**
         * 父对象的子集中加入一个子对象
         *
         * @param parent 父对象
         * @param child  子对象
         */
        void addChild(T parent, T child);
    }


    /**
     * 排序
     *
     * @param list       要排序的集合
     * @param subs       集合中单个对象获取子集的方法
     * @param comparator 排序器
     */
    private void sort(List<T> list, Function<T, List<T>> subs, Comparator<T> comparator) {
        Optional.ofNullable(list).ifPresent(list1 -> {
            if (!list1.isEmpty()) {
                if (null != comparator) {
                    list1.sort(comparator);
                    list1.parallelStream().forEach(t -> sort(subs.apply(t), subs, comparator));
                }
            }
        });
    }

    /**
     * 省市区级联
     *
     * @param province 省
     * @return {@link List}<{@link TableArea}>
     */
    public static List<TableArea> createTableArea(Map<String, List<AdministrativeRegionVo>> province) {
        List<TableArea> tableArea = new ArrayList<>();
        for (Map.Entry<String, List<AdministrativeRegionVo>> provinceEntry : province.entrySet()) {
            TableArea provinceTable = new TableArea();
            provinceTable.setLabel(provinceEntry.getKey());
            provinceTable.setValue(provinceEntry.getKey());
            List<TableArea> provinceTableList = new ArrayList<>();
            Map<String, List<AdministrativeRegionVo>> city = provinceEntry.getValue().stream().collect(Collectors.groupingBy(AdministrativeRegionVo::getCity));
            for (Map.Entry<String, List<AdministrativeRegionVo>> cityEntry : city.entrySet()) {
                TableArea cityTable = new TableArea();
                cityTable.setLabel(cityEntry.getKey());
                cityTable.setValue(cityEntry.getKey());
                List<TableArea> areaTable = cityEntry.getValue().stream().map(c -> {
                    TableArea area = new TableArea();
                    area.setLabel(c.getArea());
                    area.setValue(c.getArea());
                    return area;
                }).collect(Collectors.toList());
                cityTable.setChildren(areaTable);
                provinceTableList.add(cityTable);
            }
            provinceTable.setChildren(provinceTableList);
            tableArea.add(provinceTable);
        }
        return tableArea;
    }
}
