package net.summerfarm.crm.common.util;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;


/**
 * 重试方法
 *
 * <AUTHOR>
 **/
@Slf4j
@Builder
public class Retryer {
    /**
     * 重试次数
     */
    private int retryTimes;
    /**
     * 重试间隔
     */
    private int retryInterval;
    /**
     * 时间单位
     */
    private TimeUnit intervalTimeUnit;

    /**
     * 重试方法
     *
     * @param executor                 目标方法
     * @param additionalRetryCondition 额外重试条件:除了异常，满足条件也可以重试
     * @param additionHandle           额外重试条件: 附加处理方法
     * @param finalExceptionHandler    异常处理:指定异常处理方法
     * @return 目标方法执行结果
     */
    public <T> T execute(Supplier<T> executor, @Nullable Predicate<T> additionalRetryCondition, Consumer<T> additionHandle, Function<Exception, T> finalExceptionHandler) {
        T result = null;
        for (int i = 0; i < retryTimes; i++) {
            try {
                result = executor.get();
                // 没有额外重试条件返回结果
                if (additionalRetryCondition == null) {
                    return result;
                }

                // 不满足重试条件
                if (!additionalRetryCondition.test(result)) {
                    return result;
                }
                // 处理重试附加方法
                if (additionHandle != null) {
                    additionHandle.accept(result);
                }
            } catch (Exception e) {
                log.warn("重试失败,e:{}",e.getMessage(), e);
                if (i > retryTimes || finalExceptionHandler != null) {
                    return finalExceptionHandler.apply(e);
                }
            }
            if (i <= retryTimes) {
                try {
                    intervalTimeUnit.sleep(retryInterval);
                } catch (InterruptedException e) {
                    log.error("retry sleep失败,e:{}",e.getMessage(), e);
                }
            }
        }
        return result;
    }
}
