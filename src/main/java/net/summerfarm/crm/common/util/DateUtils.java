package net.summerfarm.crm.common.util;

import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import org.springframework.util.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;


/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public class DateUtils extends BaseDateUtils {

    public static final LocalTime CLOSING_ORDER_TYPE_TIME = LocalTime.of(20, 00, 00);

    public static final String NUMBER_DATE_FORMAT_TWO = "yyyyMMdd";

    public static final String YEAR_MONTH = "yyyyMM";




    public static final Long A_HOUR_TIME_LONG = 1000 * 60 * 60L;

    public static final Long A_DAY_TIME_LONG = 1000 * 24 * 60 * 60L;


    /**
     * 1天/1月/1年
     */
    public static final Integer ONE_DATE = 1;

    /**
     * locaDate 转 String
     */
    public static String tranfLocalDate(LocalDate localDate, String dateFormat) {
        if (localDate == null) {
            return "";
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(dateFormat);
        return df.format(localDate);
    }


    public static String localDateTimeToStringTwo(LocalDate localDate) {
        if (localDate == null) {
            return "";
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(NUMBER_DATE_FORMAT_TWO);
        return df.format(localDate);
    }

    public static LocalDate stringToLocalDate(String time) {
        if (StringUtils.isEmpty(time)) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(NUMBER_DATE_FORMAT_TWO);
        return LocalDate.parse(time,df);
    }

    public static String localDateToString(LocalDate localDate,String dateFormat) {
        if (localDate == null) {
            return "";
        }
        if(StringUtils.isEmpty(dateFormat)){
            dateFormat = DEFAULT_DATE_FORMAT;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(dateFormat);
        return df.format(localDate);
    }



    /**
     * 时间段类
     */
    @Data
    public static class TimeSlot{
        private LocalDateTime startTime;
        private LocalDateTime endTime;

        public TimeSlot(LocalDateTime startTime, LocalDateTime endTime) {
            if (startTime.isAfter(endTime)) {
                this.startTime = endTime;
                this.endTime = startTime;
            } else {
                this.startTime = startTime;
                this.endTime = endTime;
            }
        }
    }

    /**
     * 获取当前日历
     */
    public static String getCalendar() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR) + "年" + (calendar.get(Calendar.MONTH) + 1) + "月" + calendar.get(Calendar.DATE) + "日";
    }

    /**
     * 获取当前日历 年月日时分
     */
    public static String getCalendarDetail() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR) + "年" + (calendar.get(Calendar.MONTH) + 1) + "月" + calendar.get(Calendar.DATE) + "日" + calendar.get(Calendar.HOUR_OF_DAY) + "时" + calendar.get(Calendar.MINUTE) + "分";
    }

    /**
     * 获取每月第一天零点
     * @return 每月第一天零点
     */
    public static  LocalDateTime getAtBeginningOfMonth(){
        LocalDateTime now = LocalDateTime.now();
        return LocalDateTime.of(LocalDate.of(now.getYear(), now.getMonth(), 1), LocalTime.MIN);
    }
    /**
     * 获取指定月第一天零点
     * @return 每月第一天零点
     */
    public static LocalDateTime getAtBeginningOfMonth(LocalDateTime localDateTime){
        if(Objects.isNull(localDateTime)){
            localDateTime = LocalDateTime.now();
        }
        return LocalDateTime.of(localDateTime.toLocalDate().with(TemporalAdjusters.firstDayOfMonth()),LocalTime.MIN);
    }
    /**
     * 获取距指定日期还有多少天
     */
    public static Integer getTimeInMillis(Long millis) {
        Calendar current = Calendar.getInstance(TimeZone.getDefault());
        current.setTimeInMillis(System.currentTimeMillis());

        Calendar date = Calendar.getInstance(TimeZone.getDefault());
        date.setTimeInMillis(millis);
        long time = date.getTimeInMillis() - current.getTimeInMillis();
        // 天
        return Math.round(time / 1000 / 60 / 60 / 24) + 1;
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * Date转LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 获取间隔天数
     */
    public static Integer getIntervalDays(Date lastDate, Date nowDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(lastDate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(nowDate);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        int parseInt = Integer.parseInt(String.valueOf(between_days));
        parseInt = parseInt + 1;
        return parseInt;
    }

    // 当天零点时间
    public static Date initDateByDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static LocalDate max(LocalDate dateTime1, LocalDate dateTime2) {
        if (dateTime1 == null && dateTime2 == null) {
            return null; // 两者均为null时返回null
        }
        if (dateTime1 == null) {
            return dateTime2; // 仅dateTime1为null时返回dateTime2
        }
        if (dateTime2 == null) {
            return dateTime1; // 仅dateTime2为null时返回dateTime1
        }
        // 两者均非null时，比较并返回较大值
        return dateTime1.compareTo(dateTime2) >= 0 ? dateTime1 : dateTime2;
    }

    public static LocalDate min(LocalDate dateTime1, LocalDate dateTime2) {
        if (dateTime1 == null && dateTime2 == null) {
            return null; // 两者均为null时返回null
        }
        if (dateTime1 == null) {
            return dateTime2; // 仅dateTime1为null时返回dateTime2
        }
        if (dateTime2 == null) {
            return dateTime1; // 仅dateTime2为null时返回dateTime1
        }
        // 两者均非null时，比较并返回较小值
        return dateTime1.compareTo(dateTime2) <= 0 ? dateTime1 : dateTime2;
    }

}
