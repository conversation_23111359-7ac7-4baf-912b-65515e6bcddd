package net.summerfarm.crm.common.util;

import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;

public abstract class ExceptionUtils {

    /**
     * 校验dubbo接口的返回结果，如果返回失败则抛出异常
     *
     * @param dubboResponse
     */
    public static void checkDubboResponse(DubboResponse dubboResponse) {
        if (null == dubboResponse || !dubboResponse.isSuccess()) {
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
    }

}
