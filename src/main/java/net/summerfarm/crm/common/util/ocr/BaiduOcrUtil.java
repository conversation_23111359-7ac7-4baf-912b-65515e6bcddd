package net.summerfarm.crm.common.util.ocr;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import org.apache.http.client.methods.HttpPost;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/21 13:39
 */
public class BaiduOcrUtil {
    private String apiKey;

    private String secretKey;

    private static final String API_URL = "https://aip.baidubce.com/oauth/2.0/token";

    public String getAccessToken() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("grant_type", "client_credentials");
        paramMap.put("client_id", apiKey);
        paramMap.put("client_secret", secretKey);
        return HttpUtil.get(API_URL, paramMap);
    }
}
