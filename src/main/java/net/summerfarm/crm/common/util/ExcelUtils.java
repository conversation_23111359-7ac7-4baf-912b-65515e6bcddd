package net.summerfarm.crm.common.util;

import cn.hutool.core.io.IoUtil;
import com.alibaba.excel.support.ExcelTypeEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.ReflectUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public class ExcelUtils {

    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);
    public static final String EXCEL_DIRECTORY = "excel";
    /**
     * 斜杠常量
     */
    public static final String SLASH = "/";
    private String filePath;
    private String sheetName;
    private Workbook workBook;
    private Sheet sheet;
    private List<String> columnHeaderList;
    private List<List<String>> listData;
    private List<Map<String, String>> mapData;
    private boolean flag;

    public ExcelUtils(String filePath, String sheetName) {
        this.filePath = filePath;
        this.sheetName = sheetName;
        this.flag = false;
        this.load();
    }

    public ExcelUtils(Workbook workBook) {
        this.flag = false;
        sheet = workBook.getSheetAt(0);
        getSheetData();
    }

    public ExcelUtils(Workbook workBook, Integer integer) {
        this.flag = false;
        sheet = workBook.getSheetAt(0);
        getSheetDataByAdvance();
    }

    public static String tempExcelFilePath() {
        return System.getProperty("user.dir") + File.separator + tempExcelFileName();
    }
    /**
     * xlsx类型excel文件临时名称
     *
     * @return 文件名称
     */
    public static String tempExcelFileName() {
        return System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue();
    }


    private void load() {
        FileInputStream inStream = null;
        try {
            inStream = new FileInputStream(new File(filePath));
            workBook = WorkbookFactory.create(inStream);
            sheet = workBook.getSheet(sheetName);
        } catch (Exception e) {
            logger.warn("excel下载异常",e);
        } finally {
            try {
                if (inStream != null) {
                    inStream.close();
                }
            } catch (IOException e) {
                logger.warn("excel下载io异常",e);
            }
        }
    }

    public static String getCellValueStr(Cell cell) {
        String cellValue = "";
        DataFormatter formatter = new DataFormatter();
        if (cell != null) {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        cellValue = formatter.formatCellValue(cell);
                    } else {
                        DecimalFormat df = new DecimalFormat("#.##");
                        cellValue = df.format(cell.getNumericCellValue());
                    }
                    break;
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                case FORMULA:
                    cellValue = String.valueOf(cell.getCellFormula());
                    break;
                case BLANK:
                    cellValue = "";
                    break;
                case ERROR:
                    cellValue = "";
                    break;
                default:
                    cellValue = cell.toString().trim();
                    break;
            }
        }
        return cellValue.trim();
    }

    private void getSheetData() {
        listData = new ArrayList<List<String>>();
        mapData = new ArrayList<Map<String, String>>();
        columnHeaderList = new ArrayList<String>();
        int numOfRows = sheet.getLastRowNum() + 1;
        for (int i = 0; i < numOfRows; i++) {
            Row row = sheet.getRow(i);
            Map<String, String> map = new HashMap<String, String>();
            List<String> list = new ArrayList<String>();
            if (row != null) {
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (i == 0) {
                        columnHeaderList.add(getCellValueStr(cell));
                    } else {
                        map.put(columnHeaderList.get(j), getCellValueStr(cell));
                    }
                    list.add(getCellValueStr(cell));
                }
            }
            if (i > 0) {
                mapData.add(map);
                listData.add(list);
            }

        }
        flag = true;
    }
    private void getSheetDataByAdvance() {
        listData = new ArrayList<List<String>>();
        mapData = new ArrayList<Map<String, String>>();
        columnHeaderList = new ArrayList<String>();
        int numOfRows = sheet.getLastRowNum() + 1;
        for (int i = 1; i < numOfRows; i++) {
            Row row = sheet.getRow(i);
            Map<String, String> map = new HashMap<String, String>();
            List<String> list = new ArrayList<String>();
            if (row != null) {
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (i == 1) {
                        columnHeaderList.add(getCellValueStr(cell));
                    } else {
                        map.put(columnHeaderList.get(j), getCellValueStr(cell));
                    }
                    list.add(getCellValueStr(cell));
                }
            }
            if (i > 1) {
                mapData.add(map);
                listData.add(list);
            }

        }
        flag = true;
    }

    public List<String> getColumnHeaderList() {
        return columnHeaderList;
    }

    public List<List<String>> getListData() {
        return listData;
    }

    public List<Map<String, String>> getMapData() {
        return mapData;
    }

    /**
     * 设置响应头
     * @param response
     * @param fileName
     * @throws UnsupportedEncodingException
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel; charset=utf-8");
        response.setHeader("Content-Disposition","attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
    }

    /**
     * 导出excel
     *
     * @param workbook
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void outputExcel(Workbook workbook, String fileName, HttpServletResponse response) throws IOException {
        OutputStream fileOut = null;
        try {
            fileOut = response.getOutputStream();
            response.setContentType("application/vnd.ms-excel; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(fileOut);
        } catch (IOException e) {
            logger.warn("excel导出io异常",e);
        } finally {
            IoUtil.close(fileOut);
        }
    }



    /**
     * 导出excel 数据类型都是String 有些可能不合适
     *
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void outputExcel(Map<String, List<List<String>>> data, String fileName, HttpServletResponse response) throws IOException {
        Workbook workbook = new HSSFWorkbook();
        for (Map.Entry<String, List<List<String>>> entry : data.entrySet()) {
            Sheet sheet = workbook.createSheet(entry.getKey());
            List<List<String>> sheetData = entry.getValue();
            for (int i = 0; i < sheetData.size(); i++) {
                Row row = sheet.createRow(i);
                List<String> rowData = sheetData.get(i);
                for (int j = 0; j < rowData.size(); j++) {
                    row.createCell(j).setCellValue(rowData.get(j));
                }
            }
        }
        outputExcel(workbook, fileName, response);
    }


    /**
     * 获取单元格里的内容
     *
     * @param cell
     * @return
     */
    public static Object getCellValue(Cell cell) {
        Assert.notNull(cell);
        Object obj;
        switch (cell.getCellType()) {
            case STRING:
                obj = cell.getRichStringCellValue().getString();
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    obj = cell.getDateCellValue();
                } else {
                    obj = cell.getNumericCellValue();
                }
                break;
            case BOOLEAN:
                obj = cell.getBooleanCellValue();
                break;
            case FORMULA:
                obj = cell.getCellFormula();
                break;
            case BLANK:
                obj = "";
                break;
            default:
                obj = null;
        }
        return obj;
    }

    /**
     * 设置列宽
     * 公式255.86x+184.27
     * 原本参数的单位是1/256个字符宽度
     *
     * @param x
     * @return
     */
    public static int getColumnWidth(double x) {
        return (int) Math.round(255.86 * x + 184.27);
    }

    /**
     * 设置边框
     *
     * @param workbook
     * @return
     */
    public static CellStyle getSurroundBorder(Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return cellStyle;
    }

    /**
     * 下标转字母
     * @param columnIndex
     * @return
     */
    public static String excelColIndexToStr(int columnIndex) {
        if (columnIndex <= 0) {
            return null;
        }
        String columnStr = "";
        columnIndex--;
        do {
            if (columnStr.length() > 0) {
                columnIndex--;
            }
            columnStr = ((char) (columnIndex % 26 + (int) 'A')) + columnStr;
            columnIndex = (columnIndex - columnIndex % 26) / 26;
        } while (columnIndex > 0);
        return columnStr;
    }

    /**
     * 获得文件的InputStream
     *
     * @param fileName
     * @return
     */
    public static InputStream getExcelFileInputStream(Class clazz, String fileName) {

        InputStream inputStream = clazz.getClassLoader()
                .getResourceAsStream(getExcelFilePath(fileName));

        return inputStream;
    }

    public static String getExcelFilePath(String fileName) {
        return EXCEL_DIRECTORY + SLASH + fileName;
    }
}
