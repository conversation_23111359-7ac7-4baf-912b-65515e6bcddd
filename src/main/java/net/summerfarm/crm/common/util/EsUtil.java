package net.summerfarm.crm.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.es.EsIndexContext;
import net.summerfarm.common.util.es.dto.EsMerchantIndexDTO;
import net.summerfarm.common.util.es.query.EsQuery;
import net.summerfarm.crm.enums.FollowRecordEnum;
import net.summerfarm.crm.enums.FollowUpRelationEnum;
import net.summerfarm.crm.enums.MerchantSizeEnum;
import net.summerfarm.crm.model.query.CrmKeyCustomerQuery;
import net.summerfarm.crm.model.query.MerchantQuery;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import static com.alibaba.fastjson.JSON.parseObject;
import static com.alibaba.fastjson.JSON.toJSONString;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/9 14:54
 */
@Slf4j
public class EsUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(EsUtil.class);

    private static final String[] CUSTOMER_BASE = new String[]{"areaNo", "coreMerchantTag", "dangerDay", "mId", "mname",
            "size", "address", "area", "city", "lastOrderTime", "totalGmv", "timingFollowType", "merchantWhiteListTag",
            "careBdId","rValue","fValue","mValue","lifecycle","daysNotLoggedIn","officialWechatFlag","bdWechatFlag",
            "operateStatus", "valueLabel", "businessLine", "highValueLabel", "releaseTime", "protectReason", "highValueLabelV2"};


    private static final String[] KEY_CUSTOMER_BASE = new String[]{"dangerDay", "mId", "mname", "size", "address", "area",
            "city", "lastOrderTime", "careBdId", "bdId", "daysWithoutOrder", "notVisited","registerTime", "valueLabel"};

    /**
     * 处理结果方法
     *
     * @param esQuery       查询条件
     * @param searchRequest 查询条件构造器
     * @return map
     */
    public static Map<String, Object> manageResult(EsQuery esQuery, SearchRequest searchRequest) throws Exception {
        RestHighLevelClient client = null;
        JSONArray jsonArray = new JSONArray();

        // 构建查询
        SearchHits hits = null;
        try {
            client = EsClientPoolUtil.getClient();
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            hits = searchResponse.getHits();
        } finally {
            EsClientPoolUtil.returnClient(client);
        }

        if (Objects.isNull(hits)) {
            return new HashMap<>(0);
        }

        // 处理查询结果
        for (SearchHit hit : hits) {
            //原来的结果
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            //高亮存在,替换高亮
            if (!StringUtils.isEmpty(esQuery.getHighName())) {
                //解析高亮字段
                Map<String, HighlightField> highlightFields = hit.getHighlightFields();
                HighlightField areaName = highlightFields.get(esQuery.getHighName());
                if (areaName != null) {
                    Text[] fragments = areaName.fragments();
                    StringBuilder newAreaName = new StringBuilder();
                    for (Text text : fragments) {
                        newAreaName.append(text);
                    }
                    //高亮字段替换之前字段
                    sourceAsMap.put(esQuery.getHighName(), newAreaName);
                }
            }
            JSONObject jsonObject = parseObject(toJSONString(sourceAsMap));
            jsonArray.add(jsonObject);
        }
        // 处理分页回文
        return getResultMap(esQuery, jsonArray, hits);
    }

    private static Map<String, Object> getResultMap(EsQuery esQuery, JSONArray jsonArray, SearchHits hits) {
        int total = (int) hits.getTotalHits().value;
        Integer pageNum = esQuery.getPageNum();
        Integer pageSize = esQuery.getPageSize();
        Map<String, Object> map = new HashMap<>(5);
        map.put("data", jsonArray);
        map.put("pageNum", pageNum);
        map.put("pageSize", pageSize);
        map.put("total", total);
        int pages = total == 0 ? 1 : (total % pageSize == 0 ? total / pageSize : (total / pageSize) + 1);
        map.put("pages", pages);
        map.put("isLastPage", Objects.equals(pages, pageNum));
        return map;
    }

    @SneakyThrows
    public static EsMerchantIndexDTO queryByMid(Long mid) {
        EsQuery esQuery = new EsQuery(EsIndexContext.INDEX_MERCHANT, 1, 1);
        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        SearchSourceBuilder sourceBuilder = builderSourceBuilder(esQuery, null);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("mId", mid));
        sourceBuilder.query(boolQueryBuilder);
        searchRequest.source(sourceBuilder);
        Map<String, Object> stringObjectMap = EsUtil.manageResult(esQuery, searchRequest);
        if (stringObjectMap.get("data") == null) {
            return null;
        }
        JSONArray dates = (JSONArray) stringObjectMap.get("data");
        if (dates.size() == 0) {
            return null;
        }
        EsMerchantIndexDTO esMerchantIndexDTO = parseObject(JSONObject.toJSONString(dates.get(0)), EsMerchantIndexDTO.class);
        return esMerchantIndexDTO;
    }
    @SneakyThrows
    public static List<EsMerchantIndexDTO> queryByMids(List<Long> mids) {
        return queryByMidsAndSize(mids,null);
    }

    @SneakyThrows
    public static List<EsMerchantIndexDTO> queryByMidsAndSize(List<Long> mids,String size) {
        int count = mids.size();
        List<EsMerchantIndexDTO> list = new ArrayList<>(count + 1);
        EsQuery esQuery = new EsQuery(EsIndexContext.INDEX_MERCHANT, 1, count);
        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        SearchSourceBuilder sourceBuilder = builderSourceBuilder(esQuery, null);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("mId", mids));
        if (Objects.nonNull(size)){
            boolQueryBuilder.filter(QueryBuilders.termQuery("size", size));
        }
        sourceBuilder.query(boolQueryBuilder);
        searchRequest.source(sourceBuilder);
        Map<String, Object> stringObjectMap = EsUtil.manageResult(esQuery, searchRequest);
        if (stringObjectMap.get("data") == null) {
            return null;
        }
        JSONArray dates = (JSONArray) stringObjectMap.get("data");
        for (Object date : dates) {
            EsMerchantIndexDTO esMerchantIndexDTO = parseObject(JSONObject.toJSONString(date), EsMerchantIndexDTO.class);
            list.add(esMerchantIndexDTO);
        }
        return list;
    }

    public static void insertOrUpdateByMid(EsMerchantIndexDTO esMerchantIndexDTO) {
        Long mId = esMerchantIndexDTO.getmId();
        LOGGER.info("开始新增/更新 es文档数据,id:{}", mId);
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
            String merchantIndexInfoJsonStr = JSON.toJSONString(esMerchantIndexDTO);
            JSONObject jsonObject = JSONObject.parseObject(merchantIndexInfoJsonStr);

            UpdateRequest updateRequest = new UpdateRequest(EsIndexContext.INDEX_MERCHANT_CRM, mId.toString());
            updateRequest.doc(jsonObject, XContentType.JSON);
            updateRequest.upsert(jsonObject, XContentType.JSON);
            // 版本冲突重试
            updateRequest.retryOnConflict(NumberUtils.INTEGER_THREE);
            client.update(updateRequest, RequestOptions.DEFAULT);
            LOGGER.info("新增/更新es文档数据结束,id:{}", mId);
        } catch (Exception e) {
            LOGGER.error("新增/更新 es文档数据失败,mId:{},{}", mId, e);
        } finally {
            EsClientPoolUtil.returnClient(client);
        }
    }


    public static int countPrivateSea(Integer toAdminId,List<Integer> areaList,Integer isLock){
        EsQuery esQuery = new EsQuery(EsIndexContext.INDEX_MERCHANT, 1, 10);
        CrmKeyCustomerQuery query = new CrmKeyCustomerQuery();
        query.setBdId(toAdminId);
        query.setSize(MerchantSizeEnum.SINGGLE_STORE.getValue());
        query.setAreaNoList(areaList);
        query.setIsLock(isLock);
        Integer count = (Integer) EsUtil.queryKeyCustomer(esQuery, query, true, false).get("total");
        if (count == null) {
            return 0;
        }
        return count;
    }

    public static Map<String, Object> queryKeyCustomer(EsQuery esQuery, CrmKeyCustomerQuery keyCustomerQuery, Boolean bd, Boolean care) {
        //构建多条件查询
        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        SearchSourceBuilder sourceBuilder = builderSourceBuilder(esQuery, KEY_CUSTOMER_BASE);
        BoolQueryBuilder boolQueryBuilder = getKeyCustomerSearchQuery(bd, keyCustomerQuery, care);
        // sortQuery(merchantQuery, sourceBuilder);
        sourceBuilder.sort("registerTime", SortOrder.DESC);
        sourceBuilder.query(boolQueryBuilder);
        searchRequest.source(sourceBuilder);
        return search(esQuery, searchRequest, toJSONString(keyCustomerQuery));
    }

    private static SearchSourceBuilder builderSourceBuilder(EsQuery esQuery, String[] fields) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 分页
        sourceBuilder.from((esQuery.getPageNum() - 1) * esQuery.getPageSize());
        sourceBuilder.size(esQuery.getPageSize());
        // 过滤数据
        if (fields!=null && fields.length>0){
            sourceBuilder.fetchSource(fields, null);
        }
        // 搜索
        sourceBuilder.timeout(TimeValue.timeValueSeconds(60));
        return sourceBuilder;
    }

    private static Map<String, Object> search(EsQuery esQuery, SearchRequest searchRequest, String mesage) {
        Map<String, Object> search = new HashMap<>(10);
        try {
            search = EsUtil.manageResult(esQuery, searchRequest);
        } catch (Exception e) {
            LOGGER.error("查询商户数据失败,参数:{},堆栈:{}", mesage, e.getMessage());
        }
        return search;
    }


    public static Map<String, Object> queryEsMerchantIndex(EsQuery esQuery, MerchantQuery merchantQuery) {
        // 未下单天数范围校验
        if (CollectionUtil.isNotEmpty(merchantQuery.getNotOrderList()) && merchantQuery.getNotOrderList().size() != NumberUtils.INTEGER_TWO) {
            LOGGER.warn("前端未按约定规范传递下单天数范围:{}", merchantQuery);
            return Collections.emptyMap();
        }
        merchantQuery.setIsLock(NumberUtils.INTEGER_ZERO);
        //构建多条件查询
        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        SearchSourceBuilder sourceBuilder = builderSourceBuilder(esQuery, CUSTOMER_BASE);
        BoolQueryBuilder boolQueryBuilder = getSearchQuery(merchantQuery);

        sortQuery(merchantQuery, sourceBuilder);
        sourceBuilder.query(boolQueryBuilder);
        searchRequest.source(sourceBuilder);
        Map<String, Object> search = new HashMap<>(10);
        try {
            search = EsUtil.manageResult(esQuery, searchRequest);
        } catch (Exception e) {
            LOGGER.error("查询商户数据失败,参数:{},堆栈:{}", merchantQuery.toString(), e.getMessage(), e);
        }
        return search;
    }

    /**
     * 查询门店周边店铺
     *
     * @return {@link Map}<{@link String},{@link Object}>
     */
    public static Map<String,Object> queryByGeoDistance(double lat,double lon,Long mId){
        EsQuery esQuery = new EsQuery(EsIndexContext.INDEX_MERCHANT,1,10);

        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        SearchSourceBuilder sourceBuilder = builderSourceBuilder(esQuery, CUSTOMER_BASE);

        GeoDistanceQueryBuilder geoDistanceQueryBuilder = QueryBuilders.geoDistanceQuery("contacts.poi").point(lat, lon)
                .distance(100, DistanceUnit.METERS)
                .geoDistance(GeoDistance.PLANE);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(geoDistanceQueryBuilder);
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("mId", mId));


        sourceBuilder.query(boolQueryBuilder);
        searchRequest.source(sourceBuilder);

        try {
            return EsUtil.manageResult(esQuery, searchRequest);
        } catch (Exception e) {
            LOGGER.error("获取门店附近门店失败,参数:{},堆栈:{}", e.getMessage(), e);
        }
        return new HashMap<>(8);
    }

    private static void sortQuery(MerchantQuery merchantQuery, SearchSourceBuilder sourceBuilder) {
        if (Objects.isNull(merchantQuery.getSortType())) {
            return;
        }
        Integer sortType = merchantQuery.getSortType();
        switch (sortType) {
            case 0:
                sourceBuilder.sort("totalGmv", SortOrder.ASC);
                break;
            case 1:
                sourceBuilder.sort("totalGmv", SortOrder.DESC);
                break;
            case 2:
                sourceBuilder.sort("dangerDay", SortOrder.ASC);
                break;
            case 3:
                sourceBuilder.sort("dangerDay", SortOrder.DESC);
                break;
            case 4:
                sourceBuilder.sort("lastOrderTime", SortOrder.ASC);
                break;
            case 5:
                sourceBuilder.sort("lastOrderTime", SortOrder.DESC);
                break;
            default:
                break;
        }
    }

    /**
     * 通过es筛选客户
     *
     * @param merchantQuery 筛选条件:商户名,电话,
     * @return 客户列表
     */
    public static Map<String, Object> esSearchMerchant(int pageIndex, int pageSize, MerchantQuery merchantQuery) {
        merchantQuery = Optional.ofNullable(merchantQuery).orElse(new MerchantQuery());
        EsQuery query = new EsQuery(EsIndexContext.INDEX_MERCHANT, pageIndex, pageSize);
        //构建多条件查询
        int isLock = Objects.isNull(merchantQuery.getIsLock()) ? NumberUtils.INTEGER_ZERO : merchantQuery.getIsLock();
        merchantQuery.setIsLock(isLock);
        merchantQuery.setBdId(merchantQuery.getAdminId());

        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = getSearchQuery(merchantQuery);

        if (Objects.equals(NumberUtils.INTEGER_ONE, merchantQuery.getIsLock())) {
            sourceBuilder.sort("registerTime", SortOrder.DESC);
        }

        sourceBuilder.query(boolQueryBuilder);
        // 过滤数据
        sourceBuilder.fetchSource(new String[]{"address", "area", "bdId", "bdName", "city", "grade", "mId", "merchantWhiteListTag",
                "mname", "size", "timingFollowType", "areaNo", "registerTime","rValue","fValue","mValue","lifecycle",
                "daysNotLoggedIn", "operateStatus", "valueLabel", "highValueLabel", "highValueLabelV2"}, null);

        // 分页
        sourceBuilder.from((pageIndex - 1) * pageSize);
        sourceBuilder.size(pageSize);

        // 搜索
        sourceBuilder.timeout(TimeValue.timeValueSeconds(60));
        searchRequest.source(sourceBuilder);
        Map<String, Object> search = new HashMap<>(10);
        try {
            log.info("es查询客户构造条件 {}", JSONUtil.toJsonStr(boolQueryBuilder));
            search = EsUtil.manageResult(query, searchRequest);
        } catch (Exception e) {
            LOGGER.error("搜索客户失败,查询信息:{},堆栈:{}", searchRequest, e.getMessage(),e);
        }
        return search;
    }

    private static BoolQueryBuilder getSearchQuery(MerchantQuery merchantQuery) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        // term查询
        boolQueryBuilder.filter(QueryBuilders.termQuery("islock", merchantQuery.getIsLock()));
        if (Objects.nonNull(merchantQuery.getBdId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("bdId", merchantQuery.getBdId()));
        }
        if (Objects.nonNull(merchantQuery.getAreaNo())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("areaNo", merchantQuery.getAreaNo()));
        }
        if (Objects.nonNull(merchantQuery.getGrade())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("grade", merchantQuery.getGrade()));
        }
        if (Objects.nonNull(merchantQuery.getPhone())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("phone", merchantQuery.getPhone()));
        }
        if (Objects.nonNull(merchantQuery.getTimingFollowType())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("timingFollowType", merchantQuery.getTimingFollowType()));
        }
        if (Objects.nonNull(merchantQuery.getWhiteListType())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("merchantWhiteListTag", merchantQuery.getWhiteListType()));
        }
        if (Objects.nonNull(merchantQuery.getOperateStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("operateStatus", merchantQuery.getOperateStatus()));
        }
        if (!CollectionUtil.isEmpty(merchantQuery.getOperateStatusList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("operateStatus", merchantQuery.getOperateStatusList()));
        }
        if (Objects.nonNull(merchantQuery.getCoreMerchantTag())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("coreMerchantTag", merchantQuery.getCoreMerchantTag()));
        }
        if (Objects.nonNull(merchantQuery.getLifecycle())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("lifecycle.keyword", merchantQuery.getLifecycle()));
        }
        if (Objects.nonNull(merchantQuery.getRValue())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("rValue", merchantQuery.getRValue().toLowerCase()));
        }
        if (Objects.nonNull(merchantQuery.getFValue())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("fValue", merchantQuery.getFValue().toLowerCase()));
        }
        if (Objects.nonNull(merchantQuery.getMValue())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("mValue", merchantQuery.getMValue().toLowerCase()));
        }
        if (Objects.nonNull(merchantQuery.getProvince())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("province", merchantQuery.getProvince()));
        }
        if (Objects.nonNull(merchantQuery.getCity())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("city", merchantQuery.getCity()));
        }
        if (Objects.nonNull(merchantQuery.getOfficialWechatFlag())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("officialWechatFlag", merchantQuery.getOfficialWechatFlag()));
        }
        if (Objects.nonNull(merchantQuery.getBdWechatFlag())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("bdWechatFlag", merchantQuery.getBdWechatFlag()));
        }
        if (Objects.nonNull(merchantQuery.getArea())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("area.keyword", merchantQuery.getArea()));
        }
        if (Objects.nonNull(merchantQuery.getIsLoggedInWithinThirtyDays())) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("daysNotLoggedIn");
            if (merchantQuery.getIsLoggedInWithinThirtyDays()) {
                rangeQuery.lte(30);
            } else {
                rangeQuery.gte(30);
            }
            boolQueryBuilder.filter(rangeQuery);
        }
        if (Objects.nonNull(merchantQuery.getMname())) {
            // match
            boolQueryBuilder.should(QueryBuilders.multiMatchQuery(merchantQuery.getMname(), "area", "address").operator(Operator.AND));
            boolQueryBuilder.should(QueryBuilders.matchQuery("mname", merchantQuery.getMname()).fuzziness(1));
            boolQueryBuilder.should(QueryBuilders.matchQuery("mnameEn", merchantQuery.getMname()));
            boolQueryBuilder.minimumShouldMatch(NumberUtils.INTEGER_ONE);
        }
        if (CollectionUtil.isNotEmpty(merchantQuery.getAreaNoSet())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("areaNo", merchantQuery.getAreaNoSet()));
        }
        if (Objects.nonNull(merchantQuery.getSize())) {
            if (Objects.equals(MerchantSizeEnum.SINGGLE_STORE.getValue(), merchantQuery.getSize())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("size", merchantQuery.getSize()));
            } else {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("size", MerchantSizeEnum.SINGGLE_STORE.getValue()));
            }
        }
        if (CollectionUtil.isNotEmpty(merchantQuery.getMIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("mId", merchantQuery.getMIds()));
        }

        // match_phrase查询
        if (Objects.nonNull(merchantQuery.getMerchantLabel())) {
            boolQueryBuilder.filter(QueryBuilders.matchPhraseQuery("merchantLabel", merchantQuery.getMerchantLabel()));
        }

        // 范围查询
        if (Objects.nonNull(merchantQuery.getOrderCurrentMonth())) {
            long epochMilli = DateUtils.getAtBeginningOfMonth().toInstant(ZoneOffset.of("+8")).toEpochMilli();
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("lastOrderTime");

            if (merchantQuery.getOrderCurrentMonth()) {
                rangeQuery.gte(epochMilli);
            } else {
                rangeQuery.lte(epochMilli);
            }
            boolQueryBuilder.filter(rangeQuery);
        }
        if (CollectionUtil.isNotEmpty(merchantQuery.getNotOrderList())) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("daysWithoutOrder");
            rangeQuery.gte(merchantQuery.getNotOrderList().get(NumberUtils.INTEGER_ZERO));
            rangeQuery.lte(merchantQuery.getNotOrderList().get(NumberUtils.INTEGER_ONE));
            boolQueryBuilder.filter(rangeQuery);
        }
        if (Objects.nonNull(merchantQuery.getStartTime()) && Objects.nonNull(merchantQuery.getEndTime())) {
            long startTime = DateUtils.localDateTimeToDate(merchantQuery.getStartTime()).getTime();
            long endTime = DateUtils.localDateTimeToDate(merchantQuery.getEndTime()).getTime();
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("registerTime");
            rangeQuery.gte(startTime);
            rangeQuery.lte(endTime);
            boolQueryBuilder.filter(rangeQuery);
        }

        int dayOfMonth = LocalDate.now().getDayOfMonth();
        if (Objects.nonNull(merchantQuery.getFollowMerchant())) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("notVisited");
            if (Objects.equals(FollowRecordEnum.Status.NO_PAYMENT.ordinal(), merchantQuery.getFollowMerchant())) {
                rangeQuery.gte(dayOfMonth);
            } else {
                rangeQuery.lte(dayOfMonth);
            }
            boolQueryBuilder.filter(rangeQuery);
        }

        if (Objects.nonNull(merchantQuery.getDangerDayMinimum()) && Objects.nonNull(merchantQuery.getDangerDayMaximum())) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("dangerDay");
            rangeQuery.gte(merchantQuery.getDangerDayMinimum());
            rangeQuery.lte(merchantQuery.getDangerDayMaximum());
            boolQueryBuilder.filter(rangeQuery);
            // 根据dangerDay查询时需要过滤掉落保护中的客户
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("protectReason", FollowUpRelationEnum.ProtectReason.getProtectReasonValues()));
        }

        if (Objects.nonNull(merchantQuery.getValueLabel())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("valueLabel", merchantQuery.getValueLabel()));
        }

        // 过滤上门后可捞回的客户
        Boolean filterReclaimable = merchantQuery.getFilterReclaimable();
        if (filterReclaimable != null && filterReclaimable) {
            // 本质就是15天内掉落到公海且"上次跟进销售"是当前销售的客户
            LocalDateTime dropTime = LocalDateTime.now().minusDays(15);
            BoolQueryBuilder excludeQuery = new BoolQueryBuilder();
            excludeQuery.must(QueryBuilders.rangeQuery("reassignTime").gt(LocalDateTimeUtil.toEpochMilli(dropTime)));
            excludeQuery.must(QueryBuilders.rangeQuery("lastFollowUpBdId").gt(1)); // 有上次跟进销售的且上次不是公海的
            if (merchantQuery.getAdminId() != null) {
                excludeQuery.must(QueryBuilders.termQuery("lastFollowUpBdId", merchantQuery.getAdminId()));
            }

            // 剔出这部分数据
            boolQueryBuilder.mustNot(excludeQuery);
        }

        if (Objects.nonNull(merchantQuery.getBusinessLine())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("businessLine", merchantQuery.getBusinessLine()));
        }

        if (StringUtils.isNotBlank(merchantQuery.getHighValueLabel())) {
            if (Boolean.TRUE.equals(merchantQuery.getQueryPerformanceV2())) {
                // 查询绩效二期数据
                boolQueryBuilder.filter(QueryBuilders.termQuery("highValueLabelV2", merchantQuery.getHighValueLabel()));
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery("highValueLabel.keyword", merchantQuery.getHighValueLabel()));
            }
        }

        return boolQueryBuilder;
    }

    private static BoolQueryBuilder getKeyCustomerSearchQuery(Boolean bd, CrmKeyCustomerQuery merchantQuery, boolean care) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        // term查询
        if (Objects.nonNull(merchantQuery.getBdId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("bdId", merchantQuery.getBdId()));
        }
        if (Objects.nonNull(merchantQuery.getAreaNo())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("areaNo", merchantQuery.getAreaNo()));
        }
        if (CollectionUtil.isNotEmpty(merchantQuery.getAreaNoList())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("areaNo", merchantQuery.getAreaNoList()));
        }
        // match_phrase查询
        if (Objects.nonNull(merchantQuery.getTag())) {
            boolQueryBuilder.filter(QueryBuilders.matchPhraseQuery("merchantLabel", merchantQuery.getTag()));
        }

        if (Objects.nonNull(merchantQuery.getDangerDay())) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("dangerDay");
            rangeQuery.gte(0);
            rangeQuery.lte(merchantQuery.getDangerDay());
            boolQueryBuilder.filter(rangeQuery);
        }
        if (Objects.nonNull(merchantQuery.getProvince())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("province", merchantQuery.getProvince()));
        }
        if (Objects.nonNull(merchantQuery.getCity())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("city", merchantQuery.getCity()));
        }
        if (Objects.nonNull(merchantQuery.getArea())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("area", merchantQuery.getArea()));
        }
        if (!bd) {
            RangeQueryBuilder privateQuery = QueryBuilders.rangeQuery("bdId");
            privateQuery.gte(merchantQuery.getNotEmptyBd() != null && merchantQuery.getNotEmptyBd() ? 1 : 0);
            boolQueryBuilder.filter(privateQuery);
            if (care){
                RangeQueryBuilder careBD = QueryBuilders.rangeQuery("careBdId");
                careBD.gte(0);
                boolQueryBuilder.filter(careBD);
            }
        }
        if (Objects.nonNull(merchantQuery.getSize())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("size", merchantQuery.getSize()));
        }
        if (Objects.nonNull(merchantQuery.getCarBdId())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("careBdId", merchantQuery.getCarBdId()));
        }
        if (Objects.nonNull(merchantQuery.getIsLock())){
            boolQueryBuilder.filter(QueryBuilders.termQuery("islock", merchantQuery.getIsLock()));
        }
        return boolQueryBuilder;
    }
}
