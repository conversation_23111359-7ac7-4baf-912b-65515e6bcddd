package net.summerfarm.crm.common.util;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.crm.enums.DownloadBizTypeEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

import static net.summerfarm.crm.service.impl.CrmTaskServiceImpl.TEMP_FILE_PATH;

@Component
@Slf4j
public class DownloadCenterUploadUtils {

    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;

    /**
     * 上传数据到下载中心
     *
     * @param filename 要上传的文件名
     * @param data 要上传的数据列表
     * @param dataClass 数据类类型
     * @param bizType 下载中心业务类型
     * @param adminId 执行上传的管理员ID
     * @param expiredDay 下载中心文件过期时间
     * @return 下载中心记录ID
     */
    public <T> Long uploadToDownloadCenter(
            String filename,
            List<T> data,
            Class<T> dataClass,
            DownloadBizTypeEnum bizType,
            Long adminId,
            DownloadCenterEnum.FileExpiredDayEnum expiredDay) {

        // 1. Write data to temp file
        File file = createTempFile(filename, data, dataClass);

        try {
            // 2. Create download record
            Long resId = createDownloadRecord(filename, adminId, bizType, expiredDay);

            // 3. Upload file to OSS
            uploadFileToOss(filename, file, resId);

            return resId;
        } finally {
            // 4. Clean up temp file
            FileUtil.del(file);
        }
    }

    private <T> File createTempFile(String filename, List<T> data, Class<T> dataClass) {
        File file = new File(System.getProperty(TEMP_FILE_PATH) + File.separator + filename);
        EasyExcel.write(file, dataClass).sheet().doWrite(data);
        return file;
    }

    private Long createDownloadRecord(String filename, Long adminId, DownloadBizTypeEnum bizType,
                                      DownloadCenterEnum.FileExpiredDayEnum expiredDay) {
        DownloadCenterInitReq req = new DownloadCenterInitReq();
        req.setFileExpiredDay(expiredDay);
        req.setBizType(bizType.getBizType());
        req.setFileName(filename);
        req.setAdminId(adminId);

        DubboResponse<DownloadCenterResp> response = downloadCenterProvider.initRecord(req);
        if (!response.isSuccess()) {
            log.info("创建下载记录失败, msg: {}", response.getMsg());
            throw new BizException("网络波动，请重试");
        }
        return response.getData().getResId();
    }

    private void uploadFileToOss(String filename, File file, Long resId) {
        OssUploadResult upload = OssUploadUtil.upload(filename, file, OSSExpiredLabelEnum.THREE_DAY);

        DownloadCenterUploadReq req = new DownloadCenterUploadReq();
        req.setStatus(DownloadCenterEnum.Status.UPLOADED);
        req.setResId(resId);
        req.setFilePath(upload.getObjectOssKey());

        downloadCenterProvider.uploadFile(req);
    }
}
