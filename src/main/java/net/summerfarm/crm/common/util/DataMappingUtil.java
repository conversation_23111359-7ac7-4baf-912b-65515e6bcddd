package net.summerfarm.crm.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @descripton
 * @date 2023/8/25 15:24
 */
@Slf4j
public class DataMappingUtil {



    


    /**
     * 根据指定映射，将源数据补充到targetList
     *
     * @param sourceList
     * @param targetList
     * @param fieldMapping
     * @param primaryKeyField
     * @param <T>
     * @param <U>
     */
    public static <T, U> void mapAndFillFields(List<T> sourceList, List<U> targetList, Map<String, String> fieldMapping, String primaryKeyField) {

        if (CollUtil.isEmpty(sourceList) || CollUtil.isEmpty(targetList) || CollUtil.isEmpty(fieldMapping)) {
            return;
        }

        Map<Object, T> sourceMap = new HashMap<>();
        for (T source : sourceList) {
            Object primaryKey = getFieldValue(source, primaryKeyField);
            sourceMap.put(primaryKey, source);
        }

        for (U target : targetList) {
            String targetPrimaryKeyField = StrUtil.isBlank(fieldMapping.get(primaryKeyField)) ? primaryKeyField : fieldMapping.get(primaryKeyField);
            Object primaryKey = getFieldValue(target, targetPrimaryKeyField);
            T source = sourceMap.get(primaryKey);
            if (source != null) {
                for (Map.Entry<String, String> mapping : fieldMapping.entrySet()) {
                    String sourceField = mapping.getKey();
                    String targetField = mapping.getValue();
                    if (sourceField.equals(primaryKeyField)) {
                        // 跳过主键
                        break;
                    }
                    Object fieldValue = getFieldValue(source, sourceField);
                    setFieldValue(target, targetField, fieldValue);
                }
            }
        }
    }


    private static <T> Object getFieldValue(T dto, String fieldName) {
        try {
            Field field = getField(dto, fieldName);
            if (null != field) {
                field.setAccessible(true);
                return field.get(dto);
            }
        } catch (Exception e) {
            log.error("【拼接外域数据-获取数据失败】 data :{}", JSON.toJSONString(dto));
            log.error("【拼接外域数据-获取数据失败】!", e);
            return null;
        }
        log.warn("字段不存在！ field:{}, data :{}", fieldName, JSON.toJSONString(dto));
        return null;
    }

    private static <U> void setFieldValue(U dto, String fieldName, Object value) {
        try {
            Field field = getField(dto, fieldName);
            if (null != field) {
                field.setAccessible(true);
                field.set(dto, value);
                return;
            }
            log.warn("字段不存在！ 降级设值操作。field:{}, data :{}", fieldName, JSON.toJSONString(dto));
        } catch (Exception e) {
            log.error("【拼接外域数据-设置数据失败】 data :{}", JSON.toJSONString(dto));
            log.error("【拼接外域数据-设置数据失败】!", e);
        }
    }

    private static <U> Field getField(U dto, String fieldName) {
        List<Field> fields = new ArrayList<>();
        getFieldList(dto.getClass(), fields);
        if (CollUtil.isNotEmpty(fields)) {
            Optional<Field> optional = fields.stream().filter(field -> field.getName().equals(fieldName)).findFirst();
            return optional.orElse(null);
        }
        return null;
    }

    private static <U> void getFieldList(Class<U> uClass, List<Field> fields) {
        fields.addAll(Arrays.asList(uClass.getDeclaredFields()));
        if (uClass.equals(Object.class)) {
            return;
        }
        getFieldList(uClass.getSuperclass(), fields);
    }
}
