package net.summerfarm.crm;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import net.summerfarm.crm.factory.BusinessFactory;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.handler.*;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.dromara.easyes.spring.annotation.EsMapperScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Package: net.summerfarm
 * @Description: 启动类
 * @author: <EMAIL>
 * @Date: 2019-12-24
 */
@SpringBootApplication(scanBasePackages = {"net.summerfarm.crm","net.xianmu.authentication"})
@MapperScan(basePackages = {"net.summerfarm.crm.mapper", "net.summerfarm.warehouse.mapper","net.summerfarm.odps.mapper","net.summerfarm.authorization.mapper"})
@EnableAsync
@EnableScheduling
@EnableFeignClients(basePackages = {"net.summerfarm.crm.feign"})
@DubboComponentScan(basePackages = {"net.summerfarm.crm.**.provider","net.xianmu.authentication.**.provider"})
@NacosPropertySource(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
@EsMapperScan("net.summerfarm.crm.es.mapper")
public class CrmApplication{

    public static void main(String[] args) {
        SpringApplication.run(CrmApplication.class, args);
    }

    @Bean("asycExecutor")
    public Executor executor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(16);
        // 设置队列容量
        executor.setQueueCapacity(1024);
        // 设置线程活跃时间(秒)
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("asycTask-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean("businessFactory")
    public BusinessFactory businessFactory(DefaultHandler defaultHandler, ExcelHandler excelHandler, MerchantSituationHandler merchantSituationHandler
    , SampleHandler sampleHandler, MerchantPoiUpdateHandler poiUpdateHandler,MerchantRepeatedHandler repeatedHandler,RiskMerchantHandler riskMerchantHandler,WeComUserCreateHandle createHandle){
        BusinessFactory businessFactory = new BusinessFactory();
        Map<String, CrmBusiness> businessMap = new HashMap<>(10);
        businessMap.put(MessageBusiness.DEFAULT, defaultHandler);
        businessMap.put(MessageBusiness.EXCEL, excelHandler);
        businessMap.put(MessageBusiness.MERCHANT_SITUATION, merchantSituationHandler);
        businessMap.put(MessageBusiness.SAMPLE, sampleHandler);
        businessMap.put(MessageBusiness.POI_UPDATE, poiUpdateHandler);
        businessMap.put(MessageBusiness.REPEATED_MERCHANT, repeatedHandler);
        businessMap.put(MessageBusiness.RISK_MERCHANT, riskMerchantHandler);
        businessMap.put(MessageBusiness.WECOM_USER_CREATE, createHandle);
        businessFactory.setBusinessMap(businessMap);
        return businessFactory;
    }
}
