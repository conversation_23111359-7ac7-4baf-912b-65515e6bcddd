package net.summerfarm.crm.mapper.offline;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.crm.model.domain.SalesBdIncrementCategoryMtd;
import net.summerfarm.crm.model.dto.salesperformance.CategoryPromotionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SalesBdIncrementCategoryMtdMapper extends BaseMapper<SalesBdIncrementCategoryMtd> {

    /**
     * 获取销售维度的品类推广数据，在数据库层面进行聚合
     *
     * @param bdIds 销售ID列表
     * @param custIncrementType 客户类型
     * @param spuGroupList 商品分组列表
     * @param sortField 排序字段
     * @param sortDirection 排序方向（ASC/DESC）
     * @return 销售维度的品类推广数据
     */
    List<CategoryPromotionDTO> getCategoryPromotionBySales(
            @Param("bdIds") List<Long> bdIds,
            @Param("custIncrementType") String custIncrementType,
            @Param("spuGroupList") List<String> spuGroupList,
            @Param("sortField") String sortField,
            @Param("sortDirection") String sortDirection);
}