package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmCityTodayGmv;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import net.summerfarm.crm.model.vo.SalesDataVo;
import net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmCityDayGmvMapper {

    /**
     * 根据城市名查询
     * @param salesDataQuery 查询条件
     * @return 城市gmv信息
     */
    SalesDataVo selectByCity(SalesDataQuery salesDataQuery);

    /**
     * 统计城市销售数据
     *
     * @param salesCity 销售城市
     * @return {@link SalesAreaDataVo}
     */
    SalesAreaDataVo selectByCityList(@Param("salesCity") List<BdSalesCityVo> salesCity, @Param("dayTag") Integer dayTag);

    /**
     * 城市销售数据列表
     *
     * @param salesCity 销售城市
     * @return {@link SalesAreaDataVo}
     */
    List<SalesAreaDataVo> listByCities(@Param("salesCity") List<BdSalesCityVo> salesCity, @Param("dayTag") Integer dayTag);

}




