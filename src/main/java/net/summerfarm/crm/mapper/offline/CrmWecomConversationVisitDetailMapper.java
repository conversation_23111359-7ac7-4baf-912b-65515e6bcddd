package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmWecomConversationVisitDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CrmWecomConversationVisitDetailMapper {

    List<CrmWecomConversationVisitDetail> selectValidByDateTagAndBdIdIn(@Param("dateTag") Integer dateTag,
                                                                        @Param("bdIdCollection") Collection<Long> bdIdCollection);


}