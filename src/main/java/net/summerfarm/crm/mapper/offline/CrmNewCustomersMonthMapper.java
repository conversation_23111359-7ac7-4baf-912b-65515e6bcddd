package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmNewCustomersMonth;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/10/31 16:49
 */
public interface CrmNewCustomersMonthMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmNewCustomersMonth record);

    int insertSelective(CrmNewCustomersMonth record);

    CrmNewCustomersMonth selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmNewCustomersMonth record);

    int updateByPrimaryKey(CrmNewCustomersMonth record);

    /**
     * 列表通过销售id
     *
     * @param bdId 销售id
     * @return {@link List}<{@link CrmNewCustomersMonth}>
     */
    List<Long> listByBdId(@Param("bdId")Integer bdId);
}