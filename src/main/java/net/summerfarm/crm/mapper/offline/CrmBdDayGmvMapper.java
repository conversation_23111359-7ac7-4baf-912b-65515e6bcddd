package net.summerfarm.crm.mapper.offline;


import net.summerfarm.crm.model.domain.CrmBdTodayDayGmv;
import net.summerfarm.crm.model.vo.AdminInfoVo;
import net.summerfarm.crm.model.vo.RankingListVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmBdDayGmvMapper {
    /**
     * 根据销售id查询
     * @param adminId 销售id
     * @param dayTag 数据更新日期标识
     * @return 销售每日gmv信息
     */
    AdminInfoVo selectByAdminId(@Param("adminId") Integer adminId, @Param("dayTag") Integer dayTag);


    /**
     * 销售本月 gmv 列表
     *
     * @param adminIds 管理员id
     * @param dayTag   天标记
     * @return {@link List}<{@link CrmBdTodayDayGmv}>
     */
    List<CrmBdTodayDayGmv> listByAdminId(@Param("adminIds")List<Integer> adminIds, @Param("dayTag") Integer dayTag);

    /**
     * 查询排行榜
     *
     * @param type 排行榜类型 1:拉新;2:拜访
     * @param dayTag 日期标记
     * @return 排行榜数据
     */
    List<RankingListVO> selectRankingListVO(@Param("type") Integer type,@Param("dayTag")Integer dayTag);
}