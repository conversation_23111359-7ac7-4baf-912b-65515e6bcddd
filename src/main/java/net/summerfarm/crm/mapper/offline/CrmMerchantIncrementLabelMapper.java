package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmMerchantIncrementLabel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 门店标签变动
 *
 * <AUTHOR>
 * @Date 2023/8/1 13:59
 */
public interface CrmMerchantIncrementLabelMapper {
    /**
     * 根据unionid查询标签变动
     * 新添加企微客户全量更新标签
     *
     * @param unionId
     * @param dayTag
     * @param isNewCustomer 是否是新添加企微标签的客户
     * @return {@link List}<{@link CrmMerchantIncrementLabel}>
     */
    List<CrmMerchantIncrementLabel> selectByUnionId(@Param("unionId") String unionId, @Param("dayTag") Integer dayTag,@Param("isNewCustomer") Boolean isNewCustomer);

    /**
     * 查询所有标签
     *
     * @return {@link List}<{@link String}>
     */

    List<String> listAllLabel();
}
