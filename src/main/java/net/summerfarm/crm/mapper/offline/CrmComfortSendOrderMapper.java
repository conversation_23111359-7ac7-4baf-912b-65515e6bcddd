package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.query.CrmComfortSendQuery;
import net.summerfarm.crm.model.vo.ComfortSendOrderVO;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CrmComfortSendOrderMapper {
    List<ComfortSendOrderVO> selectByQuery(CrmComfortSendQuery crmComfortSendQuery);

    List<ComfortSendOrderVO> selectByQueryList(CrmComfortSendQuery crmComfortSendQuery);

    Integer countByTableBdIdAreaNo(@Param("dayTag") Integer dayTag,
                                   @Param("tableName") String tableName,
                                   @Param("bdId") Long bdId, @Param("province") String province, @Param("city") String city, @Param("area") String area);
}
