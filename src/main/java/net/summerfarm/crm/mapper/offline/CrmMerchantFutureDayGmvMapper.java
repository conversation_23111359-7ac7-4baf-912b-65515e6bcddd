package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmMerchantFutureDayGmv;
import net.summerfarm.crm.model.query.DeliveryMerchantQuery;
import net.summerfarm.crm.model.vo.DeliveryMerchantVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_merchant_future_day_gmv(商户当日及未来gmv表)】的数据库操作Mapper
* @createDate 2022-10-20 14:39:39
* @Entity net.summerfarm.crm.model.domain.CrmMerchantFutureDayGmv
*/
public interface CrmMerchantFutureDayGmvMapper {

    /**
     * 根据id查询信息
     * @param id id
     * @return gmv信息
     */
    CrmMerchantFutureDayGmv selectByPrimaryKey(Long id);

    /**
     * 根据筛选条件获取商户gmv信息列表
     * @param deliveryMerchantQuery 查询条件
     * @return 商户gmv信息列表
     */
    List<DeliveryMerchantVO> selectByQuery(DeliveryMerchantQuery deliveryMerchantQuery);
}
