package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmSkuAreaMonthMerchant;
import net.summerfarm.crm.model.query.SkuMerchantQuery;

/**
* <AUTHOR>
* @description 针对表【crm_sku_area_month_merchant(每个sku每月下单商户id表)】的数据库操作Mapper
* @createDate 2022-11-02 15:28:26
* @Entity net.summerfarm.crm.model.domain.CrmSkuAreaMonthMerchant
*/
public interface CrmSkuAreaMonthMerchantMapper {

    /**
     * 查询每个sku每月下单商户id,区域维度
     * @param skuMerchantQuery 查询条件
     * @return sku每月下单商户id
     */
    CrmSkuAreaMonthMerchant selectByQuery(SkuMerchantQuery skuMerchantQuery);


}
