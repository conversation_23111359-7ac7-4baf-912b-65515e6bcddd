package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.query.PrivateSeaQuery;
import net.summerfarm.crm.model.vo.MerchantVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmMerchantDayLabelMapper {

    /**
     * 根据商户id查询商户标签
     * @param mId 商户id
     * @param dayTag 数据所在日期标识
     * @return 商户标签
     */
    List<String> selectByPrimaryKey(@Param("mId") Long mId, @Param("dayTag") Integer dayTag);

    /**
     * 根据标签查询商户信息
     * @param selectKeys 查询条件
     * @param dayTag 时间标记
     * @return 商户ids
     */
    List<MerchantVO> selectMidListByInput( @Param("selectKeys") MerchantVO selectKeys,@Param("dayTag") Integer dayTag);

    /**
     * 查询商户id,根据标签
     * @param merchantLabel 标签名
     * @param dataTag 数据所在日期
     * @return 商户ids
     */
    Set<Long> selectMidListByLabel(@Param("merchantLabel") String merchantLabel, @Param("dataTag") Integer dataTag);


    Set<Long> selectMidListByLabelLimit(@Param("merchantLabel") String merchantLabel, @Param("dataTag") Integer dataTag,
                                        @Param("offset") Integer offset,@Param("size") Integer size);


    Long selectMidCountByLabelLimit(@Param("merchantLabel") String merchantLabel, @Param("dataTag") Integer dataTag);

    /**
     * 列出所有标签
     *
     * @return {@link List}<{@link String}>
     */
    List<String> listAllLabel();


}