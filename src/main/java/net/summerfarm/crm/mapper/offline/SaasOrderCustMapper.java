package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.SaasOrderCust;
import net.summerfarm.crm.model.query.saasorder.SaasOrderListQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface SaasOrderCustMapper {

    SaasOrderCust selectOneByOrderNo(@Param("orderNo") String orderNo);

    List<SaasOrderCust> selectByQuery(@Param("query") SaasOrderListQuery query, @Param("bdIdCollection") Collection<Long> bdIdCollection);
}