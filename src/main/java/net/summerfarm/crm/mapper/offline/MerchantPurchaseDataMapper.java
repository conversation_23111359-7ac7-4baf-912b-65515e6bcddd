package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.MerchantPurchaseData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-08-29 15:46:59
 * @version 1.0
 *
 */
@Mapper
public interface MerchantPurchaseDataMapper{

    List<MerchantPurchaseData> selectByDayTag(@Param("dayTag") Integer dayTag);
}

