package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.model.domain.CustPerformanceComm;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 操作【cust_performance_comm(客户维度绩效表现)】的Mapper
 * 
 * <AUTHOR>
 */
public interface CustPerformanceCommMapper {

    /**
     * 查询根据BD汇总的客户绩效信息
     * 
     * @param bdIds BD ID列表
     * @param custValueLabel 客户价值标签（可选）
     * @param firstDayOfMonth 当前日期是否每月的第一天
     * @return
     */
    List<CustPerformanceComm> summaryByBdIds(@Param("bdIds") List<Long> bdIds, @Param("custValueLabel") String custValueLabel,
                                             @Param("sortField") String sortField,
                                             @Param("sortDirection") SortDirectionEnum sortDirection,
                                             @Param("firstDayOfMonth") Boolean firstDayOfMonth);

    /**
     * 根据BD ID查询客户绩效数据
     * 
     * @param bdId BD ID
     * @param custValueLabel 客户价值标签（可选）
     * @param mname 客户名称（可选，模糊匹配）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @param firstDayOfMonth 当前日期是否每月的第一天
     * @return
     */
    List<CustPerformanceComm> listByBdId(@Param("bdId") Long bdId, @Param("custValueLabel") String custValueLabel,
                                         @Param("mname") String mname, @Param("sortField") String sortField,
                                         @Param("sortDirection") SortDirectionEnum sortDirection,
                                         @Param("firstDayOfMonth") Boolean firstDayOfMonth);

    /**
     * 根据来源查询客户绩效数据
     * 
     * @param orderSource 来源
     * @param firstDayOfMonth 当前日期是否每月的第一天
     * @return 客户绩效数据列表
     */
    List<CustPerformanceComm> listByOrderSource(@Param("orderSource") String orderSource,
                                                @Param("firstDayOfMonth") Boolean firstDayOfMonth);

    /**
     * 根据客户ID列表查询客户绩效数据
     * 
     * @param custIds 客户ID列表
     * @param orderSource 来源（可选）
     * @param firstDayOfMonth 当前日期是否每月的第一天
     * @return 客户绩效数据列表
     */
    List<CustPerformanceComm> listByCustIds(@Param("custIds") List<Long> custIds, @Param("orderSource") String orderSource,
                                            @Param("firstDayOfMonth") Boolean firstDayOfMonth);
}
