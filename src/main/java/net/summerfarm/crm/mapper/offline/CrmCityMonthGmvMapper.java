package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.vo.SalesDataVo;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmCityMonthGmvMapper {

    /**
     * 根据城市名查询上月gmv,核心客户数
     * @param salesDataQuery 查询条件
     * @return 城市gmv信息
     */
    SalesDataVo selectLastInfo(SalesDataQuery salesDataQuery);

    /**
     * 根据城市名查询
     * @param salesDataQuery 查询条件
     * @return 城市gmv信息
     */
    SalesDataVo selectByCity(SalesDataQuery salesDataQuery);
}




