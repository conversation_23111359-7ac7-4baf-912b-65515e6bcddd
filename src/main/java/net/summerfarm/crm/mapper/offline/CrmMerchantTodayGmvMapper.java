package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmMerchantTodayGmv;
import net.summerfarm.crm.model.query.MerchantDetailQuery;

/**
* <AUTHOR>
* @description 针对表【crm_merchant_today_gmv(商户本月gmv表)】的数据库操作Mapper
* @createDate 2022-11-02 10:56:39
* @Entity net.summerfarm.crm.model.domain.CrmMerchantTodayGmv
*/
public interface CrmMerchantTodayGmvMapper {

    /**
     * 查询商户当日gmv信息
     * @param merchantDetailQuery 查询条件
     * @return 当日gmv信息
     */
    CrmMerchantTodayGmv selectByQuery(MerchantDetailQuery merchantDetailQuery);

}
