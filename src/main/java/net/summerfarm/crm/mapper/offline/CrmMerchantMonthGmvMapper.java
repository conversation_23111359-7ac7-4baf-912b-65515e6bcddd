package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmMerchantMonthGmv;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.vo.ChangeMerchantVO;
import net.summerfarm.crm.model.vo.PrivateSeaVO;
import net.summerfarm.crm.model.vo.SalesDataVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmMerchantMonthGmvMapper {
    /**
     * 根据id查询
     * @param id id
     * @return 商户每月gmv信息
     */
    CrmMerchantMonthGmv selectByPrimaryKey(Long id);
    /**
     * 查询商户维度 本月gmv信息
     * @param salesDataQuery 查询条件
     * @return 商户维度:本月gmv,本月核心客户数
     */
    SalesDataVo selectByInput(SalesDataQuery salesDataQuery);

    /**
     * 根据商户id查询
     * @param mId 商户id
     * @param monthTag 数据更新日期标识
     * @return 销售每日gmv信息
     */
    PrivateSeaVO selectByMid(@Param("mId") Long mId, @Param("monthTag") Integer monthTag);

    /**
     * 获取核心客户信息
     * @param monthTag 数据更新日期标识
     * @param adminId 销售id
     * @return 核心客户信息
     */
    List<ChangeMerchantVO> selectCoreMerchant(@Param("adminId") Integer adminId, @Param("monthTag") Integer monthTag);

    /**
     * 查询总月活
     * @param salesDataQuery 查询条件
     * @return 总月活数
     */
    int selectAllMonthLiving(SalesDataQuery salesDataQuery);

    /**
     * 商户gmv数据
     * @param ids 商户id
     * @param areaNo 区域
     * @return 商户gmv数据
     */
    SalesDataVo selectByMids(@Param("ids") List<Integer> ids, @Param("areaNo") List<Integer> areaNo, @Param("monthTag") Integer monthTag);

}