package net.summerfarm.crm.mapper.offline;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.crm.model.domain.CustMtdCategoryComm;
import net.summerfarm.crm.model.dto.salesperformance.CategoryPromotionDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustMtdCategoryCommMapper extends BaseMapper<CustMtdCategoryComm> {

    /**
     * 获取客户维度的品类推广数据，在数据库层面进行聚合
     *
     * @param bdId          销售ID
     * @param custName      客户名称（模糊匹配）
     * @param custType      客户类型
     * @param spuGroupList  商品分组列表
     * @param sortField     排序字段
     * @param sortDirection 排序方向（ASC/DESC）
     * @return 客户维度的品类推广数据
     */
    List<CategoryPromotionDTO> getCategoryPromotionByCustomer(
            @Param("bdId") Long bdId,
            @Param("custName") String custName,
            @Param("custType") String custType,
            @Param("spuGroupList") List<String> spuGroupList,
            @Param("sortField") String sortField,
            @Param("sortDirection") String sortDirection);

    /**
     * 获取门店维度的品类推广数据，按商品分组聚合
     *
     * @param custId        客户ID
     * @param bdId          销售ID
     * @param spuGroupList  商品分组列表
     * @param sortField     排序字段
     * @param sortDirection 排序方向（ASC/DESC）
     * @return 门店维度的品类推广数据
     */
    List<CategoryPromotionDTO> getCategoryPromotionByMerchant(
            @Param("custId") Long custId,
            @Param("custType") String custType,
            @Param("bdId") Long bdId,
            @Param("spuGroupList") List<String> spuGroupList,
            @Param("sortField") String sortField,
            @Param("sortDirection") String sortDirection);
}