package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.M1PerformanceComm;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 操作【m1_performance_comm(M1绩效表)】的Mapper
 * 
 * <AUTHOR>
 */
public interface M1PerformanceCommMapper {

    /**
     * 根据M1 ID查询
     * 
     * @param m1Id M1的id
     * @param custType 客户类型（可选）
     * @param workZone 销售区域（可选）
     * @return M1绩效信息
     */
    List<M1PerformanceComm> selectByM1Id(@Param("m1Id") Long m1Id, @Param("custType") String custType, @Param("workZone") String workZone);

}
