package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmBdVisitCust;
import org.apache.ibatis.annotations.Param;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.List;

public interface CrmBdVisitCustMapper {

    /**
     * 根据日期标签, bdId和拜访类型查询有效(is_effective_visit='是' and visit_bd_id IS NOT NULL)的拜访记录
     */
    List<CrmBdVisitCust> selectEffectiveByDateTagAndTypeAndBdIdIn(@NotNull @Param("dateTag") Integer dateTag,
                                                                  @NotNull @Param("visitType") String visitType,
                                                                  @Nullable @Param("visitBdIdCollection") Collection<Long> bdIds);

}