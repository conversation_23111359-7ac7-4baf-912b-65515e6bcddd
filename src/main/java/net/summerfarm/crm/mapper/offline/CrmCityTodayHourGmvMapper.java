package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmCityTodayHourGmv;
import net.summerfarm.crm.model.query.salesdata.CityAreaQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmCityTodayHourGmvMapper {

    CrmCityTodayHourGmv sumTodayDataByCitiesAndDateTag(@Param("cities") List<CityAreaQuery> cities, @Param("dateTag") Integer dateTag);
}