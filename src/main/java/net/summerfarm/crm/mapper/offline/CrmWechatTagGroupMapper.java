package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmWechatTagGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmWechatTagGroupMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmWechatTagGroup record);

    int insertSelective(CrmWechatTagGroup record);

    CrmWechatTagGroup selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmWechatTagGroup record);

    int updateByPrimaryKey(CrmWechatTagGroup record);

    List<CrmWechatTagGroup> selectByGroupNameStatus(@Param("dayTag") Integer dayTag,
                                                    @Param("groupName") String groupName, @Param("type")Integer type);
}