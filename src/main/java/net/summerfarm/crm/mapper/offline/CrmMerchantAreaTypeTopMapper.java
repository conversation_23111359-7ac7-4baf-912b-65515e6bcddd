package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmMerchantAreaTypeTop;
import net.summerfarm.crm.model.query.MerchantDetailQuery;

/**
* <AUTHOR>
* @description 针对表【crm_merchant_area_type_top(商户同区域同行业品类,spu,top10)】的数据库操作Mapper
* @createDate 2022-11-03 17:32:34
* @Entity net.summerfarm.crm.model.domain.CrmMerchantAreaTypeTop
*/
public interface CrmMerchantAreaTypeTopMapper {

    /**
     * 根据查询条件查询top结果
     * @param merchantDetailQuery 查询条件
     * @return top结果
     */
    CrmMerchantAreaTypeTop selectByQuery(MerchantDetailQuery merchantDetailQuery);


}
