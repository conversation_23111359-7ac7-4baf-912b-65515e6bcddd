package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.model.domain.CustPbScoreComm;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 客户维度PB标品和利润积分 Mapper
 *
 * <AUTHOR>
 */
public interface CustPbScoreCommMapper {

    /**
     * 根据BD ID列表汇总PB标品数据
     * 
     * @param bdIds BD ID列表
     * @param custType 客户类型（可选）
     * @param bdWorkZone 销售区域（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return PB标品汇总数据
     */
    List<CustPbScoreComm> summaryPbByBdIds(@Param("bdIds") List<Long> bdIds,
                                           @Param("custType") String custType,
                                           @Param("bdWorkZone") String bdWorkZone,
                                           @Param("sortField") String sortField,
                                           @Param("sortDirection") SortDirectionEnum sortDirection);

    /**
     * 根据BD ID查询PB标品明细数据
     * 
     * @param bdId BD ID（必传）
     * @param mname 客户名称（可选，模糊查询）
     * @param custType 客户类型（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return PB标品明细数据
     */
    List<CustPbScoreComm> listPbByBdId(@Param("bdId") Long bdId,
                                       @Param("mname") String mname,
                                       @Param("custType") String custType,
                                       @Param("sortField") String sortField,
                                       @Param("sortDirection") SortDirectionEnum sortDirection);

    /**
     * 根据BD ID列表汇总利润积分数据
     * 
     * @param bdIds BD ID列表
     * @param custType 客户类型（可选）
     * @param bdWorkZone 销售区域（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return 利润积分汇总数据
     */
    List<CustPbScoreComm> summaryScoreByBdIds(@Param("bdIds") List<Long> bdIds,
                                              @Param("custType") String custType,
                                              @Param("bdWorkZone") String bdWorkZone,
                                              @Param("sortField") String sortField,
                                              @Param("sortDirection") SortDirectionEnum sortDirection);

    /**
     * 根据BD ID查询利润积分明细数据
     * 
     * @param bdId BD ID（必传）
     * @param mname 客户名称（可选，模糊查询）
     * @param custType 客户类型（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return 利润积分明细数据
     */
    List<CustPbScoreComm> listScoreByBdId(@Param("bdId") Long bdId,
                                          @Param("mname") String mname,
                                          @Param("custType") String custType,
                                          @Param("sortField") String sortField,
                                          @Param("sortDirection") SortDirectionEnum sortDirection);

}
