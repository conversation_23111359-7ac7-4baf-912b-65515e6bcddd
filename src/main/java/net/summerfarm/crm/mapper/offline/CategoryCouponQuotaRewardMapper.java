package net.summerfarm.crm.mapper.offline;


import net.summerfarm.crm.model.domain.CategoryCouponQuotaReward;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【category_coupon_quota_reward(bd品类券返现额度表)】的数据库操作Mapper
 * @createDate 2023-03-07 11:21:52
 * @Entity generator.domain.CategoryCouponQuotaReward
 */
public interface CategoryCouponQuotaRewardMapper {

    /**
     * 根据day tag查询记录
     *
     * @param dayTag
     * @return {@link List}<{@link CategoryCouponQuotaReward}>
     */
    List<CategoryCouponQuotaReward> selectByDayTag(@Param("dayTag") Integer dayTag);
}
