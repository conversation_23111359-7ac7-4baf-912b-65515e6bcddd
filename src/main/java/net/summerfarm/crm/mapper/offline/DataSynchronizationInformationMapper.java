package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface DataSynchronizationInformationMapper {

    /**
     * 根据id查询离线数据表数据变更日期
     * @param id id
     * @return 离线数据表数据变更信息
     */
    DataSynchronizationInformation selectByPrimaryKey(Long id);

    /**
     * 根据表名获取数据更新信息
     * @param tableName 表名
     * @return 数据更新信息
     */
    DataSynchronizationInformation selectByTableName(String tableName);

    List<DataSynchronizationInformation> selectByTableNames(List<String> tableNames, Integer dateFlag);
}