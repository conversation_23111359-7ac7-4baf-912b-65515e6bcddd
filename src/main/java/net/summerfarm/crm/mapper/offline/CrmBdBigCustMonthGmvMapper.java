package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmBdBigCustMonthGmv;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmBdBigCustMonthGmvMapper {

    /**
     * 查询bd的大客户业绩
     * @param adminId bdId
     * @param monthTah 时间标记
     * @return 大客户业绩
     */
    CrmBdBigCustMonthGmv selectByAdminId(@Param("adminId") Integer adminId,@Param("monthTah") Integer monthTah);

}