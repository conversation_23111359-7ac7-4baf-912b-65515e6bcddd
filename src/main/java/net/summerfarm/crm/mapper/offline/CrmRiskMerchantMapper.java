package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmRiskMerchant;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/11/23 10:24
 */
public interface CrmRiskMerchantMapper {
    /**
     * 查询当日风控池门店
     * @return {@link List}<{@link CrmRiskMerchant}>
     */
    List<CrmRiskMerchant> listByDayTag(@Param("dayTag")Integer dayTag);
}