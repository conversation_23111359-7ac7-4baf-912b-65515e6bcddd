package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmWecomUserSummary;
import net.summerfarm.crm.model.vo.weCom.WeComUserSummaryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23 15:14
 */
public interface CrmWecomUserSummaryMapper {

    CrmWecomUserSummary selectByPrimaryKey(Long id);

    /**
     * 统计企微客户
     *
     * @param bdId 销售 id
     * @return {@link WeComUserSummaryVo}
     */
    WeComUserSummaryVo summaryByBdId(@Param("bdId") List<Integer> bdId);
}