package net.summerfarm.crm.mapper.offline;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.crm.model.domain.CrmBdTodayHourGmv;
import org.apache.ibatis.annotations.Param;

public interface CrmBdTodayHourGmvMapper extends BaseMapper<CrmBdTodayHourGmv> {

    CrmBdTodayHourGmv selectOneByBdIdAndDateTagAndIsSameCity(@Param("bdId")Long bdId,@Param("dateTag")Integer dateTag,@Param("isSameCity")String isSameCity);
}