package net.summerfarm.crm.mapper.offline;

import io.swagger.models.auth.In;
import net.summerfarm.crm.model.domain.CrmMerchantDayAttribute;
import net.summerfarm.crm.model.vo.MerchantTagVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CrmMerchantDayAttributeMapper {

    /**
     * 查询商户属性信息
     * @param mId 商户id
     * @param dayTag 日期标记
     * @return 商户属性信息
     */
    CrmMerchantDayAttribute selectByPrimaryKey(@Param("mId") Long mId, @Param("dayTag") Integer dayTag);


    /**
     * 查询商户属性信息
     * @param mIds 商户ids
     * @param dayTag 日期标记
     * @return 商户属性信息
     */
    List<CrmMerchantDayAttribute> selectByMids(@Param("mIds") List<String> mIds, @Param("dayTag") Integer dayTag);

    /**
     * 选择bd所属merchant标签
     *
     * @param mIds    商户列表
     * @param dayTag 天标记
     * @param tagType   标签类型
     *
     * @return {@link List}<{@link MerchantTagVO}>
     */
    List<MerchantTagVO> selectBDMerchantTag(@Param("mIds") List<Long> mIds, @Param("dayTag") Integer dayTag,@Param("tagType") Integer tagType);

}




