package net.summerfarm.crm.mapper.offline;


import net.summerfarm.crm.model.domain.CrmCityTodayGmv;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/10/31 16:44
 */
public interface CrmCityTodayGmvMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmCityTodayGmv record);

    int insertSelective(CrmCityTodayGmv record);

    CrmCityTodayGmv selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmCityTodayGmv record);

    int updateByPrimaryKey(CrmCityTodayGmv record);

    /**
     * 城市当日销售数据
     *
     * @param cityList 城市列表
     * @return {@link List}<{@link CrmCityTodayGmv}>
     */
    List<CrmCityTodayGmv> listByCity(@Param("cityList")List<BdSalesCityVo> cityList,@Param("dayTag")Integer dayTag);

    /**
     * 统计城市的销售数据
     *
     * @return {@link CrmCityTodayGmv}
     */
    SalesAreaDataVo selectByCities(@Param("cityList")List<BdSalesCityVo> cityList,@Param("dayTag")Integer dayTag);
}