package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmSkuMonthGmv;
import net.summerfarm.crm.model.query.SkuMerchantQuery;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_sku_month_gmv(sku每月下单gmv表)】的数据库操作Mapper
* @createDate 2022-11-01 18:23:34
* @Entity net.summerfarm.crm.model.domain.CrmSkuMonthGmv
*/
public interface CrmSkuMonthGmvMapper {
    /**
     * 查询每个sku每月下单gmv信息
     * @param id 主键id
     * @return sku每月下单gmv信息
     */
    CrmSkuMonthGmv selectByPrimaryKey(Long id);

    /**
     * 根据区域查询下单商户sku汇总信息
     * @param skuMerchantQuery 查询条件
     * @return sku汇总信息
     */
    List<CrmSkuMonthGmvVO> selectByQuery(SkuMerchantQuery skuMerchantQuery);


}
