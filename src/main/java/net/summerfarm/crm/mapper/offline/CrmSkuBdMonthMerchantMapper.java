package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmSkuBdMonthMerchant;
import net.summerfarm.crm.model.query.SkuMerchantQuery;

/**
* <AUTHOR>
* @description 针对表【crm_sku_bd_month_merchant(每个sku每月下单商户id表)】的数据库操作Mapper
* @createDate 2022-11-02 17:44:34
* @Entity net.summerfarm.crm.model.domain.CrmSkuBdMonthMerchant
*/
public interface CrmSkuBdMonthMerchantMapper {
    /**
     * 查询每个sku每月下单商户id,bd私海维度
     * @param skuMerchantQuery 查询条件
     * @return sku每月下单商户id
     */
    CrmSkuBdMonthMerchant selectByQuery(SkuMerchantQuery skuMerchantQuery);
}
