package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.model.domain.CustCategoryPerformanceComm;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 客户维度品类推广绩效表现 Mapper
 * 
 * <AUTHOR>
 */
public interface CustCategoryPerformanceCommMapper {

    /**
     * 根据BD ID列表汇总客户品类推广绩效数据
     * 
     * @param bdIds BD ID列表，不能为空
     * @param custType 客户类型，可以为空
     * @param spuGroups 推广品类列表，可以为空
     * @param sortField 排序字段，可以为空
     * @param sortDirection 排序方向，可以为空
     * @return 汇总数据列表
     */
    List<CustCategoryPerformanceComm> summaryByBdIds(@Param("bdIds") List<Long> bdIds, @Param("custType") String custType,
                                                     @Param("spuGroups") List<String> spuGroups,
                                                     @Param("sortField") String sortField,
                                                     @Param("sortDirection") SortDirectionEnum sortDirection);

    /**
     * 根据BD ID汇总客户品类推广绩效数据
     *
     * @param bdId BD ID
     * @param custType 客户类型
     * @param mname 客户名称（模糊匹配）
     * @param spuGroups SPU组列表
     * @param sortField 排序字段
     * @param sortDirection 排序方向
     * @return 客户品类推广绩效数据列表
     */
    List<CustCategoryPerformanceComm> summaryByCustomer(@Param("bdId") Long bdId, @Param("custType") String custType,
                                                        @Param("mname") String mname, @Param("spuGroups") List<String> spuGroups,
                                                        @Param("sortField") String sortField,
                                                        @Param("sortDirection") SortDirectionEnum sortDirection);

    /**
     * 根据客户ID和BD ID查询客户的品类推广绩效详情
     *
     * @param custId 客户ID，必传
     * @param bdId BD ID，必传
     * @param custType 客户类型，可选
     * @param spuGroups SPU组列表，可选
     * @param sortField 排序字段，可选
     * @param sortDirection 排序方向，可选
     * @return 客户品类推广绩效详情列表
     */
    List<CustCategoryPerformanceComm> listByCustomer(@Param("custId") Long custId, @Param("bdId") Long bdId,
                                                     @Param("custType") String custType,
                                                     @Param("spuGroups") List<String> spuGroups,
                                                     @Param("sortField") String sortField,
                                                     @Param("sortDirection") SortDirectionEnum sortDirection);

    /**
     * 根据BD ID列表按SPU汇总品类推广绩效数据
     *
     * @param bdIds BD ID列表，必传
     * @param custType 客户类型，可选
     * @param sortField 排序字段，可选
     * @param sortDirection 排序方向，可选
     * @return SPU维度的汇总数据列表
     */
    List<CustCategoryPerformanceComm> summaryBySpuGroup(@Param("bdIds") List<Long> bdIds, @Param("custType") String custType,
                                                        @Param("sortField") String sortField,
                                                        @Param("sortDirection") SortDirectionEnum sortDirection);

    /**
     * 查询所有SPU列表（去重后）
     * @param bdIds 
     *
     * @return SPU列表
     */
    List<String> listSpuGroup(@Param("bdIds") List<Long> bdIds);

    /**
     * 查询指定BD下的客户列表
     *
     * @param bdId BD ID，必传
     * @param spuGroups SPU组列表，可选
     * @param custType 客户类型，可选
     * @param completionRewardStatus 完成奖励状态，可选
     * @return 客户ID列表
     */
    List<Long> listCustomerByBd(@Param("bdId") Long bdId, @Param("spuGroups") List<String> spuGroups,
                                @Param("custType") String custType,
                                @Param("completionRewardStatus") Integer completionRewardStatus);
}
