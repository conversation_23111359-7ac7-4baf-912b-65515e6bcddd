package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmBdTodayDayGmv;
import net.summerfarm.crm.model.vo.RankingListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【crm_bd_today_gmv(bd本日gmv表)】的数据库操作Mapper
 * @createDate 2022-11-01 14:50:18
 * @Entity net.summerfarm.crm.model.domain.CrmBdTodayDayGmv
 */
public interface CrmBdTodayGmvMapper {
    /**
     * 根据主键id查询
     * @param id 销售id
     * @return 销售每日gmv信息
     */
    CrmBdTodayDayGmv selectByPrimaryKey(Long id);

    /**
     * 根据销售id查询
     *
     * @param adminId 销售id
     * @param dayTag  数据更新日期标识
     * @return 销售每日gmv信息
     */
    CrmBdTodayDayGmv selectByAdminId(@Param("dayTag") Integer dayTag, @Param("adminId") Integer adminId);

    /**
     * 根据销售id查询
     *
     * @param adminId 销售id
     * @param dayTag  数据更新日期标识
     * @return {@link CrmBdTodayDayGmv}
     */
    List<CrmBdTodayDayGmv> listByAdminId(@Param("dayTag") Integer dayTag, @Param("adminId") List<Integer> adminId);

    /**
     * 查询收入排行榜
     *
     * @param  dayTag 日期标记
     * @return 排行榜数据
     */
    List<RankingListVO> selectIncomeRankingListVO(@Param("dayTag")Integer dayTag);
}
