package net.summerfarm.crm.mapper.offline;


import net.summerfarm.crm.model.domain.CrmCityDistrictDayGmv;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【crm_city_district_day_gmv(市区本月gmv表)】的数据库操作Mapper
 * @createDate 2023-05-16 15:03:33
 * @Entity generator.domain.CrmCityDistrictDayGmv
 */
public interface CrmCityDistrictDayGmvMapper {

    /**
     * 按城市和地区选择
     *
     * @param city     城市
     * @param district 区
     * @param dayTag   天标记
     * @return {@link List}<{@link CrmCityDistrictDayGmv}>
     */
    List<CrmCityDistrictDayGmv> selectByCityAndDistrict(@Param("city") String city, @Param("district") List<String> district, @Param("dayTag") Integer dayTag);

    /**
     * 按城市和地区总和选择
     *
     * @param city     城市
     * @param district 区
     * @param dayTag   天标记
     * @return {@link CrmCityDistrictDayGmv}
     */
    CrmCityDistrictDayGmv selectByCityAndDistrictSum(@Param("city") String city, @Param("district") List<String> district, @Param("dayTag") Integer dayTag);
}
