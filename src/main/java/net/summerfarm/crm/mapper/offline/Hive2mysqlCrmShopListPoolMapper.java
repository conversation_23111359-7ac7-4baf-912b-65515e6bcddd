package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface Hive2mysqlCrmShopListPoolMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Hive2mysqlCrmShopListPool record);

    int insertSelective(Hive2mysqlCrmShopListPool record);

    Hive2mysqlCrmShopListPool selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Hive2mysqlCrmShopListPool record);

    int updateByPrimaryKey(Hive2mysqlCrmShopListPool record);

    List<Hive2mysqlCrmShopListPool> selectPage(@Param("offset") Integer offset, @Param("offSize")Integer offSize);

    int count();
}