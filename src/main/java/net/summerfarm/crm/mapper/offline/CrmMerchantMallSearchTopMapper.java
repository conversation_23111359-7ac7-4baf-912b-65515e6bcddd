package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmMerchantMallSearchTop;
import net.summerfarm.crm.model.query.MerchantDetailQuery;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_merchant_mall_search_top(商户商城搜索记录表)】的数据库操作Mapper
* @createDate 2022-11-02 10:57:48
* @Entity net.summerfarm.crm.model.domain.CrmMerchantMallSearchTop
*/
public interface CrmMerchantMallSearchTopMapper {

    /**
     * 查询商户商城操作记录
     * @param merchantDetailQuery 查询条件
     * @return 商城操作记录
     */
    List<CrmMerchantMallSearchTop> selectByQuery(MerchantDetailQuery merchantDetailQuery);


}
