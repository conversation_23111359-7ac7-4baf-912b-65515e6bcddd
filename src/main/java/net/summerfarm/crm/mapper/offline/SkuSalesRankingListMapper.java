package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.dto.SkuSalesRankingListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * SKU销售排行榜Mapper接口
 * 用于查询offline数据源中的SKU销售排行榜数据
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Repository
public interface SkuSalesRankingListMapper {

    /**
     * 根据日期标签和门店类型查询SKU销售排行榜
     *
     * @param dayTag 日期标签，格式：yyyyMMdd
     * @param merchantType 门店类型
     * @return SKU销售排行榜数据
     */
    SkuSalesRankingListDTO selectByDayTagAndMerchantType(
            @Param("dayTag") String dayTag, 
            @Param("merchantType") String merchantType);

    /**
     * 根据日期标签查询SKU销售排行榜（不限门店类型）
     *
     * @param dayTag 日期标签，格式：yyyyMMdd
     * @return SKU销售排行榜数据
     */
    SkuSalesRankingListDTO selectByDayTag(@Param("dayTag") String dayTag);
}