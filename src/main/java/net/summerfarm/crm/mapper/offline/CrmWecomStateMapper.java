package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmWecomState;
import net.summerfarm.crm.model.vo.weCom.WeComActivateStatusVo;
import net.summerfarm.crm.model.vo.weCom.WeComActivateVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/28 10:20
 */
public interface CrmWecomStateMapper {

    CrmWecomState selectByPrimaryKey(Long id);

    /**
     * 统计下属激活状态
     *
     * @param bdId 主管id
     * @return {@link WeComActivateVo}
     */
    WeComActivateVo selectActivateByBdId(@Param("bdId") List<Integer> bdId);

    /**
     * 查询bd激活状态
     *
     * @param bdId 销售 id
     * @return {@link List}<{@link WeComActivateStatusVo}>
     */
    List<WeComActivateStatusVo> selectStateByParentId(Integer bdId);

    /**
     * 查询bd激活状态
     *
     * @param bdId 销售 id
     * @return {@link List}<{@link WeComActivateStatusVo}>
     */
    List<WeComActivateStatusVo> selectStateByBdId(Integer bdId);
}