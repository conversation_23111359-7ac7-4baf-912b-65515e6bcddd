package net.summerfarm.crm.mapper.offline;

import net.summerfarm.crm.model.domain.CrmBdMonthGmv;
import net.summerfarm.crm.model.vo.AdminInfoVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmBdMonthGmvMapper {

    /**
     * 根据id查询
     * @param id id
     * @return 销售每月gmv信息
     */
    CrmBdMonthGmv selectByPrimaryKey(Long id);

    /**
     * 根据销售id查询
     * @param adminId 销售id
     * @param monthTag 数据更新日期标识
     * @return 销售每日gmv信息
     */
    AdminInfoVo selectByAdminId(@Param("adminId") Integer adminId, @Param("monthTag") Integer monthTag);
}