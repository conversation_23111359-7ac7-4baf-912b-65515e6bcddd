package net.summerfarm.crm.mapper.repository.performance;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.mapper.offline.CustPbScoreCommMapper;
import net.summerfarm.crm.model.domain.CustPbScoreComm;
import net.xianmu.common.exception.BizException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import java.util.List;

/**
 * 客户维度PB标品和利润积分 Repository
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
public class CustPbScoreCommRepository {

    @Autowired
    private CustPbScoreCommMapper custPbScoreCommMapper;

    /**
     * 根据BD ID列表汇总PB标品数据
     * 
     * @param bdIds BD ID列表
     * @param custType 客户类型（可选）
     * @param bdWorkZone 销售区域（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return PB标品汇总数据
     */
    public List<CustPbScoreComm> summaryPbByBdIds(List<Long> bdIds, String custType, String bdWorkZone, String sortField,
                                                  SortDirectionEnum sortDirection) {
        if (CollectionUtils.isEmpty(bdIds)) {
            throw new BizException("bdId列表不能为空");
        }
        return custPbScoreCommMapper.summaryPbByBdIds(bdIds, custType, bdWorkZone, sortField, sortDirection);
    }

    /**
     * 根据BD ID分页查询PB标品明细数据
     * 
     * @param bdId BD ID（必传）
     * @param mname 客户名称（可选，模糊查询）
     * @param custType 客户类型（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return
     */
    public PageInfo<CustPbScoreComm> listPbByBdId(Long bdId, String mname, String custType, String sortField,
                                                  SortDirectionEnum sortDirection, Integer pageNum, Integer pageSize) {
        if (bdId == null) {
            throw new BizException("bdId不能为空");
        }
        if (pageNum == null || pageSize == null) {
            throw new BizException("pageNum和pageSize不能为空");
        }

        PageInfo<CustPbScoreComm> pageInfo = PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() -> {
            custPbScoreCommMapper.listPbByBdId(bdId, mname, custType, sortField, sortDirection);
        });
        return pageInfo;
    }

    /**
     * 根据BD ID列表汇总利润积分数据
     * 
     * @param bdIds BD ID列表
     * @param custType 客户类型（可选）
     * @param bdWorkZone 销售区域（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return 利润积分汇总数据
     */
    public List<CustPbScoreComm> summaryScoreByBdIds(List<Long> bdIds, String custType, String bdWorkZone, String sortField,
                                                     SortDirectionEnum sortDirection) {
        if (CollectionUtils.isEmpty(bdIds)) {
            throw new BizException("bdId列表不能为空");
        }
        return custPbScoreCommMapper.summaryScoreByBdIds(bdIds, custType, bdWorkZone, sortField, sortDirection);
    }

    /**
     * 根据BD ID分页查询利润积分明细数据
     * 
     * @param bdId BD ID（必传）
     * @param mname 客户名称（可选，模糊查询）
     * @param custType 客户类型（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 利润积分明细数据分页结果
     */
    public PageInfo<CustPbScoreComm> listScoreByBdId(Long bdId, String mname, String custType, String sortField,
                                                     SortDirectionEnum sortDirection, Integer pageNum, Integer pageSize) {
        if (bdId == null) {
            throw new BizException("bdId不能为空");
        }
        if (pageNum == null || pageSize == null) {
            throw new BizException("pageNum和pageSize不能为空");
        }

        PageInfo<CustPbScoreComm> pageInfo = PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() -> {
            custPbScoreCommMapper.listScoreByBdId(bdId, mname, custType, sortField, sortDirection);
        });
        return pageInfo;
    }

}
