package net.summerfarm.crm.mapper.repository.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.SystemConstant;
import net.summerfarm.crm.facade.GoodsCenterQueryFacade;
import net.summerfarm.crm.facade.dto.ProductSkuBaseDTO;
import net.summerfarm.crm.enums.ProductRangeTypeEnum;
import net.summerfarm.crm.facade.dto.ProductSpuBaseDTO;
import net.summerfarm.crm.mapper.manage.MerchantDataMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.mapper.offline.SkuSalesRankingListMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.dto.BDTarget.MerchantDataDTO;
import net.summerfarm.crm.model.dto.RecommendContextDTO;
import net.summerfarm.crm.model.dto.SkuSalesRankingListDTO;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店数据Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Slf4j
@Repository
public class MerchantDataRepository {

    @Resource
    private MerchantDataMapper merchantDataMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private SkuSalesRankingListMapper skuSalesRankingListMapper;

    @Resource
    private GoodsCenterQueryFacade goodsCenterQueryFacade;
    /**
     * 获取门店完整数据
     * 封装门店基础数据和历史记录的查询逻辑
     *
     * @param mId     门店ID
     * @param targets
     * @return 门店数据DTO
     */
    public MerchantDataDTO getMerchantData(Long mId, List<BdDailyTargetDetail> targets) {
        if (mId == null) {
            log.warn("门店ID为空，无法获取门店数据");
            return null;
        }

        try {
            MerchantDataDTO merchantData = new MerchantDataDTO();
            merchantData.setMId(mId);
            Merchant merchant = merchantMapper.selectMerchantByMid(mId);
            if (merchant == null) {
                log.warn("未找到门店信息，门店ID: {}", mId);
                return null;
            }
            merchantData.setMerchantName(merchant.getMname());
            merchantData.setRegisterTime(merchant.getRegisterTime());
            // 查询门店基础数据
            merchantData.setLastLoginTime(merchantDataMapper.selectLastLoginTime(mId));
            merchantData.setIsNewCustomer(merchantDataMapper.isNewCustomer(mId));
            
            // 查询历史记录
            merchantData.setVisitRecords(merchantDataMapper.selectRecentVisitRecords(mId));
            merchantData.setOrderRecords(merchantDataMapper.selectRecentOrderRecords(mId));
            merchantData.setComplaintRecords(merchantDataMapper.selectRecentComplaintRecords(mId));

            if (!CollectionUtils.isEmpty(targets)) {
                merchantData.setDailyTargetDetails(targets.stream().map(target -> {
                    MerchantDataDTO.BdDailyTargetDetailDTO detailDTO = new MerchantDataDTO.BdDailyTargetDetailDTO();
                    detailDTO.setId(target.getId());
                    detailDTO.setBdDailyTargetId(target.getBdDailyTargetId());
                    detailDTO.setPriority(target.getPriority());
                    detailDTO.setTargetType(target.getTargetType());
                    detailDTO.setTargetName(target.getTargetName());
                    detailDTO.setTargetDate(target.getTargetDate());
                    detailDTO.setBusinessType(target.getBusinessType());
                    detailDTO.setIndicatorType(target.getIndicatorType());
                    detailDTO.setIndicatorStatus(target.getIndicatorStatus());
                    detailDTO.setIndicatorCurrentValue(target.getIndicatorCurrentValue());
                    detailDTO.setIndicatorExpectedValue(target.getIndicatorExpectedValue());
                    detailDTO.setCategoryName(target.getCategoryName());
                    detailDTO.setSku(target.getSku());
                    detailDTO.setSpu(target.getSpu());
                    return detailDTO;
                }).collect(Collectors.toList()));

                List<String> skuList = merchantData.getDailyTargetDetails().stream()
                        .map(MerchantDataDTO.BdDailyTargetDetailDTO::getSku)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(skuList)){
                    List<ProductSkuBaseDTO> skuBaseDTOList = goodsCenterQueryFacade.queryProductSkuBaseInfo(
                            SystemConstant.XM_TENANT_ID, skuList);
                    Map<String, ProductSkuBaseDTO> skuBaseMap = skuBaseDTOList.stream()
                            .collect(Collectors.toMap(ProductSkuBaseDTO::getSku, Function.identity()));
                    merchantData.getDailyTargetDetails().forEach(detail -> {
                        ProductSkuBaseDTO skuBase = skuBaseMap.get(detail.getSku());
                        if (skuBase != null) {
                            detail.setSkuName(skuBase.getTitle());
                        }
                    });
                }

                List<String> spuList = merchantData.getDailyTargetDetails().stream()
                        .map(MerchantDataDTO.BdDailyTargetDetailDTO::getSpu)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(spuList)){
                    List<ProductSpuBaseDTO> spuBaseDTOList = goodsCenterQueryFacade.queryProductSpuBaseInfo(
                            SystemConstant.XM_TENANT_ID, spuList, 1);
                    Map<String, ProductSpuBaseDTO> spuBaseMap = spuBaseDTOList.stream()
                            .collect(Collectors.toMap(ProductSpuBaseDTO::getSpu, Function.identity()));
                    merchantData.getDailyTargetDetails().forEach(detail -> {
                        ProductSpuBaseDTO spuBase = spuBaseMap.get(detail.getSpu());
                        if (spuBase != null) {
                            detail.setSpuName(spuBase.getTitle());
                        }
                    });
                }
            }

            log.debug("成功获取门店数据，门店ID: {}", mId);
            return merchantData;
        } catch (Exception e) {
            log.error("获取门店数据失败，门店ID: {}", mId, e);
            throw new RuntimeException("获取门店数据失败", e);
        }
    }

    /**
     * 查询门店类型排行榜商品（针对新客户推荐）
     * 从offline数据源获取SKU销售排行榜数据
     *
     * @param merchantType 门店类型
     * @param limit 查询数量限制
     * @return 排行榜商品列表
     */
    public List<RecommendContextDTO.ProductRankingDTO> getProductRankingByMerchantType(String merchantType, Integer limit) {
        try {
            // 获取当前日期标签（格式：yyyyMMdd）,减1天
            String dayTag = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            
            // 从offline数据源查询SKU排行榜
            SkuSalesRankingListDTO rankingData = skuSalesRankingListMapper.selectByDayTagAndMerchantType(dayTag, merchantType);
            if (rankingData == null || !StringUtils.hasText(rankingData.getSkuList())) {
                log.info("未找到门店类型[{}]在日期[{}]的SKU排行榜数据", merchantType, dayTag);
                return Collections.emptyList();

            }
            
            // 解析SKU列表并转换为ProductRankingDTO
            return convertSkuListToProductRanking(rankingData.getSkuList(), limit);
            
        } catch (Exception e) {
            log.error("从offline数据源获取SKU排行榜失败，使用原有数据源。门店类型：{}，错误：{}", merchantType, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 将SKU列表转换为ProductRankingDTO列表
     *
     * @param skuList SKU列表字符串，逗号分隔
     * @param limit 返回数量限制
     * @return ProductRankingDTO列表
     */
    private List<RecommendContextDTO.ProductRankingDTO> convertSkuListToProductRanking(String skuList, Integer limit) {
        if (!StringUtils.hasText(skuList)) {
            return Collections.emptyList();
        }
        
        List<String> skus = Arrays.stream(skuList.split(","))
                .filter(StringUtils::hasText)
                .map(String::trim)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skus)){
            return Collections.emptyList();
        }
        List<RecommendContextDTO.ProductRankingDTO> result = new ArrayList<>();
        
        int maxCount = limit != null ? Math.min(limit, skus.size()) : skus.size();

        List<ProductSkuBaseDTO> skuBaseInfoList = goodsCenterQueryFacade.queryProductSkuBaseInfo(
                SystemConstant.XM_TENANT_ID, skus);
        Map<String, ProductSkuBaseDTO> skuBaseInfoMap = skuBaseInfoList.stream()
                .collect(Collectors.toMap(ProductSkuBaseDTO::getSku, Function.identity(),
                        (oldVal, newVal) -> oldVal));

        for (int i = 0; i < maxCount; i++) {
            String sku = skus.get(i);
            ProductSkuBaseDTO skuBaseInfo = skuBaseInfoMap.get(sku);
            if (skuBaseInfo == null){
                continue;
            }

            RecommendContextDTO.ProductRankingDTO productRanking = new RecommendContextDTO.ProductRankingDTO();
            productRanking.setSku(sku);
            productRanking.setProductName(skuBaseInfo.getTitle()); // 临时产品名称，实际应从产品库查询
            productRanking.setSpecification(skuBaseInfo.getSpecification()); // 临时品类名称，实际应从产品库查询
            productRanking.setRanking(i + 1);

            result.add(productRanking);
        }
        
        return result;
    }

    /**
     * 查询门店常购商品列表（针对老客户推荐）
     *
     * @param mId 门店ID
     * @param limit 查询数量限制
     * @return 常购商品列表
     */
    public List<RecommendContextDTO.ProductPurchaseDTO> getFrequentProducts(Long mId, Integer limit) {
        return merchantDataMapper.selectFrequentProducts(mId, limit);
    }


    /**
     * 查询限定范围内的商品列表（支持7种查询类型）
     * 支持：全品类、PB商品、指定SKU、指定SPU、乳制品、非乳制品、水果
     *
     * @param mId 门店ID
     * @param rangeTypeCode 查询范围类型代码（1-7）
     * @param limitValue 限定值（对于指定SKU/SPU时使用）
     * @param limit 查询数量限制
     * @return 限定范围内商品列表
     */
    public List<RecommendContextDTO.ProductInRangeDTO> getProductsInRange(Long mId, Integer rangeTypeCode, String limitValue, Integer limit) {
        if (mId == null || rangeTypeCode == null || limit == null) {
            log.warn("参数不完整，无法获取商品列表。门店ID：{}，查询类型：{}，限制数量：{}", mId, rangeTypeCode, limit);
            return Collections.emptyList();
        }
        
        // 根据代码获取查询类型枚举
        ProductRangeTypeEnum rangeType = ProductRangeTypeEnum.getByCode(rangeTypeCode);
        if (rangeType == null) {
            log.warn("不支持的查询类型代码：{}", rangeTypeCode);
            return Collections.emptyList();
        }
        
        // 对于指定SKU和指定SPU，需要提供limitValue
        if ((rangeType == ProductRangeTypeEnum.SPECIFIC_SKU || rangeType == ProductRangeTypeEnum.SPECIFIC_SPU) 
            && !StringUtils.hasText(limitValue)) {
            log.warn("指定SKU或SPU查询时，limitValue不能为空。查询类型：{}", rangeType.getDesc());
            return Collections.emptyList();
        }
        
        try {
            // 获取门店的区域信息
            Merchant merchant = merchantMapper.selectMerchantByMid(mId);
            if (merchant == null || merchant.getAreaNo() == null) {
                log.warn("门店信息不完整，无法获取区域信息。门店ID：{}", mId);
                return Collections.emptyList();
            }
            
            // 获取查询开始日期（近一周）
            String startDate = LocalDateTime.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            // 调用Mapper查询
            return merchantDataMapper.selectProductsInRange(mId, rangeTypeCode, limitValue, merchant.getAreaNo(), startDate, limit);
            
        } catch (Exception e) {
            log.error("查询限定范围内商品列表失败。门店ID：{}，查询类型：{}，错误：{}", mId, rangeType.getDesc(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}