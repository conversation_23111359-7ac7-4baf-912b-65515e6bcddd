package net.summerfarm.crm.mapper.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.HistoryCustCategoryPerformanceMapper;
import net.summerfarm.crm.model.domain.HistoryCustCategoryPerformance;
import org.springframework.stereotype.Repository;

@Repository
public class HistoryCustCategoryPerformanceRepository extends CrudRepository<HistoryCustCategoryPerformanceMapper, HistoryCustCategoryPerformance> {
}
