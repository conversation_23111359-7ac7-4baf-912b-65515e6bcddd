package net.summerfarm.crm.mapper.repository.BDTarget;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.offline.BDTarget.BdVisitPlanIndicatorSyncMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class BdVisitPlanIndicatorSyncRepository extends CrudRepository<BdVisitPlanIndicatorSyncMapper, BdVisitPlanIndicatorSync> {

    @Resource
    private BdVisitPlanIndicatorSyncMapper bdVisitPlanIndicatorSyncMapper;

    /**
     * 根据多个条件查询同步数据
     * @param bdIds 销售ID列表
     * @param bdDailyTargetIds 日常目标ID列表
     * @param bdDailyTargetDetailIds 日常目标详情ID列表
     * @return 同步数据列表
     */
    public List<BdVisitPlanIndicatorSync> selectByMultipleConditions(
            List<Integer> bdIds,
            List<Long> bdDailyTargetIds,
            List<Long> bdDailyTargetDetailIds) {
        if (CollectionUtils.isEmpty(bdIds)
                || CollectionUtils.isEmpty(bdDailyTargetIds) || CollectionUtils.isEmpty(bdDailyTargetDetailIds)) {
            return Collections.emptyList();
        }
        return bdVisitPlanIndicatorSyncMapper.selectByMultipleConditions(bdIds, null, bdDailyTargetIds, bdDailyTargetDetailIds);
    }

    /**
     * 根据多个条件查询同步数据
     * @param bdIds 销售ID列表
     * @param mIds 门店ID列表
     * @param bdDailyTargetIds 日常目标ID列表
     * @param bdDailyTargetDetailIds 日常目标详情ID列表
     * @return 同步数据列表
     */
    public List<BdVisitPlanIndicatorSync> selectByMultipleConditions(
            List<Integer> bdIds,
            List<Long> mIds,
            List<Long> bdDailyTargetIds,
            List<Long> bdDailyTargetDetailIds) {
        if (CollectionUtils.isEmpty(bdIds) || CollectionUtils.isEmpty(mIds)
            || CollectionUtils.isEmpty(bdDailyTargetIds)) {
            return Collections.emptyList();
        }
        return bdVisitPlanIndicatorSyncMapper.selectByMultipleConditions(bdIds, mIds, bdDailyTargetIds, bdDailyTargetDetailIds);
    }

    /**
     * 根据门店ID列表查询同步数据
     *
     * @param merchantIds 门店ID列表
     * @return 同步数据列表
     */
    public List<BdVisitPlanIndicatorSync> selectSyncByMerchantIds(List<Long> merchantIds) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            log.warn("根据门店ID查询同步数据：门店ID列表为空");
            return null;
        }

        try {
            List<BdVisitPlanIndicatorSync> result = bdVisitPlanIndicatorSyncMapper.selectByMerchantIds(merchantIds);
            log.debug("根据门店ID查询同步数据，门店数量: {}, 结果数量: {}",
                    merchantIds.size(), result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据门店ID查询同步数据失败，门店数量: {}", merchantIds.size(), e);
            throw e;
        }
    }
}