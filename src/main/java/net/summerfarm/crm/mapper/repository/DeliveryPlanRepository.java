package net.summerfarm.crm.mapper.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.manage.DeliveryPlanMapper;
import net.summerfarm.crm.model.domain.DeliveryPlan;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class DeliveryPlanRepository extends CrudRepository<DeliveryPlanMapper, DeliveryPlan> {

    public List<DeliveryPlan> findByOrderNo(String orderNo) {
        return this.list(Wrappers.lambdaQuery(DeliveryPlan.class).eq(DeliveryPlan::getOrderNo, orderNo));
    };
}
