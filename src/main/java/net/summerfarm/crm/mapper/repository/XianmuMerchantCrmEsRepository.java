package net.summerfarm.crm.mapper.repository;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.es.mapper.XianmuMerchantCrmMapper;
import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import net.summerfarm.crm.model.query.objectiveManagement.NearbyPoiPotentialMerchantQuery;
import net.summerfarm.crm.model.query.objectiveManagement.RecommendPotentialMerchantQuery;
import net.summerfarm.crm.model.query.objectiveManagement.EsPotentialMerchantQuery;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * 仙目商户CRM ES仓储层
 * 封装ES查询逻辑，提供地理位置查询和潜力值排序功能
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Repository
public class XianmuMerchantCrmEsRepository {

    @Resource
    private XianmuMerchantCrmMapper xianmuMerchantCrmMapper;

    /**
     * 查询附近潜力值客户（分页）
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public PageInfo<XianmuMerchantCrm> queryNearbyPotentialMerchants(NearbyPoiPotentialMerchantQuery query) {
        EsPotentialMerchantQuery esQuery = EsPotentialMerchantQuery.fromNearbyQuery(query);
        return xianmuMerchantCrmMapper.queryPotentialMerchantsUnified(esQuery);
    }

    /**
     * 查询推荐潜力值客户（分页）
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public PageInfo<XianmuMerchantCrm> queryRecommendPotentialMerchants(RecommendPotentialMerchantQuery query) {
        // 推荐查询默认按潜力值从高到低排序
        EsPotentialMerchantQuery esQuery = EsPotentialMerchantQuery.fromRecommendQuery(query, 1);
        return xianmuMerchantCrmMapper.queryPotentialMerchantsUnified(esQuery);
    }




    





    

}