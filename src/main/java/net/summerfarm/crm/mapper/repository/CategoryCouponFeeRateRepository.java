package net.summerfarm.crm.mapper.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.CategoryCouponFeeRateMapper;
import net.summerfarm.crm.model.domain.CategoryCouponFeeRate;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class CategoryCouponFeeRateRepository extends CrudRepository<CategoryCouponFeeRateMapper, CategoryCouponFeeRate> {
}
