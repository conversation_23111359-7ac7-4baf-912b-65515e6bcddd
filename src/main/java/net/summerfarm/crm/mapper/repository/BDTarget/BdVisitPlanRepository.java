package net.summerfarm.crm.mapper.repository.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.BDTarget.BdVisitPlanMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlan;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanUpdate;
import net.summerfarm.crm.model.query.objectiveManagement.BdVisitPlanListQuery;
import net.summerfarm.crm.model.vo.objectiveManagement.BdVisitPlanSummaryVO;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

/**
 * BD拜访计划Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdVisitPlanRepository {

    @Resource
    private BdVisitPlanMapper bdVisitPlanMapper;

    @Resource
    private BdVisitPlanIndicatorRepository bdVisitPlanIndicatorRepository;

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录对象
     */
    public BdVisitPlan selectByPrimaryKey(Long id) {
        if (id == null) {
            log.warn("根据主键查询拜访计划：主键为空");
            return null;
        }
        
        try {
            BdVisitPlan result = bdVisitPlanMapper.selectByPrimaryKey(id);
            log.debug("根据主键查询拜访计划，主键: {}, 结果: {}", id, result != null ? "找到" : "未找到");
            return result;
        } catch (Exception e) {
            log.error("根据主键查询拜访计划失败，主键: {}", id, e);
            throw e;
        }
    }

    /**
     * 根据查询条件查询记录列表
     *
     * @param query 查询条件
     * @return 记录列表
     */
    public List<BdVisitPlan> list(BdVisitPlanQuery query) {
        try {
            List<BdVisitPlan> result = bdVisitPlanMapper.list(query);
            log.debug("根据条件查询拜访计划列表，结果数量: {}", result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据条件查询拜访计划列表失败", e);
            throw e;
        }
    }

    /**
     * 选择性更新记录
     *
     * @param record 更新对象
     * @return 影响行数
     */
    public int updateByPrimaryKeySelective(BdVisitPlanUpdate record) {
        if (record == null || record.getId() == null) {
            log.warn("选择性更新拜访计划：更新对象或主键为空");
            return 0;
        }
        
        try {
            int result = bdVisitPlanMapper.updateByPrimaryKeySelective(record);
            log.debug("选择性更新拜访计划，主键: {}, 影响行数: {}", record.getId(), result);
            return result;
        } catch (Exception e) {
            log.error("选择性更新拜访计划失败，主键: {}", record.getId(), e);
            throw e;
        }
    }

    /**
     * 批量插入记录
     *
     * @param records 记录列表
     * @return 影响行数
     */
    public int insertBatch(List<BdVisitPlan> records) {
        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量插入拜访计划：记录列表为空");
            return 0;
        }
        
        try {
            int result = bdVisitPlanMapper.insertBatch(records);
            log.debug("批量插入拜访计划，记录数量: {}, 影响行数: {}", records.size(), result);
            return result;
        } catch (Exception e) {
            log.error("批量插入拜访计划失败，记录数量: {}", records.size(), e);
            throw e;
        }
    }

    /**
     * 根据门店ID和拜访目标ID批量更新门店潜力值
     *
     * @param updateList 拜访明细
     * @return 影响行数
     */
    public int batchUpdatePotentialValueByMerchantIdAndTargetId(List<BdVisitPlanUpdate> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            log.warn("批量更新拜访计划门店潜力值：更新列表为空");
            return 0;
        }
        
        try {
            // 循环调用单条更新
            int totalUpdateCount = 0;
            for (BdVisitPlanUpdate update : updateList) {
                try {
                    int updateCount = bdVisitPlanMapper.updatePotentialValueByMerchantIdAndTargetId(update);
                    totalUpdateCount += updateCount;
                } catch (Exception e) {
                    log.error("更新拜访计划门店潜力值失败，门店ID: {}, 拜访目标ID: {}, 潜力值: {}", 
                            update.getMId(), update.getBdDailyTargetId(), update.getMerchantPotentialValue(), e);
                    // 继续处理其他记录，不中断整个批量操作
                }
            }
            
            log.info("批量更新拜访计划门店潜力值完成，更新数量: {}, 实际更新数: {}", 
                    updateList.size(), totalUpdateCount);
            
            return totalUpdateCount;
            
        } catch (Exception e) {
            log.error("批量更新拜访计划门店潜力值失败，更新数量: {}", updateList.size(), e);
            throw e;
        }
    }

    /**
     * 根据销售ID和拜访日期查询已存在的拜访计划
     *
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @return 拜访计划列表
     */
    public List<BdVisitPlan> selectByBdIdAndVisitDate(Integer bdId, LocalDate visitDate) {
        if (bdId == null || visitDate == null) {
            log.warn("根据销售ID和拜访日期查询拜访计划：参数为空");
            return new ArrayList<>();
        }
        
        try {
            BdVisitPlanQuery query = new BdVisitPlanQuery();
            query.setBdId(bdId);
            query.setVisitDate(visitDate);
            
            List<BdVisitPlan> result = bdVisitPlanMapper.list(query);
            log.debug("根据销售ID和拜访日期查询拜访计划，销售ID: {}, 拜访日期: {}, 结果数量: {}", 
                    bdId, visitDate, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据销售ID和拜访日期查询拜访计划失败，销售ID: {}, 拜访日期: {}", bdId, visitDate, e);
            throw e;
        }
    }
    
    /**
     * 根据销售ID、拜访日期和门店ID列表查询已存在的拜访计划
     *
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @param merchantIds 门店ID列表
     * @return 拜访计划列表
     */
    public List<BdVisitPlan> selectByBdIdAndVisitDateAndMerchantIds(Integer bdId, LocalDate visitDate, List<Long> merchantIds) {
        if (bdId == null || visitDate == null || CollectionUtils.isEmpty(merchantIds)) {
            log.warn("根据销售ID、拜访日期和门店ID列表查询拜访计划：参数为空");
            return new ArrayList<>();
        }
        
        try {
            BdVisitPlanQuery query = new BdVisitPlanQuery();
            query.setBdId(bdId);
            query.setVisitDate(visitDate);
            query.setMIdList(merchantIds);
            
            List<BdVisitPlan> result = bdVisitPlanMapper.list(query);
            log.debug("根据销售ID、拜访日期和门店ID列表查询拜访计划，销售ID: {}, 拜访日期: {}, 门店数量: {}, 结果数量: {}", 
                    bdId, visitDate, merchantIds.size(), result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据销售ID、拜访日期和门店ID列表查询拜访计划失败，销售ID: {}, 拜访日期: {}, 门店数量: {}", 
                    bdId, visitDate, merchantIds.size(), e);
            throw e;
        }
    }

    /**
     * 检查门店重复性
     *
     * @param existingMerchantIds 已存在的门店ID列表
     * @param inputMerchantIds 输入的门店ID列表
     * @return 重复的门店ID列表
     */
    public List<Long> findDuplicateMerchantIds(List<Long> existingMerchantIds, List<Long> inputMerchantIds) {
        if (CollectionUtils.isEmpty(existingMerchantIds) || CollectionUtils.isEmpty(inputMerchantIds)) {
            return new ArrayList<>();
        }
        
        return inputMerchantIds.stream()
                .filter(existingMerchantIds::contains)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 从拜访计划列表中提取门店ID列表
     *
     * @param visitPlans 拜访计划列表
     * @return 门店ID列表
     */
    public List<Long> extractMerchantIds(List<BdVisitPlan> visitPlans) {
        if (CollectionUtils.isEmpty(visitPlans)) {
            return new ArrayList<>();
        }
        
        return visitPlans.stream()
                .map(BdVisitPlan::getMId)
                .filter(Objects::nonNull)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 影响行数
     */
    public int deleteByPrimaryKey(Long id) {
        if (id == null) {
            log.warn("根据主键删除拜访计划：主键为空");
            return 0;
        }
        
        try {
            int result = bdVisitPlanMapper.deleteByPrimaryKey(id);
            log.debug("根据主键删除拜访计划，主键: {}, 影响行数: {}", id, result);
            return result;
        } catch (Exception e) {
            log.error("根据主键删除拜访计划失败，主键: {}", id, e);
            throw e;
        }
    }

    /**
     * 根据主键列表批量逻辑删除记录
     *
     * @param ids 主键列表
     * @return 影响行数
     */
    public int batchLogicalDeleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("批量逻辑删除拜访计划：主键列表为空");
            return 0;
        }
        
        try {
            int result = bdVisitPlanMapper.batchLogicalDeleteByIds(ids);
            log.info("批量逻辑删除拜访计划完成，删除数量: {}, 影响行数: {}", ids.size(), result);

            // 先删除拜访计划指标
            bdVisitPlanIndicatorRepository.batchLogicalDeleteByVisitPlanIds(ids);

            return result;
        } catch (Exception e) {
            log.error("批量逻辑删除拜访计划失败，删除数量: {}", ids.size(), e);
            throw e;
        }
    }

    /**
     * 根据销售ID、门店ID和拜访日期查询拜访计划列表
     * 支持参数判空校验，确保查询的准确性和安全性
     *
     * @param mId 门店ID
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @return 拜访计划列表
     */
    public List<BdVisitPlan> selectByMIdAndBdIdAndVisitDate(Long mId, Integer bdId, LocalDate visitDate) {
        // 参数判空校验
        if (mId == null) {
            log.warn("根据门店ID、销售ID和拜访日期查询拜访计划：门店ID为空");
            return new ArrayList<>();
        }
        
        if (bdId == null) {
            log.warn("根据门店ID、销售ID和拜访日期查询拜访计划：销售ID为空");
            return new ArrayList<>();
        }
        
        if (visitDate == null) {
            log.warn("根据门店ID、销售ID和拜访日期查询拜访计划：拜访日期为空");
            return new ArrayList<>();
        }
        
        try {
            // 构建查询条件
            BdVisitPlanQuery query = new BdVisitPlanQuery();
            query.setMId(mId);
            query.setBdId(bdId);
            query.setVisitDate(visitDate);
            query.setIsDeleted(0);
            
            List<BdVisitPlan> result = bdVisitPlanMapper.list(query);
            log.debug("根据门店ID、销售ID和拜访日期查询拜访计划，门店ID: {}, 销售ID: {}, 拜访日期: {}, 结果数量: {}", 
                    mId, bdId, visitDate, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据门店ID、销售ID和拜访日期查询拜访计划失败，门店ID: {}, 销售ID: {}, 拜访日期: {}", 
                    mId, bdId, visitDate, e);
            throw e;
        }
    }
    
    /**
     * 更新拜访推荐话术
     * 根据拜访计划ID更新推荐话术字段
     *
     * @param visitPlanId 拜访计划ID
     * @param recommendScript 推荐话术内容
     * @return 影响行数
     */
    public int updateVisitRecommendScript(Long visitPlanId, String recommendScript) {
        if (visitPlanId == null) {
            log.warn("更新拜访推荐话术：拜访计划ID为空");
            return 0;
        }
        
        if (recommendScript == null || recommendScript.trim().isEmpty()) {
            log.warn("更新拜访推荐话术：推荐话术内容为空，拜访计划ID: {}", visitPlanId);
            return 0;
        }
        
        try {
            // 构建更新对象
            BdVisitPlanUpdate updateRecord = new BdVisitPlanUpdate();
            updateRecord.setId(visitPlanId);
            updateRecord.setVisitRecommendScript(recommendScript.trim());
            
            int result = bdVisitPlanMapper.updateByPrimaryKeySelective(updateRecord);
            log.debug("更新拜访推荐话术，拜访计划ID: {}, 话术长度: {}, 影响行数: {}", 
                    visitPlanId, recommendScript.trim().length(), result);
            return result;
        } catch (Exception e) {
            log.error("更新拜访推荐话术失败，拜访计划ID: {}, 话术长度: {}", 
                    visitPlanId, recommendScript != null ? recommendScript.trim().length() : 0, e);
            throw e;
        }
    }

    /**
     * 根据查询条件查询销售拜访计划列表
     *
     * @param query 查询条件
     * @return 拜访计划列表
     */
    public List<BdVisitPlan> listBdVisitPlanByBdIdAndVisitDate(BdVisitPlanListQuery query) {
        if (query == null || query.getBdId() == null || query.getVisitDate() == null) {
            throw new BizException("查询销售拜访计划列表时参数为空");
        }

        return bdVisitPlanMapper.listBdVisitPlan(query);
    }

    /**
     * 根据门店ID、销售ID和拜访日期更新拜访状态和跟进记录ID
     *
     * @param mId 门店ID
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @param visitStatus 拜访状态
     * @param followUpRecordId 跟进记录ID
     * @param originalVisitStatus 原始拜访状态
     * @return 影响行数
     */
    public int updateVisitStatusAndRecordIdByMidAndBdIdAndVisitDate(Long mId, Integer bdId, LocalDate visitDate, 
                                                                   Integer visitStatus, Long followUpRecordId,
                                                                   Integer originalVisitStatus) {
        if (mId == null || bdId == null || visitDate == null || visitStatus == null || originalVisitStatus == null) {
            throw new BizException("更新拜访状态和跟进记录ID：必要参数为空");
        }
        
        return bdVisitPlanMapper.updateVisitStatusAndRecordIdByMidAndBdIdAndVisitDate(
                mId, bdId, visitDate, visitStatus, followUpRecordId, originalVisitStatus);
    }

    /**
     * 汇总销售拜访计划数据
     * 根据销售ID和拜访日期汇总销售拜访计划
     *
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @return 销售拜访计划汇总信息
     */
    public BdVisitPlanSummaryVO summaryBdVisitPlan(Integer bdId, LocalDate visitDate) {
        if (bdId == null || visitDate == null) {
            throw new BizException("汇总销售拜访计划数据：参数为空");
        }

        return bdVisitPlanMapper.summaryBdVisitPlan(bdId, visitDate);
    }

}