package net.summerfarm.crm.mapper.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MerchantExtendsRepository {

    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    @Resource
    private MerchantMapper merchantMapper;



    public List<MerchantStoreAndExtendDTO> getAuthMerchantExtendDTO(List<Long> mIds, List<Integer> areaNos) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMIds(mIds);
        req.setAreaNos(areaNos);
        List<MerchantStoreAndExtendResp> merchantExtends = merchantQueryFacade.getMerchantExtends(req);
        if (CollUtil.isEmpty(merchantExtends)) {
            return Collections.emptyList();
        }
        List<MerchantStoreAndExtendDTO> merchantStoreAndExtendDTO = convert(merchantExtends);
        //补充信息
        List<MerchantVO> merchant = merchantMapper.selectByMids(mIds);
        MerchantStoreAndExtendDTO.wrapMerchant(merchant, merchantStoreAndExtendDTO);
        return merchantStoreAndExtendDTO;
    }

    public Map<Long,MerchantStoreAndExtendDTO> getMerchantMap(List<Long> mIds){
        if (CollectionUtil.isEmpty(mIds)){
            return new HashMap<>();
        }
        return getAuthMerchantExtendDTO(mIds, null).stream().collect(Collectors.toMap(MerchantStoreAndExtendDTO::getMId, merchant -> merchant));
    }


    public MerchantStoreAndExtendDTO getAuthMerchantExtendDTO(Long mId, List<Integer> areaNos) {
        MerchantStoreAndExtendResp merchantExtends = merchantQueryFacade.getAuthMerchantExtends(mId,areaNos);
        if (ObjectUtils.isEmpty(merchantExtends)) {
            return null;
        }
        MerchantStoreAndExtendDTO merchantStoreAndExtendDTO = convert(merchantExtends);
        //补充信息
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        if (!ObjectUtils.isEmpty(merchant)){
            merchantStoreAndExtendDTO.setDoorPic(merchant.getDoorPic());
            merchantStoreAndExtendDTO.setOperateStatus(merchant.getOperateStatus());
            merchantStoreAndExtendDTO.setMemberIntegral(merchant.getMemberIntegral());
            merchantStoreAndExtendDTO.setDirect(merchant.getDirect());
            merchantStoreAndExtendDTO.setGrade(merchant.getGrade());
            merchantStoreAndExtendDTO.setHouseNumber(merchant.getHouseNumber());
            merchantStoreAndExtendDTO.setLastOrderTime(merchant.getLastOrderTime());
            merchantStoreAndExtendDTO.setOpenid(merchant.getOpenid());
            merchantStoreAndExtendDTO.setRankId(merchant.getRankId());
            merchantStoreAndExtendDTO.setLoginTime(merchant.getLoginTime());
            merchantStoreAndExtendDTO.setPullBlackRemark(merchant.getPullBlackRemark());
            merchantStoreAndExtendDTO.setPullBlackOperator(merchant.getPullBlackOperator());
            merchantStoreAndExtendDTO.setInviteCode(merchant.getInvitecode());
            merchantStoreAndExtendDTO.setAuditUser(merchant.getAuditUser());
            merchantStoreAndExtendDTO.setAddress(merchant.getAddress());
            merchantStoreAndExtendDTO.setTradeArea(merchant.getTradeArea());
            merchantStoreAndExtendDTO.setTradeGroup(merchant.getTradeGroup());
            merchantStoreAndExtendDTO.setServer(merchant.getServer());
            merchantStoreAndExtendDTO.setShowPrice(merchant.getShowPrice());
            merchantStoreAndExtendDTO.setCluePool(merchant.getCluePool());
            merchantStoreAndExtendDTO.setCompanyBrand(merchant.getCompanyBrand());
            merchantStoreAndExtendDTO.setEnterpriseScale(merchant.getEnterpriseScale());
            merchantStoreAndExtendDTO.setMerchantType(merchant.getMerchantType());
            merchantStoreAndExtendDTO.setExamineType(merchant.getExamineType());
            merchantStoreAndExtendDTO.setDisplayButton(merchant.getDisplayButton());
        }
        return merchantStoreAndExtendDTO;
    }


    private List<MerchantStoreAndExtendDTO> convert(List<MerchantStoreAndExtendResp> merchantStoreAndExtendResps) {
        if (merchantStoreAndExtendResps == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAndExtendDTO> merchantStoreAndExtendDTOList = new ArrayList<>();
        for (MerchantStoreAndExtendResp merchantStoreAndExtendResp : merchantStoreAndExtendResps) {
            merchantStoreAndExtendDTOList.add(convert(merchantStoreAndExtendResp));
        }
        return merchantStoreAndExtendDTOList;
    }

    private MerchantStoreAndExtendDTO convert(MerchantStoreAndExtendResp merchantStoreAndExtendResp) {
        MerchantStoreAndExtendDTO merchantStoreAndExtendDTO = new MerchantStoreAndExtendDTO();
        merchantStoreAndExtendDTO.setOrganizationName(merchantStoreAndExtendResp.getOrganizationName());
        merchantStoreAndExtendDTO.setSize(merchantStoreAndExtendResp.getSize());
        merchantStoreAndExtendDTO.setAdminId(merchantStoreAndExtendResp.getAdminId());
        merchantStoreAndExtendDTO.setPopView(merchantStoreAndExtendResp.getPopView());
        merchantStoreAndExtendDTO.setChangePop(merchantStoreAndExtendResp.getChangePop());
        merchantStoreAndExtendDTO.setFirstLoginPop(merchantStoreAndExtendResp.getFirstLoginPop());
        merchantStoreAndExtendDTO.setDisplayButton(merchantStoreAndExtendResp.getDisplayButton());
        merchantStoreAndExtendDTO.setPreRegisterFlag(merchantStoreAndExtendResp.getPreRegisterFlag());
        merchantStoreAndExtendDTO.setMockLoginFlag(merchantStoreAndExtendResp.getMockLoginFlag());
        merchantStoreAndExtendDTO.setProvince(merchantStoreAndExtendResp.getProvince());
        merchantStoreAndExtendDTO.setCity(merchantStoreAndExtendResp.getCity());
        merchantStoreAndExtendDTO.setArea(merchantStoreAndExtendResp.getArea());
        merchantStoreAndExtendDTO.setPoiNote(merchantStoreAndExtendResp.getPoiNote());
        merchantStoreAndExtendDTO.setId(merchantStoreAndExtendResp.getId());
        merchantStoreAndExtendDTO.setTenantId(merchantStoreAndExtendResp.getTenantId());
        merchantStoreAndExtendDTO.setStoreName(merchantStoreAndExtendResp.getStoreName());
        merchantStoreAndExtendDTO.setType(merchantStoreAndExtendResp.getType());
        merchantStoreAndExtendDTO.setRegisterTime(merchantStoreAndExtendResp.getRegisterTime());
        merchantStoreAndExtendDTO.setStatus(merchantStoreAndExtendResp.getStatus());
        merchantStoreAndExtendDTO.setAuditRemark(merchantStoreAndExtendResp.getAuditRemark());
        merchantStoreAndExtendDTO.setRemark(merchantStoreAndExtendResp.getRemark());
        merchantStoreAndExtendDTO.setAuditTime(merchantStoreAndExtendResp.getAuditTime());
        merchantStoreAndExtendDTO.setCreateTime(merchantStoreAndExtendResp.getCreateTime());
        merchantStoreAndExtendDTO.setUpdateTime(merchantStoreAndExtendResp.getUpdateTime());
        merchantStoreAndExtendDTO.setBillSwitch(merchantStoreAndExtendResp.getBillSwitch());
        merchantStoreAndExtendDTO.setOnlinePayment(merchantStoreAndExtendResp.getOnlinePayment());
        merchantStoreAndExtendDTO.setBalanceAuthority(merchantStoreAndExtendResp.getBalanceAuthority());
        merchantStoreAndExtendDTO.setStoreNo(merchantStoreAndExtendResp.getStoreNo());
        merchantStoreAndExtendDTO.setRegionalId(merchantStoreAndExtendResp.getRegionalId());
        merchantStoreAndExtendDTO.setBusinessType(merchantStoreAndExtendResp.getBusinessType());
        merchantStoreAndExtendDTO.setMId(merchantStoreAndExtendResp.getMId());
        merchantStoreAndExtendDTO.setChannelCode(merchantStoreAndExtendResp.getChannelCode());
        merchantStoreAndExtendDTO.setAreaNo(merchantStoreAndExtendResp.getAreaNo());
        merchantStoreAndExtendDTO.setBusinessLine(merchantStoreAndExtendResp.getBusinessLine());
        return merchantStoreAndExtendDTO;
    }


}
