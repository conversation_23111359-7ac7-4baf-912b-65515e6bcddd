package net.summerfarm.crm.mapper.repository.job;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.github.yulichang.repository.JoinCrudRepository;
import net.summerfarm.crm.mapper.manage.CrmJobMerchantItemMapper;
import net.summerfarm.crm.model.domain.CrmJobMerchantItem;
import net.xianmu.common.cache.InMemoryCache;
import org.apache.ibatis.binding.MapperMethod;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class CrmJobMerchantItemRepository extends JoinCrudRepository<CrmJobMerchantItemMapper, CrmJobMerchantItem> {

    @InMemoryCache
    public List<String> queryJobItemByJobIdWithCache(Long jobId) {
        if (jobId == null) {
            return Collections.emptyList();
        }

        return this.lambdaQuery()
                .select(CrmJobMerchantItem::getItem)
                .eq(CrmJobMerchantItem::getJobId, jobId)
                .groupBy(CrmJobMerchantItem::getItem)
                .list().stream().map(CrmJobMerchantItem::getItem).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatch(Collection<? extends Wrapper<CrmJobMerchantItem>> ewList) {
        String sqlStatement = getSqlStatement(SqlMethod.UPDATE);
        return executeBatch(ewList, DEFAULT_BATCH_SIZE, (sqlSession, ew) -> {
            MapperMethod.ParamMap<Wrapper<CrmJobMerchantItem>> param = new MapperMethod.ParamMap<>();
            param.put(Constants.ENTITY, null);
            param.put(Constants.WRAPPER, ew);
            sqlSession.update(sqlStatement, param);
        });
    }
}
