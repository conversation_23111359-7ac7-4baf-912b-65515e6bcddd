package net.summerfarm.crm.mapper.repository.job;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.github.yulichang.repository.JoinCrudRepository;
import net.summerfarm.crm.mapper.manage.CrmJobMerchantDetailMapper;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import org.apache.ibatis.binding.MapperMethod;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

@Repository
public class CrmJobMerchantDetailRepository extends JoinCrudRepository<CrmJobMerchantDetailMapper, CrmJobMerchantDetail> {

    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatch(Collection<? extends Wrapper<CrmJobMerchantDetail>> ewList) {
        String sqlStatement = getSqlStatement(SqlMethod.UPDATE);
        return executeBatch(ewList, DEFAULT_BATCH_SIZE, (sqlSession, ew) -> {
            MapperMethod.ParamMap<Wrapper<CrmJobMerchantDetail>> param = new MapperMethod.ParamMap<>();
            param.put(Constants.ENTITY, null);
            param.put(Constants.WRAPPER, ew);
            sqlSession.update(sqlStatement, param);
        });
    }
}
