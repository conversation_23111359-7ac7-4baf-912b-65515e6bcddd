package net.summerfarm.crm.mapper.repository.BDTarget;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.BDTarget.BdDailyTargetDetailSyncMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailSync;
import org.springframework.stereotype.Repository;

@Repository
public class BdDailyTargetDetailSyncRepository extends CrudRepository<BdDailyTargetDetailSyncMapper, BdDailyTargetDetailSync> {
}