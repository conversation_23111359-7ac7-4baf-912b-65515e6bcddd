package net.summerfarm.crm.mapper.repository;

import net.summerfarm.crm.common.base.UserRoleIdConfig;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.facade.AuthUserQueryFacade;
import net.summerfarm.crm.mapper.manage.BdInfoExtMapper;
import net.summerfarm.crm.model.query.BdExtQuery;
import net.summerfarm.crm.model.vo.BdExtVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BdInfoExtRepository {
    //根据来源和role_id 查询 baseIds
    @Resource
    BdInfoExtMapper bdInfoExtMapper;
    @Resource
    AuthUserQueryFacade authUserQueryFacade;
    @Resource
    UserRoleIdConfig userRoleConfig;

    /**
     * @param bdExtQuery
     * @return
     */
    public List<BdExtVO> selectBdInfo(BdExtQuery bdExtQuery) {
        Integer infoType = bdExtQuery.getInfoType();
        List<Long> roleIds = getRoleIds(infoType);
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(roleIds);
            bdExtQuery.setBaseUserIds(baseUserIds);
        }
        return bdInfoExtMapper.selectBdInfo(bdExtQuery);
    }

    /**
     * <!--<if test="infoType != null and infoType == 0">
     * AND ar.role_id IN (1,5,13,20,74,89,92)
     * </if>
     * <if test="infoType != null and infoType == 1">
     * AND ar.role_id = 5
     * </if>
     * <if test="infoType != null and infoType == 5">
     * AND ar.role_id IN (13,20,74,89,92)
     * </if>-->
     */
    private List<Long> getRoleIds(Integer infoType) {
        if (infoType == null || !Arrays.asList(1, 5, 0).contains(infoType)) {
            return new ArrayList<>();
        }
        List<Integer> roleIds = new ArrayList<>();
        if (infoType == 0) {
            roleIds.addAll(CrmGlobalConstant.BD_ROLE_ID);
            roleIds.addAll(CrmGlobalConstant.SA_ROLE_ID);
            roleIds.addAll(CrmGlobalConstant.AREASA_ROLE_ID);
            roleIds.addAll(CrmGlobalConstant.SALESA_ROLE_ID);

        } else if (infoType == 1) {
            roleIds.addAll(CrmGlobalConstant.BD_ROLE_ID);
        } else if (infoType == 5) {
            roleIds.addAll(CrmGlobalConstant.AREASA_ROLE_ID);
            roleIds.addAll(CrmGlobalConstant.SALESA_ROLE_ID);
        }
        return roleIds.stream().map(Long::valueOf).collect(Collectors.toList());
    }

}
