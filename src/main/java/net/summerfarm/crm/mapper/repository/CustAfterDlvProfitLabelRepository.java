package net.summerfarm.crm.mapper.repository;

import net.summerfarm.crm.mapper.offline.CustAfterDlvProfitLabelMapper;
import net.summerfarm.crm.model.domain.CustAfterDlvProfitLabel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CustAfterDlvProfitLabelRepository {

    @Resource
    private CustAfterDlvProfitLabelMapper custAfterDlvProfitLabelMapper;

    public String selectDlvProfitLabelByCustIdAndNoSaas(Long custId) {
        CustAfterDlvProfitLabel custAfterDlvProfitLabel = custAfterDlvProfitLabelMapper.selectOneByCustIdAndNoSaas(custId);
        if (custAfterDlvProfitLabel == null) {
            // 如果没有利润标签，默认为B
            return "B";
        } else {
            return custAfterDlvProfitLabel.getDlvProfitGroup();
        }
    }
}
