package net.summerfarm.crm.mapper.repository;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.UserRoleIdConfig;
import net.summerfarm.crm.facade.AuthUserQueryFacade;
import net.summerfarm.crm.mapper.manage.AdminMapper;
import net.summerfarm.enums.AdminAuthExtendEnum;
import net.summerfarm.pojo.DO.AdminAuthExtend;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class AdminAuthExtendRepository {
    //根据来源和role_id 查询 baseIds
    @Resource
    AdminMapper adminMapper;
    @Resource
    AuthUserQueryFacade authUserQueryFacade;

    public AdminAuthExtend selectByAdminId(Integer type, Integer adminId) {
        //根据admin_id 找到user_base_id
        Long userBaseId = adminMapper.selectUserBaseIdById(adminId);
        if (userBaseId == null) {
            log.error("用户数据错误 adminId{}", adminId);
            throw new BizException("用户数据错误");
        }
        AuthUserAuthResp authUserAuthResp = authUserQueryFacade.selectByAdminId(type, userBaseId);
        if (authUserAuthResp == null) {
            return null;
        }
        return convert(adminId, type, authUserAuthResp);
    }

    private AdminAuthExtend convert(Integer adminId, Integer type, AuthUserAuthResp authUserAuthResp) {
        AdminAuthExtend extend = new AdminAuthExtend();
        extend.setAdminId(adminId);
        extend.setOpenid(authUserAuthResp.getAuthId());
        extend.setType(type);
        //dingding
        if (type.equals(AdminAuthExtendEnum.Type.DING_TALK.ordinal())) {
            extend.setUserId(authUserAuthResp.getThirdPartyId());
        }
        extend.setUnionId(authUserAuthResp.getThirdPartyId());
        return extend;
    }


}
