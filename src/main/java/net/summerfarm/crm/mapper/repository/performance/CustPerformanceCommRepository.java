package net.summerfarm.crm.mapper.repository.performance;

import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.mapper.offline.CustPerformanceCommMapper;
import net.summerfarm.crm.model.domain.CustPerformanceComm;
import net.xianmu.common.exception.BizException;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import java.time.LocalDate;
import java.util.List;

/**
 * 客户绩效Repository
 * 
 * <AUTHOR>
 */
@Repository
public class CustPerformanceCommRepository {

    @NacosValue(value = "${performance-v2.first.day.of.month:1}", autoRefreshed = true)
    private Integer firstDayOfMonth = 1;

    @Autowired
    private CustPerformanceCommMapper custPerformanceCommMapper;

    /**
     * 查询根据BD汇总的客户绩效信息
     * 
     * @param bdIds BD ID列表
     * @param custValueLabel 客户价值标签（可选）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @return
     * @throws BizException 当bdIds为null或空时抛出异常
     */
    public List<CustPerformanceComm> summaryByBdIds(List<Long> bdIds, String custValueLabel, String sortField,
                                                    SortDirectionEnum sortDirection) {
        if (CollectionUtils.isEmpty(bdIds)) {
            throw new BizException("bdId列表不能为空");
        }

        return custPerformanceCommMapper.summaryByBdIds(bdIds, custValueLabel, sortField, sortDirection, isFirstDayOfMonth());
    }

    /**
     * 根据BD ID分页查询客户绩效数据
     * 
     * @param bdId BD ID
     * @param custValueLabel 客户价值标签（可选）
     * @param mname 客户名称（可选，模糊匹配）
     * @param sortField 排序字段（可选）
     * @param sortDirection 排序方向（可选）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return
     * @throws BizException 当bdId为null时抛出异常
     */
    public PageInfo<CustPerformanceComm> listByBdId(Long bdId, String custValueLabel, String mname, String sortField,
                                                    SortDirectionEnum sortDirection, Integer pageNum, Integer pageSize) {
        if (bdId == null) {
            throw new BizException("bdId不能为空");
        }
        if (pageNum == null || pageSize == null) {
            throw new BizException("pageNum和pageSize不能为空");
        }
        PageInfo<CustPerformanceComm> pageInfo = PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() -> {
            custPerformanceCommMapper.listByBdId(bdId, custValueLabel, mname, sortField, sortDirection, isFirstDayOfMonth());
        });
        return pageInfo;
    }

    /**
     * 根据来源分页查询客户绩效数据
     * 
     * @param orderSource 来源
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 客户绩效数据列表
     */
    public PageInfo<CustPerformanceComm> listByOrderSource(String orderSource, Integer pageNum, Integer pageSize) {
        if (StringUtils.isBlank(orderSource)) {
            throw new BizException("来源不能为空");
        }
        if (pageNum == null || pageSize == null) {
            throw new BizException("pageNum和pageSize不能为空");
        }

        PageInfo<CustPerformanceComm> pageInfo = PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() -> {
            custPerformanceCommMapper.listByOrderSource(orderSource, isFirstDayOfMonth());
        });
        return pageInfo;
    }

    /**
     * 根据客户ID列表查询客户绩效数据
     * 
     * @param custIds 客户ID列表
     * @param orderSource 来源（可选）
     * @return 客户绩效数据列表
     */
    public List<CustPerformanceComm> listByCustIds(List<Long> custIds, String orderSource) {
        if (CollectionUtils.isEmpty(custIds)) {
            throw new BizException("客户ID列表不能为空");
        }

        return custPerformanceCommMapper.listByCustIds(custIds, orderSource, isFirstDayOfMonth());
    }

    private Boolean isFirstDayOfMonth() {
        return firstDayOfMonth != null && LocalDate.now().getDayOfMonth() == firstDayOfMonth;
    }

}
