package net.summerfarm.crm.mapper.repository.BDTarget;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.BDTarget.HighValueCustConfigSyncMapper;
import net.summerfarm.crm.model.domain.BDTarget.HighValueCustConfigSync;
import org.springframework.stereotype.Repository;

@Repository
public class HighValueCustConfigSyncRepository extends CrudRepository<HighValueCustConfigSyncMapper, HighValueCustConfigSync> {
}