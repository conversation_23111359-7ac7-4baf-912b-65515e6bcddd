package net.summerfarm.crm.mapper.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.github.pagehelper.StringUtil;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.facade.ProductsQueryFacade;
import net.summerfarm.crm.facade.converter.product.MarketItemInfoRespConvert;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.mapper.manage.ProductsMapper;
import net.summerfarm.crm.model.dto.CategoryProductDTO;
import net.summerfarm.crm.model.dto.CustomerAnalysisDTO;
import net.summerfarm.crm.model.query.MerchantDetailQuery;
import net.summerfarm.crm.model.query.SkuMerchantQuery;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;
import net.summerfarm.goods.client.resp.CategoryResp;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
public class ProductsRepository {

    @Resource
    ProductsQueryFacade productsQueryFacade;

    @Resource
    MerchantMapper merchantMapper;

    @Resource
    ProductsMapper productsMapper;


    /**
     * 根据名称精确查找商品
     *
     * @param skuMerchantQuery 类目信息
     * @return 商品信息
     */
    public List<CrmSkuMonthGmvVO> selectByQuery(SkuMerchantQuery skuMerchantQuery) {
        List<String> skus = new ArrayList<>();
        List<Long> pdIds = new ArrayList<>();
        if (!StringUtil.isEmpty(skuMerchantQuery.getSku())) {
            skus.add(skuMerchantQuery.getSku());
        }
        if (!CollectionUtils.isEmpty(skuMerchantQuery.getSkuList())) {
            skus.addAll(skuMerchantQuery.getSkuList());
        }
        if (!CollectionUtils.isEmpty(skuMerchantQuery.getPdIds())) {
            pdIds.addAll(skuMerchantQuery.getPdIds());
        }
        if (skuMerchantQuery.getPdId() != null) {
            pdIds.add(skuMerchantQuery.getPdId());
        }
        List<MarketItemInfoResp> marketItemInfoResps =
                productsQueryFacade.queryMarketList(skuMerchantQuery.getPdName(), skus, pdIds);
        if (CollectionUtil.isEmpty(marketItemInfoResps)) {
            return new ArrayList<>();
        }
        return marketItemInfoResps.stream().map(MarketItemInfoRespConvert::convert).collect(Collectors.toList());
    }


    /**
     * 选择分类中的前几个客户进行分析，并返回客户分析数据传输对象列表
     *
     * @param merchantDetailQuery 经营者详细信息查询对象
     * @return 客户分析数据传输对象列表
     */
    public List<CustomerAnalysisDTO> selectCategoryTop(MerchantDetailQuery merchantDetailQuery) {
        List<CustomerAnalysisDTO> customerAnalysisDTOS = merchantMapper.selectCategoryTop(merchantDetailQuery);
        if (CollectionUtil.isEmpty(customerAnalysisDTOS)) {
            return customerAnalysisDTOS;
        }
        List<Long> categoryIds = customerAnalysisDTOS.stream().map(CustomerAnalysisDTO::getCategoryId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(categoryIds)) {
            return customerAnalysisDTOS;
        }
        //货品中心查询类目 拿到类目信息

        Map<Long, String> collect = productsQueryFacade.queryByCategoryIds(categoryIds).stream().collect((Collectors.toMap(CategoryResp::getId, CategoryResp::getName)));
        if (CollectionUtil.isEmpty(collect)) {
            return customerAnalysisDTOS;
        }

        customerAnalysisDTOS.forEach(
                it -> {
                    if (it.getCategoryId() != null) {
                        it.setCategoryName(collect.get(it.getCategoryId()));
                    }
                }
        );
        return customerAnalysisDTOS;
    }

    /**
     * 商户常购商品
     *
     * @param merchantDetailQuery 查询条件
     * @return 商户常购商品清单
     */
    public List<CrmSkuMonthGmvVO> selectProductByQuery(MerchantDetailQuery merchantDetailQuery) {
        //这里查询的是以sku的分组
        List<CrmSkuMonthGmvVO> crmSkuMonthGmvVOS = productsMapper.selectProductByQuery(merchantDetailQuery);
        if (CollectionUtil.isEmpty(crmSkuMonthGmvVOS)) {
            return Collections.emptyList();
        }
        //根据sku查询spu Id
        List<String> skus = crmSkuMonthGmvVOS.stream().map(CrmSkuMonthGmvVO::getSku).filter(it -> !StringUtils.isEmpty(it)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skus)) {
            return crmSkuMonthGmvVOS;
        }
        SkuMerchantQuery skuMerchantQuery = new SkuMerchantQuery();
        skuMerchantQuery.setSkuList(skus);
        List<CrmSkuMonthGmvVO> querySkus = selectByQuery(skuMerchantQuery);
        if (CollectionUtil.isEmpty(querySkus)) {
            return crmSkuMonthGmvVOS;
        }
        //回写spu id
        Map<String, Long> sKuPidMap = querySkus.stream().collect(Collectors.toMap(CrmSkuMonthGmvVO::getSku, CrmSkuMonthGmvVO::getPdId));
        crmSkuMonthGmvVOS.forEach(
                it -> {
                    if (!StringUtils.isEmpty(it.getSku())) {
                        it.setPdId(sKuPidMap.get(it.getSku()));
                    }
                }
        );
        List<CrmSkuMonthGmvVO> havePIdList = crmSkuMonthGmvVOS.stream().filter(it -> it.getPdId() != null).collect(Collectors.toList());
        //根据pdID分组 去取第一个
        List<CrmSkuMonthGmvVO> outs = new ArrayList<>();
        Map<Long, List<CrmSkuMonthGmvVO>> collect = havePIdList.stream().collect(Collectors.groupingBy(CrmSkuMonthGmvVO::getPdId));
        collect.keySet().forEach(
                u -> {
                    if (collect.get(u).size() > 0) {
                        CrmSkuMonthGmvVO crmSkuMonthGmvVO = collect.get(u).get(0);
                        outs.add(crmSkuMonthGmvVO);
                    }
                }
        );
        return outs;
    }

    /**
     * 获取商户常购品类
     *
     * @param categoryIds ids
     * @return 商户常购品类
     */
    public List<CategoryResp> selectByCategoryQuery(List<Long> categoryIds) {
        if (CollectionUtil.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        return productsQueryFacade.queryByCategoryIds(categoryIds);
    }

    /**
     * 通过sku获取货品类别信息
     *
     * @param sku sku
     * @return {@link Integer}
     */
    public CategoryProductDTO getCategoryTypeBySku(String sku) {
        if (StringUtils.isEmpty(sku)) {
            return null;
        }
        List<MarketItemInfoResp> marketItemInfoResps =
                productsQueryFacade.queryMarketListBySkus(Collections.singletonList(sku));
        if (CollectionUtil.isEmpty(marketItemInfoResps)) {
            return null;
        }
        MarketItemInfoResp marketItemInfoResp = marketItemInfoResps.get(0);
        CategoryProductDTO categoryProductDTO = MarketItemInfoRespConvert.convertCategoryProductDTO(marketItemInfoResp);
        List<CategoryResp> categoryResps = selectByCategoryQuery(Collections.singletonList(marketItemInfoResp.getCategoryId()));
        if (CollectionUtil.isEmpty(categoryResps)) {
            return categoryProductDTO;
        }
        categoryProductDTO.setCategoryType(categoryResps.get(0).getType());
        return categoryProductDTO;
    }

    public Map<String,CrmSkuMonthGmvVO> getSkuBySkus(List<String> skus) {
        if (CollectionUtil.isEmpty(skus)) {
            return new HashMap<>();
        }
        List<MarketItemInfoResp> marketItemInfoResps =
                productsQueryFacade.queryMarketListBySkus(skus);
        return marketItemInfoResps.stream().map(MarketItemInfoRespConvert::convert).collect(Collectors.toMap(CrmSkuMonthGmvVO::getSku, Function.identity()));
    }
}
