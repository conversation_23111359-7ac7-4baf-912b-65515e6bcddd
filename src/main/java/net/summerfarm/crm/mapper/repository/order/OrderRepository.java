package net.summerfarm.crm.mapper.repository.order;

import net.summerfarm.crm.mapper.manage.OrdersMapper;
import net.summerfarm.crm.model.dto.followUpRelation.LastOrderDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
public class OrderRepository {

    @Autowired
    private OrdersMapper ordersMapper;

    /**
     * 根据mId列表获取当前日订单信息
     * @param mIds
     * @return
     */
    public List<LastOrderDTO> listCurrentDayOrderInfo(List<Long> mIds){
        if (CollectionUtils.isEmpty(mIds)) {
            return Collections.emptyList();
        }

        return ordersMapper.listCurrentDayOrderInfo(mIds);
    }

    /**
     * 根据mId列表获取当前日订单信息，以mId为key
     * @param mIds
     * @return
     */
    public Map<Long, LastOrderDTO> mapCurrentDayOrderInfo(List<Long> mIds){
        if (CollectionUtils.isEmpty(mIds)) {
            return Collections.emptyMap();
        }

        return ordersMapper.listCurrentDayOrderInfo(mIds).stream().collect(
                Collectors.toMap(LastOrderDTO::getMId, Function.identity(),
                        (oldValue, newValue) -> newValue));
    }
}
