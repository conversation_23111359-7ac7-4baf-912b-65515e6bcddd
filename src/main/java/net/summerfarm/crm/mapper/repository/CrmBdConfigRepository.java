package net.summerfarm.crm.mapper.repository;

import net.summerfarm.crm.facade.AuthUserQueryFacade;
import net.summerfarm.crm.mapper.manage.CrmBdConfigMapper;
import net.summerfarm.crm.model.vo.BdVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CrmBdConfigRepository {
    //根据来源和role_id 查询 baseIds
    @Resource
    CrmBdConfigMapper crmBdConfigMapper;
    @Resource
    AuthUserQueryFacade authUserQueryFacade;

    public List<BdVO> queryBdName(Boolean isExist, String bdName, List<Long> roleIds) {
        List<Long> baseUserIds = authUserQueryFacade.getUserBaseIdBySourceRoleIds(roleIds);
        return crmBdConfigMapper.queryBdName(isExist, bdName, baseUserIds);
    }

}
