package net.summerfarm.crm.mapper.repository.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.BDTarget.BdDailyTargetDetailMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailSync;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * BD每日目标明细Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdDailyTargetDetailRepository {

    @Resource
    private BdDailyTargetDetailMapper bdDailyTargetDetailMapper;

    /**
     * 批量更新指标当前值和状态
     * 过滤有效数据并执行循环更新操作
     *
     * @param syncList 同步数据列表
     * @return 影响的行数
     */
    public int batchUpdateIndicatorValueAndStatus(List<BdDailyTargetDetailSync> syncList) {
        if (CollectionUtils.isEmpty(syncList)) {
            log.warn("批量更新指标：同步数据列表为空");
            return 0;
        }

        // 过滤有效的同步数据（指标当前值不为空）
        List<BdDailyTargetDetailSync> validSyncList = syncList.stream()
                .filter(sync -> sync.getIndicatorCurrentValue() != null)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validSyncList)) {
            log.warn("批量更新指标：过滤后无有效数据，原始数据量：{}", syncList.size());
            return 0;
        }

        log.info("批量更新指标：原始数据量：{}，有效数据量：{}", syncList.size(), validSyncList.size());

        // 循环调用单条更新
        int totalAffectedRows = 0;
        for (BdDailyTargetDetailSync sync : validSyncList) {
            try {
                int affectedRows = bdDailyTargetDetailMapper.updateIndicatorValueAndStatus(sync);
                totalAffectedRows += affectedRows;
            } catch (Exception e) {
                log.error("更新指标失败，ID: {}, 当前值: {}", sync.getBdDailyTargetDetailId(), sync.getIndicatorCurrentValue(), e);
                // 继续处理其他记录，不中断整个批量操作
            }
        }
        
        log.info("批量更新指标完成：影响行数：{}", totalAffectedRows);
        return totalAffectedRows;
    }

    /**
     * 批量插入销售每日目标明细
     *
     * @param details 销售每日目标明细列表
     * @return 影响行数
     */
    public int batchInsert(List<BdDailyTargetDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            log.warn("批量插入目标明细：明细列表为空");
            return 0;
        }
        
        log.info("批量插入目标明细：数据量：{}", details.size());
        int affectedRows = bdDailyTargetDetailMapper.batchInsert(details);
        log.info("批量插入目标明细完成：影响行数：{}", affectedRows);
        return affectedRows;
    }

    /**
     * 根据销售每日目标ID列表查询每日目标明细
     *
     * @param bdDailyTargetIds 销售每日目标ID列表
     * @return 销售每日目标明细列表
     */
    public List<BdDailyTargetDetail> listByBdDailyTargetIds(List<Long> bdDailyTargetIds) {
        if (CollectionUtils.isEmpty(bdDailyTargetIds)) {
            log.warn("根据销售每日目标ID查询明细：目标ID列表为空");
            return Collections.emptyList();
        }
        
        return bdDailyTargetDetailMapper.listByBdDailyTargetIds(bdDailyTargetIds);
    }

    /**
     * 根据销售每日目标ID列表批量逻辑删除明细
     *
     * @param bdDailyTargetIds 销售每日目标ID列表
     * @return 影响行数
     */
    public int logicDeleteByBdDailyTargetIds(List<Long> bdDailyTargetIds) {
        if (CollectionUtils.isEmpty(bdDailyTargetIds)) {
            log.warn("根据销售每日目标ID列表批量逻辑删除明细：目标ID列表为空");
            return 0;
        }
        
        return bdDailyTargetDetailMapper.logicDeleteByBdDailyTargetIds(bdDailyTargetIds);
    }

    /**
     * 根据明细ID列表查询销售每日目标明细
     *
     * @param detailIds 明细ID列表
     * @return 销售每日目标明细列表
     */
    public List<BdDailyTargetDetail> listByDetailIds(List<Long> detailIds) {
        if (CollectionUtils.isEmpty(detailIds)) {
            log.warn("根据明细ID列表查询目标明细：明细ID列表为空");
            return Collections.emptyList();
        }
        
        return bdDailyTargetDetailMapper.listByDetailIds(detailIds);
    }

}