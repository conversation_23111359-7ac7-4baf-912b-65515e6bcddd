package net.summerfarm.crm.mapper.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.FruitPopCustValueLableMapper;
import net.summerfarm.crm.model.domain.FruitPopCustValueLable;
import org.springframework.stereotype.Repository;

@Repository
public class FruitPopCustValueLabelRepository extends CrudRepository<FruitPopCustValueLableMapper, FruitPopCustValueLable> {
}
