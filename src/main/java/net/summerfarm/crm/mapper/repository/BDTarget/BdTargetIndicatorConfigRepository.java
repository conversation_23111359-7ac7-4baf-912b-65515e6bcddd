package net.summerfarm.crm.mapper.repository.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.BDTarget.BdTargetIndicatorConfigMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfig;
import net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfigQuery;
import net.xianmu.common.cache.InMemoryCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.Collections;
import java.util.List;

/**
 * 销售目标指标配置Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdTargetIndicatorConfigRepository {

    @Autowired
    private BdTargetIndicatorConfigMapper bdTargetIndicatorConfigMapper;

    /**
     * 根据查询条件查询销售目标指标配置列表
     *
     * @param query 查询条件
     * @return 销售目标指标配置列表
     */
    public List<BdTargetIndicatorConfig> list(BdTargetIndicatorConfigQuery query) {
        if (query == null) {
            log.warn("查询销售目标指标配置：查询条件为空");
            return Collections.emptyList();
        }

        return bdTargetIndicatorConfigMapper.list(query);
    }

    /**
     * 查询所有销售目标指标配置
     *
     * @return 所有销售目标指标配置列表
     */
    @InMemoryCache(expiryTimeInSeconds = 30)
    public List<BdTargetIndicatorConfig> listAllConfigs() {
        return bdTargetIndicatorConfigMapper.listAllConfigs();
    }

}
