package net.summerfarm.crm.mapper.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.offline.SalesBdIncrementCategoryMtdMapper;
import net.summerfarm.crm.model.domain.SalesBdIncrementCategoryMtd;
import org.springframework.stereotype.Repository;

@Repository
public class SalesBdIncrementCategoryMtdRepository extends CrudRepository<SalesBdIncrementCategoryMtdMapper, SalesBdIncrementCategoryMtd> {
}
