package net.summerfarm.crm.mapper.repository.performance;

import net.summerfarm.crm.mapper.offline.M1PerformanceCommMapper;
import net.summerfarm.crm.model.domain.M1PerformanceComm;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import java.util.List;

/**
 * M1绩效Repository
 * 
 * <AUTHOR>
 */
@Repository
public class M1PerformanceCommRepository {

    @Autowired
    private M1PerformanceCommMapper m1PerformanceCommMapper;

    /**
     * 根据M1 ID查询绩效信息
     * 
     * @param m1Id M1的id
     * @param custType 客户类型（可选）
     * @param workZone 销售区域（可选）
     * @return M1绩效信息
     */
    public List<M1PerformanceComm> selectByM1Id(Long m1Id, String custType, String workZone) {
        if (m1Id == null) {
            throw new BizException("m1Id不能为空");
        }
        return m1PerformanceCommMapper.selectByM1Id(m1Id, custType, workZone);
    }
}
