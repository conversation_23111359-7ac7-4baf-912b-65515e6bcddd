package net.summerfarm.crm.mapper.repository.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.redis.CrmRedisCacheUtil;
import net.summerfarm.crm.mapper.manage.BDTarget.BdDailyTargetMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTarget;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetUpdate;
import net.summerfarm.crm.model.query.objectiveManagement.BdDailyTargetPageQuery;
import net.summerfarm.crm.model.vo.objectiveManagement.BdVisitConfigVO;
import net.xianmu.common.exception.BizException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;

/**
 * BD每日目标Repository
 * 负责数据访问抽象、复杂查询封装、数据转换
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BdDailyTargetRepository {

    @Resource
    private BdDailyTargetMapper bdDailyTargetMapper;
    @Resource
    private CrmRedisCacheUtil crmRedisCacheUtil;

    /**
     * 批量插入销售每日目标
     *
     * @param targets 销售每日目标列表
     * @return 影响行数
     */
    public int batchInsert(List<BdDailyTarget> targets) {
        if (targets == null || targets.isEmpty()) {
            return 0;
        }
        return bdDailyTargetMapper.batchInsert(targets);
    }

    /**
     * 根据销售ID列表和日期范围查询销售拜访目标
     *
     * @param bdIds 销售ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售拜访目标列表
     */
    public List<BdDailyTarget> selectByBdIdsAndDateRange(List<Integer> bdIds, LocalDate startDate, LocalDate endDate) {
        if (bdIds == null || bdIds.isEmpty() || startDate == null || endDate == null) {
            throw new BizException("销售id列表、开始日期、结束日期不能为空");
        }
        return bdDailyTargetMapper.selectByBdIdsAndDateRange(bdIds, startDate, endDate);
    }

    /*
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录对象
     */
    public BdDailyTarget selectById(Long id) {
        if (id == null) {
            log.warn("根据主键查询拜访目标：主键为空");
            return null;
        }

        return bdDailyTargetMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据主键列表批量查询记录
     *
     * @param ids 主键列表
     * @return 记录对象列表
     */
    public List<BdDailyTarget> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("根据主键列表查询拜访目标：主键列表为空");
            return Collections.emptyList();
        }

        return bdDailyTargetMapper.selectByIds(ids);
    }

    /**
     * 根据查询条件查询记录列表
     *
     * @param query 查询条件
     * @return 记录列表
     */
    public List<BdDailyTarget> list(BdDailyTargetQuery query) {
        if (query == null) {
            log.warn("查询拜访目标列表：查询条件为空");
            return Collections.emptyList();
        }
        
        try {
            List<BdDailyTarget> result = bdDailyTargetMapper.list(query);
            log.debug("查询拜访目标列表，查询条件: {}, 结果数量: {}", query, result != null ? result.size() : 0);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("查询拜访目标列表失败，查询条件: {}", query, e);
            throw e;
        }
    }

    /**
     * 根据销售ID和目标日期查询拜访目标
     *
     * @param bdId 销售ID
     * @param targetDate 目标日期
     * @return 拜访目标列表
     */
    public List<BdDailyTarget> selectByBdIdAndTargetDate(Integer bdId, LocalDate targetDate) {
        if (bdId == null || targetDate == null) {
            log.warn("根据销售ID和目标日期查询拜访目标：参数为空，bdId: {}, targetDate: {}", bdId, targetDate);
            return Collections.emptyList();
        }
        
        try {
            BdDailyTargetQuery query = new BdDailyTargetQuery();
            query.setBdId(bdId);
            query.setTargetDate(targetDate);
            query.setIsDeleted(0);
            
            List<BdDailyTarget> result = bdDailyTargetMapper.list(query);
            log.debug("根据销售ID和目标日期查询拜访目标，bdId: {}, targetDate: {}, 结果数量: {}", 
                    bdId, targetDate, result != null ? result.size() : 0);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据销售ID和目标日期查询拜访目标失败，bdId: {}, targetDate: {}", bdId, targetDate, e);
            throw e;
        }
    }

    /**
     * 分页查询每日销售目标
     *
     * @param query 分页查询条件
     * @return
     */
    public PageInfo<BdDailyTarget> listBdDailyTarget(BdDailyTargetPageQuery query) {
        if (query == null || query.getPageIndex() == null || query.getPageSize() == null) {
            throw new BizException("pageNum和pageSize不能为空");
        }
        PageInfo<BdDailyTarget> pageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize()).doSelectPageInfo(() -> {
            bdDailyTargetMapper.listBdDailyTarget(query);
        });
        return pageInfo;
    }
    
    /**
     * 分页查询销售每日目标
     * 根据查询条件进行分页查询，支持指定页码和页面大小
     *
     * @param query 查询条件
     * @param pageNo 页码（从1开始）
     * @param size 每页大小
     * @return 分页结果
     * @throws BizException 当分页参数为空时
     */
    public PageInfo<BdDailyTarget> listByQuery(BdDailyTargetQuery query, Integer pageNo, Integer size) {
        if (pageNo == null || size == null) {
            throw new BizException("pageNo和size不能为空");
        }
        if (pageNo < 1) {
            throw new BizException("pageNo必须大于0");
        }
        if (size < 1) {
            throw new BizException("size必须大于0");
        }
        
        log.debug("分页查询销售每日目标，pageNo: {}, size: {}, query: {}", pageNo, size, query);
        
        PageInfo<BdDailyTarget> pageInfo = PageHelper.startPage(pageNo, size).doSelectPageInfo(() -> {
            bdDailyTargetMapper.list(query);
        });
        
        log.debug("分页查询结果，总记录数: {}, 总页数: {}, 当前页: {}", 
                pageInfo.getTotal(), pageInfo.getPages(), pageInfo.getPageNum());
        
        return pageInfo;
    }

    /**
     * 获取拜访目标（单个）
     * 根据销售ID和拜访日期获取唯一的拜访目标，如果不存在则抛出异常
     *
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @return 拜访目标
     * @throws IllegalArgumentException 当未找到拜访目标时
     */
    public BdDailyTarget getDailyTarget(Integer bdId, LocalDate visitDate) {
        List<BdDailyTarget> dailyTargets = selectByBdIdAndTargetDate(bdId, visitDate);
        if (CollectionUtils.isEmpty(dailyTargets)) {
            log.warn("未找到销售{}在{}的拜访目标，无法生成拜访计划草案", bdId, visitDate);
            throw new IllegalArgumentException("未找到对应的拜访目标");
        }
        
        BdDailyTarget target = dailyTargets.get(0);
        log.debug("成功获取销售{}在{}的拜访目标，目标ID: {}", bdId, visitDate, target.getId());
        return target;
    }

    /**
     * 选择性插入记录
     *
     * @param record 记录对象
     * @return 影响行数
     */
    public int insertSelective(BdDailyTarget record) {
        if (record == null) {
            log.warn("插入拜访目标：记录对象为空");
            return 0;
        }
        
        try {
            int result = bdDailyTargetMapper.insertSelective(record);
            log.debug("插入拜访目标，记录: {}, 影响行数: {}", record, result);
            return result;
        } catch (Exception e) {
            log.error("插入拜访目标失败，记录: {}", record, e);
            throw e;
        }
    }

    /**
     * 选择性更新记录
     *
     * @param record 更新对象
     * @return 影响行数
     */
    public int updateByPrimaryKeySelective(BdDailyTargetUpdate record) {
        if (record == null || record.getId() == null) {
            log.warn("更新拜访目标：记录对象或主键为空");
            return 0;
        }
        
        try {
            int result = bdDailyTargetMapper.updateByPrimaryKeySelective(record);
            log.debug("更新拜访目标，记录: {}, 影响行数: {}", record, result);
            return result;
        } catch (Exception e) {
            log.error("更新拜访目标失败，记录: {}", record, e);
            throw e;
        }
    }

    /**
     * 根据主键列表批量逻辑删除记录
     *
     * @param ids 主键列表
     * @return 影响行数
     */
    public int batchLogicalDeleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("批量逻辑删除拜访目标：主键列表为空");
            return 0;
        }
        return bdDailyTargetMapper.batchLogicalDeleteByIds(ids);
    }

    /**
     * 更新拜访计划配置
     * 根据输入参数选择性更新线下拜访数量、线上拜访数量、交通方式
     *
     * @param targetId 拜访目标ID
     * @param visitOfflineCount 线下拜访数量
     * @param visitOnlineCount 线上拜访数量
     * @param trafficType 交通方式
     * @return 影响行数
     */
    public int updateVisitPlanConfig(Long targetId, Integer visitOfflineCount, Integer visitOnlineCount, Integer trafficType) {
        if (targetId == null) {
            log.warn("更新拜访计划配置失败，目标ID不能为空");
            return 0;
        }

        // 检查是否有需要更新的字段
        if (visitOfflineCount == null && visitOnlineCount == null && trafficType == null) {
            log.debug("无需更新拜访计划配置，所有字段均为空，目标ID: {}", targetId);
            return 0;
        }

        BdDailyTargetUpdate targetUpdate = new BdDailyTargetUpdate(targetId);
        if (visitOfflineCount != null) {
            targetUpdate.setVisitOfflineCount(visitOfflineCount);
        }
        if (visitOnlineCount != null) {
            targetUpdate.setVisitOnlineCount(visitOnlineCount);
        }
        if (trafficType != null) {
            targetUpdate.setTrafficType(trafficType);
        }

        int result = bdDailyTargetMapper.updateByPrimaryKeySelective(targetUpdate);
        log.info("更新拜访计划配置完成，目标ID: {}, 线下拜访数量: {}, 线上拜访数量: {}, 交通方式: {}, 影响行数: {}", 
                targetId, visitOfflineCount, visitOnlineCount, trafficType, result);
        return result;
    }

    /**
     * 查询拜访计划配置缓存key
     */
    public static final String BD_VISIT_CONFIG_CACHE_KEY = "crm_bd_visit_config_";


    /**
     * 查询拜访计划配置
     * @param bdId
     * @return
     */
    public BdVisitConfigVO queryBdVisitConfig(Integer bdId) {
        if (bdId == null) {
            log.warn("查询拜访计划配置失败，销售ID不能为空");
            return null;
        }

        String visitConfigCacheKey = BD_VISIT_CONFIG_CACHE_KEY + bdId;
        return crmRedisCacheUtil.getCacheObjectValueRefreshByHalfTime(
                visitConfigCacheKey,
                60 * 60 * 24 * 7, () -> {
                    BdDailyTargetQuery query = new BdDailyTargetQuery();
                    query.setBdId(bdId);
                    query.setIsDeleted(0);
                    query.setVisitOnlineCountIsNotNull(1);
                    PageInfo<BdDailyTarget> pageInfo = this.listByQuery(query, 1, 1);
                    if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
                        log.warn("查询拜访计划配置失败，未找到销售ID为 {} 的拜访计划配置", bdId);
                        return null;
                    }
                    List<BdDailyTarget> targets = pageInfo.getList();

                    BdVisitConfigVO result = new BdVisitConfigVO();
                    result.setBdId(bdId);
                    result.setVisitOfflineCount(targets.get(0).getVisitOfflineCount());
                    result.setVisitOnlineCount(targets.get(0).getVisitOnlineCount());
                    result.setTrafficType(targets.get(0).getTrafficType());
                    return result;
                }, BdVisitConfigVO.class);
    }

    /**
     * 更新配置
     * @param bdId
     * @param visitOfflineCount
     * @param visitOnlineCount
     * @param trafficType
     */
    public void updateBdVisitConfig(Integer bdId, Integer visitOfflineCount,
                                    Integer visitOnlineCount, Integer trafficType) {
        if (bdId == null || visitOfflineCount == null || visitOnlineCount == null || trafficType == null) {
            return;
        }

        String visitConfigCacheKey = BD_VISIT_CONFIG_CACHE_KEY + bdId;
        crmRedisCacheUtil.updateCacheObjectValue(
                visitConfigCacheKey,
                60 * 60 * 24 * 7, () -> {
                    BdVisitConfigVO result = new BdVisitConfigVO();
                    result.setBdId(bdId);
                    result.setVisitOfflineCount(visitOfflineCount);
                    result.setVisitOnlineCount(visitOnlineCount);
                    result.setTrafficType(trafficType);
                    return result;
                }, BdVisitConfigVO.class);
    }

    /**
     * 更新目标完成提醒
     * @param dailyTargetId
     * @param completeTargetDesc
     */
    public void updateTargetCompleteReminder(Long dailyTargetId, String completeTargetDesc) {
        if (dailyTargetId == null || completeTargetDesc == null) {
            return;
        }

        BdDailyTargetUpdate update = new BdDailyTargetUpdate(dailyTargetId);
        update.setTargetCompleteReminder(completeTargetDesc);
        bdDailyTargetMapper.updateByPrimaryKeySelective(update);
    }
}