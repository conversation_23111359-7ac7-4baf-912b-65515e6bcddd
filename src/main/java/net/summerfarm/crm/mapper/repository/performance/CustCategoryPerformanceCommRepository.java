package net.summerfarm.crm.mapper.repository.performance;

import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.mapper.offline.CustCategoryPerformanceCommMapper;
import net.summerfarm.crm.model.domain.CustCategoryPerformanceComm;
import net.xianmu.common.exception.BizException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.Collections;
import java.util.List;

/**
 * 客户维度品类推广绩效Repository
 * 
 * <AUTHOR>
 */
@Repository
public class CustCategoryPerformanceCommRepository {

    @Autowired
    private CustCategoryPerformanceCommMapper custCategoryPerformanceCommMapper;

    /**
     * 根据BD ID列表汇总客户品类推广绩效数据
     * 
     * @param bdIds BD ID列表，不能为空
     * @param custType 客户类型，可以为空
     * @param spuGroups 推广品类列表，可以为空
     * @param sortField 排序字段，可以为空
     * @param sortDirection 排序方向，可以为空
     * @return 汇总数据列表
     * @throws BizException 当bdIds为null或空时抛出异常
     */
    public List<CustCategoryPerformanceComm> summaryByBdIds(List<Long> bdIds, String custType, List<String> spuGroups,
                                                            String sortField, SortDirectionEnum sortDirection) {
        if (CollectionUtils.isEmpty(bdIds)) {
            throw new BizException("bdId列表不能为空");
        }

        return custCategoryPerformanceCommMapper.summaryByBdIds(bdIds, custType, spuGroups, sortField, sortDirection);
    }

    /**
     * 根据BD ID汇总客户品类推广绩效数据
     *
     * @param bdId BD ID
     * @param custType 客户类型
     * @param mname 客户名称（模糊匹配）
     * @param spuGroups SPU组列表
     * @param sortField 排序字段
     * @param sortDirection 排序方向
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 客户品类推广绩效数据列表
     */
    public PageInfo<CustCategoryPerformanceComm> summaryByCustomer(Long bdId, String custType, String mname,
                                                                   List<String> spuGroups, String sortField,
                                                                   SortDirectionEnum sortDirection, Integer pageNum,
                                                                   Integer pageSize) {
        if (bdId == null) {
            throw new BizException("bdId不能为空");
        }
        if (pageNum == null || pageSize == null) {
            throw new BizException("pageNum和pageSize不能为空");
        }
        PageInfo<CustCategoryPerformanceComm> pageInfo = PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() -> {
            custCategoryPerformanceCommMapper.summaryByCustomer(bdId, custType, mname, spuGroups, sortField, sortDirection);
        });
        return pageInfo;
    }

    /**
     * 根据客户ID和BD ID查询客户的品类推广绩效详情
     *
     * @param custId 客户ID，必传
     * @param bdId BD ID，必传
     * @param custType 客户类型，可选
     * @param spuGroups SPU组列表，可选
     * @param sortField 排序字段，可选
     * @param sortDirection 排序方向，可选
     * @return 客户品类推广绩效详情列表
     * @throws BizException 当custId或bdId为null时抛出异常
     */
    public List<CustCategoryPerformanceComm> listByCustomer(Long custId, Long bdId, String custType, List<String> spuGroups,
                                                            String sortField, SortDirectionEnum sortDirection) {
        if (custId == null) {
            throw new BizException("custId不能为空");
        }
        if (bdId == null) {
            throw new BizException("bdId不能为空");
        }

        return custCategoryPerformanceCommMapper.listByCustomer(custId, bdId, custType, spuGroups, sortField, sortDirection);
    }

    /**
     * 根据BD ID列表按SPU分页汇总品类推广绩效数据
     *
     * @param bdIds BD ID列表，必传
     * @param custType 客户类型，可选
     * @param sortField 排序字段，可选
     * @param sortDirection 排序方向，可选
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return SPU维度的汇总数据列表
     * @throws BizException 当bdIds为null或空时抛出异常
     */
    public PageInfo<CustCategoryPerformanceComm> summaryBySpuGroup(List<Long> bdIds, String custType, String sortField,
                                                                   SortDirectionEnum sortDirection, Integer pageNum,
                                                                   Integer pageSize) {
        if (CollectionUtils.isEmpty(bdIds)) {
            throw new BizException("bdId列表不能为空");
        }
        if (pageNum == null || pageSize == null) {
            throw new BizException("pageNum和pageSize不能为空");
        }

        PageInfo<CustCategoryPerformanceComm> pageInfo = PageHelper.startPage(pageNum, pageSize).doSelectPageInfo(() -> {
            custCategoryPerformanceCommMapper.summaryBySpuGroup(bdIds, custType, sortField, sortDirection);
        });
        return pageInfo;
    }

    /**
     * 查询所有SPU列表（去重后）
     * @param bdIds 
     *
     * @return SPU列表
     */
    public List<String> listSpuGroup(List<Long> bdIds) {
        if (CollectionUtils.isEmpty(bdIds)) {
            return Collections.emptyList();
        }
        return custCategoryPerformanceCommMapper.listSpuGroup(bdIds);
    }

    /**
     * 查询指定BD下的客户列表
     *
     * @param bdId BD ID，必传
     * @param spuGroups SPU组列表，可选
     * @param custType 客户类型，可选
     * @param completionRewardStatus 完成奖励状态，可选
     * @return 客户ID列表
     * @throws BizException 当bdId为null时抛出异常
     */
    public List<Long> listCustomerByBd(Long bdId, List<String> spuGroups, String custType, Integer completionRewardStatus) {
        if (bdId == null) {
            throw new BizException("bdId不能为空");
        }

        return custCategoryPerformanceCommMapper.listCustomerByBd(bdId, spuGroups, custType, completionRewardStatus);
    }
}
