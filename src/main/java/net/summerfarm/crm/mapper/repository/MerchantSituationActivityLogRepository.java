package net.summerfarm.crm.mapper.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import net.summerfarm.crm.mapper.manage.MerchantSituationActivityLogMapper;
import net.summerfarm.crm.model.domain.MerchantSituationActivityLog;
import net.summerfarm.crm.model.dto.MscActiveSpuDTO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Repository
public class MerchantSituationActivityLogRepository extends CrudRepository<MerchantSituationActivityLogMapper, MerchantSituationActivityLog> {

    @Resource
    private MerchantSituationActivityLogMapper merchantSituationActivityLogMapper;

    public List<MscActiveSpuDTO> selectActiveSpu(Long mId) {
        return merchantSituationActivityLogMapper.selectByMIdAndActiveEndDateAfter(mId, LocalDate.now());
    }
}
