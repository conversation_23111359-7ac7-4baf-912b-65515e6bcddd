package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCouponExpensePoolRange;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolExtDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmCouponExpensePoolRangeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmCouponExpensePoolRange record);

    int insertSelective(CrmCouponExpensePoolRange record);

    CrmCouponExpensePoolRange selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmCouponExpensePoolRange record);

    int updateByPrimaryKey(CrmCouponExpensePoolRange record);

    int batchInsert(@Param("list") List<CrmCouponExpensePoolRange> crmCouponExpensePoolRanges);

    void deleteByPoolId(@Param("poolId") Long poolId);

    List<CrmCouponExpensePoolExtDto> selectByPoolId(@Param("poolId")Long poolId);

    List<CrmCouponExpensePoolExtDto> selectByPoolIdObjKey(@Param("poolId")Long poolId, @Param("objKey") String objKey);

}