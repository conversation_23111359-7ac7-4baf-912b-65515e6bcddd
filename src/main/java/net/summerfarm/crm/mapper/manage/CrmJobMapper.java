package net.summerfarm.crm.mapper.manage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobWithCrmJobCompletionCriteriaList;
import net.summerfarm.crm.model.query.crmjob.CrmJobQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

public interface CrmJobMapper extends BaseMapper<CrmJob> {
    int insertSelective(CrmJob crmJob);

    int updateById(@Param("updated") CrmJob updated, @Param("id") Long id);

    /**
     * 根据任务id集合更新任务状态, 只更新不在statusCollection里的任务
     * 用于批量取消任务,所以记一下更新人
     */
    @Deprecated
    int updateStatusAndUpdaterByIdInAndStatusNotIn(@Param("updatedStatus") Integer updatedStatus,
                                                   @Param("updatedUpdater") Long updatedUpdater,
                                                   @Param("idCollection") Collection<Long> idCollection,
                                                   @Param("statusCollection") Collection<Integer> statusCollection);

    /**
     * 用于把开始时间小于当前的任务状态改为进行中
     */
    @Deprecated
    int updateStatusByStartTimeLessThanOrEqualToAndStatusIn(@Param("updatedStatus") Integer updatedStatus,
                                                            @Param("minStartTime") LocalDateTime minStartTime,
                                                            @Param("statusCollection") Collection<Integer> statusCollection);

    /**
     * 用于把结束时间大于当前的任务状态改为已结束
     */
    @Deprecated
    int updateStatusByEndTimeLessThanOrEqualToAndStatusIn(@Param("updatedStatus") Integer updatedStatus,
                                                          @Param("maxEndTime") LocalDateTime maxEndTime,
                                                          @Param("statusCollection") Collection<Integer> statusCollection);

    CrmJob selectById(@Param("id") Long id);

    CrmJob selectOneByCouponId(@Param("couponId") Integer couponId);

    /**
     * 根据查询条件查询任务列表.
     * 当bdIds不为空时,查询属于bdIds的私海的门店的任务
     * 当cities不为空时,查询属于公海里城市在cities里的任务
     * 都不空时,查并集
     */
    List<Long> selectDistinctIdByQuery(@Param("crmJobQueryDTO") CrmJobQueryDTO crmJobQueryDTO,
                                       @Param("bdIds") List<Integer> bdIds,
                                       @Param("cities") List<String> cities);

    List<CrmJobWithCrmJobCompletionCriteriaList> selectWithCriteriaByIdsIn(@Param("idCollection") Collection<Long> idCollection);

    List<String> selectInProgressJobNameByMId(@Param("mId") Long mId);
}