package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.BdExt;
import net.summerfarm.crm.model.domain.CrmManageBd;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;
import net.summerfarm.crm.model.query.BdExtQuery;
import net.summerfarm.crm.model.query.SaveManageAreaQuery;
import net.summerfarm.crm.model.vo.BdExtVO;
import net.summerfarm.pojo.DO.Area;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmManageBdMapper {
    /**
     * 删除销售区域
     * @param id 区域id
     * @return ok
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 新增销售区域
     * @param record 销售区域
     * @return ok
     */
    int insert(CrmManageBd record);

    /**
     * 新增销售区域
     * @param record 销售区域
     * @return ok
     */
    int insertSelective(CrmManageBd record);

    /**
     * 新增销售区域
     * @param id 销售区域id
     * @return ok
     */
    CrmManageBd selectByPrimaryKey(Integer id);

    /**
     * 编辑销售区域
     * @param record 销售区域
     * @return ok
     */
    int updateByPrimaryKeySelective(CrmManageBd record);

    /**
     * 编辑销售区域
     * @param record 销售区域
     * @return ok
     */
    int updateByPrimaryKey(CrmManageBd record);

    /**
     * 已存在的区域
     * @param saveManageAreaQuery query
     * @return no
     */
    int existZoneName(SaveManageAreaQuery saveManageAreaQuery);

    List<String> queryZoneName(@Param("zoneName") String zoneName);

    /**
     * 根据登录人员权限获取区域列表,为空获取所有区域
     * @param bdExtQuery 登录人id
     * @return 区域列表
     */
    List<BdExtVO> selectZoneNameByAdminId(BdExtQuery bdExtQuery);

    /**
     * 获取用户管理区域内的城市ids
     * @param adminId 登录用户id
     * @return 城市ids
     */
    List<Integer> getAreaNoByAdmin(@Param("adminId") Integer adminId);

    /**
     * 根据城市编号获取该城市对应城市负责人
     * @param areaNo
     * @return
     */
    BdExt selectRealName(@Param("areaNo") Integer areaNo);

    /**
     * 查询已存在的城市
     * @return 已存在的城市
     */
    List<CrmBdAreaDTO> queryExistArea();

    /**
     * 查询区域下关联的城市
     * @param mbId 区域编号
     * @param areaNo 区域编号
     * @return 城市信息
     */
    List<Area> selectZoneInfoById(@Param("mbId") Integer mbId,@Param("areaNo") Integer areaNo);

    /**
     * 获取所有运营大区
     * @param largeStatus 开放状态:0否1是
     * @return 运营大区
     */
    List<BdExtVO> selectOperateLargeArea(@Param("largeStatus") Integer largeStatus,@Param("largeAreaNo") Integer largeAreaNo);

    /**
     * 根据运营大区编号获取销售有权限的区域
     * @param bdExtVO 查询条件
     * @return 运营区域
     */
    @RequiresDataPermission(originalField = "a.area_no")
    List<Area> selectOperateAreaByLargeNo(BdExtVO bdExtVO);

    /**
     * 获取销售主管管理的所有行政城市
     * @param adminIds 销售主管ids
     * @return 行政城市list
     */
    List<String> selectAdministrativeCityByAdminId(@Param("adminIds") List<Integer> adminIds);

    /**
     * 获取主管管理的所有销售
     * @return 销售信息
     */
    Set<BdExt> selectManegeSubordinate(@Param("adminIds") List<Integer> adminIds);

}