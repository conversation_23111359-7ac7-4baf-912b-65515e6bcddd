package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.MerchantSituationQuota;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface MerchantSituationQuotaMapper {

    MerchantSituationQuota queryOne(@Param("adminId") Integer adminId, @Param("areaNo") Integer areaNo);

    int updateQuota(MerchantSituationQuota merchantSituationQuota);

    int insertSituationQuota(MerchantSituationQuota merchantSituationQuota);

    int updateQuotaAll();

    /**
     * 获取登录bd已使用的客情额度
     * @param adminId 登录bd的id
     * @return 客情额度
     */
    BigDecimal selectAmount(Integer adminId);
}
