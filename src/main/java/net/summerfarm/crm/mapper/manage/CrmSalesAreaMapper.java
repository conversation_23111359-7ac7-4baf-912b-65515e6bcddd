package net.summerfarm.crm.mapper.manage;
import java.util.Collection;

import net.summerfarm.crm.model.domain.CrmSalesArea;
import net.summerfarm.crm.model.vo.areaConfig.SalesAreaVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/8/29 15:09
 */
public interface CrmSalesAreaMapper {
    int deleteByPrimaryKey(Integer id);


    int insertSelective(CrmSalesArea record);

    CrmSalesArea selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CrmSalesArea record);


    /**
     * 查询销售区域
     *
     * @param salesAreaName 销售区域名称
     * @return {@link CrmSalesArea}
     */
    CrmSalesArea selectBySalesAreaName(@Param("salesAreaName") String salesAreaName,@Param("salesAreaId") Integer salesAreaId);

    /**
     * 查询销售区域
     *
     * @param bdOrgId
     * @return {@link CrmSalesArea}
     */
    List<CrmSalesArea> selectByBdOrgId(@Param("bdOrgId") Integer bdOrgId);

    /**
     * 查询销售区域
     *
     * @param bdOrgId
     * @return {@link CrmSalesArea}
     */
    List<SalesAreaVo> selectByBdOrgIdList(@Param("bdOrgId") List<Integer> bdOrgId);

    /**
     * 销售区域列表
     *
     * @return {@link List}<{@link SalesAreaVo}>
     */
    List<SalesAreaVo> listSalesArea(@Param("bdOrgId") Integer bdOrgId);

    /**
     * 更新销售负责销售区域
     *
     * @param id      id
     * @param bdOrgId 销售orgid
     * @return int
     */
    int updateBdOrgById(@Param("id")Integer id,@Param("bdOrgId")Integer bdOrgId);
    /**
     * 更新销售负责销售区域
     *
     * @param idList      id
     * @param bdOrgId 销售orgid
     * @return int
     */
    int updateBdOrgByIdList(@Param("idList")List<Integer> idList,@Param("bdOrgId")Integer bdOrgId);

    /**
     * 查询指定省市区的销售区域
     *
     * @param province 省
     * @param city     城市
     * @param area     区域
     * @return {@link SalesAreaVo}
     */
    CrmSalesArea selectAreaByCity(@Param("province") String province, @Param("city") String city, @Param("area") String area);

    List<CrmSalesArea> selectAllByBdOrgIdIn(@Param("bdOrgIdCollection") Collection<Integer> bdOrgIdCollection);

}