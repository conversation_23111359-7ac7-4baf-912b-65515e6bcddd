package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCommissionMerchantLevel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmCommissionMerchantLevelMapper {

    /**
     * 插入信息
     * @param record 客户等级信息
     * @return 0.1
     */
    int insertSelective(CrmCommissionMerchantLevel record);

    CrmCommissionMerchantLevel selectByPrimaryKey(Long id);
    /**
     * 更新
     * @param record 客户等级信息
     * @return 0.1
     */
    int updateByPrimaryKeySelective(CrmCommissionMerchantLevel record);

    /**
     * 查询全部核心商户等级信息
     * @param merchantLevelType 是否是核心客户 0否1是
     * @param grade 城市等级
     * @return 核心商户等级信息
     */
    List<CrmCommissionMerchantLevel> selectAllCoreMerchantLevel(@Param("merchantLevelType") Integer merchantLevelType,@Param("grade") String grade);

    /**
     * 跟进城市查询其核心客户阈值
     * @param areaNo 城市no
     * @return 核心客户阈值
     */
    CrmCommissionMerchantLevel selectCoreMerchantByAreaNo(Integer areaNo);

    /**
     * id存在则更新,不存在则新增
     * @param record 数据
     * @return 0|1
     */
    int insertOrUpdateById(CrmCommissionMerchantLevel record);

}