package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.BdExt;
import net.summerfarm.crm.model.query.BdExtQuery;
import net.summerfarm.crm.model.vo.BdExtVO;
import net.summerfarm.pojo.DO.Admin;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BdInfoExtMapper {

    /**
     * 获取销售区域信息,权限信息
     * @param bdExtQuery  查询条件
     * @return 销售区域信息,权限信息
     */
    @RequiresDataPermission(originalField = "cba.area_no")
    List<BdExtVO> select(BdExtQuery bdExtQuery);

    /**
     * 获取销售区域信息
     * @param bdExt  查询条件
     * @return 销售区域信息
     */
    BdExt selectOne(BdExt bdExt);

    /**
     * 查询BD列表 仅根据销售激励配置
     * @param bdExtQuery 查询条件
     * @return BD列表
     */
    List<BdExtVO> selectBdInfo(BdExtQuery bdExtQuery);

    /**
     *  获取销售产品信息
     * @param mId 商户id
     * @param startTime 时间
     * @param endTime 结束时间
     * @return
     */
    int selectSkuNumByMid(@Param("mId") Integer mId,@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);

    /**
     * 获取销售信息
     * @param adminId 销售id
     * @return 销售信息
     */
    Admin selectByPrimaryKey(Integer adminId);

}