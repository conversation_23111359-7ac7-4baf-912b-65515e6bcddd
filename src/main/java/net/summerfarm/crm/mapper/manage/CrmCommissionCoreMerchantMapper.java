package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCommissionCoreMerchant;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmCommissionCoreMerchantMapper {

    /**
     * 插入核心客户净增长数系数
     * @param record 核心客户净增长数系数
     * @return 0,1
     */
    int insertSelective(CrmCommissionCoreMerchant record);

    /**
     * 根据id查找
     * @param id id
     * @return 核心客户净增长数系数
     */
    CrmCommissionCoreMerchant selectByPrimaryKey(Long id);
    /**
     * 更新核心客户净增长数系数
     * @param record 核心客户净增长数系数
     * @return 0,1
     */
    int updateByPrimaryKeySelective(CrmCommissionCoreMerchant record);

    /**
     * 核心客户净增长数系数查询
     * @return 核心客户净增长数系数
     */
    List<CrmCommissionCoreMerchant> selectCoreMerchantsNetGrowth();

    /**
     * id存在则更新,不存在则新增
     * @param record 数据
     * @return 0|1
     */
    int insertOrUpdateById(CrmCommissionCoreMerchant record);
}