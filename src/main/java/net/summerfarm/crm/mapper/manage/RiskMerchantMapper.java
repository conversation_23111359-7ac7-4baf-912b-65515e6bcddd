package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.RiskMerchant;
import net.summerfarm.crm.model.query.riskMerchant.AuditRiskMerchantQuery;
import net.summerfarm.crm.model.query.riskMerchant.RiskMerchantQuery;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantDetailVO;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 16:58
 */
public interface RiskMerchantMapper {
    int insertSelective(RiskMerchant record);

    RiskMerchant selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(RiskMerchant record);

    /**
     * 风控门店列表
     *
     * @param query 查询
     * @return {@link List}<{@link RiskMerchantListVO}>
     */
    List<RiskMerchantListVO> listRiskMerchant(RiskMerchantQuery query);

    /**
     * 风控门店详情
     *
     * @param id id
     * @return {@link RiskMerchantDetailVO}
     */
    RiskMerchantDetailVO selectRiskMerchantDetail(@Param("id")Integer id);

    /**
     * 审核风控门店
     *
     * @param query 查询
     */
    void auditRiskMerchantDetail(@Param("query") AuditRiskMerchantQuery query,@Param("auditorId")Integer auditorId,@Param("auditorName")String auditorName);

    /**
     * 查询30天内存在的门店
     *
     * @param mIdList id列表
     * @return {@link List}<{@link Long}>
     */
    List<Long> selectByMid(@Param("mIdList")List<Long> mIdList);
}