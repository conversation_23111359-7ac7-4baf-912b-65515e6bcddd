package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.MerchantSituation;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.crm.model.query.MerchantSituationQuery;
import net.summerfarm.crm.model.vo.MerchantSituationVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface MerchantSituationMapper {

    MerchantSituation querySituation(@Param("msId") Integer msId);

    /**
     * 根据条件查询客情申请记录
     * @param merchantSituationQuery 查询条件
     * @return  客情申请记录
     */
    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantSituationVO> querySituationList(MerchantSituationQuery merchantSituationQuery);

    int updateSituation(MerchantSituation merchantSituation);

    /**
     * 新增客情审批记录
     * @param merchantSituationDTO 客情审批详情
     * @return ok
     */
    int insertMerchantSituation(MerchantSituationDTO merchantSituationDTO);

    int updateSituationALl(MerchantSituation merchantSituation);

    /**
     * 查询客户客情申请情况
     * @param merchantSituationVO 查询条件
     * @return 客户客情申请情况
     */
    List<MerchantSituationVO> querySituationListTime(MerchantSituationVO merchantSituationVO);

    /**
     * 跟进客户id 查询客户发放卡券情况
     * @param merchantSituationDTO 查询条件
     * @return 客户卡券发放情况
     */
    List<MerchantSituationVO> selectCouponByMid(MerchantSituationDTO merchantSituationDTO);


    MerchantSituation selectByPrimaryKey(Long id);
}
