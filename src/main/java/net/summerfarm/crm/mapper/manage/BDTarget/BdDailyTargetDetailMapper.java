package net.summerfarm.crm.mapper.manage.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailSync;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailUpdate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 销售每日目标明细 Mapper接口
 * <AUTHOR>
@Repository
public interface BdDailyTargetDetailMapper {

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdDailyTargetDetail record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<BdDailyTargetDetail> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdDailyTargetDetail selectByPrimaryKey(Long id);

    /**
     * 根据主键列表批量查询记录
     * @param ids 主键列表
     * @return 记录对象列表
     */
    List<BdDailyTargetDetail> selectByDailyTargetIds(@Param("dailyTargetIds") List<Long> dailyTargetIds);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdDailyTargetDetailUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdDailyTargetDetail> list(BdDailyTargetDetailQuery query);

    /**
     * 更新单条指标当前值和状态
     * @param sync 同步数据
     * @return 影响行数
     */
    int updateIndicatorValueAndStatus(BdDailyTargetDetailSync sync);

    /**
     * 根据销售每日目标ID列表查询每日目标明细
     * @param bdDailyTargetIds 销售每日目标ID列表
     * @return 销售每日目标明细列表
     */
    List<BdDailyTargetDetail> listByBdDailyTargetIds(@Param("bdDailyTargetIds") List<Long> bdDailyTargetIds);


    /**
     * 根据ID列表批量查询销售拜访目标指标
     *
     * @param ids ID列表
     * @return 销售拜访目标指标列表
     */
    List<BdDailyTargetDetail> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据销售每日目标ID列表批量逻辑删除明细
     *
     * @param bdDailyTargetIds 销售每日目标ID列表
     * @return 影响行数
     */
    int logicDeleteByBdDailyTargetIds(@Param("bdDailyTargetIds") List<Long> bdDailyTargetIds);

    /**
     * 根据明细ID列表查询销售每日目标明细
     *
     * @param detailIds 明细ID列表
     * @return 销售每日目标明细列表
     */
    List<BdDailyTargetDetail> listByDetailIds(@Param("detailIds") List<Long> detailIds);

}