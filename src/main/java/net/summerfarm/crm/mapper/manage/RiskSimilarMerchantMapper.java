package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.RiskSimilarMerchant;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantVO;
import org.apache.ibatis.annotations.Param;

/**
 * 
 * <AUTHOR>
 * @date 2023/11/17 16:58
 */
public interface RiskSimilarMerchantMapper {
    int insertSelective(RiskSimilarMerchant record);

    RiskSimilarMerchant selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RiskSimilarMerchant record);

    /**
     * 根据风控门店查询相似门店
     *
     * @param riskMerchantId id
     * @return {@link RiskMerchantVO}
     */
    RiskMerchantVO selectByRiskMerchantId(@Param("riskMerchantId") Integer riskMerchantId);
}