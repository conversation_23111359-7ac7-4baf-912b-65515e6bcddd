package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.WecomGroupTask;
import net.summerfarm.crm.model.vo.weCom.WeComTaskSummaryVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23 15:03
 */
public interface WecomGroupTaskMapper {
    int insertBatch(@Param("record") List<WecomGroupTask> record);

    int insertSelective(WecomGroupTask record);

    WecomGroupTask selectByPrimaryKey(Long id);

    /**
     * 统计企微任务
     *
     * @param bdId 销售 id
     * @return {@link WeComTaskSummaryVo}
     */
    WeComTaskSummaryVo selectByBdId(@Param("bdId") List<Integer> bdId);

    /**
     * 删除指定日期记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    void deleteByDate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}