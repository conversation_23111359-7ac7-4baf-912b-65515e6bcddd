package net.summerfarm.crm.mapper.manage;

import net.summerfarm.pojo.DO.Admin;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface AdminMapper {


    Admin select(String username);

    /**
     * 按真实姓名选择
     *
     * @param realName   用户名
     * @param userBaseId
     * @return {@link List}<{@link Admin}>
     */
    List<Admin> selectByRealName(@Param("realName") String realName,@Param("userBaseId")  List<Long> userBaseId);
    Admin selectByPrimaryKey(Integer adminId);

    List<Admin> selectByIds( @Param("ids") List<Integer> ids);

    Long selectUserBaseIdById(@Param("adminId")Integer adminId);

    Admin selectByInviterChannelCode(@Param("inviterChannelCode") String inviterChannelCode);

    List<Admin> selectByRealnameIn(@Param("realnameCollection")Collection<String> realnameCollection);

}
