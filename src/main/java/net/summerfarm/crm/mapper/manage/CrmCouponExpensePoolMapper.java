package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCouponExpensePool;
import net.summerfarm.crm.model.domain.CrmCouponExpensePoolRange;
import net.summerfarm.crm.model.query.crmCouponExpensePool.CrmCouponExpensePoolQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmCouponExpensePoolMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmCouponExpensePool record);

    int insertSelective(CrmCouponExpensePool record);

    CrmCouponExpensePool selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmCouponExpensePool record);

    List<CrmCouponExpensePool> selectByQuery(CrmCouponExpensePoolQuery cluClueQuery);

    CrmCouponExpensePool selectByName(@Param("name") String name);

    List<CrmCouponExpensePool> selectExpire();

    void updateCostLimitNull(@Param("id")Long id);

    void updateStartTimeEndTimeNull(@Param("id")Long id);
}