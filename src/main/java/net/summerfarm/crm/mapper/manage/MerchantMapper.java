package net.summerfarm.crm.mapper.manage;

import com.github.yulichang.base.MPJBaseMapper;
import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.dto.CustomerAnalysisDTO;
import net.summerfarm.crm.model.query.MerchantDetailQuery;
import net.summerfarm.crm.model.query.SkuMerchantQueryInfoQuery;
import net.summerfarm.crm.model.vo.CrmSkuMerchantGmvVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface MerchantMapper extends MPJBaseMapper<Merchant> {

    /**
     * 根据商户id查询其核心信息
     * @param mId 商户id
     * @return 核心信息
     */
    @RequiresDataPermission(originalField = "m.area_no")
    MerchantVO selectByPrimaryKey(Long mId);

    /**
     * 根据商户id查询其核心信息
     * @param mIds 商户ids
     * @return 核心信息
     */
    @RequiresDataPermission(originalField = "m.area_no")
    List<MerchantVO> selectByPrimaryKeys(@Param("mIds") List<Long> mIds);

    int updateByPrimaryKeySelective(Merchant record);

    Merchant selectOne(Map<String,String> selectKeys);

    /**
     * 根据id查询客户信息详情
     * @param mId 客户id
     * @return 客户信息详情
     */
    MerchantVO selectMerchantByMid(@Param("mId")Long mId);

    /**
     * 根据id查询客户折扣卡信息
     *
     * @param mId 客户id
     * @return 折扣卡信息
     */
    MerchantVO selectDiscountCardByMid(@Param("mId") Long mId);

    /**
     * 获取商户信息
     * @param merchantVO 查询条件
     * @return 商户信息
     */
    List<MerchantVO> queryPrivateSea(MerchantVO merchantVO);

    /**
     * 查询当日注册的店铺
     * @param mname 店铺名称
     * @param registerDate 注册时间
     * @param bdAdminId 跟进bd的id
     * @return 店铺信息
     */
    List<MerchantVO> selectRegisterPrivateSea(@Param("mname") String mname, @Param("registerDate") LocalDate registerDate, @Param("bdAdminId") Integer bdAdminId);

    /**
     * 获取商户sku下单信息
     * @param skuMerchantQueryInfoQuery 查询条件
     * @return sku下单信息
     */
    CrmSkuMerchantGmvVO selectSkuGmvByMid(SkuMerchantQueryInfoQuery skuMerchantQueryInfoQuery);

    /**
     * 商户购买品类top10
     * @param merchantDetailQuery 查询条件
     * @return 品类top10
     */
    List<CustomerAnalysisDTO> selectCategoryTop(MerchantDetailQuery merchantDetailQuery);


    List<Merchant> selectTypeByMids(@Param("ids") List<Long> ids);

    /**
     * 根据商户名称查询信息
     *
     * @param mname 商户名称
     * @return {@link MerchantVO}
     */
    MerchantVO selectByMname(@Param("mname")String mname);

    /**
     * 根据手机号查询门店
     *
     * @param phone 电话
     * @param mId   m id
     * @return {@link List}<{@link MerchantVO}>
     */
    List<MerchantVO> selectByPhone(@Param("phone") String phone,@Param("mId")Long mId);

    /**
     * 根据unionId查询门店
     *
     * @param unionId 联盟id
     * @return {@link MerchantVO}
     */
    MerchantVO selectByUnionId(@Param("unionId") String unionId);

    List<Merchant> selectByNames(@Param("names") List<String> names);

    Long updateOperatingStateByMids(@Param("mids") List<Long> mids,@Param("operateStatus") Integer operateStatus);
    /**
     * 根据行政城市查询 mid
     *
     * @param mIds     mid
     * @param city     城市
     * @param province 省
     * @param area     区域
     * @return {@link List}<{@link String}>
     */
    List<String> selectMIdByCity(@Param("mIds")List<String> mIds,@Param("province")String province,@Param("city")String city,@Param("area")String area);

    List<MerchantVO> selectByMids(@Param("mIds") List<Long> mIds);

    List<MerchantVO> selectByMidsNew(@Param("mIds") List<Long> mIds);

    /**
     * 查询存在的门店
     *
     * @param mIds m id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> selectMIdsByMIds(@Param("mIds") List<Integer> mIds);

    List<Long> selectMIdByMId(@Param("mIds") List<Long> mIds);

    /**
     * 选择通过门店 Id列表
     *
     * @param mIds id列表
     * @return {@link List}<{@link Merchant}>
     */
    List<Merchant> selectByMIdList(@Param("mIds")List<Integer> mIds);

    List<Long> selectMidsByAdminId(@Param("adminId") Integer adminId);

    /**
     * 查询operate_status=1，但却下单了的商户
     */
    List<Long> selectClosedAndOrdered(@Param("days") Integer days);
}
