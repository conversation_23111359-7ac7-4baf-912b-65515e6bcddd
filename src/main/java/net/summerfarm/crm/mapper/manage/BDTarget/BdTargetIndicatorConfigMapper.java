package net.summerfarm.crm.mapper.manage.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfigUpdate;
import net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfigQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售目标指标配置 Mapper接口
 * <AUTHOR>
@Repository
public interface BdTargetIndicatorConfigMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdTargetIndicatorConfig record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<BdTargetIndicatorConfig> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdTargetIndicatorConfig selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdTargetIndicatorConfigUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdTargetIndicatorConfig> list(BdTargetIndicatorConfigQuery query);

    /**
     * 查询所有销售目标指标配置
     * @return 所有销售目标指标配置列表
     */
    List<BdTargetIndicatorConfig> listAllConfigs();

}