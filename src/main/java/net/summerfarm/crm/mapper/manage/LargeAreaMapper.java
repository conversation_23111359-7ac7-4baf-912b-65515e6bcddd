package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.pojo.DO.Area;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/6 16:07
 */
public interface LargeAreaMapper {
    /**
     * 根据大区名称查询大区信息
     *
     * @param areaName 区域名称
     * @return {@link LargeAreaDTO}
     */
    LargeAreaDTO selectByName(@Param("areaName")String areaName);

    /**
     * 查询所有大区
     *
     * @param status 状态
     * @return {@link List}<{@link LargeAreaDTO}>
     */
    List<LargeAreaDTO> selectAll(@Param("status")Integer status);
}
