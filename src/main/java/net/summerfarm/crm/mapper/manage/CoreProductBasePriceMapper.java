package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CoreProductBasePrice;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.query.BasePriceQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 底价控制mapper
 *
 * <AUTHOR>
 * @Date 2023/3/2 17:36
 */
@Repository
public interface CoreProductBasePriceMapper {
    /**
     * 根据sku和大区获取商品底价
     *
     * @param id     id
     * @param sku
     * @param areaNo 大区编号
     * @return 底价信息
     */
    CoreProductBasePrice selectBySkuAreaNo(@Param("id") Integer id, @Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 底价列表
     *
     * @param query
     * @return {@link List}<{@link CoreProductBasePrice}>
     */
    List<CoreProductBasePrice> listBasePrice(BasePriceQuery query);

    /**
     * 更新底价
     *
     * @param basePrice 底价
     */
    void updateBasePrice(CoreProductBasePrice basePrice);

    int updateBasePriceAndMscRedLinePriceById(@Param("updatedBasePrice")BigDecimal updatedBasePrice, @Param("updatedMerchantSituationCategoryRedLinePrice")BigDecimal updatedMerchantSituationCategoryRedLinePrice, @Param("id")Integer id);

    int updateMerchantSituationCategoryBasePriceById(@Param("updatedMerchantSituationCategoryBasePrice")BigDecimal updatedMerchantSituationCategoryBasePrice,@Param("id")Integer id);


    /**
     * 删除底价
     *
     * @param id id
     */
    void delBasePrice(Integer id);

    /**
     * 删除底价
     *
     * @param ids id
     */
    void batchDelBasePrice(@Param("ids") List<Integer> ids);


    /**
     * 新增底价
     *
     * @param basePrice 底价
     * @return {@link Integer}
     */
    Integer insertBasePrice(CoreProductBasePrice basePrice);

    /**
     * 获取有底价的大区
     *
     * @return {@link List}<{@link LargeAreaDTO}>
     */
    List<LargeAreaDTO> listBasePriceArea();
}
