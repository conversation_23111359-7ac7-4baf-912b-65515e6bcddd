package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.SampleSku;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface SampleSkuMapper {

    int insertSampleSku(List<SampleSku> list);

    /**
     * 根据样品申请id查询
     * @param sampleId
     * @return
     */
    List<SampleSku> selectBySampleId(Integer sampleId);

    /**
     * 获取样品申请中的样品信息
     * @param sku sku
     * @return 样品信息
     */
/*
    SampleSku selectSkuType(String sku);
*/
}
