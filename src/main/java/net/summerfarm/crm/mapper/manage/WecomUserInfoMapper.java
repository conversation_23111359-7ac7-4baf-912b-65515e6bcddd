package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.model.vo.weCom.WeComActivateVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企微用户状态
 *
 * <AUTHOR>
 * @date 2024/2/23 15:03
 */
public interface WecomUserInfoMapper {
    int insertSelective(WecomUserInfo record);

    WecomUserInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WecomUserInfo record);

    /**
     * 用户列表
     *
     * @return {@link List}<{@link WecomUserInfo}>
     */
    List<WecomUserInfo> selectList();

    /**
     * 统计企微激活状态
     *
     * @param id id
     * @return {@link WeComActivateVo}
     */
    WeComActivateVo summaryActivate(@Param("bdList") List<Long> id);

    /**
     * 按用户id选择
     *
     * @param userid 用户标识
     * @return {@link WeComActivateVo}
     */
    WecomUserInfo selectByUserId(@Param("userid") String userid);

    /**
     * 按adminId选择
     *
     * @param adminId
     * @return
     */
    WecomUserInfo selectByAdminId(@Param("adminId") Long adminId);

    /**
     * 按创建时间选择
     *
     * @param startTime 开始时间
     * @return {@link List}<{@link WecomUserInfo}>
     */
    List<WecomUserInfo> selectActiveByUpdateTime(@Param("startTime") String startTime, @Param("endTime") String endTime);
}