package net.summerfarm.crm.mapper.manage.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlan;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanUpdate;
import net.summerfarm.crm.model.query.objectiveManagement.BdVisitPlanListQuery;
import net.summerfarm.crm.model.vo.objectiveManagement.BdVisitPlanSummaryVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 销售拜访计划 Mapper接口
 * <AUTHOR>
@Repository
public interface BdVisitPlanMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdVisitPlan record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<BdVisitPlan> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdVisitPlan selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdVisitPlanUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdVisitPlan> list(BdVisitPlanQuery query);

    /**
     * 根据门店ID和拜访目标ID更新单条门店潜力值
     * @param update 更新对象，包含门店ID、拜访目标ID和对应的潜力值
     * @return 影响行数
     */
    int updatePotentialValueByMerchantIdAndTargetId(BdVisitPlanUpdate update);

    /**
     * 根据主键列表批量逻辑删除记录
     * @param ids 主键列表
     * @return 影响行数
     */
    int batchLogicalDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据查询条件查询销售拜访计划列表
     * @param query 查询条件
     * @return 拜访计划列表
     */
    List<BdVisitPlan> listBdVisitPlan(BdVisitPlanListQuery query);

    /**
     * 更新拜访状态和跟进记录ID
     * 根据门店ID、销售ID和拜访日期更新拜访状态和跟进记录ID
     *
     * @param mId 门店ID
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @param visitStatus 拜访状态
     * @param followUpRecordId 跟进记录ID
     * @param originalVisitStatus 原始拜访状态
     * @return 影响行数
     */
    int updateVisitStatusAndRecordIdByMidAndBdIdAndVisitDate(@Param("mId") Long mId,
                                                           @Param("bdId") Integer bdId,
                                                           @Param("visitDate") LocalDate visitDate,
                                                           @Param("visitStatus") Integer visitStatus,
                                                           @Param("followUpRecordId") Long followUpRecordId,
                                                           @Param("originalVisitStatus") Integer originalVisitStatus);

    /**
     * 汇总销售拜访计划数据
     * 根据销售ID和拜访日期汇总销售拜访计划
     *
     * @param bdId 销售ID
     * @param visitDate 拜访日期
     * @return 销售拜访计划汇总信息
     */
    BdVisitPlanSummaryVO summaryBdVisitPlan(@Param("bdId") Integer bdId, @Param("visitDate") LocalDate visitDate);
}