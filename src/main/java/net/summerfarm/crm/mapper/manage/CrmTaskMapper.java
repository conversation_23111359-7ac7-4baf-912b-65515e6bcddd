package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmTask;
import net.summerfarm.crm.model.dto.task.TimingTaskDTO;
import net.summerfarm.crm.model.query.task.TaskListQuery;
import net.summerfarm.crm.model.vo.task.TaskCountVo;
import net.summerfarm.crm.model.vo.task.TaskListVo;
import net.summerfarm.crm.model.vo.task.TimingTaskVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/10/8 11:52
 */
public interface CrmTaskMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CrmTask record);

    int insertSelective(CrmTask record);

    CrmTask selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CrmTask record);

    int updateByPrimaryKey(CrmTask record);


    /**
     * 任务列表
     *
     * @param query 查询条件
     * @return {@link List}<{@link TaskListVo}>
     */
    List<TaskListVo> taskList(TaskListQuery query);

    /**
     * 根据来源 id 查询任务
     *
     * @param sourceId 源id
     * @return {@link CrmTask}
     */
    CrmTask selectBySourceId(@Param("sourceId")String sourceId);

    /**
     * 查询卡券任务个数
     *
     * @return {@link TaskCountVo}
     */
    TaskCountVo selectCouponTaskCount(@Param("taskId") Integer taskId,@Param("bdIdList") List<Integer> bdIdList);

    /**
     * 查询拜访任务个数
     *
     * @return {@link TaskCountVo}
     */
    TaskCountVo selectFollowTaskCount(@Param("taskId") Integer taskId,@Param("bdIdList") List<Integer> bdIdList);

    /**
     * 按退款时间列出订单
     *
     * @param refundTime     退款时间
     * @param mId            门店 Id
     * @param excludeOrderNo 排除订单号
     * @return {@link List}<{@link TimingTaskDTO}>
     */
    List<TimingTaskDTO> listOrderByRefundTime(@Param("refundTime") List<LocalDate> refundTime,@Param("mId")Integer mId,@Param("excludeOrderNo")String excludeOrderNo);

    /**
     * 查询省心送订单
     *
     * @param orderNos 订单号
     * @return {@link List}<{@link TimingTaskVo}>
     */
    List<TimingTaskVo> listTimingOrderByNo(@Param("orderNos")List<String> orderNos);
}