package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.MerchantLeads;
import net.summerfarm.crm.model.vo.MerchantLeadsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface MerchantLeadsMapper {
    /**
     * 新增线索池
     * @param record 记录
     * @return ok
     */
    int insertSelective(MerchantLeads record);
    /**
     * 编辑线索池
     * @param record 记录
     * @return ok
     */
    int updateByPrimaryKeySelective(MerchantLeads record);

    /**
     * 查询线索池
     * @param selectKeys 查询条件
     * @return 线索池信息
     */
    List<MerchantLeadsVO> selectMerchantLeads(MerchantLeads selectKeys);

    /**
     * 查询线索池
     * @param mname 商户名
     * @return 线索池信息
     */
    MerchantLeads selectByMname(String mname);

    /**
     * 查询线索池
     * @param id id
     * @return 线索池信息
     */
    MerchantLeads selectById(Integer id);

    /**
     * id存在则更新,不存在则新增
     * @param record 数据
     * @return 0|1
     */
    int insertOrUpdateById(MerchantLeads record);

    /**
     * 根据指定的mId获取一个MerchantLeads对象
     *
     * @param mId 需要获取的MerchantLeads对象的mId
     * @return 匹配指定mId的MerchantLeads对象
     */
    MerchantLeads selectByMId(@Param("mId") Integer mId);
}