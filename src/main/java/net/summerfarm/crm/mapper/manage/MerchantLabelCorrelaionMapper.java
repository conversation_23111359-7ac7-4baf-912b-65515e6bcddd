package net.summerfarm.crm.mapper.manage;


import net.summerfarm.crm.model.domain.MerchantLabel;
import net.summerfarm.crm.model.domain.MerchantLabelCorrelaion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantLabelCorrelaionMapper {
    /**
     * 插入选择性
     *
     * @param record 记录
     * @return int
     */
    int insertSelective(MerchantLabelCorrelaion record);

    /**
     * 按mid和label id插入
     *
     * @param mId     m id
     * @param labelId 标签id
     * @return int
     */
    int insertByMidAndLabelId(@Param("mId") Long mId, @Param("labelId") Long labelId);


    /**
     * 按mid和label id删除
     *
     * @param mId     m id
     * @param labelId 标签id
     */
    void deleteByMidAndLabelId(@Param("mId")Long mId,@Param("labelId")Long labelId);

    /**
     * 按mid和label id删除
     *
     * @param mId     m id
     * @param labelId 标签id
     */
    void deleteByMidAndLabelIdList(@Param("mId")Long mId,@Param("labelId")List<Integer> labelId);

    /**
     * 查询门店标签
     *
     * @param mId     门店 Id
     * @param labelId 标签id
     * @return {@link MerchantLabel}
     */
    List<MerchantLabelCorrelaion> selectByMidAndLabelId(@Param("mId")Long mId,@Param("labelId") List<Integer> labelId);

    /**
     * 根据门店id查询门店标签
     *
     * @param mId
     * @return
     */
    List<MerchantLabelCorrelaion> selectByMid(@Param("mId") Long mId);

    int updateLabelIdById(@Param("updatedLabelId") Long updatedLabelId, @Param("id") Long id);
}