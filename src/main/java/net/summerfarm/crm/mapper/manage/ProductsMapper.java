package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.dto.CategoryProductDTO;
import net.summerfarm.crm.model.dto.TrolleyDTO;
import net.summerfarm.crm.model.query.MerchantDetailQuery;
import net.summerfarm.crm.model.query.SkuMerchantQuery;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
@Deprecated
public interface ProductsMapper {

    /**
     * 根据名称精确查找商品
     * @param skuMerchantQuery 类目信息
     * @return  商品信息
     */
    @Deprecated
    List<CrmSkuMonthGmvVO> selectByQuery(SkuMerchantQuery skuMerchantQuery);


    /**
     * 商户常购商品
     * @param merchantDetailQuery 查询条件
     * @return 商户常购商品清单
     */
    List<CrmSkuMonthGmvVO> selectProductByQuery(MerchantDetailQuery merchantDetailQuery);

    /**
     * 获取商户常购品类
     * @param skuMerchantQuery 查询条件
     * @return 商户常购品类
     */
    String selectByCategoryQuery(SkuMerchantQuery skuMerchantQuery);

    /**
     * 通过sku获取类别类型
     *
     * @param sku sku
     * @return {@link Integer}
     */
/*
    CategoryProductDTO getCategoryTypeBySku(@Param("sku")String sku);
*/

}
