package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCommissionMerchant;
import net.summerfarm.crm.model.query.BatchModifyMerchantQuery;
import net.summerfarm.crm.model.vo.CrmCommissionMerchantVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmCommissionMerchantMapper {
    /**
     * 删除销售拉新提成
     * @param id id
     * @return ok
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 增加销售拉新提成
     * @param record 销售拉新提成
     * @return ok
     */
    int insertSelective(CrmCommissionMerchant record);

    /**
     * 修改销售拉新提成
     * @param record 销售拉新提成
     * @return ok
     */
    int updateByPrimaryKeySelective(BatchModifyMerchantQuery record);

    /**
     * 查询拉新奖励列表
     * @param zoneName 区域名
     * @return 拉新奖励列表
     */
    List<CrmCommissionMerchantVo> selectPullNewMerchantReward(@Param("zoneName") String zoneName);

    /**
     * 复制拉新奖励
     * @param copyInfo 复制信息
     * @param info 复制信息
     * @param adminId 销售id
     */
    void copyMerchant(@Param("copyInfo") String copyInfo, @Param("info") String info, @Param("adminId") Integer adminId);

    /**
     * 删除销售拉新提成
     * @param zoneName 区域名称
     * @return ok
     */
    void deleteByZoneName(String zoneName);

    /**
     * 插入或更新用户奖励sku
     * @param record 更新内容
     * @return 数量
     */
    int insertOrUpdateById(CrmCommissionMerchant record);
}