package net.summerfarm.crm.mapper.manage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface SampleApplyMapper extends BaseMapper<SampleApply> {

    /**
    * 查询申请列表
    */
    @RequiresDataPermission(originalField = "sa.area_no")
    List<SampleApplyVO> selectSampleApplies(@Param("sa") SampleApply sampleApply, @Param("keyword") String keyword);

    int insertSampleApply(SampleApply sampleApply);

    int updateSampleApply(SampleApply sampleApply);

    SampleApply selectSampleById(Integer sampleId);

       /**
     * 取消样品申请
     * @param sampleId
     * @return
     */
    int cancelSampleApply(Integer sampleId);
    /**
     * 关闭样品申请
     * @param sampleId
     * @return
     */
    int closeSampleApply(Integer sampleId);

    List<Integer> querySituationListTime(LocalDateTime endTime);

    List<SampleApplyVO> selectByAddTime(@Param("addTime") LocalDateTime addTime, @Param("endTime") LocalDateTime endTime);

    int selectOverCount(@Param("mId") Long mId);

   List<String>  selectSampleApplySkuList(@Param("mId") Long mId);

    Set<Long> getSampleInfoBySku(@Param("mIds") List<Long> mIds, @Param("sku") String sku);

    void batchInsert(@Param("list") List<SampleApply> sampleApplyList);

    int batchCancelSampleApply(@Param("list") Collection<Integer> sampleId);

    List<SampleApply> selectSampleInfoBySku(@Param("mIds") Collection<Long> mIds, @Param("sku") String sku, @Param("status") Integer status);
}

