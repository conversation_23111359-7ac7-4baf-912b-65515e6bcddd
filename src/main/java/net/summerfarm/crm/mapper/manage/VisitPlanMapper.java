package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.VisitPlan;
import net.summerfarm.crm.model.query.VisitPlanQuery;
import net.summerfarm.crm.model.vo.VisitPlanVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface VisitPlanMapper {
   /**
    * 根据id查询拜访计划
    * @param id 拜访计划id
    * @return 拜访计划详情
    */
   VisitPlanVO queryVisitPlanOne(Long id);

   @RequiresDataPermission(originalField = "vp.area_no")
   List<VisitPlanVO> queryVisitPlanList(VisitPlanQuery visitPlanQuery);

   /**
    * 更新拜访计划
    * @param visitPlan 更新计划信息
    * @return 0或1
    */
   int updateVisitPlan(VisitPlan visitPlan);

   /**
    * 插入拜访计划
    * @param visitPlan 拜访计划内容
    * @return 0或1
    */
   int insertVisitPlan(VisitPlan visitPlan);

   /**
    * 查询拜访计划
    * @param query 查询条件:adminId,date,mId,status,areaNo
    * @return 拜访计划
    */
   List<VisitPlanVO> selectList(VisitPlanVO query);

   /**
    * 查询拜访计划
    * @param query 查询条件:adminId ,status
    * @return 拜访计划
    */
   int updateUnHandlePlan(LocalDateTime time);

   /**
    * 跟进id查询拜访计划
    * @param id 拜访计划id
    * @return 拜访计划信息
    */
    VisitPlan selectById(Long id);

   List<VisitPlanVO> queryVisitPlanListNew(VisitPlanQuery visitPlanQuery);
}
