package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.bo.AdminDataPermissionBO;
import net.summerfarm.pojo.DO.AdminDataPermission;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年04月20日
 */
@Repository
public interface AdminDataPermissionMapper {

    /**
     * 获取用户数据权限
     * @param adminId
     * @return
     */
    List<AdminDataPermission> selectByAdminId(Integer adminId);

    /**
     * 获取用户所拥有的权限
     * @param adminId 用户id
     * @return 用户所拥有的权限
     */
    List<AdminDataPermissionBO> selectDataByAdminId(Integer adminId);

}
