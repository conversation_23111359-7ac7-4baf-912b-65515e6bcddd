package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CategoryCouponQuota;
import net.summerfarm.crm.model.vo.CategoryCouponQuotaVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 品类券额度
 *
 * <AUTHOR>
 * @Date 2023/3/2 18:29
 */
@Repository
public interface CategoryCouponQuotaMapper {
    /**
     * 插入操作
     *
     * @param quota
     */
    void insertSelective(CategoryCouponQuota quota);

    /**
     * 更新额度
     *
     * @param adminId m1 id
     * @param quota   额度
     */
    void updateQuota(@Param("adminId") Integer adminId, @Param("quota") BigDecimal quota,@Param("type")Integer type);

    /**
     * 更新费比
     *
     * @param id              id
     * @param newCustomerRate 新客户率
     * @param oldCustomerRate 老客户率
     */
    void updateRate(@Param("id") Integer id, @Param("newCustomerRate") BigDecimal newCustomerRate, @Param("oldCustomerRate") BigDecimal oldCustomerRate);

    /**
     * 根据bd id 获取额度信息
     *
     * @param adminId m1 id
     * @param type    类型
     * @return {@link CategoryCouponQuota}
     */
    CategoryCouponQuota selectByAdminId(@Param("adminId") Integer adminId,@Param("type")Integer type);

    /**
     * 额度列表
     *
     * @param adminId
     * @return {@link List}<{@link CategoryCouponQuotaVO}>
     */
    List<CategoryCouponQuotaVO> listQuota(@Param("adminId") Integer adminId,@Param("quotaType") Integer quotaType,
                                          @Param("adminName") String adminName,@Param("adminIds") List<Integer> adminIds);


    /**
     * 额度列表
     *
     * @param adminId
     * @return {@link List}<{@link CategoryCouponQuotaVO}>
     */
    List<CategoryCouponQuotaVO> listQuotaAll(@Param("adminId") Integer adminId,@Param("quotaType") Integer quotaType,
                                          @Param("adminName") String adminName,@Param("adminIds") List<Integer> adminIds);

    /**
     * 额度列表
     *
     * @return {@link List}<{@link CategoryCouponQuota}>
     */
    List<CategoryCouponQuota> listAdmin(@Param("quotaType") Integer quotaType);

    /**
     * 删除额度信息
     *
     * @param adminId id
     */
    void deleteByAdminId(@Param("adminId") Integer adminId,@Param("quotaType") Integer quotaType);
}
