package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.dto.AreaInfoDTO;
import net.summerfarm.crm.model.vo.AreaVO;
import net.summerfarm.pojo.DO.Area;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 区域
 *
 * <AUTHOR>
 * @date 2023/08/15
 */
public interface AreaMapper {
    /**
     * 通过大区查询运营服务区域
     *
     * @param largeAreaNo 大区编号
     * @return {@link Area}
     */
    @RequiresDataPermission(originalField = "ar.area_no")
    List<Area> selectByLargeArea(@Param("largeAreaNo") Integer largeAreaNo);

    /**
     * 查询区域
     *
     * @param areaNo 区没有
     * @return {@link Area}
     */
    Area selectByAreaNo(@Param("areaNo") Integer areaNo);


    /**
     *
     * @param areaNo
     * @return
     */
    AreaInfoDTO selectLargeAreaByAreaNo(@Param("areaNo") Integer areaNo);

    /**
     * 查询区域
     *
     * @param areaNo 区没有
     * @return {@link List}<{@link Area}>
     */
    List<Area> selectByAreaNoList(@Param("areaNo") List<Integer> areaNo);


    /**
     * 查询区域负责人
     *
     * @return {@link List}<{@link Area}>
     */
    List<AreaVO> queryAreaWithAdmins(@Param("mIds") List<Long> mIds);
}
