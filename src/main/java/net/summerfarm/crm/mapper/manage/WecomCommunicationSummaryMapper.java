package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.WecomCommunicationSummary;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23 15:03
 */
public interface WecomCommunicationSummaryMapper {
    /**
     * 批量插入
     *
     * @param record 记录
     */
    void insertBatch(@Param("record") List<WecomCommunicationSummary> record);

    int insertSelective();

    WecomCommunicationSummary selectByPrimaryKey(Long id);

    /**
     * 查询销售沟通看板
     *
     * @param bdId 销售 id
     * @return {@link List}<{@link WecomCommunicationSummary}>
     */
    List<WecomCommunicationSummary> selectByBdId(@Param("bdIdList") List<Integer> bdId);

    /**
     * 删除指定日期数据
     *
     * @param date 日期
     */
    void deleteByDate(@Param("date")LocalDate date);
}