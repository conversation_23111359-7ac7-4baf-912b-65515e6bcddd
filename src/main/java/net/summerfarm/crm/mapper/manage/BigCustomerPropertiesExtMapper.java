package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.BigCustomerPropertiesExt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BigCustomerPropertiesExtMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BigCustomerPropertiesExt record);

    int insertSelective(BigCustomerPropertiesExt record);

    BigCustomerPropertiesExt selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BigCustomerPropertiesExt record);

    int updateByPrimaryKey(BigCustomerPropertiesExt record);


    BigCustomerPropertiesExt selectByBigCustomerIdProKey(@Param("bigCustomerId") Long bigCustomerId, @Param("propKey") String propKey);

    List<BigCustomerPropertiesExt> listByBigCustomerIdsAndProKey(@Param("bigCustomerIds") List<Long> bigCustomerIds, @Param("propKey") String propKey);


}