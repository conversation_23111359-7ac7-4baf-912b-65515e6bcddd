package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.WechatTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【wechat_tag(企微标签)】的数据库操作Mapper
* @createDate 2023-08-01 15:34:45
* @Entity generator.domain.WechatTag
*/
public interface WechatTagMapper {

    /**
     * 查询所有的标签
     *
     * @return {@link List}<{@link WechatTag}>
     */
    List<WechatTag> listAll();

    /**
     * 插入
     *
     * @param tag 标签
     * @return int
     */
    int insertSelective(WechatTag tag);


    /**
     * 批量插入
     *
     * @param tag 标签
     */
    void insertBatch(@Param("tags") List<WechatTag> tag);

    List<WechatTag> selectByGroupNameTags(@Param("groupName")String groupName, @Param("tags")List<String> tags);

    void deleteIds(@Param("ids")List<Long> ids);

    /**
     * 根据组查询标签
     *
     * @return {@link List}<{@link WechatTag}>
     */
    List<WechatTag> selectByGroupName(@Param("groupName") String groupName);
}
