package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmBdConfig;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;
import net.summerfarm.crm.model.query.BatchModifyIncentiveIndexQuery;
import net.summerfarm.crm.model.query.CommissionIncentiveIndexQuery;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmBdConfigMapper {
    /**
     * 删除销售配置
     * @param id id
     * @return ok
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入激励指标
     * @param record 插入信息
     * @return 插入成功与否
     */
    int insertSelective(CrmBdConfig record);

    /**
     * 查询销售配置
     * @param id id
     * @return 销售配置
     */
    CrmBdConfig selectByPrimaryKey(Integer id);

    /**
     * 修改激励指标
     * @param record 修改内容
     * @return 成功与否
     */
    int updateByPrimaryKeySelective(BatchModifyIncentiveIndexQuery record);

    /**
     * 修改激励指标
     * @param record 修改内容
     * @return 成功与否
     */
    int updateByPrimaryKey(CrmBdConfig record);

    /**
     * 获取销售激励指标
     * @param commissionIncentiveIndexQuery 查询条件
     * @return 销售激励指标
     */
    List<CrmBdConfigVo> selectIncentiveIndex(CommissionIncentiveIndexQuery commissionIncentiveIndexQuery);

    /**
     * 复制信息并插入一条新的
     * @param copyIntInfo 被复制人id
     * @param intInfo 新增销售id
     * @param adminId 操作人id
     */
    void copyIncentiveIndex(@Param("copyIntInfo") Integer copyIntInfo,@Param("intInfo") Integer intInfo,@Param("adminId") Integer adminId);

    /**
     * 查询bd姓名
     * @param isExist 是否存在
     * @param bdName bd姓名
     * @return bd姓名
     */
    List<BdVO> queryBdName(@Param("isExist") Boolean isExist,@Param("bdName") String bdName,@Param("baseUserIds") List<Long> baseUserIds);

    /**
     * 根据区域id获取下属城市
     * @param id 区域id
     * @return 下属城市列表
     */
    List<ZoneNameListVo> selectZoneNameChildrenList(Integer id);

    /**
     * 复制信息并插入一条新的
     * @param copyIntInfo 被复制人id
     * @param intInfo 新增销售id
     * @param adminId 操作人id
     * @param lastMonthNum 上月核心客户数
     */
    void copyIncentiveIndexLastMonthGmv(@Param("copyIntInfo") Integer copyIntInfo,@Param("intInfo") Integer intInfo,@Param("adminId") Integer adminId,@Param("lastMonthNum") Integer lastMonthNum);

    /**
     * 查询销售配置信息
     * @param adminId 销售id
     * @return 销售配置信息
     */
    CrmBdConfig selectByAdminId(Integer adminId);

    /**
     * 获取销售负责的区域
     * @param adminId 销售id
     * @return 销售负责区域
     */
    List<CrmBdAreaDTO> selectBdArea(Integer adminId);

    /**
     * 获取销售负责的区域及联列表
     * @param adminId 销售id
     * @return 销售负责区域
     */
    List<TableArea> selectBdAreaCombined(@Param("adminId") Integer adminId);

    /**
     * 获取销售负责的区域及联列表
     *
     * @param adminId     销售id
     * @param largeAreaNo 大区编号
     * @return 销售负责区域
     */
    List<TableArea> selectBdAreaByAdminIdAndLargeArea(@Param("adminId") Integer adminId, @Param("value") Integer largeAreaNo);
    /**
     * 修改激励指标
     * @param record 修改内容
     * @return 成功与否
     */
    int updateByAdminId(BatchModifyIncentiveIndexQuery record);

    /**
     * 按行政城市查询记录
     *
     * @param salesDataQuery 销售数据查询
     * @return {@link List}<{@link AdminInfoVo}>
     */
    List<AdminInfoVo> selectAdminByAdministrativeCity(SalesDataQuery salesDataQuery);
}