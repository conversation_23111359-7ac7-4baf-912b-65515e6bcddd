package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmManageAdministrativeCity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmManageAdministrativeCityMapper {
    /**
     * 插入
     * @param record 插入信息
     * @return ok
     */
    int insertSelective(CrmManageAdministrativeCity record);

    /**
     * 根据id查询
     * @param id id
     * @return 区域负责城市信息
     */
    CrmManageAdministrativeCity selectByPrimaryKey(@Param("id") Long id);
    /**
     * 根据id更新
     * @param record 插入信息
     * @return ok
     */
    int updateByPrimaryKeySelective(CrmManageAdministrativeCity record);

    /**
     * 根据区域id查询行政城市
     * @param id id
     * @return 区域负责城市信息
     */
    List<String> selectByZoneId(@Param("id") Integer id);

    /**
     * 查询不在该区域的城市
     * @param id id
     * @return 不在该区域的城市
     */
    List<String> selectCity(@Param("id") Integer id);

    /**
     * 批量插入
     * @param mbId 区域id
     * @param subCity 行政城市
     * @param adminId 创建人id
     */
    void insertCity(@Param("mbId") Integer mbId, @Param("city") List<String> subCity, @Param("adminId") Integer adminId);

    /**
     * 删除区域对应的行政城市
     * @param mbId 区域id
     */
    void deleteCity(@Param("mbId") Integer mbId);
}