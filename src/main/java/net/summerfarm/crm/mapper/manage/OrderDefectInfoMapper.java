package net.summerfarm.crm.mapper.manage;

import com.alibaba.schedulerx.shade.scala.Int;
import net.summerfarm.crm.model.domain.OrderDefectInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderDefectInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrderDefectInfo record);

    int insertSelective(OrderDefectInfo record);

    OrderDefectInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderDefectInfo record);

    int updateByPrimaryKey(OrderDefectInfo record);

    List<OrderDefectInfo> selectByDeliveryId(@Param("deliveryIds") List<Integer> deliveryIds);

    List<OrderDefectInfo> selectByorderNo(@Param("orderNo") String orderNo,@Param("contactId") Long contactId);
}