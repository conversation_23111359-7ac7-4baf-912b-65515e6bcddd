package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.CrmEscortVisitPlan;
import net.summerfarm.crm.model.domain.VisitPlan;
import net.summerfarm.crm.model.dto.VisitPlanDTO;
import net.summerfarm.crm.model.query.VisitPlanQuery;
import net.summerfarm.crm.model.vo.SalesDataVo;
import net.summerfarm.crm.model.vo.VisitPlanVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface CrmEscortVisitPlanMapper {
    /**
     * 通过主键id查询陪访计划
     * @param id 陪访计划id
     * @return 陪访计划
     */
    CrmEscortVisitPlan selectById(Long id);

    /**
     * 通过主键id查询陪访计划
     * @param ids 陪访计划ids
     * @return 陪访计划
     */
    List<CrmEscortVisitPlan> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 通过拜访计划id查询查询陪访计划
     * @param visitPlanId 拜访计划id
     * @return 陪访计划
     */
    List<CrmEscortVisitPlan> selectByVisitPlanId(Long visitPlanId);

    /**
     * 通过拜访计划id查询查询陪访计划
     * @param visitPlanIds 拜访计划ids
     * @return 陪访计划
     */
    List<CrmEscortVisitPlan> selectByVisitPlanIds(@Param("visitPlanIds") List<Long> visitPlanIds);
    /**
     * 插入陪访计划
     * @param crmEscortVisitPlan 陪访计划内容
     * @return 0或1
     */
    int insertEscortVisitPlan(CrmEscortVisitPlan crmEscortVisitPlan);

    /**
     * 查询陪访计划
     * @param visitPlanQuery 查询条件
     * @return 陪访计划
     */
    List<VisitPlanVO> queryEscortPlan(VisitPlanQuery visitPlanQuery);

    /**
     * 通过拜访计划查询陪访计划
     * @param visitPlanId 拜访计划id
     * @return 陪访计划
     */
    List<VisitPlanVO> queryEscortPlanByVisitPlanId(Long visitPlanId);

    /**
     * 陪访计划变更
     * @param visitPlanDTO 变更信息
     */
    void updateVisitPlan(VisitPlanDTO visitPlanDTO);
}




