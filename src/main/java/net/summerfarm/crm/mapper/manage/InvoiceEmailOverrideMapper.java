package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.InvoiceEmailOverride;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 发票邮箱门店覆盖表 Mapper接口
 * <AUTHOR>
@Repository
public interface InvoiceEmailOverrideMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insert(InvoiceEmailOverride record);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(InvoiceEmailOverride record);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    InvoiceEmailOverride selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 记录对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(InvoiceEmailOverride record);

    /**
     * 根据主键更新记录
     * @param record 记录对象
     * @return 影响行数
     */
    int updateByPrimaryKey(InvoiceEmailOverride record);

    /**
     * 根据发票配置ID和门店ID查询覆盖配置
     * @param invoiceConfigId 发票配置ID
     * @param mId 门店ID
     * @return 覆盖配置
     */
    InvoiceEmailOverride selectByConfigIdAndMId(@Param("invoiceConfigId") Long invoiceConfigId, @Param("mId") Long mId);

    /**
     * 批量插入
     * @param list 记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<InvoiceEmailOverride> list);
}
