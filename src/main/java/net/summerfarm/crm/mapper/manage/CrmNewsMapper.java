package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmNews;
import net.summerfarm.crm.model.vo.CrmNewsVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CrmNewsMapper {

    /**
     * 新增消息通知
     * @param crmNews 消息实体
     */
    void insert(CrmNews crmNews);

    /**
     * 查询消息列表
     * @param adminId adminId
     * @param type 消息类型
     * @return 消息列表
     */
    List<CrmNewsVO> selectList(Integer adminId, Integer type);

    /**
     * 更新消息状态
     * @param adminId 消息实体
     */
    void update(Integer adminId);
}
