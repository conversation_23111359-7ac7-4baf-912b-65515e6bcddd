package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CategoryCouponQuotaChange;
import net.summerfarm.crm.model.domain.CategoryCouponQuotaReward;
import net.summerfarm.crm.model.query.CategoryQuotaChangeQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 品类券额度变化
 *
 * <AUTHOR>
 * @Date 2023/3/2 18:30
 */
@Repository
public interface CategoryCouponQuotaChangeMapper {

    /**
     * 插入操作
     *
     * @param quota
     */
    void insertSelective(CategoryCouponQuotaChange quota);

    /**
     * 根据客情id更新券id
     *
     * @param bizId            客情id
     * @param merchantCouponId 券id
     */
    void updateCouponIdByBizId(@Param("bizId") Integer bizId, @Param("merchantCouponId") Integer merchantCouponId);

    /**
     * 根据客情id查询
     *
     * @param bizId 客情id
     * @return {@link CategoryCouponQuotaChange}
     */
    CategoryCouponQuotaChange selectByBizId(@Param("bizId") Integer bizId);

    /**
     * 根据客情id删除记录
     *
     * @param bizId 客情id
     */
    void delByBizId(@Param("bizId") Integer bizId);

    /**
     * 根据用户id删除记录
     *
     * @param adminId 客情id
     */
    void delByAdmin(@Param("adminId") Integer adminId);

    /**
     * 额度变化列表
     *
     * @param query 查询
     * @return {@link List}<{@link CategoryCouponQuotaChange}>
     */
    List<CategoryCouponQuotaChange> listQuotaChange(CategoryQuotaChangeQuery query);

    /**
     * 批量新增明细
     *
     * @param reward 奖励记录
     * @param type   类型
     */
    void insertRewardBatch(@Param("reward") List<CategoryCouponQuotaReward> reward,@Param("type") Integer type);

    List<CategoryCouponQuotaChange> selectByAdminIdTypeQuotaType(@Param("adminId") Integer adminId, @Param("quotaType") Integer quotaType);
}
