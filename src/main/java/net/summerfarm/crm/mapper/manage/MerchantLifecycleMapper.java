package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.vo.DataVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MerchantLifecycleMapper {
    /**
     * 新增客户生命周期
     * @return 客户生命周期
     */
    int insertLifecycle();

    /**
     * 获取用户最新生命周期
     * @param mId 商户id
     * @return  生命周期，0新注册，1首单，2非稳，3稳定
     */
    Integer selectLast(@Param("mId")Long mId);

}