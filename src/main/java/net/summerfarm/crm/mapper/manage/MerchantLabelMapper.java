package net.summerfarm.crm.mapper.manage;


import net.summerfarm.crm.model.domain.MerchantLabel;
import net.summerfarm.crm.model.domain.MerchantSubAccount;
import net.summerfarm.crm.model.vo.MerchantVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantLabelMapper {
    /**
     * 按名称选择
     *
     * @param name 记录
     * @return int
     */
    MerchantLabel selectByName(@Param("name") String name);

    /**
     * 根据unionid查询<不存在>指定标签的门店
     * merchant_sub_account 未迁移
     * @param unionIds
     * @param name
     * @return {@link List}<{@link MerchantLabel}>
     */
    List<MerchantSubAccount> selectListMerchantLabel(@Param("unionIds") List<String> unionIds, @Param("name") String name);

    /**
     * 根据unionid/标签名查询标签
     *
     * @param unionId
     * @param name
     * @return {@link List}<{@link MerchantLabel}>
     */
    MerchantLabel selectByUnionIdAndName(@Param("unionId") String unionId, @Param("name") String name);

    /**
     * 根据门店id和名称查询标签
     *
     * @param mId  m id
     * @param name 名字
     * @return {@link List}<{@link String}>
     */
    String selectByMidAndName(@Param("mId") Long mId, @Param("name") String name);

    /**
     * 选择通过门店 Id和id
     *
     * @param mId 门店 Id
     * @param ids id
     * @return {@link List}<{@link String}>
     */
    List<String> selectByMidAndId(@Param("mId") Long mId, @Param("ids") List<Integer> ids);
}