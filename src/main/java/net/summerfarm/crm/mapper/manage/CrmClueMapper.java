package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmClue;
import net.summerfarm.crm.model.query.ClueDetailQuery;
import net.summerfarm.crm.model.query.SaasClueQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmClueMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmClue record);

    int insertSelective(CrmClue record);

    CrmClue selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmClue record);

    int updateByPrimaryKey(CrmClue record);

    List<CrmClue> query(SaasClueQuery clueQueryReq);

    CrmClue selectByBId(@Param("bId") Long bId);

    CrmClue detail(ClueDetailQuery req);
}