package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.vo.AdminInfoVo;
import net.summerfarm.pojo.DO.Area;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmBdAreaMapper {
    /**
     * 根据区域no获取bd信息,近一个月内已禁用销售仍可查询
     * @param salesDataQuery 筛选条件
     * @return bd信息
     */
    List<AdminInfoVo> selectAdminByAreas(SalesDataQuery salesDataQuery);

    /**
     * 插入销售区域配置信息
     * @param areaCityIds 区域no
     * @param adminId 销售id
     * @param creator 创建人
     */
    void insertBatch(@Param("areaCityIds") Set<Integer> areaCityIds, @Param("adminId") Integer adminId, @Param("creator") Integer creator);

    /**
     * 删除销售区域配置信息
     * @param adminId 销售id
     */
    void deleteByAdminId(Integer adminId);
    /**
     * 复制信息并插入一条新的
     * @param copyIntInfo 被复制人id
     * @param intInfo 新增销售id
     * @param creator 操作人id
     */
    void copyAreaByAdminId(@Param("copyIntInfo") Integer copyIntInfo,@Param("intInfo") Integer intInfo,@Param("creator") Integer creator);

    /**
     * 获取销售的所负责的城市列表
     * @param adminId 销售id
     * @return 城市no
     */
    List<Integer> selectByAdminId(Integer adminId);

    /**
     * 获取销售权限区域
     * @param adminId 销售id
     * @return 销售权限区域
     */
    @RequiresDataPermission(originalField = "ar.area_no")
    List<Area> selectBdArea(Integer adminId);
}