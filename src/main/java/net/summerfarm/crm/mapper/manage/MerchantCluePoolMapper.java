package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.MerchantCluePool;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MerchantCluePoolMapper {
    /**
     * 新增线索池
     * @param merchantCluePool 线索池信息
     * @return ok
     */
    int insertMerchantCluePool(MerchantCluePool merchantCluePool);

    /**
     * 查询线索池
     * @param merchantCluePool 线索池信息
     * @return 查询线索池
     */
    MerchantCluePool queryMerchantClue(MerchantCluePool merchantCluePool);

    /**
    * 查询esId 是否被绑定
    */
    int queryEsIdNumber(String esId);

    /**
     * 查询生效的clue
     * @param esId
     * @return
     */
    MerchantCluePool selectEffectClue(@Param("esId") String esId);

    int updateMerchantCluePool(MerchantCluePool merchantCluePool);

    List<String> getExitEsids(@Param("esIds")List<String> esIds);

    int  count();


}
