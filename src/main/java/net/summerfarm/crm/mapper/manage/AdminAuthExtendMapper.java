package net.summerfarm.crm.mapper.manage;

import net.summerfarm.pojo.DO.AdminAuthExtend;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface AdminAuthExtendMapper {



    /**
     * 根据userid查询绑定信息
     * @param type 类型
     * @param adminId 销售id
     * @return 钉钉绑定信息
     */
/*
    AdminAuthExtend selectByAdminId(@Param("type") Integer type, @Param("adminId") Integer adminId);
*/


}




