package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCommissionCategory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface CrmCommissionCategoryMapper {
    /**
     * 插入
     * @param record 数据
     * @return 0|1
     */
    int insertSelective(CrmCommissionCategory record);

    /**
     * 按id查询
     * @param id id
     * @return 品类奖励信息
     */
    CrmCommissionCategory selectByPrimaryKey(Long id);

    /**
     * 查询全部可用奖励系数
     * @return 全部可用奖励系数
     */
    List<CrmCommissionCategory> selectAll();

    /**
     * 按id更新
     * @param record 数据
     * @return 0|1
     */
    int updateByPrimaryKeySelective(CrmCommissionCategory record);
    /**
     * id存在则更新,不存在则新增
     * @param record 数据
     * @return 0|1
     */
    int insertOrUpdateById(CrmCommissionCategory record);

}