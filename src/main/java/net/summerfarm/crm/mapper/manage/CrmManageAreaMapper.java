package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmManageArea;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;
import net.summerfarm.crm.model.query.QueryManageAreaQuery;
import net.summerfarm.crm.model.vo.ManageAreaVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmManageAreaMapper {

    /**
     * 新增用户管理区域
     * @param record 用户管理区域
     * @return ok
     */
    int insertSelective(CrmManageArea record);

    /**
     * 编辑用户管理区域
     * @param record 用户管理区域
     * @return ok
     */
    int updateByPrimaryKeySelective(CrmManageArea record);

    /**
     * 查询区域配置页面信息
     * @param queryManageAreaQuery 查询条件：城市，区域，区域负责人，部门负责人
     * @return 区域配置页面信息
     */
    List<ManageAreaVo> selectManageArea(QueryManageAreaQuery queryManageAreaQuery);

    /**
     * 批量插入区域细腻些
     * @param mbId 区域id
     * @param subCity 区域nos
     * @param adminId 销售id
     */
    void insertArea(@Param("mbId") Integer mbId, @Param("city") List<Integer> subCity, @Param("adminId") Integer adminId);

    /**
     * 删除区域管理运营区域信息
     * @param mbId 区域id
     */
    void deleteArea(Integer mbId);

    /**
     * 区域信息
     * @param id 区域id
     * @param saveManageAreaInputId 需要排除的区域id
     * @return 区域信息
     */
    List<CrmBdAreaDTO> selectArea(@Param("id") Integer id, @Param("saveManageAreaInputId") Integer saveManageAreaInputId);

    /**
     * 获取bd所属城市的负责人
     * @param bdId bd id
     * @return 负责人信息
     * */
    ManageAreaVo selectManageByBdId(@Param("bdId")Integer bdId);
}