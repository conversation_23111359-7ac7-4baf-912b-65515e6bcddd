package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.FollowWhiteList;
import net.summerfarm.crm.model.vo.FollowWhiteListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface FollowWhiteListMapper {

    /**
     * 查询白名单列表
     * @param followWhiteList 查询条件
     * @return 白名单列表
     */
    List<FollowWhiteListVO> queryFollowWhiteList(FollowWhiteListVO followWhiteList);

    /**
     * 编辑白名单
     * @param merchantFollowWhiteList 白名单信息
     * @return 数量
     */
    Integer updateFollowWhite(FollowWhiteList merchantFollowWhiteList);

    /**
     * 新增白名单
     * @param merchantFollowWhiteList 白名单信息
     * @return 数量
     */
    Integer insertFollowWhite(FollowWhiteList merchantFollowWhiteList);


    /**
     * 新增白名单
     * @param merchantFollowWhiteList 白名单信息
     * @return 数量
     */
    Integer insertFollowWhites(@Param("list") List<FollowWhiteList> merchantFollowWhiteList);
    /**
     * 根据用户id查询其在白名单中的信息
     * @param mId 用户id
     * @return 用户信息
     */
    FollowWhiteList queryFollowWhiteListOne(@Param("mId") Long mId);

    /**
     * 根据用户id列表查询白名单信息
     *
     * @param mIds 用户id列表
     * @return
     */
    List<FollowWhiteList> queryFollowWhiteListByMids(@Param("mIds") List<Long> mIds);

    /**
     * 删除白名单
     * @param mIds 商户id
     * @return ok
     */
    Integer deleteFollowWhiteByMids(@Param("mIds") List<Long> mIds);

    /**
     * 删除白名单
     * @param mId 商户id
     * @return ok
     */
    Integer deleteFollowWhite(@Param("mId") Long mId);


    /**
     * 查询白名单数据
     *
     * @param adminId 管理员id
     * @return {@link List}<{@link FollowWhiteList}>
     */
    List<FollowWhiteList> selectByBd(@Param("adminId") Integer adminId);

}
