package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.MerchantSubAccount;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MerchantSubAccountMapper {

    /**
     * 查询店铺下未删除账号
     *
     * @param mId    店铺id
     * @return 子账号列表
     */
    List<MerchantSubAccount> selectByMId(@Param("mId") Long mId);

    /**
     * 查询店铺下未删除账号
     *
     * @param mId    店铺id
     * @return 子账号列表
     */
    List<MerchantSubAccount> selectByMIdlAll(@Param("mId") Long mId);

    /**
     *
     * @param accountId
     * @return
     */
    MerchantSubAccount selectById(Long accountId);

    /**
     * 通过unionid查询主账户
     *
     * @param unionId
     * @return {@link MerchantSubAccount}
     */
    MerchantSubAccount selectByUnionid(@Param("unionId")String unionId);

    /**
     * 通过mid查询
     *
     * @param mIds 店铺id列表
     * @return {@link MerchantSubAccount}
     */
    List<MerchantSubAccount> selectByMIds(@Param("mIds") List<Long> mIds);

    List<MerchantSubAccount> selectByUnionidIn(@Param("unionIds") List<String> unionIds);
}