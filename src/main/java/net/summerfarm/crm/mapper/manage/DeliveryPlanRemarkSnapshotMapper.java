package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.DeliveryPlanRemarkSnapshot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeliveryPlanRemarkSnapshotMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DeliveryPlanRemarkSnapshot record);

    int insertSelective(DeliveryPlanRemarkSnapshot record);

    DeliveryPlanRemarkSnapshot selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeliveryPlanRemarkSnapshot record);

    int updateByPrimaryKey(DeliveryPlanRemarkSnapshot record);

    List<DeliveryPlanRemarkSnapshot> selectByTypeBusinessIds(@Param("type") Integer type, @Param("businessIds")List<String> businessIds);

    void batchInsert(@Param("list") List<DeliveryPlanRemarkSnapshot> deliveryPlanRemarkSnapshotList);
}