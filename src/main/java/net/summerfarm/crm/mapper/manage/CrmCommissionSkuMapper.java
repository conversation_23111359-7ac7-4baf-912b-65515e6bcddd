package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCommissionSku;
import net.summerfarm.crm.model.query.CommissionRewardSkuQuery;
import net.summerfarm.crm.model.query.CopyInfoQuery;
import net.summerfarm.crm.model.vo.CrmCommissionSkuVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmCommissionSkuMapper {
    /**
     * 删除奖励sku奖励
     * @param id id
     * @return ok
     */
    int deleteByPrimaryKey(Integer id);
    /**
     * 增加奖励sku奖励
     * @param record 奖励sku奖励
     * @return ok
     */
    int insertSelective(CrmCommissionSku record);
    /**
     * 编辑奖励sku奖励
     * @param record 奖励sku奖励
     * @return ok
     */
    int updateByPrimaryKeySelective(CrmCommissionSku record);

    /**
     * 查询奖励sku
     * @param commissionRewardSkuQuery 查询条件
     * @return 奖励sku列表
     */
    List<CrmCommissionSkuVo> selectRewardSku(CommissionRewardSkuQuery commissionRewardSkuQuery);

    /**
     * 是否存在奖励sku
     * @param crmCommissionSkuVo 查询内容
     * @return 存在的数量
     */
    int exist(CrmCommissionSkuVo crmCommissionSkuVo);

    /**
     * 复制与被复制的区域是否存在
     * @param copyInfoQuery 复制与被复制的区域
     * @return sku
     */
    List<String> isExistAreaSkuInfo(CopyInfoQuery copyInfoQuery);

    /**
     * 复制奖励sku
     * @param copyAreaSkuInfo 复制区域信息
     * @param skuInfo sku信息
     * @param adminId 销售id
     */
    void copyAreaSku(@Param("copyAreaSkuInfo") String copyAreaSkuInfo, @Param("skuInfo") String skuInfo, @Param("adminId") Integer adminId);

    /**
     * 删除奖励sku
     * @param zoneName 区域信息
     */
    void deleteByZoneName(String zoneName);

    /**
     * 插入或更新用户奖励sku
     * @param record 更新内容
     * @return 数量
     */
    int insertOrUpdateById(CrmCommissionSku record);
}