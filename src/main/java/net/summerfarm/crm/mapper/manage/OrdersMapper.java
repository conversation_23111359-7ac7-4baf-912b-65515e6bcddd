package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.Orders;
import net.summerfarm.crm.model.dto.MerchantRecentSpuDTO;
import net.summerfarm.crm.model.dto.PdNameCategoryTypeDTO;
import net.summerfarm.crm.model.dto.ShortTermLostSpuDTO;
import net.summerfarm.crm.model.dto.followUpRelation.LastOrderDTO;
import net.summerfarm.crm.model.dto.merchantsituation.approval.RecentOrderDTO;
import net.summerfarm.crm.model.query.task.TimingRemindOrder;
import net.summerfarm.crm.model.vo.OrderDeliveryVO;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface OrdersMapper {
    int deleteByPrimaryKey(Long orderId);

    int insert(Orders record);

    int insertSelective(Orders record);

    Orders selectByPrimaryKey(Long orderId);

    int updateByPrimaryKeySelective(Orders record);

    int updateByPrimaryKey(Orders record);

    /**
     * 查询私海用户当日订单配送情况
     * @param mIds
     * @param deliveryTime 配送时间
     * @return
     */
    List<OrderDeliveryVO> selectOrderDeliveryByMid(@Param("mIds") List<Long> mIds, @Param("deliveryTime") LocalDate deliveryTime);

    Orders selectByOrderNo(@Param("orderNo") String orderNo);


    /**
     * 两日内均价
     *
     * @param sku sku
     * @param areaNo 区域
     * @return {@link BigDecimal}
     */
    BigDecimal twoDaysAveragePrice(@Param("sku") String sku,@Param("areaNo")Integer areaNo);

    /**
     * 按sku和商户id计算60天订单个数
     *
     * @param mId 商户id
     * @param skus 商品skus
     * @return {@link Integer}
     */
    Integer countOrderBySpuAndMerchantDuringXDay(@Param("mId")Long mId, @Param("skus")List<String> skus, @Param("numOfDay")Integer numOfDay);

    List<String> selectSixtyDaySpuByMerchant(@Param("mId")Long mId);

    List<MerchantRecentSpuDTO> selectXDayPdIdByMId(@Param("mId")Long mId, @Param("numOfDay")Integer numOfDay);

    List<PdNameCategoryTypeDTO> selectSixtyDaySpuWithCategoryTypeByMerchant(@Param("mId")Long mId);

    /**
     * 查询近{@param months}个月下过单但近{@param lastDays}天未下单SPU
     */
    List<ShortTermLostSpuDTO> selectShortTermLostSpuByMerchant(@Param("mId") Long mId, @Param("months") Integer months, @Param("lostDays") Integer lostDays);

    /**
     * 按类别和商家计算60天订单
     *
     * @param categoryId 类别id
     * @param mId        m id
     * @return {@link Integer}
     */
    Integer countSixtyDayOrderByCategoryAndMerchant(@Param("categoryId")Long categoryId,@Param("mId")Long mId);

    /**
     * 根据商户编号获取未完成订单数量-待配送、待收货
     * @param mId 客户编号
     * @return 数量
     */
    Integer getUnfilledOrderByMid(Long mId);

    /**
     * 根据商户编号获取未完的售后单
     * @param mId 客户编号
     * @return 数量
     */
    Integer getAfterUnfilledOrderByMid(Long mId);

    /**
     * 查询2天前下单且未设置配送计划订单
     *
     * @return {@link List}<{@link TimingRemindOrder}>
     */
    List<TimingRemindOrder> listTimingRemindOrder();

    Orders getOverOrder(@Param("mId") Long mid);

    /**
     * 根据mId和sku获取最新订单
     */
    RecentOrderDTO getLatestOrderBySku(@Param("mId") Long mId, @Param("sku") String sku);

    /**
     * 根据mId和sku获取最新履约订单
     */
    RecentOrderDTO getLatestFulfilledOrderBySku(@Param("mId") Long mId, @Param("sku") String sku);

    /**
     * 根据mId和订单状态列表获取订单数量和最近一次下单时间
     *
     * @param mId
     * @param statusList
     * @return
     */
    LastOrderDTO getLastOrderInfo(@Param("mId") Long mId, @Param("statusList") List<Integer> statusList);

    /**
     * 根据订单时间范围和状态列表获取客户ID和品牌ID
     *
     * @param orderTimeBegin 订单开始时间
     * @param orderTimeEnd 订单结束时间
     * @param statusList 订单状态列表
     * @return Orders对象列表，包含mId和adminId
     */
    List<Orders> getMidAndAdminIdBetweenOrderTime(@Param("orderTimeBegin") LocalDateTime orderTimeBegin,
                                                  @Param("orderTimeEnd") LocalDateTime orderTimeEnd,
                                                  @Param("statusList") List<Integer> statusList);

    /**
     * 根据客户ID列表和订单状态列表获取订单数量和最后一次下单时间
     *
     * @param mIds 客户ID列表
     * @param statusList 订单状态列表
     * @return LastOrderDTO列表，包含mId、orderCount和lastOrderTime
     */
    List<LastOrderDTO> listLastOrderInfo(@Param("mIds") List<Long> mIds, 
                                         @Param("statusList") List<Integer> statusList);


    /**
     * 获取今日是否有下单
     * @param mIds
     * @return
     */
    List<LastOrderDTO> listCurrentDayOrderInfo(@Param("mIds") List<Long> mIds);

}