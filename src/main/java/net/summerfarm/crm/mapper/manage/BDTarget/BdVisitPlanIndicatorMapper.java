package net.summerfarm.crm.mapper.manage.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorUpdate;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 销售拜访计划指标 Mapper接口
 * <AUTHOR>
@Repository
public interface BdVisitPlanIndicatorMapper {

    /**
     * 根据主键删除记录
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdVisitPlanIndicator record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<BdVisitPlanIndicator> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdVisitPlanIndicator selectByPrimaryKey(Long id);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdVisitPlanIndicatorUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdVisitPlanIndicator> list(BdVisitPlanIndicatorQuery query);

    /**
     * 批量更新指标当前值和潜力值
     * @param syncDataList 同步数据列表
     * @return 影响行数
     */
    int batchUpdateIndicatorValueAndPotential(@Param("list") List<BdVisitPlanIndicatorSync> syncDataList);

    /**
     * 更新单条指标当前值和潜力值
     * @param sync 同步数据
     * @return 影响行数
     */
    int updateIndicatorValueAndPotential(BdVisitPlanIndicatorSync sync);

    /**
     * 根据门店ID更新单条潜力值
     * @param indicator 指标更新对象
     * @return 影响行数
     */
    int updatePotentialValueByMerchantId(BdVisitPlanIndicatorUpdate indicator);

    /**
     * 根据拜访计划ID列表批量逻辑删除指标记录
     * @param visitPlanIds 拜访计划ID列表
     * @return 影响行数
     */
    int batchLogicalDeleteByVisitPlanIds(@Param("visitPlanIds") List<Long> visitPlanIds);

    /**
     * 根据门店ID查询拜访计划指标
     * @param merchantIds 门店ID列表
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectByMerchantIds(@Param("merchantIds") List<Long> merchantIds);

    /**
     * 根据销售ID查询拜访计划指标
     * @param salesId 销售ID
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectBySalesId(@Param("salesId") Integer salesId);

    /**
     * 根据目标指标ID列表查询拜访计划指标
     * @param targetIndicatorIds 目标指标ID列表
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectByTargetDetailIds(@Param("targetDetailIds") List<Long> targetDetailIds);

    /**
     * 根据门店ID列表和目标指标ID列表查询拜访计划指标
     * @param merchantIds 门店ID列表
     * @param targetDetailIds 目标指标ID列表
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectByMerchantIdsAndTargetDetailIds(@Param("merchantIds") List<Long> merchantIds, @Param("targetDetailIds") List<Long> targetDetailIds);

    /**
     * 根据组合条件查询拜访计划指标
     * @param bdDailyTargetIds 日目标ID列表
     * @param bdDailyTargetDetailIds 日目标明细ID列表
     * @param bdIds 销售ID列表
     * @param mIds 门店ID列表
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectByCompositeConditions(@Param("bdDailyTargetIds") List<Long> bdDailyTargetIds, 
                                                           @Param("bdDailyTargetDetailIds") List<Long> bdDailyTargetDetailIds,
                                                           @Param("bdIds") List<Integer> bdIds,
                                                           @Param("mIds") List<Long> mIds);

    /**
     * 根据销售拜访计划ID列表查询拜访计划指标
     * @param visitPlanIds 销售拜访计划ID列表
     * @return 拜访计划指标列表
     */
    List<BdVisitPlanIndicator> selectByVisitPlanIds(@Param("visitPlanIds") List<Long> visitPlanIds);

}