package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.InvoiceConfig;
import net.summerfarm.crm.model.vo.InvoiceConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/20 16:10
 */
public interface InvoiceConfigMapper {

    /**
     * 根据入参的参数获取到对应的抬头配置
     *
     * @param adminId: 大客户id
     * @param mId      门店id
     * @param invoiceTitle
     * @param taxNumber
     * @return
     */
    List<InvoiceConfigVo> selectByType(@Param("adminId") Integer adminId, @Param("mId") Long mId,@Param("invoiceTitle")String invoiceTitle,@Param("taxNumber")String taxNumber);

    /**
     * 按主键选择
     *
     * @param id id
     * @return {@link InvoiceConfig}
     */
    InvoiceConfig selectByPrimaryKey(@Param("id") Long id);

    /**
     * 修改门店发票抬头
     *
     * @param invoiceConfig
     * @return
     */
    int updateByPrimaryKeySelective(InvoiceConfig invoiceConfig);

    /**
     * 重置默认标志
     *
     * @param mId m id
     * @return int
     */
    int resetDefaultFlag(@Param("mId") Long mId);

    /**
     * 插入品牌抬头配置
     *
     * @param invoiceConfig 大客户抬头配置
     * @return int
     */
    int insertSelective(InvoiceConfig invoiceConfig);
}
