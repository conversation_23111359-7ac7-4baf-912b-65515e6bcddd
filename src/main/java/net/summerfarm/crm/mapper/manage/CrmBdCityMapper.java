package net.summerfarm.crm.mapper.manage;


import io.swagger.models.auth.In;
import net.summerfarm.crm.model.domain.CrmBdCity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【crm_bd_city(bd负责城市)】的数据库操作Mapper
* @createDate 2023-05-16 14:05:33
* @Entity generator.domain.CrmBdCity
*/
public interface CrmBdCityMapper {
    /**
     * 批量插入
     *
     * @param list  记录
     * @param bdId    bd id
     * @param bdName  bd名字
     * @param creator 创建人
     * @return int
     */
    int insertBatch(@Param("list") List<CrmBdCity> list, @Param("bdId") Integer bdId, @Param("bdName") String bdName, @Param("creator") String creator);

    /**
     * 删除bd负责城市
     *
     * @param bdId    bd id
     * @return int
     */
    int deleteByBdId(@Param("bdId")Integer bdId);

    /**
     * 通过admin id复制城市
     *
     * @param copiedBdId 被复制bd id
     * @param bdId       bd id
     * @param bdName     bd名字
     * @param creator    创造者
     * @return int
     */
    int copyCityByAdminId(@Param("copiedBdId")Integer copiedBdId,@Param("bdId")Integer bdId,@Param("bdName")String bdName, @Param("creator") String creator);

    /**
     * 按bd id选择
     *
     * @param bdId bd id
     * @return {@link List}<{@link CrmBdCity}>
     */
    List<CrmBdCity> selectByBdId(@Param("bdId")Integer bdId);

    /**
     * 按bd id选择
     *
     * @param bdId bd id
     * @return {@link List}<{@link CrmBdCity}>
     */
    List<CrmBdCity> listByBdId(@Param("bdIdList")List<Integer> bdId);


    /**
     * 按bd id选择
     *
     * @param bdId bd id
     * @return {@link List}<{@link CrmBdCity}>
     */
    List<CrmBdCity> selectByBdIdCityArea(@Param("bdId")Integer bdId,
                                         @Param("city")String city,
                                         @Param("area")String area);


}
