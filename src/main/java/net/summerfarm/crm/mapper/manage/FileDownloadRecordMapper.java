package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.FileDownloadRecord;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @date 2021/12/12 11:49
 */
@Repository
public interface FileDownloadRecordMapper {

    /**
     * 根据导出人id查询导出记录
     * @param adminId
     * @return
     */
    List<FileDownloadRecord> selectByAdminId(Integer adminId);

    /**
     * 新增导出记录
     * @param fileDownloadRecord
     * @return
     */
    int insert(FileDownloadRecord fileDownloadRecord);

    /**
     * 更新导出记录
     * @param record
     */
    void update(FileDownloadRecord record);

    /**
     * 修改状态
     * @param record
     */
    void updateFileName(FileDownloadRecord record);

    /**
     * 获取上传文件信息,根据UID
     * @param uId 唯一标识
     * @return 文件信息
     */
    FileDownloadRecord selectByUid(String uId);

    /**
     * 删除指定导出记录
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 删除用户所有的文件导出记录
     * @param adminId
     * @return
     */
    int deleteAll(Integer adminId);

}
