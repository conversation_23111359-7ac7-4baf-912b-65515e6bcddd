package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.FollowUpEvaluation;
import org.apache.ibatis.annotations.Param;

public interface FollowUpEvaluationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FollowUpEvaluation record);

    int insertSelective(FollowUpEvaluation record);

    FollowUpEvaluation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FollowUpEvaluation record);

    int updateByPrimaryKey(FollowUpEvaluation record);

    FollowUpEvaluation selectByFollowRecordId(@Param("followRecordId")Integer followRecordId);
}