package net.summerfarm.crm.mapper.manage.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdDailyTarget;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetUpdate;
import net.summerfarm.crm.model.query.objectiveManagement.BdDailyTargetPageQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售每日目标 Mapper接口
 * <AUTHOR>
@Repository
public interface BdDailyTargetMapper {

    /**
     * 选择性插入记录
     * @param record 记录对象
     * @return 影响行数
     */
    int insertSelective(BdDailyTarget record);

    /**
     * 批量插入记录
     * @param records 记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<BdDailyTarget> records);

    /**
     * 根据主键查询记录
     * @param id 主键
     * @return 记录对象
     */
    BdDailyTarget selectByPrimaryKey(Long id);

    /**
     * 根据主键列表批量查询记录
     * @param ids 主键列表
     * @return 记录对象列表
     */
    List<BdDailyTarget> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 选择性更新记录
     * @param record 更新对象
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BdDailyTargetUpdate record);

    /**
     * 根据查询条件查询记录列表
     * @param query 查询条件
     * @return 记录列表
     */
    List<BdDailyTarget> list(BdDailyTargetQuery query);


    /**
     * 根据销售ID列表和日期范围查询销售拜访目标
     *
     * @param bdIds 销售ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售拜访目标列表
     */
    List<BdDailyTarget> selectByBdIdsAndDateRange(@Param("bdIds") List<Integer> bdIds, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 分页查询每日销售目标
     * @param query 分页查询条件
     * @return
     */
    List<BdDailyTarget> listBdDailyTarget(BdDailyTargetPageQuery query);

    /**
     * 根据主键列表批量逻辑删除记录
     * @param ids 主键列表
     * @return 影响行数
     */
    int batchLogicalDeleteByIds(@Param("ids") List<Long> ids);

}