package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.query.areaConfig.BdAreaConfigQuery;
import net.summerfarm.crm.model.vo.AdminInfoVo;
import net.summerfarm.crm.model.vo.areaConfig.BdAreaConfigDetail;
import net.summerfarm.crm.model.vo.areaConfig.BdAreaConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/29 15:14
 */
public interface CrmBdOrgMapper {
    int deleteByPrimaryKey(Integer id);

    int deleteByKeyList(@Param("idList") List<Integer> idList);

    int insertSelective(CrmBdOrg record);

    CrmBdOrg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CrmBdOrg record);


    /**
     * 销售区域配置列表
     *
     * @param qry qry
     * @return {@link List}<{@link BdAreaConfigVo}>
     */
    List<BdAreaConfigVo> listBdAreaConfig(BdAreaConfigQuery qry);

    /**
     * 销售团队详情
     *
     * @param cityAdminId 城市管理员id
     * @return {@link BdAreaConfigDetail}
     */
    BdAreaConfigDetail selectAreaConfigDetail(@Param("cityAdminId") Integer cityAdminId, @Param("salesAreaId") Integer salesAreaId);

    /**
     * 获取下级
     *
     * @param parentId 父id
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> selectByParentId(@Param("parentId") Integer parentId);

    /**
     * 指定级别的的 bd
     *
     * @param rankList 级别
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listByRank(@Param("rankList") List<Integer> rankList);

    /**
     * 根据 admin id 获取 组织信息
     *
     * @param adminId
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listByBdId(@Param("adminId")Integer adminId);

    /**
     * 查询指定级别的销售
     *
     * @param bdId 销售id
     * @param rank 级别
     * @return {@link CrmBdOrg}
     */
    CrmBdOrg selectByBdIdAndRank(@Param("bdId") Integer bdId, @Param("rank") Integer rank);

    /**
     * 获取bd 的所有下级
     *
     * @param parentId 父id
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listChildByParentId(@Param("parentId") Integer parentId);

    /**
     * 获取用户的所有上级
     *
     * @param childrenId
     * @param notContainChildren 是否包含自身 true:不包含 false:包含
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listParentByChildrenId(@Param("childrenId") Integer childrenId, @Param("notContainChildren") Boolean notContainChildren);

    /**
     * 获取bd 指定级别的下级
     *
     * @param parentId 父id
     * @param rank
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<AdminInfoVo> listChildByParentIdAndRank(@Param("parentId") String parentId, @Param("rank") Integer rank);

    /**
     * 根据 bdid 和 rank 删除 bd
     *
     * @param bdId 销售id
     * @param rank 排名
     * @return int
     */
    int deleteByBdIdAndRank(@Param("bdId")Integer bdId,@Param("rank")Integer rank);

    /**
     * 获取bd 指定级别的等级
     *
     * @param bdIds bdid
     * @param rank
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listAdminIdRank(@Param("bdIds") List<Integer> bdIds, @Param("rank") Integer rank);


    /**
     * 获取ids
     *
     * @param bdIds ids
     * @return {@link List}<{@link CrmBdOrg}>
     */
    List<CrmBdOrg> listByIds(@Param("ids") List<Integer> bdIds);

    /**
     * 根据城市和区域获取城市的M1级销售
     *
     * @param city 城市
     * @param area 区
     * @return 选定区域的M1销售
     */
    CrmBdOrg selectM1ByCityAndArea(String city, String area);

    /**
     * 根据等级选择销售.只选最低等级
     * 例如: 如果一个销售既是普通销售又是m1,当rank = bd时,这位销售不会被选中.
     */
    List<Integer> selectByMinRank(Integer rank);

    /**
     * 获取全部bdId
     *
     * @return
     */
    List<Integer> listAllBdId();
}