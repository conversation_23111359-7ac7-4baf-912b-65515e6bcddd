package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.WeComUserContactWay;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【wecom_user_contact_way(企微客户联系「联系我」配置)】的数据库操作Mapper
* @createDate 2024-06-06 11:30:33
*/
public interface WeComUserContactWayMapper {

    int insert(WeComUserContactWay weComUserContactWay);

    void deleteByConfigId(@Param("configId") String configId);

    WeComUserContactWay selectByAdminIdAndState(@Param("adminId") Integer adminId, @Param("state") String state);
}
