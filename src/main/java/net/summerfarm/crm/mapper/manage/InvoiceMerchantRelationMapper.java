package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.InvoiceMerchantRelation;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @Date 2023/7/20 18:40
 */
public interface InvoiceMerchantRelationMapper {
    /**
     * 大客户对应的抬头与门店的链接关系
     * @param invoiceId 取自invoice_config表内(admin的抬头)
     * @param merchantId 取自merchant表中m_id
     * @return int
     */
    int insert(@Param("invoiceId") Long invoiceId, @Param("merchantId") Long merchantId);

    /**
     * 删除大客户对应的抬头与门店的链接关系
     * @param merchantId 取自merchant表中m_id
     * @return int
     */
    int deleteByInvoiceIdAndMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 查询大客户对应的抬头与门店的链接关系
     * @param merchantId 取自merchant表中m_id
     * @return int
     */
    InvoiceMerchantRelation selectByMerchantId(@Param("merchantId") Long merchantId);
}
