package net.summerfarm.crm.mapper.manage;

import net.summerfarm.common.annotation.RequiresDataPermission;
import net.summerfarm.crm.model.domain.SampleApplyReview;

import net.summerfarm.crm.model.vo.SampleApplyReviewVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface SampleApplyReviewMapper {

    int insertSelective(SampleApplyReview record);

    int updateByPrimaryKeySelective(SampleApplyReview record);

    /**
     * 分页查询样品申请列表
     * @param mName 商户名称
     * @param status 申请状态
     * @param areaNo 区域no
     * @param bdId 销售id
     * @return 样品申请列表
     */
    @RequiresDataPermission(originalField = "sa.area_no")
    List<SampleApplyReviewVO> selectByBdIds(@Param("mName") String mName, @Param("status") Integer status, @Param("areaNo") Integer areaNo, @Param("bdId") Integer bdId);

    SampleApplyReviewVO selectSampleApplyReviewVOById(Integer sampleApplyId);

    /**
     * 查询样品审核记录
     * @param sampleApplyId 样品id
     * @param status 审核状态
     * @return 样品审核记录
     */
    SampleApplyReview isReview(@Param("sampleApplyId") Integer sampleApplyId, @Param("status") Integer status);

    /**
     * 根据样品申请id变更样品审核
     * @param sampleApplyReview 样品申请审核内容
     * @return ok
     */
    int updateBySampleId(SampleApplyReview sampleApplyReview);

}




