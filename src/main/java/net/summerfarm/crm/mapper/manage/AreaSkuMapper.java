package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.AreaSku;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

public interface AreaSkuMapper {

    AreaSku selectOneBySkuAndAreaNo(@Param("sku") String sku, @Param("areaNo") Integer areaNo);

    /**
     * 按sku和区域编号选择价格
     *
     * @param sku    sku
     * @param areaNO 区域编号
     * @return {@link BigDecimal}
     */
    BigDecimal selectPriceBySkuAndAreaNo(@Param("sku") String sku, @Param("areaNo") Integer areaNO);

    List<String> selectSkuBySkuIn(@Param("skuCollection") Collection<String> skuCollection);

    List<AreaSku> selectBySkuInAndAreaNo(@Param("skuCollection")Collection<String> skuCollection,@Param("areaNo")Integer areaNo);
}