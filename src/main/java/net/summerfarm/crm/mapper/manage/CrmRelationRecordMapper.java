package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmRelationRecord;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CrmRelationRecordMapper {
    /**
     * 跟进id删除
     * @param id id
     * @return 0|1
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入sql
     * @param record 插入信息
     * @return 0|1
     */
    int insertSelective(FollowUpRelation record);

    /**
     * 批量插入商户流转记录
     * @param followUpRelations 商户流转信息
     * @return 0| followUpRelations.size
     */
    int bulkInsert(@Param("list") List<FollowUpRelation> followUpRelations);
    /**
     * 根据id查询流转记录
     * @param id id
     * @return 流程信息
     */
    CrmRelationRecord selectByPrimaryKey(Integer id);

    /**
     * 更新操作,根据id
     * @param record 更新内容,id必传
     * @return 0|1
     */
    int updateByPrimaryKeySelective(CrmRelationRecord record);

    /**
     * 查询商户所有流转记录
     * @param mId 商户id
     * @return 商户所有流转记录
     */
    List<CrmRelationRecord> selectByMid(Integer mId);
}