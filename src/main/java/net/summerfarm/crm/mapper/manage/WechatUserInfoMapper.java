package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.WechatUserInfo;
import net.summerfarm.crm.model.vo.wechat.WechatCustomerInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/8/25 18:15
 */
public interface WechatUserInfoMapper {
    int insertSelective(WechatUserInfo record);

    int updateByPrimaryKeySelective(WechatUserInfo record);

    /**
     * 查询用户是否存在
     *
     * @param userId  用户id
     * @param unionId 企微 id
     * @return {@link WechatUserInfo}
     */
    WechatUserInfo selectByUserIdAndUnionId(@Param("userId")String userId,@Param("unionId")String unionId);

    /**
     * 查询用户是否存在
     *
     * @param userIds  用户ids
     * @param unionId 企微 id
     * @return {@link WechatUserInfo}
     */
    List<WechatUserInfo> selectByUserIdsAndUnionId(@Param("userIds")List<String> userIds,@Param("unionId")String unionId);
    /**
     * 根据用户id和unionid更新状态
     *
     * @param userId  用户id
     * @param unionId unionid
     * @param status  状态
     * @return int
     */
    int updateStatusByUserIdAndUnionId(@Param("userId")String userId,@Param("unionId")String unionId,@Param("status")Integer status);

    /**
     * 根据用户 id 查询 union id 存在的列表
     *
     * @param userId   用户id
     * @param unionIds union id 列表
     * @return {@link List}<{@link WechatUserInfo}>
     */
    List<WechatUserInfo> selectByUserIdAndUnionIds(@Param("userId")String userId,@Param("unionIds")List<String> unionIds);

    /**
     * 批量插入
     *
     * @param list 列表
     * @return int
     */
    int insertBatch(@Param("list")List<WechatUserInfo> list);

    /**
     * 更新添加渠道
     *
     * @param state 状态
     * @param userInfo    id
     */
    void updateStateById(@Param("state")String state,@Param("userInfo")List<WechatCustomerInfo> userInfo);

    /**
     * 根据用户 id 查询 union id 存在的列表
     *
     * @param userId   用户id
     * @return {@link List}<{@link WechatUserInfo}>
     */
    List<WechatUserInfo> selectByUserId(@Param("userId")String userId);


    /**
     * 根据unionid查询状态为正常(1)的用户
     *
     * @param unionIds
     * @return
     */
    List<WechatUserInfo> selectActiveByUnionIds(@Param("unionIds")List<String> unionIds);


    List<WechatUserInfo> selectByUnionid(@Param("unionid")String unionid);

}