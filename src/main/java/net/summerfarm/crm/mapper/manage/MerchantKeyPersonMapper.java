package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.MerchantKeyPerson;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface MerchantKeyPersonMapper {

    /**
     * 插入商户kp
     * @param record 商户kp信息
     * @return
     */
    int insertSelective(MerchantKeyPerson record);

    /**
     * 根据id选择
     * @param id kp的id
     * @return kp信息
     */
    MerchantKeyPerson selectByPrimaryKey(Long id);

    /**
     * 更新商户kp
     * @param record 商户kp信息
     * @return 0|1
     */
    int updateByPrimaryKeySelective(MerchantKeyPerson record);

    /**
     * 根据id选择
     * @param mId 商户id
     * @return kp信息
     */
    List<MerchantKeyPerson> selectKeyPerson(Long mId);

    List<MerchantKeyPerson> selectByIds(@Param("kpIds") List<Long> kpIds);
}