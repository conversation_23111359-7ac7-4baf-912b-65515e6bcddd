package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.Contact;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @createDate 2022-08-22 14:22:06
*/
public interface ContactMapper {
    /**
     * 插入联系地址数据
     * @param record 联系地址
     * @return ok
     */
    int insertSelective(Contact record);

    /**
     * 查询联系地址数据
     * @param id 联系地址id
     * @return 查询联系地址数据
     */
    Contact selectByPrimaryKey(Long id);

    /**
     * 查询联系地址数据
     * @param ids 联系地址id
     * @return 查询联系地址数据
     */
    List<Contact> selectByPrimaryKeys(@Param("ids") List<Long> ids);

    /**
     * 修改联系地址数据
     * @param record 联系地址
     * @return ok
     */
    int updateByPrimaryKeySelective(Contact record);

    /**
     * 查询联系地址数据
     * @param mId 商户id
     * @return 查询联系地址数据
     */
    List<Contact> selectByMid(Long mId);

    /**
     * 根据手机号码查询联系人
     *
     * @param phone 电话
     * @param mId
     * @return {@link List}<{@link Contact}>
     */
    List<Contact> selectByPhone(@Param("phone")String phone,@Param("mId")Long mId);

    /**
     * 按id更新poi
     *
     * @param contactId 联系人id
     * @param poi       芋泥
     */
    void updatePoiById(@Param("contactId")Integer contactId,@Param("poi")String poi);

    /**
     * 查询商户联系方式, where status=1
     */
    List<Contact> selectByMidIn(@Param("mIds") Collection<Long> mIds);
}
