package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmFollow;
import net.summerfarm.crm.model.query.ClueDetailQuery;

import java.util.List;

public interface CrmFollowMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmFollow record);

    int insertSelective(CrmFollow record);

    CrmFollow selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmFollow record);

    int updateByPrimaryKey(CrmFollow record);

    List<CrmFollow> query(ClueDetailQuery cluClueQuery);
}