package net.summerfarm.crm.mapper.manage;

import akka.io.Inet;
import net.summerfarm.crm.model.domain.CrmTaskDetail;
import net.summerfarm.crm.model.vo.task.TaskDetailExportVo;
import net.summerfarm.crm.model.vo.task.TaskDetailVo;
import net.summerfarm.crm.model.vo.task.TimingTaskCountVo;
import net.summerfarm.crm.model.vo.task.TimingTaskVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/10/8 11:56
 */
public interface CrmTaskDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmTaskDetail record);

    int insertSelective(CrmTaskDetail record);

    CrmTaskDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmTaskDetail record);

    int updateByPrimaryKey(CrmTaskDetail record);

    /**
     * 查询任务中存在的门店
     *
     * @param taskId
     * @param mIds     m id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> listMidByMIdsAndTaskId(@Param("taskId")Integer taskId,@Param("mIds")List<Integer> mIds);

    /**
     * 插入新增
     *
     * @param insertList 插入列表
     */
    void insertBatch(@Param("insertList") List<Integer> insertList,@Param("taskId")Integer taskId,@Param("sourceId")String sourceId);

    /**
     * 根据门店 id 新增任务详情
     *
     * @param mId      门店 Id
     * @param taskId   任务id
     * @param sourceId 卡券 id
     */
    void insertByMid(@Param("mId") Integer mId,@Param("taskId")Integer taskId,@Param("sourceId")String sourceId);

    /**
     * 导出任务明细
     *
     * @param taskId 任务id
     * @return {@link List}<{@link TaskDetailExportVo}>
     */
    List<TaskDetailExportVo> taskExport(Integer taskId);

    /**
     * 销售任务详情
     *
     * @param bdId   销售id
     * @param status 状态     0:未完成;1:已完成
     * @param taskId 任务 id
     * @param type   任务类型 0:发券;1:拜访;
     * @return {@link List}<{@link TaskDetailVo}>
     */
    List<TaskDetailVo> listDetail(@Param("bdId")List<Integer> bdId,@Param("status")Integer status,@Param("taskId") Integer taskId,@Param("type")Integer type);

    /**
     * 根据门店和类型查询任务详情
     *
     * @param mIds       门店 Id
     * @param type       类型
     * @return {@link List}<{@link CrmTaskDetail}>
     */
    List<CrmTaskDetail> listByMIdAndType(@Param("mIds")List<Integer> mIds,@Param("type")Integer type);

    /**
     * 查询门店省心送订单
     *
     * @param mId       门店 Id
     * @param type       类型
     * @return {@link List}<{@link CrmTaskDetail}>
     */
    List<CrmTaskDetail> listNonNullSourceTask(@Param("mId")Integer mId, @Param("type")Integer type);

    /**
     * 取消任务
     *
     * @param id id
     */
    void cancelTask(@Param("sourceId") String id);

    /**
     * 省心送订单个数
     *
     * @param bdIds 销售id
     * @return {@link TimingTaskCountVo}
     */
    TimingTaskCountVo timingTaskCount(@Param("bdIds") List<Integer> bdIds);

    /**
     * 省心送任务下载
     *
     * @param day 一天
     * @return {@link List}<{@link TimingTaskVo}>
     */
    List<TimingTaskVo> timingTaskDownload(LocalDate day);

    /**
     * 查询mid对应taskId中是否有未完成任务
     *
     * @param taskId  taskId
     * @param status status
     * @param mId     m id
     * @return {@link List}<{@link Integer}>
     */
    Integer hasNotFinishTask(@Param("taskId")Long taskId,@Param("mId")Long mId,@Param("status")Integer status);

    /**
     * 拜访销售任务详情
     *
     * @param bdId   销售id
     * @param status 状态     0:未完成;1:已完成
     * @param taskId 任务 id
     * @param type   任务类型 0:发券;1:拜访;
     * @return {@link List}<{@link TaskDetailVo}>
     */
    List<TaskDetailVo> listDetailForVisitTask(@Param("bdId")List<Integer> bdId,@Param("status")Integer status,@Param("taskId") Integer taskId,@Param("type")Integer type);

    /**
     * 拜访销售任务详情
     *
     * @param status 状态     0:未完成;1:已完成
     * @param mId    mId      mId
     * @param type   任务类型 0:发券;1:拜访;
     * @return {@link List}<{@link TaskDetailVo}>
     */
    List<TaskDetailVo> listDetailForMId(@Param("mId")Long mId,@Param("status")Integer status,@Param("type")Integer type);

    /**
     * 批量修改任务状态
     *
     * @param status status
     * @param upsertList 插入列表
     */
    void upsertBatch(@Param("upsertList") List<Integer> upsertList,@Param("status")Integer status);


    List<Integer> selectOverTask(@Param("taskId")Long taskId);
}