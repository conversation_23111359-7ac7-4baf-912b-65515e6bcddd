package net.summerfarm.crm.mapper.manage;

import com.github.yulichang.base.MPJBaseMapper;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.query.crmjob.CrmJobMerchantListQueryDTO;
import net.summerfarm.crm.model.query.crmjobv2.CrmJobMerchantListQueryV2;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmJobMerchantDetailMapper extends MPJBaseMapper<CrmJobMerchantDetail> {
    int insertList(@Param("list") List<CrmJobMerchantDetail> list);

    int updateFollowUpRecordIdByMIdAndFollowUpRecordIdIsNull(
            @Param("updatedFollowUpRecordId") Integer updatedFollowUpRecordId,
            @Param("mId") Long mId);

    List<Long> selectMIdByJobId(@Param("jobId") Long jobId);

    /**
     * 根据查询条件查询任务列表.
     * 当bdIds不为空时,查询属于bdIds的私海的门店的任务
     * 当cities不为空时,查询属于公海里城市在cities里的任务
     * 都不空时,查并集
     */
    List<CrmJobMerchantDetail> selectByQuery(@Param("queryDTO") CrmJobMerchantListQueryDTO queryDTO,
                                             @Param("bdIds") List<Integer> bdIds,
                                             @Param("cities") List<String> cities);

    /**
     * 根据查询条件查询任务门店详情列表（包含item聚合信息）
     */
    List<CrmJobMerchantDetail> selectMerchantDetailWithItemsByQueryV2(
            @Param("jobType") Integer jobType,
            @Param("query") CrmJobMerchantListQueryV2 query,
            @Param("bdIds") List<Integer> bdIds,
            @Param("cities") List<String> cities);
}