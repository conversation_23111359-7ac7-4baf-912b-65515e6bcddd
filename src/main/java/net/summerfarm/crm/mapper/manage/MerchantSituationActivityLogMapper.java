package net.summerfarm.crm.mapper.manage;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.summerfarm.crm.model.domain.MerchantSituationActivityLog;
import net.summerfarm.crm.model.dto.MscActiveSpuDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface MerchantSituationActivityLogMapper extends BaseMapper<MerchantSituationActivityLog> {

    /**
     * 根据mId查询每个pdId最新(max(activity_time))的品类拓宽券
     * 仅查找审核通过(merchant_situation.status=2)的品类拓宽券(merchant_situation.situation_type=3)且activeEndTime >= minActiveEndTime
     * 若有需要,未来可拓展
     */
    List<MscActiveSpuDTO> selectByMIdAndActiveEndDateAfter(@Param("mId") Long mId, @Param("minActiveEndTime") LocalDate minActiveEndTime);
}