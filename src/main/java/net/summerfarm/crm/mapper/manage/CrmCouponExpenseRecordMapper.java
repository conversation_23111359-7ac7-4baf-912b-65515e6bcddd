package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmCouponExpensePool;
import net.summerfarm.crm.model.domain.CrmCouponExpenseRecord;
import net.summerfarm.crm.model.query.crmCouponExpensePool.CrmCouponExpensePoolQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmCouponExpenseRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmCouponExpenseRecord record);

    int insertSelective(CrmCouponExpenseRecord record);

    CrmCouponExpenseRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmCouponExpenseRecord record);

    int updateByPrimaryKey(CrmCouponExpenseRecord record);

    List<CrmCouponExpenseRecord> selectByPoolId(@Param("poolId") Long poolId);

    int deleteByPoolId(@Param("poolId") Long poolId);

    List<CrmCouponExpenseRecord> groupByAdminIdsAmount(@Param("adminIds") List<Integer> adminIds);

    List<CrmCouponExpensePool> crmCouponExpensePoolQuery(CrmCouponExpensePoolQuery crmCouponExpensePoolQuery);

    CrmCouponExpenseRecord selectPoolIdAdminId(@Param("poolId")Long poolId, @Param("adminId")Integer adminId);
}