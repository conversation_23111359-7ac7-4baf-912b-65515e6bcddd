package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.CrmSalesCity;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2023/8/29 15:09
 */
public interface CrmSalesCityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CrmSalesCity record);

    int insertSelective(CrmSalesCity record);

    CrmSalesCity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CrmSalesCity record);

    int updateByPrimaryKey(CrmSalesCity record);

    /**
     * 列出所有
     *
     * @return {@link List}<{@link CrmSalesCity}>
     */
    List<CrmSalesCity> listAll();

    /**
     * 根据销售区域查询列表
     *
     * @param salesAreaId 销售区域id
     * @return {@link List}<{@link BdSalesCityVo}>
     */
    List<BdSalesCityVo> listBySalesAreaId(@Param("salesAreaId") Integer salesAreaId);

    /**
     * 根据销售区域id查询城市列表
     */
    List<CrmSalesCity> selectBySalesAreaId(@Param("salesAreaId")Integer salesAreaId);


    /**
     * 查询不在指定销售区域的城市列表
     *
     * @param salesCityVoList 销售城市清单
     * @param salesAreaId     销售区域id
     * @return {@link List}<{@link CrmSalesCity}>
     */
    List<CrmSalesCity> listByCityAndSalesArea(@Param("salesCityVoList") List<CrmSalesCity> salesCityVoList,@Param("salesAreaId") Integer salesAreaId);

    /**
     * 批量插入
     *
     * @param list        列表
     * @param salesAreaId 销售区域id
     */
    void insertBatch(@Param("list") List<CrmSalesCity> list, @Param("salesAreaId") Integer salesAreaId);

    /**
     * 删除销售区域所有城市
     *
     * @param salesAreaId 销售区域id
     */
    void deleteBySalesAreaId(@Param("salesAreaId")Integer salesAreaId);

    /**
     * 获取 bd 负责的城市
     *
     * @param bdOrgIdList 销售orgid
     * @return {@link List}<{@link CrmSalesCity}>
     */
    List<CrmSalesCity> listByBdOrgId(@Param("bdOrgIdList") List<Integer> bdOrgIdList);
}