package net.summerfarm.crm.mapper.manage;

import com.github.yulichang.base.MPJBaseMapper;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.dto.FollowUpRelationDTO;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.vo.FollowUpRelationVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.model.vo.SalesDataVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

@Repository
public interface FollowUpRelationMapper extends MPJBaseMapper<FollowUpRelation> {
    /**
     * 插入操作
     *
     * @param record 插入信息
     * @return 0|1
     */
    int insertSelective(FollowUpRelation record);

    /**
     * 查询商户跟进信息
     *
     * @param selectKeys 查询条件,mid,reassign,adminName
     * @return 商户跟进信息
     */
    FollowUpRelation selectOne(FollowUpRelation selectKeys);

    /**
     * 根据id更新
     *
     * @param followUpRelation 更新的信息
     * @return 1|0
     */
    int updateReassign(FollowUpRelation followUpRelation);

    /**
     * 根据Mid更新
     *
     * @param followUpRelation 更新的信息
     * @return 1|0
     */
    int updateDangerDay(FollowUpRelation followUpRelation);

    List<FollowUpRelation> selectByAreaNo(FollowUpRelationVO followUpRelationVO);

    int countByAreaNo(FollowUpRelationVO followUpRelationVO);

    FollowUpRelation selectByMid(Integer mId);

    /**
     * 历史流转次数
     *
     * @param mId 商户id
     * @return 流转次数
     */
    int countByMId(Long mId);

    /**
     * 更新bd私海客户信息,adminId必传
     *
     * @param followUpRelation 更进信息
     */
    void updateReassignByAdminId(FollowUpRelation followUpRelation);

    /**
     * 更新私海商户信息
     *
     * @param adminId          bd的id
     * @param infoArea         商户所在的运营区域
     * @param followUpRelation 跟进信息
     */
    void updateReassignByAdminIdArea(@Param("adminId") Integer adminId, @Param("infoArea") List<Integer> infoArea, @Param("followUpRelation") FollowUpRelation followUpRelation);

    /**
     * 查询bd下的私海客户,分城市查询
     *
     * @param adminId 销售id
     * @return 私海客户信息
     */
    List<FollowUpRelation> selectByCity(@Param("adminId") Integer adminId, @Param("province") String province, @Param("city") String city, @Param("area") String area);

    /**
     * 查询商户的最新跟进bd信息
     *
     * @param mId 页数
     * @return 商户最新跟进bd名字及状态
     */
    MerchantVO queryFollow(@Param("mId") Long mId);


    /**
     * 获取所有大客户销售团队id
     *
     * @return adminIds
     */
    List<Integer> selectBdType();

    /**
     * 获取公,私海客户数
     *
     * @param salesDataQuery 查询条件
     * @return 公私海客户数
     */
    @Deprecated
    SalesDataVo selectMerchantNum(SalesDataQuery salesDataQuery);

    /**
     * 获取公海倒闭客户数
     *
     * @param salesDataQuery 搜索条件
     * @return 公海倒闭客户数
     */
    @Deprecated
    int selectOperateMerchantInOpenSea(SalesDataQuery salesDataQuery);

    /**
     * 自动释放私海客户
     *
     * @param mIds   商户id
     * @param reason 释放原因
     * @return 释放数量
     */
    int autoRelease(@Param("mIds") Collection<Long> mIds, @Param("reason") String reason);

    /**
     * 删除跟进记录 后面插入
     *
     * @param mIds 商户id
     * @return 释放数量
     */
    int deleteByMids(@Param("mIds") Collection<Long> mIds);

    FollowUpRelation selectLastFollowOne(@Param("adminId") Integer adminId, @Param("mId") Integer mId);


    /**
     * @param id       主键id
     * @param careBdId
     * @return
     */
    int updateCareAdminId(@Param("id") Integer id, @Param("careBdId") Integer careBdId);

    /**
     * 根据管理员id选择mid
     *
     * @param bdId 销售id
     * @return {@link List}<{@link Long}>
     */
    List<Long> selectMidByBdId(@Param("bdId") Integer bdId);

    /**
     * 查询有销售跟进的门店
     *
     * @param mIds m id
     * @return {@link List}<{@link Integer}>
     */
    List<Integer> selectMIdByMId(@Param("mIds") List<Integer> mIds);

    /**
     * 门店当前跟进 bd
     *
     * @param mId m id
     * @return {@link FollowUpRelation}
     */
    FollowUpRelation selectUnReassign(@Param("mId") Long mId);

    /**
     * 查询跟进用户信息批量
     *
     * @param mIds      mIds
     * @param bdAdminId bdAdminId
     * @return 用户信息
     */
    List<FollowUpRelation> batchSelect(@Param("mIds") List<Long> mIds, @Param("bdAdminId") Integer bdAdminId);

    /**
     * 根据ReassignTime查询adminId>0或不为空的数据
     */
    List<FollowUpRelation> selectPrivateSeaByReassignTimeAndBdIds(@Param("reassignTime") String reassignTime, @Param("bdIds") List<Integer> bdIds);

    List<FollowUpRelation> selectByReassignAndMIdIn(@Param("reassign") Boolean reassign, @Param("mIdCollection") Collection<Long> mIdCollection);

    List<Long> selectRecentlyReleasedByBdIdAndCity(@Param("bdId") Integer bdId,
                                                   @Param("province") String province,
                                                   @Param("city") String city,
                                                   @Param("numOfDay") Integer numOfDay);

    int updateDangerDayAndReasonByMId(@Param("updatedDangerDay")Integer updatedDangerDay,
                                      @Param("updatedReason")String updatedReason,
                                      @Param("releaseTime") LocalDateTime releaseTime,
                                      @Param("protectReason") String protectReason,
                                      @Param("mId")Long mId);

    List<FollowUpRelation> selectByAdminIdAndDangerDayIn(@Param("adminId")Integer adminId,@Param("dangerDayCollection")Collection<Integer> dangerDayCollection);

    List<FollowUpRelationDTO> selectFollowUpRelationsWithAdmins(@Param("mIds") List<Long> mIds);


}