package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.dto.BDTarget.MerchantDataDTO;
import net.summerfarm.crm.model.dto.RecommendContextDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店数据查询Mapper接口
 * 用于查询门店的各种业务数据，支持AI推荐逻辑
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Repository
public interface MerchantDataMapper {

    /**
     * 查询门店最近登录时间
     *
     * @param mId 门店ID
     * @return 最近登录时间
     */
    LocalDateTime selectLastLoginTime(@Param("mId") Long mId);

    /**
     * 查询门店最近10次历史拜访记录
     *
     * @param mId 门店ID
     * @return 拜访记录列表
     */
    List<MerchantDataDTO.VisitRecordDTO> selectRecentVisitRecords(@Param("mId") Long mId);

    /**
     * 查询门店最近10笔订单记录
     *
     * @param mId 门店ID
     * @return 订单记录列表
     */
    List<MerchantDataDTO.OrderRecordDTO> selectRecentOrderRecords(@Param("mId") Long mId);

    /**
     * 查询门店最近10笔订单的客诉内容
     *
     * @param mId 门店ID
     * @return 客诉记录列表
     */
    List<MerchantDataDTO.ComplaintRecordDTO> selectRecentComplaintRecords(@Param("mId") Long mId);

    /**
     * 判断门店是否为新客户（历史未下过订单）
     *
     * @param mId 门店ID
     * @return 是否为新客户
     */
    Boolean isNewCustomer(@Param("mId") Long mId);


    /**
     * 查询门店常购商品列表（针对老客户推荐）
     *
     * @param mId 门店ID
     * @param limit 查询数量限制
     * @return 常购商品列表
     */
    List<RecommendContextDTO.ProductPurchaseDTO> selectFrequentProducts(
            @Param("mId") Long mId, 
            @Param("limit") Integer limit);

    /**
     * 查询限定范围内的商品列表
     *
     * @param mId 门店ID
     * @param rangeTypeCode 查询范围类型代码：1-全品类，2-PB商品，3-指定SKU，4-指定SPU，5-乳制品，6-非乳制品，7-水果
     * @param limitValue 限定值（当rangeTypeCode为3或4时使用）
     * @param areaNo 门店所属区域编号
     * @param startDate 查询开始日期
     * @param limit 返回数量限制
     * @return 商品列表
     */
    List<RecommendContextDTO.ProductInRangeDTO> selectProductsInRange(
            @Param("mId") Long mId,
            @Param("rangeTypeCode") Integer rangeTypeCode,
            @Param("limitValue") String limitValue,
            @Param("areaNo") Integer areaNo,
            @Param("startDate") String startDate,
            @Param("limit") Integer limit);
}