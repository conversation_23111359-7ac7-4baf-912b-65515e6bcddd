package net.summerfarm.crm.mapper.manage;

import net.summerfarm.crm.model.domain.Config;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Repository
public interface ConfigMapper {

    /**
     * 获取配置值
     * @param key key
     * @return value
     */
    Config selectOne(@Param("key") String key);

    /**
     * 更新配置值
     * @param config 配置详情
     */
    void update(Config config);

    /**
     * 修改value
     * @param key key
     * @param value 值
     * @return ok
     */
    int updateValue(@Param("key") String key, @Param("value") String value);

}
