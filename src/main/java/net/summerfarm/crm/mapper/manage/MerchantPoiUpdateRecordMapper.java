package net.summerfarm.crm.mapper.manage;


import net.summerfarm.crm.model.domain.MerchantPoiUpdateRecord;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【merchant_poi_update_record(门店poi更新记录)】的数据库操作Mapper
 * @createDate 2023-06-27 16:32:17
 * @Entity generator.domain.MerchantPoiUpdateRecord
 */
public interface MerchantPoiUpdateRecordMapper {

    /**
     * 按id查询
     *
     * @param id id
     * @return {@link MerchantPoiUpdateRecord}
     */
    MerchantPoiUpdateRecord selectById(@Param("id") Long id);

    /**
     * 选择插入
     *
     * @param record 记录
     * @return int
     */
    int insertSelective(MerchantPoiUpdateRecord record);

    /**
     * 按主键选择更新
     *
     * @param record 记录
     * @return int
     */
    int updateByPrimaryKeySelective(MerchantPoiUpdateRecord record);
}
