package net.summerfarm.crm.feign.decoder;

import com.alibaba.fastjson.JSON;
import feign.FeignException;
import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.model.bo.FeignResultBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Type;


/**
 * <AUTHOR>
 * @Description feign解码器
 * @date 2022/6/17 13:15
 */
@Slf4j
public class FeignResultDecoder implements Decoder {

    private static final Logger LOGGER = LoggerFactory.getLogger(FeignResultDecoder.class);

    @Override
    public Object decode(Response response, Type type) throws IOException, DecodeException, FeignException {
        if (response.body() == null) {
            throw new DecodeException(response.status(), "没有返回有效的数据", response.request());
        }
        String bodyStr = Util.toString(response.body().asReader(Util.UTF_8));

        LOGGER.info("feign接口响应体为:{}",bodyStr);
        FeignResultBO feignResultBO = new FeignResultBO();
        try {
            feignResultBO = JSON.parseObject(bodyStr, type);
        } catch (Exception e) {
            feignResultBO.setCode(ResultConstant.PARAM_FAULT);
            feignResultBO.setMsg(e.getMessage());
        }
        return feignResultBO;
    }
}
