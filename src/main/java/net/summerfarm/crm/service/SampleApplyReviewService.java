package net.summerfarm.crm.service;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.domain.SampleApplyReview;
import net.summerfarm.crm.model.query.SampleApplyQuery;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import net.summerfarm.crm.model.vo.sample.CheckSampleDto;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface SampleApplyReviewService {

    /**
     * 分页查询
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param sampleApply 样品申请
     * @return 样品申请信息
     */
    AjaxResult selectSampleApplyReview(int pageIndex, int pageSize, SampleApply sampleApply);

    /**
     * 查询样品申请详情
     * @param sampleApplyId 样品申请Id
     * @return 样品申请详情
     */
    AjaxResult selectSampleApplyReviewVO(Integer sampleApplyId);

    /**
     * 样品申请审核
     * @param sampleApplyReview 样品申请审核信息
     * @return ok
     */
    AjaxResult sampleApplyReview(SampleApplyReview sampleApplyReview);

    /**
     * 定时任务，月底关闭所有样品申请
     */
    void closeMonthSampleApplyReview();

    /**
     * 冻结库存的样品申请部分数据库操作
     * @param jsonObject 样品申请信息
     * @return 成功与否
     */
    AjaxResult frozenInventory(JSONObject jsonObject);

    /**
     * 检查门店是否可以被风控
     * @param query
     * @return
     */
    CheckSampleDto checkMerchant(SampleApplyQuery query);

    /**
     * 检查该门店是否可以申请样品
     * @param sampleApplyReview
     * @return
     */
    CheckSampleDto checkMerchantSample(SampleApplyVO sampleApplyReview);
}
