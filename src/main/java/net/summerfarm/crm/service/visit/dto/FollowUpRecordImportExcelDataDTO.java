package net.summerfarm.crm.service.visit.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.FollowRecordEnum;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@EqualsAndHashCode(callSuper = true)
public class FollowUpRecordImportExcelDataDTO extends ImportExcelBaseDTO {

    /**
     * 商户id
     */
    @ExcelProperty("mid(必填)")
    @NotNull(message = "mid不能为空")
    private Long mId;

    /**
     * 销售名称
     */
    @ExcelProperty("销售名称(必填)")
    @NotBlank(message = "销售名称不能为空")
    private String adminName;

    /**
     * 拜访方式
     */
    @ExcelProperty("拜访方式(必填)")
    @NotBlank(message = "拜访方式不能为空")
    private String followUpWay;

    /**
     * 拜访时间
     */
    @ExcelProperty("填写时间(必填)")
    @NotBlank(message = "填写时间不能为空")
    private String followUpTime;

    /**
     * 拜访目的
     */
    @ExcelProperty("拜访目的(选填)")
    private String followUpObjective;

    /**
     * 拜访记录
     */
    @ExcelProperty("拜访记录(必填)")
    @NotBlank(message = "拜访记录不能为空")
    @Size(max = 2000, message = "拜访记录内容过长，请删减至2000个字符之内")
    private String condition;

    /**
     * 销售id
     */
    @ExcelIgnore
    private Integer adminId;

    /**
     * 添加时间
     */
    @ExcelIgnore
    private Date addTime;

    /**
     * 拜访目的id
     * 
     * @see FollowRecordEnum.VisitObjective#getId()
     */
    @ExcelIgnore
    private Integer visitObjective;

}
