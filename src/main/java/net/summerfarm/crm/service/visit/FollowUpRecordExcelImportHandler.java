package net.summerfarm.crm.service.visit;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.enums.DownloadCenterEnum.RequestSource;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.CrmDownloadCenterTypeEnum;
import net.summerfarm.crm.enums.FollowRecordEnum;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.service.FollowUpRecordService;
import net.summerfarm.crm.service.visit.dto.FollowUpRecordImportExcelDataDTO;
import net.summerfarm.pojo.DO.Admin;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FollowUpRecordExcelImportHandler
        extends DownloadCenterImportDefaultHandler<FollowUpRecordImportExcelDataDTO> {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private MerchantMapper merchantMapper;
    @Autowired
    private AdminMapper adminMapper;
    @Autowired
    private FollowUpRecordMapper followUpRecordMapper;
    @Autowired
    private ContactMapper contactMapper;
    @Autowired
    private FollowUpRecordService followUpRecordService;
    @Autowired
    private CrmConfig crmConfig;
    @Autowired
    private CrmBdOrgMapper bdOrgMapper;

    @Override
    public RequestSource getSource() {
        // download_center_type_config表配置的source
        return DownloadCenterEnum.RequestSource.XIANMU;
    }

    @Override
    public Integer getBizType() {
        return CrmDownloadCenterTypeEnum.IMPORT_FOLLOW_UP_RECORD.getType();
    }

    @Override
    protected void dealExcelData(List<FollowUpRecordImportExcelDataDTO> list, DownloadCenterDataMsg msg) {
        // 校验导入拜访记录权限
        verifyImportFollowUpRecordPermission(msg);
        if (CollectionUtils.isEmpty(list)) {
            log.info("导入的拜访记录为空，直接返回");
            return;
        }
        log.info("开始处理拜访记录的导入，size：{}", list.size());
        for (List<FollowUpRecordImportExcelDataDTO> subList : Lists.partition(list, 500)) {
            // 数据trim
            trimFollowUpRecords(subList);
            // 基础信息校验
            checkFollowUpRecords(subList);
            List<FollowUpRecordImportExcelDataDTO> checkSuccessList =
                    subList.stream().filter(x -> StringUtils.isEmpty(x.getErrorMsg())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(checkSuccessList)) {
                continue;
            }
            log.info("基础信息校验成功的拜访记录条数：{}", checkSuccessList.size());
            // 导入拜访记录
            saveFollowUpRecords(checkSuccessList);
        }
    }

    private void verifyImportFollowUpRecordPermission(DownloadCenterDataMsg msg) {
        if (msg == null || msg.getUserBase() == null || msg.getUserBase().getSystemOrigin() == null
                || msg.getUserBase().getBizUserId() == null) {
            throw new BizException("导入拜访记录时获取用户信息失败");
        }
        if (!SystemOriginEnum.ADMIN.getType().equals(msg.getUserBase().getSystemOrigin())) {
            throw new BizException("导入拜访记录时非鲜沐商城用户");
        }
        if (!followUpRecordService.hasImportFollowUpRecordPermission(msg.getUserBase().getBizUserId())) {
            throw new BizException("用户暂无该功能权限");
        }
    }

    private void trimFollowUpRecords(List<FollowUpRecordImportExcelDataDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(x -> {
            x.setAdminName(StringUtils.trim(x.getAdminName()));
            x.setFollowUpWay(StringUtils.trim(x.getFollowUpWay()));
            x.setFollowUpTime(StringUtils.trim(x.getFollowUpTime()));
            x.setFollowUpObjective(StringUtils.trim(x.getFollowUpObjective()));
            x.setCondition(StringUtils.trim(x.getCondition()));
        });
    }

    private void checkFollowUpRecords(List<FollowUpRecordImportExcelDataDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 获取存在的mId列表
        List<Long> mIds =
                list.stream().map(FollowUpRecordImportExcelDataDTO::getMId).distinct().collect(Collectors.toList());
        List<Long> existingMIds = merchantMapper.selectMIdByMId(mIds);

        // 获取在职的销售人员
        List<String> adminNames = list.stream().map(FollowUpRecordImportExcelDataDTO::getAdminName).distinct()
                .collect(Collectors.toList());
        List<Admin> existingAdmins = adminMapper.selectByRealnameIn(adminNames);
        Map<String, List<Admin>> adminMap = existingAdmins.stream().collect(Collectors.groupingBy(Admin::getRealname));

        // 定义拜访方式的有效选项
        Set<String> validFollowUpWays =
                new HashSet<>(Arrays.asList("普通拜访-微信", "普通拜访-企微", "普通拜访-电话", "普通上门拜访", "有效拜访", "不确定"));

        // 对于每个dto进行校验
        for (FollowUpRecordImportExcelDataDTO dto : list) {
            // 对于不存在的mId添加错误提示
            if (!existingMIds.contains(dto.getMId())) {
                dto.appendErrorMsg("客户不存在，请核对mid是否正确");
            }

            // adminName校验和adminId设置
            String adminName = dto.getAdminName();
            List<Admin> adminList = adminMap.get(adminName);
            if (CollectionUtils.isEmpty(adminList)) {
                dto.appendErrorMsg("销售人员不存在或已离职，请核对销售名称");
            } else if (adminList.size() > 1) {
                dto.appendErrorMsg("销售名称对应多个销售人员");
            } else {
                dto.setAdminId(adminList.get(0).getAdminId());
            }

            // 拜访方式校验
            if (!validFollowUpWays.contains(dto.getFollowUpWay())) {
                dto.appendErrorMsg("拜访方式填写不规范，请从模板规定选项中选择");
            }

            // 拜访时间校验
            try {
                LocalDateTime addTime = LocalDateTime.parse(dto.getFollowUpTime(), formatter);
                if (addTime.isBefore(LocalDate.now().plusDays(1L).atStartOfDay())) {
                    dto.setAddTime(DateUtils.localDateTimeToDate(addTime));
                } else {
                    dto.appendErrorMsg("填写时间不能为未来时间");
                }
            } catch (DateTimeParseException e) {
                dto.appendErrorMsg("填写时间格式不正确，请使用YYYY-MM-DD HH:MM:SS格式");
            }

            // 拜访目的校验
            if (StringUtils.isNotBlank(dto.getFollowUpObjective())) {
                FollowRecordEnum.VisitObjective visitObjective =
                        FollowRecordEnum.VisitObjective.getByStatusName(dto.getFollowUpObjective());
                if (!FollowRecordEnum.VisitObjective.getOfflineValidVisitObjectives().contains(visitObjective)) {
                    dto.appendErrorMsg("拜访目的填写不规范，请从模板规定选项中选择");
                } else {
                    dto.setVisitObjective(visitObjective.getId());
                }
            }
        }
    }

    private void saveFollowUpRecords(List<FollowUpRecordImportExcelDataDTO> basicPreCheckSuccessList) {
        List<Long> mIds = basicPreCheckSuccessList.stream().map(FollowUpRecordImportExcelDataDTO::getMId).distinct()
                .collect(Collectors.toList());
        Map<Long, Contact> mIdContactMap = this.getContactForMerchant(mIds);
        for (FollowUpRecordImportExcelDataDTO dto : basicPreCheckSuccessList) {
            try {
                // 检查是否存在重复记录
                int existingCount = followUpRecordMapper.countByMIdAndAdminIdAndAddTimeAndCondition(dto.getMId(), dto.getAdminId(),
                        dto.getAddTime(), dto.getCondition());
                if (existingCount > 0) {
                    dto.appendErrorMsg("该客户在当天已有拜访记录，请勿重复导入");
                    continue;
                }
                // 检查是否存在联系人信息
                Contact contact = mIdContactMap.get(dto.getMId());
                if (contact == null) {
                    dto.appendErrorMsg("该客户不存在联系人信息");
                    continue;
                }
                // 构建FollowUpRecordVO对象
                FollowUpRecordVO recordVO = new FollowUpRecordVO();
                recordVO.setmId(dto.getMId());
                recordVO.setAdminId(dto.getAdminId());
                recordVO.setAdminName(dto.getAdminName());
                recordVO.setFollowUpWay(dto.getFollowUpWay());
                recordVO.setCondition(dto.getCondition());
                recordVO.setAddTime(dto.getAddTime());
                recordVO.setVisitObjective(dto.getVisitObjective());
                recordVO.setContactId(Math.toIntExact(contact.getContactId()));
                recordVO.setStatus(FollowRecordEnum.Status.WAIT_DELIVERY.ordinal());
                // 调用保存方法
                AjaxResult ajaxResult = followUpRecordService.saveRecord(recordVO, true);
                if (!ajaxResult.isSuccess()) {
                    log.warn("保存拜访记录失败，mId:{}, adminId:{}, addTime:{}, message:{}", dto.getMId(), dto.getAdminId(),
                            dto.getAddTime(), ajaxResult.getMsg());
                    dto.appendErrorMsg("导入拜访记录失败");
                } else {
                    log.info("保存拜访记录成功，mId:{}, adminId:{}, addTime:{}", dto.getMId(), dto.getAdminId(),
                            dto.getAddTime());
                }
            } catch (Exception ex) {
                log.error("保存拜访记录失败，mId:{}, adminId:{}, addTime:{}", dto.getMId(), dto.getAdminId(), dto.getAddTime(),
                        ex);
                dto.appendErrorMsg("导入拜访记录失败");
            }
        }
    }

    private Map<Long, Contact> getContactForMerchant(List<Long> mIds) {
        if (CollectionUtils.isEmpty(mIds)) {
            return new HashMap<>();
        }

        return contactMapper.selectByMidIn(mIds).stream()
                .collect(Collectors.toMap(Contact::getmId, Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparingInt(Contact::getIsDefault) // 先选默认地址
                                .thenComparingLong(Contact::getContactId))));
    }

}
