package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.client.dto.CrmBdInfoDTO;
import net.summerfarm.crm.client.input.BdInfoQueryInput;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.query.MerchantQuery;
import net.summerfarm.crm.model.query.NearbyQuery;
import net.summerfarm.crm.model.vo.NearbyVO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface FollowUpRelationService {
    /**
     * 批量修改客戶归属bd
     * @param mIds mid集合
     * @param toAdminId 归属bd
     * @param reason 修改原因
     * @param type 是否可以跨白名单操作，1：可以
     * @param source 来源是否来自审核 1来自 来自审核要发送钉钉和创建关联关系
     * @return ok
     */
    AjaxResult reassign(List<Long> mIds, Integer toAdminId, String reason, Integer type, Integer source);

    /**
     * 批量修改客戶归属bd
     * @param mIds mid集合
     * @param toAdminId 归属bd
     * @param reason 修改原因
     * @param type 是否可以跨白名单操作，1：可以
     * @param source 来源是否来自审核 1来自 来自审核要发送钉钉和创建关联关系
     * @param turnAdminId 要流转到adminId
     * @return ok
     */
    AjaxResult reassign(List<Long> mIds, Integer toAdminId, String reason, Integer type, Integer source, Integer turnAdminId);
    /**
     * 修改客户跟进关系
     * @param mId mid
     * @return ok
     */
    AjaxResult bdAssign(List<Long> mId);

    /**
     * 公海列表
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param merchantQuery 查询条件
     * @return 列表
     */
    AjaxResult selectOpenSea(int pageIndex, int pageSize, MerchantQuery merchantQuery);

    /**
     * 查询客户归属BD流转记录
     * @param mId mid
     * @return 商户流转记录
     */
    AjaxResult queryRelationRecord(int pageIndex,int pageSize,Integer mId);

    /**
     * 查询私海数据
     * @param merchantQuery 查询条件
     * @param pageIndex 页码
     * @param pageSize 数量
     * @return 私海数据列表
     */
    AjaxResult queryPrivateSea(int pageIndex,int pageSize, MerchantQuery merchantQuery);

    /**
     * 搜索附近的客户
     * @param req 查询条件
     * @param pageIndex 页码
     * @param pageSize 数量
     * @return 附近的客户
     */
    AjaxResult merchantNearby(NearbyQuery req,int pageIndex,int pageSize);

    /**
     * 销售与商户关系变动,判断是否已存在跟进关系
     * 在销售-商户关系表中更新(或插入),在流转记录表中插入
     * @param followUpRelation 销售-商户关系
     */
    void updateAndInsertFollow(FollowUpRelation followUpRelation);


    AjaxResult<NearbyVO> queryByEsId(NearbyQuery req);

    /**
     *
     * @param adminId 大客户id
     * @param bdId bdid
     */
    Boolean updateAdminTurning(Integer adminId, Integer bdId);

    List<CrmBdInfoDTO> selectBdInfoByMidAndAreaNos(List<BdInfoQueryInput> list);
}
