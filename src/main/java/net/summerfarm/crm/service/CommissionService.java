package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.query.*;
import net.summerfarm.crm.model.vo.CrmBdConfigVo;
import net.summerfarm.crm.model.vo.CrmCommissionMerchantVo;
import net.summerfarm.crm.model.vo.CrmCommissionSkuVo;
import net.summerfarm.crm.model.vo.TableArea;

import java.util.List;


/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CommissionService {

    /**
     * 新城市新销售定义查询,配置在config中
     * @return 新销售及新城市定义
     */
    AjaxResult selectAreaBd();

    /**
     * 新城市,新销售定义修改
     * @param config 新城市,新销售定义
     * @return ok
     */
    AjaxResult saveAreaBd(Config config);

    /**
     * 按件奖励查询,区域奖励sku
     *
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param commissionRewardSkuQuery 查询条件
     * @return 区域及奖励sku列表
     */
    AjaxResult selectRewardSku(int pageIndex, int pageSize, CommissionRewardSkuQuery commissionRewardSkuQuery);

    /**
     * 按件奖励编辑
     * @param crmCommissionSkuVo 编辑内容
     * @return ok
     */
    AjaxResult saveRewardSku(CrmCommissionSkuVo crmCommissionSkuVo);

    /**
     * 按件奖励批量复制
     * @param copyInfoQuery 复制内容
     * @return ok
     */
    AjaxResult copyRewardSku(CopyInfoQuery copyInfoQuery);

    /**
     * 删除按件奖励sku
     * @param id id
     * @return ok
     */
    AjaxResult deleteRewardSku(int id);

    /**
     * 销售拉新奖励列表
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param zoneName 区域名称
     * @return 拉新奖励列表
     */
    AjaxResult selectPullNewMerchantReward(int pageIndex, int pageSize, String zoneName);

    /**
     * 删除销售拉新奖励
     * @param id id
     * @return ok
     */
    AjaxResult deletePullNewMerchantReward(int id);

    /**
     * 拉新奖励编辑
     * @param crmCommissionMerchantVo 编辑内容
     * @return ok
     */
    AjaxResult savePullNewMerchantReward(CrmCommissionMerchantVo crmCommissionMerchantVo);

    /**
     * 按新奖励复制
     * @param copyInfoQuery 复制内容
     * @return ok
     */
    AjaxResult copyPullNewMerchantReward(CopyInfoQuery copyInfoQuery);

    /**
     * 拉新奖励批量修改
     * @param batchModifyMerchantQuery 批量修改奖励内容
     * @return ok
     */
    AjaxResult batchModifyPullNewMerchantReward(BatchModifyMerchantQuery batchModifyMerchantQuery);

    /**
     * 激励指标列表
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param commissionRewardSkuQuery 查询条件
     * @return 激励指标列表
     */
    AjaxResult selectIncentiveIndex(int pageIndex, int pageSize,CommissionIncentiveIndexQuery commissionRewardSkuQuery);

    /**
     * 激励指标删除
     * @param id 页码
     * @return 删除是否成功
     */
    AjaxResult deleteIncentiveIndex(int id);

    /**
     * 激励指标编辑
     * @param crmBdConfigVo 变动内容
     * @return 编辑成功与否
     */
    AjaxResult saveIncentiveIndex(CrmBdConfigVo crmBdConfigVo);

    /**
     * 激励指标复制
     * @param copyIntInfoQuery 复制信息
     * @return ok
     */
    AjaxResult copyIncentiveIndex(CopyIntInfoQuery copyIntInfoQuery);

    /**
     * 批量修改激励指标
     * @param batchModifyIncentiveIndexQuery 修改内容
     * @return 成功与否
     */
    AjaxResult batchModifyIncentiveIndex(BatchModifyIncentiveIndexQuery batchModifyIncentiveIndexQuery);


    /**
     * 模糊查询表格中存在和不存在名称
     * @param isExist 是否存在
     * @param bdName bd名称
     * @return 存在和不存在名称
     */
    AjaxResult queryBdName(Boolean isExist, String bdName);

    /**
     * 获取上月核心客户数
     * @param adminId 销售id
     * @return 核心客户数
     */
    AjaxResult selectNumLastMonth(Integer adminId);

    /**
     * 获取销售按gmv奖励数据
     * @return 销售按gmv奖励数据
     */
    AjaxResult selectGmvTarget();

    /**
     * 编辑销售按gmv奖励系数
     * @param config 变更字段
     * @return 0|1
     */
    AjaxResult saveGmvTarget(Config config);

    /**
     * 查询按品类提成奖励
     * @return 查询按品类提成奖励
     */
    AjaxResult selectCategoryAward();

    /**
     * 更新品类提成奖励
     * @param crmCommissionCategory 品类提成奖励
     * @return 0|1
     */
    AjaxResult saveCategoryAward(CrmCommissionCategory crmCommissionCategory);

    /**
     * 查询客户等级系数
     * @param merchantLevelType 是否是核心客户 0否1是
     * @param grade 城市等级
     * @return 客户等级系数列表
     */
    AjaxResult selectCoreMerchant(Integer merchantLevelType, String grade);

    /**
     * 更新客户等级系数
     * @param crmCommissionMerchantLevel 客户等级系数
     * @return 0|1
     */
    AjaxResult saveCoreMerchant(CrmCommissionMerchantLevel crmCommissionMerchantLevel);

    /**
     * 核心客户净增长系数查询
     * @return 核心客户净增长系数
     */
    AjaxResult selectCoreMerchantsNetGrowth();

    /**
     * 核心客户净增长系数编辑
     * @param crmCommissionCoreMerchant 核心客户净增长系数信息
     * @return 核心客户净增长系数
     */
    AjaxResult saveCoreMerchantsNetGrowth(CrmCommissionCoreMerchant crmCommissionCoreMerchant);

    /**
     * 查询月活池
     * @return 月活池的值
     */
    AjaxResult selectMonthLivingCouponQuota();

    /**
     * 修改月活池
     * @param monthLivingCouponQuota 月活池额度
     * @return ok
     */
    AjaxResult updateMonthLivingCouponQuota(String monthLivingCouponQuota);

    /**
     * 每月一号更新月活池额度
     */
    void updateMonthLivingCouponQuotaEveryMonth();

    /**
     * 查询销售负责城市
     *
     * @return {@link List}<{@link TableArea}>
     */
    List<TableArea> selectBdCities();
}
