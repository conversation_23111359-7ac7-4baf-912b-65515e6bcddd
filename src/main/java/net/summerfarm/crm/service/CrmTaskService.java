package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.client.dto.TaskDTO;
import net.summerfarm.crm.client.dto.TaskDetailDTO;
import net.summerfarm.crm.model.domain.CrmTask;
import net.summerfarm.crm.model.dto.task.TimingTaskDTO;
import net.summerfarm.crm.model.query.task.*;
import net.summerfarm.crm.model.vo.task.*;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/7 10:53
 */
public interface CrmTaskService {
    /**
     * 任务列表
     *
     * @param query 查询
     * @return {@link PageInfo}<{@link TaskListVo}>
     */
    PageInfo<TaskListVo> taskList(TaskListQuery query);

    /**
     * 后台任务详情
     *
     * @param taskId 查询
     * @return {@link PageInfo}<{@link TaskListVo}>
     */
    CrmTask taskInfo(Integer taskId);

    /**
     * 新增任务
     *
     * @param input 输入
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Integer> taskInsert(TaskInsertInput input);

    /**
     * 新增任务明细
     *
     * @param input 输入
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<TaskImportResultCountVo> taskDetailInsert(@RequestBody TaskDetailInsertInput input);

    /**
     * 上传导入结果
     *
     * @param errorImportList 错误导入列表
     * @param filename        文件名
     * @param resId           res id
     */
    void uploadExportFile(List<TaskImportResultVo> errorImportList, String filename, Long resId);


    /**
     * 导出任务详细信息
     *
     * @param taskId 任务id
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> taskDetailExport(Integer taskId);

    /**
     * 导出任务详细信息
     *
     * @param taskId   任务id
     * @param resId    文件 id
     * @param filename 文件名称
     */
    void taskDetailExportAsync(Long resId, String filename, Integer taskId);

    /**
     * CRM任务详情
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link TaskDetailVo}>
     */
    CommonResult<PageInfo<TaskDetailVo>> taskDetail(@RequestBody TaskDetailQuery query);

    /**
     * 创建任务
     *
     * @param task 任务
     */
    void createTask(CrmTask task);

    /**
     * 创建省心送任务
     *
     */
    void createTimingTask();

    /**
     * 创建任务详情
     *
     * @param taskDetail 任务详情
     */
    CommonResult<String> createTaskDetail(TaskDetailDTO taskDetail);

    /**
     * 取消任务
     *
     * @param sourceId 任务id
     * @return {@link CommonResult}<{@link Void}>
     */
    void cancelTask(String sourceId);

    /**
     * 生效的省心送任务个数
     *
     * @return {@link CommonResult}<{@link Integer}>
     */
    CommonResult<TimingTaskCountVo> timingTaskCount();

    /**
     * 任务个数
     *
     * @param taskId 任务 id
     * @return {@link CommonResult}<{@link Integer}>
     */
    CommonResult<TaskCountVo> taskCount(Integer taskId);

    /**
     * 指定日期省心送订单下载
     *
     * @param day 一天
     * @param code 校验码
     * @return {@link CommonResult}<{@link Void}>
     */
    List<TimingTaskVo> timingTaskDownload(LocalDate day,String code);

    /**
     * 2 天前省心送订单未设置配送计划提醒
     */
    void sendTimingRemindMsg();


    void updateTaskUpStatus(List<Long> taskIds);
}
