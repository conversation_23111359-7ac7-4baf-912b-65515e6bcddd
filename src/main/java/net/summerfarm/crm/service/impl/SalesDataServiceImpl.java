package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;

import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.*;
import net.summerfarm.crm.enums.*;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.*;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.query.TeamDataQuery;
import net.summerfarm.crm.model.query.salesdata.NewCustomerQuery;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.model.vo.areaConfig.SalesAreaVo;
import net.summerfarm.crm.model.vo.saledata.NewCustomerVo;
import net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.constant.MessageKeyEnum;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.util.Producer;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.FollowWhiteListService;
import net.summerfarm.crm.service.SalesDataService;
import net.summerfarm.enums.PageJurisdictionEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.*;
import static net.summerfarm.crm.enums.DataSynchronizationInformationEnum.CRM_CITY_TODAY_GMV;

@Service
public class SalesDataServiceImpl extends BaseService implements SalesDataService {

    @Resource
    SalesDataMapper salesDataMapper;
    @Resource
    CrmManageBdMapper crmManageBdMapper;
    @Resource
    CrmBdConfigMapper crmBdConfigMapper;
    @Resource
    ConfigMapper configMapper;
    @Resource
    CrmBdAreaMapper crmBdAreaMapper;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private Producer producer;
    @Resource
    private MerchantSituationQuotaMapper merchantSituationQuotaMapper;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private CrmMerchantDayGmvMapper crmMerchantDayGmvMapper;
    @Resource
    private CrmMerchantMonthGmvMapper crmMerchantMonthGmvMapper;
    @Resource
    private CrmBdDayGmvMapper crmBdDayGmvMapper;
    @Resource
    private CrmBdMonthGmvMapper crmBdMonthGmvMapper;
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private CrmCommissionCoreMerchantMapper crmCommissionCoreMerchantMapper;
    @Resource
    private FollowWhiteListService followWhiteListService;
    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;
    @Resource
    private CrmCommissionMerchantLevelMapper crmCommissionMerchantLevelMapper;
    @Resource
    private CrmCityDayGmvMapper crmCityDayGmvMapper;
    @Resource
    private CrmCityMonthGmvMapper crmCityMonthGmvMapper;
    @Resource
    private CrmNewCustomersMonthMapper newCustomersMonthMapper;
    @Resource
    private CrmBdTodayGmvMapper crmBdtodayGmvMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private FollowUpRecordMapper followUpRecordMapper;
    @Resource
    private CrmCityDistrictDayGmvMapper cityDistrictDayGmvMapper;
    @Resource
    private CrmBdOrgMapper orgMapper;
    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private CrmSalesCityMapper salesCityMapper;
    @Resource
    private CrmBdCityMapper bdCityMapper;
    @Resource
    private CrmSalesAreaMapper salesAreaMapper;
    @Resource
    private CrmCityTodayGmvMapper todayGmvMapper;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    @Resource
    private CrmConfig crmConfig;

    @Override
    public AjaxResult selectGmvByZoneName(SalesDataQuery salesDataQuery) {
        // 处理地区筛选
        this.dealWithInput(salesDataQuery);
        // 时间本月,前端传参格式:yyyyMM
        SalesDataVo salesDataVo;
        // 本月
        if (salesDataQuery.getThisMonth()) {
            // 数据更新时间
            DataSynchronizationInformation thisMerchantDay = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
            Integer dataFlag = Objects.isNull(thisMerchantDay) ? NumberUtils.INTEGER_ZERO : thisMerchantDay.getDateFlag();
            DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_MONTH_GMV.getTableName());
            Integer lastDataFlag = Objects.isNull(lastMonth) ? NumberUtils.INTEGER_ZERO : lastMonth.getDateFlag();
            salesDataVo = this.thisBdTeamQuery(dataFlag, lastDataFlag, salesDataQuery);
        } else {
            // 上月,上上月筛选
            salesDataVo = this.lastBdTeamQuery(salesDataQuery);
        }

        // 公私海客户数,拉新数
        SalesDataVo merchantNum = followUpRelationMapper.selectMerchantNum(salesDataQuery);
        Integer newAdminOrderNum = salesDataMapper.selectNewAdmin(salesDataQuery);
        salesDataVo.setIntroducingNewReward(newAdminOrderNum);
        salesDataVo.setPrivateMerchant(merchantNum.getPrivateMerchant());
        salesDataVo.setOpenMerchant(merchantNum.getOpenMerchant());
        // 公私海本月未下单客户数
        salesDataQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        if (salesDataQuery.getType() != null && salesDataQuery.getType() == 1) {
            List<Integer> integers = salesDataMapper.selectAdminIds();
            integers.removeAll(salesDataMapper.selectAdminIdsByTypeLock(2, 1));
            salesDataQuery.setAdminIds(integers);
        }
        SalesDataVo dataVo = salesDataMapper.selectBdMerchantOrderNum(salesDataQuery);
        salesDataVo.setPrivateMerchantNoOrderNum(merchantNum.getPrivateMerchant() - dataVo.getPrivateMerchantNoOrderNum());
        salesDataVo.setOpenMerchantNoOrderNum(merchantNum.getOpenMerchant() - dataVo.getOpenMerchantNoOrderNum());
        // 拜访数,陪访数
        SalesDataVo visit = salesDataMapper.selectVisitByArea(salesDataQuery);
        salesDataVo.setVisitNum(visit.getVisitNum());
        salesDataVo.setEscortNum(visit.getEscortNum());
        salesDataVo.setDropInVisitNum(visit.getDropInVisitNum());
        salesDataVo.setEfficientNum(visit.getEfficientNum());
        // 公海倒闭客户数
        int operateMerchantNum = followUpRelationMapper.selectOperateMerchantInOpenSea(salesDataQuery);
        salesDataVo.setOperateMerchantNum(operateMerchantNum);
        return AjaxResult.getOK(salesDataVo);
    }

    private void excludeBigMerchantMonthLiving(SalesDataQuery salesDataQuery) {
        Config cbdAdminIds = configMapper.selectOne(ConfigValueEnum.CBD_ADMIN_ID.getKey());
        Config shuYiAdminIds = configMapper.selectOne(ConfigValueEnum.SHU_YI_ADMIN_ID.getKey());
        List<Integer>  adminIdList = this.analysisStringToNum(cbdAdminIds.getValue());
        List<Integer> shuYiAdminIdList = this.analysisStringToNum(shuYiAdminIds.getValue());
        adminIdList.addAll(shuYiAdminIdList);
        salesDataQuery.setBigMerchantAdminIds(adminIdList);
    }

    private List<Integer> analysisStringToNum(String value) {
        List<Integer> ids = new ArrayList<>(20);
        String[] split = value.split(CrmGlobalConstant.SEPARATING_SYMBOL);
        for (String numStr : split) {
            Integer integer = Integer.valueOf(numStr);
            ids.add(integer);
        }
        return ids;
    }


    private SalesDataVo thisBdTeamQuery(Integer merchantDay, Integer merchantMonth, SalesDataQuery salesDataQuery) {
        // 上月gmv,上月核心客户数
        salesDataQuery.setQueryTime(merchantMonth);
        SalesDataVo lastMonthSalesData = crmMerchantMonthGmvMapper.selectByInput(salesDataQuery);
        if (Objects.isNull(lastMonthSalesData)) {
            lastMonthSalesData = new SalesDataVo();
        }
        // 商户总gmv,总核心客户数,自营品牌gmv
        salesDataQuery.setQueryTime(merchantDay);
        SalesDataVo target = crmMerchantDayGmvMapper.selectByInput(salesDataQuery);
        // 茶百道及书亦id
        this.excludeBigMerchantMonthLiving(salesDataQuery);
        // 商户总月活,私海月活,公海月活
        SalesDataVo monthNum = salesDataMapper.selectMonthLiving(salesDataQuery);
        target.setMonthLiving(monthNum.getMonthLiving());
        target.setPrivateMonthLiving(monthNum.getPrivateMonthLiving());
        target.setOpenMonthLiving(monthNum.getOpenMonthLiving());
        // 若有销售团队筛选
        if (Objects.nonNull(salesDataQuery.getType())) {
            // 大客户团队的m_id:本月gmv,总月活,,核心客户数    拉新数,私海月活,公海月活
            List<Integer> mIds = followUpRelationMapper.selectBdType();
            SalesDataVo vipMidGmv = this.bulkInsert(mIds, salesDataQuery);
            // 大客户团队:上月gmv,上月总月活,上月总核心客户数
            SalesDataVo lastVipTeamData = this.lastBulkInsert(mIds,salesDataQuery.getAreaNo(), merchantMonth);
            // 筛选条件:大客户团队
            if (Objects.equals(CrmBdTeamEnum.VIP_BD.ordinal(), salesDataQuery.getType())) {
                this.fillVipTeamSaleData(target, vipMidGmv, lastVipTeamData);
            } else {
                this.fillOtherTeamSaleData(target, vipMidGmv, lastVipTeamData, lastMonthSalesData);
            }
        } else {
            // 上月gmv,上月核心客户数
            target.setLastMonthGmv(lastMonthSalesData.getCurrentMonthGmv());
            target.setLastMonthCoreMerchantNum(lastMonthSalesData.getCoreMerchantNum());
        }
        return target;
    }

    private SalesDataVo lastBdTeamQuery(SalesDataQuery salesDataQuery) {
        // 商户:筛选月gmv,核心客户数
        SalesDataVo salesDataVo = crmMerchantMonthGmvMapper.selectByInput(salesDataQuery);
        // 茶百道及书亦id
        this.excludeBigMerchantMonthLiving(salesDataQuery);
        // 商户总月活,私海月活,公海月活
        SalesDataVo monthNum = salesDataMapper.selectMonthLiving(salesDataQuery);
        salesDataVo.setMonthLiving(monthNum.getMonthLiving());
        salesDataVo.setPrivateMonthLiving(monthNum.getPrivateMonthLiving());
        salesDataVo.setOpenMonthLiving(monthNum.getOpenMonthLiving());
        // 若有销售团队筛选
        if (Objects.nonNull(salesDataQuery.getType())) {
            // 大客户团队的m_id:本月gmv,总月活,,核心客户数    拉新数,私海月活,公海月活
            List<Integer> mIds = followUpRelationMapper.selectBdType();
            SalesDataVo vipTeamData = this.bulkInsert(mIds, salesDataQuery);
            // 筛选条件:大客户
            if (Objects.equals(CrmBdTeamEnum.VIP_BD.ordinal(), salesDataQuery.getType())) {
                // 大客户团队:本月gmv,拉新数,总月活,私海月活,公海月活,核心客户数
                this.fillVipTeamSaleData(salesDataVo, vipTeamData, null);
            } else {
                // 其他团队:本月gmv,拉新数,总月活,私海月活,公海月活,核心客户数
                this.fillOtherTeamSaleData(salesDataVo, vipTeamData, null, null);
            }
        }
        return salesDataVo;
    }

    private SalesDataVo bulkInsert(List<Integer> mIds, SalesDataQuery salesDataQuery) {
        // 批量搜索,每次最多一千条
        int size = mIds.size() / 900;
        SalesDataVo vipTeamData = new SalesDataVo();
        for (int i = 0; i <= size; i++) {
            // 依次跳过前900 * i行
            int skip = 900 * i;
            List<Integer> list = mIds.stream().skip(skip).limit(900).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            SalesDataVo dataVo;
            if(salesDataQuery.getThisMonth()){
                dataVo = crmMerchantDayGmvMapper.selectByMids(list, salesDataQuery.getAreaNo(), salesDataQuery.getQueryTime());
            }else {
                dataVo = crmMerchantMonthGmvMapper.selectByMids(list, salesDataQuery.getAreaNo(), salesDataQuery.getQueryTime());
            }
            if (Objects.nonNull(dataVo)) {
                fillDataVo(vipTeamData, dataVo);
            }
        }
        return vipTeamData;
    }

    private SalesDataVo lastBulkInsert(List<Integer> mIds, List<Integer> areaNo,Integer monthTag) {
        // 批量搜索,每次最多一千条
        int size = mIds.size() / 900;
        SalesDataVo vipTeamData = new SalesDataVo();
        for (int i = 0; i <= size; i++) {
            // 依次跳过前900 * i行
            int skip = 900 * i;
            List<Integer> list = mIds.stream().skip(skip).limit(900).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            SalesDataVo dataVo = crmMerchantMonthGmvMapper.selectByMids(list, areaNo, monthTag);
            if (Objects.nonNull(dataVo)) {
                fillDataVo(vipTeamData, dataVo);
            }
        }
        return vipTeamData;
    }

    /**
     * 填充数据
     * @param vipTeamData 大客户团队目标值数据
     * @param dataVo 源数据
     */
    private void fillDataVo(SalesDataVo vipTeamData, SalesDataVo dataVo) {
        BigDecimal currentMonthGmv = vipTeamData.getCurrentMonthGmv().add(dataVo.getCurrentMonthGmv());
        int coreMerchantNum = vipTeamData.getCoreMerchantNum() + dataVo.getCoreMerchantNum();
        int monthNum = vipTeamData.getMonthLiving() + dataVo.getMonthLiving();
        BigDecimal brandGmv = vipTeamData.getBrandGmv().add(dataVo.getBrandGmv());
        vipTeamData.setMonthLiving(monthNum);
        vipTeamData.setCurrentMonthGmv(currentMonthGmv);
        vipTeamData.setCoreMerchantNum(coreMerchantNum);
        vipTeamData.setBrandGmv(brandGmv);
    }

    /**
     * 填充销售数据:本月gmv,总月活,核心客户数,上月gmv,上月核心客户数
     *
     * @param target          目标
     * @param vipMidGmv       大客户团队
     * @param lastVipTeamData 大客户团队上月数据
     */
    private void fillVipTeamSaleData(SalesDataVo target, SalesDataVo vipMidGmv, SalesDataVo lastVipTeamData) {
        target.setCurrentMonthGmv(vipMidGmv.getCurrentMonthGmv());
        target.setPrivateMonthLiving(vipMidGmv.getMonthLiving());
        target.setCoreMerchantNum(vipMidGmv.getCoreMerchantNum());
        target.setMonthLiving(vipMidGmv.getMonthLiving());
        target.setBrandGmv(vipMidGmv.getBrandGmv());
        if (Objects.nonNull(lastVipTeamData)) {
            target.setLastMonthGmv(lastVipTeamData.getCurrentMonthGmv());
            target.setLastMonthCoreMerchantNum(lastVipTeamData.getCoreMerchantNum());
        }
    }

    /**
     * 填充销售数据:本月gmv,拉新数,总月活,私海月活,公海月活,核心客户数,上月gmv,上月核心客户数
     *
     * @param target             目标
     * @param vipTeamData        大客户团队数据
     * @param lastVipTeamData    上月大客户团队数据
     * @param lastMonthSalesData 上月总商户数据
     */
    private void fillOtherTeamSaleData(SalesDataVo target, SalesDataVo vipTeamData, SalesDataVo lastVipTeamData, SalesDataVo lastMonthSalesData){
        target.setCurrentMonthGmv(target.getCurrentMonthGmv().subtract(vipTeamData.getCurrentMonthGmv()));
        target.setBrandGmv(target.getBrandGmv().subtract(vipTeamData.getBrandGmv()));
        target.setCoreMerchantNum(target.getCoreMerchantNum() - vipTeamData.getCoreMerchantNum());

        if (Objects.nonNull(lastVipTeamData) && Objects.nonNull(lastMonthSalesData)) {
            target.setLastMonthGmv(lastMonthSalesData.getCurrentMonthGmv().subtract(lastVipTeamData.getCurrentMonthGmv()));
            target.setLastMonthCoreMerchantNum(lastMonthSalesData.getCoreMerchantNum() - lastVipTeamData.getCoreMerchantNum());
        }
    }

    /**
     * 处理页面查询条件:可见地区,销售团队
     *
     * @param salesDataQuery 页面查询条件
     */
    private void dealWithInput(SalesDataQuery salesDataQuery) {
        // 入参区域为空时，默认取当前用户管理区域,超管可见所有
        Integer adminId = super.getAdminId();
        if (super.isSA()) {
            adminId = null;
        }
        if (Objects.isNull(salesDataQuery) || CollectionUtils.isEmpty(salesDataQuery.getAreaNo())) {
            // 页面加载时已验证用户管理区域做,若为空不会执行该接口
            List<Integer> areaNoByAdmin = crmManageBdMapper.getAreaNoByAdmin(adminId);
            salesDataQuery.setAreaNo(areaNoByAdmin);
        }
        if (Objects.isNull(salesDataQuery.getThisMonth())) {
            salesDataQuery.setThisMonth(true);
        }
        LocalDateTime start = DateUtils.getAtBeginningOfMonth();
        LocalDateTime end = BaseDateUtils.getDayEnd(LocalDateTime.now());
        if (salesDataQuery.getThisMonth()) {
            salesDataQuery.setStartTime(start);
            salesDataQuery.setEndTime(end);
        } else {
            // 页面传输月份
            int time = salesDataQuery.getQueryTime() * 100 + 1;
            LocalDate parse = LocalDate.parse(Integer.toString(time), DateTimeFormatter.ofPattern(BaseDateUtils.MID_DATE_FORMAT));
            LocalDate monthEnd = parse.plusMonths(DateUtils.ONE_DATE);
            salesDataQuery.setStartTime(LocalDateTime.of(parse, LocalTime.MIN));
            salesDataQuery.setEndTime(LocalDateTime.of(monthEnd, LocalTime.MIN));
        }
    }

    @Override
    public AjaxResult selectBdDataByZoneName(int pageIndex, int pageSize, SalesDataQuery salesDataQuery) {
        // 一个月内禁用的销售展示其信息
        salesDataQuery.setUpdateTime(LocalDateTime.now().minusMonths(DateUtils.ONE_DATE));
        salesDataQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        salesDataQuery.setEndTime(DateUtils.getDayEnd(LocalDateTime.now()));

        PageHelper.startPage(pageIndex, pageSize);
        //取区域下所有bd
        List<AdminInfoVo> adminInfos = crmBdAreaMapper.selectAdminByAreas(salesDataQuery);

        this.dealWithBDData(salesDataQuery,adminInfos);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(adminInfos));
    }

    private void dealWithBDData(SalesDataQuery salesDataQuery,List<AdminInfoVo> adminInfos ){
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_DAY_GMV.getTableName());
        Integer dateFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();

        for (AdminInfoVo adminInfo : adminInfos) {
            // 取bd的激励奖励
            CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminInfo.getAdminId());
            if (Objects.isNull(crmBdConfig)) {
                crmBdConfig = new CrmBdConfig();
                crmBdConfig.setPrivateSeaLimit(0);
                crmBdConfig.setQuotaLimit(0);
            }

            // 下单新客户数
            salesDataQuery.setAdminId(adminInfo.getAdminId());
            Integer newAdminOrderNum = salesDataMapper.selectNewAdminOrderNum(salesDataQuery);
            // 私海
            int privateSea = EsUtil.countPrivateSea(adminInfo.getAdminId(), salesDataQuery.getAreaNo(),0);
            // 去除白名单客户
            int whiteNum = followWhiteListService.selectNumByBd(adminInfo.getAdminId(), salesDataQuery.getAreaNo());
            privateSea = privateSea - whiteNum;
            // 客情
            BigDecimal amount = merchantSituationQuotaMapper.selectAmount(adminInfo.getAdminId());
            amount = Objects.isNull(amount) ? BigDecimal.ZERO : amount;
            // 拜访总数
            Integer visitTotal = salesDataMapper.selectVisitNum(salesDataQuery);
            adminInfo.setVisitTotal(visitTotal);
            // 填充信息
            adminInfo.setNewAdminOrderNum(newAdminOrderNum);
            adminInfo.setPrivateSeaProportion(privateSea + "/" + crmBdConfig.getPrivateSeaLimit());
            adminInfo.setGuestSentimentProportion(amount.stripTrailingZeros().toPlainString() + "/" + crmBdConfig.getQuotaLimit());
            // 拜访数(去重),普通拜访数,普通上门拜访数,有效拜访数,价值拜访数
            AdminInfoVo infoVo = crmBdDayGmvMapper.selectByAdminId(adminInfo.getAdminId(), dateFlag);
            if (Objects.isNull(infoVo)) {
                logger.info("{}尚未同步gmv信息:", adminInfo.getAdminName());
                continue;
            }
            this.fillVisitNum(adminInfo,infoVo);
            adminInfo.setNewAdminNum(infoVo.getNewAdminNum());
        }

    }

    @Override
    public CommonResult<PageInfo<AdminInfoVo>> selectBdDataByCity(SalesDataQuery salesDataQuery) {
        // 一个月内禁用的销售展示其信息
        salesDataQuery.setUpdateTime(LocalDateTime.now().minusMonths(DateUtils.ONE_DATE));
        salesDataQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        salesDataQuery.setEndTime(DateUtils.getDayEnd(LocalDateTime.now()));

        PageHelper.startPage(salesDataQuery.getPageIndex(), salesDataQuery.getPageSize());
        //取区域下所有bd
        List<AdminInfoVo> adminInfos = getChildrenBd(salesDataQuery.getBdOrgId());
        this.dealWithBDData(salesDataQuery,adminInfos);
        return CommonResult.ok(PageInfoHelper.createPageInfo(adminInfos));
    }

    @Override
    public CommonResult<AdminInfoVo> selectBdData(Integer adminId) {
        // 获取销售激励配置
        adminId = Optional.ofNullable(adminId).orElse(super.getAdminId());

        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminId);
        if (Objects.isNull(crmBdConfig)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST,"激励指标暂未配置,请联系您的主管配置激励指标后再来查看");
        }

        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_DAY_GMV.getTableName());
        Integer dateFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        AdminInfoVo adminInfoVo = crmBdDayGmvMapper.selectByAdminId(adminId, dateFlag);
        if (Objects.isNull(adminInfoVo)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST,"个人数据信息暂未同步,请检查您的销售身份,并于明日再来查看");
        }
        adminInfoVo.setCrmBdConfig(crmBdConfig);
        adminInfoVo.setMonthOrderTarget(crmBdConfig.getMonthOrderTarget());
        // 绩效str
        adminInfoVo.setPerformanceStr(adminInfoVo.getPerformance().toPlainString());
        adminInfoVo.setCategoryMultiplyGmvStr(adminInfoVo.getCategoryMultiplyGmv().toPlainString());
        this.calculationCompletion(adminInfoVo);

        // 上月gmv信息
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_MONTH_GMV.getTableName());
        Integer lastDateFlag = Objects.isNull(lastMonth) ? NumberUtils.INTEGER_ZERO : lastMonth.getDateFlag();
        AdminInfoVo lastMonthGmv = crmBdMonthGmvMapper.selectByAdminId(adminId, lastDateFlag);
        if (Objects.nonNull(lastMonthGmv)) {
            this.calculationScale(adminInfoVo,lastMonthGmv);
        }

        // 单店客户数,品牌客户数,大客户门店数
        LocalDateTime startTime = DateUtils.getAtBeginningOfMonth();
        AdminInfoVo merchantInfo = salesDataMapper.selectMerchantNum(adminId);
        adminInfoVo.setSingleShopNum(merchantInfo.getSingleShopNum());
        adminInfoVo.setVipNum(merchantInfo.getVipNum());
        adminInfoVo.setBrandNum(merchantInfo.getBrandNum());

        // 品牌下单门店数,,单店下单客户数
        AdminInfoVo orderInfo = salesDataMapper.selectOrderNum(adminId, startTime);
        adminInfoVo.setSingleShopNotOrderNum(merchantInfo.getSingleShopNum() - orderInfo.getSingleShopOrderNum());
        adminInfoVo.setVipNotOrderNum(merchantInfo.getVipNum() - orderInfo.getVipOrderNum());

        // 拜访总数
        SalesDataQuery salesDataQuery = new SalesDataQuery();
        salesDataQuery.setAdminId(super.getAdminId());
        salesDataQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        salesDataQuery.setEndTime(BaseDateUtils.getDayEnd(LocalDateTime.now()));
        Integer visitTotal = salesDataMapper.selectVisitNum(salesDataQuery);
        adminInfoVo.setVisitTotal(visitTotal);

        // 上月核心客户数(取基础值)
        Integer lastDateCoreMerchantNum = crmBdConfig.getCoreMerchantAmount();
        adminInfoVo.setLastCoreMerchantNum(lastDateCoreMerchantNum);


        // 本月核心客户净增长数
        int coreMerchantGrowNum = adminInfoVo.getCoreMerchantNum() - lastDateCoreMerchantNum;
        int maxCoreMerchantGrowNum = Math.max(coreMerchantGrowNum, NumberUtils.INTEGER_ZERO);
        adminInfoVo.setCoreMerchantGrowNum(maxCoreMerchantGrowNum);

        // 奖励牌级
        List<CrmCommissionCoreMerchant> crmCommissionCoreMerchants = crmCommissionCoreMerchantMapper.selectCoreMerchantsNetGrowth();
        // 按照最大值排序
        List<Integer> maxNumList = crmCommissionCoreMerchants.stream().map(CrmCommissionCoreMerchant::getMaximum).sorted().collect(Collectors.toList());
        for (int i = 0; i < maxNumList.size(); i++) {
            // 最后一次为最高级别,直接跳过
            if(i == maxNumList.size() - 1){
                break;
            }
            Integer maxNum = maxNumList.get(i);
            // 获取牌级差值
            if(maxNum.compareTo(maxCoreMerchantGrowNum) > 0){
                adminInfoVo.setGradeDifference(maxNum - maxCoreMerchantGrowNum);
                break;
            }
        }

        return CommonResult.ok(adminInfoVo);
    }

    private void calculationCompletion(AdminInfoVo adminInfoVo) {
        CrmBdConfig crmBdConfig = adminInfoVo.getCrmBdConfig();
        // 月活完成度占比
        BigDecimal monthOrderTarget = Objects.isNull(crmBdConfig.getMonthOrderTarget()) ? BigDecimal.ONE : BigDecimal.valueOf(crmBdConfig.getMonthOrderTarget());
        monthOrderTarget = BigDecimal.ZERO.compareTo(monthOrderTarget) >= 0 ? BigDecimal.ONE : monthOrderTarget;
        adminInfoVo.setMonthlyActivityCompletion(BigDecimal.valueOf(adminInfoVo.getMonthLiving()).divide(monthOrderTarget,2,RoundingMode.HALF_UP));

        // gmv占比
        BigDecimal gmvTarget = Objects.isNull(crmBdConfig.getGmvTarget()) ? BigDecimal.ONE : crmBdConfig.getGmvTarget();
        gmvTarget = BigDecimal.ZERO.compareTo(gmvTarget) >= 0 ? BigDecimal.ONE : gmvTarget;
        adminInfoVo.setGmvCompletion(adminInfoVo.getTotalGmv().divide(gmvTarget,2,RoundingMode.HALF_UP));

        // 自营gmv占比
        BigDecimal brandGmvTarget = Objects.isNull(crmBdConfig.getBrandGmvTarget()) ? BigDecimal.ONE : crmBdConfig.getBrandGmvTarget();
        brandGmvTarget = BigDecimal.ZERO.compareTo(brandGmvTarget) >= 0 ? BigDecimal.ONE : brandGmvTarget;
        adminInfoVo.setBrandGmvCompletion(adminInfoVo.getBrandGmv().divide(brandGmvTarget,2,RoundingMode.HALF_UP));
    }

    /**
     * 计算单店,大客户gmv信息环比
     * @param adminInfoVo 当月gmv信息
     * @param lastMonthGmv 上月gmv信息
     */
    private void calculationScale(AdminInfoVo adminInfoVo, AdminInfoVo lastMonthGmv) {
        adminInfoVo.setLastBrandGmv(lastMonthGmv.getBrandGmv());
        adminInfoVo.setLastTotalGmv(lastMonthGmv.getTotalGmv());

        // 单店gmv环比
        BigDecimal singleGmvSub = adminInfoVo.getSingleShopGmv().subtract(lastMonthGmv.getSingleShopGmv());
        BigDecimal singleGmvRingRatio = BigDecimal.ONE;
        if(BigDecimal.ZERO.compareTo(lastMonthGmv.getSingleShopGmv()) < 0){
            singleGmvRingRatio = singleGmvSub.divide(lastMonthGmv.getSingleShopGmv(), 2, RoundingMode.HALF_UP);
        }
        adminInfoVo.setSingleGmvRingRatio(singleGmvRingRatio);

        // 单店月活环比
        BigDecimal singleMonthSub = BigDecimal.valueOf(adminInfoVo.getSingleMonthLiveNum()).subtract(BigDecimal.valueOf(lastMonthGmv.getSingleMonthLiveNum()));
        BigDecimal singleMonthRingRatio = BigDecimal.ONE;
        if(BigDecimal.ZERO.compareTo(BigDecimal.valueOf(lastMonthGmv.getSingleMonthLiveNum())) < 0){
            singleMonthRingRatio = singleMonthSub.divide(BigDecimal.valueOf(lastMonthGmv.getSingleMonthLiveNum()), 2, RoundingMode.HALF_UP);
        }
        adminInfoVo.setSingleMonthRingRatio(singleMonthRingRatio);

        // 单店spu均值
        BigDecimal singleSpuSub = adminInfoVo.getSingleSpuAverage().subtract(lastMonthGmv.getSingleSpuAverage());
        BigDecimal singleSpuRingRatio = BigDecimal.ONE;
        if(BigDecimal.ZERO.compareTo(lastMonthGmv.getSingleSpuAverage()) < 0){
            singleSpuRingRatio = singleSpuSub.divide(lastMonthGmv.getSingleSpuAverage(), 2, RoundingMode.HALF_UP);
        }
        adminInfoVo.setSingleSpuRingRatio(singleSpuRingRatio);

        // 大客户gmv环比
        BigDecimal vipGmvSub = adminInfoVo.getVipGmv().subtract(lastMonthGmv.getVipGmv());
        BigDecimal vipGmvRingRatio = BigDecimal.ONE;
        if(BigDecimal.ZERO.compareTo(lastMonthGmv.getVipGmv()) < 0){
            vipGmvRingRatio = vipGmvSub.divide(lastMonthGmv.getVipGmv(), 2, RoundingMode.HALF_UP);
        }
        adminInfoVo.setVipGmvRingRatio(vipGmvRingRatio);

        // 大客户月活环比
        BigDecimal vipMonthSub = BigDecimal.valueOf(adminInfoVo.getVipMonthLiveNum()).subtract(BigDecimal.valueOf(lastMonthGmv.getVipMonthLiveNum()));
        BigDecimal vipMonthRingRatio = BigDecimal.ONE;
        if(lastMonthGmv.getVipMonthLiveNum() >0){
            vipMonthRingRatio = vipMonthSub.divide(BigDecimal.valueOf(lastMonthGmv.getVipMonthLiveNum()), 2, RoundingMode.HALF_UP);
        }
        adminInfoVo.setVipMonthRingRatio(vipMonthRingRatio);

        // 大客户spu均值环比
        BigDecimal vipSpuSub = adminInfoVo.getVipSpuAverage().subtract(lastMonthGmv.getVipSpuAverage());
        BigDecimal vipSpuRingRatio = BigDecimal.ONE;
        if(BigDecimal.ZERO.compareTo(lastMonthGmv.getVipSpuAverage()) < 0){
            vipSpuRingRatio = vipSpuSub.divide(lastMonthGmv.getVipSpuAverage(), 2, RoundingMode.HALF_UP);
        }
        adminInfoVo.setVipSpuRingRatio(vipSpuRingRatio);
    }

    @Override
    public CommonResult<CrmBdTodayDayGmv> todayGmv() {
        // 主管城市业绩
        if (isAreaSA() || isSaleSA()) {
            CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                return CommonResult.ok();
            }
            List<Integer> bdOrgList = new ArrayList<>();
            // m2/m3获取下属 m1
            if (topRankOrg.getRank() < CITY_MANAGER) {
                List<CrmBdOrg> orgList = new ArrayList<>();
                bdAreaConfigService.listChildrenByParentId(topRankOrg.getId(), orgList);
                bdOrgList = orgList.stream().filter(o -> ObjectUtil.equal(o.getRank(), CITY_MANAGER)).map(CrmBdOrg::getId).collect(Collectors.toList());
            } else if (topRankOrg.getRank() == CITY_MANAGER) {
                bdOrgList = Collections.singletonList(topRankOrg.getId());
            }
            if (bdOrgList.isEmpty()) {
                return CommonResult.ok();
            }
            List<CrmSalesCity> salesCity = salesCityMapper.listByBdOrgId(bdOrgList);
            salesCity.forEach(s -> {
                if (!crmConfig.getSplitCityList().contains(s.getCity())) {
                    s.setArea("无");
                }
            });
            if (salesCity.isEmpty()) {
                return CommonResult.ok();
            }
            DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(CRM_CITY_TODAY_GMV.getTableName());
            Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
            SalesAreaDataVo salesAreaDataVo = todayGmvMapper.selectByCities(salesCity.stream().map(BdSalesCityVo::beanToVo).collect(Collectors.toList()), dateTag);
            return CommonResult.ok(SalesAreaDataVo.todayDayGmv(salesAreaDataVo));
        }

        return CommonResult.ok(this.todayGmv(super.getAdminId()));
    }

    @Override
    public CommonResult<List<RankingListVO>> rankingList(Integer type) {
        List<RankingListVO> rankingListVOS = new ArrayList<>();
        if (type == 0) {
            DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_TODAY_DAY_GMV.getTableName());
            Integer dayTag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
            rankingListVOS = crmBdtodayGmvMapper.selectIncomeRankingListVO(dayTag);
        } else {
            DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_DAY_GMV.getTableName());
            Integer dayTag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
            rankingListVOS = crmBdDayGmvMapper.selectRankingListVO(type,dayTag);
        }
        rankingListVOS.forEach(this::setRankingListAreaName);
        return CommonResult.ok(rankingListVOS);
    }

    @Override
    public CommonResult<AdministrativeCityVo> defaultAdministrativeCity() {
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(getAdminId());
        AdministrativeCityVo city=new AdministrativeCityVo();
        AdministrativeCityLabelVo labelVo = new AdministrativeCityLabelVo();
        if (crmBdConfig==null){
            labelVo.setLabel("杭州市");
            labelVo.setValue("杭州市");
        }else {
            labelVo.setLabel(crmBdConfig.getAdministrativeCity());
            labelVo.setValue(crmBdConfig.getAdministrativeCity());
        }
        city.setAdministrativeCityList(Arrays.asList(labelVo));
        return CommonResult.ok(city);
    }

    @Override
    public CommonResult<CityDistrictGmvVo> selectGmvByCityDistrict(SalesDataQuery salesDataQuery) {
        DataSynchronizationInformation flag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_CITY_DISTRICT_DAY_GMV.getTableName());
        if (flag == null) {
            return CommonResult.ok();
        }
        CityDistrictGmvVo gmvVo = new CityDistrictGmvVo();
        List<CrmCityDistrictDayGmv> gmvList = cityDistrictDayGmvMapper.selectByCityAndDistrict(salesDataQuery.getCity(), salesDataQuery.getDistrict(), flag.getDateFlag());
        gmvVo.setGmvList(gmvList);
        CrmCityDistrictDayGmv gmv = cityDistrictDayGmvMapper.selectByCityAndDistrictSum(salesDataQuery.getCity(), salesDataQuery.getDistrict(), flag.getDateFlag());
        gmvVo.setTotalGmv(gmv);
        return CommonResult.ok(gmvVo);
    }

    @Override
    public List<CrmBdTodayDayGmv> bdTodayGmv(Integer salesAreaId) {
        List<Integer> bdIdList = getBdBySalesArea(salesAreaId);

        if (CollectionUtil.isEmpty(bdIdList)) {
            return new ArrayList<>();
        }
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_TODAY_DAY_GMV.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        return crmBdtodayGmvMapper.listByAdminId(dateTag, bdIdList);
    }

    @Override
    public List<CrmCityTodayGmv> cityTodayGmv(Integer salesAreaId) {
        List<BdSalesCityVo> salesCity = getSalesCity(salesAreaId);

        if (CollectionUtil.isEmpty(salesCity)) {
            return new ArrayList<>();
        }
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(CRM_CITY_TODAY_GMV.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        return todayGmvMapper.listByCity(salesCity,dateTag);
    }

    @Override
    public List<SalesAreaDataVo> salesAreaGmv(Integer type) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        List<SalesAreaDataVo> list = new ArrayList<>();
        if (topRankOrg == null || topRankOrg.getRank() == BD) {
            return list;
        }
        List<CrmBdOrg> orgList = orgMapper.listChildByParentId(topRankOrg.getId());
        orgList.add(topRankOrg);
        List<SalesAreaVo> salesAreaList = salesAreaMapper.selectByBdOrgIdList(orgList.stream().map(CrmBdOrg::getId).collect(Collectors.toList()));
        // 当日 or 当月业绩
        boolean isDay = ObjectUtil.equals(0, type);
        DataSynchronizationInformationEnum dataInfo = isDay ? CRM_CITY_TODAY_GMV : DataSynchronizationInformationEnum.CRM_CITY_DAY_GMV;
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(dataInfo.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        // 统计销售区域数据
        for (SalesAreaVo salesArea : salesAreaList) {
            List<BdSalesCityVo> salesCity = salesArea.getSalesCityList();
            if (salesCity.isEmpty()) {
                continue;
            }
            salesCity.forEach(s -> {
                if (!crmConfig.getSplitCityList().contains(s.getCity())) {
                    s.setArea("无");
                }
            });

            SalesAreaDataVo salesAreaData = null;
            // 当日 gmv 信息
            if (isDay) {
                salesAreaData = todayGmvMapper.selectByCities(salesCity, dateTag);
            } else {
                // 当月 gmv 信息
                salesAreaData = crmCityDayGmvMapper.selectByCityList(salesCity, dateTag);
            }
            salesAreaData.setSalesAreaId(salesArea.getSalesAreaId());
            salesAreaData.setSalesAreaName(salesArea.getSalesAreaName());
            list.add(salesAreaData);
        }
        return list;
    }

    @Override
    public List<CrmBdTodayDayGmv> bdMonthGmv(Integer salesAreaId) {
        List<Integer> bdIdList = getBdBySalesArea(salesAreaId);

        if (CollectionUtil.isEmpty(bdIdList)) {
            return new ArrayList<>();
        }
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_DAY_GMV.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        return crmBdDayGmvMapper.listByAdminId(bdIdList, dateTag);
    }

    @Override
    public List<SalesAreaDataVo> cityMonthGmv(Integer salesAreaId) {
        List<BdSalesCityVo> salesCity = getSalesCity(salesAreaId);

        if (CollectionUtil.isEmpty(salesCity)) {
            return new ArrayList<>();
        }
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_CITY_DAY_GMV.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        return crmCityDayGmvMapper.listByCities(salesCity, dateTag);
    }

    @Override
    public List<NewCustomerVo> newCustomer(NewCustomerQuery query) {
        if (query.getBdId() == null) {
            query.setBdId(getAdminId());
        }
        List<Long> mIds = newCustomersMonthMapper.listByBdId(query.getBdId());
        if (mIds.isEmpty()){
            return new ArrayList<>();
        }
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIds);

        return merchantExtendList.stream().map(NewCustomerVo::toVo).collect(Collectors.toList());
    }

    public List<Integer> getBdBySalesArea(Integer salesAreaId) {
        List<CrmBdOrg> crmBdOrgList;
        if (salesAreaId != null) {
            CrmSalesArea crmSalesArea = salesAreaMapper.selectByPrimaryKey(salesAreaId);
            if (crmSalesArea == null) {
                throw new BizException("销售区域不存在，请刷新");
            }
            crmBdOrgList = bdAreaConfigService.listChildrenBd(crmSalesArea.getBdOrgId());
        } else {
            crmBdOrgList = bdAreaConfigService.listChildrenBd();
        }
        return crmBdOrgList.stream().map(CrmBdOrg::getBdId).distinct().collect(Collectors.toList());
    }

    public List<BdSalesCityVo> getSalesCity(Integer salesAreaId) {
        List<BdSalesCityVo> salesCity;

        if (salesAreaId == null) {
            // 没有指定销售区域默认 m1 获取自己负责的区域配置负责的城市
            CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                return new ArrayList<>();
            }
            List<CrmSalesCity> crmSalesCities = salesCityMapper.listByBdOrgId(Collections.singletonList(topRankOrg.getId()));
            salesCity = crmSalesCities.stream().map(BdSalesCityVo::beanToVo).collect(Collectors.toList());
        } else {
            // 指定销售区域获取负责城市
            salesCity = salesCityMapper.listBySalesAreaId(salesAreaId);
        }
        // 非上海不显示区
         salesCity.forEach(s -> {
             if (!crmConfig.getSplitCityList().contains(s.getCity())) {
                s.setArea("无");
            }
        });
        return salesCity;
    }

    /**
     * 根据bdId设置排行榜信息
     *
     * @param rankingList bdId
     */
    private void setRankingListAreaName(RankingListVO rankingList) {
        if (StringUtils.isNotBlank(rankingList.getBdName())) {
            rankingList.setBdName(StrUtil.sub(rankingList.getBdName(), 0, 1) + "**");
        }
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(rankingList.getBdId());
        if (crmBdConfig != null) {
            rankingList.setAreaName(crmBdConfig.getAdministrativeCity());
        }
    }

    /**
     * 获取指定bd的当日gmv信息
     *
     * @param adminId bdId
     * @return bd的当日gmv信息
     */
    private CrmBdTodayDayGmv todayGmv(Integer adminId) {
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_TODAY_DAY_GMV.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        return Optional.ofNullable(crmBdtodayGmvMapper.selectByAdminId(dateTag, adminId)).orElse(new CrmBdTodayDayGmv());
    }

    /**
     * 发送下个月的销售数据
     */
    @Override
    public void sendMail() {
        LocalDateTime now = LocalDateTime.now();
        //取区域下所有bd
        List<AdminInfoVo> adminInfos = crmBdAreaMapper.selectAdminByAreas(null);
        DataSynchronizationInformation bdLastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_MONTH_GMV.getTableName());
        Integer bdDateFlag = Objects.isNull(bdLastMonth) ? NumberUtils.INTEGER_ZERO : bdLastMonth.getDateFlag();
        for (AdminInfoVo adminInfo : adminInfos) {
            // 获取销售数据
            AdminInfoVo infoVo = crmBdMonthGmvMapper.selectByAdminId(adminInfo.getAdminId(), bdDateFlag);
            if (Objects.isNull(infoVo)) {
                logger.info("{}尚未同步gmv信息:", adminInfo.getAdminName());
                continue;
            } else {
                BeanUtils.copyProperties(infoVo, adminInfo);
            }
            // gmv目标
            CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminInfo.getAdminId());
            BigDecimal gmvTarget = Objects.isNull(crmBdConfig) ? BigDecimal.ZERO : crmBdConfig.getGmvTarget();
            adminInfo.setGmvTarget(gmvTarget);
            // 核心客户净增长数
            AdminInfoVo lastTwoMonthGmv = crmBdMonthGmvMapper.selectByAdminId(adminInfo.getAdminId(), bdDateFlag);
            int baseNum = crmBdConfig.getCoreMerchantAmount();
            int lastNum = Objects.nonNull(lastTwoMonthGmv) ? lastTwoMonthGmv.getCoreMerchantNum() : NumberUtils.INTEGER_ZERO;
            adminInfo.setCoreMerchantGrowNum(Math.max(lastNum - baseNum, NumberUtils.INTEGER_ZERO));
        }

        if (!CollectionUtils.isEmpty(adminInfos)) {
            Map<String, Workbook> bodyPart = new HashMap<>();
            String title = DateUtils.localDateToString(now.toLocalDate(), "yyyyMM") + "销售月度业绩";
            bodyPart.put(title + ".xls", handleExecl(adminInfos));
            try {
                logger.info("发送销售数据邮件:" + adminInfos.size());
                Config bdAchievementReceive = configMapper.selectOne(ConfigValueEnum.BD_ACHIEVEMENT_RECEIVE.getKey());
                mailUtil.sendMail(title, null, bdAchievementReceive.getValue().split(CrmGlobalConstant.SEPARATING_SYMBOL), null, bodyPart);
            } catch (Exception e) {
                logger.error("销售邮件发送失败:{}",e.getMessage());
            }
        }
    }

    @Override
    public AjaxResult selectBdGmvByZoneName(int pageIndex, int pageSize, SalesDataQuery salesDataQuery) {
        if (Objects.isNull(salesDataQuery) || Objects.isNull(salesDataQuery.getQueryTime())) {
            return AjaxResult.getErrorWithMsg("参数错误,时间筛选为必选项");
        }
        this.dealWithInput(salesDataQuery);
        // 一个月内禁用的销售展示其信息
        salesDataQuery.setUpdateTime(LocalDateTime.now().minusMonths(DateUtils.ONE_DATE));
        PageHelper.startPage(pageIndex, pageSize);
        // 取区域下bd
        List<AdminInfoVo> adminInfos = crmBdAreaMapper.selectAdminByAreas(salesDataQuery);
        this.dealWithAdminInfoVo(salesDataQuery,adminInfos);

        return AjaxResult.getOK(PageInfoHelper.createPageInfo(adminInfos));
    }

    private void dealWithAdminInfoVo(SalesDataQuery salesDataQuery,List<AdminInfoVo> adminInfos){
        AdminInfoVo infoVo;
        if (salesDataQuery.getThisMonth()) {
            // 如果是本月,获取销售当日数据更新时间
            DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_DAY_GMV.getTableName());
            Integer dateFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
            // 循环拿到销售gmv信息,需循环pageSize次
            for (AdminInfoVo adminInfo : adminInfos) {
                //取bd的激励奖励,主管配置
                CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminInfo.getAdminId());
                if (Objects.isNull(crmBdConfig)) {
                    // 无配置激励指标,默认均为0
                    crmBdConfig = new CrmBdConfig();
                    crmBdConfig.setGmvTarget(BigDecimal.ZERO);
                    crmBdConfig.setGmvTargetExcludeAT(BigDecimal.ZERO);
                    crmBdConfig.setPrivateSeaLimit(0);
                    crmBdConfig.setQuotaLimit(0);
                }
                // 销售信息
                infoVo = crmBdDayGmvMapper.selectByAdminId(adminInfo.getAdminId(), dateFlag);
                if (Objects.isNull(infoVo)) {
                    logger.info("{}尚未同步gmv信息:", adminInfo.getAdminName());
                    continue;
                } else {
                    BeanUtils.copyProperties(infoVo, adminInfo);
                }
                // GMV目标(单店gmv+大客户gmv+指定商品gmv)/设定的目标值
                String gmvTarget = adminInfo.getTotalGmv().stripTrailingZeros().toPlainString() + "/" + crmBdConfig.getGmvTarget().stripTrailingZeros().toPlainString();
                adminInfo.setGmvCurrentProportion(gmvTarget);
                adminInfo.setGmvTarget(crmBdConfig.getGmvTarget());
                adminInfo.setGmvTargetExcludeAT(crmBdConfig.getGmvTargetExcludeAT());
            }
        } else {
            // 循环拿到销售gmv信息,需循环pageSize次
            for (AdminInfoVo adminInfo : adminInfos) {
                //取bd的激励奖励,主管配置
                CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminInfo.getAdminId());
                if (Objects.isNull(crmBdConfig)) {
                    crmBdConfig = new CrmBdConfig();
                    crmBdConfig.setGmvTarget(BigDecimal.ZERO);
                    crmBdConfig.setGmvTargetExcludeAT(BigDecimal.ZERO);
                    crmBdConfig.setPrivateSeaLimit(0);
                    crmBdConfig.setQuotaLimit(0);
                }
                // 销售信息
                infoVo = crmBdMonthGmvMapper.selectByAdminId(adminInfo.getAdminId(), salesDataQuery.getQueryTime());
                if (Objects.isNull(infoVo)) {
                    logger.info("{}用户尚未同步gmv信息:", adminInfo.getAdminName());
                    continue;
                } else {
                    BeanUtils.copyProperties(infoVo, adminInfo);
                }
                // GMV目标(单店gmv+大客户gmv+指定商品gmv)/设定的目标值
                String gmvTarget = adminInfo.getTotalGmv().stripTrailingZeros().toPlainString() + "/" + crmBdConfig.getGmvTarget().stripTrailingZeros().toPlainString();
                adminInfo.setGmvCurrentProportion(gmvTarget);
                adminInfo.setGmvTarget(crmBdConfig.getGmvTarget());
                adminInfo.setGmvTargetExcludeAT(crmBdConfig.getGmvTargetExcludeAT());
            }
        }
    }


    @Override
    public CommonResult<PageInfo<AdminInfoVo>> selectBdGmvByCity(SalesDataQuery salesDataQuery) {
        if (Objects.isNull(salesDataQuery) || Objects.isNull(salesDataQuery.getQueryTime())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"参数错误,时间筛选为必选项");
        }
        if (CollectionUtil.isEmpty(salesDataQuery.getBdOrgId())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"负责人不能为空");
        }
        // 分页获取指定级别的下属
        PageHelper.startPage(salesDataQuery.getPageIndex(),salesDataQuery.getPageSize());
        List<AdminInfoVo> adminInfos = getChildrenBd(salesDataQuery.getBdOrgId());

        this.dealWithAdminInfoVo(salesDataQuery,adminInfos);
        return CommonResult.ok(PageInfoHelper.createPageInfo(adminInfos));
    }

    public List<AdminInfoVo> getChildrenBd(List<Integer> bdOrgId) {
        String parentIds = bdOrgId.stream().map(String::valueOf).collect(Collectors.joining(StrPool.COMMA));
        return orgMapper.listChildByParentIdAndRank(parentIds, BD);
    }

    public List<CrmBdOrg> getParent(List<Integer> parentIdList) {
        List<CrmBdOrg> crmBdOrgList = new ArrayList<>();
        for (Integer parentId : parentIdList) {
            bdAreaConfigService.listChildrenByParentId(parentId, crmBdOrgList);
        }
        return crmBdOrgList;
    }

    private Workbook handleExecl(List<AdminInfoVo> adminInfos) {
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("销售记录");
        int rowIndex = 0;
        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setBold(Boolean.TRUE);
        sheet.setColumnWidth(0, ExcelUtils.getColumnWidth(9));
        sheet.setColumnWidth(1, ExcelUtils.getColumnWidth(12));
        sheet.setColumnWidth(2, ExcelUtils.getColumnWidth(12));
        sheet.setColumnWidth(3, ExcelUtils.getColumnWidth(12));
        sheet.setColumnWidth(4, ExcelUtils.getColumnWidth(12));
        sheet.setColumnWidth(5, ExcelUtils.getColumnWidth(16));
        sheet.setColumnWidth(6, ExcelUtils.getColumnWidth(16));
        sheet.setColumnWidth(7, ExcelUtils.getColumnWidth(16));
        Row title = sheet.createRow(rowIndex++);
        String[] titleName = {"BD姓名", "指定商品销量", "指定商品gmv", "总gmv", "gmv目标", "大客户gmv", "单店gmv", "活跃客户数",
                "上月核心客户净增长数","鲜果gmv","乳制品gmv","非乳制品gmv","自有品牌gmv","绩效预估金额"};
        for (int i = 0; i < titleName.length; i++) {
            Cell cell = title.createCell(i);
            cell.setCellValue(titleName[i]);
        }
        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(cellFont);
        cellStyle.setAlignment(HorizontalAlignment.FILL);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Row row;
        for (AdminInfoVo vo : adminInfos) {
            if (Objects.nonNull(vo.getDesGoodsSales())) {
                row = sheet.createRow(rowIndex++);
                row.createCell(0).setCellValue(vo.getAdminName());
                row.createCell(1).setCellValue(vo.getDesGoodsSales());
                row.createCell(2).setCellValue(vo.getDesGoodsSalesGmv().toString());
                row.createCell(3).setCellValue(vo.getTotalGmv().toString());
                row.createCell(4).setCellValue(vo.getGmvTarget().toPlainString());
                row.createCell(5).setCellValue(vo.getVipGmv().toString());
                row.createCell(6).setCellValue(vo.getSingleShopGmv().toString());
                row.createCell(7).setCellValue(vo.getMonthLiving().toString());
                row.createCell(8).setCellValue(vo.getCoreMerchantGrowNum().toString());
                row.createCell(9).setCellValue(vo.getFruitGmv().toString());
                row.createCell(10).setCellValue(vo.getDairyGmv().toString());
                row.createCell(11).setCellValue(vo.getNonDairyGmv().toString());
                row.createCell(12).setCellValue(vo.getBrandGmv().toString());
                row.createCell(13).setCellValue(vo.getPerformance().toString());
            }
        }
        return workbook;
    }


    @Override
    public AjaxResult selectBDByArea(String bdName) {
        return AjaxResult.getOK(salesDataMapper.selectBDByArea(super.getAdminId(), bdName));
    }

    @Override
    public AjaxResult salesTeamData(int pageIndex, int pageSize, TeamDataQuery teamDataQuery) {
        // 按前台条件查询信息
        teamDataQuery.setAdminId(super.getAdminId());
        this.fillTime(teamDataQuery);
        // 茶百道ids
        List<Integer> adminIds = this.cbdAdminIds();
        teamDataQuery.setAdminIds(adminIds);
        // 获取筛选后的商户订单
        PageHelper.startPage(pageIndex, pageSize);
        List<TeamDataVO> teamDataVos = this.getTeamDataVOS(teamDataQuery);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(teamDataVos));
    }

    /**
     * 填充开始结束时间，默认是最近两周
     *
     * @param teamDataQuery 页面输入的时间
     */
    private void fillTime(TeamDataQuery teamDataQuery) {
        LocalDate startTime = LocalDate.now().minusWeeks(2);
        if (Objects.isNull(teamDataQuery.getStartTime())) {
            teamDataQuery.setStartTime(startTime);
        }
        if (Objects.isNull(teamDataQuery.getEndTime())) {
            teamDataQuery.setEndTime(LocalDate.now());
        }
    }

    @Override
    public AjaxResult orderData(TeamDataQuery teamDataQuery) {
        // 判断权限，只有销售主管，区域主管和超级管理员可导出文档
        if (!(super.isSA() || super.isAreaSA() || super.isSaleSA())) {
            AjaxResult.getError(ResultConstant.UNAUTHORIZED);
        }

        String uId = UUID.randomUUID().toString();
        String now = BaseDateUtils.date2String(new Date(), BaseDateUtils.NUMBER_DATE_FORMAT);
        String fileName = ("销售业务数据" + "_" + now + ".xls");

        FileDownloadRecord record = new FileDownloadRecord();
        record.setAdminId(getAdminId());
        record.setStatus(CrmGlobalConstant.WAIT_UPLOAD);
        record.setType(FileDownloadRecordEnum.Type.SALE_DATA_EXCEL.ordinal());
        record.setFileName(fileName);
        record.setParams(JSONObject.toJSONString(teamDataQuery));
        record.setuId(uId);
        fileDownloadRecordMapper.insert(record);

        JSONObject msgJson = new JSONObject();
        msgJson.put("params", teamDataQuery);
        msgJson.put("adminId", getAdminId());
        msgJson.put("UUID", uId);
        String msg = msgJson.toJSONString();
        // 发送消息
        MqData mqData = new MqData();
        mqData.setType(MessageType.SALE_DATA_EXCEL);
        mqData.setBusiness(MessageBusiness.EXCEL);
        mqData.setData(msg);
        producer.sendDataToQueue(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST, MessageKeyEnum.SALE_DATA_EXCEL, JSON.toJSONString(mqData));
        logger.info("销售 {}正在下载销售业务数据", getAdminName());
        return AjaxResult.getOK();
    }

    @Override
    public CommonResult<AdminInfoVo> selectBdMerchantData(Integer adminId) {
        adminId = Optional.ofNullable(adminId).orElse(super.getAdminId());

        // 本月核心客户数,拉新数,固定奖励sku销量,拜访数(去重),普通拜访数,普通上门拜访数,有效拜访数,价值拜访数
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_DAY_GMV.getTableName());
        Integer dateFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        AdminInfoVo infoVo = crmBdDayGmvMapper.selectByAdminId(adminId, dateFlag);
        if (Objects.isNull(infoVo)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST,"个人数据信息暂未同步,请检查您的销售身份,并于明日再来查看");
        }
        // 拜访总数
        SalesDataQuery salesDataQuery = new SalesDataQuery();
        salesDataQuery.setAdminId(super.getAdminId());
        salesDataQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        salesDataQuery.setEndTime(BaseDateUtils.getDayEnd(LocalDateTime.now()));
        Integer visitTotal = salesDataMapper.selectVisitNum(salesDataQuery);
        infoVo.setVisitTotal(visitTotal);

        // 上月核心客户数(取基础值)
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminId);
        Integer lastDateCoreMerchantNum = Objects.isNull(crmBdConfig) ? NumberUtils.INTEGER_ZERO : crmBdConfig.getCoreMerchantAmount();
        infoVo.setLastCoreMerchantNum(lastDateCoreMerchantNum);

        // 本月核心客户净增长数
        int coreMerchantGrowNum = infoVo.getCoreMerchantNum() - lastDateCoreMerchantNum;
        int maxCoreMerchantGrowNum = Math.max(coreMerchantGrowNum, NumberUtils.INTEGER_ZERO);
        infoVo.setCoreMerchantGrowNum(maxCoreMerchantGrowNum);

        // 奖励牌级
        List<CrmCommissionCoreMerchant> crmCommissionCoreMerchants = crmCommissionCoreMerchantMapper.selectCoreMerchantsNetGrowth();
        // 按照最大值排序
        List<Integer> maxNumList = crmCommissionCoreMerchants.stream().map(CrmCommissionCoreMerchant::getMaximum).sorted().collect(Collectors.toList());
        for (int i = 0; i < maxNumList.size(); i++) {
            // 最后一次为最高级别,直接跳过
            if(i == maxNumList.size() - 1){
                break;
            }
            Integer maxNum = maxNumList.get(i);
            // 获取牌级差值
            if(maxNum.compareTo(maxCoreMerchantGrowNum) > 0){
                infoVo.setGradeDifference(maxNum - maxCoreMerchantGrowNum);
                break;
            }
        }
        return CommonResult.ok(infoVo);
    }

    @Override
    public AjaxResult selectChangeMerchant(Integer adminId) {
        if(Objects.isNull(adminId)){
            adminId =  super.getAdminId();
        }
        // 上月核心客户信息
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_MONTH_GMV.getTableName());
        Integer lastDataFlag = Objects.isNull(lastMonth) ? NumberUtils.INTEGER_ZERO : lastMonth.getDateFlag();
        List<ChangeMerchantVO> lastCore = crmMerchantMonthGmvMapper.selectCoreMerchant(adminId, lastDataFlag);

        // 本月核心客户
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        Integer dataFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        List<ChangeMerchantVO> thisChangeMerchant = crmMerchantDayGmvMapper.selectCoreMerchantById(adminId, dataFlag);

        List<ChangeMerchantVO> middleList = new ArrayList<>(thisChangeMerchant);
        Map<String, List<ChangeMerchantVO>> info = new HashMap<>(2);
        if (!CollectionUtils.isEmpty(thisChangeMerchant)) {
            // 本月新增的核心客户
            thisChangeMerchant.removeAll(lastCore);
            thisChangeMerchant.forEach(m -> m.setRoleTag(PageJurisdictionEnum.MerchantDetail.DETAILED.ordinal()));
            info.put("upMerchant", thisChangeMerchant);
            // 本月下降的核心客户
            lastCore.removeAll(middleList);
        }

        boolean isOnlyBd = !(isSA() || isAreaSA() || isSaleSA());
        if(CollectionUtil.isNotEmpty(lastCore)){
            for (ChangeMerchantVO changeMerchantVO : lastCore) {
                FollowUpRelation followUpRelation = followUpRelationMapper.selectByMid(changeMerchantVO.getmId().intValue());
                if (Objects.isNull(followUpRelation)) {
                    continue;
                }
                boolean myMerchant = Objects.equals(adminId, followUpRelation.getAdminId());
                boolean isBriefBool = isOnlyBd && !myMerchant && !followUpRelation.getReassign();
                int roleTag = isBriefBool ? PageJurisdictionEnum.MerchantDetail.BRIEF.ordinal()
                        : PageJurisdictionEnum.MerchantDetail.DETAILED.ordinal();
                changeMerchantVO.setRoleTag(roleTag);
            }
        }
        info.put("downMerchant", lastCore);
        return AjaxResult.getOK(info);
    }

    @Override
    public AjaxResult selectSkuCategory(int pageIndex, int pageSize, String name) {
        PageHelper.startPage(pageIndex, pageSize);
        List<SalesDataVo> salesDataVos = salesDataMapper.selectSkuCategory(name);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(salesDataVos));
    }

    @Override
    public AjaxResult selectGmvBeforeDays(int days, SalesDataQuery salesDataQuery) {
        if(days <= 0 || days > 10){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        // 处理区域
        this.dealWithInput(salesDataQuery);
        // 离线库最新更新时间
        DataSynchronizationInformation thisMerchantDay = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        if(Objects.isNull(thisMerchantDay)){
            return AjaxResult.getErrorWithMsg("暂无同步数据");
        }
        Integer dayTag = thisMerchantDay.getDateFlag();
        LocalDate dataUpdateTime = DateUtils.stringToLocalDate(dayTag.toString());
        if (salesDataQuery.getType() != null && salesDataQuery.getType() == 1) {
            List<Integer> integers = salesDataMapper.selectAdminIds();
            integers.removeAll(salesDataMapper.selectAdminIdsByTypeLock(2, 1));
            salesDataQuery.setAdminIds(integers);
        }
        List<SalesDataVo> gmvDataBeforeDaysList = new ArrayList<>(days);
        for (int i = 0; i <= days; i++) {
            salesDataQuery.setStartTime(LocalDateTime.of(dataUpdateTime,LocalTime.MIN));
            salesDataQuery.setEndTime(LocalDateTime.of(dataUpdateTime,LocalTime.MAX));
            // 总gmv,核心客户,自营gmv
            SalesDataVo salesDataVo = this.thisBdTeamQuery(dayTag, null, salesDataQuery);
            // 计算gmv,核心客户变化值
            if(i > 0){
                // 如果是1号,就不处理
                SalesDataVo dataVoAfterDay = gmvDataBeforeDaysList.get(i - 1);
                if(ObjectUtil.notEqual(dataVoAfterDay.getDataUpdateTime().getDayOfMonth(), NumberUtils.INTEGER_ONE)){
                    this.dealWithGmvAndCoreMerchantNum(salesDataVo,dataVoAfterDay);
                }
            }
            // 日期
            salesDataVo.setDataUpdateTime(dataUpdateTime);
            // 月活
            int monthLiving = salesDataMapper.selectMonthLivingByOrders(salesDataQuery);
            salesDataVo.setMonthLiving(monthLiving);
            // 拉新
            Integer newAdminOrderNum = salesDataMapper.selectNewAdmin(salesDataQuery);
            salesDataVo.setIntroducingNewReward(newAdminOrderNum);
            // 拜访数,陪访数
            SalesDataVo visit = salesDataMapper.selectVisitByArea(salesDataQuery);
            salesDataVo.setVisitNum(visit.getVisitNum());
            salesDataVo.setEscortNum(visit.getEscortNum());
            // 减一天
            dataUpdateTime = dataUpdateTime.minusDays(NumberUtils.INTEGER_ONE);
            String dataFormat = dataUpdateTime.format(DateTimeFormatter.ofPattern(BaseDateUtils.MID_DATE_FORMAT));
            dayTag = Integer.parseInt(dataFormat);
            // 最后一次循环为第days+1天,不需要
            if(i != days){
                gmvDataBeforeDaysList.add(salesDataVo);
            }
        }
        return AjaxResult.getOK(gmvDataBeforeDaysList);
    }

    /**
     * 处理gmv及核心客户数,展示变化数量
     * @param salesDataVo 选中天的gmv数据
     * @param dataVoAfterDay 选中天后一天的gmv数据
     */
    private void dealWithGmvAndCoreMerchantNum(SalesDataVo salesDataVo, SalesDataVo dataVoAfterDay) {
        BigDecimal gmvDifference = dataVoAfterDay.getCurrentMonthGmv().subtract(salesDataVo.getCurrentMonthGmv());
        BigDecimal brandGmv = dataVoAfterDay.getBrandGmv().subtract(salesDataVo.getBrandGmv());
        BigDecimal spuAverage = dataVoAfterDay.getSpuAverage().subtract(salesDataVo.getSpuAverage());
        BigDecimal distributionGmv = dataVoAfterDay.getDistributionGmv().subtract(salesDataVo.getDistributionGmv());

        int coreMerchantDifference = dataVoAfterDay.getCoreMerchantNum() - salesDataVo.getCoreMerchantNum();
        dataVoAfterDay.setCurrentMonthGmv(gmvDifference);
        dataVoAfterDay.setCoreMerchantNum(coreMerchantDifference);
        dataVoAfterDay.setSpuAverage(spuAverage);
        dataVoAfterDay.setBrandGmv(brandGmv);
        dataVoAfterDay.setDistributionGmv(distributionGmv);
    }

    private void fillVisitNum(AdminInfoVo adminInfoVo, AdminInfoVo infoVo){
        adminInfoVo.setOrdinaryNum(infoVo.getOrdinaryNum());
        adminInfoVo.setDropInVisitNum(infoVo.getDropInVisitNum());
        adminInfoVo.setEfficientNum(infoVo.getEfficientNum());
        adminInfoVo.setWorthNum(infoVo.getWorthNum());
        int visitNum = infoVo.getOrdinaryNum() + infoVo.getDropInVisitNum() + infoVo.getEfficientNum();
        adminInfoVo.setVisitNum(visitNum);
        adminInfoVo.setNoOrderRegister(infoVo.getNoOrderRegister());
        adminInfoVo.setOrdinaryPullNewAmount(infoVo.getOrdinaryPullNewAmount());
        adminInfoVo.setPullNewAmount(infoVo.getPullNewAmount());
    }

    private List<Integer> cbdAdminIds() {
        // 获取茶百道品牌adminIds
        String adminIdStr = configMapper.selectOne(CrmGlobalConstant.CBD_ADMIN_ID).getValue();
        String[] split = adminIdStr.split(CrmGlobalConstant.SEPARATING_SYMBOL);
        List<Integer> adminIds = new ArrayList<>(20);
        for (String value : split) {
            adminIds.add(Integer.valueOf(value));
        }
        return adminIds;
    }

    @NotNull
    private List<TeamDataVO> getTeamDataVOS(TeamDataQuery teamDataQuery) {
        // 获取筛选后的商户订单,已按最新下单时间降序排序
        List<TeamDataVO> tdList = salesDataMapper.salesTeamData(teamDataQuery);
        // 获取城市负责人姓名,循环pageSize次
        for (TeamDataVO dataVO : tdList) {
            BdExt bdExt = crmManageBdMapper.selectRealName(dataVO.getAreaNo());
            if (Objects.nonNull(bdExt)) {
                dataVO.setManageName(bdExt.getAdminName());
            }
        }
        return tdList;
    }


    @Override
    public AjaxResult orderDetails(TeamDataQuery teamDataQuery) {
        // 默认展示本月信息，可由前台自选
        this.fillTime(teamDataQuery);
        // 获取客户数据
        TeamDataVO merchantDetail = salesDataMapper.selectOrderDetails(teamDataQuery.getmId());
        if (Objects.isNull(merchantDetail)) {
            return AjaxResult.getOK();
        }
        if (!Objects.isNull(merchantDetail.getAdminId())) {
            CrmBdOrg crmBdOrg = orgMapper.selectByBdIdAndRank(merchantDetail.getBdId(), BD);
            if (crmBdOrg != null) {
                List<CrmBdOrg> crmBdOrgList = bdAreaConfigService.listParentByByParentId(crmBdOrg.getId(),Boolean.TRUE);
                for (CrmBdOrg bdOrg : crmBdOrgList) {
                    if (bdOrg.getRank() == CITY_MANAGER) {
                        merchantDetail.setManageName(bdOrg.getBdName());
                    } else if (bdOrg.getRank() == AREA_MANAGER) {
                        merchantDetail.setParentName(bdOrg.getBdName());
                    }
                }
            }
        }
        // 拼接注册地址
        String registeredAddress = merchantDetail.getProvince() + merchantDetail.getCity() + merchantDetail.getArea() + merchantDetail.getAddress();
        merchantDetail.setRegisteredAddress(registeredAddress);
        return AjaxResult.getOK(merchantDetail);
    }


    @Override
    public AjaxResult merchantLevelDistribution() {
        DataSynchronizationInformation merchantDayTag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        Integer dataFlag = Objects.isNull(merchantDayTag) ? NumberUtils.INTEGER_ZERO : merchantDayTag.getDateFlag();
        // 查询商户GMV等级L1,客单价等级L2枚举
        List<CrmCommissionMerchantLevel> merchantLevels = crmCommissionMerchantLevelMapper.selectAllCoreMerchantLevel(NumberUtils.INTEGER_ZERO, null);
        // 取l1+l2最小值~最大值区间
        BigDecimal gmvMinProportion = merchantLevels.stream().map(CrmCommissionMerchantLevel::getGmvProportion).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal gmvMaxProportion = merchantLevels.stream().map(CrmCommissionMerchantLevel::getGmvProportion).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal priceMinProportion = merchantLevels.stream().map(CrmCommissionMerchantLevel::getPriceProportion).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal priceMaxProportion = merchantLevels.stream().map(CrmCommissionMerchantLevel::getPriceProportion).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
        BigDecimal minProportion = gmvMinProportion.add(priceMinProportion);
        BigDecimal maxProportion = gmvMaxProportion.add(priceMaxProportion);

        BigDecimal bigDecimal = new BigDecimal("0.1");
        List<MerchantLevelDistributionVO> merchantLevelDistributionVOList = new ArrayList<>(20);
        for (; minProportion.compareTo(maxProportion) <= 0;minProportion = minProportion.add(bigDecimal)) {
            List<MerchantLevelDistributionVO> merchantGmvLevelDistributionVO =
                    crmMerchantDayGmvMapper.selectLevelDistribution(minProportion,super.getAdminId(),dataFlag);

            int size = CollectionUtils.isEmpty(merchantGmvLevelDistributionVO) ? NumberUtils.INTEGER_ZERO : merchantGmvLevelDistributionVO.size();
            merchantLevelDistributionVOList.add(new MerchantLevelDistributionVO(minProportion,size));
        }
        return AjaxResult.getOK(merchantLevelDistributionVOList);
    }

    @Override
    public AjaxResult merchantLevelDistributionInfo(String proportion) {
        BigDecimal levelProportion = new BigDecimal(proportion);
        DataSynchronizationInformation merchantDayTag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        if(Objects.isNull(merchantDayTag)){
            return AjaxResult.getErrorWithMsg("数据暂未更新,请明日再来!");
        }
        List<MerchantLevelDistributionVO> merchantGmvLevelDistributionVO =
                crmMerchantDayGmvMapper.selectLevelDistribution(levelProportion,super.getAdminId(),merchantDayTag.getDateFlag());
        return AjaxResult.getOK(merchantGmvLevelDistributionVO);
    }

    @Override
    public CommonResult<SalesDataVo> selectGmvByCity(SalesDataQuery salesDataQuery) {
        salesDataQuery = Optional.ofNullable(salesDataQuery).orElse(new SalesDataQuery());
        if(Objects.isNull(salesDataQuery.getThisMonth())){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"参数错误，是否为本月标识未传参，请联系管理员解决");
        }
        salesDataQuery.setType(NumberUtils.INTEGER_ONE);
        // 处理销售管理的行政城市筛选
        if (!CollectionUtil.isEmpty(salesDataQuery.getBdOrgId())) {
            List<String> cities = dealWithManageCity(salesDataQuery);
            if (cities == null) {
                return CommonResult.ok(new SalesDataVo());
            }
        }
        // 处理销售区域行政城市筛选
        if(ObjectUtil.isNotNull(salesDataQuery.getSalesAreaId())){
            List<BdSalesCityVo> salesCityList = salesCityMapper.listBySalesAreaId(salesDataQuery.getSalesAreaId());
            salesDataQuery.setAdministrativeCityList(salesCityList.stream().map(BdSalesCityVo::getCity).collect(Collectors.toList()));
        }

        SalesDataVo salesDataVo;
        // 本月
        if (salesDataQuery.getThisMonth()) {
            DataSynchronizationInformation cityGmvTag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_CITY_DAY_GMV.getTableName());
            Integer cityGmvData = Objects.isNull(cityGmvTag) ? NumberUtils.INTEGER_ZERO : cityGmvTag.getDateFlag();
            salesDataQuery.setQueryTime(cityGmvData);
            salesDataVo = crmCityDayGmvMapper.selectByCity(salesDataQuery);
            salesDataVo = Optional.ofNullable(salesDataVo).orElse(new SalesDataVo());
            // 上月gmv信息
            DataSynchronizationInformation cityMonthGmvTag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_CITY_MONTH_GMV.getTableName());
            Integer cityMonthGmvData = Objects.isNull(cityMonthGmvTag) ? NumberUtils.INTEGER_ZERO : cityMonthGmvTag.getDateFlag();
            salesDataQuery.setQueryTime(cityMonthGmvData);
            SalesDataVo lastMonthSaleDataVo = crmCityMonthGmvMapper.selectLastInfo(salesDataQuery);
            lastMonthSaleDataVo = Optional.ofNullable(lastMonthSaleDataVo).orElse(new SalesDataVo());
            salesDataVo.setLastMonthGmv(lastMonthSaleDataVo.getLastMonthGmv());
            salesDataVo.setLastMonthCoreMerchantNum(lastMonthSaleDataVo.getLastMonthCoreMerchantNum());
        } else {
            salesDataVo = crmCityMonthGmvMapper.selectByCity(salesDataQuery);
        }
        return CommonResult.ok(salesDataVo);
    }

    /**
     * 处理销售管理的行政城市筛选
     *
     * @param salesDataQuery
     */
    private List<String> dealWithManageCity(@NotNull SalesDataQuery salesDataQuery) {
        List<CrmBdOrg> crmBdOrgList = getParent(salesDataQuery.getBdOrgId());
        for (Integer orgId : salesDataQuery.getBdOrgId()) {
            CrmBdOrg crmBdOrg = orgMapper.selectByPrimaryKey(orgId);
            if (crmBdOrg == null || !ObjectUtil.equal(crmBdOrg.getRank(), CITY_MANAGER)) {
                continue;
            }
            crmBdOrgList.add(crmBdOrg);
        }
        List<Integer> orgIds = crmBdOrgList.stream().filter(cbo -> ObjectUtil.equal(cbo.getRank(), CITY_MANAGER)).map(CrmBdOrg::getId).collect(Collectors.toList());
        if (orgIds.isEmpty()) {
            return null;
        }
        List<CrmSalesCity> crmSalesCities = salesCityMapper.listByBdOrgId(orgIds);
        if (crmSalesCities.isEmpty()) {
            return null;
        }
        List<String> cities = crmSalesCities.stream().map(CrmSalesCity::getCity).distinct().collect(Collectors.toList());
        salesDataQuery.setAdministrativeCityList(cities);
        return cities;
    }

    @Override
    public AjaxResult selectCityGmvBeforeDays(int days, SalesDataQuery salesDataQuery) {
        if(days <= 0 || days > 10){
            return AjaxResult.getErrorWithMsg("可查看天数超过10天,暂不支持!");
        }
        salesDataQuery = Optional.ofNullable(salesDataQuery).orElse(new SalesDataQuery());
        if(Objects.isNull(salesDataQuery.getType())){
            salesDataQuery.setType(NumberUtils.INTEGER_ZERO);
        }

        // 离线库最新更新时间
        DataSynchronizationInformation thisMerchantDay = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_CITY_DAY_GMV.getTableName());
        if(Objects.isNull(thisMerchantDay)){
            return AjaxResult.getErrorWithMsg("暂无同步数据");
        }
        Integer dayTag = thisMerchantDay.getDateFlag();
        LocalDate dataUpdateTime = DateUtils.stringToLocalDate(dayTag.toString());

        List<SalesDataVo> gmvDataBeforeDaysList = new ArrayList<>(days);

        if (CollectionUtil.isNotEmpty(salesDataQuery.getBdOrgId())){
            List<Integer> bdOrgList= new ArrayList<>();
            CrmBdOrg crmBdOrg = orgMapper.selectByPrimaryKey(salesDataQuery.getBdOrgId().get(0));
            if (crmBdOrg == null) {
                return AjaxResult.getOK(gmvDataBeforeDaysList);
            }
            if (crmBdOrg.getRank() == CITY_MANAGER) {
                bdOrgList = Collections.singletonList(crmBdOrg.getId());
            } else if (ObjectUtil.equal(crmBdOrg.getRank(), AREA_MANAGER) || ObjectUtil.equal(crmBdOrg.getRank(), DEPARTMENT_MANAGER)) {
                List<CrmBdOrg> orgList = new ArrayList<>();
                bdAreaConfigService.listChildrenByParentId(crmBdOrg.getId(),orgList);
                bdOrgList = orgList.stream().filter(o -> o.getRank() == CITY_MANAGER).map(CrmBdOrg::getId).collect(Collectors.toList());
            }
            if (bdOrgList.isEmpty()) {
                return AjaxResult.getOK(gmvDataBeforeDaysList);
            }
            List<CrmSalesCity> crmSalesCities = salesCityMapper.listByBdOrgId(bdOrgList);
            List<String> cities = crmSalesCities.stream().map(CrmSalesCity::getCity).distinct().collect(Collectors.toList());
            salesDataQuery.setAdministrativeCityList(cities);
            if (cities.isEmpty()){
                return AjaxResult.getOK(gmvDataBeforeDaysList);
            }
        }

        for (int i = 0; i <= days; i++) {
            salesDataQuery.setQueryTime(dayTag);
            // 当月1号至今的数据
            SalesDataVo salesDataVo = crmCityDayGmvMapper.selectByCity(salesDataQuery);
            salesDataVo = Optional.ofNullable(salesDataVo).orElse(new SalesDataVo());
            // 计算gmv,核心客户变化值
            if(i > 0){
                // 如果是1号,就不处理
                SalesDataVo dataVoAfterDay = gmvDataBeforeDaysList.get(i - 1);
                if(ObjectUtil.notEqual(dataVoAfterDay.getDataUpdateTime().getDayOfMonth(), NumberUtils.INTEGER_ONE)){
                    this.dealWithCityGmvInfo(salesDataVo,dataVoAfterDay);
                }
            }
            // 日期
            salesDataVo.setDataUpdateTime(dataUpdateTime);
            // 减一天
            dataUpdateTime = dataUpdateTime.minusDays(NumberUtils.INTEGER_ONE);
            String dataFormat = dataUpdateTime.format(DateTimeFormatter.ofPattern(BaseDateUtils.MID_DATE_FORMAT));
            dayTag = Integer.parseInt(dataFormat);
            // 最后一次循环为第days+1天,不需要
            if(i != days){
                gmvDataBeforeDaysList.add(salesDataVo);
            }
        }
        return AjaxResult.getOK(gmvDataBeforeDaysList);
    }

    /**
     * 处理行政城市销售数据
     * @param saleInfo 选中天的gmv数据
     * @param nextDayInfo 选中天的后一天的gmv数据
     */
    private void dealWithCityGmvInfo(SalesDataVo saleInfo, SalesDataVo nextDayInfo) {
        // GMV 月活 拉新 核心客户数 拜访数 陪访数
        BigDecimal currentMonthGmv = nextDayInfo.getCurrentMonthGmv().subtract(saleInfo.getCurrentMonthGmv());
        BigDecimal brandGmv = nextDayInfo.getBrandGmv().subtract(saleInfo.getBrandGmv());
        BigDecimal spuAverage = nextDayInfo.getSpuAverage().subtract(saleInfo.getSpuAverage());

        int monthLivingNum = nextDayInfo.getMonthLiving() - saleInfo.getMonthLiving();
        int introducingNewReward = nextDayInfo.getIntroducingNewReward() - saleInfo.getIntroducingNewReward();
        int coreMerchantNum = nextDayInfo.getCoreMerchantNum() - saleInfo.getCoreMerchantNum();
        int visitNum = nextDayInfo.getVisitNum() - saleInfo.getVisitNum();
        int escortNum = nextDayInfo.getEscortNum() - saleInfo.getEscortNum();

        // 将选中天的后一天数据提换为日数据
        nextDayInfo.setCurrentMonthGmv(currentMonthGmv);
        nextDayInfo.setMonthLiving(monthLivingNum);
        nextDayInfo.setIntroducingNewReward(introducingNewReward);
        nextDayInfo.setCoreMerchantNum(coreMerchantNum);
        nextDayInfo.setVisitNum(visitNum);
        nextDayInfo.setEscortNum(escortNum);
        nextDayInfo.setSpuAverage(spuAverage);
        nextDayInfo.setBrandGmv(brandGmv);
    }
}

