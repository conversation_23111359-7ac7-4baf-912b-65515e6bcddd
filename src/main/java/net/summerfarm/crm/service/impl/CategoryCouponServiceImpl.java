package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.enums.*;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.CategoryCouponQuotaRewardMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.mapper.repository.CategoryCouponFeeRateRepository;
import net.summerfarm.crm.mapper.repository.ProductsRepository;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.BasePriceFileDTO;
import net.summerfarm.crm.model.dto.CategoryProductDTO;
import net.summerfarm.crm.model.dto.CategoryQuotaDTO;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.query.BasePriceQuery;
import net.summerfarm.crm.model.query.CategoryQuotaChangeQuery;
import net.summerfarm.crm.model.query.monthLiving.QuotaListQuery;
import net.summerfarm.crm.model.query.monthLiving.QuotaRatioQuery;
import net.summerfarm.crm.model.vo.CategoryCouponFeeRateVO;
import net.summerfarm.crm.model.vo.CategoryCouponQuotaVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.CategoryCouponService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.oss.common.util.OssGetUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.AREA_MANAGER;
import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.DEPARTMENT_MANAGER;
import static net.summerfarm.crm.enums.BdAreaConfigEnum.isCityManager;

/**
 * <AUTHOR>
 * @Date 2023/3/6 15:46
 */
@Service
public class CategoryCouponServiceImpl extends BaseService implements CategoryCouponService {
    @Resource
    private CoreProductBasePriceMapper basePriceMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private CategoryCouponQuotaMapper quotaMapper;
    @Resource
    private CategoryCouponQuotaChangeMapper quotaChangeMapper;
    @Resource
    private CategoryCouponQuotaRewardMapper rewardMapper;
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private CategoryCouponFeeRateRepository categoryCouponFeeRateRepository;

    @Resource
    private ProductsRepository productsRepository;
    @Resource
    private LargeAreaMapper largeAreaMapper;
    @Resource
    private BdAreaConfigService areaConfigService;
    @Resource
    private CrmBdOrgMapper orgMapper;
    @Resource
    BdAreaConfigService bdAreaConfigService;
    @Resource
    CrmCouponExpenseRecordMapper crmCouponExpenseRecordMapper;


    @Override
    public PageInfo<CoreProductBasePrice> listBasePrice(BasePriceQuery query, boolean displayBasePrice, boolean displayMerchantSituationCategoryBasePrice) {
        if (query.getPageIndex() != null || query.getPageSize() != null) {
            PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        }
        List<CoreProductBasePrice> basePriceList = basePriceMapper.listBasePrice(query);
        if (!displayBasePrice) {
            basePriceList.forEach(it -> it.setBasePrice(null));
            basePriceList.forEach(it -> it.setMerchantSituationCategoryRedLinePrice(null));
        }
        if (!displayMerchantSituationCategoryBasePrice) {
            basePriceList.forEach(it -> it.setMerchantSituationCategoryBasePrice(null));
        }

        return PageInfoHelper.createPageInfo(basePriceList);
    }

    @Override
    public CommonResult<Void> updateBasePrice(CoreProductBasePrice basePrice, boolean setBasePrice, boolean setMerchantSituationCategoryBasePrice) {
        CoreProductBasePrice basePriceQuery = basePriceMapper.selectBySkuAreaNo(basePrice.getId(), basePrice.getSku(), basePrice.getLargeAreaNo());
        if (basePriceQuery != null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "大区已存在该sku，请重试");
        }

        if (setBasePrice) {
            basePriceMapper.updateBasePriceAndMscRedLinePriceById(basePrice.getBasePrice(), basePrice.getMerchantSituationCategoryRedLinePrice(), basePrice.getId());
        }
        if (setMerchantSituationCategoryBasePrice) {
            basePriceMapper.updateMerchantSituationCategoryBasePriceById(basePrice.getMerchantSituationCategoryBasePrice(), basePrice.getId());
        }

        return CommonResult.ok();
    }

    @Override
    public void delBasePrice(Integer id) {
        basePriceMapper.delBasePrice(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> batchDelBasePrice(BasePriceFileDTO fileDTO) {
        basePriceMapper.batchDelBasePrice(fileDTO.getIds());
        return CommonResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBasePrice(List<CoreProductBasePrice> basePriceList, boolean setMerchantSituationCategoryBasePrice) {
        for (CoreProductBasePrice basePrice : basePriceList) {
            CoreProductBasePrice basePriceQuery = basePriceMapper.selectBySkuAreaNo(null, basePrice.getSku(), basePrice.getLargeAreaNo());
            if (basePriceQuery != null) {
                throw new BizException("大区已存在该底价的sku");
            }
            if (!setMerchantSituationCategoryBasePrice) {
                basePrice.setMerchantSituationCategoryBasePrice(null);
            }
            basePriceMapper.insertBasePrice(basePrice);
        }
    }

    @Override
    public CommonResult<String> batchInsertBasePrice(BasePriceFileDTO fileDTO, boolean setMerchantSituationCategoryBasePrice) {
        InputStream inputStream = null;
        StringBuilder sb = new StringBuilder();
        try {
            inputStream = OssGetUtil.getInputStream(fileDTO.getFilePath());
            List<CoreProductBasePrice> insertList = EasyExcel.read(inputStream).head(CoreProductBasePrice.class).sheet().doReadSync();
            int i = 1;
            for (CoreProductBasePrice basePrice : insertList) {
                boolean paramValid = StringUtils.isBlank(basePrice.getLargeAreaName())
                        || StringUtils.isBlank(basePrice.getSku())
                        || basePrice.getBasePrice() == null
                        || basePrice.getMerchantSituationCategoryRedLinePrice() == null;
                if (paramValid) {
                    sb.append(i).append("行，必填信息不能为空\n");
                    continue;
                }
                LargeAreaDTO largeAreaDTO = largeAreaMapper.selectByName(basePrice.getLargeAreaName());
                if (largeAreaDTO == null) {
                    sb.append(basePrice.getLargeAreaName()).append("不存在\n");
                    continue;
                }
                CategoryProductDTO categoryTypeBySku = productsRepository.getCategoryTypeBySku(basePrice.getSku());
                if (categoryTypeBySku == null) {
                    sb.append(basePrice.getLargeAreaName()).append("sku").append(basePrice.getSku()).append("不正确");
                    continue;
                }
                basePrice.setLargeAreaNo(largeAreaDTO.getLargeAreaNo());
                CoreProductBasePrice price = basePriceMapper.selectBySkuAreaNo(null, basePrice.getSku(), basePrice.getLargeAreaNo());
                if (price != null) {
                    sb.append(basePrice.getLargeAreaName()).append("已经存在sku").append(basePrice.getSku());
                    continue;
                }

                if (!setMerchantSituationCategoryBasePrice) {
                    basePrice.setMerchantSituationCategoryBasePrice(null);
                }

                basePrice.setPdId(categoryTypeBySku.getPdId());
                basePrice.setPdName(categoryTypeBySku.getPdName());
                basePrice.setWeight(categoryTypeBySku.getWeight());
                basePriceMapper.insertBasePrice(basePrice);
            }
        } catch (Exception e) {
            logger.error("底价批量导入出错，msg:{}", e.getMessage(), e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "导入出错");
        } finally {
            IoUtil.close(inputStream);
        }
        if (sb.length() != 0) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, sb.toString());
        }
        return CommonResult.ok();
    }

    @Override
    public List<LargeAreaDTO> listLargeArea() {
        return basePriceMapper.listBasePriceArea();
    }

    @Override
    public void configSetting(String key, String value) {
        configMapper.updateValue(key, value);
    }

    @Override
    public Config getConfig(String key) {
        return configMapper.selectOne(key);
    }

    @Override
    public PageInfo<CategoryCouponQuotaVO> listQuota(QuotaListQuery pageInput) {
//        List<Integer> queryAdminIds = getQueryAdminIds();
        // 所有销售都可以看到所有人的额度
        List<Integer> queryAdminIds = new ArrayList<>();
        PageHelper.startPage(pageInput.getPageIndex(), pageInput.getPageSize());
        if (CategoryQuotaEnum.QuotaType.CATEGORY_PRICE.getCode().equals(pageInput.getType())) {
            List<CategoryCouponQuotaVO> categoryCouponQuotaVOS = quotaMapper.listQuotaAll(null, pageInput.getType(), pageInput.getAdminName(), queryAdminIds);
            PageInfo<CategoryCouponQuotaVO> pageInfo = PageInfoHelper.createPageInfo(categoryCouponQuotaVOS);
            convertAmount(pageInfo.getList());
            return pageInfo;
        }
        List<CategoryCouponQuotaVO> categoryCouponQuotaVOS = quotaMapper.listQuota(null, pageInput.getType(), pageInput.getAdminName(), null);
        PageInfo<CategoryCouponQuotaVO> pageInfo = PageInfoHelper.createPageInfo(categoryCouponQuotaVOS);
        // 获取费比
        List<Integer> quotaIds = categoryCouponQuotaVOS.stream().map(CategoryCouponQuotaVO::getId).collect(Collectors.toList());
        LambdaQueryWrapper<CategoryCouponFeeRate> queryWrapper = Wrappers.lambdaQuery(CategoryCouponFeeRate.class)
                .in(CategoryCouponFeeRate::getCategoryCouponQuotaId, quotaIds);
        Map<Integer, List<CategoryCouponFeeRate>> feeRateMap = categoryCouponFeeRateRepository.list(queryWrapper).stream()
                .collect(Collectors.groupingBy(CategoryCouponFeeRate::getCategoryCouponQuotaId));
        // 转换费比
        categoryCouponQuotaVOS.forEach(it -> {
            List<CategoryCouponFeeRate> feeRates = feeRateMap.get(it.getId());
            if (CollectionUtil.isNotEmpty(feeRates)) {
                List<CategoryCouponFeeRateVO> feeRateVOS = feeRates.stream().map(feeRate -> {
                    CategoryCouponFeeRateVO feeRateVO = new CategoryCouponFeeRateVO();
                    BeanUtils.copyProperties(feeRate, feeRateVO);
                    return feeRateVO;
                }).collect(Collectors.toList());
                it.setFeeRateList(feeRateVOS);
            }
        });

        return pageInfo;
    }

    private void convertAmount(List<CategoryCouponQuotaVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //adminIdS
        List<Integer> adminIds = list.stream().map(CategoryCouponQuotaVO::getAdminId).distinct().collect(Collectors.toList());
        //统计当前总额度
        List<CrmCouponExpenseRecord> crmCouponExpenseRecords = crmCouponExpenseRecordMapper.groupByAdminIdsAmount(adminIds);
        Map<Integer, BigDecimal> collect = crmCouponExpenseRecords.stream().collect(Collectors.toMap(CrmCouponExpenseRecord::getAdminId, CrmCouponExpenseRecord::getTotalAmount));
        list.forEach(
                it -> {
                    BigDecimal bigDecimal = collect.get(it.getAdminId()) == null ? BigDecimal.ZERO : collect.get(it.getAdminId());
                    it.setQuota(bigDecimal);
                }
        );
    }



    private List<Integer> getQueryAdminIds() {
        Integer adminId = getAdminId();
        //admin m3看所有
        List<CrmBdOrg> orgList = orgMapper.listByBdId(adminId);
        if (orgList.isEmpty()) {
            throw new BizException("组织架构未配置");
        }
        CrmBdOrg crmBdOrg = orgList.get(orgList.size()-1);
        if(crmBdOrg.getRank().equals(DEPARTMENT_MANAGER)){
            return new ArrayList<>();
        }
        //m2看自己 m1的
        else if (crmBdOrg.getRank().equals(AREA_MANAGER)) {
            List<Integer> adminIds = new ArrayList<>();
            adminIds.add(adminId);
            CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                throw new BizException("区域未配置");
            }
            List<Integer> m1IdS = bdAreaConfigService.listChildrenBd(topRankOrg.getId()).stream().filter(it -> isCityManager(it.getRank())).map(CrmBdOrg::getBdId).distinct().collect(Collectors.toList());
            adminIds.addAll(m1IdS);
            return adminIds;
        } else {
            return Collections.singletonList(adminId);
        }
    }

    @Override
    public List<CategoryCouponQuota> listAdmin() {
        return quotaMapper.listAdmin(CategoryQuotaEnum.QuotaType.CATEGORY.getCode());
    }

    @Override
    public List<CategoryCouponQuotaVO> quotaDetail(Integer quotaType) {
        return quotaMapper.listQuota(getAdminId(), quotaType, null, null);
    }

    @Override
    public CommonResult<List<CategoryCouponQuotaVO>> crmQuotaDetail() {
        CrmBdOrg topRankOrg = areaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "组织信息不存在");
        }
        List<CategoryCouponQuotaVO> quotaList = new ArrayList<>();
        if (isCityManager(topRankOrg.getRank())) {
            // m1 获取自己品类券额度和上级的额度
            CrmBdOrg crmBdOrg = orgMapper.selectByPrimaryKey(topRankOrg.getParentId());
            if (crmBdOrg == null) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "上级不存在");
            }
            List<CategoryCouponQuotaVO> parentQuota = quotaMapper.listQuota(crmBdOrg.getBdId(), CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode(), null, null);
            if (!parentQuota.isEmpty()) {
                quotaList.addAll(parentQuota);
            }
           /* List<CategoryCouponQuotaVO> quota = quotaMapper.listQuota(topRankOrg.getBdId(), CategoryQuotaEnum.QuotaType.CATEGORY.getCode(), null, null);
            if (!quota.isEmpty()) {
                quotaList.addAll(quota);
            }*/
        } else {
            // 获取自己的所有类型额度
            quotaList = quotaMapper.listQuota(topRankOrg.getBdId(), null, null, null);
        }

        List<CategoryCouponQuotaVO> outs = quotaList.stream().filter(it -> Objects.equals(it.getType(), CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode())).collect(Collectors.toList());
        //查询自己的品类卷的
        List<CategoryCouponQuotaVO> categoryCouponQuotaVOS = quotaMapper.listQuotaAll(getAdminId(), CategoryQuotaEnum.QuotaType.CATEGORY_PRICE.getCode(), null, null);
        convertAmount(categoryCouponQuotaVOS);
        categoryCouponQuotaVOS.forEach(
                it->{
                    it.setType(CategoryQuotaEnum.QuotaType.CATEGORY.getCode());
                    it.setAwardedQuota(BigDecimal.ZERO);
                }
        );
        outs.addAll(categoryCouponQuotaVOS);
        return CommonResult.ok(outs);
    }

    @Override
    public PageInfo<CategoryCouponQuotaChange> listQuotaChange(CategoryQuotaChangeQuery query) {
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        return PageInfoHelper.createPageInfo(quotaChangeMapper.listQuotaChange(query));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> quotaSetting(CategoryQuotaDTO quotaDto) {
        CategoryCouponQuota quota = quotaMapper.selectByAdminId(quotaDto.getAdminId(), quotaDto.getQuotaType());
        if (quota == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "当前暂无额度信息，请联系运营配置");
        }
        // 可用额度判断
        if (quota.getQuota().compareTo(quotaDto.getQuota().negate()) < 0) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "可用额度不足，请确认后重试");
        }
        quotaMapper.updateQuota(quotaDto.getAdminId(), quotaDto.getQuota(), quotaDto.getQuotaType());
        quotaDto.setType(CouponEnum.CouponQuotaChangeType.SETTING.getCode());
        generateQuotaChange(quotaDto, getAdminId(), getAdminName(), quotaDto.getQuotaType());
        return CommonResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> divisionSetting(CategoryQuotaDTO quota) {
        if (quota.getAdminId().equals(getAdminId())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "不能给自己划分额度");
        }
        CategoryCouponQuota manageQuota = quotaMapper.selectByAdminId(getAdminId(), quota.getQuotaType());
        if (manageQuota == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "当前账号未配置额度信息，请联系运营配置");
        }
        if (manageQuota.getQuota().compareTo(quota.getQuota()) < 0) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "额度不足，请联系运营配置");
        }
        if (quota.getQuota().compareTo(BigDecimal.ZERO) < 0) {
            CategoryCouponQuota bdQuota = quotaMapper.selectByAdminId(quota.getAdminId(), quota.getQuotaType());
            if (bdQuota.getQuota().compareTo(quota.getQuota().negate()) < 0) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "BD额度不足，请修改后重试");
            }
        }
        // 更新被划分的bd 额度和明细
        quota.setType(CouponEnum.CouponQuotaChangeType.DIVISION.getCode());
        generateQuotaChange(quota, getAdminId(), getAdminName(), quota.getQuotaType());
        quotaMapper.updateQuota(quota.getAdminId(), quota.getQuota(), quota.getQuotaType());
        // 更新主管额度和明细
        quota.setAdminId(getAdminId());
        quota.setAdminName(getAdminName());
        quota.setQuota(quota.getQuota().negate());
        quota.setType(CouponEnum.CouponQuotaChangeType.DIVISION.getCode());
        generateQuotaChange(quota, getAdminId(), getAdminName(), quota.getQuotaType());
        quotaMapper.updateQuota(getAdminId(), quota.getQuota(), quota.getQuotaType());
        return CommonResult.ok();
    }

    @Override
    public CommonResult<Void> quotaInsert(CategoryQuotaDTO quota) {
        CategoryCouponQuota couponQuotaQuery = quotaMapper.selectByAdminId(quota.getAdminId(), quota.getQuotaType());
        if (couponQuotaQuery != null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "信息已存在，请确认");
        }
        CategoryCouponQuota couponQuota = new CategoryCouponQuota();
        BeanUtils.copyProperties(quota, couponQuota);
        couponQuota.setType(quota.getQuotaType());
        quotaMapper.insertSelective(couponQuota);
        return CommonResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> deleteQuota(Integer adminId, Integer type) {
        CategoryCouponQuota couponQuota = quotaMapper.selectByAdminId(adminId, type);
        if (couponQuota == null) {
            throw new BizException("已被删除");
        }
        if (Objects.equals(couponQuota.getType(), CouponEnum.CouponType.CATEGORY_COUPON.getCode())) {
            //检查余额
            List<CrmCouponExpenseRecord> crmCouponExpenseRecords = crmCouponExpenseRecordMapper.groupByAdminIdsAmount(Arrays.asList(adminId));
            if (!CollectionUtils.isEmpty(crmCouponExpenseRecords) && crmCouponExpenseRecords.get(0).getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                throw new BizException("用户余额池未清零,不能被删除");
            }
        }
        quotaMapper.deleteByAdminId(adminId, type);
        CategoryQuotaDTO quota = new CategoryQuotaDTO();
        quota.setRemark("删除额度信息");
        quota.setAdminId(couponQuota.getAdminId());
        quota.setAdminName(couponQuota.getAdminName());
        quota.setType(CouponEnum.CouponQuotaChangeType.SETTING.getCode());
        quota.setQuota(couponQuota.getQuota().negate());
        generateQuotaChange(quota, getAdminId(), getAdminName(), type);
        return CommonResult.ok();
    }

    @Override
    public void quotaReward() {
        DataSynchronizationInformation dataSynchronizationInformation = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CATEGORY_COUPON_QUOTA_REWARD.getTableName());
        List<CategoryCouponQuotaReward> categoryCouponQuotaRewards = rewardMapper.selectByDayTag(dataSynchronizationInformation.getDateFlag());
        if (categoryCouponQuotaRewards.isEmpty()) {
            return;
        }
        quotaChangeMapper.insertRewardBatch(categoryCouponQuotaRewards, CategoryQuotaEnum.QuotaType.CATEGORY.getCode());
        categoryCouponQuotaRewards.forEach(c -> quotaMapper.updateQuota(c.getAdminId(), c.getAmount(), CategoryQuotaEnum.QuotaType.CATEGORY.getCode()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> ratioSetting(QuotaRatioQuery ratioQuery) {
        CategoryCouponQuota quota = quotaMapper.selectByAdminId(ratioQuery.getAdminId(), ratioQuery.getQuotaType());
        if (quota == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "费比设置用户不存在，请刷新后重试");
        }

        List<CategoryCouponFeeRate> feeRateList = categoryCouponFeeRateRepository.list(Wrappers.lambdaQuery(CategoryCouponFeeRate.class)
                .eq(CategoryCouponFeeRate::getCategoryCouponQuotaId, quota.getId()));
        ratioQuery.getFeeRateInputList().forEach(feeRateInput -> {
            CategoryCouponFeeRate old = feeRateList.stream()
                    .filter(it -> Objects.equals(it.getMerchantType(), feeRateInput.getMerchantType())
                            && Objects.equals(it.getCategoryType(), feeRateInput.getCategoryType()))
                    .findFirst().orElse(null);
            // 新旧费比一致时,不更新
            if (old != null && feeRateInput.getFeeRate().compareTo(old.getFeeRate()) == 0) {
                return;
            }

            // 更新费比
            CategoryCouponFeeRate updated = new CategoryCouponFeeRate();
            updated.setFeeRate(feeRateInput.getFeeRate());
            if (old == null) {
                // 不存在时,新增
                updated.setCategoryCouponQuotaId(quota.getId());
                updated.setMerchantType(feeRateInput.getMerchantType());
                updated.setCategoryType(feeRateInput.getCategoryType());
                categoryCouponFeeRateRepository.save(updated);
            } else {
                updated.setId(old.getId());
                categoryCouponFeeRateRepository.updateById(updated);
            }

            // 费比变动,新增费比设置明细
            CategoryQuotaDTO dto = getQuotaChange(quota, ratioQuery.getRemark());
            dto.setQuota(feeRateInput.getFeeRate());
            // 费比变更类型, 新客还是老客
            CouponEnum.CouponQuotaChangeType type = Objects.equals(CategoryQuotaFeeRateEnum.MerchantType.OLD.getCode(), feeRateInput.getMerchantType()) ?
                    CouponEnum.CouponQuotaChangeType.OLD_CUSTOMER_RATIO : CouponEnum.CouponQuotaChangeType.NEW_CUSTOMER_RATIO;
            dto.setType(type.getCode());
            // 在remark上记录品类类型
            ProductCategoryTypeEnum categoryTypeEnum = ProductCategoryTypeEnum.fromCode(feeRateInput.getCategoryType());
            dto.setRemark(categoryTypeEnum.getDes() + (StrUtil.isEmpty(ratioQuery.getRemark()) ? "" : " - " + ratioQuery.getRemark()));

            this.generateQuotaChange(dto, getAdminId(), getAdminName(), CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode());
        });

//
//        quotaMapper.updateRate(quota.getId(), ratioQuery.getNewCustomerRate(), ratioQuery.getOldCustomerRate());
//        boolean isNewChange = quota.getNewCustomerRate().compareTo(ratioQuery.getNewCustomerRate()) != 0;
//        boolean isOldChange = quota.getNewCustomerRate().compareTo(ratioQuery.getNewCustomerRate()) != 0;
//        if (isNewChange || isOldChange) {
//            CategoryQuotaDTO dto = getQuotaChange(quota, ratioQuery.getRemark());
//            // 如果费比变动新增费比设置明细
//            if (isNewChange) {
//                dto.setType(CouponEnum.CouponQuotaChangeType.NEW_CUSTOMER_RATIO.getCode());
//                dto.setQuota(ratioQuery.getNewCustomerRate());
//                generateQuotaChange(dto, getAdminId(), getAdminName(), CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode());
//            }
//            if (isOldChange) {
//                dto.setType(CouponEnum.CouponQuotaChangeType.OLD_CUSTOMER_RATIO.getCode());
//                dto.setQuota(ratioQuery.getOldCustomerRate());
//                generateQuotaChange(dto, getAdminId(), getAdminName(), CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode());
//            }
//        }
        return CommonResult.ok();
    }

    private void generateQuotaChange(CategoryQuotaDTO quota, Integer creator, String creatorName, Integer quotaType) {
        CategoryCouponQuotaChange quotaChange = new CategoryCouponQuotaChange();
        quotaChange.setAdminId(quota.getAdminId())
                .setAdminName(quota.getAdminName())
                .setQuota(quota.getQuota())
                .setType(quota.getType())
                .setQuotaType(quotaType)
                .setCreator(creator).setCreatorName(creatorName).setRemark(quota.getRemark());
        quotaChangeMapper.insertSelective(quotaChange);
    }

    private CategoryQuotaDTO getQuotaChange(CategoryCouponQuota quota, String remark) {
        CategoryQuotaDTO dto = new CategoryQuotaDTO();
        dto.setAdminId(quota.getAdminId());
        dto.setAdminName(quota.getAdminName());
        dto.setQuotaType(CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode());
        dto.setRemark(remark);
        return dto;
    }
}
