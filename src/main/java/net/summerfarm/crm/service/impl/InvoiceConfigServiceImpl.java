package net.summerfarm.crm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.enums.DefaultInvoiceConfigEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.InvoiceConfigMapper;
import net.summerfarm.crm.mapper.manage.InvoiceMerchantRelationMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.mapper.repository.MerchantExtendsRepository;
import net.summerfarm.crm.model.domain.InvoiceConfig;
import net.summerfarm.crm.model.domain.InvoiceEmailOverride;
import net.summerfarm.crm.model.domain.InvoiceMerchantRelation;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.crm.model.query.InvoiceConfigListQuery;
import net.summerfarm.crm.model.vo.InvoiceConfigVo;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.service.InvoiceConfigService;
import net.summerfarm.crm.service.InvoiceEmailOverrideService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/7/20 16:09
 */
@Slf4j
@Service
public class InvoiceConfigServiceImpl  extends BaseService implements InvoiceConfigService{
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    InvoiceConfigMapper invoiceConfigMapper;
    @Resource
    InvoiceMerchantRelationMapper relationMapper;
    @Resource
    MerchantQueryFacade merchantQueryFacade;
    @Resource
    MerchantExtendsRepository merchantExtendsRepository;
    @Resource
    private InvoiceEmailOverrideService invoiceEmailOverrideService;


    @Override
    public CommonResult<List<InvoiceConfigVo>> listInvoiceConfig(InvoiceConfigListQuery query) {
        MerchantStoreAndExtendResp merchantExtend = merchantQueryFacade.getAuthMerchantExtends(query.getMId(), getCompatibleDataPermission());
        if (merchantExtend == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "门店不存在/没有区域权限，请确认后重试");
        }
        Integer adminId = merchantExtend.getAdminId() == null ? null : merchantExtend.getAdminId().intValue();
        return CommonResult.ok(invoiceConfigMapper.selectByType(adminId,merchantExtend.getMId(),null,null));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> updateInvoiceConfig(InvoiceConfig invoiceConfig) {
        MerchantStoreAndExtendResp merchantExtends = merchantQueryFacade.getMerchantExtendsByMid(invoiceConfig.getMerchantId());
        // 大客户邮箱支持门店维度重写一份 开票使用重写邮箱接收
        if (ObjectUtil.equal(merchantExtends.getSize(), RegionalOrganizationEnums.Size.ADMIN.getCode())) {
            InvoiceConfig existedConfig = invoiceConfigMapper.selectByPrimaryKey(invoiceConfig.getId());
            String updateCompanyEmail = invoiceConfig.getCompanyEmail();
            String existedCompanyEmail = existedConfig.getCompanyEmail();
            if (!Objects.equals(updateCompanyEmail, existedCompanyEmail)) {
                // 写入自定义邮箱 不使用大客户的邮箱进行接收发票
                InvoiceEmailOverride invoiceEmailOverride = buildInvoiceEmailOverride(invoiceConfig);
                invoiceEmailOverrideService.insertOrUpdateByUK(invoiceEmailOverride);
                log.info("{}重写了大客户门店下的自定义邮箱:{}, 原信息:{}", getAdminName(), updateCompanyEmail, existedConfig);
                invoiceConfig.setCompanyEmail(null);
            }
        }
        if (ObjectUtil.equal(invoiceConfig.getDefaultFlag(), DefaultInvoiceConfigEnum.DEFAULT_CONFIG.getCode())) {
            if (ObjectUtil.equal(merchantExtends.getSize(), RegionalOrganizationEnums.Size.ADMIN.getCode())) {
                // 取消关联关系
                relationMapper.deleteByInvoiceIdAndMerchantId(invoiceConfig.getMerchantId());
                // 新增关联关系
                relationMapper.insert(invoiceConfig.getId(),invoiceConfig.getMerchantId());
                invoiceConfig.setDefaultFlag(null);
            } else {
                // 重置门店默认抬头标签
                invoiceConfigMapper.resetDefaultFlag(invoiceConfig.getMerchantId());
            }
        }
        //修改抬头信息
        invoiceConfigMapper.updateByPrimaryKeySelective(invoiceConfig);
        return CommonResult.ok();
    }

    private InvoiceEmailOverride buildInvoiceEmailOverride(InvoiceConfig invoiceConfig) {
        InvoiceEmailOverride invoiceEmailOverride = new InvoiceEmailOverride();
        invoiceEmailOverride.setInvoiceConfigId(invoiceConfig.getId());
        invoiceEmailOverride.setMId(invoiceConfig.getMerchantId());
        invoiceEmailOverride.setEmail(invoiceConfig.getCompanyEmail());
        invoiceEmailOverride.setCreator(getAdminName());
        return invoiceEmailOverride;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> insertInvoiceConfig(InvoiceConfig invoiceConfig) {
        MerchantStoreAndExtendDTO merchantExtend = merchantExtendsRepository.getAuthMerchantExtendDTO(invoiceConfig.getMerchantId(), getCompatibleDataPermission());
        invoiceConfig.setCreator(getAdminName());
        List<InvoiceConfigVo> configList = invoiceConfigMapper.selectByType(null, invoiceConfig.getMerchantId(),invoiceConfig.getInvoiceTitle(),invoiceConfig.getTaxNumber());
        boolean isDefault = ObjectUtil.equal(invoiceConfig.getDefaultFlag(), DefaultInvoiceConfigEnum.DEFAULT_CONFIG.getCode());
        // 新增单店抬头
        if (ObjectUtil.equals(merchantExtend.getSize(), RegionalOrganizationEnums.Size.MERCHANT.getCode())) {
            invoiceConfig.setType(0);
            if (!configList.isEmpty()) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "门店抬头已存在，请确认后重试");
            }
            if (isDefault) {
                // 重置门店默认抬头标签
                invoiceConfigMapper.resetDefaultFlag(invoiceConfig.getMerchantId());
            }
            invoiceConfigMapper.insertSelective(invoiceConfig);
        } else {
            if (merchantExtend.getAdminId() == null) {
                return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "大客户门店 id 不正确");
            }
            invoiceConfig.setAdminId(merchantExtend.getAdminId().intValue());
            // 门店抬头不存在，新增门店抬头
            if (configList.isEmpty()){
                invoiceConfig.setType(0);
                invoiceConfig.setDefaultFlag(DefaultInvoiceConfigEnum.NOT_DEFAULT.getCode());
                invoiceConfigMapper.insertSelective(invoiceConfig);
            }

            // 先查询该工商信息品牌是否已经拥有
            List<InvoiceConfigVo> existConfig = invoiceConfigMapper.selectByType(invoiceConfig.getAdminId(),null, invoiceConfig.getInvoiceTitle(), invoiceConfig.getTaxNumber());
            // 品牌不存在添加品牌抬头
            if (existConfig.isEmpty()) {
                invoiceConfig.setType(1);
                invoiceConfig.setDefaultFlag(DefaultInvoiceConfigEnum.NOT_DEFAULT.getCode());
                invoiceConfigMapper.insertSelective(invoiceConfig);
            }
            Long id = !existConfig.isEmpty() ? existConfig.get(0).getId() : invoiceConfig.getId();
            if (isDefault) {
                if (ObjectUtil.equal(merchantExtend.getDirect(), 1)) {
                    InvoiceMerchantRelation relation = relationMapper.selectByMerchantId(invoiceConfig.getMerchantId());
                    if (relation != null) {
                        throw new BizException("账期大客户不支持换绑抬头");
                    }
                }
                // 取消关联关系
                relationMapper.deleteByInvoiceIdAndMerchantId(invoiceConfig.getMerchantId());
                // 新增关联关系
                relationMapper.insert(id, invoiceConfig.getMerchantId());
            }
        }
        return CommonResult.ok();
    }
}
