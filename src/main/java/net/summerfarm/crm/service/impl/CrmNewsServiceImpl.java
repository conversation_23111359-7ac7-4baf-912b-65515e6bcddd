package net.summerfarm.crm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;

import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.CrmNewsTypeEnum;
import net.summerfarm.crm.mapper.manage.CrmNewsMapper;
import net.summerfarm.crm.model.domain.CrmNews;
import net.summerfarm.crm.model.dto.ProductNewsDTO;
import net.summerfarm.crm.model.query.CrmNewsQuery;
import net.summerfarm.crm.model.vo.CrmNewsVO;
import net.summerfarm.crm.service.CrmNewsService;
import net.xianmu.common.result.CommonResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
public class CrmNewsServiceImpl extends BaseService implements CrmNewsService {

    @Resource
    private CrmNewsMapper crmNewsMapper;
    @Resource
    private Executor asyncServiceExecutor;


    @Override
    public void insert(CrmNews crmNews) {
        if (crmNews != null){
            Integer adminId = super.getAdminId();
            crmNews.setCreator(adminId);
            crmNews.setUpdater(adminId);
            crmNewsMapper.insert(crmNews);
        }
    }

    @Override
    public void update(Integer adminId) {
        if (adminId != null){
            crmNewsMapper.update(adminId);
        }
    }

    @Override
    public CommonResult<PageInfo<CrmNewsVO>> selectList(CrmNewsQuery crmNewsQuery) {
        Integer adminId = super.getAdminId();
        if(Objects.nonNull(crmNewsQuery) && Objects.nonNull(crmNewsQuery.getAdminId())){
            adminId = crmNewsQuery.getAdminId();
        }
        PageHelper.startPage(crmNewsQuery.getPageIndex(),crmNewsQuery.getPageSize());
        List<CrmNewsVO> crmNewsList = crmNewsMapper.selectList(adminId, crmNewsQuery.getType());
        for (CrmNewsVO crmNew : crmNewsList) {
            List<String> list = new ArrayList<>();
            if(Objects.equals(CrmNewsTypeEnum.SHORTAGE.getType(),crmNew.getType())){
                list = JSONObject.parseObject(crmNew.getContent(), List.class);
            }else if(Objects.equals(CrmNewsTypeEnum.PRODUCT.getType(),crmNew.getType())){
                ProductNewsDTO productNewsDTO = JSONObject.parseObject(crmNew.getContent(), ProductNewsDTO.class);
                crmNew.setProductNewsDTO(productNewsDTO);
            } else {
                list.add(crmNew.getContent());
            }
            crmNew.setNameList(list);
        }
        PageInfo<CrmNewsVO> pageInfo = PageInfoHelper.createPageInfo(crmNewsList);
        return CommonResult.ok(pageInfo);
    }

    @Override
    public Integer selectIsRead(Integer adminId,Integer position) {
        List<CrmNewsVO> crmNewsList = crmNewsMapper.selectList(adminId, null);
        Integer num = NumberUtils.INTEGER_ZERO;
        if (!CollectionUtils.isEmpty(crmNewsList)){
            List<CrmNews> unReadList = crmNewsList.stream().filter(e -> e.getStatus() == 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(unReadList)){
                // 异步执行更新消息状态 第二次打开页面红点不展示
                if (position == 1){
                    asyncServiceExecutor.execute(() ->{
                        crmNewsMapper.update(adminId);
                    });
                }
                num = unReadList.size();
            }
        }
        return num;
    }
}
