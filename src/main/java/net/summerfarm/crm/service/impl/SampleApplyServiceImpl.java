package net.summerfarm.crm.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.SnowflakeUtil;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.ResultConstant;

import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.constant.CommonRedisKey;
import net.summerfarm.crm.enums.*;
import net.summerfarm.crm.facade.*;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.repository.AdminAuthExtendRepository;
import net.summerfarm.crm.mapper.repository.MerchantExtendsRepository;
import net.summerfarm.crm.model.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.crm.model.convert.DeliverPlanRemarkConverter;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.crm.model.dto.SampleBatchApplyImportDTO;
import net.summerfarm.crm.model.input.samples.SampleApplyBatchInsertInput;
import net.summerfarm.crm.model.query.SkuMerchantQuery;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.constant.MessageKeyEnum;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.util.Producer;
import net.summerfarm.crm.service.DingTalkMsgSender;
import net.summerfarm.crm.service.SampleApplyReviewService;
import net.summerfarm.crm.service.SampleApplyService;
import net.summerfarm.mall.client.resp.AutoCreateSampleResp;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Get;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

/**
 * <AUTHOR> ct
 * create at:  2020/4/26  15:46
 */
@Service
@Slf4j
public class SampleApplyServiceImpl extends BaseService implements SampleApplyService {
    @Resource
    SampleSkuMapper sampleSkuMapper;
    @Resource
    SampleApplyMapper sampleApplyMapper;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    private Producer producer;
    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    DeliveryPlanRemarkSnapshotMapper deliveryPlanRemarkSnapshotMapper;
    @Resource
    ContactMapper contactMapper;
    @Resource
    MerchantExtendsRepository merchantExtendsRepository;
    @Resource
    private OfcSampleApplyQueryFacade sampleApplyQueryFacade;
    @NacosValue(value = "${riskAdminds:}",autoRefreshed = true)
    private List<Integer> riskAdminIds;
    @Resource
    private ProductsQueryFacade productsQueryFacade;
    @Resource
    private WncQueryFacade wncQueryFacade;
    @Resource
    private GoodsCenterQueryFacade goodsCenterQueryFacade;
    @Resource
    private SellingEntityQueryFacade sellingEntityQueryFacade;
    @Resource
    private OrderQueryFacade orderQueryFacade;

    @Resource
    private SampleApplyReviewService sampleApplyReviewService;

    @NacosValue(value = "${batchCreateSampleMaxUploadNum:300}",autoRefreshed = true)
    private Integer maxUploadNum;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    @Lazy
    private SampleApplyService mySelfService;

    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;

    private static final int MAX_SIZE = 50;
    private static final String BATCH_FAIL_PRE = "batch_fail_sample_apply";
    public static final Long XIANMU_TENANT_ID = 1L;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertSampleApply(SampleApplyVO samleApplyVO) {
        List<SampleSku> sampleSkuList = samleApplyVO.getSampleSkuList();
        List<String> skus = sampleSkuList.stream().map(SampleSku::getSku).distinct().collect(Collectors.toList());
        Map<String, Integer> skuSubTypeMap = goodsCenterQueryFacade.getSkuSubTypeMap(skus);
        // 判断skuSubTypeMap是否存在代销不入仓的品
        if (CollectionUtils.isNotEmpty(skuSubTypeMap.values()) && skuSubTypeMap.values().stream().anyMatch(SkuSubTypeEnum::isFullCategorySku)) {
            return AjaxResult.getError("暂不支持申请代销不入仓的样品单，请重新选择");
        }
        // 销售主体名称
        String sellingEntityName = sellingEntityQueryFacade.querySellingEntityByMId(samleApplyVO.getmId());

        MerchantStoreAndExtendDTO merchantExtendDTO = merchantExtendsRepository.getAuthMerchantExtendDTO(samleApplyVO.getmId(), null);
        // 样品申请数据同步至数据库
        samleApplyVO.setAddTime(new Date());
        samleApplyVO.setUpdateTime(new Date());
        samleApplyVO.setStatus(VisitPlanEnum.Status.UN_HANDLE.ordinal());
        samleApplyVO.setBdName(getAdminName());
        samleApplyVO.setBdId(getAdminId());
        samleApplyVO.setPurchaseIntention(VisitPlanEnum.PurchaseIntention.NOT_EVALUATED.ordinal());
        samleApplyVO.setSatisfaction(VisitPlanEnum.Satisfaction.NOT_EVALUATED.ordinal());
        samleApplyVO.setGrade(merchantExtendDTO.getGrade());
        samleApplyVO.setCreateId(getAdminId());
        samleApplyVO.setCreateName(getAdminName());
        samleApplyVO.setmSize(MERCHANT.getCode().equals(merchantExtendDTO.getSize())? MERCHANT.getDesc():ADMIN.getDesc());
        samleApplyVO.setmName(merchantExtendDTO.getStoreName());
        samleApplyVO.setSellingEntityName(sellingEntityName);
        sampleApplyMapper.insertSampleApply(samleApplyVO);


        SkuMerchantQuery skuMerchantQuery = new SkuMerchantQuery();
        skuMerchantQuery.setSkuList(skus);
        //根据sku去查询变成map
        Map<String, MarketItemInfoResp> skuPdNameMap = productsQueryFacade.queryMarketListBySkus(skus)
                .stream().collect(Collectors.toMap(MarketItemInfoResp::getItemCode, Function.identity(), (v1, v2) -> v1));

        // 样品申请 商品信息同步至数据库
        sampleSkuList.forEach(sampleSku -> {
            sampleSku.setSampleId(samleApplyVO.getSampleId());
            MarketItemInfoResp marketItemInfoResp = skuPdNameMap.get(sampleSku.getSku());
            if (marketItemInfoResp == null) {
                return;
            }
            sampleSku.setPdName(marketItemInfoResp.getMarketTitle());
            sampleSku.setWeight(sampleSku.getWeight() == null ? marketItemInfoResp.getSpecification() : sampleSku.getWeight());
        });
        Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(samleApplyVO.getContactId()));
        addSampleAppySnapshot(contact, samleApplyVO);
        sampleSkuMapper.insertSampleSku(sampleSkuList);



        return AjaxResult.getOK(samleApplyVO.getSampleId());
    }
    public void addSampleAppySnapshot(Contact contact, SampleApplyVO samleApplyVO) {
        DeliveryPlanRemarkSnapshot deliveryPlanRemarkSnapshot = DeliverPlanRemarkConverter.toDeliveryPlanRemarkSnapshot(contact);
        deliveryPlanRemarkSnapshot.setBusinessId(samleApplyVO.getSampleId().toString());
        deliveryPlanRemarkSnapshot.setType(AddressSnapshotTypeEnum.SnapshotType.SAMPLE_APPLY.ordinal());
        deliveryPlanRemarkSnapshot.setCreateTime(new Date());
        deliveryPlanRemarkSnapshot.setUpdateTime(new Date());
        deliveryPlanRemarkSnapshotMapper.insertSelective(deliveryPlanRemarkSnapshot);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cancelSampleApply(int sampleId) {
        // 参数校验
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleId);
        List<SampleSku> sampleSkuList = sampleSkuMapper.selectBySampleId(sampleId);
        if (CollectionUtils.isEmpty(sampleSkuList) || Objects.isNull(sampleApply)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        if(Objects.equals(SampleApplyStatusEnum.CANCEL.getId(),sampleApply.getStatus())){
            return AjaxResult.getErrorWithMsg("该申请单已取消,请勿重复操作");
        }
        // 过配送日期前一天20：15无法取消 -- 修改成wnc获取截单时间进行校验
        if(Objects.nonNull(sampleApply.getDeliveryTime()) && Objects.nonNull(sampleApply.getContactId())){
            boolean noCancel = LocalDate.now().isAfter(sampleApply.getDeliveryTime().minusDays(NumberUtils.INTEGER_ONE));
            if (noCancel) {
                return AjaxResult.getErrorWithMsg("样品出库任务已生成无法取消");
            }
            LocalTime closeTime = wncQueryFacade.queryCloseTime(sampleApply.getContactId().longValue(), SourceEnum.XM_SAMPLE_APPLY);
            if (Objects.isNull(closeTime)) {
                return AjaxResult.getErrorWithMsg("当前地区截单时间为空！");
            }
            LocalDateTime closeDate = sampleApply.getDeliveryTime().minusDays(1).atTime(closeTime);
            if (LocalDateTime.now().isAfter(closeDate.minusMinutes(2))) {
                return AjaxResult.getErrorWithMsg("样品出库任务已生成无法取消，需在当天" + closeTime.minusMinutes(2) + "前取消！");
            }
        }

        //自提样品单不支持取消
        if (sampleApplyQueryFacade.hasSampleApplySelfPicked(sampleId)){
            return AjaxResult.getErrorWithMsg("样品单已经自提，无法取消");
        }

        // 审核中状态直接取消
        if (Objects.equals(SampleApplyStatusEnum.REVIEWING.getId(),sampleApply.getStatus())) {
            sampleApplyMapper.cancelSampleApply(sampleId);
            return AjaxResult.getOK();
        }

        // 待反馈状态需要恢复库存信息
        if (Objects.equals(SampleApplyStatusEnum.WAIT_HANDLE.getId(),sampleApply.getStatus())) {
            // 消息机制 恢复库存信息
            this.unFrozenInventory(sampleId,sampleApply.getContactId());
        }
        return AjaxResult.getOK();
    }

    private void unFrozenInventory(Integer sampleId,Integer contactId) {
        MqData mqData = new MqData();
        mqData.setType(MessageType.UNFREEZE_INVENTORY);
        mqData.setBusiness(MessageBusiness.STOCK);
        JSONObject msgJson = new JSONObject();
        msgJson.put("sampleId",sampleId);
        msgJson.put("contactId",contactId);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendMessageInTransaction(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST, mqData);
    }

    @Override
    public AjaxResult unFrozenInventory(JSONObject jsonObject) {
        logger.info("开始执行解冻库存本地事务,message:{}", jsonObject.toJSONString());
        JSONObject data = jsonObject.getJSONObject("data");
        Integer sampleId = data.getInteger("sampleId");
        sampleApplyMapper.cancelSampleApply(sampleId);
        // 判断是否已审核，已审核则还原库存，未审核不需要还原
        SampleApplyReview isReview = sampleApplyReviewMapper.isReview(sampleId, SampleApplyStatusEnum.WAIT_HANDLE.getId());
        if(Objects.nonNull(isReview)){
            return AjaxResult.getOK();
        }
        return AjaxResult.getError();
    }

    @Override
    public AjaxResult updateSampleApply(SampleApply sampleApply) {
        SampleApply querySample = sampleApplyMapper.selectSampleById(sampleApply.getSampleId());
        if(Objects.isNull(querySample)){
            return AjaxResult.getErrorWithMsg("样品申请不存在");
        }
        if (!Objects.equals(querySample.getStatus(), 0)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT,"当前样品申请状态不可填写反馈");
        }
        sampleApply.setStatus(1);
        sampleApply.setUpdateTime(new Date());
        sampleApplyMapper.updateSampleApply(sampleApply);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectSampleApplyVO(int pageIndex, int pageSize, SampleApply sampleApply, String keyword) {
        //风控人员id
        Integer adminId = getAdminId();
        if (riskAdminIds.contains(adminId)){
            /*sampleApply.setBdId(null);*/
        }else {
            boolean isBd = isBD() && !(isSaleSA() || isAreaSA() || isSA());
            if(isBd){
                sampleApply.setBdId(getAdminId());
            }
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<SampleApplyVO> sampleApplyVOList = sampleApplyMapper.selectSampleApplies(sampleApply, keyword);
        for (SampleApplyVO sampleApplyVO : sampleApplyVOList) {
            Integer sampleId = sampleApplyVO.getSampleId();
            List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleId);
            sampleApplyVO.setSampleSkuList(sampleSkus);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(sampleApplyVOList));
    }

    @Override
    public void sendDingTalk(){
        LocalDateTime now = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime addTime = now.minusDays(4);
        LocalDateTime endTime = addTime.plusDays(1);
        // 需要发送消息的样品申请
        List<SampleApplyVO> sampleApplyVOList = sampleApplyMapper.selectByAddTime(addTime, endTime);
        for (SampleApplyVO sampleApplyVO : sampleApplyVOList) {
            Integer bdId = sampleApplyVO.getBdId();

            StringBuffer title = new StringBuffer(getAdminName());
            title.append("请")
                    .append(sampleApplyVO.getBdName())
                    .append("及时收集")
                    .append(sampleApplyVO.getSampleId())
                    .append("号样品申请的反馈意见");
            StringBuilder sb = new StringBuilder("#### " + title + "\n");
            sb.append("> ")
                    .append("###### ")
                    .append("详细说明:");
            List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleApplyVO.getSampleId());

            sampleSkus.forEach(sampleSku -> {
                StringJoiner joiner = new StringJoiner("-","","\n");
                joiner.add(sampleSku.getPdName())
                        .add(sampleSku.getWeight())
                        .add(String.valueOf(sampleSku.getAmount()));
                sb.append(joiner);
            });

            String grade = sampleApplyVO.getGrade() != null ? String.valueOf(sampleApplyVO.getGrade()) :"";
            StringJoiner joiner = new StringJoiner("-");
            joiner.add(sampleApplyVO.getmName())
                    .add(grade)
                    .add(sampleApplyVO.getmSize())
                    .add(sampleApplyVO.getAreaName());
            sb.append(joiner);

            logger.info("发送样品反馈消息至:{}",sampleApplyVO.getBdName());

            DingTalkMsgReceiverIdBO bo = new DingTalkMsgReceiverIdBO();
            bo.setReceiverIdList(Collections.singletonList(Long.valueOf(bdId)));
            bo.setText(sb.toString());
            bo.setMsgType(DingTalkMsgEnum.MARKDOWN.getType());
            bo.setTitle(title.toString());
            dingTalkMsgSender.sendMessageWithFeiShu(bo);
        }
    }

    @Override
    @XmLock(prefixKey = CommonRedisKey.Lock.BATCH_SAMPLE_APPLY_INSERT, key = "{input.adminId}")
    public SampleApplyBatchInsertResp batchInsertSampleApply(SampleApplyBatchInsertInput input) {
        List<Long> mIds = new ArrayList<>();
        String sku = input.getSku();
        if (!StringUtils.isEmpty(input.getKey())) {
            //解析文件数据
            InputStream inputStream = OssGetUtil.getInputStream(input.getKey());
            List<SampleBatchApplyImportDTO> sampleBatchApplyImportDTOS;
            try {
                sampleBatchApplyImportDTOS = EasyExcel.read(inputStream,
                        SampleBatchApplyImportDTO.class, null).doReadAllSync();
            } catch (Exception e) {
                throw new BizException("上传客户模版解析异常！");
            }

            mIds = sampleBatchApplyImportDTOS.stream().map(SampleBatchApplyImportDTO::getMId).collect(Collectors.toList());
        } else if (input.getMId() != null) {
            mIds.add(input.getMId());
        }

        if (CollectionUtils.isEmpty(mIds)) {
            throw new BizException("创建失败，客户信息为空！");
        }
        if (mIds.size() >= maxUploadNum) {
            throw new BizException("创建失败，客户数量不能超过" + maxUploadNum + "条！");
        }

        Map<String, Integer> skuSubTypeMap = goodsCenterQueryFacade.getSkuSubTypeMap(Collections.singletonList(input.getSku()));

        // 判断skuSubTypeMap是否存在代销不入仓的品
        if (CollectionUtils.isNotEmpty(skuSubTypeMap.values()) && skuSubTypeMap.values().stream().anyMatch(SkuSubTypeEnum::isFullCategorySku)) {
            throw new BizException("暂不支持申请代销不入仓的样品单，请重新选择");
        }

        List<MarketItemInfoResp> marketItemInfoResps = productsQueryFacade.queryMarketListBySkus(Collections.singletonList(sku));
        if (CollectionUtils.isEmpty(marketItemInfoResps) || marketItemInfoResps.get(0).getSamplePool() != 1) {
            throw new BizException("当前商品不在样品池，请重新选择");
        }

        //已注销或者拉黑则剔除对应门店
        List<MerchantVO> merchantVOS = merchantMapper.selectByMids(mIds);
        List<MerchantVO> merchantVOList = merchantVOS.stream().filter(merchantVO -> merchantVO.getIslock() != null &&
                merchantVO.getIslock() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(merchantVOList)) {
            throw new BizException("有效客户数量为空（暂不支持申请已注销或者拉黑客户），请重新选择");
        }
        Map<Long, MerchantVO> merchantVOMap = merchantVOList.stream().collect(Collectors.
                toMap(MerchantVO::getmId, merchantVO -> merchantVO, (v1, v2) -> v1));

        //过滤已经申请过的mid信息
        Set<Long> sampleApplyMidList = sampleApplyMapper.getSampleInfoBySku(mIds, sku);
        log.info("过滤已经申请过的客户信息, sampleApplyMidList:{}", JSON.toJSONString(sampleApplyMidList));
        Set<Long> mIdSet = mIds.stream().filter(mId -> !sampleApplyMidList.contains(mId)).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mIdSet)) {
            throw new BizException("有效客户数量为空（暂不支持重复申请的客户），请重新选择");
        }

        List<List<Long>> partitionList = Lists.partition(new ArrayList<>(mIdSet), MAX_SIZE);
        List<AutoCreateSampleResp> allAutoCreateSampleResps = new ArrayList<>();
        for (List<Long> subList : partitionList) {
            //配送地址：取待配送订单相同地址，若有多条，则取创建时间最早的地址
            List<AutoCreateSampleResp> autoCreateSampleResps = orderQueryFacade.queryOrderInfoByAutoCreateSampleTask(new HashSet<>(subList));
            if (CollectionUtils.isEmpty(autoCreateSampleResps)) {
                log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]autoCreateSampleResps is empty! subList:{}", JSON.toJSONString(subList));
                continue;
            }
            allAutoCreateSampleResps.addAll(autoCreateSampleResps);
        }
        Map<Long, AutoCreateSampleResp> autoCreateSampleRespMap = allAutoCreateSampleResps.stream().
                collect(Collectors.toMap(AutoCreateSampleResp::getMId, Function.identity(), (v1, v2) -> v1));

        List<SampleApply> sampleApplyList = new ArrayList<>();
        List<Integer> sampleApplyIds = new ArrayList<>();
        List<Long> contactIds = new ArrayList<>();
        SampleApplyBatchInsertResp sampleApplyBatchInsertResp = new SampleApplyBatchInsertResp();
        List<SampleApplyBatchCreateFailExport> sampleApplyBatchCreateFailExports = new ArrayList<>();
        mIds.forEach(mId -> {
            MerchantVO merchantVO = merchantVOMap.get(mId);
            if (merchantVO == null) {
                log.info("找不到客户信息:{}", mId);
                SampleApplyBatchCreateFailExport sampleApplyBatchCreateFailExport = new SampleApplyBatchCreateFailExport();
                sampleApplyBatchCreateFailExport.setMId(mId);
                sampleApplyBatchCreateFailExport.setFailReason("客户信息不存在或已拉黑");
                sampleApplyBatchCreateFailExports.add(sampleApplyBatchCreateFailExport);
                return;
            }
            if (sampleApplyMidList.contains(mId)) {
                SampleApplyBatchCreateFailExport sampleApplyBatchCreateFailExport = new SampleApplyBatchCreateFailExport();
                sampleApplyBatchCreateFailExport.setMId(mId);
                sampleApplyBatchCreateFailExport.setFailReason("客户已经申请过当前样品");
                sampleApplyBatchCreateFailExports.add(sampleApplyBatchCreateFailExport);
                return;
            }
            AutoCreateSampleResp autoCreateSampleResp = autoCreateSampleRespMap.get(mId);
            if (autoCreateSampleResp == null) {
                log.info("找不到配送信息:{}", mId);
                SampleApplyBatchCreateFailExport sampleApplyBatchCreateFailExport = new SampleApplyBatchCreateFailExport();
                sampleApplyBatchCreateFailExport.setMId(mId);
                sampleApplyBatchCreateFailExport.setFailReason("客户不存在t+1待配送计划");
                sampleApplyBatchCreateFailExports.add(sampleApplyBatchCreateFailExport);
                return;
            }

            // 销售主体名称
            String sellingEntityName = sellingEntityQueryFacade.querySellingEntityByMId(mId);

            //组装样品申请记录信息
            SampleApply sampleApply = new SampleApply();
            sampleApply.setAddTime(new Date());
            sampleApply.setUpdateTime(new Date());
            sampleApply.setStatus(SampleApplyStatusEnum.REVIEWING.getId());
            sampleApply.setBdName(getAdminName());
            sampleApply.setBdId(getAdminId());
            sampleApply.setPurchaseIntention(VisitPlanEnum.PurchaseIntention.NOT_EVALUATED.ordinal());
            sampleApply.setSatisfaction(VisitPlanEnum.Satisfaction.NOT_EVALUATED.ordinal());
            sampleApply.setGrade(merchantVO.getGrade());
            sampleApply.setCreateId(getAdminId());
            sampleApply.setCreateName("系统默认");
            sampleApply.setmSize(merchantVO.getSize());
            sampleApply.setmName(merchantVO.getMname());
            sampleApply.setSellingEntityName(sellingEntityName);
            sampleApply.setAreaNo(merchantVO.getAreaNo());
            sampleApply.setmId(mId);
            sampleApply.setContactId(autoCreateSampleResp.getContactId().intValue());
            sampleApply.setmContact(autoCreateSampleResp.getMContact());
            sampleApply.setmPhone(autoCreateSampleResp.getMPhone());
            sampleApply.setStoreNo(autoCreateSampleResp.getStoreNo());
            sampleApply.setDeliveryTime(autoCreateSampleResp.getDeliveryDate());
            sampleApply.setRemark(org.apache.commons.lang3.StringUtils.isBlank(input.getRemark()) ? "后台批量-自动创建样品单" : input.getRemark());
            sampleApplyList.add(sampleApply);
            contactIds.add(autoCreateSampleResp.getContactId());
        });

        String exportId = null;
        if (CollectionUtils.isNotEmpty(sampleApplyBatchCreateFailExports)) {
            exportId = String.valueOf(SnowflakeUtil.nextId());
            redisTemplate.opsForHash().put(BATCH_FAIL_PRE, exportId, JSONObject.toJSONString(sampleApplyBatchCreateFailExports));
            redisTemplate.expire(BATCH_FAIL_PRE, 300L, TimeUnit.SECONDS);
        }

        sampleApplyBatchInsertResp.setExportId(exportId);
        sampleApplyBatchInsertResp.setFailCount(sampleApplyBatchCreateFailExports.size());
        sampleApplyBatchInsertResp.setSuccessCount(sampleApplyList.size());
        if (CollectionUtils.isEmpty(sampleApplyList)) {
            return sampleApplyBatchInsertResp;
        }

        //保存样品单数据落库
        mySelfService.saveSampleApplyInfo(input, sampleApplyList, sampleApplyIds, contactIds);

        //异步审核
        MqData mqData = new MqData();
        mqData.setType(MessageType.AUTO_REVIEW_SAMPLE_APPLY);
        mqData.setBusiness(MessageBusiness.SAMPLE);
        JSONObject msgJson = new JSONObject();
        msgJson.put("sampleApplyIds", sampleApplyIds);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendDataToQueue(RocketMqMessageConstant.CRM_LIST, MessageKeyEnum.AUTO_REVIEW_SAMPLE_APPLY, JSON.toJSONString(mqData));
        return sampleApplyBatchInsertResp;
    }

    @Override
    public Boolean exportSampleApplyFailData(String exportId) {
        if (StringUtils.isEmpty(exportId)) {
            return Boolean.FALSE;
        }
        String jsonString = (String) redisTemplate.opsForHash().get(BATCH_FAIL_PRE, exportId);
        if (StringUtils.isEmpty(jsonString)) {
            throw new BizException("导出失败数据不存在");
        }
        List<SampleApplyBatchCreateFailExport> failList = null;
        try {
            failList = JSONArray.parseArray(jsonString, SampleApplyBatchCreateFailExport.class);
        } catch (Exception e) {
            throw new BizException("数据解析异常");
        }

        String fileName = "批量创建客户失败数据" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xlsx";
        DownloadCenterInitReq initReq = new DownloadCenterInitReq();
        initReq.setBizId(UUID.fastUUID().toString());
        initReq.setUserId(getAdminId().longValue());
        initReq.setFileName(fileName);
        initReq.setBizType(DownloadBizTypeEnum.BATCH_SAMPLE_APPLY_FAIL_DATA.getBizType());
        initReq.setFileExpiredDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY);
        initReq.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        initReq.setTenantId(XIANMU_TENANT_ID);
        DubboResponse<DownloadCenterResp> dubboResponse = downloadCenterProvider.initRecord(
                initReq);
        if (!dubboResponse.isSuccess() || dubboResponse.getData() == null) {
            throw new BizException("下载失败，请重新下载");
        }
        Long resId = dubboResponse.getData().getResId();

        //异步处理并上传至oss
        mySelfService.asyncDownload(exportId, fileName, resId, failList);
        return Boolean.TRUE;
    }

    @Override
    @Async("asycExecutor")
    public void asyncDownload(String exportId, String fileName, Long resId, List<SampleApplyBatchCreateFailExport> failList) {
        log.info("开始处理批量导出客户失败数据, resId:{}", resId);
        DownloadCenterUploadReq uploadReq = new DownloadCenterUploadReq();
        uploadReq.setResId(resId);
        File file = new File(fileName);

        try {
            ExcelWriter excelWriter = EasyExcel.write(file, SampleApplyBatchCreateFailExport.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("批量创建样品单失败客户数据").build();

            excelWriter.write(failList, writeSheet);
            excelWriter.finish();

            //获取filepath
            OssUploadResult upload = OssUploadUtil.upload(fileName, file, OSSExpiredLabelEnum.THREE_DAY);
            uploadReq.setStatus(DownloadCenterEnum.Status.UPLOADED);
            uploadReq.setFilePath(upload.getObjectOssKey());
            downloadCenterProvider.uploadFile(uploadReq);
            log.info("开始处理批量导出客户失败数据-处理成功, resId:{}", resId);
        } catch (Exception e) {
            log.error("开始处理批量导出客户失败数据-处理失败, resId:{}", resId, e);
            uploadReq.setStatus(DownloadCenterEnum.Status.FAILED);
            downloadCenterProvider.uploadFile(uploadReq);
        } finally {
            //删除临时文件
            if (file.exists()) {
                file.delete();
            }
        }
        log.info("导出批量创建样品单失败数据结束, fileName:{}, resId:{}", fileName, resId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSampleApplyInfo(SampleApplyBatchInsertInput input, List<SampleApply> sampleApplyList, List<Integer> sampleApplyIds, List<Long> contactIds) {
        sampleApplyMapper.batchInsert(sampleApplyList);

        List<SampleSku> sampleSkuList = new ArrayList<>();
        List<DeliveryPlanRemarkSnapshot> deliveryPlanRemarkSnapshotList = new ArrayList<>();
        List<Contact> contacts = contactMapper.selectByPrimaryKeys(contactIds);
        Map<Long, Contact> contactMap = contacts.stream().collect(Collectors.toMap(Contact::getContactId,
                Function.identity(), (entity1, entity2) -> entity1));

        sampleApplyList.forEach(sampleApply -> {
            //组装样品sku信息
            SampleSku sampleSku = new SampleSku();
            sampleSku.setSampleId(sampleApply.getSampleId());
            sampleSku.setSku(input.getSku());
            sampleSku.setAmount(input.getAmount());
            sampleSku.setWeight(input.getWeight());
            sampleSku.setPdName(input.getPdName());
            sampleSkuList.add(sampleSku);
            sampleApplyIds.add(sampleApply.getSampleId());

            //组装配送地址快照信息
            Contact contact = contactMap.get(sampleApply.getContactId().longValue());
            if (contact == null) {
                return;
            }
            DeliveryPlanRemarkSnapshot deliveryPlanRemarkSnapshot = DeliverPlanRemarkConverter.toDeliveryPlanRemarkSnapshot(contact);
            deliveryPlanRemarkSnapshot.setBusinessId(sampleApply.getSampleId().toString());
            deliveryPlanRemarkSnapshot.setType(AddressSnapshotTypeEnum.SnapshotType.SAMPLE_APPLY.ordinal());
            deliveryPlanRemarkSnapshot.setCreateTime(new Date());
            deliveryPlanRemarkSnapshot.setUpdateTime(new Date());
            deliveryPlanRemarkSnapshotList.add(deliveryPlanRemarkSnapshot);
        });
        if (CollectionUtils.isNotEmpty(sampleSkuList)) {
            sampleSkuMapper.insertSampleSku(sampleSkuList);
        }
        if (CollectionUtils.isNotEmpty(deliveryPlanRemarkSnapshotList)) {
            deliveryPlanRemarkSnapshotMapper.batchInsert(deliveryPlanRemarkSnapshotList);
        }
    }

    @Override
    public void syncSampleApplyReview(List<Integer> sampleApplyIds) {
        for (Integer sampleId : sampleApplyIds) {
            try {
                log.info("批量创建样品单开始审核, sampleId:{}", sampleId);
                SampleApplyReview sampleApplyReview = new SampleApplyReview();
                sampleApplyReview.setSampleId(sampleId);
                sampleApplyReview.setReviewRemark("后台定时任务-自动审核样品单");
                sampleApplyReview.setStatus(ExamineEnum.SampleApplyReview.SUCCESS.ordinal());
                AjaxResult applyReview = sampleApplyReviewService.sampleApplyReview(sampleApplyReview);
                if (!applyReview.isSuccess()) {
                    log.warn("样品单自动审核失败! sampleApplyReview:{}, applyReview:{}", JSON.toJSONString(sampleApplyReview), applyReview.getMsg());
                    continue;
                }
            } catch (Exception e) {
                log.warn("批量创建样品单异步审核失败! sampleId:{}, cause:{}", sampleId, e);
            }
        }
    }

    @Override
    public SampleApplyUploadMerchantVO checkBatchInsertUploadInfo(SampleApplyBatchInsertInput input) {
        if (input == null || StringUtils.isEmpty(input.getKey())) {
            throw new BizException("上传文档key不能为空");
        }
        //解析文件数据
        InputStream inputStream = OssGetUtil.getInputStream(input.getKey());
        List<SampleBatchApplyImportDTO> sampleBatchApplyImportDTOS;
        try {
            sampleBatchApplyImportDTOS = EasyExcel.read(inputStream,
                    SampleBatchApplyImportDTO.class, null).doReadAllSync();
        } catch (Exception e) {
            throw new BizException("上传客户模版解析异常！");
        }

        Set<Long> mIds = sampleBatchApplyImportDTOS.stream().map(SampleBatchApplyImportDTO::getMId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(mIds)) {
            throw new BizException("上传客户模版数据为空！");
        }

        SampleApplyUploadMerchantVO sampleApplyUploadMerchantVO = new SampleApplyUploadMerchantVO();
        List<MerchantVO> merchantVOS = merchantMapper.selectByMids(new ArrayList<>(mIds));
        Map<Long, MerchantVO> merchantVOMap = merchantVOS.stream().collect(Collectors.toMap(MerchantVO::getmId, Function.identity(), (o1, o2) -> o1));

        int invalidCount = 0;
        int effectiveCount = 0;
        List<MerchantVO> effectiveMerchantVOS = new ArrayList<>();
        for (Long mId : mIds) {
            MerchantVO merchantVO = merchantVOMap.getOrDefault(mId, null);
            if (merchantVO == null || merchantVO.getIslock() != 0) {
                invalidCount++;
            } else {
                effectiveCount++;
                effectiveMerchantVOS.add(merchantVO);
            }
        }
        sampleApplyUploadMerchantVO.setInvalidCount(invalidCount);
        sampleApplyUploadMerchantVO.setEffectiveCount(effectiveCount);
        sampleApplyUploadMerchantVO.setEffectiveMerchantVOS(effectiveMerchantVOS);
        return sampleApplyUploadMerchantVO;
    }

    @Override
    public Boolean batchCancelSampleApply(SampleApplyBatchInsertInput input) {
        Set<Long> mIds = new HashSet<>();
        String sku = input.getSku();
        if (!StringUtils.isEmpty(input.getKey())) {
            //解析文件数据
            InputStream inputStream = OssGetUtil.getInputStream(input.getKey());
            List<SampleBatchApplyImportDTO> sampleBatchApplyImportDTOS;
            try {
                sampleBatchApplyImportDTOS = EasyExcel.read(inputStream,
                        SampleBatchApplyImportDTO.class, null).doReadAllSync();
            } catch (Exception e) {
                throw new BizException("上传客户模版解析异常！");
            }

            mIds = sampleBatchApplyImportDTOS.stream().map(SampleBatchApplyImportDTO::getMId).collect(Collectors.toSet());
        } else if (input.getMId() != null) {
            mIds.add(input.getMId());
        }

        if (CollectionUtils.isEmpty(mIds)) {
            throw new BizException("删除失败，客户信息为空！");
        }

        //审核中的样品单可以直接取消
        List<SampleApply> sampleApplyList = sampleApplyMapper.selectSampleInfoBySku(mIds, sku, SampleApplyStatusEnum.REVIEWING.getId());
        if (CollectionUtils.isEmpty(sampleApplyList)) {
            return Boolean.FALSE;
        }
        List<Integer> sampleIds = sampleApplyList.stream().map(SampleApply::getSampleId).collect(Collectors.toList());
        int i = sampleApplyMapper.batchCancelSampleApply(sampleIds);
        log.info("批量取消样品单成功！sampleIds:{}, updateCount", JSON.toJSONString(sampleIds), i);
        return i > 0;
    }
}
