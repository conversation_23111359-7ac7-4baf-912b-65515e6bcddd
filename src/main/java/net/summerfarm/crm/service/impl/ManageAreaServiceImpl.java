package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.facade.WncQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.CrmManageBd;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;
import net.summerfarm.crm.model.query.QueryManageAreaQuery;
import net.summerfarm.crm.model.query.SaveManageAreaQuery;
import net.summerfarm.crm.model.vo.ManageAreaVo;
import net.summerfarm.crm.service.ManageAreaService;
import net.summerfarm.wnc.client.resp.QueryAreaLegitimacyResp;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Service
public class ManageAreaServiceImpl extends BaseService implements ManageAreaService {

    @Resource
    CrmManageAreaMapper crmManageAreaMapper;
    @Resource
    CrmManageBdMapper crmManageBdMapper;
    @Resource
    CrmCommissionSkuMapper crmCommissionSkuMapper;

    @Resource
    CrmCommissionMerchantMapper crmCommissionMerchantMapper;

    @Resource
    CrmManageAdministrativeCityMapper crmManageAdministrativeCityMapper;

    @Resource
    private WncQueryFacade wncQueryFacade;



    /**
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param queryManageAreaQuery 查询条件
     * @return 区域配置列表
     */
    @Override
    public AjaxResult selectManageArea(int pageIndex, int pageSize, QueryManageAreaQuery queryManageAreaQuery) {
        PageHelper.startPage(pageIndex,pageSize);
        List<ManageAreaVo> manageAreaVo = crmManageAreaMapper.selectManageArea(queryManageAreaQuery);
        // 查询区域负责运营区域
        for (ManageAreaVo areaVo : manageAreaVo) {
            List<CrmBdAreaDTO> area = crmManageAreaMapper.selectArea(areaVo.getId(),null);
            areaVo.setAreaCity(area);
        }
        // 查询区域负责行政城市
        for (ManageAreaVo areaVo : manageAreaVo) {
            List<String> cityList = crmManageAdministrativeCityMapper.selectByZoneId(areaVo.getId());
            areaVo.setAdministrativeCitys(cityList);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(manageAreaVo));
    }

    /**
     * 编辑区域配置数据
     * @param saveManageAreaQuery 插入信息
     * @return 执行成功状态
     */
    @Override
    @Transactional(rollbackOn = { Exception.class })
    public AjaxResult saveManageArea(SaveManageAreaQuery saveManageAreaQuery) {
        //区域名称不能重复
        int existZoneName = crmManageBdMapper.existZoneName(saveManageAreaQuery);
        if(existZoneName > 0){
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"区域名称已存在");
        }

        // 判断下属城市是否已经在别的区域内
        List<CrmBdAreaDTO> area = crmManageAreaMapper.selectArea(null, saveManageAreaQuery.getId());
        List<Integer> areaNos = area.stream().map(CrmBdAreaDTO::getAreaNo).collect(Collectors.toList());
        List<Integer> subCity = saveManageAreaQuery.getSubCity();
        areaNos.retainAll(subCity);
        if (!areaNos.isEmpty()) {
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"下属运营区域需要唯一");
        }

        // 判断负责行政城市是否在别的区域内
        List<String> administrativeCityList = crmManageAdministrativeCityMapper.selectCity(saveManageAreaQuery.getId());
        List<String> administrativeCitys = saveManageAreaQuery.getAdministrativeCitys();
        administrativeCityList.retainAll(administrativeCitys);
        if(!CollectionUtil.isEmpty(administrativeCityList)){
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"下属行政城市需要唯一");
        }

        // 行政城市需要在围栏内
        List<QueryAreaLegitimacyResp> bcCitiesLegitimacyList = wncQueryFacade.checkAreaLegitimacy(saveManageAreaQuery.getAdministrativeCitys());
        if(CollectionUtils.isEmpty(bcCitiesLegitimacyList) || saveManageAreaQuery.getAdministrativeCitys().size() != bcCitiesLegitimacyList.size()){
            return AjaxResult.getError(String.valueOf(ResultStatusEnum.SERVER_ERROR),"所属行政城市不在围栏中,请更换城市或设置围栏!");
        }

        //id为空，保存
        CrmManageBd crmManageBd = new CrmManageBd();
        //取当前用户id
        Integer adminId = getAdminId();
        crmManageBd.setUpdater(adminId);
        BeanUtils.copyProperties(saveManageAreaQuery,crmManageBd);
        if(Objects.isNull(saveManageAreaQuery.getId())){
            crmManageBd.setCreator(adminId);
            //插入crmMangeBd
            crmManageBdMapper.insert(crmManageBd);
            //插入crmManageArea
            crmManageAreaMapper.insertArea(crmManageBd.getId(), saveManageAreaQuery.getSubCity(),adminId);
            // 插入行政城市
            crmManageAdministrativeCityMapper.insertCity(crmManageBd.getId(), saveManageAreaQuery.getAdministrativeCitys(),adminId);
        }else{
            crmManageBdMapper.updateByPrimaryKey(crmManageBd);
            // 运营区域 先删除，再新增
            crmManageAreaMapper.deleteArea(crmManageBd.getId());
            crmManageAreaMapper.insertArea(crmManageBd.getId(), saveManageAreaQuery.getSubCity(),adminId);
            // 行政城市 先删除,再新增
            crmManageAdministrativeCityMapper.deleteCity(crmManageBd.getId());
            crmManageAdministrativeCityMapper.insertCity(crmManageBd.getId(), saveManageAreaQuery.getAdministrativeCitys(),adminId);
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public AjaxResult deleteManageArea(int id) {
        //取id对应的区域负责人
        CrmManageBd crmManageBd = crmManageBdMapper.selectByPrimaryKey(id);
        //删除crmMangeBd
        crmManageBdMapper.deleteByPrimaryKey(id);
        //删除crmManageArea表数据
        crmManageAreaMapper.deleteArea(id);
        //删除按件奖励中关于此区域的数据
        crmCommissionSkuMapper.deleteByZoneName(crmManageBd.getZoneName());
        //删除所属的行政城市
        crmManageAdministrativeCityMapper.deleteCity(id);
        //删除拉新奖励中关于此区域的数据
        crmCommissionMerchantMapper.deleteByZoneName(crmManageBd.getZoneName());
        return  AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryZoneName(String zoneName) {
        List<String> info  = crmManageBdMapper.queryZoneName(zoneName);
        return AjaxResult.getOK(info);
    }

    @Override
    public AjaxResult queryExistArea() {
        List<CrmBdAreaDTO> areaLists = crmManageBdMapper.queryExistArea();
        return AjaxResult.getOK(areaLists);
    }

    @Override
    public AjaxResult queryExistCity() {
        List<String> list = crmManageAdministrativeCityMapper.selectByZoneId(null);
        return AjaxResult.getOK(list);
    }
}
