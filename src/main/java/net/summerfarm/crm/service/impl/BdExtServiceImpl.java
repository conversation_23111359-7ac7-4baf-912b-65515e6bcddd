package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.BdExtEnum;
import net.summerfarm.crm.enums.PrivateSeaTypeEnum;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.repository.BdInfoExtRepository;
import net.summerfarm.crm.model.domain.BdExt;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.query.BdExtQuery;
import net.summerfarm.crm.model.vo.BdExtVO;
import net.summerfarm.crm.model.vo.PrivateSeaType;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.BdExtService;
import net.summerfarm.pojo.DO.Admin;
import net.summerfarm.pojo.DO.Area;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Service
public class BdExtServiceImpl extends BaseService implements BdExtService {

    @Resource
    private BdInfoExtMapper bdInfoExtMapper;
    @Resource
    private BdInfoExtRepository bdInfoExtRepository;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private CrmManageBdMapper crmManageBdMapper;
    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;
    @Resource
    private LargeAreaMapper largeAreaMapper;
    @Resource
    private CrmBdOrgMapper orgMapper;
    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private CrmConfig crmConfig;

    /**
     * 公海客户
     */
    private static final String NO_ATTRIBUTION_BD = "无归属BD";

    @Override
    public AjaxResult selectBdForAreaList(Integer areaNo){
        BdExtQuery bdExtQuery = new BdExtQuery();
        bdExtQuery.setAreaNo(areaNo);
        return AjaxResult.getOK(bdInfoExtMapper.selectBdInfo(bdExtQuery));
    }

    @Override
    public AjaxResult queryBdInfo(BdExtQuery bdExtQuery) {
        bdExtQuery = Optional.ofNullable(bdExtQuery).orElse(new BdExtQuery());
        this.checkAdminRole(bdExtQuery);
        Set<BdExt> bdExtSet = new HashSet<>();
        if(Objects.equals(BdExtEnum.infoType.BD_MANAGER.ordinal(),bdExtQuery.getInfoType())){
            // 获取销售的销售主管
            if(Objects.isNull(bdExtQuery.getAdminId())){
                bdExtQuery.setAdminId(super.getAdminId());
            }
            bdExtSet = this.selectDirector(bdExtQuery);
        }else if(Objects.equals(BdExtEnum.infoType.MANAGER_SUBORDINATE.ordinal(),bdExtQuery.getInfoType())){
            List<Integer> adminIds = isSA() ? null : Collections.singletonList(super.getAdminId());
            bdExtSet = crmManageBdMapper.selectManegeSubordinate(adminIds);
            BdExt bdExt = new BdExt();
            bdExt.setAdminName(super.getCurrentUser().getRealname());
            bdExt.setAdminId(super.getAdminId());
            bdExtSet.add(bdExt);
        } else {
            List<BdExtVO> bdExtList = bdInfoExtRepository.selectBdInfo(bdExtQuery);
            return AjaxResult.getOK(bdExtList);
        }
        return AjaxResult.getOK(bdExtSet);
    }

    private void selectDirectorName(BdExtVO bdExtVO) {
        // M1
        Integer manageAdminId = bdExtVO.getManageAdminId();
        Admin manageAdmin = adminMapper.selectByPrimaryKey(manageAdminId);
        bdExtVO.setManageAdminName(manageAdmin.getRealname());
        // M2
        Integer parentAdminId = bdExtVO.getParentAdminId();
        Admin parentAdmin = adminMapper.selectByPrimaryKey(parentAdminId);
        bdExtVO.setParentAdminName(parentAdmin.getRealname());
        // M3
        Integer departmentAdminId = bdExtVO.getDepartmentAdminId();
        Admin departmentAdmin = adminMapper.selectByPrimaryKey(departmentAdminId);
        bdExtVO.setDepartmentAdminName(departmentAdmin.getRealname());
    }


    private Set<BdExt> selectDirector(BdExtQuery bdExtQuery) {
        CrmBdOrg crmBdOrg = orgMapper.selectByBdIdAndRank(bdExtQuery.getAdminId(), BD);
        List<CrmBdOrg> crmBdOrgList = bdAreaConfigService.listParentByByParentId(crmBdOrg.getId(), Boolean.TRUE);
        return crmBdOrgList.stream().map(c -> {
                    BdExtVO manageAdminInfo = new BdExtVO();
                    manageAdminInfo.setAdminId(c.getBdId());
                    manageAdminInfo.setManageAdminName(c.getBdName());
                    return manageAdminInfo;
                }
        ).collect(Collectors.toSet());
    }

    @Override
    public AjaxResult<List<BdExtVO>> queryBdArea(BdExtQuery bdExtQuery) {
        bdExtQuery = Optional.ofNullable(bdExtQuery).orElse(new BdExtQuery());
        this.checkAdminRole(bdExtQuery);
        // 销售纬度区域
        if(Objects.equals(BdExtEnum.areaType.BD_AREA.ordinal(),bdExtQuery.getAreaType())){
            return this.selectBdArea(bdExtQuery);
        }
        // 运营纬度区域
        if(Objects.equals(BdExtEnum.areaType.OPERATE_AREA.ordinal(),bdExtQuery.getAreaType())){
            return this.selectOperateArea();
        }
        return AjaxResult.getOK();
    }

    @Override
    public CommonResult<Boolean> queryBdRole() {
        return CommonResult.ok(super.isSA() || super.isSaleSA() || super.isAreaSA());
    }

    @Override
    public AreaQueryResp matchAreaNo(Contact contact) {
        AreaQueryReq req = new AreaQueryReq();
        req.setCity(contact.getCity());
        req.setArea(contact.getArea());
        DubboResponse<AreaQueryResp> response = deliveryFenceQueryProvider.queryAreaByAddress(req);

        if (!response.isSuccess()) {
            logger.info("获取围栏失败:{}", response.getMsg());
           throw  new BizException("获取围栏失败，请稍候重试");
        }
        if (response.getData() == null) {
            return null;
        }
        return response.getData();
    }

    @Override
    public List<LargeAreaDTO> selectAllLargeArea() {
        return largeAreaMapper.selectAll(null);
    }

    @Override
    public PrivateSeaType queryBdPrivateSeaType() {
        Integer bdId = this.getAdminId();

        if (crmConfig.getPopBdList().contains(bdId)) {
            return new PrivateSeaType(PrivateSeaTypeEnum.POP.getCode());
        }
        if (crmConfig.getXianmuAndPopBdList().contains(bdId)) {
            return new PrivateSeaType(PrivateSeaTypeEnum.XIANMU_AND_POP.getCode());
        }

        return new PrivateSeaType(PrivateSeaTypeEnum.XIANMU.getCode());
    }

    /**
     *  检查销售角色
     * @param bdExtQuery 查询信息
     */
    private void checkAdminRole(BdExtQuery bdExtQuery){
        //如果是超管,返回所有区域
        Integer adminId = super.isSA() ? null : super.getAdminId();
        adminId = Objects.isNull(bdExtQuery.getAdminId()) ? adminId : bdExtQuery.getAdminId();
        bdExtQuery.setAdminId(adminId);
        Integer areaType = Objects.isNull(bdExtQuery.getAreaType()) ? NumberUtils.INTEGER_ZERO : bdExtQuery.getAreaType();
        bdExtQuery.setAreaType(areaType);
        Integer infoType = Objects.isNull(bdExtQuery.getInfoType()) ? NumberUtils.INTEGER_ZERO : bdExtQuery.getInfoType();
        bdExtQuery.setInfoType(infoType);
    }

    private AjaxResult selectOperateArea() {
        List<BdExtVO> allOperateLargeArea = crmManageBdMapper.selectOperateLargeArea(NumberUtils.INTEGER_ONE,null);
        for (BdExtVO bdExtVO : allOperateLargeArea) {
            List<Area> areas = crmManageBdMapper.selectOperateAreaByLargeNo(bdExtVO);
            bdExtVO.setChildrenArea(areas);
        }
        allOperateLargeArea = allOperateLargeArea.stream()
                .filter(b -> CollectionUtil.isNotEmpty(b.getChildrenArea()))
                .collect(Collectors.toList());
        return AjaxResult.getOK(allOperateLargeArea);
    }

    private AjaxResult selectBdArea(BdExtQuery bdExtQuery) {
        if(Objects.nonNull(bdExtQuery.getAdminId()) && CollectionUtil.isEmpty(bdExtQuery.getAdminIds())){
            bdExtQuery.setAdminIds(Collections.singletonList(bdExtQuery.getAdminId()));
        }

        // 获取筛选人员管理的区域,无则展示自身
        List<BdExtVO> zoneNameListVos = crmManageBdMapper.selectZoneNameByAdminId(bdExtQuery);
        if (CollectionUtils.isEmpty(zoneNameListVos)) {
            return AjaxResult.getErrorWithMsg("请先添加管理区域");
        }

        // 如有筛选,只能获取自身与筛选人员共同管理的区域
        if(CollectionUtil.isNotEmpty(bdExtQuery.getAdminIds()) && Objects.nonNull(bdExtQuery.getAdminId())){
            BdExtQuery adminQuery = new BdExtQuery();
            adminQuery.setAdminIds(Collections.singletonList(bdExtQuery.getAdminId()));
            // 自己管理的
            List<BdExtVO> myManageZoneList = crmManageBdMapper.selectZoneNameByAdminId(adminQuery);
            myManageZoneList = Optional.ofNullable(myManageZoneList).orElse(new ArrayList<>());
            zoneNameListVos.retainAll(myManageZoneList);
        }
        if (CollectionUtils.isEmpty(zoneNameListVos)) {
            return AjaxResult.getErrorWithMsg("请先添加管理区域");
        }

        for (BdExtVO zoneNameListVo : zoneNameListVos) {
            List<Area> areas = crmManageBdMapper.selectZoneInfoById(zoneNameListVo.getAreaNo(),null);
            zoneNameListVo.setChildrenArea(areas);
        }
        return AjaxResult.getOK(zoneNameListVos);
    }

}
