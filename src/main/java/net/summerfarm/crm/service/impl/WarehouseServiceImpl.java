package net.summerfarm.crm.service.impl;/**
 * <AUTHOR>
 * @date 2023/1/5 15:44
 */

import com.github.pagehelper.PageInfo;

import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.PageInfoHelperUtil;
import net.summerfarm.crm.facade.WarehouseQueryFacade;
import net.summerfarm.crm.model.vo.WarehouseBatchProveRecordVO;
import net.summerfarm.crm.model.vo.WarehouseStorageVO;
import net.summerfarm.crm.service.WarehouseService;
import net.summerfarm.manage.client.wms.dto.req.WarehouseQueryReq;
import net.summerfarm.manage.client.wms.dto.res.WarehouseBatchProveRecordDTO;
import net.summerfarm.manage.client.wms.dto.res.WarehouseStorageDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5 15:44
 */
@Service("warehouseService")
public class WarehouseServiceImpl extends BaseService implements WarehouseService {
    @Resource
    WarehouseQueryFacade warehouseFacade;

    @Override
    public PageInfo<WarehouseStorageVO> inventoryList(Integer pageIndex, Integer pageSize, WarehouseQueryReq param) {
        PageInfo<WarehouseStorageDTO> storageDTOS = warehouseFacade.inventoryList(pageIndex, pageSize, param);
        PageInfo<WarehouseStorageVO> voPageInfo = PageInfoHelperUtil.pageInfoConvert(storageDTOS,WarehouseStorageVO.class);
        return voPageInfo;
    }

    @Override
    public PageInfo<WarehouseBatchProveRecordVO> proveList(Integer pageIndex, Integer pageSize, WarehouseQueryReq param) {
        PageInfo<WarehouseBatchProveRecordDTO> recordDTOS = warehouseFacade.proveList(pageIndex, pageSize, param);
        PageInfo<WarehouseBatchProveRecordVO> voPageInfo = PageInfoHelperUtil.pageInfoConvert(recordDTOS,WarehouseBatchProveRecordVO.class);
        return voPageInfo;
    }
}
