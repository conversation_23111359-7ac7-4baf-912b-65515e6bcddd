package net.summerfarm.crm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;

import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.mapper.manage.MerchantKeyPersonMapper;
import net.summerfarm.crm.model.domain.MerchantKeyPerson;
import net.summerfarm.crm.service.MerchantKeyPersonService;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 13:50
 */
@Service
public class MerchantKeyPersonServiceImpl extends BaseService implements MerchantKeyPersonService {

    @Resource
    private MerchantKeyPersonMapper merchantKeyPersonMapper;


    @Override
    public AjaxResult selectKeyPerson(Long mId) {
        if(Objects.isNull(mId)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        List<MerchantKeyPerson> kpList = merchantKeyPersonMapper.selectKeyPerson(mId);
        return AjaxResult.getOK(kpList);
    }

    @Override
    public AjaxResult updateKeyPerson(MerchantKeyPerson merchantKeyPerson) {
        boolean isPhone = StringUtils.isMobile(merchantKeyPerson.getPhone());
        if(!isPhone){
            return AjaxResult.getErrorWithMsg("请输入正确的注册手机号");
        }
        merchantKeyPerson.setCreateId(getAdminId());
        try {
            if(Objects.isNull(merchantKeyPerson.getId())){
                // 新增
                merchantKeyPersonMapper.insertSelective(merchantKeyPerson);
            }else {
                // 修改
                merchantKeyPersonMapper.updateByPrimaryKeySelective(merchantKeyPerson);
            }
        } catch (DuplicateKeyException e) {
            return AjaxResult.getErrorWithMsg("该手机号已绑定至该店铺kp人员");
        }
        return AjaxResult.getOK();
    }
}
