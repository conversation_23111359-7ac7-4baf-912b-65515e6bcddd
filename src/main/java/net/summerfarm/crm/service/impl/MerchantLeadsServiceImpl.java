package net.summerfarm.crm.service.impl;

import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.AreaMapper;
import net.summerfarm.crm.mapper.manage.MerchantCluePoolMapper;
import net.summerfarm.crm.mapper.manage.MerchantLeadsMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.domain.MerchantCluePool;
import net.summerfarm.crm.model.domain.MerchantLeads;
import net.summerfarm.crm.model.dto.AreaInfoDTO;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.vo.AreaVO;
import net.summerfarm.crm.model.vo.MerchantLeadsVO;
import net.summerfarm.crm.service.CityPermissionService;
import net.summerfarm.crm.service.MerchantLeadsService;
import net.summerfarm.crm.service.MerchantService;
import net.summerfarm.enums.MerchantLeadsStatus;
import net.summerfarm.pojo.DO.Area;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.beetl.core.om.AABuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Service
public class MerchantLeadsServiceImpl extends BaseService implements MerchantLeadsService {

    @Resource
    private MerchantLeadsMapper merchantLeadsMapper;
    @Resource
    private MerchantCluePoolMapper merchantCluePoolMapper;
    @Resource
    private MerchantService merchantService;
    @Resource
    private CityPermissionService cityPermissionService;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    @Resource
    private CrmConfig crmConfig;
    @Resource
    private AreaMapper areaMapper;

    @Override
    public AjaxResult selectMerchantLeads(int pageIndex, int pageSize, MerchantLeads selectKeys) {
        PageHelper.startPage(pageIndex, pageSize);
        if(!isSA()){
            selectKeys.setAdminId(getAdminId());
        }
        List<MerchantLeadsVO> merchantLeadsList =  merchantLeadsMapper.selectMerchantLeads(selectKeys);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchantLeadsList));
    }

    /**
     * 修改/更新同一个方法
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult saveMerchantLeads(MerchantLeadsVO merchantLeads) {
        // 例子名称不可重复
        MerchantStoreResultResp merchantExtend = merchantQueryFacade.getMerchantByStoreName(merchantLeads.getMname().trim());
        if (merchantExtend != null) {
            return AjaxResult.getErrorWithMsg("该店铺名称已注册");
        }
        if (merchantLeads.getPoiNote() == null) {
            return AjaxResult.getErrorWithMsg("POI不能为空");
        }
        if (!StringUtils.isEmpty(merchantLeads.getMname()) && merchantLeads.getMname().length() >= 20) {
            return AjaxResult.getErrorWithMsg("店铺名称长度不能超过20个字符");
        }
        if (!StringUtils.isMname(merchantLeads.getMname())) {
            return AjaxResult.getErrorWithMsg("店铺名称只能由汉字、数字和英文字母组成，请重新输入");
        }
        if (merchantLeads.getMerchantType() == null) {
            return AjaxResult.getErrorWithMsg("业务类型不能为空");
        }

        MerchantLeads leads = merchantLeadsMapper.selectByMname(merchantLeads.getMname().trim());
        if (leads != null) {
            if (merchantLeads.getId() == null || !Objects.equals(merchantLeads.getId(), leads.getId())) {
                return AjaxResult.getErrorWithMsg("当前客户已有对应线索，请返回线索池搜索");
            }
        }

        cityPermissionService.checkAreaPermission(getAdminId(), merchantLeads.getCity(), merchantLeads.getArea());
        //若修改此处应该有校验，除超管外，只能修改自己跟进的用户
        //维护剩余信息
        if (merchantLeads.getId() == null) {
            merchantLeads.setAdminId(getAdminId());
            merchantLeads.setAdminName(getAdminName());
            merchantLeads.setSource("BD");
            merchantLeads.setStatus(MerchantLeadsStatus.FOLLOWING.ordinal());
        }
        merchantLeads.setAuthor(getAdminName());
        merchantLeads.setSize("单店");


        //是否是更新
        boolean updateOrSave = false;
        //修改例子
        if (merchantLeads.getId() != null) {
            updateOrSave = true;
            MerchantCluePool queryCluePool = new MerchantCluePool();
            queryCluePool.setMlId(merchantLeads.getId());
            MerchantCluePool merchantCluePool = merchantCluePoolMapper.queryMerchantClue(queryCluePool);
            //新增 修改 线索池关联
            if (!StringUtils.isEmpty(merchantLeads.getEsId())) {
                if (merchantCluePool == null || !Objects.equals(merchantCluePool.getEsId(), merchantLeads.getEsId())) {
                    int i = merchantCluePoolMapper.queryEsIdNumber(merchantLeads.getEsId());
                    if (i > 0) {
                        return AjaxResult.getErrorWithMsg("当前线索已被选择");
                    }
                }
            }

        }
        //新增例子
        if (!updateOrSave && !StringUtils.isEmpty(merchantLeads.getEsId())) {
            int i = merchantCluePoolMapper.queryEsIdNumber(merchantLeads.getEsId());
            if (i > 0) {
                return AjaxResult.getErrorWithMsg("当前线索已被选择");
            }
        }
        merchantLeadsMapper.insertOrUpdateById(merchantLeads);

        //新增例子 插入线索池关联信息
        if(!updateOrSave && !StringUtils.isEmpty(merchantLeads.getEsId())){
            MerchantCluePool merchantCluePool = new MerchantCluePool(merchantLeads);
            merchantService.updateCurlPool(merchantCluePool,MerchantCluePool.BANDING,null);
        }
        //更新例子池
        if(updateOrSave){
            updateMerchantCluePool(merchantLeads);
        }
        return AjaxResult.getOK(merchantLeads.getId());
    }

    @Override
    public CommonResult<LargeAreaDTO> queryPopAreaNo() {
        AreaInfoDTO areaInfoDTO = areaMapper.selectLargeAreaByAreaNo(crmConfig.getPopDefaultAreaNo());
        LargeAreaDTO dto = new LargeAreaDTO();
        if(areaInfoDTO != null) {
            dto.setLargeAreaName(areaInfoDTO.getLargeAreaName());
            dto.setLargeAreaNo(areaInfoDTO.getLargeAreaNo());
            Area area = new Area();
            area.setAreaNo(areaInfoDTO.getAreaNo());
            area.setAreaName(areaInfoDTO.getAreaName());
            dto.setAreaList(Collections.singletonList(area));
        }
        return CommonResult.ok(dto);
    }

    /**
     * 更新线索池信息
     * @param merchantLeads 线索池信息
     */
    private void updateMerchantCluePool(MerchantLeadsVO merchantLeads){

        if(merchantLeads.getId() == null){
            return;
        }
        MerchantCluePool queryCluePool = new MerchantCluePool();
        queryCluePool.setMlId(merchantLeads.getId());
        MerchantCluePool merchantCluePool = merchantCluePoolMapper.queryMerchantClue(queryCluePool);
        String oldEsId = merchantCluePool != null ? merchantCluePool.getEsId() : null;

        //新增关联关系
        if(merchantCluePool == null && !StringUtils.isEmpty(merchantLeads.getEsId())){
            MerchantCluePool insertCluePool = new MerchantCluePool(merchantLeads);
            merchantService.updateCurlPool(insertCluePool,MerchantCluePool.BANDING,null);
        }

        //移除关联关系
        if(merchantCluePool != null && StringUtils.isEmpty(merchantLeads.getEsId())){
            merchantService.updateCurlPool(null,MerchantCluePool.BANDING,oldEsId);
        }
        //修改关联关系
        if(merchantCluePool != null && !StringUtils.isEmpty(merchantLeads.getEsId()) && !Objects.equals(merchantLeads.getEsId(),oldEsId)){
            MerchantCluePool insertCluePool = new MerchantCluePool(merchantLeads);
            insertCluePool.setmId(merchantCluePool.getmId());
            merchantService.updateCurlPool(insertCluePool,MerchantCluePool.BANDING,oldEsId);
        }
    }
}
