package net.summerfarm.crm.service.impl;

import net.summerfarm.crm.mapper.manage.InvoiceEmailOverrideMapper;
import net.summerfarm.crm.model.domain.InvoiceEmailOverride;
import net.summerfarm.crm.service.InvoiceEmailOverrideService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* @description: 
* @author: George
* @date: 2025-07-22
**/
@Service
public class InvoiceEmailOverrideServiceImpl implements InvoiceEmailOverrideService {

    @Resource
    private InvoiceEmailOverrideMapper invoiceEmailOverrideMapper;

    @Override
    public int insertOrUpdateByUK(InvoiceEmailOverride invoiceEmailOverride) {
        InvoiceEmailOverride existed = invoiceEmailOverrideMapper.selectByConfigIdAndMId(invoiceEmailOverride.getInvoiceConfigId(), invoiceEmailOverride.getMId());
        if (existed != null) {
            invoiceEmailOverride.setId(existed.getId());
            return invoiceEmailOverrideMapper.updateByPrimaryKeySelective(invoiceEmailOverride);
        }
        return invoiceEmailOverrideMapper.insert(invoiceEmailOverride);
    }

    @Override
    public InvoiceEmailOverride selectByConfigIdAndMId(Long invoiceConfigId, Long merchantId) {
        return invoiceEmailOverrideMapper.selectByConfigIdAndMId(invoiceConfigId, merchantId);
    }
}
