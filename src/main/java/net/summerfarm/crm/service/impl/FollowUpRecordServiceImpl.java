package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.common.util.ExcelUtils;
import net.summerfarm.crm.common.util.MailUtil;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.*;
import net.summerfarm.crm.enums.BDTarget.BdVisitPlanEnum;
import net.summerfarm.crm.facade.ContactQueryFacade;
import net.summerfarm.crm.facade.FeiShuPersonalMsgFacade;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.repository.FollowUpRecordRepository;
import net.summerfarm.crm.mapper.repository.MerchantExtendsRepository;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanRepository;
import net.summerfarm.crm.model.bo.DingdingFormBO;
import net.summerfarm.crm.model.bo.ProcessInstanceCreateBO;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlan;
import net.summerfarm.crm.model.dto.*;
import net.summerfarm.crm.model.query.NearbyQuery;
import net.summerfarm.crm.model.query.task.TaskFollowUpQuery;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.model.vo.task.TaskDetailVo;
import net.summerfarm.crm.mq.NewMQProducer;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.constant.MessageKeyEnum;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.util.Producer;
import net.summerfarm.crm.service.*;
import net.summerfarm.pojo.DO.Admin;
import net.summerfarm.pojo.DO.Area;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.common.user.UserBase;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.cosfo.message.client.req.MessageBodyReq;
import com.github.pagehelper.PageHelper;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import static net.summerfarm.crm.enums.MerchantSizeEnum.MAJOR_ACCOUNT;
import static net.summerfarm.crm.enums.MerchantSizeEnum.SINGGLE_STORE;
import static net.summerfarm.crm.mq.NewMQProducer.TAG_CRM_WX_FEEDBACK;
import static net.summerfarm.crm.mq.NewMQProducer.TOPIC_CRM_WX_OFFICIAL_PUSH;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;

;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Service
@Slf4j
public class FollowUpRecordServiceImpl extends BaseService implements FollowUpRecordService {

    private static final String  WX_PUSH_TEMPLATE_ID= "ruf0odIjU53ZWxzMiioCeyyZjY6_Yk47aaKDN4x44hE";
    @Resource
    private FollowUpRecordMapper followUpRecordMapper;
    @Resource
    private VisitPlanMapper visitPlanMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;
    @Resource
    private Producer producer;
    @Resource
    private BdInfoExtMapper bdInfoExtMapper;
    @Resource
    private MerchantKeyPersonMapper merchantKeyPersonMapper;
    @Resource
    private CrmEscortVisitPlanMapper crmEscortVisitPlanMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    NewMQProducer newMQProducer;
    @Resource
    MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private MerchantPoiUpdateRecordMapper poiUpdateRecordMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private ContactQueryFacade contactQueryFacade;
    @Resource
    private MerchantCluePoolMapper merchantCluePoolMapper;
    @Resource
    private MerchantService merchantService;
    @Resource
    FollowUpRelationService followUpRelationService;
    @Resource
    private FollowUpEvaluationMapper followUpEvaluationMapper;
    @Resource
    private CityPermissionService cityPermissionService;
    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    FeiShuPersonalMsgFacade feiShuPersonalMsgFacade;
    @Resource
    AdminMapper adminMapper;
    @Resource
    private CrmBdOrgMapper bdOrgMapper;

    @Resource
    private MerchantExtendsRepository merchantExtendsRepository;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    @Resource
    private FollowUpRecordRepository followUpRecordRepository;
    @Resource
    private BdVisitPlanRepository bdVisitPlanRepository;
    private static String EMOJI_REG="[^\\u0000-\\uFFFF]";
    @Resource
    private BigCustomerPropertiesExtMapper adminPropertiesExtMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private MerchantLabelMapper merchantLabelMapper;
    @Resource
    private MerchantLabelCorrelaionMapper labelCorrelaionMapper;
    @Resource
    private CrmConfig crmConfig;
    @Resource
    private FollowUpDownloadService followUpDownloadService;

    private static final String showFruitArPuTag="4月水果提额任务(录妙计)";

    @NacosValue(value = "${showFruitArPuCrowdId:0}",autoRefreshed = true)
    private Long showFruitArPuCrowdId;

    // 水果召回任务
    @NacosValue(value = "${showFruitRecallTag:召回任务}",autoRefreshed = true)
    private String showFruitRecallTag;
    @NacosValue(value = "${showFruitRecallCrowdId:0}",autoRefreshed = true)
    private Long showFruitRecallCrowdId;
    @NacosValue(value = "${showFruitRecallTaskId:0}", autoRefreshed = true)
    private Long showFruitRecallTaskId;

    // 鲜果任务 - 注册未下单
    @NacosValue(value = "${freshFruitRegisteredNoOrderTag:5月鲜果任务-注册未下单}",autoRefreshed = true)
    private String freshFruitRegisteredNoOrderTag;
    @NacosValue(value = "${freshFruitRegisteredNoOrderCrowdId:0}",autoRefreshed = true)
    private Long freshFruitRegisteredNoOrderCrowdId;
    @NacosValue(value = "${freshFruitRegisteredNoOrderTaskId:0}",autoRefreshed = true)
    private Long freshFruitRegisteredNoOrderTaskId;

    // 鲜果任务 - 近90天未下单
    @NacosValue(value = "${freshFruitNearlyDaysNoOrderTag:5月鲜果任务-近90天未下单}",autoRefreshed = true)
    private String freshFruitNearlyDaysNoOrderTag;
    @NacosValue(value = "${freshFruitNearlyDaysNoOrderCrowdId:0}",autoRefreshed = true)
    private Long freshFruitNearlyDaysNoOrderCrowdId;
    @NacosValue(value = "${freshFruitNearlyDaysNoOrderTaskId:0}",autoRefreshed = true)
    private Long freshFruitNearlyDaysNoOrderTaskId;


    // PT奶油推品任务
    @NacosValue(value = "${creamPushTaskTag:5月PT奶油推品任务}",autoRefreshed = true)
    private String creamPushTaskTag;
    @NacosValue(value = "${creamPushTaskCrowdId:0}",autoRefreshed = true)
    private Long creamPushTaskCrowdId;
    @NacosValue(value = "${creamPushTaskId:0}",autoRefreshed = true)
    private Long creamPushTaskId;



    @Resource
    private MerchantPoolDetailMapper merchantPoolDetailMapper;
    @Resource
    private CrmTaskDetailMapper crmTaskDetailMapper;
    @Resource
    private CrmJobMerchantDetailMapper crmJobMerchantDetailMapper;


    @Override
    public List<String> getFruitTags(Long mId) {
        List<String> tags = new ArrayList<>(5);
        if ( showFruitArPuCrowdId > 0 && merchantPoolDetailMapper.countByMidPoolInfoID(mId, showFruitArPuCrowdId) > 0) {
            tags.add(showFruitArPuTag);
        }
        if (showFruitRecallCrowdId != null && merchantPoolDetailMapper.countByMidPoolInfoID(mId, showFruitRecallCrowdId) > 0) {
            tags.add(showFruitRecallTag);
        }
        if (freshFruitRegisteredNoOrderCrowdId != null && merchantPoolDetailMapper.countByMidPoolInfoID(mId, freshFruitRegisteredNoOrderCrowdId) > 0) {
            tags.add(freshFruitRegisteredNoOrderTag);
        }
        if (freshFruitNearlyDaysNoOrderCrowdId != null && merchantPoolDetailMapper.countByMidPoolInfoID(mId, freshFruitNearlyDaysNoOrderCrowdId) > 0) {
            tags.add(freshFruitNearlyDaysNoOrderTag);
        }
        if (creamPushTaskCrowdId != null && merchantPoolDetailMapper.countByMidPoolInfoID(mId, creamPushTaskCrowdId) > 0) {
            tags.add(creamPushTaskTag);
        }
        return tags;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public AjaxResult saveRecord(FollowUpRecordVO record, boolean isTask){
        if (record.getmId() == null ){
            throw new BizException("门店id不能为");
        }
        if (record.getContactId() == null){
            throw new BizException("联系人id不能为空");
        }

        Integer adminId = record.getAdminId() == null ? getAdminId() : record.getAdminId();
        String adminName = record.getAdminName() == null ? getAdminName() : record.getAdminName();


        //Merchant merchant = merchantMapper.selectByPrimaryKey(record.getmId());
        MerchantStoreAndExtendDTO merchantExtendDTO =  merchantExtendsRepository.getAuthMerchantExtendDTO(record.getmId(),getCompatibleDataPermission(isTask));
        if(Objects.isNull(merchantExtendDTO)){
            return AjaxResult.getErrorWithMsg("你没有该客户区域的数据权限,请联系主管添加!");
        }
        // 判断是否包含表情字符
//        Pattern pattern = Pattern.compile(EMOJI_REG);
//        Matcher matcher = pattern.matcher(record.getCondition());
//        if (matcher.find()) {
//            return AjaxResult.getErrorWithMsg("拜访记录不允许输入特殊字符");
//        }
        //已经绑定过m_id
        if (!StringUtils.isEmpty(record.getEsId())){
            if (merchantCluePoolMapper.queryEsIdNumber(record.getEsId())>0){
                return AjaxResult.getErrorWithMsg("线索已被绑定");
            }
            MerchantCluePool pool = new MerchantCluePool();
            pool.setmId(record.getmId());
            MerchantCluePool midPool = merchantCluePoolMapper.queryMerchantClue(pool);
            if (midPool !=null){
                return AjaxResult.getErrorWithMsg("门店已经绑定别的线索");
            }
        }

        // 没传拜访计划id但当前门店当天有拜访计划，则设置拜访计划id
        if (record.getVisitPlanId() == null && !Objects.equals(FollowRecordEnum.Type.ESCORT.ordinal(), record.getVisitType())) {
            VisitPlanVO query = new VisitPlanVO();
            query.setAdminId(adminId);
            query.setDate(LocalDate.now());
            query.setmId(record.getmId());
            query.setStatus(VisitPlanEnum.Status.WAIT.ordinal());
            List<VisitPlanVO> visitPlanVOList = visitPlanMapper.selectList(query);
            if (!CollectionUtils.isEmpty(visitPlanVOList)) {
                VisitPlanVO visitPlanVO = visitPlanVOList.stream().filter(x -> record.getContactId().equals(x.getContactId())).findFirst().orElse(null);
                if (visitPlanVO != null) {
                    log.info("当前门店当天有拜访计划， query：{}，contactId：{}，visitPlanId：{}", JSON.toJSONString(query), record.getContactId(), visitPlanVO.getId());
                    record.setVisitPlanId(visitPlanVO.getId());
                }
            }
        }

        if(Objects.nonNull(record.getVisitPlanId())){
            if(Objects.equals(FollowRecordEnum.Type.ESCORT.ordinal(), record.getVisitType())){
                // 更新陪访计划
                VisitPlanDTO visitPlanDTO = new VisitPlanDTO();
                visitPlanDTO.setId(record.getVisitPlanId());
                visitPlanDTO.setStatus(VisitPlanEnum.Status.FINISH.ordinal());
                visitPlanDTO.setUpdater(getAdminName());
                crmEscortVisitPlanMapper.updateVisitPlan(visitPlanDTO);
            }else {
                // 更新拜访计划
                this.updateVisitPlan(record);
            }
        }

        // 陪访人是否已有今日的拜访计划
        if(CollectionUtil.isNotEmpty(record.getEscortAdminIdList())){
            // 是否有拜访计划
            if(Objects.isNull(record.getVisitPlanId())){
                VisitPlan visitPlan = new VisitPlan();
                visitPlan.setmId(record.getmId());
                visitPlan.setContactId(record.getContactId());
                visitPlan.setExpectedTime(LocalDateTime.of(LocalDate.now(),LocalTime.MIN));
                visitPlan.setAdminId(adminId);
                visitPlan.setCreator(getAdminName());
                visitPlan.setStatus(VisitPlanEnum.Status.FINISH.ordinal());
                visitPlan.setType(VisitPlanEnum.Type.VISIT.ordinal());
                visitPlan.setProvince(merchantExtendDTO.getProvince());
                visitPlan.setCity(merchantExtendDTO.getCity());
                visitPlan.setArea(merchantExtendDTO.getArea());
                visitPlan.setAreaNo(merchantExtendDTO.getAreaNo());
                visitPlanMapper.insertVisitPlan(visitPlan);
                record.setVisitPlanId(visitPlan.getId());
            }
            // 查询现有的陪访计划
            List<Long> hasEscortVisitPlanAdminIds = new ArrayList<>();
            List<CrmEscortVisitPlan> crmEscortVisitPlans = crmEscortVisitPlanMapper.selectByVisitPlanId(record.getVisitPlanId());
            if (!CollectionUtils.isEmpty(crmEscortVisitPlans)) {
                hasEscortVisitPlanAdminIds = crmEscortVisitPlans.stream().map(CrmEscortVisitPlan::getAdminId).collect(Collectors.toList());
            }

            // 插入陪访计划
            for (Long escortAdmin : record.getEscortAdminIdList()) {
                if (hasEscortVisitPlanAdminIds.contains(escortAdmin)) {
                    log.info("已存在陪访计划，visitPlanId:{}，陪访人：{}", record.getVisitPlanId(), escortAdmin);
                    continue;
                }
                CrmEscortVisitPlan crmEscortVisitPlan = new CrmEscortVisitPlan();
                crmEscortVisitPlan.setAdminId(escortAdmin);
                crmEscortVisitPlan.setVisitPlanId(record.getVisitPlanId());
                crmEscortVisitPlan.setExpectedTime(LocalDateTime.of(LocalDate.now(),LocalTime.MIN));
                crmEscortVisitPlan.setStatus(VisitPlanEnum.Status.WAIT.ordinal());
                crmEscortVisitPlan.setCreator(getAdminName());
                crmEscortVisitPlanMapper.insertEscortVisitPlan(crmEscortVisitPlan);
            }
        }

        if (StringUtils.isEmpty(merchantExtendDTO.getDoorPic())&&!StringUtils.isEmpty(record.getDoorPic())){
            merchantExtendDTO.setDoorPic(record.getDoorPic());
            Merchant updateMerchant = new Merchant();
            updateMerchant.setmId(merchantExtendDTO.getMId());
            updateMerchant.setDoorPic(record.getDoorPic());
            merchantMapper.updateByPrimaryKeySelective(updateMerchant);
        }

        // 指定下次拜访,默认为普通拜访
        if (Objects.nonNull(record.getNextFollowTime())){
            if(record.getNextFollowTime().isBefore(LocalDate.now())){
                return AjaxResult.getErrorWithMsg("下次拜访时间需在今日之后");
            }
            this.insertVisitPlan(record,adminId, VisitPlanEnum.Type.VISIT.ordinal(),record.getNextFollowTime(),merchantExtendDTO);
        }

        // 客户倒闭审批
        if(!Objects.equals(merchantExtendDTO.getOperateStatus(),record.getOperateStatus())){
            this.operateStatusApproval(merchantExtendDTO,record);
        }
        // 生成拜访记录
        this.insertFollowRecord(adminId, adminName, record,merchantExtendDTO, record.getmId());
        // 更新poi拆分至后台
        if(Objects.equals(record.getCheckPoi(),true)){
            MerchantPoiUpdateRecord poiUpdateRecord = createPoiUpdateRecord(record, merchantExtendDTO);
            poiUpdateRecordMapper.insertSelective(poiUpdateRecord);
            sendDingTalkMsg(poiUpdateRecord);
        }

        if (net.summerfarm.common.util.StringUtils.isNotBlank(record.getEsId())){
            NearbyQuery nearbyQuery = new NearbyQuery();
            nearbyQuery.setEsId(record.getEsId());
            try {
                AjaxResult<NearbyVO> nearbyVOAjaxResult = followUpRelationService.queryByEsId(nearbyQuery);
                if (nearbyVOAjaxResult.getData()!=null){
                    MerchantCluePool merchantCluePool = new MerchantCluePool();
                    merchantCluePool.setmId(record.getmId());
                    merchantCluePool.setPhone(nearbyVOAjaxResult.getData().getPhone());
                    merchantCluePool.setAddress(nearbyVOAjaxResult.getData().getAddress());
                    Map<String,String> selectKeys  =  new HashMap<>();
                    selectKeys.put("mId",record.getmId().toString());
                    merchantCluePool.setmName(nearbyVOAjaxResult.getData().getMname());
                    merchantCluePool.setEsId(record.getEsId());
                    merchantService.updateCurlPool(merchantCluePool,MerchantCluePool.BANDING,null);
                }
            }catch (Exception e){
                logger.error("添加拜访查询es获取到异常",e);
            }
        }
        //跨区拜访 找到这个的m1 发送消息
        crossNotice2M1(adminId, record.getContactId(), merchantExtendDTO);

        // 2025-01-17 过年标签不再通过拜访记录添加
        // 处理过年标签
//        if (CollectionUtil.isNotEmpty(record.getNewYearBusinessTag())) {
//            for (String tag : record.getNewYearBusinessTag()) {
//                MerchantLabel merchantLabel = merchantLabelMapper.selectByName(tag);
//                if (merchantLabel != null) {
//                    if (crmConfig.getNewYearOperatingStatusLabels().contains(merchantLabel.getId().intValue())) {
//                        labelCorrelaionMapper.deleteByMidAndLabelIdList(record.getmId(), crmConfig.getNewYearOperatingStatusLabels());
//                    } else if (crmConfig.getCampusStoreLabels().contains(merchantLabel.getId().intValue())) {
//                        labelCorrelaionMapper.deleteByMidAndLabelIdList(record.getmId(), crmConfig.getCampusStoreLabels());
//                    } else if (crmConfig.getOpeningTimeLabels().contains(merchantLabel.getId().intValue())) {
//                        labelCorrelaionMapper.deleteByMidAndLabelIdList(record.getmId(), crmConfig.getOpeningTimeLabels());
//                    }
//                    labelCorrelaionMapper.insertByMidAndLabelId(record.getmId(), merchantLabel.getId());
//                }
//            }
//        }
        //根据mid查询所有的任务id   任务类型为拜访   并且是未完成的  修改为已完成
        List<TaskDetailVo> taskDetailVos = crmTaskDetailMapper.listDetailForMId(record.getmId(),CrmTaskDeatilEnum.uncompleted.getStatus(), CrmTaskEnum.Type.VISIT.getType());
        if (!CollectionUtil.isEmpty(taskDetailVos)){
            List<Integer> taskDetailIdList = taskDetailVos.stream().map(TaskDetailVo::getTaskDetailId).collect(Collectors.toList());
            if (!CollectionUtil.isEmpty(taskDetailIdList)){
                crmTaskDetailMapper.upsertBatch(taskDetailIdList,CrmTaskDeatilEnum.completed.getStatus());
            }
        }
        // 任务中心v2 修改拜访状态
        crmJobMerchantDetailMapper.updateFollowUpRecordIdByMIdAndFollowUpRecordIdIsNull(record.getId(),record.getmId());
        // 销售拜访计划修改拜访状态
        updateBdVisitPlanVisitStatus(record);
        return AjaxResult.getOK(record);
    }

    public void crossNotice2M1(Integer adminId, Integer contactId, MerchantStoreAndExtendDTO merchantExtendDTO){
        log.info("获取到信息 跨区域消息提醒 adminId{} contactId {}",adminId,contactId);
        Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(contactId));
        if (contact == null){
            log.info("联系人地址不存在 {}",contactId);
            return;
        }
        CrmBdCity crmBdCity = cityPermissionService.selectAminIdCityandArea(adminId, contact.getCity(), contact.getArea());
        if (crmBdCity != null){
            return;
        }
        //找到bd的上级
        List<CrmBdOrg> crmBdOrgs = bdAreaConfigService.listParentOrg(adminId);
        if (CollectionUtils.isEmpty(crmBdOrgs)){
            log.info("跨区域拜访没找到bd的上级信息 bdId {}",adminId);
            return;
        }
        CrmBdOrg crmBdOrg = crmBdOrgs.get(0);
        CrmBdOrg parent = bdOrgMapper.selectByPrimaryKey(crmBdOrg.getParentId());
        if (parent == null){
            log.info("跨区域拜访没找到bd的上级信息 bdId {}", adminId);
            return;
        }
        MessageBodyReq req = new MessageBodyReq();
        req.setContentType(1);
        req.setMsgBodyType(0);
        req.setTitle("销售拜访跨区提醒");
        String msgStr = format(adminId,merchantExtendDTO,contact).replaceAll("#","")
                .replaceAll(">>", ">")
                .replaceAll(">", "\n")
                .replaceAll("\n\n", "\n")
                .replaceAll(" ","");

        JSONObject text = new JSONObject();
        text.put("text", msgStr);
        req.setData(text.toJSONString());
        feiShuPersonalMsgFacade.sendFeiShuPersonalMsg(Collections.singletonList(parent.getBdId().longValue()), req);
    }


    private String format(Integer adminId, MerchantStoreAndExtendDTO merchantExtendDTO, Contact contact) {
        String adminName = "";
        String mName = merchantExtendDTO.getStoreName();
        String addr = contact.getCity() + contact.getArea() + contact.getAddress();
        Admin admin = adminMapper.selectByPrimaryKey(adminId);
        if (admin != null) {
            adminName = admin.getRealname();
        }
        StringBuilder content = new StringBuilder();
        content.append(" 销售拜访跨区提醒：").append("\n");
        content.append("> ###### 拜访人：").append(adminName).append("\n");
        content.append("> ###### 拜访时间：").append(DateUtils.localDateTimeToString(LocalDateTime.now())).append("\n");
        content.append("> ###### 拜访客户名称：").append(mName).append("\n");
        content.append("> ###### 拜访地址：").append(addr).append("\n");
        return content.toString();
    }

    /**
     * 创建poi更新记录
     *
     * @param followRecord 跟踪记录
     * @param merchantExtendDTO     商人
     */
    public MerchantPoiUpdateRecord createPoiUpdateRecord(FollowUpRecordVO followRecord,MerchantStoreAndExtendDTO merchantExtendDTO){
        Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(followRecord.getContactId()));
        MerchantPoiUpdateRecord record=new MerchantPoiUpdateRecord();
        record.setFollowUpRecordId(Long.valueOf(followRecord.getId()))
                .setPoiBeforeChange(merchantExtendDTO.getPoiNote())
                .setAddressBeforeChange(contact.getAddress())
                .setAddressAfterChange(followRecord.getLocation())
                .setPoiAfterChange(followRecord.getPoiNote())
                .setMId(followRecord.getmId())
                .setMname(merchantExtendDTO.getStoreName())
                .setContactId(followRecord.getContactId())
                .setSalerId(getAdminId())
                .setSalerName(getAdminName())
                .setDistance(followRecord.getDistance())
                .setFeishuHeaderImageCode(followRecord.getFeishuHeaderImageCode())
                .setFeishuMerchantImageCode(followRecord.getFeishuMerchantImageCode());
        return record;
    }

    private void sendDingTalkMsg(MerchantPoiUpdateRecord poiUpdateRecord) {
        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.FOLLOW_UP_UPDATE_POI);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(getAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(poiUpdateRecord.getId());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(10);

        DingdingFormBO df0 = new DingdingFormBO();
        df0.setFormName("门店名称");
        df0.setFormValue(poiUpdateRecord.getMname());
        dingForms.add(df0);

        DingdingFormBO df1 = new DingdingFormBO();
        df1.setFormName("门店id");
        df1.setFormValue(poiUpdateRecord.getMId().toString());
        dingForms.add(df1);

        DingdingFormBO df = new DingdingFormBO();
        df.setFormName("申请销售");
        df.setFormValue(poiUpdateRecord.getSalerName());
        dingForms.add(df);

        DingdingFormBO df2 = new DingdingFormBO();
        df2.setFormName("原POI对应地址");
        df2.setFormValue(poiUpdateRecord.getAddressBeforeChange());
        dingForms.add(df2);

        DingdingFormBO df3 = new DingdingFormBO();
        df3.setFormName("更新后POI对应地址");
        df3.setFormValue(poiUpdateRecord.getAddressAfterChange());
        dingForms.add(df3);

        DingdingFormBO df4 = new DingdingFormBO();
        df4.setFormName("与原地址相差距离");
        df4.setFormValue(poiUpdateRecord.getDistance().toPlainString());
        dingForms.add(df4);

        DingdingFormBO df5 = new DingdingFormBO();
        df5.setFormName("poi距离");
        df5.setFormValue(poiUpdateRecord.getDistance().toPlainString());
        dingForms.add(df5);

        List<String> pictures = new ArrayList<>();
        if(!CollectionUtils.isEmpty(poiUpdateRecord.getFeishuMerchantImageCode())){
            pictures.addAll(poiUpdateRecord.getFeishuMerchantImageCode());
        }
        if (!StringUtils.isEmpty(poiUpdateRecord.getFeishuHeaderImageCode())){
            pictures.add(poiUpdateRecord.getFeishuHeaderImageCode());
        }
        if (!CollectionUtils.isEmpty(pictures)) {
            DingdingFormBO df6 = new DingdingFormBO();
            df6.setFormName("附加图片");
            String collect = JSONUtil.toJsonStr(pictures);
            df6.setFormValue(collect);
            dingForms.add(df6);
        }
        processInstanceCreateBO.setDingdingForms(dingForms);

        MqData mqData = new MqData();
        mqData.setBusiness(MessageBusiness.DING_DING);
        mqData.setType(MessageType.MERCHANT_POI_UPDATE);
        JSONObject msgJson = new JSONObject();
        msgJson.put("params", processInstanceCreateBO);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendDataToQueue(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST,MessageKeyEnum.DING_DING_MESSAGE,mqData);
    }

    private void updatePoi(Integer contactId, String poiNote) {
        MqData mqData = new MqData();
        mqData.setType(MessageType.UPDATE_POI);
        mqData.setBusiness(MessageBusiness.CONTACT);
        JSONObject msgJson = new JSONObject();
        msgJson.put("contactId", contactId);
        msgJson.put("poiNote", poiNote);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendDataToQueue(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST, MessageKeyEnum.UPDATE_POI, JSON.toJSONString(mqData));
    }

    /**
     * 插入拜访记录
     * @param record 拜访记录信息
     */
    private void insertFollowRecord(Integer adminId, String adminName, FollowUpRecordVO record,MerchantStoreAndExtendDTO merchantExtendDTO, Long mId) {
        record.setAdminId(adminId);
        record.setAdminName(adminName);
        record.setCreator(adminName);
        record.setAddTime(record.getAddTime() == null ? new Date() : record.getAddTime());
        record.setProvince(merchantExtendDTO.getProvince());
        record.setCity(merchantExtendDTO.getCity());
        record.setArea(merchantExtendDTO.getArea());
        record.setAreaNo(merchantExtendDTO.getAreaNo());
        if (Objects.equals(FollowRecordEnum.Type.ESCORT.ordinal(), record.getVisitType())) {
            record.setEscortVisitPlanId(record.getVisitPlanId());
            record.setVisitPlanId(null);
        }

        // 查找上次拜访记录
        // 2024-11-05后不再使用,由于有延迟插入,上次拜访记录改为实时查询
//        FollowUpRecord latestRecord = followUpRecordMapper.selectLatestRecord(mId, adminId);
//        log.info("创建拜访记录 - mId: {}, adminId: {}, latestRecord: {}", mId, adminId, latestRecord);
//        if (latestRecord != null) {
//            record.setLastVisitTime(latestRecord.getAddTime());
//            record.setLastVisitRecordId(latestRecord.getId());
//        }

        followUpRecordMapper.insertSelective(record);
        //只有拜访才钉钉通知
        if (Objects.equals(FollowRecordEnum.Type.VISIT.ordinal(), record.getVisitType())) {
            FeedBackMessageDTO feedBackMessageDTO = new FeedBackMessageDTO();
            feedBackMessageDTO.setAccountId(record.getAccountId());
            //ruf0odIjU53ZWxzMiioCeyyZjY6_Yk47aaKDN4x44hE 线上
            feedBackMessageDTO.setTemplateId(WX_PUSH_TEMPLATE_ID);
            feedBackMessageDTO.setFeedTime(DateUtil.now());
            feedBackMessageDTO.setFollowId(record.getId().longValue());
            newMQProducer.sendMessage(TOPIC_CRM_WX_OFFICIAL_PUSH, TAG_CRM_WX_FEEDBACK, feedBackMessageDTO);
        }
    }

    /**
     * 钉钉倒闭审批
     * @param merchantExtendDTO 商户信息
     * @param record 倒闭信息
     */
    private void operateStatusApproval(MerchantStoreAndExtendDTO merchantExtendDTO,FollowUpRecordVO record) {
        if(Objects.equals(MerchantOperatingStateEnum.CLOSE_DOWN.getId(),record.getOperateStatus())){
            this.createCustomerFailProcess(record.getmId(), merchantExtendDTO.getStoreName(), record.getFollowUpImage());
        }else if(Objects.equals(MerchantOperatingStateEnum.OPERATE_STATE.getId(),record.getOperateStatus())){
            // 设置店铺为正常经营
            Merchant update = new Merchant();
            update.setmId(record.getmId());
            update.setOperateStatus(record.getOperateStatus());
            update.setUpdater(getAdminId());
            merchantMapper.updateByPrimaryKeySelective(update);
        }
    }

    /**
     * 更新拜访计划
     * @param record 拜访记录信息
     */
    private void updateVisitPlan(FollowUpRecordVO record){
        VisitPlan oldPlan = visitPlanMapper.selectById(record.getVisitPlanId());
        if(Objects.nonNull(oldPlan)){
            // 更改状态
            VisitPlan updateVisitPlan = new VisitPlan();
            updateVisitPlan.setId(record.getVisitPlanId());
            updateVisitPlan.setStatus(VisitPlanEnum.Status.FINISH.ordinal());
            // 是否是拉新操作
            if(Objects.equals(VisitPlanEnum.Type.LEADS.ordinal(),oldPlan.getType())){
                updateVisitPlan.setmId(record.getmId());
                updateVisitPlan.setContactId(record.getContactId());
            }
            visitPlanMapper.updateVisitPlan(updateVisitPlan);
        }
    }

    /**
     * 新增拜访计划
     * @param record 拜访记录信息
     * @param adminId 拜访人
     * @param type 拜访类型
     */
    private void insertVisitPlan(FollowUpRecordVO record,Integer adminId,Integer type,LocalDate followTime, MerchantStoreAndExtendDTO merchantExtendDTO){
        VisitPlan nextVisitPlan = new VisitPlan();
        nextVisitPlan.setExpectedTime(LocalDateTime.of(followTime, LocalTime.MIN));
        nextVisitPlan.setStatus(VisitPlanEnum.Status.WAIT.ordinal());
        nextVisitPlan.setmId(record.getmId());
        nextVisitPlan.setAdminId(adminId);
        nextVisitPlan.setContactId(record.getContactId());
        nextVisitPlan.setCreator(getAdminName());
        nextVisitPlan.setType(type);
        nextVisitPlan.setProvince(merchantExtendDTO.getProvince());
        nextVisitPlan.setCity(merchantExtendDTO.getCity());
        nextVisitPlan.setArea(merchantExtendDTO.getArea());
        nextVisitPlan.setExpectedContent(record.getExpectedContent());
        nextVisitPlan.setAreaNo(merchantExtendDTO.getAreaNo());
        visitPlanMapper.insertVisitPlan(nextVisitPlan);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult updateRecord(FollowUpRecordVO record) {
        FollowUpRecord followUpRecord = followUpRecordMapper.selectById(record.getId());
        if (LocalDateTime.now().isAfter(DateUtils.date2LocalDate(followUpRecord.getAddTime()).atTime(6, 0))) {
            return AjaxResult.getErrorWithMsg("修改时间截止");
        }
        FollowUpRecord update = new FollowUpRecord();
        update.setContactId(record.getContactId());
        update.setId(record.getId());
        update.setFollowUpWay(record.getFollowUpWay());
        update.setCondition(record.getCondition());
        update.setStatus(record.getStatus());
        update.setFollowUpPic(record.getFollowUpPic());
        update.setVisitObjective(record.getVisitObjective());
        followUpRecordMapper.updateById(update);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectRecord(int pageIndex, int pageSize, int type , FollowUpRecordVO selectKeys) {
        selectKeys = Optional.ofNullable(selectKeys).orElse(new FollowUpRecordVO());
        boolean typeUrl = isSA() || isAreaSA() || isSaleSA();
        // 普通销售只可见自己的拜访记录
        if(!typeUrl){
            selectKeys.setAdminId(super.getAdminId());
        }
        // 处理查询条件
        if(Objects.isNull(selectKeys.getStartTime()) && Objects.isNull(selectKeys.getmId())){
            LocalDateTime atBeginningOfMonth = DateUtils.getAtBeginningOfMonth();
            selectKeys.setEndTime(BaseDateUtils.localDateTime2Date(LocalDate.now().plusDays(1L).atStartOfDay()));
            selectKeys.setStartTime(BaseDateUtils.localDateTime2Date(atBeginningOfMonth));
        }
        if(!StringUtils.isEmpty(selectKeys.getFollowUpWay())){
            String[] followUpWayList = selectKeys.getFollowUpWay().split(CrmGlobalConstant.SEPARATING_SYMBOL);
            selectKeys.setFollowUpWayList(Arrays.asList(followUpWayList));
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<FollowUpRecordVO> followUpRecordVOs = followUpRecordMapper.selectVO(selectKeys);

        List<Long> mIdList = followUpRecordVOs.stream().map(FollowUpRecord::getmId).collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIdList);
        Map<Long, MerchantStoreAndExtendResp> merchantMap = merchantExtendList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getMId, Function.identity()));

        List<Long> contactIdList = followUpRecordVOs.stream().map(FollowUpRecord::getContactId).filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList());
        List<ContactDto> merchantContactList = contactQueryFacade.getMerchantContactList(contactIdList);
        Map<Long, ContactDto> contactMap = merchantContactList.stream().collect(Collectors.toMap(Contact::getContactId, Function.identity()));

        for (FollowUpRecordVO followUpRecordVO : followUpRecordVOs) {
            this.fillMerchantKeyPerson(followUpRecordVO);
            if (merchantMap.containsKey(followUpRecordVO.getmId())) {
                MerchantStoreAndExtendResp merchantExtend = merchantMap.get(followUpRecordVO.getmId());
                followUpRecordVO.setMname(merchantExtend.getStoreName());
                MerchantSizeEnum merchantSizeEnum = ADMIN.getCode().equals(merchantExtend.getSize()) ? MAJOR_ACCOUNT : SINGGLE_STORE;
                followUpRecordVO.setmSize(merchantSizeEnum.getValue());
            }
            if (followUpRecordVO.getContactId() != null && contactMap.containsKey(followUpRecordVO.getContactId().longValue())) {
                ContactDto contactDto = contactMap.get(followUpRecordVO.getContactId().longValue());
                String address = contactDto.getCity() + contactDto.getArea() + contactDto.getAddress();
                followUpRecordVO.setAddress(StrUtil.replace(address, "null", ""));
                followUpRecordVO.setPhone(contactDto.getPhone());
            }

            // 查询上次拜访记录
            Long lastVisitRecordId = followUpRecordMapper.selectLastRecordIdById(followUpRecordVO.getId());
            if (lastVisitRecordId != null) {
                FollowUpRecord lastVisitRecord  = followUpRecordMapper.selectById(Math.toIntExact(lastVisitRecordId));
                followUpRecordVO.setLastVisitTime(lastVisitRecord.getAddTime());
                followUpRecordVO.setLastVisitRecordId(lastVisitRecord.getId());
            }

        }
        // crm上需要展示陪访人信息
        if (Objects.equals(type, NumberUtils.INTEGER_ZERO)) {
            // 筛选类型为陪访或有拜访计划或陪访计划的记录
            followUpRecordVOs.forEach(this::fillFollowUpRecord);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(followUpRecordVOs));
    }

    @Override
    public AjaxResult selectDetail(Long id) {
        /*FollowUpRecord query = new FollowUpRecordVO();
        query.setId(id.intValue());
        List<FollowUpRecordVO> followUpRecordVOList = followUpRecordMapper.selectVO(query);*/
        FollowUpRecordVO followUpRecordVO = followUpRecordRepository.getFollowUpRecordVO(id,getCompatibleDataPermission());
        if (ObjectUtils.isEmpty(followUpRecordVO)) {
            return AjaxResult.getError(ResultConstant.RECORD_NOT_EXIST);
        }
        this.fillMerchantKeyPerson(followUpRecordVO);
        // 适配旧版,不通过id关联情况下的记录
        boolean isEscort = ObjectUtil.notEqual(NumberUtils.INTEGER_ZERO, followUpRecordVO.getEscortVisitPlanId().intValue()) ||
                ObjectUtil.notEqual(NumberUtils.INTEGER_ZERO, followUpRecordVO.getEscortAdminId()) || ObjectUtil.notEqual(NumberUtils.INTEGER_ZERO, followUpRecordVO.getVisitPlanId());
        if(isEscort){
            // 填充所属bd及陪访主管姓名
            this.fillFollowUpRecord(followUpRecordVO);
        }
        FollowUpEvaluation followUpEvaluation = followUpEvaluationMapper.selectByFollowRecordId(id.intValue());
        if (!ObjectUtils.isEmpty(followUpEvaluation)){
            followUpRecordVO.setFollowUpEvaluation(followUpEvaluation);
        }
        return AjaxResult.getOK(followUpRecordVO);
    }

    /**
     * 填充被访问kp的信息
     * @param followUpRecordVO 拜访记录信息
     */
    private void fillMerchantKeyPerson(FollowUpRecordVO followUpRecordVO) {
        // 被访kp
        if(!Objects.equals(Long.valueOf(NumberUtils.INTEGER_ZERO), followUpRecordVO.getKpId())){
            MerchantKeyPerson merchantKeyPerson = merchantKeyPersonMapper.selectByPrimaryKey(followUpRecordVO.getKpId());
            String personName = Objects.isNull(merchantKeyPerson) ? "无"
                    : MerchantKeyPersonEnum.getName(merchantKeyPerson.getRole()) + "-" + merchantKeyPerson.getPersonName()
                    + "-" + merchantKeyPerson.getPhone();
            followUpRecordVO.setPersonName(personName);
        }
        followUpRecordVO.setAccountInfo("无");
        if (followUpRecordVO.getAccountId()!= null) {
            MerchantSubAccount merchantSubAccount = merchantSubAccountMapper.selectById(followUpRecordVO.getAccountId());
            if (merchantSubAccount!=null){
                followUpRecordVO.setAccountInfo(merchantSubAccount.getContact() + "-" + merchantSubAccount.getPhone());
            }
        }
    }

    /**
     * 填充陪访人的信息
     *
     * @param followUpRecordVO 拜访记录信息
     */
    private void fillFollowUpRecord(FollowUpRecordVO followUpRecordVO) {
        // 适配旧版,不通过id关联情况下的记录
        Admin admin = bdInfoExtMapper.selectByPrimaryKey(followUpRecordVO.getEscortAdminId());
        admin = Optional.ofNullable(admin).orElse(new Admin());
        followUpRecordVO.setEscortAdminName(admin.getRealname());
        String realname=null;
        // 根据陪访计划id查询陪访计划
        if (ObjectUtil.notEqual(NumberUtils.INTEGER_ZERO, followUpRecordVO.getEscortVisitPlanId().intValue())) {
            // 拜访人
            CrmEscortVisitPlan  crmEscortVisitPlan = crmEscortVisitPlanMapper.selectById(followUpRecordVO.getEscortVisitPlanId());
            if (Objects.isNull(crmEscortVisitPlan)) {
                return;
            }
            Admin visitAdmin = bdInfoExtMapper.selectByPrimaryKey(crmEscortVisitPlan.getAdminId().intValue());
            if (visitAdmin!=null){
                realname=visitAdmin.getRealname();
            }
            // 根据拜访计划id查询陪访计划
        } else if (ObjectUtil.notEqual(NumberUtils.INTEGER_ZERO, followUpRecordVO.getVisitPlanId().intValue())) {
            List<CrmEscortVisitPlan> visitPlanList = crmEscortVisitPlanMapper.selectByVisitPlanId(followUpRecordVO.getVisitPlanId());
            if (CollectionUtil.isEmpty(visitPlanList)){
                return;
            }
            StringBuffer sb=new StringBuffer();
            for (CrmEscortVisitPlan visitPlan:visitPlanList){
                Admin visitAdmin = bdInfoExtMapper.selectByPrimaryKey(visitPlan.getAdminId().intValue());
                if (visitAdmin==null){
                    continue;
                }
                sb.append(visitAdmin.getRealname()).append(",");
            }
            realname=sb.deleteCharAt(sb.length()-1).toString();
        }
        followUpRecordVO.setEscortAdminName(realname);
    }

    @Override
    public void autoSendMsg() {
        //上线后下周一不发送邮件
        if(LocalDate.now().isBefore(LocalDate.of(2021,1,5))){
            return;
        }
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(),LocalTime.MIN);
        LocalDateTime startTime = endTime.minusDays(1);
        // 填充时间筛选
        FollowUpRecordVO selectKeys = new FollowUpRecordVO();
        selectKeys.setStartTime(BaseDateUtils.localDateTime2Date(startTime));
        selectKeys.setEndTime(BaseDateUtils.localDateTime2Date(endTime));

        List<FollowUpRecordVO> followUpRecords = followUpRecordMapper.selectByStart(selectKeys);
        fillMerchantAndAreaName(followUpRecords);
        if(!CollectionUtils.isEmpty(followUpRecords)){
            Map<String, Workbook> bodyPart = new HashMap<>();
            String title = DateUtils.localDateTimeToStringTwo(startTime.toLocalDate()) + "-" + DateUtils.localDateTimeToStringTwo(endTime.toLocalDate())
                    + "拜访记录";
            bodyPart.put(title + ".xls", handleFollowMsg(followUpRecords));
            try {
//                mailUtil.sendMail(title, null, new String[]{"<EMAIL>"}, null, bodyPart);
            } catch (Exception e) {
                logger.error("销售邮件发送异常:{}", e.getMessage(), e);
            }
        }
        logger.info("发送销售gmv邮件结束:{}",LocalDateTime.now());
    }

    @Override
    public AjaxResult noteDetails(int mId,Integer couponId){
        if(Objects.isNull(couponId)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT,"参数错误,请联系管理员");
        }
        FollowUpRecord followUpRecord = followUpRecordMapper.noteDetails(mId, couponId);
        return AjaxResult.getOK(followUpRecord);
    }

    @Override
    public  AjaxResult exportFollowUpRecord(FollowUpRecordVO selectKeys){
        selectKeys = Optional.ofNullable(selectKeys).orElse(new FollowUpRecordVO());
        // 普通销售不可导出
        boolean role = isSA() || isAreaSA() || isSaleSA();
        if(!role){
            return AjaxResult.getError(ResultConstant.UNAUTHORIZED);
        }
        // 默认导出最近一周
        LocalDate start;
        if(Objects.isNull(selectKeys.getStartTime())){
            start = LocalDate.now().minusWeeks(DateUtils.ONE_DATE);
            selectKeys.setEndTime(new Date());
            selectKeys.setStartTime(BaseDateUtils.localDate2Date(start));
        }else {
            LocalDate end = BaseDateUtils.date2LocalDate(selectKeys.getEndTime());
            start = BaseDateUtils.date2LocalDate(selectKeys.getStartTime());
            // 时间跨度不可超过一月
            LocalDate oneMonthAgo = end.minusMonths(DateUtils.ONE_DATE);
            if(start.isBefore(oneMonthAgo)){
                return AjaxResult.getErrorWithMsg("仅支持导出时间跨度在一月以内的拜访记录");
            }
        }
        // 拜访方式
        if(!StringUtils.isEmpty(selectKeys.getFollowUpWay())){
            String[] followUpWayList = selectKeys.getFollowUpWay().split(CrmGlobalConstant.SEPARATING_SYMBOL);
            selectKeys.setFollowUpWayList(Arrays.asList(followUpWayList));
        }

        String uId = UUID.randomUUID().toString();
        String file = new StringBuffer()
                .append("拜访记录")
                .append(start)
                .append("至")
                .append(BaseDateUtils.date2LocalDate(selectKeys.getEndTime()))
                .append("_")
                .append(uId, 0, 5)
                .append(".xls")
                .toString();

        selectKeys.setFileName(file);
        FileDownloadRecord record = new FileDownloadRecord();
        record.setAdminId(super.getAdminId());
        record.setStatus(CrmGlobalConstant.WAIT_UPLOAD);
        record.setType(FileDownloadRecordEnum.Type.FOLLOW_UP_RECORD.ordinal());
        record.setFileName(file);
        record.setParams(JSONObject.toJSONString(selectKeys));
        record.setuId(uId);
        fileDownloadRecordMapper.insert(record);


        JSONObject msgJson = new JSONObject();
        msgJson.put("params", selectKeys);
        msgJson.put("adminId", getAdminId());
        msgJson.put("UUID", uId);
        String msg = msgJson.toJSONString();
        // 发送消息
        MqData mqData = new MqData();
        mqData.setType(MessageType.FOLLOW_UP_RECORD);
        mqData.setBusiness(MessageBusiness.EXCEL);
        mqData.setData(msg);
        producer.sendDataToQueue(RocketMqMessageConstant.CRM_LIST,MessageKeyEnum.FOLLOW_UP_RECORD, JSON.toJSONString(mqData));
        logger.info("销售{}正在下载拜访记录", getAdminName());

        return AjaxResult.getOK();
    }

    @Override
    public CommonResult exportFollowUpRecordNew(UserBase userBase, FollowUpRecordVO selectKeys) {
        selectKeys = Optional.ofNullable(selectKeys).orElse(new FollowUpRecordVO());
        // 普通销售不可导出
        boolean role = isSA() || isAreaSA() || isSaleSA();
        if(!role){
            throw new BizException("普通销售不可导出");
        }
        // 默认导出最近一周
        LocalDate start;
        if(Objects.isNull(selectKeys.getStartTime())){
            start = LocalDate.now().minusWeeks(DateUtils.ONE_DATE);
            selectKeys.setEndTime(new Date());
            selectKeys.setStartTime(BaseDateUtils.localDate2Date(start));
        }else {
            LocalDate end = BaseDateUtils.date2LocalDate(selectKeys.getEndTime());
            start = BaseDateUtils.date2LocalDate(selectKeys.getStartTime());
           // 时间跨度不可超过一月
            LocalDate oneMonthAgo = end.minusMonths(DateUtils.ONE_DATE);
            if(start.isBefore(oneMonthAgo)){
                throw new BizException("仅支持导出时间跨度在一月以内的拜访记录");
            }
        }
        // 拜访方式
        if(!StringUtils.isEmpty(selectKeys.getFollowUpWay())){
            String[] followUpWayList = selectKeys.getFollowUpWay().split(CrmGlobalConstant.SEPARATING_SYMBOL);
            selectKeys.setFollowUpWayList(Arrays.asList(followUpWayList));
        }

        String uId = UUID.randomUUID().toString();
        String file = new StringBuffer()
                .append("拜访记录")
                .append(start)
                .append("至")
                .append(BaseDateUtils.date2LocalDate(selectKeys.getEndTime()))
                .append("_")
                .append(uId, 0, 5)
                .append(".xls")
                .toString();
        selectKeys.setFileName(file);
        followUpDownloadService.exportFollowUpRecord(userBase, selectKeys);
        return CommonResult.ok();
    }

    @Override
    public Workbook handleFollowMsg(List<FollowUpRecordVO> followUpRecords){
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("拜访记录");
        int rowIndex = 0;

        Row title = sheet.createRow(rowIndex++);
        String[] titleName = {"区域", "客户名称", "填写BD","拜访方式","填写时间", "拜访目的","主管陪访","被访kp","客户生意", "门店使用产品信息",
                "下单情况", "竞对信息", "售后情况", "配送情况", "客户流失原因", "其他","打卡定位"};
        for (int i = 0; i < titleName.length; i++) {
            Cell cell = title.createCell(i);
            cell.setCellValue(titleName[i]);
        }

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.FILL);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Row row;
        for (FollowUpRecordVO vo: followUpRecords) {
            row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(vo.getAreaName());
            row.createCell(1).setCellValue(vo.getMname());
            row.createCell(2).setCellValue(vo.getAdminName());
            row.createCell(3).setCellValue(vo.getFollowUpWay());
            row.createCell(4).setCellValue(DateUtils.date2String(vo.getAddTime(),BaseDateUtils.LONG_DATE_FORMAT));
            row.createCell(5).setCellValue(FollowRecordEnum.VisitObjective.getName(vo.getVisitObjective()));
            String name = "无";
            if(!Objects.equals(NumberUtils.INTEGER_ZERO,vo.getEscortAdminId())){
                Admin admin = bdInfoExtMapper.selectByPrimaryKey(vo.getEscortAdminId());
                name = Objects.isNull(admin) ? name : admin.getRealname();
            }
            row.createCell(6).setCellValue(name);
            String kpData = "无";
            if(!Objects.equals(NumberUtils.INTEGER_ZERO,vo.getKpId().intValue())){
                MerchantKeyPerson merchantKeyPerson = merchantKeyPersonMapper.selectByPrimaryKey(vo.getKpId());
                kpData = Objects.isNull(merchantKeyPerson) ? kpData : MerchantKeyPersonEnum.getName(merchantKeyPerson.getRole())
                        + "-" + merchantKeyPerson.getPersonName() + "-" + merchantKeyPerson.getPhone();
            }
            if (!Objects.equals(Long.valueOf(NumberUtils.INTEGER_ZERO), vo.getAccountId()) && vo.getAccountId()!=null) {
                MerchantSubAccount merchantSubAccount = merchantSubAccountMapper.selectById(vo.getAccountId());
                if (merchantSubAccount!=null){
                    kpData =merchantSubAccount.getContact() + "-" + merchantSubAccount.getPhone();
                }
            }
            row.createCell(7).setCellValue(kpData);
            String condition = vo.getCondition();
            HashMap<String, String> map = handleCondition(condition);
            createCell(row.createCell(8),cellStyle,handleString(map.get("客户生意")));
            createCell(row.createCell(9),cellStyle,handleString(map.get("门店使用产品信息")));
            createCell(row.createCell(10),cellStyle,handleString(map.get("下单情况")));
            createCell(row.createCell(11),cellStyle,handleString(map.get("竞对信息")));
            createCell(row.createCell(12),cellStyle,handleString(map.get("售后情况")));
            createCell(row.createCell(13),cellStyle,handleString(map.get("配送情况")));
            createCell(row.createCell(14),cellStyle,handleString(map.get("客户流失原因")));
            createCell(row.createCell(15),cellStyle,handleString(map.get("其他")));
            row.createCell(16).setCellValue(vo.getLocation());
        }
        return workbook;
    }

    @Override
    public void sendDingMessage() {
        // 上小时拜访记录统计
        List<FollowUpRecordDingTalkDTO> recordList = new ArrayList<>();

        // 上小时拜访记录统计
        List<FollowUpRecord> followUpRecords = followUpRecordMapper.selectLastHourRecord();
        List<Long> mIdList = followUpRecords.stream().map(FollowUpRecord::getmId).collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIdList);
        Map<String, List<MerchantStoreAndExtendResp>> cityMap = merchantExtendList.stream().collect(Collectors.groupingBy(MerchantStoreAndExtendResp::getCity));
        cityMap.forEach((key, value) -> {
            List<Long> cityMid = value.stream().map(MerchantStoreResultResp::getMId).collect(Collectors.toList());

            FollowUpRecordDingTalkDTO dingTalkDTO = new FollowUpRecordDingTalkDTO();
            List<FollowUpRecord> cityFollow = followUpRecords.stream().filter(fur -> cityMid.contains(fur.getmId())).collect(Collectors.toList());
            long bdCount = cityFollow.stream().map(FollowUpRecord::getAdminId).distinct().count();
            long visitCount = cityFollow.stream().filter(cf -> cf.getVisitType() == FollowRecordEnum.Type.VISIT.ordinal()).count();
            long escortCount = cityFollow.stream().filter(cf -> cf.getVisitType() == FollowRecordEnum.Type.ESCORT.ordinal()).count();
            dingTalkDTO.setAreaName(key);
            dingTalkDTO.setTotalVisitCount(cityFollow.size());
            dingTalkDTO.setVisitCount((int) visitCount);
            dingTalkDTO.setAccompanyVisitCount((int) escortCount);
            dingTalkDTO.setBdCount((int) bdCount);
            recordList.add(dingTalkDTO);
        });


        StringBuilder textSb = new StringBuilder();
        String dateTime = DateUtil.format(DateUtil.date(), "MM月dd日 HH点");
        int followUpCount = recordList.stream().mapToInt(FollowUpRecordDingTalkDTO::getTotalVisitCount).sum();
        textSb.append("截止").append(dateTime).append("拜访记录新增").append(followUpCount).append("条，各区域拜访情况如下:\n\n");
        for (FollowUpRecordDingTalkDTO record : recordList) {
            textSb.append("【").append(record.getAreaName()).append("】 新增拜访记录").append(record.getVisitCount()).append("条，陪访记录").append(record.getAccompanyVisitCount()).append("条，拜访BD").append(record.getBdCount()).append("人").append("\n\n");
        }
        //机器人url
        Config config = configMapper.selectOne("visitPlanRobotUrl");
        // 拜访文件路径
        Config fileConfig = configMapper.selectOne("follow_up_record_download");
        textSb.append("如需查看更多拜访记录，可前往【CRM小程序或后台拜访记录】页面\n\n")
                .append(fileConfig.getValue()).append(DateUtil.format(DateUtil.date(), "yyyy-MM-ddHH:mm:ss")).append("/")
                .append(StrUtil.sub(config.getValue(),config.getValue().length()-36,-1)).append(")");


        HashMap<String, String> msgMap = new HashMap<>();
        msgMap.put("text", textSb.toString());
        msgMap.put("title", "拜访记录");

        DingTalkRobotUtil.sendMsg("markdown", config.getValue(), () -> msgMap,false,null );
    }

    @Override
    public void followUpHourRecord(HttpServletResponse response, String datetime,String token) {
        // 钉钉通知机器人url
        Config config = configMapper.selectOne("visitPlanRobotUrl");
        if (!StrUtil.sub(config.getValue(),config.getValue().length()-36,-1).equals(token)){
            throw new DefaultServiceException("参数校验失败");
        }

        // 指定时间上一个小时
        LocalDateTime dateTime = LocalDateTime.parse(datetime, DateTimeFormatter.ofPattern("yyyy-MM-ddHH:mm:ss"));
        LocalDateTime endTime= dateTime.toLocalDate().atTime(dateTime.getHour(),0,0);
        LocalDateTime startTime = endTime.toLocalDate().atTime(endTime.minusHours(1).getHour(),0,0);

        FollowUpRecordVO selectKeys = new FollowUpRecordVO();
        selectKeys.setStartTime(BaseDateUtils.localDateTime2Date(startTime));
        selectKeys.setEndTime(BaseDateUtils.localDateTime2Date(endTime));
        List<FollowUpRecordVO> followUpRecordVOs = followUpRecordMapper.selectByStart(selectKeys);
        fillMerchantAndAreaName(followUpRecordVOs);
        Workbook wb = this.handleFollowMsg(followUpRecordVOs);
        String filename = "拜访记录"+DateUtil.format(DateUtils.localDateTime2Date(startTime),"yyyy年MM月dd日HH时") +".xls";
        try {
            ExcelUtils.outputExcel(wb,filename,response);
        } catch (IOException e) {
            throw new DefaultServiceException("下载文件失败，请重试");
        }
    }

    @Override
    public CommonResult feedback(FeedBackVO feedBackVO) {
         Long followRecordId = feedBackVO.getFollowRecordId();
        FollowUpRecord followUpRecord = followUpRecordMapper.selectById(followRecordId.intValue());
        if (followUpRecord == null ||!StringUtils.isEmpty(followUpRecord.getFeedback()) ){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "拜访记录异常");
        }
        if (followUpRecord.getAddTime()!=null&& !judgmentDate(followUpRecord.getAddTime(),new Date())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "拜访反馈已经失效");
        }
        FollowUpRecord newUpdate = new FollowUpRecord();
        newUpdate.setFeedback(feedBackVO.getFeedBack());
        newUpdate.setFeedbackTime(new Date());
        newUpdate.setId(followUpRecord.getId());
        followUpRecordMapper.updateById(newUpdate);
        return CommonResult.ok();
    }

    @Override
    public CommonResult<FollowUpRecord> selectTaskFollowUp(TaskFollowUpQuery query) {
        FollowUpRecord followUpRecord = followUpRecordMapper.selectTaskFollowUp(query.getTaskId(), query.getMId());
        return CommonResult.ok(followUpRecord);
    }

    @Override
    public CommonResult<FollowUpConfigDto> followUpConfigDto(AdminTurningConfigVO adminTurningConfigVO) {
        Long mId = adminTurningConfigVO.getMId();
        FollowUpConfigDto configDto = new FollowUpConfigDto();
        if (showFruitArPuCrowdId != null && showFruitArPuCrowdId > 0) {
               configDto.setShowFruitArPuConfig(merchantPoolDetailMapper.countByMidPoolInfoID(mId, showFruitArPuCrowdId) > 0);
        }

        if (showFruitRecallCrowdId != null && showFruitRecallCrowdId > 0) {
            boolean merchantPoolFlag = merchantPoolDetailMapper.countByMidPoolInfoID(mId, showFruitRecallCrowdId) > 0;
            if (merchantPoolFlag) {
                configDto.setShowFruitRecallConfig(crmTaskDetailMapper.hasNotFinishTask(showFruitRecallTaskId,mId,CrmTaskDeatilEnum.uncompleted.getStatus()) > 0);
            } else {
                configDto.setShowFruitRecallConfig(Boolean.FALSE);
            }
        }

        if (freshFruitRegisteredNoOrderCrowdId != null && freshFruitRegisteredNoOrderCrowdId > 0) {
            boolean merchantPoolFlag = merchantPoolDetailMapper.countByMidPoolInfoID(mId, freshFruitRegisteredNoOrderCrowdId) > 0;
            if (merchantPoolFlag) {
                //任务没完成就是true 任务完成就是返回false
                configDto.setFreshFruitRegisteredNoOrderConfig(crmTaskDetailMapper.hasNotFinishTask(freshFruitRegisteredNoOrderTaskId,mId,CrmTaskDeatilEnum.uncompleted.getStatus()) > 0);
            }else {
                configDto.setFreshFruitRegisteredNoOrderConfig(Boolean.FALSE);
            }
        }
        if (freshFruitNearlyDaysNoOrderCrowdId != null && freshFruitNearlyDaysNoOrderCrowdId > 0) {
            boolean merchantPoolFlag = merchantPoolDetailMapper.countByMidPoolInfoID(mId, freshFruitNearlyDaysNoOrderCrowdId) > 0;
            if (merchantPoolFlag) {
                //任务没完成就是true 任务完成就是返回false
                configDto.setFreshFruitNearlyDaysNoOrderConfig(crmTaskDetailMapper.hasNotFinishTask(freshFruitNearlyDaysNoOrderTaskId,mId,CrmTaskDeatilEnum.uncompleted.getStatus()) > 0);
            }else {
                configDto.setFreshFruitNearlyDaysNoOrderConfig(Boolean.FALSE);
            }
        }
        if (creamPushTaskCrowdId != null && creamPushTaskCrowdId > 0) {
            boolean merchantPoolFlag = merchantPoolDetailMapper.countByMidPoolInfoID(mId, creamPushTaskCrowdId) > 0;
            if (merchantPoolFlag) {
                //任务没完成就是true 任务完成就是返回false
                configDto.setCreamPushTaskConfig(crmTaskDetailMapper.hasNotFinishTask(creamPushTaskId,mId,CrmTaskDeatilEnum.uncompleted.getStatus()) > 0);
            }else {
                configDto.setCreamPushTaskConfig(Boolean.FALSE);
            }
        }
        return  CommonResult.ok(configDto);
    }

    @Override
    public CommonResult<AdminTurningConfigDto> turningConfig(AdminTurningConfigVO adminTurningConfigVO) {
        return CommonResult.ok(turningConfigDto(adminTurningConfigVO));
    }

    @Override
    public void fillMerchantAndAreaName(List<FollowUpRecordVO> followUpRecordVOList) {
        if (CollectionUtil.isEmpty(followUpRecordVOList)) {
            return;
        }
        List<Long> mIdList = followUpRecordVOList.stream().map(FollowUpRecord::getmId).collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIdList);
        Map<Long, MerchantStoreAndExtendResp> merchantExtendMap = merchantExtendList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getMId, Function.identity()));
        if (merchantExtendList.isEmpty()) {
            return;
        }

        List<Integer> areaNoList = merchantExtendList.stream().map(MerchantStoreAndExtendResp::getAreaNo).collect(Collectors.toList());
        List<Area> areas = areaMapper.selectByAreaNoList(areaNoList);
        Map<Integer, Area> areaMap = areas.stream().collect(Collectors.toMap(Area::getAreaNo, Function.identity()));
        for (FollowUpRecordVO followUpRecord : followUpRecordVOList) {
            if (merchantExtendMap.containsKey(followUpRecord.getmId())) {
                MerchantStoreAndExtendResp merchantExtend = merchantExtendMap.get(followUpRecord.getmId());
                followUpRecord.setMname(merchantExtend.getStoreName());
                if (areaMap.containsKey(merchantExtend.getAreaNo())) {
                    followUpRecord.setAreaName(areaMap.get(merchantExtend.getAreaNo()).getAreaName());
                }
            }
        }
    }

    @Override
    public boolean hasImportFollowUpRecordPermission(Integer adminId) {
        if (adminId == null) {
            log.error("用户id为空");
            return false;
        }
        if (crmConfig.getAllowImportFollowUpRecordAdminIds().contains(adminId)) {
            log.info("用户{}在导入拜访记录白名单中，允许导入", adminId);
            return true;
        }
        List<CrmBdOrg> crmBdOrgList = bdOrgMapper.listByBdId(adminId);
        if (crmBdOrgList.isEmpty()) {
            log.info("用户非销售，不允许导入拜访记录, adminId:{}", adminId);
            return false;
        }
        CrmBdOrg topRankCrmBdOrg = crmBdOrgList.get(crmBdOrgList.size() - 1);
        if (topRankCrmBdOrg.getRank().intValue() == BdAreaConfigEnum.SaleRank.BD) {
            log.info("用户为销售BD，不允许导入拜访记录, adminId:{}", adminId);
            return false;
        }
        return true;
    }

    public AdminTurningConfigDto turningConfigDto(AdminTurningConfigVO adminTurningConfigVO){
        if (adminTurningConfigVO.getMId() != null) {
            MerchantStoreAndExtendResp merchantExtend = merchantQueryFacade.getMerchantExtendsByMid(adminTurningConfigVO.getMId());
            if (merchantExtend == null || merchantExtend.getAdminId() == null || merchantExtend.getAdminId() == 0) {
                return null;
            }
            //如果没有adminid说明是根据mid去查询的
            if (adminTurningConfigVO.getAdminId() == null) {
                adminTurningConfigVO.setAdminId(merchantExtend.getAdminId());
            }
        }
        BigCustomerPropertiesExt adminPropertiesExt = adminPropertiesExtMapper.selectByBigCustomerIdProKey(adminTurningConfigVO.getAdminId(), AdminConfigValueEnum.ADMIN_TURNING_CONFIG.getKey());
        if (adminPropertiesExt == null){
            return null;
        }
        AdminTurningConfigDto dto = new AdminTurningConfigDto();
        dto.setAdminId(adminPropertiesExt.getBigCustomerId());
        if (!StringUtils.isEmpty(adminPropertiesExt.getPropValue())){
            Integer bdId = Integer.valueOf(adminPropertiesExt.getPropValue());
            dto.setBdId(bdId);
            Map<Integer, String> bdRealNames = getBdRealNames(Collections.singletonList(bdId));
            String name = bdRealNames.get(bdId);
            dto.setBdName(name);
        }
        return dto;
    }
    private boolean judgmentDate(Date startDate, Date endDate){
        long cha = endDate.getTime() - startDate.getTime();
        if (cha < 0) {
            return false;
        }
        double result = cha * 1.0 / (1000 * 60 * 60);
        if (result <= 24) {
            return true;//是小于等于 hour 小时
        } else {
            return false;
        }
    }

    private  HashMap<String, String> handleCondition(String condition){

        HashMap<String, String> map = new HashMap<>();
        if(StringUtils.isEmpty(condition)){
            return map;
        }
        Arrays.asList(condition.split("；")).forEach(s -> {
            String[] split = s.split("-");
            if(split.length > 1){
                map.put(split[0],split[1]);
            }
        });
        return map;
    }

    private String handleString(String msg){
        return StringUtils.isEmpty(msg) ? "" : msg;
    }


    private void createCell(Cell cell,CellStyle cellStyle,String value){
        if(!StringUtils.isEmpty(value) && value.length() > 9){
            cell.setCellStyle(cellStyle);
        }
        cell.setCellValue(value);
    }

    /**
     * 发起客户倒闭审批流审批
     * @param mName 商户名
     * @param picUrl 图片路径
     */
    private void createCustomerFailProcess(Long mId,String mName,String picUrl){
        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.CUSTOMER_FAIL);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(super.getAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(mId);
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(3);
        DingdingFormBO df1 = new DingdingFormBO();
        df1.setFormName("发起人");
        df1.setFormValue(super.getAdminName());
        dingForms.add(df1);
        DingdingFormBO df2 = new DingdingFormBO();
        df2.setFormName("待确认倒闭客户名");
        df2.setFormValue(mName);
        dingForms.add(df2);
        DingdingFormBO df3 = new DingdingFormBO();
        df3.setFormName("倒闭图片");
        String[] picUrls = picUrl.split(",");
        df3.setFormValue(JSON.toJSONString(picUrls));
        dingForms.add(df3);
        processInstanceCreateBO.setDingdingForms(dingForms);

        // 发送消息至后台,发起钉钉审批
        MqData mqData = new MqData();
        mqData.setType(MessageType.CUSTOMER_BANKRUPTCY_APPROVAL);
        mqData.setBusiness(MessageBusiness.DING_DING);
        JSONObject msgJson = new JSONObject();
        msgJson.put("params", processInstanceCreateBO);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendDataToQueue(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST,MessageKeyEnum.CUSTOMER_BANKRUPTCY_APPROVAL, JSON.toJSONString(mqData));
    }

    private void updateBdVisitPlanVisitStatus(FollowUpRecordVO record) {
        try {
            // 1. 查询销售拜访计划
            LocalDate visitDate = record.getAddTime() != null ? 
                DateUtils.dateToLocalDateTime(record.getAddTime()).toLocalDate() : LocalDate.now();
            List<BdVisitPlan> bdVisitPlans = bdVisitPlanRepository.selectByMIdAndBdIdAndVisitDate(
                record.getmId(), record.getAdminId(), visitDate);
            if (CollectionUtils.isEmpty(bdVisitPlans)) {
                return;
            }
            BdVisitPlan visitPlan = bdVisitPlans.get(0);
            
            // 2 判断销售拜访计划的拜访状态，已拜访则直接返回
            if (BdVisitPlanEnum.VisitStatus.VISITED.getCode().equals(visitPlan.getVisitStatus())) {
                return;
            }
            
            // 3. 销售拜访计划的拜访类型是线下拜访时，只有"普通上门拜访"或"有效拜访"这两种拜访类型才能更新拜访状态
            if (BdVisitPlanEnum.VisitType.OFFLINE.getCode().equals(visitPlan.getVisitType())) {
                if (!"普通上门拜访".equals(record.getFollowUpWay()) && !"有效拜访".equals(record.getFollowUpWay())) {
                    return;
                }
            }
            
            // 4. 更新销售拜访计划的拜访状态和拜访记录id
            bdVisitPlanRepository.updateVisitStatusAndRecordIdByMidAndBdIdAndVisitDate(
                record.getmId(), record.getAdminId(), visitDate, 
                BdVisitPlanEnum.VisitStatus.VISITED.getCode(), record.getId().longValue(), visitPlan.getVisitStatus());
        } catch (Exception e) {
            log.error("更新销售拜访计划的拜访状态失败，mId：{}，bdId：{}，addTime:{}, 拜访记录ID：{}", record.getmId(), record.getAdminId(), record.getAddTime(), record.getId(), e);
        }
    }
}
