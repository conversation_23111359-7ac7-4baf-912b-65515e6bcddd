package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;

import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.TreeUtil;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.enums.CrmBdConfigTypeEnum;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.facade.WncQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.CrmBdMonthGmvMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.mapper.repository.CrmBdConfigRepository;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;
import net.summerfarm.crm.model.query.*;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.service.CommissionService;
import net.summerfarm.wnc.client.resp.QueryAreaLegitimacyResp;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;

/**
 * <AUTHOR>
 * @Description 佣金业务
 * @date 2022/6/14 2:00
 */
@Service
public class CommissionServiceImpl extends BaseService implements CommissionService {

    @Resource
    ConfigMapper configMapper;

    @Resource
    CrmCommissionSkuMapper crmCommissionSkuMapper;

    @Resource
    CrmCommissionMerchantMapper crmCommissionMerchantMapper;

    @Resource
    CrmBdConfigMapper crmBdConfigMapper;

    @Resource
    CrmBdConfigRepository crmBdConfigResp;

    @Resource
    CrmBdAreaMapper crmBdAreaMapper;

    @Resource
    FollowUpRelationMapper followUpRelationMapper;

    @Resource
    private WncQueryFacade wncQueryFacade;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Resource
    private CrmBdMonthGmvMapper crmBdMonthGmvMapper;

    @Resource
    private CrmCommissionCategoryMapper crmCommissionCategoryMapper;

    @Resource
    private CrmCommissionMerchantLevelMapper crmCommissionMerchantLevelMapper;

    @Resource
    private CrmCommissionCoreMerchantMapper crmCommissionCoreMerchantMapper;
    @Resource
    private CrmBdCityMapper bdCityMapper;
    /**
     * 销售org映射器
     */
    @Resource
    private CrmBdOrgMapper bdOrgMapper;

    @Override
    public AjaxResult selectAreaBd() {
        Config newAreaMonth = configMapper.selectOne(ConfigValueEnum.NEW_AREA_MONTH.getKey());
        Config newBdMonth = configMapper.selectOne(ConfigValueEnum.NEW_BD_MONTH.getKey());
        HashMap<String, Config> info = new HashMap<>(2);
        info.put("newAreaMonth",newAreaMonth);
        info.put("newBdMonth",newBdMonth);
        return AjaxResult.getOK(info);
    }

    @Override
    public AjaxResult saveAreaBd(Config config) {
        configMapper.update(config);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectRewardSku(int pageIndex, int pageSize, CommissionRewardSkuQuery commissionRewardSkuQuery) {
        PageHelper.startPage(pageIndex, pageSize);
        List<CrmCommissionSkuVo> rewardSkuList = crmCommissionSkuMapper.selectRewardSku(commissionRewardSkuQuery);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(rewardSkuList));
    }

    @Override
    public AjaxResult deleteRewardSku(int id) {
        crmCommissionSkuMapper.deleteByPrimaryKey(id);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveRewardSku(CrmCommissionSkuVo crmCommissionSkuVo) {

        int existCrm = crmCommissionSkuMapper.exist(crmCommissionSkuVo);
        if(existCrm > 0){
            return AjaxResult.getError(ResultConstant.SKU_ERROR,crmCommissionSkuVo.getSku()+"已存在，不可重复提交");
        }

        CrmCommissionSku crmCommissionSku = new CrmCommissionSku();
        BeanUtils.copyProperties(crmCommissionSkuVo,crmCommissionSku);
        //获取当前用户ID
        Integer adminId = getAdminId();
        crmCommissionSku.setUpdater(adminId);
        crmCommissionSku.setCreator(adminId);
        crmCommissionSkuMapper.insertOrUpdateById(crmCommissionSku);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult copyRewardSku(CopyInfoQuery copyInfoQuery) {
        //取当前用户
        Integer adminId = getAdminId();
        //被复制按件奖励信息
        List<String> existRewardSku = crmCommissionSkuMapper.isExistAreaSkuInfo(copyInfoQuery);

        if(CollectionUtil.isNotEmpty(existRewardSku)){
            return AjaxResult.getError(ResultConstant.SKU_ERROR, "sku:"+StringUtils.join(existRewardSku,",")+"已存在，不可重复提交");
        }

        List<String> areaSkuInfo = copyInfoQuery.getInfo();
        for (String skuInfo : areaSkuInfo) {
            crmCommissionSkuMapper.copyAreaSku(copyInfoQuery.getCopyInfo(),skuInfo,adminId);
        }

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectPullNewMerchantReward(int pageIndex, int pageSize, String zoneName) {
        PageHelper.startPage(pageIndex,pageSize);
        List<CrmCommissionMerchantVo> info =  crmCommissionMerchantMapper.selectPullNewMerchantReward(zoneName);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(info));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deletePullNewMerchantReward(int id) {
        crmCommissionMerchantMapper.deleteByPrimaryKey(id);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult savePullNewMerchantReward(CrmCommissionMerchantVo crmCommissionMerchantVo) {
        Integer adminId = getAdminId();
        CrmCommissionMerchant crmCommissionMerchant = new CrmCommissionMerchant();
        BeanUtils.copyProperties(crmCommissionMerchantVo,crmCommissionMerchant);
        crmCommissionMerchant.setUpdater(adminId);
        crmCommissionMerchant.setCreator(adminId);
        crmCommissionMerchantMapper.insertOrUpdateById(crmCommissionMerchant);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult copyPullNewMerchantReward(CopyInfoQuery copyInfoQuery) {
        Integer adminId = getAdminId();
        List<String> infos = copyInfoQuery.getInfo();
        for (String info : infos) {
            crmCommissionMerchantMapper.copyMerchant(copyInfoQuery.getCopyInfo(),info,adminId);
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult batchModifyPullNewMerchantReward(BatchModifyMerchantQuery batchModifyMerchantQuery) {
        batchModifyMerchantQuery.setUpdater(getAdminId());
        batchModifyMerchantQuery.setUpdateTime(LocalDateTime.now());
        crmCommissionMerchantMapper.updateByPrimaryKeySelective(batchModifyMerchantQuery);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectIncentiveIndex(int pageIndex, int pageSize, CommissionIncentiveIndexQuery commissionIncentiveIndexQuery) {
        PageHelper.startPage(pageIndex,pageSize);
        List<CrmBdConfigVo> crmBdConfigVos = crmBdConfigMapper.selectIncentiveIndex(commissionIncentiveIndexQuery);
        for (CrmBdConfigVo crmBdConfigVo : crmBdConfigVos) {
            // 销售负责运营区域
            List<CrmBdAreaDTO> bdArea = crmBdConfigMapper.selectBdArea(crmBdConfigVo.getAdminId());
            crmBdConfigVo.setAreaCity(bdArea);
            List<TableArea> areaCities = crmBdConfigMapper.selectBdAreaCombined(crmBdConfigVo.getAdminId());
            crmBdConfigVo.setAreaCities(areaCities);
            List<QueryAreaLegitimacyResp> crmBdConfigLegitimacyList = wncQueryFacade.checkAreaLegitimacy(Collections.singletonList(crmBdConfigVo.getAdministrativeCity()));
            if(CollectionUtils.isEmpty(crmBdConfigLegitimacyList)){
                crmBdConfigVo.setAdministrativeCity(CrmGlobalConstant.ADD_CITY);
            }
            if (!CollectionUtils.isEmpty(crmBdConfigLegitimacyList)){
                crmBdConfigVo.setAdministrativeCitys(Arrays.asList(crmBdConfigLegitimacyList.get(0).getProvince(), crmBdConfigLegitimacyList.get(0).getCity()));
            }
            List<CrmBdCity> crmBdCities = bdCityMapper.selectByBdId(crmBdConfigVo.getAdminId());
            crmBdConfigVo.setBdCities(crmBdCities);
            // 生成省市区及联数据
            Map<String, List<AdministrativeRegionVo>> regionMap = crmBdCities.stream().map(b -> {
                AdministrativeRegionVo region = new AdministrativeRegionVo();
                region.setProvince(b.getProvince());
                region.setCity(b.getCity());
                region.setArea(b.getArea());
                return region;
            }).collect(Collectors.groupingBy(AdministrativeRegionVo::getProvince));
            List<TableArea> tableArea= TreeUtil.createTableArea(regionMap);
            crmBdConfigVo.setTableArea(tableArea);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(crmBdConfigVos));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteIncentiveIndex(int id) {
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByPrimaryKey(id);
        if(Objects.isNull(crmBdConfig)){
            return AjaxResult.getErrorWithMsg("请不要重复删除!刷新一下试试看");
        }
        crmBdConfigMapper.deleteByPrimaryKey(id);
        crmBdAreaMapper.deleteByAdminId(crmBdConfig.getAdminId());
        FollowUpRelation followUpRelation = new FollowUpRelation();

        followUpRelation.setReassign(false);
        followUpRelation.setReason("主管分配");
        followUpRelation.setAdminId(crmBdConfig.getAdminId());
        followUpRelation.setReassignTime(LocalDateTime.now());
        //将该用户下的所有私海释放
        followUpRelation.setReassign(true);
        followUpRelationMapper.updateReassignByAdminId(followUpRelation);

        // 删除区域配置
        bdOrgMapper.deleteByBdIdAndRank(crmBdConfig.getAdminId(),BD);

        bdCityMapper.deleteByBdId(crmBdConfig.getAdminId());
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveIncentiveIndex(CrmBdConfigVo crmBdConfigVo) {
        if (CollectionUtils.isEmpty(crmBdConfigVo.getAreaCityIds())){
            return AjaxResult.getError(String.valueOf(ResultStatusEnum.SERVER_ERROR),"请选择城市");
        }
        // 页面传值,城市
        List<Integer> inputArea = crmBdConfigVo.getAreaCityIds();
        Set<Integer> areaCityIds = new HashSet<>(inputArea);

        // 调用manage,判断行政城市是否在围栏
        List<String> administrativeCityList = crmBdConfigVo.getAdministrativeCitys();
        String administrativeCity = administrativeCityList.get(1);
        List<String> cityList = Collections.singletonList(administrativeCity);
        List<QueryAreaLegitimacyResp> crmBdConfigLegitimacyList =  wncQueryFacade.checkAreaLegitimacy(cityList);
        if(CollectionUtils.isEmpty(crmBdConfigLegitimacyList)){
            return AjaxResult.getError(String.valueOf(ResultStatusEnum.SERVER_ERROR),"所属行政城市不在围栏中,请更换城市或设置围栏!");
        }

        // 判断负责城市是否在围栏
        List<String> bcCities = crmBdConfigVo.getBdCities().stream().map(CrmBdCity::getCity).distinct().collect(Collectors.toList());
        List<QueryAreaLegitimacyResp> bcCitiesLegitimacyList = wncQueryFacade.checkAreaLegitimacy(bcCities);
        if(CollectionUtils.isEmpty(bcCitiesLegitimacyList) || bcCities.size() != bcCitiesLegitimacyList.size()){
            return AjaxResult.getError(String.valueOf(ResultStatusEnum.SERVER_ERROR),"所属行政城市不在围栏中,请更换城市或设置围栏!");
        }

        crmBdConfigVo.setAdministrativeCity(administrativeCity);
        Integer adminId = super.getAdminId();
        //如果type类型为2时，取上月核心客户数作为基础值
        if(Objects.equals(CrmBdConfigTypeEnum.LAST_MONTH.ordinal(),crmBdConfigVo.getType())){
            int coreNum = this.getCoreNum(crmBdConfigVo.getAdminId());
            crmBdConfigVo.setCoreMerchantAmount(coreNum);
        }
        CrmBdConfig crmBdConfig = new CrmBdConfig();
        BeanUtils.copyProperties(crmBdConfigVo,crmBdConfig);
        crmBdConfig.setUpdater(adminId);

        // 更新上级信息
        if (crmBdConfigVo.getParentId() != null) {
            CrmBdOrg crmBdOrg = bdOrgMapper.selectByBdIdAndRank(crmBdConfigVo.getAdminId(), BD);
            CrmBdOrg parent = bdOrgMapper.selectByPrimaryKey(crmBdConfigVo.getParentId());
            if (parent == null) {
                return AjaxResult.getErrorWithMsg("上级不存在,请刷新后重试");
            }
            if (crmBdOrg == null) {
                CrmBdOrg org = new CrmBdOrg();
                org.setBdId(crmBdConfigVo.getAdminId()).setBdName(crmBdConfigVo.getAdminName()).setParentId(crmBdConfigVo.getParentId()).setParentName(parent.getBdName()).setRank(BD);
                bdOrgMapper.insertSelective(org);
            } else if (!ObjectUtil.equal(crmBdOrg.getParentId(), crmBdConfigVo.getParentId())) {
                crmBdOrg.setParentId(crmBdConfigVo.getParentId());
                crmBdOrg.setParentName(parent.getBdName());
                bdOrgMapper.updateByPrimaryKeySelective(crmBdOrg);
            }
        }

        //新增
        if(Objects.isNull(crmBdConfigVo.getId())){
            CrmBdConfig bdConfig = crmBdConfigMapper.selectByAdminId(crmBdConfigVo.getAdminId());
            if(Objects.nonNull(bdConfig)){
                return AjaxResult.getErrorWithMsg("销售已存在激励指标,请勿重复添加");
            }
            crmBdConfig.setCreator(adminId);
            crmBdConfigMapper.insertSelective(crmBdConfig);
            // 批量插入城市信息
            crmBdAreaMapper.insertBatch(areaCityIds,crmBdConfigVo.getAdminId(),adminId);
            bdCityMapper.insertBatch(crmBdConfigVo.getBdCities(),crmBdConfigVo.getAdminId(),crmBdConfigVo.getAdminName(),getAdminName());
        }else{
            List<Integer> info = crmBdAreaMapper.selectByAdminId(crmBdConfigVo.getAdminId());
            crmBdConfigMapper.updateByPrimaryKey(crmBdConfig);
            crmBdAreaMapper.deleteByAdminId(crmBdConfigVo.getAdminId());
            crmBdAreaMapper.insertBatch(areaCityIds,crmBdConfigVo.getAdminId(),adminId);
            //将删除的城市所关联的私海删除
            info.removeAll(areaCityIds);
            FollowUpRelation followUpRelation = new FollowUpRelation();
            followUpRelation.setReassign(true);
            followUpRelation.setReason("主管分配");
            followUpRelation.setAdminId(crmBdConfig.getAdminId());
            followUpRelation.setReassignTime(LocalDateTime.now());
            if(!info.isEmpty()){
                // 将bd被删除区域的私海客户释放
                followUpRelationMapper.updateReassignByAdminIdArea(crmBdConfigVo.getAdminId(),info,followUpRelation);
            }
            // 插入负责城市
            if (CollectionUtil.isNotEmpty(crmBdConfigVo.getBdCities())){
                bdCityMapper.deleteByBdId(crmBdConfigVo.getAdminId());
                bdCityMapper.insertBatch(crmBdConfigVo.getBdCities(),crmBdConfigVo.getAdminId(),crmBdConfigVo.getAdminName(),getAdminName());
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult copyIncentiveIndex(CopyIntInfoQuery copyIntInfoQuery) {
        Integer adminId = getAdminId();
        Integer intInfos = copyIntInfoQuery.getIntInfo();
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(copyIntInfoQuery.getCopyIntInfo());
        if(Objects.isNull(crmBdConfig)){
            return AjaxResult.getErrorWithMsg("被复制人不存在激励指标,请为他添加后重试");
        }
        //如果被复制人插入时也是使用上月的数据,那新增销售也将取个人上月数据作为基础值
        if(Objects.equals(CrmBdConfigTypeEnum.LAST_MONTH.ordinal(),crmBdConfig.getType())){
            int coreNum = this.getCoreNum(intInfos);
            crmBdConfigMapper.copyIncentiveIndexLastMonthGmv(copyIntInfoQuery.getCopyIntInfo(),intInfos,adminId,coreNum);
        }else{
            crmBdConfigMapper.copyIncentiveIndex(copyIntInfoQuery.getCopyIntInfo(),intInfos,adminId);
        }
        // 复制城市信息
        crmBdAreaMapper.copyAreaByAdminId(copyIntInfoQuery.getCopyIntInfo(),intInfos,adminId);
        // 复制负责城市
        bdCityMapper.copyCityByAdminId(copyIntInfoQuery.getCopyIntInfo(),intInfos,copyIntInfoQuery.getName(),getAdminName());
        // 被复制人信息
        CrmBdOrg crmBdOrg = bdOrgMapper.selectByBdIdAndRank(copyIntInfoQuery.getCopyIntInfo(), BD);
        if (crmBdOrg!=null){
            CrmBdOrg org = new CrmBdOrg();
            org.setBdId(copyIntInfoQuery.getIntInfo()).setBdName(copyIntInfoQuery.getName()).setParentId(crmBdOrg.getParentId()).setParentName(crmBdOrg.getParentName()).setRank(BD);
            bdOrgMapper.insertSelective(org);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult batchModifyIncentiveIndex(BatchModifyIncentiveIndexQuery batchModifyIncentiveIndexQuery) {
        Integer type = batchModifyIncentiveIndexQuery.getType();
        batchModifyIncentiveIndexQuery.setUpdater(getAdminId());
        batchModifyIncentiveIndexQuery.setUpdateTime(LocalDateTime.now());
        //如果type类型为2时，取上月核心客户数作为基础值
        if(Objects.equals(CrmBdConfigTypeEnum.LAST_MONTH.ordinal(),type)){
            List<Integer> adminIds = batchModifyIncentiveIndexQuery.getId();
            for (Integer adminId : adminIds) {
                int coreNum = this.getCoreNum(adminId);
                batchModifyIncentiveIndexQuery.setCoreMerchantAmount(coreNum);
                batchModifyIncentiveIndexQuery.setAdminId(adminId);
                crmBdConfigMapper.updateByAdminId(batchModifyIncentiveIndexQuery);
            }
        }else{
            crmBdConfigMapper.updateByPrimaryKeySelective(batchModifyIncentiveIndexQuery);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult queryBdName(Boolean isExist, String bdName) {
        Config crmAdminAuth = configMapper.selectOne(ConfigValueEnum.CRM_ADMIN_AUTH.getKey());
        String[] split = crmAdminAuth.getValue().split(CrmGlobalConstant.SEPARATING_SYMBOL);
        List<Long> roleIds = Arrays.stream(split).map(Long::valueOf).collect(Collectors.toList());
        List<BdVO> info = crmBdConfigResp.queryBdName(isExist, bdName, roleIds);
        return AjaxResult.getOK(info);
    }

    @Override
    public AjaxResult selectNumLastMonth(Integer adminId) {
        int coreNum = this.getCoreNum(adminId);
        return AjaxResult.getOK(coreNum);
    }

    @Override
    public AjaxResult selectGmvTarget() {
        // 获取系数
        Config commissionCoefficient = configMapper.selectOne(CrmGlobalConstant.COMMISSION_COEFFICIENT);
        Config targetOfMonthLiving = configMapper.selectOne(CrmGlobalConstant.TARGET_MONTH_LIVING);
        Config targetOfGmv = configMapper.selectOne(CrmGlobalConstant.TARGET_GMV);
        // 返回值信息
        GmvTargetVO gmvTargetVO = new GmvTargetVO();
        gmvTargetVO.setCommissionCoefficient(commissionCoefficient.getValue());
        gmvTargetVO.setTargetOfMonthLiving(targetOfMonthLiving.getValue());
        gmvTargetVO.setTargetOfGmv(targetOfGmv.getValue());

        return AjaxResult.getOK(gmvTargetVO);
    }

    @Override
    public AjaxResult saveGmvTarget(Config config) {
        // 更新gmv奖励系数
        if(Objects.isNull(config)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        configMapper.update(config);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectCategoryAward() {
        // 获取品类系数奖励
        List<CrmCommissionCategory> crmCommissionCategories = crmCommissionCategoryMapper.selectAll();
        return AjaxResult.getOK(crmCommissionCategories);
    }

    @Override
    public AjaxResult saveCategoryAward(CrmCommissionCategory crmCommissionCategory) {
        if(Objects.isNull(crmCommissionCategory)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        // 插入或更新品类系数奖励
        crmCommissionCategory.setCreateName(super.getAdminName());
        crmCommissionCategory.setUpdateName(super.getAdminName());
        crmCommissionCategoryMapper.insertOrUpdateById(crmCommissionCategory);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectCoreMerchant(Integer merchantLevelType, String grade) {
        // 获取商户等级系数
        List<CrmCommissionMerchantLevel> crmCommissionMerchantLevels =  crmCommissionMerchantLevelMapper.selectAllCoreMerchantLevel(merchantLevelType,grade);
        return AjaxResult.getOK(crmCommissionMerchantLevels);
    }

    @Override
    public AjaxResult saveCoreMerchant(CrmCommissionMerchantLevel crmCommissionMerchantLevel) {
        if(Objects.isNull(crmCommissionMerchantLevel)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        // 商户等级gmv及客单价区间最高值不能低于最低值
        boolean isNotDelete = Objects.isNull(crmCommissionMerchantLevel.getDeleteFlag());
        if(isNotDelete && !NumberUtils.INTEGER_ONE.equals(crmCommissionMerchantLevel.getMerchantLevelType())){
            boolean isGmvSmaller = crmCommissionMerchantLevel.getGmvMaximum().compareTo(crmCommissionMerchantLevel.getGmvMinimum()) <= NumberUtils.INTEGER_ZERO;
            boolean isPriceSmaller = crmCommissionMerchantLevel.getPriceMaximum().compareTo(crmCommissionMerchantLevel.getPriceMinimum()) <= NumberUtils.INTEGER_ZERO;
            if(isGmvSmaller || isPriceSmaller){
                return AjaxResult.getErrorWithMsg("商户等级区间中最大值必须大于最小值");
            }
        }
        // 插入或更新商户等级系数
        crmCommissionMerchantLevel.setCreateName(super.getAdminName());
        crmCommissionMerchantLevel.setUpdateName(super.getAdminName());
        crmCommissionMerchantLevelMapper.insertOrUpdateById(crmCommissionMerchantLevel);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectCoreMerchantsNetGrowth() {
        // 获取核心客户净增长数牌级
        List<CrmCommissionCoreMerchant> crmCommissionCoreMerchants = crmCommissionCoreMerchantMapper.selectCoreMerchantsNetGrowth();
        return AjaxResult.getOK(crmCommissionCoreMerchants);
    }

    @Override
    public AjaxResult saveCoreMerchantsNetGrowth(CrmCommissionCoreMerchant crmCommissionCoreMerchant) {
        if(Objects.isNull(crmCommissionCoreMerchant)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        boolean isNotDelete = Objects.isNull(crmCommissionCoreMerchant.getDeleteFlag());
        if(isNotDelete){
            // 最高值不能低于最低值
            boolean isSmaller = crmCommissionCoreMerchant.getMaximum().compareTo(crmCommissionCoreMerchant.getMinimum()) <= NumberUtils.INTEGER_ZERO;
            if(isSmaller){
                return AjaxResult.getErrorWithMsg("净增长数区间最大值必须大于最小值");
            }
        }
        // 插入或更新核心客户净增长数牌级
        crmCommissionCoreMerchant.setCreateName(super.getAdminName());
        crmCommissionCoreMerchant.setUpdateName(super.getAdminName());
        crmCommissionCoreMerchantMapper.insertOrUpdateById(crmCommissionCoreMerchant);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectMonthLivingCouponQuota() {
        Config config = configMapper.selectOne(ConfigValueEnum.MONTHLY_LIVING_POOL_QUOTA.getKey());
        return AjaxResult.getOK(config.getValue());
    }

    @Override
    public AjaxResult updateMonthLivingCouponQuota(String monthLivingCouponQuota) {
        try {
            new BigDecimal(monthLivingCouponQuota);
        } catch (NullPointerException e) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        configMapper.updateValue(ConfigValueEnum.MONTHLY_LIVING_POOL_QUOTA.getKey(),monthLivingCouponQuota);
        return AjaxResult.getOK();
    }

    @Override
    public void updateMonthLivingCouponQuotaEveryMonth() {
        BigDecimal bigDecimal = BigDecimal.ZERO;
        configMapper.updateValue(ConfigValueEnum.MONTHLY_LIVING_POOL_QUOTA.getKey(),bigDecimal.toPlainString());
        logger.info("月活池额度更新结束:{}",LocalDateTime.now());
    }

    @Override
    public List<TableArea> selectBdCities() {
        List<CrmBdCity> crmBdCities = bdCityMapper.selectByBdId(getAdminId());
        return TreeUtil.createTableArea(crmBdCities.stream().map(b -> {
            AdministrativeRegionVo region = new AdministrativeRegionVo();
            region.setProvince(b.getProvince());
            region.setCity(b.getCity());
            region.setArea(b.getArea());
            return region;
        }).collect(Collectors.groupingBy(AdministrativeRegionVo::getProvince)));
    }

    /**
     * 获取上月核心客户数
     */
    private int getCoreNum(Integer adminId){
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_MONTH_GMV.getTableName());
        Integer dateFlag = Objects.isNull(lastMonth) ? NumberUtils.INTEGER_ZERO : lastMonth.getDateFlag();
        AdminInfoVo adminInfoVo = crmBdMonthGmvMapper.selectByAdminId(adminId, dateFlag);
        return Objects.isNull(adminInfoVo) ? NumberUtils.INTEGER_ZERO : adminInfoVo.getCoreMerchantNum();
    }

}
