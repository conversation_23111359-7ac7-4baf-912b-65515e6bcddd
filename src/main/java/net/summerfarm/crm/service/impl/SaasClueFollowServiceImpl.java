package net.summerfarm.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.mapper.manage.CrmClueMapper;
import net.summerfarm.crm.mapper.manage.CrmFollowMapper;
import net.summerfarm.crm.model.convert.CrmFollowConverter;
import net.summerfarm.crm.model.domain.CrmClue;
import net.summerfarm.crm.model.domain.CrmFollow;
import net.summerfarm.crm.model.dto.CrmFollowDTO;
import net.summerfarm.crm.model.query.ClueDetailQuery;
import net.summerfarm.crm.model.vo.CreateCrmFollowVO;
import net.summerfarm.crm.service.SaasClueFollowService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SaasClueFollowServiceImpl extends BaseService implements SaasClueFollowService {
    static final String CLUE = "clue";

    @Resource
    private CrmFollowMapper crmFollowMapper;

    @Resource
    private CrmClueMapper clueMapper;

    @Override
    public CommonResult<PageInfo<CrmFollowDTO>> queryFollow(ClueDetailQuery cluClueQuery) {
        cluClueQuery.setType(CLUE);
        PageHelper.startPage(cluClueQuery.getPageIndex(), cluClueQuery.getPageSize());
        List<CrmFollow> crmFollows = crmFollowMapper.query(cluClueQuery);
        PageInfo<CrmFollow> pageInfo = PageInfoHelper.createPageInfo(crmFollows);
        PageInfo<CrmFollowDTO> crmFollowDTOPageInfo = PageInfoConverter.toPageResp(pageInfo, CrmFollowConverter::toCrmFollowDTO);
        List<Integer> bdIds = crmFollowDTOPageInfo.getList().stream().map(CrmFollowDTO::getBdId).collect(Collectors.toList());
        Map<Integer, String> bdRealNames = getBdRealNames(bdIds);
        crmFollowDTOPageInfo.getList().forEach(
                it -> it.setBdName(bdRealNames.get(it.getBdId()))
        );
        return CommonResult.ok(crmFollowDTOPageInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CrmFollowDTO> create(CreateCrmFollowVO createCrmFollowVO, UserBase userBase) {
        CrmFollow crmFollow = new CrmFollow();
        crmFollow.setCreateTime(LocalDateTime.now());
        crmFollow.setUpdateTime(LocalDateTime.now());
        crmFollow.setFollowTime(LocalDateTime.now());
        crmFollow.setType(CLUE);
        crmFollow.setBdId(userBase.getBizUserId());
        crmFollow.setCustomerFeedback(createCrmFollowVO.getCustomerFeedback());
        crmFollow.setImages(createCrmFollowVO.getImages());
        crmFollow.setFollowGoal(createCrmFollowVO.getFollowGoal());
        crmFollow.setFollowModel(createCrmFollowVO.getFollowModel());
        crmFollow.setSubjectId(createCrmFollowVO.getSubjectId());
        crmFollow.setNextFollow(createCrmFollowVO.getNextFollow());
        crmFollowMapper.insertSelective(crmFollow);
        CrmClue crmClue = new CrmClue();
        crmClue.setLastFollowTime(LocalDateTime.now());
        crmClue.setId(createCrmFollowVO.getSubjectId());
        clueMapper.updateByPrimaryKeySelective(crmClue);
        return CommonResult.ok(CrmFollowConverter.toCrmFollowDTO(crmFollow));
    }
}
