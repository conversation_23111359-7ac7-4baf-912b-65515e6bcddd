package net.summerfarm.crm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.enums.FollowUpRelationEnum;
import net.summerfarm.crm.enums.KeyCustomerEnum;
import net.summerfarm.crm.es.mapper.XianmuMerchantCrmMapper;
import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import net.summerfarm.crm.mapper.manage.FollowUpRecordMapper;
import net.summerfarm.crm.model.dto.keycustomer.KeyCustomer;
import net.summerfarm.crm.model.dto.keycustomer.KeyCustomerCount;
import net.summerfarm.crm.model.dto.keycustomer.KeyCustomerTypeCount;
import net.summerfarm.crm.model.query.keycustomer.KeyCustomerCountQuery;
import net.summerfarm.crm.model.query.keycustomer.KeyCustomerQuery;
import net.summerfarm.crm.service.KeyCustomerService;
import net.xianmu.common.exception.BizException;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.constant.CrmGlobalConstant.REGISTER_NO_ORDER_30_DAY;

@Service
@Slf4j
public class KeyCustomerServiceImpl implements KeyCustomerService {

    private static final LocalDate NONE_REASSIGN_FOREVER_DATE = LocalDate.of(9999, 12, 31);

    @NacosValue(value = "${follow.up.relation.reclaimable.days:15}", autoRefreshed = true)
    private Integer reclaimableDays;
    @NacosValue(value = "${follow.up.relation.key.danger.day:29}", autoRefreshed = true)
    private Integer KEY_DANGER_DAY;

    /**
     * 首单客户显示的最大掉落日期
     */
    @NacosValue(value = "${max.first.time.buyer.danger.day:30}", autoRefreshed = true)
    private Integer maxFirstTimeBuyerDangerDay;

    /**
     * 公转私客户显示的最大掉落日期
     */
    @NacosValue(value = "${max.open.to.private.danger.day:30}", autoRefreshed = true)
    private Integer maxOpenToPrivateDangerDay;

    @Resource
    private BaseService baseService;
    @Resource
    private XianmuMerchantCrmMapper xianmuMerchantCrmMapper;
    @Resource
    private FollowUpRecordMapper followUpRecordMapper;
    @Autowired
    private CrmConfig crmConfig;


    @Override
    public KeyCustomerCount countKeyCustomer(KeyCustomerCountQuery query) {
        List<KeyCustomerTypeCount> typeCounts = Arrays.stream(KeyCustomerEnum.Type.values())
                .map(type -> {
                    KeyCustomerQuery keyCustomerQuery = new KeyCustomerQuery();
                    BeanUtil.copyProperties(query, keyCustomerQuery);
                    keyCustomerQuery.setType(type.getCode());
                    LambdaEsQueryWrapper<XianmuMerchantCrm> queryWrapper = this.buildQueryWrapper(keyCustomerQuery);

                    Long count = xianmuMerchantCrmMapper.selectCount(queryWrapper);
                    return new KeyCustomerTypeCount(type.getCode(), Math.toIntExact(count));
                }).collect(Collectors.toList());

        int total = typeCounts.stream().mapToInt(KeyCustomerTypeCount::getCount).sum();
        return new KeyCustomerCount(total, typeCounts);
    }

    @Override
    public PageInfo<KeyCustomer> pageKeyCustomer(KeyCustomerQuery query) {
        // 从es中查询重点客户详情
        LambdaEsQueryWrapper<XianmuMerchantCrm> queryWrapper = this.buildQueryWrapper(query);
        EsPageInfo<XianmuMerchantCrm> esPageInfo = xianmuMerchantCrmMapper.pageQuery(queryWrapper, query.getPageIndex(), query.getPageSize());
        if (CollectionUtil.isEmpty(esPageInfo.getList())) {
            return new PageInfo<>(Collections.emptyList());
        }

        // 设置展示的备注标签
        List<KeyCustomer> keyCustomers = esPageInfo.getList().stream().map(it -> {
            KeyCustomer keyCustomer = new KeyCustomer();
            BeanUtil.copyProperties(it, keyCustomer);

            switch (KeyCustomerEnum.Type.getByCode(query.getType())) {
                case REGISTER_UNORDERED:
                    long registerDays = ChronoUnit.DAYS.between(it.getRegisterTime(), LocalDateTime.now());
                    keyCustomer.setRemark(String.format("已注册: %s天", registerDays));
                    break;
                case RECLAIMABLE_AFTER_ON_SITE:
                    LocalDateTime reclaimableDdl = it.getReassignTime().plusDays(reclaimableDays);
                    keyCustomer.setRemark(String.format("%s前可捞回", DateUtil.format(reclaimableDdl, "MM月dd号HH:mm")));
                    break;
                default:
                    LocalDateTime releaseTime = it.getReleaseTime();
                    if (releaseTime == null && it.getDangerDay() != null) {
                        log.warn("查询重点客户缺少释放时间，使用dangerDay兜底，mId:{}", it.getMId());
                        releaseTime = LocalDate.now().plusDays(it.getDangerDay()).atTime(crmConfig.getPrivateSeaReassignTime());
                    }
                    if (releaseTime != null) {
                        if (NONE_REASSIGN_FOREVER_DATE.equals(releaseTime.toLocalDate())) {
                            keyCustomer.setRemark("永不掉落");
                        } else {
                            keyCustomer.setRemark(DateUtil.format(releaseTime, "M月d号H:mm掉落"));
                        }
                    } else {
                        log.error("\n查询重点客户缺少释放时间，先默认永不掉落，mId:{}\n", it.getMId());
                        keyCustomer.setRemark("永不掉落");
                    }
                    break;
            }

            // 查询上次拜访时间
            Optional.ofNullable(it.getBdId()) // 查询当前私海销售的拜访记录
                    .flatMap(bdId ->
                            Optional.ofNullable(followUpRecordMapper.selectLatestRecord(it.getMId())))
                    .ifPresent(followUpRecord -> keyCustomer.setLastVisitTimeByCurrentBd(followUpRecord.getAddTime()));
            return keyCustomer;
        }).collect(Collectors.toList());

        PageInfo<KeyCustomer> pageInfo = new PageInfo<>();
        BeanUtil.copyProperties(esPageInfo, pageInfo);
        pageInfo.setList(keyCustomers);
        return pageInfo;
    }

    // --------------------------------------------- private methods ---------------------------------------------

    private LambdaEsQueryWrapper<XianmuMerchantCrm> buildQueryWrapper(KeyCustomerQuery query) {
        // 非主管查看自己的客户
        if (!(baseService.isSA() || baseService.isSaleSA() || baseService.isAreaSA())) {
            query.setBdId(baseService.getAdminId());
        }

        KeyCustomerEnum.Type type = KeyCustomerEnum.Type.getByCode(query.getType());

        LambdaEsQueryWrapper<XianmuMerchantCrm> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.eq(XianmuMerchantCrm::getProvince, query.getProvince());
        queryWrapper.eq(XianmuMerchantCrm::getCity, query.getCity());
        // 只查询审核通过的客户
        queryWrapper.eq(XianmuMerchantCrm::getIslock, 0);

        // 15天内可捞回的客户.本质就是15天内掉落到公海且"上次跟进销售"是当前销售的客户
        if (Objects.equals(KeyCustomerEnum.Type.RECLAIMABLE_AFTER_ON_SITE, type)) {
            queryWrapper.eq(XianmuMerchantCrm::getBdId, 0); // 在公海里的
            Optional.ofNullable(query.getBdId()).ifPresent(bdId ->
                    queryWrapper.eq(XianmuMerchantCrm::getLastFollowUpBdId, bdId)); // 上次跟进销售是当前销售
            queryWrapper.gt(XianmuMerchantCrm::getLastFollowUpBdId, 1); // 上次跟进销售不是系统/公海
            // 掉落时间在15天内的
            LocalDateTime dropTime = LocalDateTime.now().minusDays(reclaimableDays);
            queryWrapper.gt(XianmuMerchantCrm::getReassignTime, LocalDateTimeUtil.toEpochMilli(dropTime));
            queryWrapper.orderByAsc(XianmuMerchantCrm::getReassignTime);

            return queryWrapper;
        }

        // 以下类型都是找私海里的客户
        Optional.ofNullable(query.getBdId()).ifPresent(it -> queryWrapper.eq(XianmuMerchantCrm::getBdId, it));

        // 注册未下单的客户
        if (Objects.equals(KeyCustomerEnum.Type.REGISTER_UNORDERED, type)) {
            // 注册未下单的不是掉落客户
            queryWrapper.matchPhrase(XianmuMerchantCrm::getMerchantLabel, REGISTER_NO_ORDER_30_DAY);
            queryWrapper.orderByAsc(XianmuMerchantCrm::getRegisterTime); // 注册时间升序
        }
        // 以下都是掉落客户类型
        else {
            queryWrapper.orderByAsc(XianmuMerchantCrm::getDangerDay); // 掉落天数升序
            queryWrapper.gt(XianmuMerchantCrm::getBdId, 0); // 在私海里的才算是掉落客户
            switch (type) {
                case FIRST_TIME_BUYER:
                    queryWrapper.le(XianmuMerchantCrm::getDangerDay, maxFirstTimeBuyerDangerDay);
                    queryWrapper.eq(XianmuMerchantCrm::getDangerDayReason, FollowUpRelationEnum.DangerDayRule.FIRST_TIME_BUYER.getValue());
                    break;
                case OPEN_TO_PRIVATE:
                    queryWrapper.le(XianmuMerchantCrm::getDangerDay, maxOpenToPrivateDangerDay);
                    queryWrapper.eq(XianmuMerchantCrm::getDangerDayReason, FollowUpRelationEnum.DangerDayRule.OPEN_TO_PRIVATE.getValue());
                    break;
                case OTHER:
                    // "其它"就是非首单和公转私的客户
                    queryWrapper.le(XianmuMerchantCrm::getDangerDay, KEY_DANGER_DAY);
                    queryWrapper.not().in(XianmuMerchantCrm::getDangerDayReason,
                            FollowUpRelationEnum.DangerDayRule.FIRST_TIME_BUYER.getValue(),
                            FollowUpRelationEnum.DangerDayRule.OPEN_TO_PRIVATE.getValue());
                    break;
                default:
                    throw new BizException("未知的重点客户类型:" + type);
            }
        }

        return queryWrapper;
    }

}
