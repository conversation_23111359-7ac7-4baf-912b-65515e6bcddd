package net.summerfarm.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.offline.SaasOrderCustMapper;
import net.summerfarm.crm.mapper.offline.SaasOrderItemMapper;
import net.summerfarm.crm.model.convert.SaasOrderConverter;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.SaasOrderCust;
import net.summerfarm.crm.model.domain.SaasOrderItem;
import net.summerfarm.crm.model.query.saasorder.SaasOrderListQuery;
import net.summerfarm.crm.model.vo.OrderOverviewVO;
import net.summerfarm.crm.model.vo.saasorder.SaasOrderDetailVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.SaasOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SaasOrderServiceImpl implements SaasOrderService {

    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private SaasOrderCustMapper saasOrderCustMapper;
    @Resource
    private SaasOrderItemMapper saasOrderItemMapper;

    @Override
    public PageInfo<OrderOverviewVO> pageSaasOrderList(SaasOrderListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            log.info("不是销售身份");
            return new PageInfo<>();
        }

        // m1m2m3可以看到下属bd的订单
        // 普通bd只能看到自己的订单
        List<CrmBdOrg> childrenBd = bdAreaConfigService.listChildrenBd();
        childrenBd.add(topRankOrg);
        List<Long> bdIds = childrenBd.stream().map(CrmBdOrg::getBdId).map(Long::valueOf).distinct().collect(Collectors.toList());

        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<SaasOrderCust> saasOrderCusts = saasOrderCustMapper.selectByQuery(query, bdIds);
        List<OrderOverviewVO> orderOverviewVOS = SaasOrderConverter.INSTANCE.saasOrderCustListToOrderOverviewVOList(saasOrderCusts);
        return new PageInfo<>(orderOverviewVOS);
    }

    @Override
    public SaasOrderDetailVO getSaasOrderDetail(String orderNo) {
        SaasOrderCust saasOrderCust = saasOrderCustMapper.selectOneByOrderNo(orderNo);
        if (saasOrderCust == null) {
            return null;
        }
        List<SaasOrderItem> saasOrderItems = saasOrderItemMapper.selectByOrderNo(orderNo);
        return SaasOrderConverter.INSTANCE.saasOrderDetailToVO(saasOrderCust, saasOrderItems);
    }
}
