package net.summerfarm.crm.service.impl;

import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.mapper.manage.CrmSalesAreaMapper;
import net.summerfarm.crm.mapper.manage.CrmSalesCityMapper;
import net.summerfarm.crm.mapper.offline.CrmBdTodayHourGmvMapper;
import net.summerfarm.crm.mapper.offline.CrmCityTodayHourGmvMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.convert.salesdata.SalesDataConverter;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.query.salesdata.CityAreaQuery;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import net.summerfarm.crm.model.vo.areaConfig.SalesAreaVo;
import net.summerfarm.crm.model.vo.saledata.CrmAreaDataVO;
import net.summerfarm.crm.model.vo.saledata.CrmBdDataVO;
import net.summerfarm.crm.model.vo.saledata.CrmCityDataVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.SalesDataServiceV2;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;
import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.CITY_MANAGER;
import static net.summerfarm.crm.enums.DataSynchronizationInformationEnum.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SalesDataServiceV2Impl extends BaseService implements SalesDataServiceV2 {

    @Resource
    private CrmConfig crmConfig;
    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private CrmBdTodayHourGmvMapper crmBdTodayHourGmvMapper;
    @Resource
    private CrmCityTodayHourGmvMapper crmCityTodayHourGmvMapper;
    @Resource
    private CrmSalesCityMapper crmSalesCityMapper;

    @Override
    public CrmBdTodayHourGmv fetchTodayDataForBd(CrmBdOrg bdOrg) {
        Integer dateTag = this.getDateTagFromSyncInfo(CRM_BD_TODAY_HOUR_GMV);
        // 目前业务逻辑是只取与M1负责区域一致的数据(即is_same_city字段为"是")
        return crmBdTodayHourGmvMapper.selectOneByBdIdAndDateTagAndIsSameCity(bdOrg.getBdId().longValue(), dateTag, "是");
    }

    @Override
    public CrmCityTodayHourGmv fetchTodayDataForManager(CrmBdOrg bdOrg) {
        // 获取该主管管辖的所有城市
        List<CrmSalesArea> crmSalesAreas = bdAreaConfigService.listSalesAreasManagedByManager(bdOrg);
        List<CityAreaQuery> cities = crmSalesAreas.stream()
                .map(CrmSalesArea::getId)
                .map(this::findCitiesInSalesArea)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        return this.sumTodayDataForCities(cities);
    }

    @Override
    public List<CrmAreaDataVO> fetchPerformancePerAreaForManager(String timeType) {
        CrmBdOrg bdOrg = bdAreaConfigService.getTopRankOrg();
        if (bdOrg == null || bdOrg.getRank() == BD) {
            return Collections.emptyList();
        }

        // 获取该主管管辖的区域
        List<CrmSalesArea> salesAreaList = bdAreaConfigService.listSalesAreasManagedByManager(bdOrg);
        if (CollectionUtils.isEmpty(salesAreaList)) {
            return Collections.emptyList();
        }

        return salesAreaList.stream().map(salesArea -> {
            CrmAreaDataVO areaData = null;

            // 获取该区域下所有城市
            List<CityAreaQuery> cities = findCitiesInSalesArea(salesArea.getId());

            if (Objects.equals("day", timeType)) {
                CrmCityTodayHourGmv data = this.sumTodayDataForCities(cities);
                areaData = SalesDataConverter.INSTANCE.CrmCityTodayHourGmvToCrmAreaDataVO(data);
            } else {
                // TODO 等待月度数据表更新
            }
            if (areaData != null) {
                areaData.setSalesAreaId(salesArea.getId());
                areaData.setSalesAreaName(salesArea.getSalesAreaName());
            }
            return areaData;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<CrmCityDataVO> fetchPerformancePerCityInArea(Integer salesAreaId, String timeType) {
        // 获取该区域下所有城市
        List<CityAreaQuery> cities = findCitiesInSalesArea(salesAreaId);
        // 按行政城市名分组
        Map<String, List<CityAreaQuery>> cityAreaMap = cities.stream().collect(Collectors.groupingBy(CityAreaQuery::getCity));

        return cityAreaMap.entrySet().stream()
                .map(entry -> {
                    String city = entry.getKey();
                    List<CityAreaQuery> cityAreaQueries = entry.getValue();
                    CrmCityDataVO cityData = null;

                    if (Objects.equals("day", timeType)) {
                        CrmCityTodayHourGmv data = this.sumTodayDataForCities(cityAreaQueries);
                        cityData = SalesDataConverter.INSTANCE.CrmCityTodayHourGmvToCrmCityDataVO(data);
                    } else {
                        // TODO 等待月度数据表更新
                    }

                    if (cityData != null) {
                        cityData.setCity(city);
                    }

                    return cityData;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<CrmBdDataVO> fetchPerformancePerBdInArea(Integer salesAreaId, String timeType) {
        // 获取该区域下的所有BD
        List<CrmBdOrg> bds = bdAreaConfigService.listBdOrgBySalesArea(salesAreaId);
        if (CollectionUtils.isEmpty(bds)) {
            return Collections.emptyList();
        }

        if (Objects.equals("day", timeType)) {
            return bds.stream()
                    .map(this::fetchTodayDataForBd)
                    .map(SalesDataConverter.INSTANCE::CrmBdTodayHourGmvToCrmBdDataVO)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            // TODO 等待月度数据表更新
            return null;
        }
    }

    // ------------------------------------------------ private methods ------------------------------------------------

    /**
     * 查询销售区域下的城市,如果不是拆分城市则area为"无"
     */
    private List<CityAreaQuery> findCitiesInSalesArea(Integer salesAreaId) {
        List<CrmSalesCity> cities = crmSalesCityMapper.selectBySalesAreaId(salesAreaId);
        return cities.stream().map(city -> {
            CityAreaQuery query = new CityAreaQuery();
            query.setCity(city.getCity());
            query.setArea(crmConfig.getSplitCityList().contains(city.getCity()) ? city.getArea() : "无");
            return query;
        }).distinct().collect(Collectors.toList());
    }


    private CrmCityTodayHourGmv sumTodayDataForCities(List<CityAreaQuery> cities) {
        if (CollectionUtils.isEmpty(cities)) {
            return null;
        }

        Integer dateTag = getDateTagFromSyncInfo(CRM_CITY_TODAY_HOUR_GMV);
        return crmCityTodayHourGmvMapper.sumTodayDataByCitiesAndDateTag(cities, dateTag);
    }


    private Integer getDateTagFromSyncInfo(DataSynchronizationInformationEnum table) {
        DataSynchronizationInformation dataSyncInfo = dataSynchronizationInformationMapper.selectByTableName(table.getTableName());
        return dataSyncInfo == null ? 0 : dataSyncInfo.getDateFlag();
    }
}
