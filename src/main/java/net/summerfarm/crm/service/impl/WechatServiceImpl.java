package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.Retryer;
import net.summerfarm.crm.common.util.WeChatBaseUtil;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.enums.WechatEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.MerchantLabel;
import net.summerfarm.crm.model.domain.MerchantSubAccount;
import net.summerfarm.crm.model.domain.WechatUserInfo;
import net.summerfarm.crm.model.dto.QwChatMessageDTO;
import net.summerfarm.crm.model.query.qiwei.QiweiTransferInput;
import net.summerfarm.crm.model.vo.wechat.CustomerCallBackResp;
import net.summerfarm.crm.model.vo.wechat.ExternalContactDetail;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;
import net.summerfarm.crm.model.vo.wechat.WeChatCustomerRemarkVo;
import net.summerfarm.crm.service.WechatService;
import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.EnterpriseWechatProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.*;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.OFFICIAL_WECHAT;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.SALES_WECHAT;
import static net.summerfarm.crm.enums.WechatEnum.WechatFollowStatusEnum.*;
import static net.summerfarm.crm.facade.FeiShuPersonalMsgFacade.XIANMU_TENANT_ID;

/**
 * <AUTHOR>
 * @Date 2023/8/3 11:54
 */
@Slf4j
@Service
public class WechatServiceImpl implements WechatService {
    @DubboReference
    private EnterpriseWechatProvider wechatProvider;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private MerchantLabelMapper merchantLabelMapper;
    @Resource
    private MerchantLabelCorrelaionMapper correlaionMapper;
    @Resource
    private MerchantSubAccountMapper subAccountMapper;
    public static String token = null;
    @Resource
    private WechatUserInfoMapper wechatUserInfoMapper;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    private static final Retryer RETRYER = Retryer.builder().retryTimes(3).retryInterval(100).intervalTimeUnit(TimeUnit.MILLISECONDS).build();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void customerCallBack(CustomerCallBackResp resp) {
        WechatEnum.WechatCustomerEventEnum event = WechatEnum.WechatCustomerEventEnum.getByEvent(resp.getChangeType());
        if (event == null) {
            log.info("不能处理的微信回调:{}", JSONUtil.toJsonPrettyStr(resp));
            return;
        }
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("external_userid", resp.getExternalUserID());
        String get = get(GET_BY_USER, reqMap);

        // 客户详情
        ExternalContactDetail externalContactDetail = ExternalContactDetail.fromJson(get);
        if (!externalContactDetail.success()) {
            log.info("获取客户详情失败:{}", get);
            throw new BizException("获取客户详情失败");
        }
        if (externalContactDetail.getExternalContact() == null) {
            log.info("回调结果获取用户为空:{}", get);
            return;
        }
        String unionId = externalContactDetail.getExternalContact().getUnionid();
        MerchantSubAccount account = subAccountMapper.selectByUnionid(unionId);
        if (account == null) {
            log.info("unionid找不到客户:{}", get);
            return;
        }
        // 官微用户列表
        Config config = configMapper.selectOne(ConfigValueEnum.OFFICIAL_WECHAT_USER_ID.getKey());
        List<String> officialUserIdList = StrUtil.split(config.getValue(), StrUtil.COMMA);
        String tagName = officialUserIdList.contains(resp.getUserID()) ? OFFICIAL_WECHAT : SALES_WECHAT;
        MerchantLabel merchantLabel = merchantLabelMapper.selectByName(tagName);
        if (merchantLabel == null) {
            log.info("标签找不到 {}", tagName);
            return;
        }
        // 客户详情
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectByUserIdAndUnionId(resp.getUserID(), unionId);
        if (ObjectUtil.equal(event, WechatEnum.WechatCustomerEventEnum.ADD_EXTERNAL_CONTACT)) {
            // 判断客户详情是否存在,不存在新增，存在更新状态
            if (wechatUserInfo != null) {
                wechatUserInfo.setStatus(FOLLOW.getStatus());
                wechatUserInfoMapper.updateByPrimaryKeySelective(wechatUserInfo);
            } else {
                wechatUserInfo = createWechatUserInfo(resp, unionId);
                wechatUserInfoMapper.insertSelective(wechatUserInfo);
            }
            //修改备注
            updateRemark(account, wechatUserInfo.getUserId(), wechatUserInfo.getExternalUserid());

            if (merchantLabelMapper.selectByUnionIdAndName(unionId, tagName) != null) {
                return;
            }
            correlaionMapper.insertByMidAndLabelId(account.getMId(), merchantLabel.getId());
        } else if (ObjectUtil.equal(event, WechatEnum.WechatCustomerEventEnum.DEL_EXTERNAL_CONTACT) || ObjectUtil.equal(event, WechatEnum.WechatCustomerEventEnum.DEL_FOLLOW_USER)) {
            // 判断用户删除员工/员工删除客户/互删
            if (wechatUserInfo != null) {
                boolean isFollow = wechatUserInfo.getStatus().equals(FOLLOW.getStatus());
                if (isFollow) {
                    wechatUserInfo.setStatus(ObjectUtil.equal(event, WechatEnum.WechatCustomerEventEnum.DEL_EXTERNAL_CONTACT) ? BD_NOT_FOLLOW.getStatus() : CUSTOMER_NOT_FOLLOW.getStatus());
                } else {
                    wechatUserInfo.setStatus(WechatEnum.WechatFollowStatusEnum.NOT_FOLLOW.getStatus());
                }
                wechatUserInfoMapper.updateByPrimaryKeySelective(wechatUserInfo);
            }
            correlaionMapper.deleteByMidAndLabelId(account.getMId(), merchantLabel.getId());
        }
    }

    public WechatUserInfo createWechatUserInfo(CustomerCallBackResp resp, String unionid) {
        WechatUserInfo wechatUserInfo = new WechatUserInfo();
        wechatUserInfo.setUserId(resp.getUserID())
                .setUnionid(unionid)
                .setAddTime(LocalDateTimeUtil.of(resp.getCreateTime() * 1000))
                .setExternalUserid(resp.getExternalUserID())
                .setState(resp.getState())
                .setStatus(FOLLOW.getStatus());
        return wechatUserInfo;
    }

    public String get(String uri, Map<String, Object> data) {
        return execute(() -> WeChatBaseUtil.get(getApiUrl(uri), token, data));
    }

    @Override
    public String post(String uri, String data) {
        return execute(() -> WeChatBaseUtil.post(getApiUrl(uri), token, data));
    }

    public String uploadMedia(String uri, String type, File file) {
        return execute(() -> WeChatBaseUtil.uploadMedia(getApiUrl(uri), token, type, file));
    }

    public String execute(Supplier<String> executor) {
        return RETRYER.execute(executor, i -> !WeChatBaseResp.fromJson(i).success(), new Consumer<String>() {
            @Override
            public void accept(String res) {
                if (WeChatBaseResp.fromJson(res).isTokenInvalid()) {
                    token = getAccessToken(true);
                }
            }
        }, e -> {
            log.warn("企微接口请求失败:{}", e.getMessage(), e);
            return null;
        });
    }

    /**
     * 修改备注
     *
     * @param account
     * @param userId
     * @param extendUserId
     */
    public void updateRemark(MerchantSubAccount account, String userId, String extendUserId) {
        if (account == null || StringUtils.isEmpty(userId) || StringUtils.isEmpty(extendUserId)) {
            return;
        }
        Long mId = account.getMId();
        if (mId == null) {
            return;
        }

        MerchantStoreResultResp merchant = merchantQueryFacade.getMerchantByMid(mId);
        if (merchant == null) {
            return;
        }

        WeChatCustomerRemarkVo weChatCustomerRemarkVo = new WeChatCustomerRemarkVo();
        weChatCustomerRemarkVo.setUserid(userId);
        weChatCustomerRemarkVo.setExternalUserid(extendUserId);
        weChatCustomerRemarkVo.setDescription(merchant.getStoreName());
        String merchantName = merchant.getStoreName().length() > 16 ? merchant.getStoreName().substring(0, 15) : merchant.getStoreName();
        weChatCustomerRemarkVo.setRemark(merchantName);
        weChatCustomerRemarkVo.setRemarkMobiles(Collections.singletonList(account.getPhone()));
        post(UPDATE_REMARK_PHONE, JSON.toJSONString(weChatCustomerRemarkVo));
    }

    @Override
    public void transferCustomerList(String formUserId, String toUserId, String externalUserId, String userName) {
        QiweiTransferInput transferInput = new QiweiTransferInput();
        transferInput.setTakeoverUserId(toUserId);
        transferInput.setHandoverUserId(formUserId);
        transferInput.setExternalUserId(Collections.singletonList(externalUserId));
        transferInput.setTransferSuccessMsg("您好，您的服务已升级，后续将由我的同事接替我的工作，继续为您服务。");
        String post = post(TRANSFER_CUSTOMER, JSON.toJSONString(transferInput));
        if (StringUtils.isEmpty(post)) {
            log.info("error fromUserId {} externalUserId {}", formUserId, externalUserId);
        }
    }

    @Override
    public void transferCustomerList(String fromUserId, String toUserId, List<String> externalUserIds) {
        // 一次转移上限为100个
        List<List<String>> partition = ListUtil.partition(externalUserIds, 100);
        partition.forEach(externalUserIdsList -> {
            QiweiTransferInput transferInput = new QiweiTransferInput();
            transferInput.setTakeoverUserId(toUserId);
            transferInput.setHandoverUserId(fromUserId);
            transferInput.setExternalUserId(externalUserIdsList);
            transferInput.setTransferSuccessMsg("您好，您的服务已升级，后续将由我的同事接替我的工作，继续为您服务。");
            String post = post(TRANSFER_CUSTOMER, JSON.toJSONString(transferInput));
            if (StringUtils.isEmpty(post)) {
                log.info("error fromUserId {} externalUserId {}", fromUserId, externalUserIdsList);
            }
        });
    }

    @Override
    public void transferCustomerListForResignedBd(String fromUserId, String toUserId, List<String> externalUserIds) {
        // 一次转移上限为100个
        List<List<String>> partition = ListUtil.partition(externalUserIds, 100);
        partition.forEach(externalUserIdsList -> {
            QiweiTransferInput transferInput = new QiweiTransferInput();
            transferInput.setTakeoverUserId(toUserId);
            transferInput.setHandoverUserId(fromUserId);
            transferInput.setExternalUserId(externalUserIdsList);
            String post = post(RESIGNED_TRANSFER_CUSTOMER, JSON.toJSONString(transferInput));
            if (StringUtils.isEmpty(post)) {
                log.info("error fromUserId {} externalUserId {}", fromUserId, externalUserIdsList);
            }
        });
    }

    public String getAccessToken(boolean update) {
        DubboResponse<String> resp = wechatProvider.queryEnterpriseWeChatToken(SystemOriginEnum.ADMIN, XIANMU_TENANT_ID, EnterpriseWeChatTokenTypeEnum.CUSTOMER_ACCESS_TOKEN, update);
        if (!resp.isSuccess()) {
            log.error("获取企微token失败:{}", JSONUtil.toJsonPrettyStr(resp));
        }
        return resp.getData();
    }


    /**
     * 创建企业群发
     * <a href="https://developer.work.weixin.qq.com/document/path/92135">...</a>
     *
     * @param message 消息主体
     */
    public void sendMessage(QwChatMessageDTO message) {
        String post = post(SEND_MESSAGE, JSON.toJSONString(message));
        if (StringUtils.isEmpty(post)) {
            log.info("发送消息失败, message: {}", JSON.toJSONString(message));
        }
    }
}
