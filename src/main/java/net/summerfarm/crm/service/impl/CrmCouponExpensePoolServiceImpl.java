package net.summerfarm.crm.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.CategoryQuotaEnum;
import net.summerfarm.crm.enums.CategoryTypeEnum;
import net.summerfarm.crm.enums.CouponEnum;
import net.summerfarm.crm.facade.CrmOperationLogCommandFacade;
import net.summerfarm.crm.facade.CrmOperationLogQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.repository.ProductsRepository;
import net.summerfarm.crm.model.convert.CrmCouponExpenseAdminDtoConverter;
import net.summerfarm.crm.model.convert.CrmCouponExpensePoolDtoConverter;
import net.summerfarm.crm.model.convert.CrmCouponExpensePoolLogDtoConverter;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.CategoryProductDTO;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpenseAdminDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolExtDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolLogDto;
import net.summerfarm.crm.model.query.crmCouponExpensePool.CrmCouponExpensePoolQuery;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolDivideVo;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolExtVo;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolLogoVo;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolVo;
import net.summerfarm.crm.service.CrmCouponExpensePoolService;
import net.summerfarm.pojo.DO.Admin;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import net.xianmu.oss.common.util.OssGetUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.constant.CrmGlobalConstant.CRM_COUPON_EXPENSE_POOL_PRE;
import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;
import static net.summerfarm.crm.enums.CategoryQuotaEnum.QuotaType.CATEGORY_PRICE;
import static net.summerfarm.crm.model.convert.CrmCouponExpensePoolLogDtoConverter.toCrmCouponExpensePoolLogDto;

@Service
public class CrmCouponExpensePoolServiceImpl extends BaseService implements CrmCouponExpensePoolService {
    @Resource
    CrmBdOrgMapper orgMapper;
    @Resource
    AdminMapper adminMapper;
    @Resource
    CategoryCouponQuotaChangeMapper quotaChangeMapper;
    @Resource
    private CrmCouponExpensePoolMapper crmCouponExpensePoolMapper;
    @Resource
    private CrmCouponExpensePoolRangeMapper crmCouponExpensePoolRangeMapper;
    @Resource
    private CrmCouponExpenseRecordMapper crmCouponExpenseRecordMapper;
    @Resource
    private CrmOperationLogQueryFacade crmOperationLogQueryFacade;
    @Resource
    private CrmOperationLogCommandFacade crmOperationLogCommandFacade;
    @Resource
    private ProductsRepository productsRepository;

    /**
     * 查询优惠券消费池信息
     *
     * @param cluClueQuery 查询条件，包含页码和页大小等
     * @param userBase     当前用户基础信息
     * @return 返回分页后的优惠券消费池信息结果，包括总页数、当前页码、每页记录数等
     */
    @Override
    public CommonResult<PageInfo<CrmCouponExpensePoolDto>> queryPool(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase) {
        // 使用PageHelper进行分页，根据查询条件获取页码和页大小
        PageHelper.startPage(cluClueQuery.getPageIndex(), cluClueQuery.getPageSize());
        // 根据查询条件查询符合条件的优惠券消费池记录
        List<CrmCouponExpensePool> crmCouponExpensePools = crmCouponExpensePoolMapper.selectByQuery(cluClueQuery);
        // 根据查询结果生成PageInfo对象，包含分页信息
        PageInfo<CrmCouponExpensePool> pageInfo = PageInfoHelper.createPageInfo(crmCouponExpensePools);
        // 将PageInfo对象转换为业务对象的PageInfo，用于前端展示
        PageInfo<CrmCouponExpensePoolDto> crmCouponExpensePoolDtoPageInfo = PageInfoConverter.toPageResp(pageInfo, CrmCouponExpensePoolDtoConverter::toCrmCouponExpensePoolDto);
        mergePoolDto(crmCouponExpensePoolDtoPageInfo.getList());
        // 返回转换后的分页信息结果
        return CommonResult.ok(crmCouponExpensePoolDtoPageInfo);
    }
    private void mergePoolDto(List<CrmCouponExpensePoolDto> dtos){
        if (CollectionUtils.isEmpty(dtos)){
            return;
        }
        dtos.forEach(
                it->{
                    Byte productRange = it.getProductRange();
                    if (CategoryQuotaEnum.TargetType.ALL.getCode().equals(productRange.intValue()) || CategoryQuotaEnum.TargetType.SKU.getCode().equals(productRange.intValue())){
                        it.setLimits(Arrays.asList(CategoryQuotaEnum.TargetType.getDesc(productRange.intValue())));
                    }else {
                        List<CrmCouponExpensePoolExtDto> crmCouponExpensePoolExtDtos = crmCouponExpensePoolRangeMapper.selectByPoolId(it.getPoolId());
                        if (!CollectionUtils.isEmpty(crmCouponExpensePoolExtDtos)){
                            List<String> collect = crmCouponExpensePoolExtDtos.stream().map(u-> CategoryTypeEnum.getValue(Integer.valueOf(u.getObjKey()))).collect(Collectors.toList());
                            it.setLimits(collect);
                        }
                    }
                }
        );
    }


    @Override
    public CommonResult<CrmCouponExpensePoolDto> queryPoolDetail(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase) {
        List<CrmCouponExpensePool> crmCouponExpensePools = crmCouponExpensePoolMapper.selectByQuery(cluClueQuery);
        if (CollectionUtils.isEmpty(crmCouponExpensePools)){
            return CommonResult.ok();
        }
        return CommonResult.ok(CrmCouponExpensePoolDtoConverter.toCrmCouponExpensePoolDto(crmCouponExpensePools.get(0)));
    }

    /**
     * 查询扩展后的优惠券池信息
     *
     * @param cluClueQuery 查询条件，包含页码、页大小和池ID
     * @param userBase     当前用户基础信息
     * @return 返回优惠券池信息的分页结果，包括每页的数据列表
     */
    @Override
    public CommonResult<List<CrmCouponExpensePoolExtDto>> queryPoolExt(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase) {
        // 根据池ID查询数据
        List<CrmCouponExpensePoolExtDto> crmCouponExpensePoolExtDtos = crmCouponExpensePoolRangeMapper.selectByPoolId(cluClueQuery.getPoolId());

        convertCrmCouponExpensePoolExtDto(cluClueQuery.getPoolId(), crmCouponExpensePoolExtDtos);
        return CommonResult.ok(crmCouponExpensePoolExtDtos);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CrmCouponExpensePool> save(CrmCouponExpensePoolVo crmCouponExpensePoolVo, UserBase userBase) {
        Admin admin = getCurrentUser();
        CrmCouponExpensePool queryNamePool = crmCouponExpensePoolMapper.selectByName(crmCouponExpensePoolVo.getName());
        if (queryNamePool != null) {
            throw new BizException("费用池名称已存在");
        }
        //基础表
        CrmCouponExpensePool crmCouponExpensePool = buildCrmCouponExpensePool(crmCouponExpensePoolVo, admin);
        crmCouponExpensePool.setRemainingAmount(crmCouponExpensePoolVo.getTotalAmount());
        crmCouponExpensePool.setStatus(CategoryQuotaEnum.PoolStatus.COMMON.getCode().byteValue());
        //扩展属性
        List<String> crmCouponExpensePoolRanges = buildPoolExt(crmCouponExpensePoolVo);
        //bd保存
        bdSave(crmCouponExpensePool, crmCouponExpensePoolRanges);
        //日志
        CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo = buildCrmCouponExpensePoolLogoVo(admin, crmCouponExpensePool.getTotalAmount(), null, CategoryQuotaEnum.PoolOperateLogType.ADD.getDesc(), null);
        crmOperationLogCommandFacade.addCouponExpensePoolLog(userBase.getId(), CRM_COUPON_EXPENSE_POOL_PRE, crmCouponExpensePool.getId().toString(), crmCouponExpensePoolLogoVo);
        return CommonResult.ok(crmCouponExpensePool);
    }

    public void bdSave(CrmCouponExpensePool crmCouponExpensePool, List<String> crmCouponExpensePoolRanges) {
        crmCouponExpensePoolMapper.insert(crmCouponExpensePool);
        if (CollectionUtils.isEmpty(crmCouponExpensePoolRanges)){
            return;
        }
        addExt(crmCouponExpensePool.getId(),crmCouponExpensePoolRanges);
    }

    public void addExt(Long poolId,  List<String> crmCouponExpensePoolRanges){
        if (CollectionUtils.isEmpty(crmCouponExpensePoolRanges)){
            return;
        }
        List<CrmCouponExpensePoolRange> collect = crmCouponExpensePoolRanges.stream().map(
                range -> {
                    CrmCouponExpensePoolRange crmCouponExpensePoolRange = new CrmCouponExpensePoolRange();
                    crmCouponExpensePoolRange.setPoolId(poolId);
                    crmCouponExpensePoolRange.setObjKey(range);
                    return crmCouponExpensePoolRange;
                }
        ).collect(Collectors.toList());
        crmCouponExpensePoolRangeMapper.batchInsert(collect);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CrmCouponExpensePool> update(CrmCouponExpensePoolVo crmCouponExpensePoolVo, UserBase userBase) {
        Long id = crmCouponExpensePoolVo.getPoolId();
        Admin admin = getCurrentUser();
        CrmCouponExpensePool crmCouponExpensePool = checkUpdate(id);
        List<CrmCouponExpensePoolLogoVo> logsList = new ArrayList<>();
        CrmCouponExpensePool updatePool = new CrmCouponExpensePool();
        updatePool.setId(id);
        //名字变更
        if (!Objects.isNull(crmCouponExpensePoolVo.getName()) && !Objects.equals(crmCouponExpensePoolVo.getName(), crmCouponExpensePool.getName())) {
            CrmCouponExpensePool queryNamePool = crmCouponExpensePoolMapper.selectByName(crmCouponExpensePoolVo.getName());
            if (queryNamePool != null) {
                throw new BizException("费用池名称已存在");
            }
            updatePool.setName(crmCouponExpensePoolVo.getName());
        }
        //价钱变更
        if (!Objects.isNull(crmCouponExpensePoolVo.getTotalAmount()) && !Objects.equals(crmCouponExpensePoolVo.getTotalAmount(), crmCouponExpensePool.getTotalAmount())) {
            BigDecimal subtract = crmCouponExpensePoolVo.getTotalAmount().subtract(crmCouponExpensePool.getTotalAmount());
            //新增的金额>原来金额
            if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException("费用总金额只能增加不能减少");
            }
            updatePool.setTotalAmount(crmCouponExpensePoolVo.getTotalAmount());
            updatePool.setRemainingAmount(subtract.add(crmCouponExpensePool.getRemainingAmount()));
            logsList.add(buildCrmCouponExpensePoolLogoVo(admin, crmCouponExpensePool.getTotalAmount(), "", CategoryQuotaEnum.PoolOperateLogType.ADD.getDesc(), ""));

        }
        //状态变更 日志
        if (!Objects.isNull(crmCouponExpensePoolVo.getStatus()) && !Objects.equals(crmCouponExpensePoolVo.getStatus(), crmCouponExpensePool.getStatus())) {
            updatePool.setStatus(crmCouponExpensePoolVo.getStatus());
            logsList.add(buildCrmCouponExpensePoolLogoVo(admin, BigDecimal.ZERO, "", CategoryQuotaEnum.PoolOperateLogType.DELETE.getDesc(), ""));
            updateAdminPool(id, getCurrentUser());
            updatePool.setName(crmCouponExpensePool.getName() + "-已废弃" + RandomUtil.randomNumbers(4));


        }
        //商品费比变更
        if (!Objects.isNull(crmCouponExpensePoolVo.getCostLimit()) && !Objects.equals(crmCouponExpensePoolVo.getCostLimit(), crmCouponExpensePool.getCostLimit())) {
            updatePool.setCostLimit(crmCouponExpensePoolVo.getCostLimit());
        }
        //商品费比变更
        if (!Objects.isNull(crmCouponExpensePoolVo.getAutoApprove()) && !Objects.equals(crmCouponExpensePoolVo.getAutoApprove(), crmCouponExpensePool.getAutoApprove())) {
            updatePool.setAutoApprove(crmCouponExpensePoolVo.getAutoApprove());
        }
        if (crmCouponExpensePoolVo.getCostDisabled()!=null && Objects.equals(crmCouponExpensePoolVo.getCostDisabled(), CategoryQuotaEnum.PoolUpdateLimit.LIMIT.getCode())){
            crmCouponExpensePoolMapper.updateCostLimitNull(id);
        }
        if (crmCouponExpensePoolVo.getDateDisabled() != null && Objects.equals(crmCouponExpensePoolVo.getDateDisabled(), CategoryQuotaEnum.PoolUpdateLimit.LIMIT.getCode())){
            crmCouponExpensePoolMapper.updateStartTimeEndTimeNull(id);
        }
        //开始时间
        if (!Objects.isNull(crmCouponExpensePoolVo.getStartDate()) && !Objects.equals(crmCouponExpensePoolVo.getStartDate(), crmCouponExpensePool.getStartDate())) {
            updatePool.setStartDate(crmCouponExpensePoolVo.getStartDate());
        }
        //结束时间
        if (!Objects.isNull(crmCouponExpensePoolVo.getEndDate()) && !Objects.equals(crmCouponExpensePoolVo.getEndDate(), crmCouponExpensePool.getEndDate())) {
            updatePool.setEndDate(crmCouponExpensePoolVo.getEndDate());
        }
        //商品限制
        if (!Objects.isNull(crmCouponExpensePoolVo.getProductRange()) && !Objects.equals(crmCouponExpensePoolVo.getProductRange(), crmCouponExpensePool.getTargetType())) {
            updatePool.setTargetType(crmCouponExpensePoolVo.getProductRange());
            crmCouponExpensePoolRangeMapper.deleteByPoolId(id);
            List<String> crmCouponExpensePoolRanges = buildPoolExt(crmCouponExpensePoolVo);
            addExt(id, crmCouponExpensePoolRanges);
        }
        if (!CollectionUtils.isEmpty(crmCouponExpensePoolVo.getLimits())){
            crmCouponExpensePoolRangeMapper.deleteByPoolId(id);
            List<String> crmCouponExpensePoolRanges = buildPoolExt(crmCouponExpensePoolVo);
            addExt(id, crmCouponExpensePoolRanges);
        }
        updatePool.setUpdateTime(LocalDateTime.now());
        crmCouponExpensePoolMapper.updateByPrimaryKeySelective(updatePool);
        crmOperationLogCommandFacade.addCouponExpensePoolLogList(userBase.getId(), CRM_COUPON_EXPENSE_POOL_PRE, id.toString(), logsList);
        //日志
        return CommonResult.ok(updatePool);
    }



    @Override
    public CommonResult<PageInfo<CrmCouponExpensePoolLogDto>> queryPoolLog(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase) {
        PageInfo<String> stringPageInfo = crmOperationLogQueryFacade.queryCouponExpensePoolBaseLog(cluClueQuery.getPoolId(), cluClueQuery.getPageIndex(), cluClueQuery.getPageSize());
        PageInfo<CrmCouponExpensePoolLogDto> crmCouponExpensePoolLogDtoPageInfo = PageInfoConverter.toPageResp(stringPageInfo, this::logConvert);
        return CommonResult.ok(crmCouponExpensePoolLogDtoPageInfo);
    }

    @Override
    public CommonResult<PageInfo<CrmCouponExpenseAdminDto>> adminPool(UserBase userBase, CrmCouponExpensePoolQuery crmCouponExpensePoolQuery) {
        //特殊角色 是池子 其他是自己的
        if (crmCouponExpensePoolQuery.getAdminId() == null && updateExpensePoolRoleId()) {
            PageHelper.startPage(crmCouponExpensePoolQuery.getPageIndex(), crmCouponExpensePoolQuery.getPageSize());
            crmCouponExpensePoolQuery.setStatus(CategoryQuotaEnum.PoolStatus.COMMON.getCode());
            List<CrmCouponExpensePool> crmCouponExpensePools = crmCouponExpensePoolMapper.selectByQuery(crmCouponExpensePoolQuery);
            PageInfo<CrmCouponExpensePool> pageInfo = PageInfoHelper.createPageInfo(crmCouponExpensePools);
            return CommonResult.ok(PageInfoConverter.toPageResp(pageInfo, CrmCouponExpenseAdminDtoConverter::toCrmCouponExpenseAdminDto));
        }
        crmCouponExpensePoolQuery.setStatus(CategoryQuotaEnum.PoolStatus.COMMON.getCode());
        if (crmCouponExpensePoolQuery.getQueryParent()) {
            crmCouponExpensePoolQuery.setAdminId(getParent(getAdminId()).getBdId());
        } else {
            crmCouponExpensePoolQuery.setAdminId(crmCouponExpensePoolQuery.getAdminId());
        }
        if (crmCouponExpensePoolQuery.getQueryMyself()){
            crmCouponExpensePoolQuery.setAdminId(getAdminId());
        }
        PageHelper.startPage(crmCouponExpensePoolQuery.getPageIndex(), crmCouponExpensePoolQuery.getPageSize());
        //查询自己拥有的
        List<CrmCouponExpensePool> crmCouponExpensePools = crmCouponExpenseRecordMapper.crmCouponExpensePoolQuery(crmCouponExpensePoolQuery);
        PageInfo<CrmCouponExpensePool> pageInfo = PageInfoHelper.createPageInfo(crmCouponExpensePools);
        return CommonResult.ok(PageInfoConverter.toPageResp(pageInfo, CrmCouponExpenseAdminDtoConverter::toCrmCouponExpenseAdminDto));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> divide(UserBase userBase, CrmCouponExpensePoolDivideVo divideVo) {
        Long poolId = divideVo.getPoolId();
        CrmCouponExpensePool crmCouponExpensePool = checkUpdate(poolId);
        BigDecimal amount = divideVo.getAmount();
        if (Objects.equals(userBase.getBizUserId(), divideVo.getToAdminId())){
            throw new BizException("自己不能给自己分配");

        }
        Admin toAdmin = adminMapper.selectByPrimaryKey(divideVo.getToAdminId());
        if (toAdmin == null) {
            throw new BizException("被划分用户不存在");
        }
        //特殊角色 是池子 其他是自己的
        if (isSA() || updateExpensePoolRoleId()) {
            //查询池子的id
            BigDecimal remainingAmount = crmCouponExpensePool.getRemainingAmount();
            if (amount.compareTo(remainingAmount) > 0) {
                throw new BizException("被划分的金额不能大于余额");
            }
            //扣除池子余额
            CrmCouponExpensePool updatePool = new CrmCouponExpensePool();
            updatePool.setId(crmCouponExpensePool.getId());
            updatePool.setRemainingAmount(remainingAmount.subtract(amount));
            crmCouponExpensePoolMapper.updateByPrimaryKeySelective(updatePool);
            //修改池子日志
            crmOperationLogCommandFacade.addCouponExpensePoolLog(userBase.getId(), CRM_COUPON_EXPENSE_POOL_PRE, crmCouponExpensePool.getId().toString(), buildCrmCouponExpensePoolLogoVo(getCurrentUser(), divideVo.getAmount(), toAdmin.getRealname(), CategoryQuotaEnum.PoolOperateLogType.DIVIDE.getDesc(), divideVo.getRemark()));
        }else {
            CrmCouponExpenseRecord queryPool = crmCouponExpenseRecordMapper.selectPoolIdAdminId(poolId, getAdminId());
            if (queryPool == null){
                throw new BizException("当前人员的池子余额不存在");
            }
            if (queryPool.getRemainingAmount().compareTo(amount) < 0) {
                throw new BizException("当前人员的池子余额不足");
            }
            CrmCouponExpenseRecord updatePoolRecord = new CrmCouponExpenseRecord();
            updatePoolRecord.setRemainingAmount(queryPool.getRemainingAmount().subtract(amount));
            updatePoolRecord.setId(queryPool.getId());
            crmCouponExpenseRecordMapper.updateByPrimaryKeySelective(updatePoolRecord);
        }
        //新增记录表
        CrmCouponExpenseRecord queryPool = crmCouponExpenseRecordMapper.selectPoolIdAdminId(poolId, divideVo.getToAdminId());
        if (queryPool == null) {
            CrmCouponExpenseRecord addPool = new CrmCouponExpenseRecord();
            addPool.setPoolId(poolId);
            addPool.setAdminId(divideVo.getToAdminId());
            addPool.setTotalAmount(divideVo.getAmount());
            addPool.setRemainingAmount(divideVo.getAmount());
            addPool.setCreateTime(new Date());
            addPool.setCreateUserId(userBase.getBizUserId().longValue());
            crmCouponExpenseRecordMapper.insertSelective(addPool);
        } else {
            CrmCouponExpenseRecord updatePoolRecord = new CrmCouponExpenseRecord();
            updatePoolRecord.setId(queryPool.getId());
            BigDecimal total = queryPool.getTotalAmount() == null ? BigDecimal.ZERO : queryPool.getTotalAmount();
            BigDecimal remaining = queryPool.getRemainingAmount() == null ? BigDecimal.ZERO : queryPool.getRemainingAmount();
            updatePoolRecord.setTotalAmount(total.add(amount));
            updatePoolRecord.setRemainingAmount(remaining.add(amount));

            crmCouponExpenseRecordMapper.updateByPrimaryKeySelective(updatePoolRecord);
        }
        generateQuotaChange(divideVo, toAdmin, CouponEnum.CouponQuotaChangeType.DIVISION, userBase.getBizUserId(), getAdminName(), CATEGORY_PRICE.getCode());
        return CommonResult.ok(true);
    }

    private void generateQuotaChange(CrmCouponExpensePoolDivideVo crmCouponExpensePoolDivideVo, Admin toAdmin, CouponEnum.CouponQuotaChangeType couponQuotaChangeType,
                                     Integer creator, String creatorName, Integer quotaType) {
        CategoryCouponQuotaChange quotaChange = new CategoryCouponQuotaChange();
        quotaChange.setAdminId(crmCouponExpensePoolDivideVo.getToAdminId())
                .setAdminName(toAdmin.getRealname())
                .setQuota(crmCouponExpensePoolDivideVo.getAmount())
                .setType(couponQuotaChangeType.getCode())
                .setQuotaType(quotaType)
                .setPoolId(crmCouponExpensePoolDivideVo.getPoolId())
                .setCreator(creator).setCreatorName(creatorName).setRemark(crmCouponExpensePoolDivideVo.getRemark());
        quotaChangeMapper.insertSelective(quotaChange);
    }



    @Override
    public CommonResult<Boolean> addPoolExt(UserBase userBase, CrmCouponExpensePoolExtVo vo) {
        Long poolId = vo.getPoolId();
        CrmCouponExpensePool crmCouponExpensePool = checkUpdate(poolId);
        if (!CollectionUtils.isEmpty(vo.getLimits())) {
            return CommonResult.ok(true);
        }
        Long id = crmCouponExpensePool.getId();
        addExt(id, vo.getLimits());
        return CommonResult.ok(true);
    }

    @Override
    public CommonResult<Boolean> deletePoolExt(UserBase userBase, CrmCouponExpensePoolExtVo vo) {
        crmCouponExpensePoolRangeMapper.deleteByPrimaryKey(vo.getId());
        return CommonResult.ok(true);
    }

    public void  checkPoolSkuCanUse(String sku, Long poolId, Integer adminId, MerchantSituationDTO merchantSituationDTO){
        if (sku == null){
            throw new BizException("sku不能为空");
        }
        Map map = JSONUtil.toBean(sku, Map.class);
        Object[] objects = map.keySet().toArray();
        sku  = objects[0].toString();
        if (poolId == null){
            throw new BizException("poolId不能为空");
        }
        CrmCouponExpensePool crmCouponExpensePool = crmCouponExpensePoolMapper.selectByPrimaryKey(poolId);
        if (crmCouponExpensePool == null || crmCouponExpensePool.getStatus().equals(CategoryQuotaEnum.PoolStatus.DELETE.getCode().byteValue())){
            throw new BizException("池子不存在或以及被失效");
        }
        merchantSituationDTO.setPoolName(crmCouponExpensePool.getName());
        merchantSituationDTO.setAutoApprove(crmCouponExpensePool.getAutoApprove().intValue() == CategoryQuotaEnum.AutoApproveStatus.OPEN.getCode());
        //判断时间范围
        if (crmCouponExpensePool.getStartDate()!=null && crmCouponExpensePool.getEndDate()!=null){
            //开始时间小于现在 结束时间大于现在
            if (!crmCouponExpensePool.getStartDate().isAfter(LocalDateTime.now()) && crmCouponExpensePool.getEndDate().isAfter(LocalDateTime.now())){
              logger.info("卡卷时间OK");
            }else {
                throw new BizException("卡卷池不在生效时间内");
            }
        }

        //范围
        if (crmCouponExpensePool.getTargetType().intValue()==CategoryQuotaEnum.TargetType.SKU.getCode()){
            //按照sku
            List<CrmCouponExpensePoolExtDto> crmCouponExpensePoolExtDtos = crmCouponExpensePoolRangeMapper.selectByPoolIdObjKey(poolId, sku);
            if (CollectionUtils.isEmpty(crmCouponExpensePoolExtDtos)){
                List<String> collect = crmCouponExpensePoolRangeMapper.selectByPoolId(poolId).stream().map(CrmCouponExpensePoolExtDto::getObjKey).collect(Collectors.toList());
                String join = String.join(",", collect);
                throw new BizException("该优惠券不满足商品sku为"+join+"的要求，请重新提交");
            }
        }
        if (crmCouponExpensePool.getTargetType().intValue()==CategoryQuotaEnum.TargetType.TYPE.getCode()){
            CategoryProductDTO categoryTypeBySku = productsRepository.getCategoryTypeBySku(sku);
            if (categoryTypeBySku == null){
                throw new BizException("sku不存在");
            }
            //如果种类是
            String searchKey = Arrays.asList(CategoryTypeEnum.DAIRY.getType().toString(),CategoryTypeEnum.FRUIT.getType().toString()).contains(categoryTypeBySku.getCategoryType().toString())?categoryTypeBySku.getCategoryType().toString():CategoryTypeEnum.OTHERS.getType().toString();
            List<CrmCouponExpensePoolExtDto> crmCouponExpensePoolExtDtos = crmCouponExpensePoolRangeMapper.selectByPoolIdObjKey(poolId, searchKey);
            if (CollectionUtils.isEmpty(crmCouponExpensePoolExtDtos)){
                List<String> collect = crmCouponExpensePoolRangeMapper.selectByPoolId(poolId).stream().map(CrmCouponExpensePoolExtDto::getObjKey).collect(Collectors.toList());
                List<String> msg = collect.stream().map(it -> CategoryTypeEnum.getValue(Integer.valueOf(it))).collect(Collectors.toList());
                throw new BizException("该优惠券不满足商品品类为"+msg+"的要求，请重新提交");
            }
        }
        //判断费比
        if (crmCouponExpensePool.getCostLimit() !=null && crmCouponExpensePool.getCostLimit() != 0){
             //费比=优惠券金额/使用门槛*100%,例如满50-5的券,费比
            //5/50*100%=10%,此时费比记为10。
            //优惠金额
            BigDecimal multiply = merchantSituationDTO.getCouponAmount().divide(merchantSituationDTO.getThreshold(),4,BigDecimal.ROUND_HALF_EVEN).multiply(BigDecimal.valueOf(100));
            if (multiply.doubleValue() > crmCouponExpensePool.getCostLimit()){
                throw new BizException("该优惠券不满足费用比为"+crmCouponExpensePool.getCostLimit()+"%的优惠券要求，请重新提交");
            }
        }
        //判断余额
        CrmCouponExpenseRecord crmCouponExpenseRecord = crmCouponExpenseRecordMapper.selectPoolIdAdminId(poolId, adminId);
        if (crmCouponExpenseRecord == null){
            throw new BizException("该管理员没有该费用池的权限，请联系管理员");
        }
        BigDecimal couponAmount = merchantSituationDTO.getCouponAmount();
        if (couponAmount.compareTo(crmCouponExpenseRecord.getRemainingAmount())>0){
            throw new BizException("该优惠券超出额度，请联系M1主管增加额度值");
        }




    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void successUpdateAdminPool(MerchantSituation queryMerchantSituation) {
        //查找m1adminId
        Integer createAdminId = queryMerchantSituation.getCreatorId();
        Integer m1AdminId = null;
        CrmBdOrg parent = null;
        Admin admin = null;
        try {
             parent = getParent(createAdminId);
             m1AdminId =  parent.getBdId();
             admin = adminMapper.selectByPrimaryKey(createAdminId);
        }catch (Exception e){
            logger.info("message  {}",e.getMessage());
        }
        Long poolId = queryMerchantSituation.getPoolId();
        if (poolId == null){
            logger.info("successUpdateAdminPool 成功没有 poolId {}",queryMerchantSituation.getId());
            throw new BizException("没有选择余额池");
        }
        if (admin ==null){
            throw new BizException("创建人不存在");
        }
        if (m1AdminId ==null){
            throw new BizException("组织架构不存在");
        }
        CrmCouponExpenseRecord crmCouponExpenseRecord = crmCouponExpenseRecordMapper.selectPoolIdAdminId(poolId, m1AdminId);
        CrmCouponExpensePool crmCouponExpensePool = crmCouponExpensePoolMapper.selectByPrimaryKey(poolId);
        if (crmCouponExpensePool == null){
            throw new BizException("余额池不存在");
        }
        //删除也记录下
        if (crmCouponExpenseRecord == null && Objects.equals(crmCouponExpensePool.getStatus().intValue(), CategoryQuotaEnum.PoolStatus.DELETE.getCode())){
            //加入变更日志
            CategoryCouponQuotaChange categoryCouponQuotaChange = generateQuotaChange(parent, admin, queryMerchantSituation, CategoryQuotaEnum.QuotaType.CATEGORY_PRICE.getCode(), poolId);
            categoryCouponQuotaChange.setMerchantCouponId(queryMerchantSituation.getMerchantCouponId());
            quotaChangeMapper.insertSelective(categoryCouponQuotaChange);
            return;
        }
        if (crmCouponExpenseRecord == null){
            throw new BizException("上级余额不足");
        }
        //优惠金额
        BigDecimal price = queryMerchantSituation.getAmount();
        if (crmCouponExpenseRecord.getRemainingAmount().compareTo(price)<0){
            throw new BizException("余额不足");
        }
        //修改余额
        CrmCouponExpenseRecord updateRecord = new CrmCouponExpenseRecord();
        BigDecimal divide = crmCouponExpenseRecord.getRemainingAmount().subtract(price);
        updateRecord.setRemainingAmount(divide);
        updateRecord.setId(crmCouponExpenseRecord.getId());
        crmCouponExpenseRecordMapper.updateByPrimaryKeySelective(updateRecord);

        CategoryCouponQuotaChange categoryCouponQuotaChange = generateQuotaChange(parent, admin, queryMerchantSituation, CategoryQuotaEnum.QuotaType.CATEGORY_PRICE.getCode(), poolId);
        categoryCouponQuotaChange.setMerchantCouponId(queryMerchantSituation.getMerchantCouponId());

        quotaChangeMapper.insertSelective(categoryCouponQuotaChange);
    }

    @Override
    public void updatePoolStatus() {
        List<CrmCouponExpensePool> crmCouponExpensePools = crmCouponExpensePoolMapper.selectExpire();
        if (CollectionUtils.isEmpty(crmCouponExpensePools)){
            return;
        }
        for (CrmCouponExpensePool crmCouponExpensePool : crmCouponExpensePools) {
            CrmCouponExpensePool updatePool = new CrmCouponExpensePool();
            updatePool.setStatus(CategoryQuotaEnum.PoolStatus.DELETE.getCode().byteValue());
            updatePool.setName(crmCouponExpensePool.getName()+"-已废弃"+ RandomUtil.randomNumbers(4));
            updatePool.setId(crmCouponExpensePool.getId());
            crmCouponExpensePoolMapper.updateByPrimaryKeySelective(updatePool);
            Admin admin = new Admin();
            admin.setRealname("过期废弃");
            admin.setAdminId(0);
            List<CrmCouponExpensePoolLogoVo> logsList = new ArrayList<>();
            logsList.add(buildCrmCouponExpensePoolLogoVo(admin, BigDecimal.ZERO, "", CategoryQuotaEnum.PoolOperateLogType.DELETE.getDesc(), ""));
            updateAdminPool(crmCouponExpensePool.getId(), admin);
            crmOperationLogCommandFacade.addCouponExpensePoolLogList(0L, CRM_COUPON_EXPENSE_POOL_PRE, crmCouponExpensePool.getId().toString(), logsList);

        }


    }

    public CategoryCouponQuotaChange generateQuotaChange(CrmBdOrg crmBdOrg, Admin admin, MerchantSituation merchantSituation
            , Integer quotaType, Long poolId) {
        CategoryCouponQuotaChange quotaChange = new CategoryCouponQuotaChange();
        quotaChange.setAdminId(crmBdOrg.getBdId())
                .setAdminName(crmBdOrg.getBdName())
                .setQuota(merchantSituation.getAmount().negate())
                .setBasePrice(merchantSituation.getBasePrice())
                .setType(CouponEnum.CouponQuotaChangeType.COUPON.getCode())
                .setDingtalkBizId(merchantSituation.getId())
                .setRemark(merchantSituation.getSituationRemake())
                .setCreator(admin.getAdminId())
                .setQuotaType(quotaType)
                .setPoolId(poolId)
                .setCreatorName(admin.getRealname());
        return quotaChange;
    }

    @Override
    public CommonResult<PageInfo<CrmCouponExpensePoolLogDto>> queryAdminPoolLog(CrmCouponExpensePoolQuery query, UserBase userBase) {
        if (query.getAdminId() == null) {
            throw new BizException("adminId 不能为空");
        }
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<CategoryCouponQuotaChange> categoryCouponQuotaChanges = quotaChangeMapper.selectByAdminIdTypeQuotaType(query.getAdminId(), CATEGORY_PRICE.getCode());
        PageInfo<CategoryCouponQuotaChange> pageInfo = PageInfoHelper.createPageInfo(categoryCouponQuotaChanges);
        PageInfo<CrmCouponExpensePoolLogDto> crmCouponExpensePoolLogDtoPageInfo = PageInfoConverter.toPageResp(pageInfo, CrmCouponExpensePoolLogDtoConverter::toCrmCouponExpensePoolLogDto);
        List<Long> poolIds = crmCouponExpensePoolLogDtoPageInfo.getList().stream().map(CrmCouponExpensePoolLogDto::getPoolId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, CrmCouponExpensePool> poolMap = getPoolMap(poolIds);
        List<Integer> createAdminIds =  crmCouponExpensePoolLogDtoPageInfo.getList().stream().map(CrmCouponExpensePoolLogDto::getChangeCreateId).collect(Collectors.toList());
        Map<Integer, String> bdRealNames = getBdRealNames(createAdminIds);
        crmCouponExpensePoolLogDtoPageInfo.getList().forEach(it -> {
            CrmCouponExpensePool crmCouponExpensePool = poolMap.get(it.getPoolId());
            if (crmCouponExpensePool!=null){
                it.setPoolName( crmCouponExpensePool.getName() );
                it.setProductRange(crmCouponExpensePool.getTargetType().intValue());
                it.setCostLimit(crmCouponExpensePool.getCostLimit());
                it.setAutoApprove(crmCouponExpensePool.getAutoApprove().intValue());
                it.setEndDate(crmCouponExpensePool.getEndDate());
                it.setStartDate(crmCouponExpensePool.getStartDate());
            }
            Integer changeCreateId = it.getChangeCreateId();
            it.setCreateName(bdRealNames.get(changeCreateId));
        });
        return CommonResult.ok(crmCouponExpensePoolLogDtoPageInfo);
    }



    private Map<Long, CrmCouponExpensePool> getPoolMap(List<Long> poolIds) {
        if (CollectionUtils.isEmpty(poolIds)) {
            return new HashMap<>();
        }
        CrmCouponExpensePoolQuery cluClueQuery = new CrmCouponExpensePoolQuery();
        cluClueQuery.setPoolIds(poolIds);
        Map<Long, CrmCouponExpensePool> collect = crmCouponExpensePoolMapper.selectByQuery(cluClueQuery).stream().collect(Collectors.toMap(CrmCouponExpensePool::getId, Function.identity()));
        return collect;
    }


    private List<String> buildPoolExt(CrmCouponExpensePoolVo crmCouponExpensePoolVo) {
        //不限
        if (crmCouponExpensePoolVo.getProductRange() == null || Objects.equals(CategoryQuotaEnum.TargetType.ALL.getCode().byteValue(), crmCouponExpensePoolVo.getProductRange())) {
            return new ArrayList<>();
        }
        //按照种类
        if (Objects.equals(CategoryQuotaEnum.TargetType.TYPE.getCode().byteValue(), crmCouponExpensePoolVo.getProductRange())) {
            if (CollectionUtils.isEmpty(crmCouponExpensePoolVo.getLimits())) {
                throw new BizException("选择商品范围不能为空");
            }
            return crmCouponExpensePoolVo.getLimits();
        }
        if (Objects.equals(CategoryQuotaEnum.TargetType.SKU.getCode().byteValue(), crmCouponExpensePoolVo.getProductRange())) {
            if (StringUtils.isEmpty(crmCouponExpensePoolVo.getUrl()) && CollectionUtils.isEmpty(crmCouponExpensePoolVo.getLimits())) {
                throw new BizException("选择商品范围不能为空");
            }
            List<String> limits = crmCouponExpensePoolVo.getLimits();
            if (!StringUtils.isEmpty(crmCouponExpensePoolVo.getUrl())) {
                limits = readExcel(crmCouponExpensePoolVo.getUrl());
            }
            return limits;
        }
        return new ArrayList<>();
    }


    public void updateAdminPool(Long poolId, Admin loginUser) {
        // 所有销售的费用池全部失效
        List<CrmCouponExpenseRecord> crmCouponExpenseRecords = crmCouponExpenseRecordMapper.selectByPoolId(poolId);
        if (CollectionUtils.isEmpty(crmCouponExpenseRecords)) {
            return;
        }
        for (CrmCouponExpenseRecord crmCouponExpenseRecord : crmCouponExpenseRecords) {
            Admin admin = adminMapper.selectByPrimaryKey(crmCouponExpenseRecord.getAdminId());
            CrmCouponExpensePoolDivideVo crmCouponExpensePoolDivideVo = new CrmCouponExpensePoolDivideVo();
            crmCouponExpensePoolDivideVo.setToAdminId(crmCouponExpenseRecord.getAdminId());
            crmCouponExpensePoolDivideVo.setPoolId(crmCouponExpenseRecord.getPoolId());
            crmCouponExpensePoolDivideVo.setAmount(crmCouponExpenseRecord.getRemainingAmount().negate());
            crmCouponExpensePoolDivideVo.setRemark("作废");
            generateQuotaChange(crmCouponExpensePoolDivideVo, admin, CouponEnum.CouponQuotaChangeType.CLOSE, loginUser.getAdminId(), loginUser.getRealname(), CATEGORY_PRICE.getCode());
        }

        //删除池子
        crmCouponExpenseRecordMapper.deleteByPoolId(poolId);
    }

    private CrmCouponExpensePool checkUpdate(Long id) {
        if (id == null) {
            throw new BizException("id不能为空");
        }
        CrmCouponExpensePool crmCouponExpensePool = crmCouponExpensePoolMapper.selectByPrimaryKey(id);
        if (crmCouponExpensePool == null) {
            throw new BizException("余额池不存在");
        }
        if (Objects.equals(crmCouponExpensePool.getStatus().intValue(), CategoryQuotaEnum.PoolStatus.DELETE.getCode())) {
            throw new BizException("已作废的不能被编辑");
        }
        return crmCouponExpensePool;
    }

    private List<String> readExcel(String url) {
        InputStream inputStream = null;
        try {
            inputStream = OssGetUtil.getInputStream(url);
            List<CrmCouponExpensePoolRange> insertList = EasyExcel.read(inputStream).head(CrmCouponExpensePoolRange.class).sheet().doReadSync();
            return insertList.stream().map(CrmCouponExpensePoolRange::getObjKey).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("费用池商品导入出错，msg:{}", e.getMessage(), e);
            throw new BizException("费用池商品导入出错");
        } finally {
            IoUtil.close(inputStream);
        }
    }

    private CrmCouponExpensePool buildCrmCouponExpensePool(CrmCouponExpensePoolVo addVo, Admin admin) {
        CrmCouponExpensePool crmCouponExpensePool = new CrmCouponExpensePool();
        crmCouponExpensePool.setTotalAmount(addVo.getTotalAmount());
        crmCouponExpensePool.setName(addVo.getName());
        crmCouponExpensePool.setStatus(addVo.getStatus());
        crmCouponExpensePool.setStartDate(addVo.getStartDate());
        crmCouponExpensePool.setEndDate(addVo.getEndDate());
        crmCouponExpensePool.setAutoApprove(addVo.getAutoApprove());
        crmCouponExpensePool.setTotalAmount(addVo.getTotalAmount());
        crmCouponExpensePool.setCreateUserName(admin.getRealname());
        crmCouponExpensePool.setCreateUserId(admin.getAdminId().longValue());
        crmCouponExpensePool.setCostLimit(addVo.getCostLimit());
        crmCouponExpensePool.setCreateTime(LocalDateTime.now());
        crmCouponExpensePool.setUpdateTime(LocalDateTime.now());
        crmCouponExpensePool.setTargetType(addVo.getProductRange());
        return crmCouponExpensePool;
    }

    private CrmCouponExpensePoolLogoVo buildCrmCouponExpensePoolLogoVo(Admin admin, BigDecimal amount, String toAdmin, String type, String remark) {
        CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo = new CrmCouponExpensePoolLogoVo();
        crmCouponExpensePoolLogoVo.setAmount(amount);
        crmCouponExpensePoolLogoVo.setAdminName(admin.getRealname());
        crmCouponExpensePoolLogoVo.setLocalDateTime(LocalDateTime.now());
        crmCouponExpensePoolLogoVo.setRemark(remark);
        crmCouponExpensePoolLogoVo.setType(type);
        crmCouponExpensePoolLogoVo.setToAdminName(toAdmin);
        return crmCouponExpensePoolLogoVo;
    }

    private CrmCouponExpensePoolLogDto logConvert(String log) {
        if (StringUtils.isEmpty(log)) {
            return null;
        }
        CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo = JSONUtil.toBean(log, CrmCouponExpensePoolLogoVo.class);
        return toCrmCouponExpensePoolLogDto(crmCouponExpensePoolLogoVo);
    }

    private void convertCrmCouponExpensePoolExtDto(Long poolId, List<CrmCouponExpensePoolExtDto> dos) {
        if (CollectionUtils.isEmpty(dos)) {
            return;
        }
        CrmCouponExpensePool crmCouponExpensePool = crmCouponExpensePoolMapper.selectByPrimaryKey(poolId);
        if (crmCouponExpensePool == null) {
            return;
        }
        if (CategoryQuotaEnum.TargetType.ALL.getCode().equals(crmCouponExpensePool.getTargetType().intValue())) {
            return;
        }
        if (CategoryQuotaEnum.TargetType.TYPE.getCode().equals(crmCouponExpensePool.getTargetType().intValue())) {
            dos.forEach(
                    it -> {
                        it.setCategoryName(CategoryTypeEnum.getValue(Integer.valueOf(it.getObjKey())));
                        if (crmCouponExpensePool.getTargetType()!=null){
                            it.setExtType(crmCouponExpensePool.getTargetType().intValue());
                        }
                    }
            );
            return;
        }
        if (CategoryQuotaEnum.TargetType.SKU.getCode().equals(crmCouponExpensePool.getTargetType().intValue())) {
            List<String> skus = dos.stream().map(CrmCouponExpensePoolExtDto::getObjKey).distinct().collect(Collectors.toList());
            Map<String, CrmSkuMonthGmvVO> skuBySkusMap = productsRepository.getSkuBySkus(skus);
            dos.forEach(
                    it -> {
                        CrmSkuMonthGmvVO crmSkuMonthGmvVO = skuBySkusMap.get(it.getObjKey());
                        it.setSku(it.getObjKey());
                        if (crmSkuMonthGmvVO != null) {
                            it.setProductName(crmSkuMonthGmvVO.getPdName());
                            it.setWeight(crmSkuMonthGmvVO.getWeight());
                            if (crmCouponExpensePool.getTargetType()!=null){
                                it.setExtType(crmCouponExpensePool.getTargetType().intValue());
                            }
                        }
                    }
            );
        }
    }

    private CrmBdOrg getParent(Integer adminId) {
        CrmBdOrg crmBdOrg = orgMapper.selectByBdIdAndRank(adminId, BD);
        if (crmBdOrg == null) {
            throw new BizException("销售组织不存在");
        }
        CrmBdOrg parent = orgMapper.selectByPrimaryKey(crmBdOrg.getParentId());
        if (parent == null) {
            throw new BizException("上级不存在，请指定上级");
        }
        return parent;
    }



}
