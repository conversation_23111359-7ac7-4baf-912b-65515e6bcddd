package net.summerfarm.crm.service.impl;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.util.SplitUtils;
import net.summerfarm.crm.facade.ContactQueryFacade;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.AdminMapper;
import net.summerfarm.crm.mapper.manage.ContactMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.dto.ContactDto;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.model.vo.PoiVO;
import net.summerfarm.crm.service.ContactService;
import net.summerfarm.pojo.DO.Admin;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ContactServiceImpl implements ContactService {
    @Resource
    ContactMapper contactMapper;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    AdminMapper adminMapper;
    @Resource
    FollowUpRelationMapper followUpRelationMapper;

    @Resource
    private ContactQueryFacade contactQueryFacade;

    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    @Override
    public AjaxResult<ContactDto> getContactDto(Long contactId) {
        //Contact contact = contactMapper.selectByPrimaryKey(contactId);
        ContactDto contactDto = contactQueryFacade.getMerchantContactById(contactId);
        if (contactDto == null) {
            return AjaxResult.getOK();
        }
        //MerchantVO merchantVO = merchantMapper.selectMerchantByMid(mid);

        MerchantVO merchantVO = followUpRelationMapper.queryFollow(contactDto.getmId());

        if (merchantVO != null && merchantVO.getAdminId() != null) {
            Admin admin = adminMapper.selectByPrimaryKey(merchantVO.getAdminId());
            if (admin != null) {
                contactDto.setBdName(admin.getRealname());
                contactDto.setBdPhone(admin.getPhone());
            }
        }
        return AjaxResult.getOK(contactDto);
    }

    @Override
    public CommonResult<Contact> selectContact(Long contactId) {
        //Contact contact = contactMapper.selectByPrimaryKey(contactId);
        ContactDto contactDto = contactQueryFacade.getMerchantContactById(contactId);
        if (contactDto==null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"联系人信息不存在或已被删除,请添加后选择地址");
        }
        PoiVO poiVO = SplitUtils.string2poi(contactDto.getPoiNote());
        contactDto.setPoi(poiVO);
        return CommonResult.ok(contactDto);
    }
}
