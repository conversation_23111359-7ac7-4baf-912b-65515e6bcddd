package net.summerfarm.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.facade.converter.clue.SaasClueCreateConverter;
import net.summerfarm.crm.facade.converter.clue.SaasClueDTOConverter;
import net.summerfarm.crm.mapper.manage.AdminMapper;
import net.summerfarm.crm.mapper.manage.CrmClueMapper;
import net.summerfarm.crm.model.domain.CrmClue;
import net.summerfarm.crm.model.dto.CrmClueDTO;
import net.summerfarm.crm.model.query.ClueDetailQuery;
import net.summerfarm.crm.model.query.SaasClueQuery;
import net.summerfarm.crm.model.vo.CrmClueBindVO;
import net.summerfarm.crm.model.vo.MidVO;
import net.summerfarm.crm.model.vo.SaasClueClueVO;
import net.summerfarm.crm.service.SaasClueService;
import net.summerfarm.pojo.DO.Admin;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SaasClueServiceImpl extends BaseService implements SaasClueService {
//    @Resource
//    SaasClueFacade saasClueFacade;
    @Resource
    CrmClueMapper clueMapper;
    @Resource
    AdminMapper adminMapper;

    @Override
    public CommonResult<PageInfo<CrmClueDTO>> query(SaasClueQuery cluClueQuery, UserBase userBase) {
        initQuery(cluClueQuery, userBase);
        PageHelper.startPage(cluClueQuery.getPageIndex(), cluClueQuery.getPageSize());
        PageInfo<CrmClue> pageInfo = PageInfoHelper.createPageInfo(clueMapper.query(cluClueQuery));
        PageInfo<CrmClueDTO> crmFollowDTOPageInfo = PageInfoConverter.toPageResp(pageInfo, SaasClueDTOConverter::toCluClueDTO);
        if (!CollectionUtils.isEmpty(crmFollowDTOPageInfo.getList())) {
            mergeAllData(crmFollowDTOPageInfo.getList());
        }
        return CommonResult.ok(crmFollowDTOPageInfo);

    }


    @Override
    public CommonResult<Boolean> queryBybId(MidVO midVO, UserBase userBase) {
        ClueDetailQuery clueDetailQuery = new ClueDetailQuery();
        clueDetailQuery.setBId(midVO.getBId());
        CrmClue detail = clueMapper.detail(clueDetailQuery);
        return CommonResult.ok(detail != null);
    }

    @Override
    public CommonResult<CrmClueDTO> insert(SaasClueClueVO cluClueVO, UserBase userBase) {
        if (cluClueVO.getBId() != null && cluClueVO.getBId() > 0) {
            Admin merchantVO = adminMapper.selectByPrimaryKey(cluClueVO.getBId().intValue());
            if (merchantVO == null) {
                throw new BizException("该品牌已被删除或不存在");
            }
            if (!Objects.equals(merchantVO.getNameRemakes(), cluClueVO.getCustomerName())) {
                throw new BizException("品牌名称不一致");
            }
        }
        ClueDetailQuery query = new ClueDetailQuery();
        query.setCustomerName(cluClueVO.getCustomerName());
        CrmClue detail = clueMapper.detail(query);
        if (detail != null) {
            throw new BizException("当前品牌已存在，不可重复新建");
        }
        if (cluClueVO.getBId() != null && cluClueVO.getBId() > 0) {
            ClueDetailQuery bIdQuery = new ClueDetailQuery();
            bIdQuery.setBId(cluClueVO.getBId());
            CrmClue one = clueMapper.detail(bIdQuery);
            if (one != null) {
                throw new BizException("当前品牌已存在，不可重复新建");
            }
        }
        CrmClue clue = SaasClueCreateConverter.toCrmClue(cluClueVO);
        clue.setBdId(userBase.getBizUserId());
        clue.setCreateBdId(userBase.getBizUserId());
        clue.setCreateTime(LocalDateTime.now());
        clue.setUpdateTime(LocalDateTime.now());
        clueMapper.insertSelective(clue);
        return CommonResult.ok(SaasClueDTOConverter.toCluClueDTO(clue));
    }

    @Override
    public CommonResult<CrmClueDTO> update(SaasClueClueVO cluClueVO, UserBase userBase) {
        if (cluClueVO.getId() == null) {
            throw new BizException("线索id不能为空");
        }
        CrmClue clue = SaasClueCreateConverter.toCrmClue(cluClueVO);
        clue.setBdId(userBase.getBizUserId());
        clue.setUpdateTime(LocalDateTime.now());
        int i = clueMapper.updateByPrimaryKey(clue);
        return CommonResult.ok(SaasClueDTOConverter.toCluClueDTO(clue));
    }

    @Override
    public CommonResult<CrmClueDTO> detail(ClueDetailQuery clueDetailQuery) {

        CrmClue clue = clueMapper.detail(clueDetailQuery);
        if (clue == null) {
            return CommonResult.ok();
        }
        CrmClueDTO cluClueDTO = SaasClueDTOConverter.toCluClueDTO(clue);
        List<CrmClueDTO> cluClueDTOS = Collections.singletonList(cluClueDTO);
        mergeAllData(cluClueDTOS);
        return CommonResult.ok(cluClueDTOS.get(0));
    }

    @Override
    public CommonResult<Boolean> bind(CrmClueBindVO crmFollowVO, UserBase userBase) {
        Admin admin = adminMapper.selectByPrimaryKey(crmFollowVO.getBId().intValue());
        if (admin == null) {
            throw new BizException("该品牌不存在");
        }
        //判断线索id是否存在
        CrmClue crmClue = clueMapper.selectByPrimaryKey(crmFollowVO.getClueId());
        if (crmClue == null) {
            throw new BizException("线索id不存在");
        }
        if (crmClue.getBId() != null && crmClue.getBId() > 0) {
            throw new BizException("当前品牌已被绑定，不可重复绑定");
        }
        CrmClue byBId = clueMapper.selectByBId(crmFollowVO.getBId());
        if (byBId != null) {
            throw new BizException("当前品牌已被绑定，不可重复绑定");
        }
        CrmClue update = new CrmClue();
        update.setId(crmClue.getId());
        update.setBId(crmFollowVO.getBId());
        update.setUpdateTime(LocalDateTime.now());
        clueMapper.updateByPrimaryKey(update);
        return CommonResult.ok(true);
    }


    /**
     * bd只能看自己的 主管们可以选择城市 主管选择城市清理到自己的bdId
     *
     * @param clueQueryReq
     * @param userBase
     */
    private void initQuery(SaasClueQuery clueQueryReq, UserBase userBase) {
        Integer bdId = userBase.getBizUserId();
        if (isBD()) {
            clueQueryReq.setBdId(bdId);
        }
        if ((isSA() || isAreaSA() || isSaleSA()) && !CollectionUtils.isEmpty(clueQueryReq.getAreaCodes())) {
            clueQueryReq.setBdId(null);
        }
    }


    private void mergeAllData(List<CrmClueDTO> cluClueDTOS) {
        if (CollectionUtils.isEmpty(cluClueDTOS)) {
            return;
        }
        List<Integer> allBdIds = new ArrayList<>();
        List<Integer> createBdIds = cluClueDTOS.stream().map(CrmClueDTO::getCreateBdId).collect(Collectors.toList());
        List<Integer> bdIds = cluClueDTOS.stream().map(CrmClueDTO::getBdId).collect(Collectors.toList());
        List<Integer> assBdIds = cluClueDTOS.stream().map(CrmClueDTO::getAssistBdId).collect(Collectors.toList());

        allBdIds.addAll(createBdIds);
        allBdIds.addAll(bdIds);
        allBdIds.addAll(assBdIds);
        if (CollectionUtils.isEmpty(allBdIds)) {
            return;
        }
        Map<Integer, String> bdRealNames = getBdRealNames(allBdIds);
        cluClueDTOS.forEach(
                it -> {
                    it.setBdName(bdRealNames.get(it.getBdId()));
                    it.setAssistBdName(bdRealNames.get(it.getAssistBdId()));
                    it.setCreateBdName(bdRealNames.get(it.getCreateBdId()));
                }
        );

    }
}
