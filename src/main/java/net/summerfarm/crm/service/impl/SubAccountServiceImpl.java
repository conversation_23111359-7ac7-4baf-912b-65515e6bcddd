package net.summerfarm.crm.service.impl;

import net.summerfarm.crm.facade.MerchantAccountQueryFacade;
import net.summerfarm.crm.mapper.manage.MerchantSubAccountMapper;
import net.summerfarm.crm.model.domain.MerchantSubAccount;
import net.summerfarm.crm.model.dto.SubAccountDTO;
import net.summerfarm.crm.service.SubAccountService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SubAccountServiceImpl implements SubAccountService {

    @Resource
    private MerchantAccountQueryFacade merchantAccountQueryFacade;
    @Resource
    MerchantSubAccountMapper merchantSubAccountMapper;

    @Override
    public CommonResult<List<SubAccountDTO>> subAccount(Long mid) {
        List<MerchantStoreAccountResultResp> accountResultResp = merchantAccountQueryFacade.getMerchantAccount(mid);
        List<SubAccountDTO> collect = accountResultResp.stream().map(this::merge).collect(Collectors.toList());
        return CommonResult.ok(collect);
    }

    private SubAccountDTO merge(MerchantStoreAccountResultResp account) {
        SubAccountDTO subAccountDTO = new SubAccountDTO();
        subAccountDTO.setMid(account.getMId());
        subAccountDTO.setPhone(account.getPhone());
        subAccountDTO.setContact(account.getAccountName());
        subAccountDTO.setAccountId(account.getXmAccountId());
        subAccountDTO.setLastLoginTime(account.getLastLoginTime());
        subAccountDTO.setType(account.getType());
        return subAccountDTO;
    }
}
