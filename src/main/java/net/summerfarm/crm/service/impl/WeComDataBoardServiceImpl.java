package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.WeComContactWayEnum;
import net.summerfarm.crm.enums.WeComEnum;
import net.summerfarm.crm.enums.WechatEnum;
import net.summerfarm.crm.mapper.manage.CrmBdOrgMapper;
import net.summerfarm.crm.mapper.manage.WecomCommunicationSummaryMapper;
import net.summerfarm.crm.mapper.manage.WecomGroupTaskMapper;
import net.summerfarm.crm.mapper.manage.WecomUserInfoMapper;
import net.summerfarm.crm.mapper.offline.CrmWecomStateMapper;
import net.summerfarm.crm.mapper.offline.CrmWecomUserSummaryMapper;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.WecomCommunicationSummary;
import net.summerfarm.crm.model.domain.WecomGroupTask;
import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.model.query.wecom.*;
import net.summerfarm.crm.model.query.wecom.contactway.WeComAddContactWayInput;
import net.summerfarm.crm.model.vo.weCom.*;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.WeComDataBoardService;
import net.summerfarm.crm.service.WechatService;
import net.summerfarm.crm.service.wecom.WeComContactWayService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.*;

/**
 * 企微数据看板
 *
 * <AUTHOR>
 * @date 2024/2/23 10:30
 */
@Slf4j
@Service
public class WeComDataBoardServiceImpl implements WeComDataBoardService {

    private static final Long USER_NOT_FOUND_CODE = 60111L;

    @Resource
    private WecomUserInfoMapper wecomUserInfoMapper;
    @Resource
    private WechatService wechatService;
    @Resource
    private WecomCommunicationSummaryMapper communicationSummaryMapper;
    @Resource
    private WecomGroupTaskMapper groupTaskMapper;
    @Resource
    private CrmBdOrgMapper orgMapper;
    @Resource
    private BdAreaConfigService areaConfigService;
    @Resource
    private CrmWecomUserSummaryMapper userSummaryMapper;
    @Resource
    private CrmWecomStateMapper wecomStateMapper;
    @Resource
    private WeComContactWayService weComContactWayService;

    @Override
    public void weComStateSync() {
        List<WecomUserInfo> userInfoList = wecomUserInfoMapper.selectList();
        for (WecomUserInfo userInfo : userInfoList) {
            if (StrUtil.isEmpty(userInfo.getUserId())) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("userid", userInfo.getUserId());
            String result = wechatService.get(GET_USER, map);
            WeComUserInfoRes weComUserInfo = WeComUserInfoRes.fromJson(result);
            if (!weComUserInfo.success()) {
                log.warn("同步企微用户状态失败,userid:{},resp:{}", userInfo.getUserId(), result);
                // 用户不存在, 则更新用户状态为离职
                if (USER_NOT_FOUND_CODE.equals(weComUserInfo.getErrcode())
                        && !ObjectUtil.equals(userInfo.getStatus(), weComUserInfo.getStatus())) {
                    userInfo.setStatus(WeComEnum.Status.QUIT.getCode());
                    wecomUserInfoMapper.updateByPrimaryKeySelective(userInfo);
                }
                continue;
            }
            if (!ObjectUtil.equals(userInfo.getStatus(), weComUserInfo.getStatus())) {
                userInfo.setStatus(weComUserInfo.getStatus());
                wecomUserInfoMapper.updateByPrimaryKeySelective(userInfo);

                // 如果状态为已激活, 则为用户添加联系我二维码
                if (weComUserInfo.getStatus() == 1) {
                    List<WeComContactWayEnum.State> stateList = Arrays.asList(WeComContactWayEnum.State.values());
                    stateList.forEach(state -> {
                        WeComAddContactWayInput input = WeComAddContactWayInput
                                .builder()
                                .remark("批量生成销售“联系我”二维码")
                                .state(state.getState())
                                .userId(userInfo.getUserId())
                                .build();
                        weComContactWayService.addContactWay(input);
                    });
                }
            }
        }
    }

    @Override
    public void userBehaviorData() {
        List<WecomUserInfo> userInfoList = wecomUserInfoMapper.selectList();
        LocalDate yesterday = LocalDate.now().minusDays(1);
        communicationSummaryMapper.deleteByDate(yesterday);
        long startTime = yesterday.atTime(LocalTime.MIN).toEpochSecond(ZoneOffset.UTC);
        List<WecomCommunicationSummary> communicationSummaryList = new ArrayList<>();
        for (WecomUserInfo userInfo : userInfoList) {
            WeComUserBehaviorInput behaviorInput = WeComUserBehaviorInput.builder().userid(Collections.singletonList(userInfo.getUserId())).startTime(startTime).endTime(startTime).build();
            String result = wechatService.post(USER_BEHAVIOR_DATA, behaviorInput.toJson());
            WeComUserBehaviorResp behaviorResp = WeComUserBehaviorResp.fromJson(result);
            if (!behaviorResp.success() || CollectionUtil.isEmpty(behaviorResp.getBehaviorData())) {
                log.warn("同步客户联系失败,result:{}", result);
                continue;
            }
            WeComUserBehaviorResp.BehaviorData behaviorData = behaviorResp.getBehaviorData().get(0);
            WecomCommunicationSummary communicationSummary = createCommunicationSummary(userInfo, behaviorData);
            communicationSummaryList.add(communicationSummary);
        }
        if (CollectionUtil.isNotEmpty(communicationSummaryList)) {
            communicationSummaryMapper.insertBatch(communicationSummaryList);
        }
    }

    @Override
    public void groupMsgData() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startTime = yesterday.atTime(LocalTime.MIN);
        LocalDateTime endTime = yesterday.atTime(LocalTime.MAX);
        groupTaskMapper.deleteByDate(LocalDate.now().atTime(LocalTime.MIN),LocalDate.now().atTime(LocalTime.MAX));

        String groupInput = WeComGroupMsgListInput.builder().chatType("single").startTime(startTime.toEpochSecond(ZoneOffset.UTC)).endTime(endTime.toEpochSecond(ZoneOffset.UTC)).build().toJson();
        String groupMsg = wechatService.post(GET_GROUP_MSG_LIST, groupInput);
        WeComGroupMsgListResp groupMsgListResp = WeComGroupMsgListResp.fromJson(groupMsg);
        if (!groupMsgListResp.success() || CollectionUtil.isEmpty(groupMsgListResp.getGroupMsgList())) {
            log.warn("群发任务同步失败或者为空,resp:{}", groupMsg);
            return;
        }
        Map<String, WecomUserInfo> userInfoMap = wecomUserInfoMapper.selectList().stream().collect(Collectors.toMap(WecomUserInfo::getUserId, Function.identity()));

        List<WecomGroupTask> taskList = new ArrayList<>();
        for (WeComGroupMsgListResp.GroupMsg msg : groupMsgListResp.getGroupMsgList()) {
            String msgTaskInput = WeComGroupMsgTaskInput.builder().msgid(msg.getMsgid()).build().toJson();
            String msgTaskPost = wechatService.post(GET_GROUP_MSG_TASK, msgTaskInput);
            WeComGroupTaskResp taskResp = WeComGroupTaskResp.fromJson(msgTaskPost);
            if (!taskResp.success() || CollectionUtil.isEmpty(taskResp.getTaskList())) {
                log.warn("群发成员发送任务列表同步失败或者为空,resp:{}", msgTaskPost);
                continue;
            }
            for (WeComGroupTaskResp.TaskList task : taskResp.getTaskList()) {
                if (!userInfoMap.containsKey(task.getUserid())) {
                    continue;
                }
                WecomUserInfo userInfo = userInfoMap.get(task.getUserid());
                WecomGroupTask wecomGroupTask = new WecomGroupTask();
                wecomGroupTask.setBdId(userInfo.getAdminId());
                wecomGroupTask.setBdName(userInfo.getAdminName());
                wecomGroupTask.setMessageId(msg.getMsgid());
                wecomGroupTask.setMessageStatus(task.getStatus());

                String json = WeComGroupMsgSendResultInput.builder().msgid(msg.getMsgid()).userid(task.getUserid()).build().toJson();
                String sendResultPost = wechatService.post(GET_GROUP_MSG_SEND_RESULT, json);
                WeComGroupMsgSendResultResp sendResultResp = WeComGroupMsgSendResultResp.fromJson(sendResultPost);
                if (!sendResultResp.success() || CollectionUtil.isEmpty(sendResultResp.getSendList())) {
                    log.warn("群发成员发送任务列表同步失败或者为空,resp:{}", msgTaskPost);
                    sendResultResp.setSendList(new ArrayList<>());
                }
                long count = sendResultResp.getSendList().stream().filter(send -> ObjectUtil.equal(WechatEnum.GroupTaskSendStatusEnum.SEND_OK.getStatus(), send.getStatus())).count();
                wecomGroupTask.setSendCount((int) count);
                taskList.add(wecomGroupTask);
            }
        }
        if (!taskList.isEmpty()) {
            groupTaskMapper.insertBatch(taskList);
        }
    }

    @Override
    public List<WeComActivateVo> activateStatus(WeComBdQuery query) {
        CrmBdOrg topRankOrg = areaConfigService.getTopRankOrg();
        // 查看自己的下属激活状态
        if (query.getOrgId() == null) {
            WeComActivateVo activateStatus = getActivateStatus(topRankOrg);
            if (activateStatus == null) {
                return new ArrayList<>();
            }
            return Collections.singletonList(activateStatus);
        }
        if (BdAreaConfigEnum.isCityManager(topRankOrg.getRank())) {
            return new ArrayList<>();
        }
        // 查看指定下属激活状态
        List<WeComActivateVo> activateVoList = new ArrayList<>();
        List<CrmBdOrg> org = orgMapper.selectByParentId(query.getOrgId());
        org.forEach(o -> {
            WeComActivateVo activateStatus = getActivateStatus(o);
            if (activateStatus == null) {
                return;
            }
            activateVoList.add(activateStatus);
        });
        return activateVoList;
    }

    public WeComActivateVo getActivateStatus(CrmBdOrg topRankOrg) {
        if (topRankOrg == null || BdAreaConfigEnum.isDepartmentManager(topRankOrg.getRank())) {
            return null;
        }
        if (BdAreaConfigEnum.isBd(topRankOrg.getRank())) {
            return null;
        }
        if (BdAreaConfigEnum.isCityManager(topRankOrg.getRank())) {
            WeComActivateVo weComActivateVo = wecomStateMapper.selectActivateByBdId(Collections.singletonList(topRankOrg.getBdId()));
            weComActivateVo.setBdName(topRankOrg.getBdName());
            weComActivateVo.setBdId(topRankOrg.getBdId());
            weComActivateVo.setOrgId(topRankOrg.getId());
            weComActivateVo.setRank(topRankOrg.getRank());
            return weComActivateVo;
        }
        List<CrmBdOrg> childrenOrg = orgMapper.selectByParentId(topRankOrg.getId());
        List<Integer> childrenId = childrenOrg.stream().map(CrmBdOrg::getBdId).collect(Collectors.toList());
        if (childrenId.isEmpty()) {
            return null;
        }
        WeComActivateVo weComActivateVo = wecomStateMapper.selectActivateByBdId(childrenId);
        weComActivateVo.setBdName(topRankOrg.getBdName());
        weComActivateVo.setBdId(topRankOrg.getBdId());
        weComActivateVo.setOrgId(topRankOrg.getId());
        weComActivateVo.setRank(topRankOrg.getRank());
        return weComActivateVo;
    }

    @Override
    public List<WeComActivateStatusVo> activateStatusList(WeComBdQuery query) {
        // 查看自己激活状态
        if (query.getOrgId() == null) {
            CrmBdOrg topRankOrg = areaConfigService.getTopRankOrg();
            if (topRankOrg == null || !BdAreaConfigEnum.isBd(topRankOrg.getRank())) {
                return new ArrayList<>();
            }
            return wecomStateMapper.selectStateByBdId(topRankOrg.getBdId());
        }
        // 查看下属激活状态
        CrmBdOrg org = orgMapper.selectByPrimaryKey(query.getOrgId());
        if (org == null || !BdAreaConfigEnum.isCityManager(org.getRank())) {
            return new ArrayList<>();
        }
        return wecomStateMapper.selectStateByParentId(org.getBdId());
    }

    @Override
    public List<WeComUserSummaryVo> userSummary(WeComBdQuery query) {
        CrmBdOrg bdOrg = areaConfigService.getTopRankOrg();
        if (bdOrg == null) {
            return new ArrayList<>();
        }
        // m3不展示 & bd不支持查询
        if (BdAreaConfigEnum.isDepartmentManager(bdOrg.getRank())) {
            return new ArrayList<>();
        }
        // 展示自身及其下属信息
        if (query.getOrgId() == null) {
            WeComUserSummaryVo summaryVo = getSummaryVo(bdOrg);
            if (summaryVo == null) {
                return new ArrayList<>();
            }
            return Collections.singletonList(summaryVo);
        }
        List<WeComUserSummaryVo> summaryVoList = new ArrayList<>();

        // 展示指定下属信息
        List<CrmBdOrg> childrenOrg = orgMapper.selectByParentId(query.getOrgId());
        for (CrmBdOrg org : childrenOrg) {
            WeComUserSummaryVo summaryVo = getSummaryVo(org);
            if (summaryVo == null) {
                continue;
            }
            summaryVoList.add(summaryVo);
        }
        return summaryVoList;
    }

    public WeComUserSummaryVo getSummaryVo(CrmBdOrg org) {
        List<Integer> childrenBdId = getChildrenBdId(org);
        if (childrenBdId.isEmpty()) {
            return null;
        }
        // 当前用户私海信息
        WeComUserSummaryVo weComUserSummaryVo = userSummaryMapper.summaryByBdId(childrenBdId);
        weComUserSummaryVo.setOrgId(org.getId());
        weComUserSummaryVo.setRank(org.getRank());
        weComUserSummaryVo.setBdName(org.getBdName());
        weComUserSummaryVo.setBdId(org.getBdId());
        return weComUserSummaryVo;
    }

    @Override
    public List<WeComCommunicationSummaryVo> communicationSummary(WeComBdQuery query) {
        CrmBdOrg org = areaConfigService.getTopRankOrg();
        if (org == null) {
            return new ArrayList<>();
        }
        if (BdAreaConfigEnum.isDepartmentManager(org.getRank())) {
            return new ArrayList<>();
        }
        List<Integer> childrenBdId = getChildrenBdId(org);
        if (childrenBdId.isEmpty()) {
            return new ArrayList<>();
        }
        List<WecomCommunicationSummary> communicationSummaryList = communicationSummaryMapper.selectByBdId(childrenBdId);
        return communicationSummaryList.stream().map(WeComCommunicationSummaryVo::toVo).collect(Collectors.toList());
    }

    @Override
    public List<WeComTaskSummaryVo> taskSummary(WeComBdQuery query) {
        // 查询当前用户企微任务
        if (query.getOrgId() == null) {
            CrmBdOrg topRankOrg = areaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                return new ArrayList<>();
            }
            WeComTaskSummaryVo taskSummaryVo = getTaskSummaryVo(topRankOrg);
            if (taskSummaryVo == null) {
                return new ArrayList<>();
            }
            return Collections.singletonList(taskSummaryVo);
        }
        // 查询下属指定用户企微任务
        List<CrmBdOrg> crmBdOrgList = orgMapper.selectByParentId(query.getOrgId());
        List<WeComTaskSummaryVo> summaryVoList = new ArrayList<>();
        for (CrmBdOrg org : crmBdOrgList) {
            WeComTaskSummaryVo taskSummaryVo = getTaskSummaryVo(org);
            if (taskSummaryVo == null) {
                continue;
            }
            summaryVoList.add(taskSummaryVo);
        }
        return summaryVoList;
    }

    public WeComTaskSummaryVo getTaskSummaryVo(CrmBdOrg topRankOrg) {
        List<Integer> childrenBdId = getChildrenBdId(topRankOrg);
        if (childrenBdId.isEmpty()) {
            return null;
        }
        WeComTaskSummaryVo summaryVo = groupTaskMapper.selectByBdId(childrenBdId);
        summaryVo.setBdId(topRankOrg.getBdId());
        summaryVo.setBdName(topRankOrg.getBdName());
        summaryVo.setOrgId(topRankOrg.getId());
        summaryVo.setRank(topRankOrg.getRank());
        return summaryVo;
    }

    public List<Integer> getChildrenBdId(CrmBdOrg org) {
        List<Integer> childrenBdId;
        if (BdAreaConfigEnum.isBd(org.getRank())) {
            childrenBdId = Collections.singletonList(org.getBdId());
        } else {
            List<CrmBdOrg> childrenBd = areaConfigService.listChildrenBd(org.getId());
            if (childrenBd == null) {
                return new ArrayList<>();
            }
            childrenBdId = childrenBd.stream().filter(o -> BdAreaConfigEnum.isBd(o.getRank())).map(CrmBdOrg::getBdId).collect(Collectors.toList());
        }
        return childrenBdId;
    }

    public WecomCommunicationSummary createCommunicationSummary(WecomUserInfo userInfo, WeComUserBehaviorResp.BehaviorData behaviorData) {
        WecomCommunicationSummary communicationSummary = new WecomCommunicationSummary();
        communicationSummary.setBdId(userInfo.getAdminId());
        communicationSummary.setBdName(userInfo.getAdminName());
        communicationSummary.setDate(LocalDate.now().minusDays(1));
        communicationSummary.setConversationCount(behaviorData.getChatCnt());
        communicationSummary.setMessageCount(behaviorData.getMessageCnt());
        communicationSummary.setAnswerProportion(behaviorData.getReplyPercentage() != null ? behaviorData.getReplyPercentage() : BigDecimal.ZERO);
        communicationSummary.setAnswerDuration(behaviorData.getAvgReplyTime() != null ? behaviorData.getAvgReplyTime() : 0);
        return communicationSummary;
    }
}
