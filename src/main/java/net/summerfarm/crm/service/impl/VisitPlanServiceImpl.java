package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.BeanCopyUtil;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.StoreSizeEnum;
import net.summerfarm.crm.enums.VisitPlanEnum;
import net.summerfarm.crm.facade.ContactQueryFacade;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.repository.MerchantExtendsRepository;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.ContactDto;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.crm.model.dto.VisitPlanDTO;
import net.summerfarm.crm.model.query.FollowUpQuery;
import net.summerfarm.crm.model.query.VisitPlanQuery;
import net.summerfarm.crm.model.vo.VisitPlanVO;
import net.summerfarm.crm.service.VisitPlanService;
import net.summerfarm.pojo.DO.Admin;
import net.xianmu.common.result.CommonResult;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

;

/**
 * <AUTHOR> ct
 * create at:  2019/8/1  6:03 PM
 */
@Service("visitPlanService")
public class VisitPlanServiceImpl extends BaseService implements VisitPlanService {
    @Resource
    private VisitPlanMapper visitPlanMapper;
    @Resource
    private FollowUpRecordMapper followUpRecordMapper;
    @Resource
    private ConfigMapper configMapper;

    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private CrmEscortVisitPlanMapper crmEscortVisitPlanMapper;
    @Resource
    MerchantQueryFacade merchantQueryFacade;
    @Resource
    private MerchantExtendsRepository merchantExtendsRepository;
    @Resource
    private ContactQueryFacade contactQueryFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertVisitPlan(VisitPlanDTO visitPlanDTO) {
        if(ObjectUtil.notEqual(VisitPlanEnum.Type.LEADS.ordinal(),visitPlanDTO.getType()) ){
            boolean whetherPlan = this.selectWhetherPlan(visitPlanDTO);
            if (whetherPlan){
                return AjaxResult.getErrorWithMsg("当天已有拜访计划");
            }
        }

        MerchantStoreAndExtendResp merchantExtend = merchantQueryFacade.getMerchantExtendsByMid(visitPlanDTO.getmId());
        if (merchantExtend==null){
            return AjaxResult.getErrorWithMsg("当前门店不存在，请刷新后重试");
        }
        visitPlanDTO.setProvince(merchantExtend.getProvince());
        visitPlanDTO.setCity(merchantExtend.getCity());
        visitPlanDTO.setArea(merchantExtend.getArea());
        visitPlanDTO.setAreaNo(merchantExtend.getAreaNo());

        // 2024.11.25去除公海不可拜访限制
//        FollowUpRelation followUpRelation = followUpRelationMapper.selectByMid(visitPlanDTO.getmId().intValue());
//        if(followUpRelation.getReassign()){
//            return AjaxResult.getErrorWithMsg("当前客户无归属BD，请先拉入私海后操作");
//        }
        //查询线索池信息

        // 插入拜访计划
        this.insertPlan(visitPlanDTO);

        // 如果有陪访人,插入陪访计划
        if(CollectionUtil.isNotEmpty(visitPlanDTO.getEscortAdminIdList())){
            this.insertEscortVisitPlan(visitPlanDTO);
        }



        return AjaxResult.getOK();
    }

    /**
     * 插入陪访计划
     * @param visitPlanDTO 陪访计划信息
     */
    private void insertEscortVisitPlan(VisitPlanDTO visitPlanDTO) {
        for (Long adminId : visitPlanDTO.getEscortAdminIdList()) {
            CrmEscortVisitPlan crmEscortVisitPlan = new CrmEscortVisitPlan(adminId,visitPlanDTO.getId(),super.getAdminName());
            BeanCopyUtil.copyProperties(visitPlanDTO,crmEscortVisitPlan,"id","adminId");
            crmEscortVisitPlanMapper.insertEscortVisitPlan(crmEscortVisitPlan);
        }
    }

    /**
     * 判断当天是否已有拜访计划
     * @param visitPlan 新增的拜访计划信息
     * @return 是否
     */
    private boolean selectWhetherPlan(VisitPlan visitPlan){
        VisitPlanQuery query = new VisitPlanQuery();
        query.setStatus(VisitPlanEnum.Status.WAIT.ordinal());
        query.setmId(visitPlan.getmId());
        query.setContactId(visitPlan.getContactId());
        query.setExpectedTime(visitPlan.getExpectedTime());
        query.setAdminId(super.getAdminId());
        List<VisitPlanVO> voList = queryVisitPlanList(query);
        if (CollectionUtils.isEmpty(voList)){
            return false;
        }
        return true;
    }

    @Override
    public AjaxResult queryVisitPlanList(int pageIndex, int pageSize, VisitPlanQuery visitPlanQuery) {
        visitPlanQuery = Optional.ofNullable(visitPlanQuery).orElse(new VisitPlanQuery());
        //销售看自己 其他看自己
        if (!(isAreaSA() || isSaleSA() || isSA())) {
            visitPlanQuery.setAdminId(super.getAdminId());

        }
        // 默认近一个月
        if(Objects.isNull(visitPlanQuery.getStartTime())){
            visitPlanQuery.setStartTime(LocalDateTime.now().minusMonths(NumberUtils.INTEGER_ONE));
        }

        List<VisitPlanVO> visitPlanList;
        PageHelper.startPage(pageIndex,pageSize);
        if(Objects.equals(VisitPlanEnum.Type.ESCORT.ordinal(),visitPlanQuery.getType())){
            visitPlanList = queryEscortPlan(visitPlanQuery);
            visitPlanList.forEach(v -> v.setType(VisitPlanEnum.Type.ESCORT.ordinal()));
        }else {
            visitPlanList = queryVisitPlanList(visitPlanQuery);
        }

        for (VisitPlanVO visitPlanVO : visitPlanList) {
            Admin admin = adminMapper.selectByPrimaryKey(visitPlanVO.getAdminId());
            visitPlanVO.setAdminName(admin.getRealname());
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(visitPlanList));
    }


    @Override
    public AjaxResult queryVisitPlan(Long visitPlanId, Integer type) {
        VisitPlanVO visitPlanVO;
        // 陪访计划详情
        if(Objects.equals(VisitPlanEnum.Type.ESCORT.ordinal(),type)){
            VisitPlanQuery visitPlanQuery = new VisitPlanQuery();
            visitPlanQuery.setId(visitPlanId);
            List<VisitPlanVO> visitPlanVOList = queryEscortPlan(visitPlanQuery);
            if(CollectionUtil.isEmpty(visitPlanVOList)){
                return AjaxResult.getOK();
            }
            visitPlanVO = visitPlanVOList.get(0);
            // 拜访记录id
            FollowUpRecord followUpRecord = followUpRecordMapper.selectByVisitPlanId(null,visitPlanId);
            if (Objects.nonNull(followUpRecord)) {
                visitPlanVO.setFollowUpRecordId(followUpRecord.getId());
            }

        } else {
            // 拜访,拉新
            visitPlanVO = visitPlanMapper.queryVisitPlanOne(visitPlanId);
            if (Objects.isNull(visitPlanVO)) {
                return AjaxResult.getErrorWithMsg("该拜访计划不存在");
            }
            MerchantStoreAndExtendResp merchant = merchantQueryFacade.getMerchantExtendsByMid(visitPlanVO.getmId());
            if (merchant != null) {
                visitPlanVO.setMname(merchant.getStoreName());
                visitPlanVO.setmSize(StoreSizeEnum.getSize(merchant.getSize()).getDesc());
                if (!ObjectUtil.equal(visitPlanVO.getType(), VisitPlanEnum.Type.LEADS.ordinal())) {
                    visitPlanVO.setAreaNo(merchant.getAreaNo());
                }
            }
            if (visitPlanVO.getContactId() != null) {
                ContactDto contactDto = contactQueryFacade.getMerchantContactById(visitPlanVO.getContactId().longValue());
                visitPlanVO.setByContact(contactDto);
            }

            // 陪访人姓名
            List<VisitPlanVO> visitPlanVOList = crmEscortVisitPlanMapper.queryEscortPlanByVisitPlanId(visitPlanId);
            if(CollectionUtil.isNotEmpty(visitPlanVOList)){
                StringBuilder stringBuffer = new StringBuilder();
                for (VisitPlanVO planVO : visitPlanVOList) {
                    stringBuffer.append(planVO.getAdminName())
                            .append(".");
                }

                visitPlanVO.setEscortAdminName(stringBuffer.toString());
            }

            // 拜访记录id
            FollowUpRecord followUpRecord = followUpRecordMapper.selectByVisitPlanId(visitPlanId,null);
            if (Objects.nonNull(followUpRecord)) {
                visitPlanVO.setFollowUpRecordId(followUpRecord.getId());
            }
        }
        return AjaxResult.getOK(visitPlanVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult cancelPlan(VisitPlanDTO visitPlanDTO) {
        if(Objects.equals(VisitPlanEnum.Type.ESCORT.ordinal(),visitPlanDTO.getType())){{
            // 取消陪访计划
            this.cancelEscortPlan(visitPlanDTO);
            return AjaxResult.getOK();
        }}

        // 取消拜访计划
        VisitPlan visitPlan = visitPlanMapper.selectById(visitPlanDTO.getId());
        // 计划有陪访人员
        List<VisitPlanVO> visitPlanVOList = crmEscortVisitPlanMapper.queryEscortPlanByVisitPlanId(visitPlanDTO.getId());
        if(CollectionUtil.isNotEmpty(visitPlanVOList)){
            List<VisitPlanVO> collect = visitPlanVOList.stream()
                    .filter(plan -> Objects.equals(VisitPlanEnum.Status.FINISH.ordinal(), plan.getStatus()))
                    .collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
                return AjaxResult.getErrorWithMsg("主管已陪访完成,请继续填写完成拜访记录");
            }
            // 取消陪访计划
            for (VisitPlanVO visitPlanVO : visitPlanVOList) {
                VisitPlanDTO cancelPlan = new VisitPlanDTO();
                cancelPlan.setId(visitPlanVO.getId());
                cancelPlan.setCancelContent(visitPlanDTO.getCancelContent());
                this.cancelEscortPlan(cancelPlan);
            }
        }

        // 取消该拜访计划
        VisitPlan update = new VisitPlan();
        update.setId(visitPlan.getId());
        update.setStatus(VisitPlanEnum.Status.CANCEL.ordinal());
        update.setCancelContent(visitPlanDTO.getCancelContent());
        visitPlanMapper.updateVisitPlan(update);
        return AjaxResult.getOK();
    }

    /**
     * 取消陪访计划
     * @param cancelPlan 取消内容
     */
    private void cancelEscortPlan(VisitPlanDTO cancelPlan) {
        cancelPlan.setStatus(VisitPlanEnum.Status.CANCEL.ordinal());
        cancelPlan.setUpdater(getAdminName());
        crmEscortVisitPlanMapper.updateVisitPlan(cancelPlan);
    }

    @Override
    public void sendDingTalkMsg() {
        LocalDate localDate = LocalDate.now();
        VisitPlanQuery query = new VisitPlanQuery();
        query.setStartTime(LocalDateTime.of(localDate,LocalTime.MIN));
        query.setEndTime(LocalDateTime.of(localDate,LocalTime.MAX));
        query.setStatus(VisitPlanEnum.Status.WAIT.ordinal());
        List<VisitPlanVO> visitPlanVOS = queryVisitPlanList(query);
        if(CollectionUtils.isEmpty(visitPlanVOS)){
            return;
        }
        //根据adminId分组
        Map<Integer, List<VisitPlanVO>> collect = visitPlanVOS.stream().collect(Collectors.groupingBy(VisitPlan::getAdminId));
        Config config = configMapper.selectOne("visitPlanRobotUrl");
        String url = config.getValue();

        collect.forEach((adminId, visitPlanVOList) -> {
            HashMap<String, String> msgMap = new HashMap<>(NumberUtils.INTEGER_TWO);

            String adminName = visitPlanVOList.get(0).getCreator();
            StringBuffer title = new StringBuffer(adminName);
            title.append(localDate.getMonthValue())
                    .append("月")
                    .append(localDate.getDayOfMonth())
                    .append("日拜访计划");
            msgMap.put("title", title.toString());

            StringBuffer text = new StringBuffer("#### " + title + "\n");
            int i = 0;
            int count = 0;
            for (VisitPlanVO visitPlanVO : visitPlanVOList) {
                if(VisitPlanEnum.Type.LEADS.ordinal() == visitPlanVO.getType()){
                    count++;
                    continue;
                }

                i++;
                text.append("> ")
                        .append("###### ").append(i).append(". ")
                        .append(visitPlanVO.getMname())
                        .append(",")
                        .append(visitPlanVO.getAddress())
                        .append(",")
                        .append(visitPlanVO.getmSize());
                if (!StringUtils.isEmpty(visitPlanVO.getExpectedContent())) {
                    text.append(",")
                            .append(visitPlanVO.getExpectedContent());
                }
                text.append("\n\n");
            }

            //拉新
            if(count > 0){
                text.append("> ").append("拉新").append(count).append("家客户");
            }

            msgMap.put("text", text.toString());
            logger.info("发送{}拜访计划消息至销售群",adminName);
            DingTalkRobotUtil.sendMarkDownMsg(url, () -> msgMap, null);
        });
    }

    @Override
    public CommonResult<List<VisitPlanVO>> queryPlanByDate(VisitPlanQuery visitPlanQuery) {
        VisitPlanVO query = new VisitPlanVO();
        query.setAdminId(getAdminId());
        query.setDate(visitPlanQuery.getDate());
        query.setAreaNo(visitPlanQuery.getAreaNo());
        query.setType(visitPlanQuery.getType());
        query.setProvince(visitPlanQuery.getProvince());
        query.setCity(visitPlanQuery.getCity());
        query.setArea(visitPlanQuery.getArea());
        List<VisitPlanVO> planList = visitPlanMapper.selectList(query);
        fillMerchantAndAreaName(planList);

        // 拉新
        if(Objects.equals(VisitPlanEnum.Type.LEADS.ordinal(),visitPlanQuery.getType())){
            return CommonResult.ok(planList);
        }

        planList = Optional.ofNullable(planList).orElse(new ArrayList<>());
        for (VisitPlanVO visitPlanVO : planList) {
            List<VisitPlanVO> visitPlanVOList = crmEscortVisitPlanMapper.queryEscortPlanByVisitPlanId(visitPlanVO.getId());
            if(CollectionUtil.isNotEmpty(visitPlanVOList)){
                StringBuilder stringBuffer = new StringBuilder();
                for (VisitPlanVO planVO : visitPlanVOList) {
                    stringBuffer.append(planVO.getAdminName())
                            .append(".");
                }
                visitPlanVO.setEscortAdminName(stringBuffer.toString());
            }
        }

        // 陪访计划
        List<VisitPlanVO> escortPlanList = queryEscortPlan(visitPlanQuery);
        if(CollectionUtil.isNotEmpty(escortPlanList)){
            for (VisitPlanVO visitPlanVO : escortPlanList) {
                visitPlanVO.setType(VisitPlanEnum.Type.ESCORT.ordinal());
                Admin admin = adminMapper.selectByPrimaryKey(visitPlanVO.getAdminId());
                admin = Optional.ofNullable(admin).orElse(new Admin());
                visitPlanVO.setAdminName(admin.getRealname());
            }
            planList.addAll(escortPlanList);
        }

        return CommonResult.ok(planList);
    }

    @Override
    public int updateUnHandlePlan(LocalDateTime time) {
        logger.info("取消未拜访的拜访计划:{}",LocalDateTime.now());
        return visitPlanMapper.updateUnHandlePlan(time);
    }

    @Override
    public CommonResult<Integer> count(String province, String city, String area, LocalDate localDate) {
        // 拜访
        VisitPlanVO planQuery = new VisitPlanVO();
        planQuery.setProvince(province);
        planQuery.setCity(city);
        planQuery.setArea(area);
        planQuery.setDate(localDate);
        planQuery.setAdminId(getAdminId());
        List<VisitPlanVO> visitPlanVOList = visitPlanMapper.selectList(planQuery);
        int visitSize = CollectionUtil.isEmpty(visitPlanVOList) ? NumberUtils.INTEGER_ZERO : visitPlanVOList.size();

        // 陪访
        VisitPlanQuery visitPlanQuery = new VisitPlanQuery();
        BeanCopyUtil.copyProperties(planQuery,visitPlanQuery);
        List<VisitPlanVO> escortPlanList = queryEscortPlan(visitPlanQuery);
        int escortSize = CollectionUtil.isEmpty(escortPlanList) ? NumberUtils.INTEGER_ZERO : escortPlanList.size();

        return CommonResult.ok(visitSize + escortSize);
    }

    @Override
    public AjaxResult createBatch(Integer count, String province, String city, String date) {
        VisitPlan visitPlan = new VisitPlan();
        visitPlan.setProvince(province);
        visitPlan.setCity(city);
        visitPlan.setStatus(VisitPlanEnum.Status.WAIT.ordinal());
        visitPlan.setExpectedTime(LocalDateTime.of(DateUtils.date2LocalDate(DateUtils.string2Date(date, DateUtils.DEFAULT_DATE_FORMAT)), LocalTime.now()));
        visitPlan.setAdminId(getAdminId());
        visitPlan.setCreator(getAdminName());
        visitPlan.setType(VisitPlanEnum.Type.LEADS.ordinal());

        for (int i = 0; i < count; i++) {
            visitPlan.setId(null);
            visitPlanMapper.insertVisitPlan(visitPlan);
        }

        return AjaxResult.getOK();
    }

    @Override
    public CommonResult<PageInfo<VisitPlanVO>> queryFollowup(FollowUpQuery input) {
        PageHelper.startPage(input.getPageIndex(), input.getPageSize());
        VisitPlanQuery query = new VisitPlanQuery();
        query.setAdminId(getAdminId());
        query.setSource(1);
        query.setStatus(0);
        List<VisitPlanVO> visitPlanVOS = visitPlanMapper.queryVisitPlanListNew(query);
        if (!CollectionUtils.isEmpty(visitPlanVOS)) {
            List<Long> mIds = visitPlanVOS.stream().map(VisitPlanVO::getmId).collect(Collectors.toList());
            List<Long> contactIds = visitPlanVOS.stream().map(VisitPlanVO::getContactId)
                    .collect(Collectors.toList())
                    .stream()
                    .map(Integer::longValue)
                    .collect(Collectors.toList());
            List<MerchantStoreAndExtendDTO> merchantStoreAndExtendDTOS =  merchantExtendsRepository.getAuthMerchantExtendDTO(mIds,getCompatibleDataPermission());
            if (CollectionUtil.isNotEmpty(merchantStoreAndExtendDTOS)){
                //批量查询地址信息
                List<ContactDto> contactDtos = contactQueryFacade.getMerchantContactList(contactIds);
                Map<Long,ContactDto> contactDtoMap = contactDtos.stream().collect(Collectors.toMap(ContactDto::getContactId, Function.identity()));
                //过滤没有权限的商户
                List<Long> authMId = merchantStoreAndExtendDTOS.stream().map(MerchantStoreAndExtendDTO::getMId).collect(Collectors.toList());
                Map<Long, MerchantStoreAndExtendDTO> merchantMap = merchantStoreAndExtendDTOS.stream().collect(Collectors.toMap(MerchantStoreAndExtendDTO::getMId, Function.identity()));
                visitPlanVOS = visitPlanVOS.stream().filter(it -> authMId.contains(it.getmId())).collect(Collectors.toList());
                visitPlanVOS.forEach(visitPlanVO -> {
                    visitPlanVO.setMname(merchantMap.get(visitPlanVO.getmId()).getStoreName());
                    visitPlanVO.setGrade(merchantMap.get(visitPlanVO.getmId()).getGrade());
                    visitPlanVO.setmSize(ObjectUtil.equal(merchantMap.get(visitPlanVO.getmId()).getSize(), ADMIN.getCode()) ? ADMIN.getDesc() : MERCHANT.getDesc());
                    visitPlanVO.setMainType(merchantMap.get(visitPlanVO.getmId()).getBusinessType());
                    if (visitPlanVO.getType() != VisitPlanEnum.Type.LEADS.ordinal()){
                        visitPlanVO.setAreaNo(merchantMap.get(visitPlanVO.getmId()).getAreaNo());
                    }
                    if (visitPlanVO.getContactId() != null) {
                        ContactDto contactDto = contactDtoMap.get(Long.valueOf(visitPlanVO.getContactId()));
                        visitPlanVO.setByContact(contactDto);
                    }
                });

                List<Integer> ids = visitPlanVOS.stream().map(VisitPlanVO::getAdminId).collect(Collectors.toList());
                List<Admin> admins = adminMapper.selectByIds(ids);
                if (!CollectionUtils.isEmpty(admins)) {
                    Map<Integer, String> collect = admins.stream().collect(Collectors.toMap(Admin::getAdminId, Admin::getRealname));
                    visitPlanVOS.forEach(
                            it -> it.setAdminName(collect.get(it.getAdminId()))
                    );
                }
            }
        }

        return CommonResult.ok(PageInfoHelper.createPageInfo(visitPlanVOS));
    }

    @Override
    public void fillMerchantAndAreaName(List<VisitPlanVO> visitPlanVOList) {
        if (CollectionUtil.isEmpty(visitPlanVOList)){
            return;
        }
        List<Long> mIdList = visitPlanVOList.stream().map(VisitPlanVO::getmId).collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIdList);
        Map<Long, MerchantStoreAndExtendResp> merchantExtendMap = merchantExtendList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getMId, Function.identity()));

        List<Long> contactIdList = visitPlanVOList.stream().map(VisitPlan::getContactId).filter(Objects::nonNull).map(Integer::longValue).collect(Collectors.toList());
        List<ContactDto> contactList = contactQueryFacade.getMerchantContactList(contactIdList);
        Map<Long, ContactDto> contactMap = contactList.stream().collect(Collectors.toMap(Contact::getContactId, Function.identity()));

        for (VisitPlanVO visitPlan : visitPlanVOList) {
            if (merchantExtendMap.containsKey(visitPlan.getmId())) {
                MerchantStoreAndExtendResp merchantExtend = merchantExtendMap.get(visitPlan.getmId());
                if (!Objects.equals(VisitPlanEnum.Type.LEADS.ordinal(), visitPlan.getType())) {
                    visitPlan.setAreaNo(merchantExtend.getAreaNo());
                }
                visitPlan.setMname(merchantExtend.getStoreName());
                visitPlan.setmSize(StoreSizeEnum.getSize(merchantExtend.getSize()).getDesc());
            }
            if (visitPlan.getContactId() != null && contactMap.containsKey(visitPlan.getContactId().longValue())) {
                ContactDto c = contactMap.get(visitPlan.getContactId().longValue());
                visitPlan.setByContact(c);
            }
        }
    }

    public List<VisitPlanVO> queryEscortPlan(VisitPlanQuery visitPlanQuery) {
        if (!isSA() && visitPlanQuery.getAreaNo() == null && CollUtil.isEmpty(visitPlanQuery.getAreaNos())) {
            visitPlanQuery.setAreaNos(getCompatibleDataPermission());
        }
        List<VisitPlanVO> visitPlanList = crmEscortVisitPlanMapper.queryEscortPlan(visitPlanQuery);
        if (!visitPlanList.isEmpty()) {
            List<Long> mIdList = visitPlanList.stream().map(VisitPlan::getmId).filter(Objects::nonNull).collect(Collectors.toList());
            List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIdList);
            Map<Long, MerchantStoreAndExtendResp> merchantExtendMap = merchantExtendList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getMId, Function.identity()));

            List<Long> contactIdList = visitPlanList.stream().map(VisitPlan::getContactId).filter(Objects::nonNull).map(Integer::longValue).collect(Collectors.toList());
            List<ContactDto> contactList = contactQueryFacade.getMerchantContactList(contactIdList);
            Map<Long, ContactDto> contactMap = contactList.stream().collect(Collectors.toMap(ContactDto::getContactId, Function.identity()));

            for (VisitPlanVO vp : visitPlanList) {
                if (merchantExtendMap.containsKey(vp.getmId())) {
                    MerchantStoreAndExtendResp merchantExtend = merchantExtendMap.get(vp.getmId());
                    vp.setMname(merchantExtend.getStoreName());
                    vp.setmSize(StoreSizeEnum.getSize(merchantExtend.getSize()).getDesc());
                    vp.setAreaNo(merchantExtend.getAreaNo());
                }
                if (vp.getContactId() != null && contactMap.containsKey(vp.getContactId().longValue())) {
                    ContactDto c = contactMap.get(vp.getContactId().longValue());
                    vp.setByContact(c);
                }
            }

        }
        return visitPlanList;
    }

    public List<VisitPlanVO> queryVisitPlanList(VisitPlanQuery visitPlanQuery) {
        List<VisitPlanVO> visitPlanList = visitPlanMapper.queryVisitPlanList(visitPlanQuery);

        List<Long> mIds = visitPlanList.stream().map(VisitPlan::getmId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIds);
        Map<Long, MerchantStoreAndExtendResp> merchantMap = merchantExtendList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getMId, Function.identity()));

        List<Long> contactIdList = visitPlanList.stream().map(VisitPlan::getContactId).filter(Objects::nonNull).map(Integer::longValue).distinct().collect(Collectors.toList());
        List<ContactDto> contactList = contactQueryFacade.getMerchantContactList(contactIdList);
        Map<Long, ContactDto> contactMap = contactList.stream().collect(Collectors.toMap(ContactDto::getContactId, Function.identity()));

        for (VisitPlanVO visitPlan : visitPlanList) {
            if (merchantMap.containsKey(visitPlan.getmId())) {
                MerchantStoreAndExtendResp merchantExtend = merchantMap.get(visitPlan.getmId());
                visitPlan.setMname(merchantExtend.getStoreName());
                visitPlan.setmSize(StoreSizeEnum.getSize(merchantExtend.getSize()).getDesc());
                visitPlan.setMainType(merchantExtend.getBusinessType());
            }
            if (visitPlan.getContactId() != null) {
                ContactDto c = contactMap.get(visitPlan.getContactId().longValue());
                visitPlan.setByContact(c);
            }
        }
        return visitPlanList;
    }
    public void insertPlan(VisitPlan visitPlan){
        if(Objects.isNull(visitPlan.getAdminId())){
            visitPlan.setAdminId(getAdminId());
        }
        visitPlan.setCreator(getAdminName());
        visitPlan.setStatus(VisitPlanEnum.Status.WAIT.ordinal());
        visitPlanMapper.insertVisitPlan(visitPlan);
    }

}
