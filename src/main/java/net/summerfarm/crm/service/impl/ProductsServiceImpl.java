package net.summerfarm.crm.service.impl;/**
 * <AUTHOR>
 * @date 2023/1/5 15:51
 */

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.facade.ProductsQueryFacade;
import net.summerfarm.crm.model.vo.ProductsVO;
import net.summerfarm.crm.service.ProductsService;
import net.summerfarm.mall.client.provider.ProductProvider;
import net.summerfarm.mall.client.req.HelpOrderProductListQueryReq;
import net.summerfarm.mall.client.resp.HelpOrderProductInfoQueryResp;
import net.summerfarm.manage.client.wms.dto.res.ProductsDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5 15:51
 */
@Service("productsService")
public class ProductsServiceImpl extends BaseService implements ProductsService {
    @Resource
    ProductsQueryFacade productsFacade;
    @DubboReference
    ProductProvider productProvider;

    @Override
    public List<ProductsVO> search(String pdName) {
        List<ProductsDTO> productsDTOS = productsFacade.search(pdName);
        if (productsDTOS == null) {
            return new ArrayList<>();
        }
        List<ProductsVO> productsVOS = new ArrayList<>();
        productsDTOS.forEach(p -> productsVOS.add(ProductsVO.toVO(p)));
        return productsVOS;
    }

    @Override
    public CommonResult<PageInfo<HelpOrderProductInfoQueryResp>> selectSkuList(HelpOrderProductListQueryReq req) {
        DubboResponse<PageInfo<HelpOrderProductInfoQueryResp>> productRes = productProvider.getHelpOrderProductList(req);
        if (productRes.isSuccess()) {
            return CommonResult.ok(productRes.getData());
        }
        return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "查询商品失败,请重试");
    }
}
