package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.OrderTypeEnum;
import net.summerfarm.crm.enums.RiskMerchantEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.CrmRiskMerchantMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.query.riskMerchant.AuditRiskMerchantQuery;
import net.summerfarm.crm.model.query.riskMerchant.RiskMerchantDetailQuery;
import net.summerfarm.crm.model.query.riskMerchant.RiskMerchantQuery;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.model.vo.riskMerchant.DeliveryAddressVO;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantDetailVO;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantListVO;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantVO;
import net.summerfarm.crm.service.RiskMerchantService;
import net.summerfarm.mall.client.provider.DeliveryPlanProvider;
import net.summerfarm.mall.client.req.DeliveryPlanQueryReq;
import net.summerfarm.mall.client.req.DeliveryPlayQueryReq;
import net.summerfarm.mall.client.resp.DeliveryPlanQueryResp;
import net.summerfarm.mall.client.resp.RecentDeliveryPlanResp;
import net.summerfarm.pojo.DO.Area;
import net.summerfarm.tms.client.delivery.provider.standard.TmsDeliverySiteQueryStandardProvider;
import net.summerfarm.tms.client.delivery.req.standard.DeliverySiteStandardResp;
import net.summerfarm.tms.client.dist.provider.standard.TmsDistOrderQueryStandardProvider;
import net.summerfarm.tms.client.dist.req.standard.DeliverySiteQueryStandardReq;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import net.summerfarm.tms.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.DataSynchronizationInformationEnum.CRM_RISK_MERCHANT;
import static net.summerfarm.crm.enums.RiskMerchantEnum.TriggerClassification.SUSPECTED_SPURIOUS;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:45
 */
@Slf4j
@Service
public class RiskMerchantServiceImpl extends BaseService implements RiskMerchantService {
    @Resource
    private RiskMerchantMapper riskMerchantMapper;
    @DubboReference
    private DeliveryPlanProvider deliveryPlanProvider;
    @DubboReference
    private TmsDistOrderQueryStandardProvider standardProvider;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private CrmRiskMerchantMapper crmRiskMerchantMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private RiskSimilarMerchantMapper riskSimilarMerchantMapper;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    @Override
    public PageInfo<RiskMerchantListVO> list(RiskMerchantQuery query) {
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<RiskMerchantListVO> riskMerchantList = riskMerchantMapper.listRiskMerchant(query);
        return PageInfoHelper.createPageInfo(riskMerchantList);
    }

    @Override
    public CommonResult<RiskMerchantDetailVO> riskMerchantDetail(RiskMerchantDetailQuery query) {
        // 风控门店信息
        RiskMerchantDetailVO riskMerchant = riskMerchantMapper.selectRiskMerchantDetail(query.getRiskMerchantId());
        if (riskMerchant == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "风控门店详情不存在，请刷新后重试");
        }
        // 查询最近的 2 次收货地址
        DubboResponse<List<RecentDeliveryPlanResp>> deliveryPlanResp = getDeliveryPlan(riskMerchant);
        // 获取配送信息
        List<RecentDeliveryPlanResp> deliveryPlanRes = deliveryPlanResp.getData();
        if (CollectionUtil.isNotEmpty(deliveryPlanRes)) {
            List<DeliveryAddressVO> deliveryAddressList = new ArrayList<>();
            deliveryPlanRes.forEach(
                    deliveryPlan -> {
                        DubboResponse<DistOrderStandardResp> resp = getDistOrderStandardResp(deliveryPlan);
                        boolean isSuccess = resp != null && resp.isSuccess() && resp.getData() != null;
                        // 获取配送点位信息
                        if (isSuccess) {
                            Contact contact = contactMapper.selectByPrimaryKey(deliveryPlan.getContactId());
                            DeliveryAddressVO deliveryAddressVO = getDeliveryAddressVO(resp.getData(), contact);
                            deliveryAddressList.add(deliveryAddressVO);
                        }
                    }
            );

            riskMerchant.setDeliveryAddressList(deliveryAddressList);
        }
        return CommonResult.ok(riskMerchant);
    }

    @Override
    public CommonResult<Void> auditRiskMerchant(AuditRiskMerchantQuery query) {
        RiskMerchantDetailVO riskMerchantDetail = riskMerchantMapper.selectRiskMerchantDetail(query.getRiskMerchantId());
        if (riskMerchantDetail == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "风控门店id不正确，请刷新后重试");
        }
        riskMerchantMapper.auditRiskMerchantDetail(query, getAdminId(), getAdminName());
        return CommonResult.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void riskMerchantSync() {
        DataSynchronizationInformation data = dataSynchronizationInformationMapper.selectByTableName(CRM_RISK_MERCHANT.getTableName());
        if (data == null || !data.getDateFlag().toString().equals(DateUtils.localDateTimeToStringTwo(LocalDate.now().minusDays(1)))) {
            log.error("data_synchronization_information 为空或者日期不正确:{}", JSONUtil.toJsonPrettyStr(data));
            return;
        }
        Integer dayTag = data.getDateFlag();
        List<CrmRiskMerchant> crmRiskMerchants = crmRiskMerchantMapper.listByDayTag(dayTag);
        if (crmRiskMerchants.isEmpty()) {
            log.info("{}风控门店为空", dayTag);
            return;
        }
        // 风控门店列表
        List<Long> mIdList = crmRiskMerchants.stream().map(CrmRiskMerchant::getMId).collect(Collectors.toList());
        List<MerchantVO> merchantList = merchantMapper.selectByPrimaryKeys(mIdList);
        // 过滤 30 天已存在的门店
        List<Long> existMid = riskMerchantMapper.selectByMid(mIdList);
        Map<Long, MerchantVO> merchantMap = merchantList.stream().collect(Collectors.toMap(Merchant::getmId, Function.identity()));

        // 相似门店列表
        List<MerchantVO> similarMerchantList = new ArrayList<>();
        List<Long> similarMidList = crmRiskMerchants.stream().map(CrmRiskMerchant::getSimilarMId).collect(Collectors.toList());
        if (!similarMidList.isEmpty()) {
            similarMerchantList = merchantMapper.selectByPrimaryKeys(similarMidList);
        }
        Map<Long, MerchantVO> similarMerchantMap = similarMerchantList.stream().collect(Collectors.toMap(Merchant::getmId, Function.identity()));

        // 同步离线风控门店 在线表是 风控门店1: 相似门店 N ，离线数据会对到多条数据   所以需要分组再同步
        Map<Long, List<CrmRiskMerchant>> riskMerchantMap = crmRiskMerchants.stream().collect(Collectors.groupingBy(CrmRiskMerchant::getMId));
        riskMerchantMap.forEach((mId, riskMerchantList) -> {
            MerchantVO merchantVO = merchantMap.get(mId);
            if (merchantVO == null) {
                log.warn("找不到风控门店对应的门店详情:{}", mId);
                return;
            }
            if (existMid.contains(mId)) {
                log.info("30天内已存在该门店:{}", mId);
                return;
            }
            RiskMerchant riskMerchant = createRiskMerchant(merchantVO);
            CrmRiskMerchant crmRiskMerchant = riskMerchantList.get(0);
            riskMerchant.setTriggerOccasions(crmRiskMerchant.getTriggerOccasions());
            riskMerchant.setTriggerClassification(crmRiskMerchant.getTriggerClassification());
            riskMerchantMapper.insertSelective(riskMerchant);
            riskMerchantList.forEach(cm -> {
                RiskSimilarMerchant similarRiskMerchant = createRiskSimilarMerchant(cm);
                if (similarMerchantMap.containsKey(cm.getSimilarMId())) {
                    MerchantVO similarMerchant = similarMerchantMap.get(cm.getSimilarMId());
                    similarMerchant.setDoorPic(similarMerchant.getDoorPic());

                }
                similarRiskMerchant.setRiskMerchantId(riskMerchant.getId());
                riskSimilarMerchantMapper.insertSelective(similarRiskMerchant);
            });
        });
    }

    public DubboResponse<List<RecentDeliveryPlanResp>> getDeliveryPlan(RiskMerchantDetailVO riskMerchant) {
        DeliveryPlayQueryReq deliveryPlanReq = new DeliveryPlayQueryReq();
        deliveryPlanReq.setMId(riskMerchant.getMId());
        deliveryPlanReq.setOrderTime(riskMerchant.getCreateTime());
        // 查询配送计划获取城配仓
        DubboResponse<List<RecentDeliveryPlanResp>> deliveryPlanResp = deliveryPlanProvider.queryRecentTwoDeliveryPlan(deliveryPlanReq);
        if (!deliveryPlanResp.isSuccess()) {
            throw new BizException("网络不给力，请刷新后重试");
        }
        return deliveryPlanResp;
    }

    public DubboResponse<DistOrderStandardResp> getDistOrderStandardResp(RecentDeliveryPlanResp deliveryPlan) {
        DistOrderQueryStandardReq req = new DistOrderQueryStandardReq();
        req.setOuterContactId(String.valueOf(deliveryPlan.getContactId()));
        req.setExpectBeginTime(deliveryPlan.getDeliveryTime().atStartOfDay());
        req.setOuterOrderId(deliveryPlan.getOrderNo());
        if (OrderTypeEnum.NORMAL.getCode().equals(deliveryPlan.getOrderType())) {
            req.setSource(SourceEnum.XM_MALL.getValue());
        } else if (OrderTypeEnum.TIMING.getCode().equals(deliveryPlan.getOrderType())) {
            req.setSource(SourceEnum.XM_MALL_TIMING.getValue());
        } else {
            log.warn("无法识别的订单类型:{}", JSONUtil.toJsonStr(deliveryPlan));
            return null;
        }
        return standardProvider.queryDistOrderDetail(req);
    }

    private static DeliveryAddressVO getDeliveryAddressVO(DistOrderStandardResp resp, Contact contact) {
        DeliveryAddressVO deliveryAddressVO = new DeliveryAddressVO();
        if (contact != null) {
            deliveryAddressVO.setAddress(contact.getAddress());
            deliveryAddressVO.setPoi(contact.getPoiNote());
        }
        deliveryAddressVO.setLastDeliveryTime(resp.getRealArrivalTime());
        if (resp.getDeliverySiteMessage() != null) {
            deliveryAddressVO.setSignInPics(resp.getDeliverySiteMessage().getSignInPics());
        }
        return deliveryAddressVO;
    }

    /**
     * 风控门店
     *
     * @param merchantVO 商人签证官
     * @return {@link RiskMerchant}
     */
    @Override
    public RiskMerchant createRiskMerchant(MerchantVO merchantVO) {
        RiskMerchant riskMerchant = new RiskMerchant();
        riskMerchant.setMId(merchantVO.getmId().intValue());
        riskMerchant.setSize(merchantVO.getSize());
        riskMerchant.setPhone(merchantVO.getPhone());
        riskMerchant.setTriggerOccasions(RiskMerchantEnum.TriggerOccasions.REGISTER_MERCHANT.getCode());
        riskMerchant.setTriggerClassification(SUSPECTED_SPURIOUS.getCode());
        riskMerchant.setMname(merchantVO.getMname());
        riskMerchant.setProvince(merchantVO.getProvince());
        riskMerchant.setCity(merchantVO.getCity());
        riskMerchant.setArea(merchantVO.getArea());
        riskMerchant.setAreaNo(merchantVO.getAreaNo());
        riskMerchant.setPoi(merchantVO.getPoiNote());
        Area area = areaMapper.selectByAreaNo(merchantVO.getAreaNo());
        if (area != null) {
            riskMerchant.setAreaName(area.getAreaName());
        }
        riskMerchant.setDoorPic(merchantVO.getDoorPic());
        FollowUpRelation followUpRelation = followUpRelationMapper.selectUnReassign(merchantVO.getmId());
        if (followUpRelation != null) {
            riskMerchant.setBdId(followUpRelation.getAdminId());
            riskMerchant.setBdName(followUpRelation.getAdminName());
        }
        return riskMerchant;
    }

    /**
     * 创建相似门店
     *
     * @param crmRiskMerchant CRM风险商人
     * @return {@link RiskSimilarMerchant}
     */
    public RiskSimilarMerchant createRiskSimilarMerchant(CrmRiskMerchant crmRiskMerchant) {
        RiskSimilarMerchant similarMerchant = new RiskSimilarMerchant();
        similarMerchant.setMId(crmRiskMerchant.getSimilarMId());
        similarMerchant.setMname(crmRiskMerchant.getSimilarName());
        similarMerchant.setPhone(crmRiskMerchant.getSimilarPhone());
        similarMerchant.setSize(crmRiskMerchant.getSimilarSize());
        similarMerchant.setTriggerCondition(crmRiskMerchant.getTriggerCondition());
        if (RiskMerchantEnum.SourceType.XM.getCode().equals(crmRiskMerchant.getSourceType()) && crmRiskMerchant.getSimilarMId() != null) {
            MerchantVO merchantVO = merchantMapper.selectByPrimaryKey(crmRiskMerchant.getSimilarMId());
            if (merchantVO != null) {
                similarMerchant.setDoorPic(merchantVO.getDoorPic());
                similarMerchant.setAreaNo(merchantVO.getAreaNo());
                Area area = areaMapper.selectByAreaNo(merchantVO.getAreaNo());
                if (area != null) {
                    similarMerchant.setAreaName(area.getAreaName());
                }
            }
            FollowUpRelation followUpRelation = followUpRelationMapper.selectUnReassign(merchantVO.getmId());
            if (followUpRelation != null) {
                similarMerchant.setBdId(followUpRelation.getAdminId());
                similarMerchant.setBdName(followUpRelation.getAdminName());
            }
        }
        return similarMerchant;
    }
}
