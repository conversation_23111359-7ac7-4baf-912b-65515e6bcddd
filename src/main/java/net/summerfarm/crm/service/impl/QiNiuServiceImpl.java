package net.summerfarm.crm.service.impl;

import com.google.gson.Gson;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.qiNiu.Auth;
import net.summerfarm.contexts.QiNiuConstant;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.service.QiNiuService;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @title: QiNiuServiceImpl
 * @date 2021/12/15 13:48
 */
@Service
public class QiNiuServiceImpl implements QiNiuService {


    private static final Logger logger = LoggerFactory.getLogger(QiNiuService.class);

    @Override
    public AjaxResult uploadFile(String fileName, Workbook workbook) {

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            byte[] uploadBytes = byteArrayOutputStream.toByteArray();
            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(uploadBytes);
            Auth auth = Auth.create(accessKey, secretKey);
            String upToken = auth.uploadToken(bucket);
            try {
                Response response = uploadManager.put(byteInputStream, key, upToken, null, null);
                //解析上传成功的结果
                DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            } catch (QiniuException ex) {
                logger.error("七牛云上传异常 {}", ex.getMessage(), ex);
            }
        } catch (IOException e) {
            logger.error("七牛云上传IO异常 {}",e.getMessage(),e);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult uploadZip(Map<String, Workbook> workbooks, String fileName) throws IOException{

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        String key = fileName;

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = null;
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);
        //压缩文件
        try {
            for (String name : workbooks.keySet()) {
                Workbook workbook = workbooks.get(name);
                ZipEntry entry = new ZipEntry(name);
                zos.putNextEntry(entry);
                workbook.write(zos);
                zos.closeEntry();
            }
        } catch (IOException e) {
            logger.info("七牛云上传失败");
        } finally {
            zos.close();
            byteArrayOutputStream.close();
        }
        //上传文件信息
        bytes = byteArrayOutputStream.toByteArray();
        if (bytes.length < 1) {
            return AjaxResult.getErrorWithMsg("无文件信息");
        }
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        Response response = null;
        try {
            response = uploadManager.put(bytes, key, upToken);
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        } catch (QiniuException e) {
            logger.info("七牛云上传失败");
        }
        //解析上传成功的结果
        return AjaxResult.getOK();
    }


}
