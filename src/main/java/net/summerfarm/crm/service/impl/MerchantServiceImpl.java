package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.resp.merchant.MerchantQueryResp;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.es.EsIndexContext;
import net.summerfarm.common.util.es.dto.EsMerchantIndexDTO;
import net.summerfarm.common.util.es.query.EsQuery;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.config.MerchantLabelNacosConfig;
import net.summerfarm.crm.common.constant.CommonRedisKey;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.*;
import net.summerfarm.crm.enums.*;
import net.summerfarm.crm.facade.*;
import net.summerfarm.crm.facade.converter.contact.ContactConverter;
import net.summerfarm.crm.facade.converter.merchant.MerchantExtendConverter;
import net.summerfarm.crm.facade.dto.AdminDTO;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.*;
import net.summerfarm.crm.mapper.repository.*;
import net.summerfarm.crm.mapper.repository.performance.CustPerformanceCommRepository;
import net.summerfarm.crm.model.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.crm.model.convert.salesdata.DashboardModuleConverter;
import net.summerfarm.crm.model.convert.salesperformance.HighValueCustomerV2Converter;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.*;
import net.summerfarm.crm.model.query.*;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.model.vo.saledata.CRMDashboardModule;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailV2VO;
import net.summerfarm.crm.service.*;
import net.summerfarm.crm.service.crmjob.CrmJobMerchantDetailManager;
import net.summerfarm.enums.CommonNumbersEnum;
import net.summerfarm.enums.PageJurisdictionEnum;
import net.summerfarm.goods.client.resp.CategoryResp;
import net.summerfarm.mall.client.provider.ShoppingCartProvider;
import net.summerfarm.mall.client.req.ShoppingCartReq;
import net.summerfarm.mall.client.resp.ShoppingCartResp;
import net.summerfarm.pojo.DO.Admin;
import net.summerfarm.pojo.DO.Area;
import net.summerfarm.tms.client.dist.provider.TmsDistOrderQueryProvider;
import net.summerfarm.tms.client.dist.req.DistOrderQueryReq;
import net.summerfarm.tms.client.dist.resp.DeliveryBatchResp;
import net.summerfarm.tms.client.dist.resp.DeliverySiteResp;
import net.summerfarm.tms.client.dist.resp.DistOrderResp;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.oss.helper.XianMuOssHelper;
import net.xianmu.redis.support.cache.annotation.XmCache;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreChangeLogResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.MultiMatchQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import static net.summerfarm.crm.common.constant.CrmGlobalConstant.REGISTER_FIRST_ORDER_30_DAY;
import static net.summerfarm.crm.common.constant.CrmGlobalConstant.REGISTER_NO_ORDER_30_DAY;
import static net.summerfarm.crm.common.es.EsIndex.INDEX_CRUL_POOL_V1;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.OFFICIAL_WECHAT;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.SALES_WECHAT;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

;

/**
 * <AUTHOR>
 * @Description 商户业务接口实现
 * @date 2022/6/14 2:00
 */
@Slf4j
@Service
public class MerchantServiceImpl extends BaseService implements MerchantService {

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private CrmBdAreaMapper crmBdAreaMapper;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Lazy
    @Resource
    private FollowUpRelationService followUpRelationService;
    @Resource
    private MerchantLeadsMapper merchantLeadsMapper;
    @Resource
    private VisitPlanMapper visitPlanMapper;
    @Resource
    private VisitPlanService visitPlanService;
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private CrmMerchantMonthGmvMapper crmMerchantMonthGmvMapper;
    @Resource
    private CrmMerchantDayGmvMapper crmMerchantDayGmvMapper;
    @Resource
    private CrmBdConfigMapper crmBdConfigMapper;
    @Resource
    private FollowWhiteListMapper followWhiteListMapper;
    @Resource
    private FollowWhiteListService followWhiteListService;
    @Resource
    private CrmCommissionMerchantLevelMapper crmCommissionMerchantLevelMapper;
    @Resource
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private ProductsRepository productsRepository;
    @Resource
    private CrmMerchantDayAttributeMapper crmMerchantDayAttributeMapper;
    @Resource
    private MerchantCluePoolMapper merchantCluePoolMapper;
    @Resource
    private CrmManageBdMapper crmManageBdMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    @Resource
    private CrmMerchantFutureDayGmvMapper crmMerchantFutureDayGmvMapper;

    @Resource
    private CrmSkuMonthGmvMapper crmSkuMonthGmvMapper;

    @Resource
    private CrmSkuAreaMonthMerchantMapper crmSkuAreaMonthMerchantMapper;

    @Resource
    private CrmSkuBdMonthMerchantMapper crmSkuBdMonthMerchantMapper;

    @Resource
    private CrmMerchantMallSearchTopMapper crmMerchantMallSearchTopMapper;

    @Resource
    private CrmMerchantTodayGmvMapper crmMerchantTodayGmvMapper;

    @Resource
    private CrmMerchantAreaTypeTopMapper crmMerchantAreaTypeTopMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private ComfortSendServiceImpl comfortSendService;
    @Resource
    private OrdersMapper ordersMapper;

    @Resource
    private OrderDefectInfoMapper orderDefectInfoMapper;

    @Resource
    private ContactMapper contactMapper;

    @DubboReference
    private TmsDistOrderQueryProvider tmsDistOrderQueryProvider;
    @Resource
    ConfigMapper configMapper;

    @Resource
    private AuthUserQueryFacade authFacade;
    @Resource
    private MerchantLabelMapper merchantLabelMapper;
    @Resource
    private BdExtService bdExtService;
    @Resource
    private CityPermissionService cityPermissionService;
    //您暂无此区域权限,请联系主管添加后操作
    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    @Resource
    private MerchantExtendsRepository merchantExtendsRepository;
    @Resource
    private MerchantAccountQueryFacade merchantAccountQueryFacade;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private ContactQueryFacade contactQueryFacade;
    @DubboReference
    private ShoppingCartProvider shoppingCartProvider;
    @Resource
    private CrmConfig crmConfig;
    @Resource
    private MerchantLabelNacosConfig merchantLabelNacosConfig;
    @Resource
    @Lazy
    FollowUpRecordService followUpRecordService;
    @Resource
    private CustAfterDlvProfitLabelRepository custAfterDlvProfitLabelRepository;
    @Resource
    private MerchantSituationActivityLogRepository merchantSituationActivityLogRepository;
    @Resource
    private FruitPopCustValueLabelRepository fruitPopCustValueLabelRepository;
    @Resource
    private CustMtdPerformanceRepository custMtdPerformanceRepository;
    @Autowired
    private CustPerformanceCommRepository custPerformanceCommRepository;
    @Autowired
    private DashboardModuleConverter dashboardModuleConverter;
    @Autowired
    private MerchantCommandFacade merchantCommandFacade;
    @Autowired
    private CrmJobMerchantDetailManager crmJobMerchantDetailManager;
    @Autowired
    private AdminQueryFacade adminQueryFacade;

    private static final Logger logger = LoggerFactory.getLogger(MerchantService.class);



    @Override
    public CommonResult<MerchantVO> selectDetail(MerchantDetailQuery merchantDetailQuery) {
        if (Objects.isNull(merchantDetailQuery) || Objects.isNull(merchantDetailQuery.getMerchantId())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "商户id不能为空,请刷新页面后重试");
        }

        MerchantVO merchantVO = this.getBaseDetail(merchantDetailQuery.getMerchantId());

        // 对"其他区域"的历史脏数据兼容
        String queryArea = merchantVO.getArea();
        if ("其他区域".equals(queryArea)) {
            queryArea = null;
        }

        CrmBdCity crmBdCity = cityPermissionService.selectAminIdCityandArea(getAdminId(), merchantVO.getCity(), queryArea);
        if (crmBdCity == null){
            return CommonResult.createResult(ResultStatusEnum.BAD_REQUEST, "您暂无此区域权限,请联系主管添加后操作","NO_PERMISSION");
        }
        //历史流转
        int relationCount = followUpRelationMapper.countByMId(merchantDetailQuery.getMerchantId());
        merchantVO.setHistoryRelation(relationCount);

        //今天是否有拜访计划，直接设置成false这样前端就会允许销售在客户详情页打拜访记录
        // this.getTodayVisitPlan(merchantDetailQuery, merchantVO);
        merchantVO.setHasVisitPlanToday(false);

        // 拜访天数/未下单天数/生命周期/下单周期/倒计时规则
        this.getMerchantAttribute(merchantDetailQuery, merchantVO);

        // 下单预警标识
        Boolean orderCycleWarn = Objects.nonNull(merchantVO.getNotOrder()) && (merchantVO.getNotOrder() > merchantVO.getOrderCycle()
                || merchantVO.getNotOrder() > 30);
        merchantVO.setOrderCycleWarn(orderCycleWarn);

        // 当前已超过平均下单周期天数
        if (orderCycleWarn) {
            int moreThanOrderCycle = merchantVO.getNotOrder() - merchantVO.getOrderCycle();
            merchantVO.setMoreThanOrderCycle(moreThanOrderCycle);
        }

        // 今日距离月底是否超过10天
        int dayNumDiff = LocalDate.now().lengthOfMonth() - LocalDate.now().getDayOfMonth();
        if (dayNumDiff <= NumberUtils.INTEGER_TEN) {
            merchantVO.setDayNumFromMonthEnd(dayNumDiff);
        }
        //是否关注
        Long careBdId = 0L;
        if (merchantVO.getAdminId() != null && merchantVO.getAdminId() > 0) {
            FollowUpRelation followUpRelation = followUpRelationMapper.selectLastFollowOne(merchantVO.getAdminId(), merchantVO.getmId().intValue());
            if (followUpRelation != null) {
                careBdId = followUpRelation.getCareBdId() == null ? 0L : followUpRelation.getCareBdId().longValue();
            }
        }
        merchantVO.setCareBdId(careBdId);
        return CommonResult.ok(merchantVO);
    }

    private void getMerchantAttribute(MerchantDetailQuery merchantDetailQuery, MerchantVO merchantVO) {
        DataSynchronizationInformation dataTag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_ATTRIBUTE.getTableName());
        Integer attributeDataFlag = Objects.isNull(dataTag) ? NumberUtils.INTEGER_ZERO : dataTag.getDateFlag();
        CrmMerchantDayAttribute merchantDayAttribute = crmMerchantDayAttributeMapper.selectByPrimaryKey(merchantDetailQuery.getMerchantId(), attributeDataFlag);
        merchantDayAttribute = Optional.ofNullable(merchantDayAttribute).orElse(new CrmMerchantDayAttribute());
        merchantVO.setNotOrder(merchantDayAttribute.getDaysWithoutOrder());
        merchantVO.setNotOrderFollow(merchantDayAttribute.getDaysWithoutOrderFollow());
        merchantVO.setNotFollow(merchantDayAttribute.getNotVisited());
        merchantVO.setRValue(merchantDayAttribute.getRValue());
        merchantVO.setFValue(merchantDayAttribute.getFValue());
        merchantVO.setMValue(merchantDayAttribute.getMValue());

        // pop客户生命周期
        if (MerchantEnum.BusinessLineEnum.POP.getCode().equals(merchantVO.getBusinessLine())) {
            LambdaQueryWrapper<FruitPopCustValueLable> wrapper = Wrappers.lambdaQuery(FruitPopCustValueLable.class)
                    .eq(FruitPopCustValueLable::getCustId, merchantVO.getmId());
            FruitPopCustValueLable fruitPopCustValueLable = fruitPopCustValueLabelRepository.getOne(wrapper);
            if (fruitPopCustValueLable != null) {
                merchantVO.setLifecycle(fruitPopCustValueLable.getCustValueLable());
            }
        } else {
            merchantVO.setLifecycle(merchantDayAttribute.getLifecycle());
        }

        // 查询绩效二期数据
        if (Boolean.TRUE.equals(merchantDetailQuery.getQueryPerformanceV2())) {
            List<CustPerformanceComm> custPerformanceComms = custPerformanceCommRepository
                    .listByCustIds(Collections.singletonList(merchantVO.getmId()), PerformanceOrderSourceEnum.XIANMU.getValue());
            if (!CollectionUtils.isEmpty(custPerformanceComms)) {
                merchantVO.setHighValueLabel(custPerformanceComms.get(0).getCustValueLable());
                HighValueCustomerDetailV2VO highValueCustomerDetail = HighValueCustomerV2Converter.convertToHighValueCustomerDetailV2VO(custPerformanceComms.get(0));
                CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(highValueCustomerDetail,
                        ConfigValueEnum.CRM_MERCHANT_DETAIL_HIGH_VALUE_CONFIG,
                        Optional.ofNullable(highValueCustomerDetail).map(HighValueCustomerDetailV2VO::getMname).orElse(""),
                        "高价值履约数据");
                merchantVO.setHighValueCustomerDetail(module);
            }
        } else {
            // 使用绩效一期的高价值客户标签
            CustMtdPerformance highValueLabel = custMtdPerformanceRepository.lambdaQuery()
                    .select(CustMtdPerformance::getCustValueLable)
                    .eq(CustMtdPerformance::getCustId, merchantVO.getmId())
                    .one();
            merchantVO.setHighValueLabel(highValueLabel == null ? null : highValueLabel.getCustValueLable());
        }

        // 下单周期
        merchantVO.setOrderCycle(merchantDayAttribute.getOrderCycle());
        if (NumberUtils.INTEGER_ZERO.equals(merchantDayAttribute.getOrderCycle())) {
            merchantVO.setOrderCycle(merchantVO.getNotOrder());
        }

        // 倒计时规则
        if (Objects.isNull(merchantVO.getDangerDay())) {
            return;
        }
        merchantVO.setDangerDayRule(FollowUpRelationEnum.DangerDayRule.RULE_ONE.getValue());
        if (Objects.isNull(merchantDayAttribute.getDaysWithoutOrder()) || Objects.isNull(merchantDayAttribute.getNotVisited())) {
            return;
        }
        if (merchantDayAttribute.getDaysWithoutOrder() > NumberUtils.INTEGER_THIRTY
                || merchantDayAttribute.getNotVisited() > NumberUtils.INTEGER_FIFTEEN) {
            merchantVO.setDangerDayRule(FollowUpRelationEnum.DangerDayRule.RULE_TWO.getValue());
        }
    }

    private void getTodayVisitPlan(MerchantDetailQuery merchantDetailQuery, MerchantVO merchantVO) {
        VisitPlanVO query = new VisitPlanVO();
        query.setAdminId(getAdminId());
        query.setDate(LocalDate.now());
        query.setmId(merchantDetailQuery.getMerchantId());
        query.setAreaNo(merchantVO.getAreaNo());
        List<VisitPlanVO> visitPlanVOList = visitPlanMapper.selectList(query);
        merchantVO.setHasVisitPlanToday(!CollectionUtils.isEmpty(visitPlanVOList));
    }

    @Override
    public CommonResult<MerchantVO> baseDetail(MerchantDetailQuery merchantDetailQuery) {
        MerchantVO merchantVO = this.getBaseDetail(merchantDetailQuery.getMerchantId());
        return CommonResult.ok(merchantVO);
    }

    @Override
    public MerchantVO getMerchantVoByMid(Long mId) {
        // 门店详情 买家中心字段 并且补充部分买家中心暂未迁移的字段
        MerchantStoreAndExtendDTO merchantExtendDTO = merchantExtendsRepository.getAuthMerchantExtendDTO(mId,null);
        MerchantVO merchantVO = MerchantExtendConverter.convertMerchantVo(merchantExtendDTO);
        if (Objects.isNull(merchantExtendDTO)) {
            return new MerchantVO();
        }
        // 门店主账户信息
        MerchantStoreAccountResultResp merchantAccount = merchantAccountQueryFacade.getPrimaryAccount(mId);
        if (merchantAccount != null) {
            merchantVO.setMcontact(merchantAccount.getAccountName());
            merchantVO.setPhone(merchantAccount.getPhone());
        }
        if (merchantExtendDTO.getAdminId() != null) {
            Admin admin = adminMapper.selectByPrimaryKey(merchantExtendDTO.getAdminId().intValue());
            if (admin != null) {
                merchantVO.setRealName(admin.getRealname());
                merchantVO.setCloseOrderTime(admin.getCloseOrderTime());
                merchantVO.setNameRemakes(admin.getNameRemakes());
                merchantVO.setAdminPhone(admin.getPhone());
            }
        }
        FollowWhiteList followWhiteList = followWhiteListMapper.queryFollowWhiteListOne(mId);
        if (followWhiteList != null) {
            merchantVO.setWhiteListType(followWhiteList.getStatus());
        }

        FollowUpRelation followUpRelation = followUpRelationMapper.selectUnReassign(mId);
        if (followUpRelation != null) {
            merchantVO.setAdminName(followUpRelation.getAdminName());
            merchantVO.setAdminId(followUpRelation.getAdminId());
            merchantVO.setFollowType(followUpRelation.getFollowType());
            merchantVO.setTimingFollowType(followUpRelation.getTimingFollowType());
            merchantVO.setFollowId(followUpRelation.getAdminId());
            merchantVO.setReassign(Boolean.FALSE.equals(followUpRelation.getReassign()) ? 0 : 1);
            merchantVO.setDangerDay(followUpRelation.getDangerDay());
            LocalDateTime releaseTime = followUpRelation.getReleaseTime();
            if (releaseTime == null && followUpRelation.getDangerDay() != null) {
                log.warn("查询客户信息时缺少释放时间，使用dangerDay兜底，mId:{}", followUpRelation.getmId());
                releaseTime = LocalDate.now().plusDays(followUpRelation.getDangerDay()).atTime(crmConfig.getPrivateSeaReassignTime());
            }
            if (releaseTime != null) {
                merchantVO.setReleaseTime(BaseDateUtils.localDateTimeToString(releaseTime));
            }
            merchantVO.setProtectReason(followUpRelation.getProtectReason());
        }
        return merchantVO;
    }

    private MerchantVO getBaseDetail(Long mId) {
        // 客户信息
        MerchantVO merchantVO = getMerchantVoByMid(mId);

        MerchantVO discountInfo = merchantMapper.selectDiscountCardByMid(mId);
        merchantVO.setHaveDiscountCard(discountInfo == null ? 0 : 1);
        if (discountInfo != null) {
            merchantVO.setDiscountCardStatus(discountInfo.getDiscountCardStatus());
            merchantVO.setDiscountUsedTimes(discountInfo.getDiscountUsedTimes());
            merchantVO.setDiscountTotalTimes(discountInfo.getDiscountTotalTimes());
            merchantVO.setDiscountDeadline(discountInfo.getDiscountDeadline());
        }

        // 获取全部客户标签
        List<String> merchantAllLabel = this.selectMerchantAllLabelByMid(mId);
        String salesWechat = merchantLabelMapper.selectByMidAndName(mId, SALES_WECHAT);
        merchantVO.setSalesWechat(salesWechat);
        String officialWechat = merchantLabelMapper.selectByMidAndName(mId, OFFICIAL_WECHAT);
        merchantVO.setOfficialWechat(officialWechat);

//        List<String> newYearBusinessTag = merchantLabelMapper.selectByMidAndId(mId, crmConfig.getNewYearBusinessTag());
        List<String> labelsToDisplay = merchantLabelMapper.selectByMidAndId(mId, merchantLabelNacosConfig.getLabelToDisplay());
        List<String> fruitTags = followUpRecordService.getFruitTags(mId);
        List<String> tags = new ArrayList<>();
        tags.addAll(fruitTags);
        tags.addAll(labelsToDisplay);
        merchantVO.setNewYearBusinessTag(tags);

        if (CollectionUtil.isNotEmpty(merchantAllLabel)) {
            // 需要展示的周年庆标签
            List<String> allAnniversaryEnumValue = MerchantLabelEnum.getAllAnniversaryEnumValue();
            // 其他标签
            List<String> middleList = new ArrayList<>(merchantAllLabel);
            middleList.removeAll(allAnniversaryEnumValue);
            // 需要的标签
            merchantAllLabel.removeAll(middleList);
            merchantVO.setMerchantLabelList(merchantAllLabel);
        }

        merchantVO.getNewYearBusinessTag().add(custAfterDlvProfitLabelRepository.selectDlvProfitLabelByCustIdAndNoSaas(mId));

        return merchantVO;
    }


    @Async
    @Override
    public AjaxResult updateCurlPool(MerchantCluePool merchantCluePool, Integer manage, String oldEsId) {

        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }
        try {
            //更新绑定信息
            if (merchantCluePool != null) {
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.index(INDEX_CRUL_POOL_V1);
                updateRequest.id(merchantCluePool.getEsId());
                updateRequest.doc(XContentFactory.jsonBuilder().startObject().field("manage", manage).endObject());
                UpdateResponse update = client.update(updateRequest, RequestOptions.DEFAULT);
                merchantCluePool.setEsId(update.getId());
            }

            //取消原来的绑定
            if (!StringUtils.isEmpty(oldEsId)) {
                UpdateRequest updateOldRequest = new UpdateRequest();
                updateOldRequest.index(INDEX_CRUL_POOL_V1);
                updateOldRequest.id(oldEsId);
                updateOldRequest.doc(XContentFactory.jsonBuilder().startObject().field("manage", 0).endObject());
                client.update(updateOldRequest, RequestOptions.DEFAULT);
            }


        } catch (IOException e) {
            logger.warn("链接io异常",e);
        } finally {
            EsClientPoolUtil.returnClient(client);
        }

        //取消线索池关联
        if (!StringUtils.isEmpty(oldEsId)) {
            MerchantCluePool updatePool = new MerchantCluePool();
            updatePool.setEsId(oldEsId);
            updatePool.setStatus(MerchantCluePool.CLUE_POOL_NOT_EFFICACY);
            merchantCluePoolMapper.updateMerchantCluePool(updatePool);
        }
        //插入关联数据
        if (merchantCluePool != null) {
            merchantCluePool.setStatus(MerchantCluePool.CLUE_POOL_EFFICACY);
            merchantCluePoolMapper.insertMerchantCluePool(merchantCluePool);
        }
        return AjaxResult.getOK();
    }

    private List<String> selectMerchantAllLabelByMid(Long mId) {
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        Integer dataFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        List<String> labels = crmMerchantDayLabelMapper.selectByPrimaryKey(mId, dataFlag);

        labels.add(custAfterDlvProfitLabelRepository.selectDlvProfitLabelByCustIdAndNoSaas(mId));
        return labels;
    }

    @Override
    public AjaxResult queryMerchantRelease(int pageIndex, int pageSize, MerchantQuery merchantQuery) {
        // 未下单天数范围校验
        if (CollectionUtil.isNotEmpty(merchantQuery.getNotOrderList()) && merchantQuery.getNotOrderList().size() != NumberUtils.INTEGER_TWO) {
            return AjaxResult.getErrorWithMsg("请联系管理员并问问他,为啥不按规范传值");
        }
        Set<Integer> dataPermission = super.getDataPermission();
        boolean isAll = dataPermission.contains(NumberUtils.INTEGER_ZERO) || isSA();
        if (!isAll) {
            merchantQuery.setAreaNoSet(dataPermission);
        }
        // 获取销售所有的私海id
        Map<String, Object> searchMap = EsUtil.esSearchMerchant(pageIndex, pageSize, merchantQuery);
        JSONArray data = (JSONArray) searchMap.get("data");
        data = Optional.ofNullable(data).orElse(new JSONArray());
        List<MerchantListVO> merchantList = new ArrayList<>();
        boolean isOnlyBd = !(isSA() || isAreaSA() || isSaleSA());

        for (Object detail : data) {
            String jsonString = JSON.toJSONString(detail);
            MerchantListVO merchantListVO = JSONObject.parseObject(jsonString, MerchantListVO.class);
            boolean openSeaFlag = Objects.equals(NumberUtils.INTEGER_ZERO, merchantListVO.getBdId());
            boolean myMerchant = Objects.equals(super.getAdminId(), merchantListVO.getBdId());
            boolean isBriefBool = isOnlyBd && !myMerchant && !openSeaFlag;
            int roleTag = isBriefBool ? PageJurisdictionEnum.MerchantDetail.BRIEF.ordinal()
                    : PageJurisdictionEnum.MerchantDetail.DETAILED.ordinal();
            merchantListVO.setRoleTag(roleTag);
            // 查询绩效二期数据时返回绩效二期的高价值客户标签
            if (Boolean.TRUE.equals(merchantQuery.getQueryPerformanceV2())) {
                merchantListVO.setHighValueLabel(merchantListVO.getHighValueLabelV2());
            }

            this.selectGmvList(merchantListVO);
            merchantList.add(merchantListVO);
        }
        searchMap.put("data", merchantList);
        return AjaxResult.getOK(searchMap);
    }

    /**
     * 查询 gmv,配送客单价,产品数
     * @return gmv集合
     */
    private void selectGmvList(MerchantListVO merchantListVO) {
        // 本月gmv,本月配送客单价,产品数
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        Integer dataFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        PrivateSeaVO privateSeaVO = crmMerchantDayGmvMapper.selectByMid(merchantListVO.getMId(), dataFlag);
        // 获取上月gmv,上月配送客单价,产品数
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_MONTH_GMV.getTableName());
        Integer lastDataFlag = Objects.isNull(lastMonth) ? NumberUtils.INTEGER_ZERO : lastMonth.getDateFlag();
        PrivateSeaVO lastPrivateSeaVO = crmMerchantMonthGmvMapper.selectByMid(merchantListVO.getMId(), lastDataFlag);

        if (privateSeaVO != null) {
            merchantListVO.setThisMonthGmv(privateSeaVO.getThisMonthGmv());
            merchantListVO.setThisMonthSkuCount(privateSeaVO.getThisMonthSkuCount());
        }
        if (lastPrivateSeaVO != null) {
            merchantListVO.setLastMonthGmv(lastPrivateSeaVO.getThisMonthGmv());
            merchantListVO.setLastMonthSkuCount(lastPrivateSeaVO.getThisMonthSkuCount());
        }
    }

    @Override
    public CommonResult<String> getAssignTemplate() {
        Config config = configMapper.selectOne(ConfigValueEnum.ASSIGN_TEMPLATE.getKey());
        return CommonResult.ok(XianMuOssHelper.generateUrl(config.getValue()));
    }

    @Override
    public CommonResult<List<AssignBdResultVo>> batchAssign(String file){
        InputStream inputStream = OssGetUtil.getInputStream(file);
        List<AssignBdDTO> assignBdList = EasyExcel.read(inputStream).head(AssignBdDTO.class).sheet().doReadSync();
        IoUtil.close(inputStream);
        if (CollectionUtils.isEmpty(assignBdList)) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "分配内容为空，请核对后重试");
        }
        // 获取具有销售权限的admin
        List<AssignBdResultVo> resultList = new ArrayList<>();

        List<Long> mIdList = assignBdList.stream().map(AssignBdDTO::getMId).filter(Objects::nonNull).collect(Collectors.toList());
        if(mIdList.isEmpty()){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "分配门店为空，请核对后重试");
        }
        List<MerchantStoreResultResp> merchantExtend = merchantQueryFacade.getMerchantByMid(mIdList);
        if(CollectionUtil.isEmpty(merchantExtend)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "分配门店为空，请核对后重试");
        }
        Map<Long, MerchantStoreResultResp> merchantMap = merchantExtend.stream().collect(Collectors.toMap(MerchantStoreResultResp::getMId, Function.identity()));
        Map<String, List<Admin>> bdMap = adminMapper.selectByRealnameIn(assignBdList.stream()
                .map(AssignBdDTO::getBdName).filter(StringUtils::isNotEmpty).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(Admin::getRealname));


        for (AssignBdDTO assignBd : assignBdList) {
            Long mId = assignBd.getMId();
            String bdName = assignBd.getBdName();

            if (mId == null || StringUtils.isEmpty(bdName)) {
                resultList.add(generateResult(mId, "分配信息为空"));
                continue;
            }
            List<Admin> admins = bdMap.get(bdName);
            if (CollectionUtils.isEmpty(admins)) {
                resultList.add(generateResult(mId, String.format("对应bd [%s] 不存在", bdName)));
                continue;
            }
            if (admins.size() > 1) {
                resultList.add(generateResult(mId, String.format("对应bd [%s] 存在多个", bdName)));
                continue;
            }
            Integer bdId = admins.get(0).getAdminId();

            if (!merchantMap.containsKey(mId)) {
                resultList.add(generateResult(mId, "商户不存在"));
                continue;
            }
            AjaxResult ajaxResult = followUpRelationService.reassign(Collections.singletonList(mId), Math.toIntExact(bdId), null, NumberUtils.INTEGER_ZERO, NumberUtils.INTEGER_ZERO);
            if (!ajaxResult.isSuccess()) {
                resultList.add(generateResult(mId, ajaxResult.getMsg()));
            }
        }
        return CommonResult.ok(resultList);
    }
    public AssignBdResultVo generateResult(Long mId,String reason){
        AssignBdResultVo resultVo=new AssignBdResultVo();
        resultVo.setMId(mId);
        resultVo.setReason(reason);
        return resultVo;
    }

    @Override
    public AjaxResult queryMerchantWightLight() {
        MerchantNumVO merchantNumVO = new MerchantNumVO();
        Integer adminId = super.getAdminId();
        // 查询客户激励配置
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminId);
        if (Objects.isNull(crmBdConfig)) {
            return AjaxResult.getErrorWithMsg("请先前往管理后台,配置bd激励指标");
        }
        // 查询客户私海容量
        int privateNum = EsUtil.countPrivateSea(adminId,null,0);
        // 去除白名单客户
        int whiteNum = followWhiteListService.selectNumByBd(adminId, null);
        // 查询数据更新时间
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        Integer dataFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();
        merchantNumVO.setDataUpdateTime(dataFlag);
        merchantNumVO.setPrivateNum(crmBdConfig.getPrivateSeaLimit());
        merchantNumVO.setCurPrivateNum(privateNum - whiteNum);
        return AjaxResult.getOK(merchantNumVO);
    }

    @Override
    public AjaxResult queryBdPrivateSea(int type, MerchantVO record) {
        Integer adminId = getAdminId();
        Integer reassign = NumberUtils.INTEGER_ZERO;
        boolean isLawful = type == NumberUtils.INTEGER_ONE && (isAreaSA() || isSaleSA() || isSA());
        if (isLawful) {
            adminId = null;
            reassign = null;
        }
        List<Long> mIds = null;
        if (record.getmId() != null) {
            mIds = Lists.newArrayList(record.getmId());
        } else if (!CollectionUtils.isEmpty(record.getMIdList())) {
            mIds = record.getMIdList();
        }
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.fuzzyGetMerchantExtendsByStoreName(mIds, record.getMname(),getCompatibleDataPermission());
        if (CollectionUtil.isEmpty(merchantExtendList)) {
            return AjaxResult.getOK(new ArrayList<MerchantVO>());
        }
        List<Long> mIdList = merchantExtendList.stream().map(MerchantStoreResultResp::getMId).filter(Objects::nonNull).collect(Collectors.toList());
        if (mIdList.isEmpty()) {
            return AjaxResult.getOK(new ArrayList<MerchantVO>());
        }
        record.setMIdList(mIdList);

        record.setAdminId(adminId);
        record.setReassign(reassign);

        List<MerchantVO> merchantVOList = merchantMapper.queryPrivateSea(record);
        Map<Long, MerchantStoreAndExtendResp> merchantMap = merchantExtendList.stream().collect(Collectors.toMap(MerchantStoreResultResp::getMId, Function.identity()));

        for (MerchantVO merchantVO : merchantVOList) {
            if (merchantMap.containsKey(merchantVO.getmId())){
                MerchantStoreAndExtendResp merchantExtend = merchantMap.get(merchantVO.getmId());
                merchantVO.setMname(merchantExtend.getStoreName());
                merchantVO.setAreaNo(merchantExtend.getAreaNo());
                merchantVO.setSize(StoreSizeEnum.getSize(merchantExtend.getSize()).getDesc());
                Integer adminIdInt = merchantExtend.getAdminId() == null ? null : merchantExtend.getAdminId().intValue();
                merchantVO.setPopMerchant(isPopMerchant(adminIdInt));
                merchantVO.setType(merchantExtend.getBusinessType());
            }

            List<String> labelList = this.selectMerchantAllLabelByMid(merchantVO.getmId());
            if (CollectionUtil.isNotEmpty(labelList)) {
                merchantVO.setMerchantLabelList(labelList);
            }
        }
        return AjaxResult.getOK(merchantVOList);
    }

    public boolean isPopMerchant(Integer adminId) {
        return crmConfig.getPopDefaultAdminId().equals(adminId);
    }

    @Override
    public AjaxResult existMerchantName(Long leadId, String mname) {
        MerchantStoreResultResp merchantByStoreName = merchantQueryFacade.getMerchantByStoreName(mname);

        boolean flag = false;
        if (merchantByStoreName != null) {
            flag = true;
        } else {
            MerchantLeads leads = merchantLeadsMapper.selectByMname(mname);
            if (leads != null) {
                if (leadId != null) {
                    //判断是否和自己名称相同
                    flag = !Objects.equals(leadId, leads.getId());
                }
            }
        }

        return AjaxResult.getOK(flag);
    }


    @Override
    public AjaxResult querySimilarMerchant(Merchant merchant) {
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder
                .filter(new TermQueryBuilder("islock", 0))
                .must(new TermQueryBuilder("contacts.status", 1))
                .must(new MatchQueryBuilder("contacts.city", merchant.getCity()))
                .must(new MatchQueryBuilder("contacts.province", merchant.getProvince()).analyzer("ik_max_word"));
        if (StringUtils.isNotBlank(merchant.getArea())) {
            queryBuilder.must(new MatchQueryBuilder("contacts.area", merchant.getArea()));
        }
        if (StringUtils.isNotBlank(merchant.getAddress())) {
            queryBuilder.must(new MatchQueryBuilder("contacts.address", merchant.getAddress()));
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.from(0).size(3);
        searchSourceBuilder.query(queryBuilder);

        SearchRequest searchRequest = new SearchRequest(EsIndexContext.INDEX_MERCHANT);
        searchRequest.source(searchSourceBuilder);

        List<EsMerchantIndexDTO> result = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            response.getHits().forEach(el -> {
                EsMerchantIndexDTO esMerchantIndexDTO = JSONObject.parseObject(el.getSourceAsString(), EsMerchantIndexDTO.class);
                //保留有效地址
                esMerchantIndexDTO.setContacts(esMerchantIndexDTO.getContacts().stream().filter(contact -> Objects.equals(1, contact.getStatus())).collect(Collectors.toList()));
                result.add(esMerchantIndexDTO);
            });
        } catch (IOException e) {
            logger.warn("es搜索io异常",e);
        } finally {
            EsClientPoolUtil.returnClient(client);
        }
        return AjaxResult.getOK(result);
    }


    @Override
    public AjaxResult queryTodayRegister(String mname) {
        //merchantMapper.selectRegisterPrivateSea(mname, LocalDate.now(), getAdminId());
        List<MerchantVO> merchantList = new ArrayList<>();
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setStoreName(mname);
        req.setStatus(MerchantStoreEnums.Status.AUDIT_SUCCESS.getCode());
        req.setEndRegisterTime(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        req.setStartRegisterTime(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
        req.setStartAuditTime(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
        req.setEndAuditTime(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        List<MerchantStoreAndExtendResp> merchantStoreAndExtendResps = merchantQueryFacade.getMerchantExtends(req);
        if (CollectionUtil.isEmpty(merchantStoreAndExtendResps)) {
            return AjaxResult.getOK();
        }
        List<Long> mIdList = merchantStoreAndExtendResps.stream().map(MerchantStoreAndExtendResp::getMId).collect(Collectors.toList());

        List<FollowUpRelation> followUpRelationList = followUpRelationMapper.batchSelect(mIdList,getAdminId());
        if (CollectionUtil.isEmpty(followUpRelationList)) {
            return AjaxResult.getOK();
        }

        List<Long> followUpRelationidList = followUpRelationList.stream().map(FollowUpRelation::getmId).collect(Collectors.toList());


        merchantStoreAndExtendResps.forEach(merchant -> {
            if (followUpRelationidList.contains(merchant.getMId())) {
                MerchantVO merchantVO = new MerchantVO();
                merchantVO.setmId(merchant.getMId());
                merchantVO.setMname(merchant.getStoreName());
                merchantVO.setProvince(merchant.getProvince());
                merchantVO.setCity(merchant.getCity());
                merchantVO.setArea(merchant.getArea());
                merchantVO.setRemark(merchant.getRemark());
                merchantVO.setAreaNo(merchant.getAreaNo());
                merchantVO.setSize(ObjectUtil.equal(merchant.getSize(), ADMIN.getCode()) ? ADMIN.getDesc() : MERCHANT.getDesc());
                merchantVO.setType(merchant.getBusinessType());
                merchantList.add(merchantVO);
            }
        });

        //补充主账户信息
        Map<Long, String> merchantStoreAccountResultRespMap = new HashMap<>();
        List<MerchantStoreAccountResultResp> merchantStoreAccountResultResps = merchantAccountQueryFacade.getPrimaryAccountList(followUpRelationidList);
        if (!CollectionUtil.isEmpty(merchantStoreAccountResultResps)) {
            merchantStoreAccountResultRespMap = merchantStoreAccountResultResps.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getMId,MerchantStoreAccountResultResp::getAccountName));
        }

        //补充信息
        List<MerchantVO> merchantVOList =  merchantMapper.selectByMids(followUpRelationidList);
        Map<Long, MerchantVO> merchantVOMap = merchantVOList.stream().collect(Collectors.toMap(MerchantVO::getmId, Function.identity()));
        Map<Long, String> finalMerchantStoreAccountResultRespMap = merchantStoreAccountResultRespMap;
        merchantList.forEach(merchantVO -> {
            merchantVO.setPhone(merchantVOMap.get(merchantVO.getmId()).getPhone());
            merchantVO.setAddress(merchantVOMap.get(merchantVO.getmId()).getAddress());
            merchantVO.setUnionid(merchantVOMap.get(merchantVO.getmId()).getUnionid());
            merchantVO.setDirect(merchantVOMap.get(merchantVO.getmId()).getDirect());
            merchantVO.setServer(merchantVOMap.get(merchantVO.getmId()).getServer());
            merchantVO.setGrade(merchantVOMap.get(merchantVO.getmId()).getGrade());
            merchantVO.setHouseNumber(merchantVOMap.get(merchantVO.getmId()).getHouseNumber());
            merchantVO.setMcontact(finalMerchantStoreAccountResultRespMap.get(merchantVO.getmId()));
        });

        return AjaxResult.getOK(merchantList);
    }


    @Override
    public AjaxResult queryCluePool(Merchant merchant) {

        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }
        String poiNote = merchant.getPoiNote();
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        //排除已经绑定数据
        queryBuilder
                .mustNot(new TermQueryBuilder("manage", 1));
        if (StringUtils.isNotEmpty(merchant.getMname())) {
            queryBuilder.must(new MatchQueryBuilder("shop_name", merchant.getMname()));
        }
        if (StringUtils.isNotBlank(merchant.getCity())) {
            queryBuilder.should(new MultiMatchQueryBuilder(merchant.getCity().replace("市", ""), "city", "district"));
        }

        //根据poi两点间距离排序
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (!StringUtils.isEmpty(poiNote)) {
            String[] split = poiNote.split(",");
            GeoDistanceSortBuilder sortBuilder =
                    SortBuilders.geoDistanceSort("poi", Double.parseDouble(split[1]), Double.parseDouble(split[0]))
                            .unit(DistanceUnit.METERS)
                            .order(SortOrder.ASC);
            searchSourceBuilder.sort(sortBuilder);
        }

        searchSourceBuilder.from(0).size(10);
        searchSourceBuilder.query(queryBuilder);
        SearchRequest searchRequest = new SearchRequest(INDEX_CRUL_POOL_V1);
        searchRequest.source(searchSourceBuilder);
        List<CluePool> result = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = response.getHits();
            //根据poi和店铺名称搜索数据
            List<SearchHit> arrayHit = Arrays.asList(hits.getHits());
            //整体数据
            List<SearchHit> searchHits = new ArrayList<>(arrayHit);
            // 根据地址搜索周边
            if (!StringUtils.isEmpty(poiNote)) {
                searchHits.addAll(searchByPoi(arrayHit, client, searchRequest, searchSourceBuilder));
            }
            for (SearchHit hit : searchHits) {
                String sourceAsString = hit.getSourceAsString();
                String id = hit.getId();
                CluePool curlPoolDO = JSONObject.parseObject(sourceAsString, CluePool.class);
                curlPoolDO.setId(id);
                String phone = curlPoolDO.getPhone();
                if (!net.summerfarm.common.util.StringUtils.isMobile(phone)) {
                    curlPoolDO.setPhone(null);
                }

                curlPoolDO.setCity(curlPoolDO.getCity() + "市");
                try {
                    if (StringUtils.isNotEmpty(poiNote)){
                        String[] split = poiNote.split(",");
                        Double distance = GaoDeUtil.getLineDistance(Float.parseFloat(Double.parseDouble(split[0])+""),Float.parseFloat(Double.parseDouble(split[1])+""), Float.parseFloat(curlPoolDO.getPoi().getLon()+""), Float.parseFloat(curlPoolDO.getLat()+""));
                        curlPoolDO.setDistance(distance.intValue());
                    }
                    if (StringUtils.isNotEmpty(curlPoolDO.getShopAge())) {
                        int age = Integer.parseInt(curlPoolDO.getShopAge()) / 30;
                        curlPoolDO.setShopAge(age == 0 ? "1" : age+"");
                    }
                }catch (Exception e){
                    logger.warn("计算poi异常");
                }
                result.add(curlPoolDO);
            }
        } catch (IOException e) {
            logger.warn("es搜索io异常",e);
        } finally {
            EsClientPoolUtil.returnClient(client);
        }
        return AjaxResult.getOK(result);
    }

    @Override
    public AjaxResult queryBdArea() {
        return AjaxResult.getOK(crmBdAreaMapper.selectBdArea(getAdminId()));
    }


    /**
     * 根据poi搜索es数据并筛选
     *
     * @param arrayHit            poi和店铺名称搜素数据
     * @param client              es链接
     * @param searchRequest       请求信息
     * @param searchSourceBuilder 搜索信息
     * @return 合并poi筛选数据
     */
    private List<SearchHit> searchByPoi(List<SearchHit> arrayHit, RestHighLevelClient client,
                                        SearchRequest searchRequest, SearchSourceBuilder searchSourceBuilder) {

        List<SearchHit> searchHits = new ArrayList<>();
        //数据大于搜索上限
        if (!CollectionUtils.isEmpty(arrayHit) && arrayHit.size() >= CrmGlobalConstant.QUERY_CLUE_POOL_SIZE) {
            return searchHits;
        }
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        //排除已经绑定数据
        queryBuilder.filter(new TermQueryBuilder("manage", 0));
        searchSourceBuilder.query(queryBuilder);
        searchRequest.source(searchSourceBuilder);
        //只根据poi搜索数据
        try {
            SearchResponse search = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits queryHits = search.getHits();
            SearchHit[] poiHits = queryHits.getHits();
            List<SearchHit> queryByPoi = Arrays.asList(poiHits);
            //为空直接返回数据
            if (CollectionUtils.isEmpty(arrayHit)) {
                searchHits.addAll(queryByPoi);
                return searchHits;
            }
            //可查询数据数量
            int length = CrmGlobalConstant.QUERY_CLUE_POOL_SIZE - arrayHit.size();
            //遍历移除相同数据
            for (SearchHit documentFields : queryByPoi) {
                boolean isSome = false;
                for (SearchHit searchHit : arrayHit) {
                    //es_id相同
                    if (Objects.equals(documentFields.getId(), searchHit.getId())) {
                        isSome = true;
                        break;
                    }
                }
                //es_id相同则剔除
                if (!isSome) {
                    searchHits.add(documentFields);
                }
                //超过数量结束
                if (searchHits.size() >= length) {
                    break;
                }
            }
        } catch (IOException e) {
            logger.info("err Msg={}", e.getMessage());
        }

        return searchHits;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateOperatingState(Long mId, Integer operateStatus) {
        if (Objects.isNull(operateStatus) || Objects.isNull(mId)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT, "参数错误,请联系管理员解决");
        }
        // 设置店铺状态
        Merchant merchant = new Merchant();
        merchant.setmId(mId);
        merchant.setOperateStatus(operateStatus);
        merchant.setUpdater(getAdminId());
        merchantMapper.updateByPrimaryKeySelective(merchant);

        if (!Objects.equals(operateStatus, MerchantOperatingStateEnum.OPERATE_STATE.getId())) {
            // 客户倒闭,将其流出私海
            followUpRelationService.reassign(Collections.singletonList(mId), null,
                    FollowUpRelationEnum.Reason.CUSTOMER_FAILURE.getValue(), FollowUpRelationEnum.Status.RELEASE_TYPE.ordinal(), null);
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectMerchantList(MerchantQuery merchantQuery) {
        Integer page = merchantQuery.getPageIndex();
        Integer perPage = merchantQuery.getPageSize();
        boolean isSa = isSA() || isSaleSA() || isAreaSA();
        if (!isSa) {
            return AjaxResult.getOK();
        }
        merchantQuery.setIsLock(null);
        merchantQuery.setOperateStatusList(Arrays.asList(OperateStatusEnum.PENDING_VERIFICATION.getCode()));
        // 获取查询信息
        Map<String, Object> searchMap = EsUtil.esSearchMerchant(page, perPage, merchantQuery);
        // 处理查询结果
        JSONArray data = (JSONArray) searchMap.get("data");
        data = Optional.ofNullable(data).orElse(new JSONArray());
        List<MerchantListVO> merchantVOList = new ArrayList<>(perPage);
        long nowTimeLong = System.currentTimeMillis();

        for (Object datum : data) {
            String jsonString = JSON.toJSONString(datum);
            MerchantListVO merchantListVO = JSON.parseObject(jsonString, MerchantListVO.class);
            Date registerTime = merchantListVO.getRegisterTime();
            long registerTimeLong = Optional.ofNullable(registerTime).orElse(new Date()).getTime();
            long diff = nowTimeLong - registerTimeLong;
            // 相差天
            long diffDay = diff / DateUtils.A_DAY_TIME_LONG;
            // 相差多少小时
            long diffHour = diff % DateUtils.A_DAY_TIME_LONG / DateUtils.A_HOUR_TIME_LONG;
            if (diffDay > NumberUtils.INTEGER_ZERO || diffHour > NumberUtils.INTEGER_ZERO) {
                String diffStr = diffDay > NumberUtils.INTEGER_ZERO ? diffDay + "天" : diffHour + "小时";
                merchantListVO.setRegisterTimeToNowStr("注册已超过" + diffStr);
            }
            // 警示等级
            int warnIngSign = diffDay > CommonNumbersEnum.SEVEN.getNumber()
                    ? WarnIngSignEnum.RED.ordinal() : WarnIngSignEnum.YELLOW.ordinal();
            merchantListVO.setWarnIngSign(warnIngSign);

            // 注册时间
            merchantListVO.setRegisterTimeStr(DateUtils.date2String(registerTime, BaseDateUtils.LONG_DATE_FORMAT));
            // 运营服务区域
            List<Area> areas = crmManageBdMapper.selectZoneInfoById(null, merchantListVO.getAreaNo());
            if (CollectionUtil.isNotEmpty(areas)) {
                Integer largeAreaNo = areas.get(NumberUtils.INTEGER_ZERO).getLargeAreaNo();
                List<BdExtVO> bdExtVOS = crmManageBdMapper.selectOperateLargeArea(NumberUtils.INTEGER_ONE, largeAreaNo);
                String largeAreaName = CollectionUtil.isNotEmpty(bdExtVOS) ? bdExtVOS.get(NumberUtils.INTEGER_ZERO).getAreaName() : "无";
                LargeAreaDTO largeAreaDTO = new LargeAreaDTO(largeAreaNo, largeAreaName, areas);
                merchantListVO.setLargeAreaDTO(largeAreaDTO);
            }

            merchantVOList.add(merchantListVO);
        }
        searchMap.put("data", merchantVOList);
        return AjaxResult.getOK(searchMap);
    }

    @Override
    public void sendMerchantReviewMessage() {
        // 获取区域及区域主管
        List<CrmBdAreaDTO> crmBdAreaDTOS = crmManageBdMapper.queryExistArea();
        // 根据城市发送待审核客户消息
        MerchantQuery query = new MerchantQuery();
        query.setIsLock(NumberUtils.INTEGER_ONE);
        for (CrmBdAreaDTO crmBdAreaDTO : crmBdAreaDTOS) {
            // 根据城市查询待审核客户，每个城市上限为100个
            query.setAreaNo(crmBdAreaDTO.getAreaNo());
            Map<String, Object> searchMap = EsUtil.esSearchMerchant(NumberUtils.INTEGER_ONE, NumberUtils.INTEGER_ONE_HUNDRED, query);
            JSONArray data = (JSONArray) searchMap.get("data");
            if (CollectionUtil.isEmpty(data)) {
                continue;
            }

            String title = crmBdAreaDTO.getAreaName() + "新客户注册提醒，客户已注册，请尽快审核";
            StringBuffer text = new StringBuffer("#### " + title + "\n");
            text.append("> ").append("###### ")
                    .append("运营区域:").append(crmBdAreaDTO.getAreaName())
                    .append("\n")
                    .append("> ").append("###### ")
                    .append("待审核商户数:").append(data.size())
                    .append("\n")
                    .append("请尽快进入CRM小程序进行门店审核");

            // 获取该城市负责人
            BdExt bdExt = crmManageBdMapper.selectRealName(crmBdAreaDTO.getAreaNo());
            if (Objects.isNull(bdExt)) {
                logger.info("{}没有配置城市负责人", crmBdAreaDTO.getAreaNo());
                continue;
            }

            DingTalkMsgReceiverIdBO bo = new DingTalkMsgReceiverIdBO();
            bo.setReceiverIdList(Collections.singletonList(Long.valueOf(bdExt.getAdminId())));
            bo.setText(text.toString());
            bo.setMsgType(DingTalkMsgEnum.MARKDOWN.getType());
            bo.setTitle(title);
            dingTalkMsgSender.sendMessageWithFeiShu(bo);
        }

    }

    @Override
    public CommonResult<DeliveryMerchantVO> deliveryMerchantCount(DeliveryMerchantQuery deliveryMerchantQuery) {
        LocalDate date = deliveryMerchantQuery.getDeliveryTime().plusDays(NumberUtils.INTEGER_ONE);
        deliveryMerchantQuery.setDeliveryTime(date);
        this.dealWithDeliveryMerchantQuery(deliveryMerchantQuery);
        DeliveryMerchantVO deliveryMerchantVO = new DeliveryMerchantVO();
        List<DeliveryMerchantVO> deliveryMerchantVOList = crmMerchantFutureDayGmvMapper.selectByQuery(deliveryMerchantQuery);
        int total = Optional.ofNullable(deliveryMerchantVOList)
                .orElse(new ArrayList<>())
                .size();
        deliveryMerchantVO.setDeliveryOrderNum(total);
        Integer saleTag = deliveryMerchantQuery.getSaleTag() ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO;
        deliveryMerchantVO.setSaleTag(saleTag);
        return CommonResult.ok(deliveryMerchantVO);
    }

    private void dealWithDeliveryMerchantQuery(DeliveryMerchantQuery deliveryMerchantQuery) {
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_FUTURE_DAY_GMV.getTableName());
        Integer attributeDataFlag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        deliveryMerchantQuery.setDayTag(attributeDataFlag);
        deliveryMerchantQuery.setDeliveryGmvComplianceThreshold(DeliveryMerchantEnum.DELIVERY_UP_TO_STANDARD);
        boolean isSa = super.isSA() || super.isSaleSA() || super.isAreaSA();
        deliveryMerchantQuery.setSaleTag(isSa);
        if (!isSa) {
            deliveryMerchantQuery.setAdminId(super.getAdminId());
        }
    }

    @Override
    public CommonResult<PageInfo<DeliveryMerchantVO>> deliveryMerchantList(DeliveryMerchantQuery deliveryMerchantQuery) {
        this.dealWithDeliveryMerchantQuery(deliveryMerchantQuery);

        PageHelper.startPage(deliveryMerchantQuery.getPageIndex(), deliveryMerchantQuery.getPageSize(), true, true);
        List<DeliveryMerchantVO> deliveryMerchantVOList = crmMerchantFutureDayGmvMapper.selectByQuery(deliveryMerchantQuery);

        if (deliveryMerchantVOList.isEmpty()){
            return CommonResult.ok(PageInfoHelper.createPageInfo(deliveryMerchantVOList));
        }

        List<Long> contactIdList = deliveryMerchantVOList.stream().map(DeliveryMerchantVO::getContactId).collect(Collectors.toList());
        List<ContactDto> contactList = contactQueryFacade.getMerchantContactList(contactIdList);
        Map<Long, ContactDto> contactMap = contactList.stream().collect(Collectors.toMap(Contact::getContactId, Function.identity()));

        List<Long> mIdList = deliveryMerchantVOList.stream().map(DeliveryMerchantVO::getmId).filter(Objects::nonNull).collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtendsByMid(mIdList);
        Map<Long, MerchantStoreAndExtendResp> merchantExtendMap = merchantExtendList.stream().collect(Collectors.toMap(MerchantStoreAndExtendResp::getMId, Function.identity()));

        List<FollowUpRelation> followUpRelations = followUpRelationMapper.batchSelect(mIdList, getAdminId());
        Map<Long, FollowUpRelation> followMap = followUpRelations.stream().collect(Collectors.toMap(FollowUpRelation::getmId, Function.identity()));


        for (DeliveryMerchantVO deliveryMerchantVO : deliveryMerchantVOList) {
            // 获取商户名, 配送地址, 跟进销售名称
            if (contactMap.containsKey(deliveryMerchantVO.getContactId())){
                ContactDto c = contactMap.get(deliveryMerchantVO.getContactId());
                String address=c.getCity()+c.getArea()+c.getAddress()+c.getHouseNumber();
                deliveryMerchantVO.setDeliveryAddress(address);
            }

            if (merchantExtendMap.containsKey(deliveryMerchantVO.getmId())){
                MerchantStoreAndExtendResp merchant = merchantExtendMap.get(deliveryMerchantVO.getmId());
                deliveryMerchantVO.setSize(StoreSizeEnum.getSize(merchant.getSize()).getDesc());
                deliveryMerchantVO.setMerchantName(merchant.getStoreName());
            }

            if (followMap.containsKey(deliveryMerchantVO.getmId())){
                deliveryMerchantVO.setBdName(followMap.get(deliveryMerchantVO.getmId()).getAdminName());
            }

            int diffSpuNum = DeliveryMerchantEnum.SPU_UP_TO_STANDARD - deliveryMerchantVO.getSpuNum();
            // 差额
            BigDecimal diffDeliveryGmv = DeliveryMerchantEnum.DELIVERY_UP_TO_STANDARD.subtract(deliveryMerchantVO.getDeliveryGmv());
            diffDeliveryGmv = BigDecimal.ZERO.compareTo(diffDeliveryGmv) > 0 ? BigDecimal.ZERO : diffDeliveryGmv;
            deliveryMerchantVO.setLackDeliveryGmv(diffDeliveryGmv);
            deliveryMerchantVO.setLackSpuNum(Math.max(diffSpuNum, 0));
        }

        return CommonResult.ok(PageInfoHelper.createPageInfo(deliveryMerchantVOList));
    }

    @Override
    public CommonResult<PageInfo<CrmSkuMonthGmvVO>> skuGmvList(SkuMerchantQuery skuMerchantQuery) {
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_SKU_MONTH_GMV.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        skuMerchantQuery.setMonthTag(dateTag);

        if (Objects.nonNull(skuMerchantQuery.getPdName())) {
            List<CrmSkuMonthGmvVO> skuMonthGmvVOList = productsRepository.selectByQuery(skuMerchantQuery);
            if (CollectionUtil.isNotEmpty(skuMonthGmvVOList)) {
                List<String> skuList = skuMonthGmvVOList.stream().map(CrmSkuMonthGmvVO::getSku).collect(Collectors.toList());
                skuMerchantQuery.setSkuList(skuList);
            }
        }

        PageHelper.startPage(skuMerchantQuery.getPageIndex(), skuMerchantQuery.getPageSize(), Boolean.TRUE, Boolean.TRUE);
        List<CrmSkuMonthGmvVO> crmSkuMonthGmvVOList = crmSkuMonthGmvMapper.selectByQuery(skuMerchantQuery);

        if (CollectionUtil.isEmpty(crmSkuMonthGmvVOList)){
           return CommonResult.ok(PageInfoHelper.createPageInfo(crmSkuMonthGmvVOList));
        }
        SkuMerchantQuery query = new SkuMerchantQuery();
        List<String> skuList = crmSkuMonthGmvVOList.stream().map(CrmSkuMonthGmvVO::getSku).collect(Collectors.toList());
        query.setSkuList(skuList);
        List<CrmSkuMonthGmvVO> skuMonthGmvVOList = productsRepository.selectByQuery(query);

        if (CollectionUtil.isEmpty(skuMonthGmvVOList)){
            return CommonResult.ok(PageInfoHelper.createPageInfo(crmSkuMonthGmvVOList));
        }
        Map<String, List<CrmSkuMonthGmvVO>> collect = skuMonthGmvVOList.stream().filter(it->StringUtils.isNotEmpty(it.getSku())).collect(Collectors.groupingBy(CrmSkuMonthGmvVO::getSku));

        for (CrmSkuMonthGmvVO crmSkuMonthGmvVO : crmSkuMonthGmvVOList) {
            String sku = crmSkuMonthGmvVO.getSku();
            if (StringUtils.isNotEmpty(sku) && collect.containsKey(sku)) {
                this.fillCrmMonthProductInfo(crmSkuMonthGmvVO, collect.get(sku));
            }else {
                crmSkuMonthGmvVO.setPdName("查无此sku");
            }

        }

        return CommonResult.ok(PageInfoHelper.createPageInfo(crmSkuMonthGmvVOList));
    }

    private void fillCrmMonthProductInfo(CrmSkuMonthGmvVO crmSkuMonthGmvVO, List<CrmSkuMonthGmvVO> skuMonthGmvVOList) {
        CrmSkuMonthGmvVO gmvVO = skuMonthGmvVOList.get(NumberUtils.INTEGER_ZERO);
        crmSkuMonthGmvVO.setPdName(gmvVO.getPdName());
        crmSkuMonthGmvVO.setWeight(gmvVO.getWeight());
        crmSkuMonthGmvVO.setPdId(gmvVO.getPdId());
        crmSkuMonthGmvVO.setSpu(gmvVO.getSpu());
        crmSkuMonthGmvVO.setPicturePath(gmvVO.getPicturePath());
    }


    @Override
    public CommonResult<CrmSkuMonthGmvVO> skuGmvDetail(SkuMerchantQuery skuMerchantQuery) {
        if (Objects.isNull(skuMerchantQuery.getSku()) || Objects.isNull(skuMerchantQuery.getAreaNo())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "参数错误,区域编号和sku均不能为空");
        }

        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_SKU_MONTH_GMV.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();
        skuMerchantQuery.setMonthTag(dateTag);
        // 本月
        List<CrmSkuMonthGmvVO> crmSkuMonthGmvVOList = crmSkuMonthGmvMapper.selectByQuery(skuMerchantQuery);
        if (CollectionUtil.isEmpty(crmSkuMonthGmvVOList)) {
            return CommonResult.ok();
        }
        CrmSkuMonthGmvVO crmSkuMonthGmvVO = crmSkuMonthGmvVOList.get(NumberUtils.INTEGER_ZERO);

        List<CrmSkuMonthGmvVO> skuMonthGmvVOList = productsRepository.selectByQuery(skuMerchantQuery);
        if (CollectionUtil.isEmpty(skuMonthGmvVOList)) {
            crmSkuMonthGmvVO.setPdName("查无此sku");
        }
        this.fillCrmMonthProductInfo(crmSkuMonthGmvVO, skuMonthGmvVOList);

        // 上月
        String dateStr = DateUtils.localDateTimeToStringTwo(LocalDate.now().minusMonths(NumberUtils.INTEGER_ONE));
        int lastMonthTag = Integer.parseInt(dateStr) / NumberUtils.INTEGER_ONE_HUNDRED;
        skuMerchantQuery.setMonthTag(lastMonthTag);
        List<CrmSkuMonthGmvVO> lastCrmSkuMonthGmvVOList = crmSkuMonthGmvMapper.selectByQuery(skuMerchantQuery);
        if (CollectionUtil.isEmpty(lastCrmSkuMonthGmvVOList)) {
            return CommonResult.ok(crmSkuMonthGmvVO);
        }
        CrmSkuMonthGmvVO lastCrmSkuMonthGmvVO = lastCrmSkuMonthGmvVOList.get(NumberUtils.INTEGER_ZERO);

        // gmv环比
        BigDecimal gmvSub = crmSkuMonthGmvVO.getGmv().subtract(lastCrmSkuMonthGmvVO.getGmv());
        BigDecimal gmvRingRatio = BigDecimal.ONE;
        if (BigDecimal.ZERO.compareTo(lastCrmSkuMonthGmvVO.getGmv()) < 0) {
            gmvRingRatio = gmvSub.divide(lastCrmSkuMonthGmvVO.getGmv(), 2, RoundingMode.HALF_UP);
        }
        crmSkuMonthGmvVO.setGmvRingRatio(gmvRingRatio);

        // 销量环比
        BigDecimal salesVolumeSub = BigDecimal.valueOf(crmSkuMonthGmvVO.getSalesVolume()).subtract(BigDecimal.valueOf(lastCrmSkuMonthGmvVO.getSalesVolume()));
        BigDecimal salesVolumeRingRatio = BigDecimal.ONE;
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(lastCrmSkuMonthGmvVO.getSalesVolume())) < 0) {
            salesVolumeRingRatio = salesVolumeSub.divide(BigDecimal.valueOf(lastCrmSkuMonthGmvVO.getSalesVolume()), 2, RoundingMode.HALF_UP);
        }
        crmSkuMonthGmvVO.setSalesVolumeRingRatio(salesVolumeRingRatio);

        // 下单商户数环比
        BigDecimal merchantNumSub = BigDecimal.valueOf(crmSkuMonthGmvVO.getMerchantNum()).subtract(BigDecimal.valueOf(lastCrmSkuMonthGmvVO.getMerchantNum()));
        BigDecimal merchantNumRingRatio = BigDecimal.ONE;
        if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(lastCrmSkuMonthGmvVO.getMerchantNum())) < 0) {
            merchantNumRingRatio = merchantNumSub.divide(BigDecimal.valueOf(lastCrmSkuMonthGmvVO.getMerchantNum()), 2, RoundingMode.HALF_UP);
        }
        crmSkuMonthGmvVO.setMerchantNumRingRatio(merchantNumRingRatio);
        return CommonResult.ok(crmSkuMonthGmvVO);
    }

    @Override
    public CommonResult careCustomer(CrmCareCustomerVO crmCareCustomerVO) {
        Integer mId = crmCareCustomerVO.getMId();
        Integer adminId = getAdminId();
        FollowUpRelation followUpRelation = followUpRelationMapper.selectLastFollowOne(adminId, mId);
        if (followUpRelation == null) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "该客户不存在或不是自己的私海客户，暂时不允许被操作");
        }
        int careAdminId = crmCareCustomerVO.getStatus() == 0 ? adminId : 0;
        int result = followUpRelationMapper.updateCareAdminId(followUpRelation.getId(), careAdminId);
        if (result > 0) {
            EsMerchantIndexDTO esMerchantIndexDTO = EsUtil.queryByMid(mId.longValue());
            if (esMerchantIndexDTO == null){
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "该客户不存在");
            }
            esMerchantIndexDTO.setCareBdId((long) careAdminId);
            EsUtil.insertOrUpdateByMid(esMerchantIndexDTO);
        }
        return CommonResult.ok();
    }

    @Override
    public CommonResult<KeyCustomerCountVO> keyCustomerCount(String province,String city,String area, Long bdId) {
        ExecutorService executor = ThreadUtils.executor;
        KeyCustomerCountVO customerCountVO = new KeyCustomerCountVO();
        CountDownLatch countDownLatch = new CountDownLatch(4);
        customerCountVO.init();
        boolean bd = isBd();
        Long BdID = bd ? getAdminId().longValue() : null;
        try {
            //即将掉入公海
            CrmKeyCustomerQuery dangerQueryVo = new CrmKeyCustomerQuery();
            dangerQueryVo.setProvince(province);
            dangerQueryVo.setCity(city);
            dangerQueryVo.setArea(area);
            dangerQueryVo.setDangerDay(3);
            dangerQueryVo.setBd(bd);
            dangerQueryVo.setNotEmptyBd(Boolean.TRUE);
            initKeyCustomerQuery(false, dangerQueryVo);
            executor.submit(() -> dangerCount(countDownLatch, dangerQueryVo, customerCountVO));

            //30天为下单
            CrmKeyCustomerQuery noOderQueryVo = new CrmKeyCustomerQuery();
            dangerQueryVo.setProvince(province);
            dangerQueryVo.setCity(city);
            dangerQueryVo.setArea(area);
            noOderQueryVo.setTag(REGISTER_NO_ORDER_30_DAY);
            noOderQueryVo.setBd(bd);
            initKeyCustomerQuery(false, noOderQueryVo);
            executor.submit(() -> noOrderCount(countDownLatch, noOderQueryVo, customerCountVO));

            //首单客户
            CrmKeyCustomerQuery firstOrderVo = new CrmKeyCustomerQuery();
            dangerQueryVo.setProvince(province);
            dangerQueryVo.setCity(city);
            dangerQueryVo.setArea(area);
            firstOrderVo.setTag(REGISTER_FIRST_ORDER_30_DAY);
            firstOrderVo.setBd(bd);
            initKeyCustomerQuery(false, firstOrderVo);
            executor.submit(() -> firstOrderCount(countDownLatch, firstOrderVo, customerCountVO));


            //省心送客户
            executor.submit(() -> comfortSendCount(countDownLatch, province,city,area, BdID, customerCountVO));


            //等待2秒 error 异常
            countDownLatch.await(2, TimeUnit.SECONDS);
            customerCountVO.sum();

        } catch (Exception e) {
            logger.warn("查询rCount error,  BdId {}  ", getAdminId(), e);

        }
        return CommonResult.ok(customerCountVO);
    }

    private void dangerCount(CountDownLatch countDownLatch, CrmKeyCustomerQuery crmKeyCustomerQuery, KeyCustomerCountVO customerCountVO) {
        try {
            crmKeyCustomerQuery.countInit();
            int total = (int) search(crmKeyCustomerQuery, false).get("total");
            customerCountVO.setDangerCount(total);
        } catch (Exception e) {
            logger.warn("查询dangerCount error,  query {}  ", JSONObject.toJSONString(crmKeyCustomerQuery), e);

        } finally {
            countDownLatch.countDown();
        }
    }

    private void initKeyCustomerQuery(Boolean care, CrmKeyCustomerQuery keyCustomerQuery) {
        Integer adminId = getAdminId();
        boolean bd = isBd();
        if (bd) {
            if (care) {
                keyCustomerQuery.setCarBdId(adminId);
            }
            keyCustomerQuery.setBdId(adminId);
        }
    }


    private void comfortSendCount(CountDownLatch countDownLatch, String  province,String city,String area, Long bdId, KeyCustomerCountVO customerCountVO) {
        try {
            int count = comfortSendService.sumComfortCount(bdId, province,city,area);
            customerCountVO.setComfortSendCount(count);

        } catch (Exception e) {
            logger.warn("comfortSendCount error, bdid {}", bdId, e);
        } finally {
            countDownLatch.countDown();
        }
    }


    private void firstOrderCount(CountDownLatch countDownLatch, CrmKeyCustomerQuery crmKeyCustomerQuery, KeyCustomerCountVO customerCountVO) {
        try {
            crmKeyCustomerQuery.countInit();
            int total = (int) search(crmKeyCustomerQuery, false).get("total");
            customerCountVO.setFirstOrderCount(total);
        } catch (Exception e) {
            logger.warn("查询firstCount error,  query {}  ", JSONObject.toJSONString(crmKeyCustomerQuery), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    private void noOrderCount(CountDownLatch countDownLatch, CrmKeyCustomerQuery crmKeyCustomerQuery, KeyCustomerCountVO customerCountVO) {
        try {
            crmKeyCustomerQuery.countInit();
            int total = (int) search(crmKeyCustomerQuery, false).get("total");
            customerCountVO.setNoOrderCount(total);
        } catch (Exception e) {
            logger.warn("查询noOrderCount error,  query {}  ", JSONObject.toJSONString(crmKeyCustomerQuery), e);
        } finally {
            countDownLatch.countDown();
        }
    }


    @Override
    public CommonResult<Map<String, Object> > dangerCustomer(CrmKeyCustomerQuery keyCustomerQuery) {
        keyCustomerQuery.setDangerDay(3);
        keyCustomerQuery.setBd(isBd());
        keyCustomerQuery.setNotEmptyBd(Boolean.TRUE);
        initKeyCustomerQuery(false, keyCustomerQuery);
        Map<String, Object> search = search(keyCustomerQuery, false);
        merge(search);
        return CommonResult.ok(search);
    }


    private Map<String, Object> search(CrmKeyCustomerQuery keyCustomerQuery, boolean care) {
        EsQuery esQuery = new EsQuery(EsIndexContext.INDEX_MERCHANT, keyCustomerQuery.getPageIndex(), keyCustomerQuery.getPageSize());
        boolean bd = keyCustomerQuery.getBd();
        return EsUtil.queryKeyCustomer(esQuery, keyCustomerQuery, bd, care);
    }

    @Override
    public CommonResult<Map<String, Object>>  noOrderCustomer(CrmKeyCustomerQuery keyCustomerQuery) {
        keyCustomerQuery.setTag(REGISTER_NO_ORDER_30_DAY);
        keyCustomerQuery.setBd(isBd());
        initKeyCustomerQuery(false, keyCustomerQuery);

        Map<String, Object> search = search(keyCustomerQuery, false);
        merge(search);
        return CommonResult.ok(search);
    }

    @Override
    public CommonResult<Map<String, Object>>  firstOrderCustomer(CrmKeyCustomerQuery keyCustomerQuery) {
        keyCustomerQuery.setTag(REGISTER_FIRST_ORDER_30_DAY);
        keyCustomerQuery.setBd(isBd());
        initKeyCustomerQuery(false, keyCustomerQuery);

        Map<String, Object> search = search(keyCustomerQuery, false);
        merge(search);
        return CommonResult.ok(search);
    }

    @Override
    public CommonResult queryCareCustomer(CrmKeyCustomerQuery keyCustomerQuery) {
        keyCustomerQuery.setBd(isBd());
        initKeyCustomerQuery(true, keyCustomerQuery);
        if (isBD()) {
            keyCustomerQuery.setCarBdId(getAdminId());
        }
        Map<String, Object> search = search(keyCustomerQuery, true);
        merge(search, true);

        return CommonResult.ok(search);
    }

    @Override
    public CommonResult<KeyCustomerCountDTO> keyCustomerCountVO(AreaCodeBdIdQuery query) {
        KeyCustomerCountDTO customerCountDTO = new KeyCustomerCountDTO();
        CrmKeyCustomerQuery crmKeyCustomerQuery = new CrmKeyCustomerQuery();
        if (query.getBdId() != null) {
            crmKeyCustomerQuery.setBdId(query.getBdId().intValue());
        }
        crmKeyCustomerQuery.setBd(isBd());
        crmKeyCustomerQuery.countInit();
        crmKeyCustomerQuery.setProvince(query.getProvince());
        crmKeyCustomerQuery.setCity(query.getCity());
        crmKeyCustomerQuery.setArea(query.getArea());
        CommonResult<Map<String, Object>> dangerCustomer = dangerCustomer(crmKeyCustomerQuery);
        Object dangerCustomerCount = dangerCustomer.getData().get("total");
        if (dangerCustomerCount!=null){
            customerCountDTO.setDangerCount(Integer.valueOf(dangerCustomerCount.toString()));
        }
        crmKeyCustomerQuery.setDangerDay(null);
        CommonResult<Map<String, Object>> noOrderCustomer = noOrderCustomer(crmKeyCustomerQuery);
        Object totalCount = noOrderCustomer.getData().get("total");
        if (totalCount!=null){
            customerCountDTO.setNoOrderCount(Integer.valueOf(totalCount.toString()));
        }
        CommonResult<Map<String, Object>> firstOrderCustomer = firstOrderCustomer(crmKeyCustomerQuery);
        Object firstCount = firstOrderCustomer.getData().get("total");
        if (firstCount!=null){
            customerCountDTO.setFirstOrderCount(Integer.valueOf(firstCount.toString()));
        }
        return CommonResult.ok(customerCountDTO);
    }

    @Override
    public CommonResult<OrderDeliveryTodayDTO> orderDeliveryToday(String province,String city,String area) {
        List<FollowUpRelation> followUpRelationList = followUpRelationMapper.selectByCity(getAdminId(), province,city, area);
        if (!isBD() || CollectionUtils.isEmpty(followUpRelationList)) {
            return CommonResult.ok(new OrderDeliveryTodayDTO());
        }
        OrderDeliveryTodayDTO orderDeliveryTodayDTO = new OrderDeliveryTodayDTO();
        List<Long> mIds = followUpRelationList.stream().map(FollowUpRelation::getmId).collect(Collectors.toList());
        List<OrderDeliveryVO> orderDeliveryVOList =  ordersMapper.selectOrderDeliveryByMid(mIds,LocalDate.now());
        if (!CollectionUtils.isEmpty(orderDeliveryVOList)){
            List<Integer> deliveryPlanIds = orderDeliveryVOList.stream().map(OrderDeliveryVO::getDeliveryPlanId).collect(Collectors.toList());
            List<OrderDefectInfo> defectInfos =  orderDefectInfoMapper.selectByDeliveryId(deliveryPlanIds);
            DistOrderQueryReq distOrderReq = new DistOrderQueryReq();
            List<String> orderNoList = orderDeliveryVOList.stream().map(OrderDeliveryVO::getOrderNo).collect(Collectors.toList());
            List<String> contactIdList = orderDeliveryVOList.stream().map(OrderDeliveryVO::getContactId).map(String::valueOf).collect(Collectors.toList());
            distOrderReq.setOuterOrderIds(orderNoList);
            distOrderReq.setOuterContactIds(contactIdList);
            List<Integer> sources = new ArrayList<>();
            sources.add(DistOrderSourceEnum.XM_MALL.getCode());
            sources.add(DistOrderSourceEnum.XM_MALL_TIMING.getCode());
            distOrderReq.setSources(sources);
            distOrderReq.setExpectBeginTime(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
            List<DistOrderResp> distOrderRespList = new ArrayList<>();
            logger.info("请求TMS订单参数：{}",JSON.toJSONString(distOrderReq));
            DubboResponse<List<DistOrderResp>> interceptResponse = tmsDistOrderQueryProvider.queryDistOrderList(distOrderReq);
            if (Objects.nonNull(interceptResponse) && interceptResponse.getCode().equals(DubboResponse.COMMON_SUCCESS_CODE)){
                distOrderRespList = interceptResponse.getData();
                logger.info("请求TMS订单返回：{}",JSON.toJSONString(distOrderRespList));
            }else{
                throw new DefaultServiceException(interceptResponse.getMsg());
            }
            Integer orderDeliveryCount = orderDeliveryVOList.size();
            Long finishDeliveryCount = distOrderRespList.stream().filter(order -> order.getStatus().equals(DistOrderStatusEnum.COMPLETE_DELIVERY.getCode())).count();
            Long inDeliveryCount = distOrderRespList.stream().filter(order -> order.getStatus().equals(DistOrderStatusEnum.IN_DELIVERY.getCode())).count();
            Integer outStockQuantity = defectInfos.stream().filter(info ->  info.getDefectAmount() > 0).collect(Collectors.toList()).size();
            orderDeliveryTodayDTO.setFinishDeliveryCount(finishDeliveryCount);
            orderDeliveryTodayDTO.setInDeliveryCount(inDeliveryCount);
            orderDeliveryTodayDTO.setOrderDeliveryCount(orderDeliveryCount);
            orderDeliveryTodayDTO.setOutStockQuantity(outStockQuantity);
        }
       return CommonResult.ok(orderDeliveryTodayDTO);
    }

    @Override
    public CommonResult<OrderDeliveryRouteVO> orderDeliveryList(OrderDeliveryQuery orderDeliveryQuery) {
        List<FollowUpRelation> followUpRelationList = followUpRelationMapper.selectByCity(getAdminId(), orderDeliveryQuery.getProvince(), orderDeliveryQuery.getCity(), orderDeliveryQuery.getArea());
        List<MerchantStoreResultResp> merchantStore = merchantQueryFacade.getMerchantByStoreNameAndId(followUpRelationList.stream().map(FollowUpRelation::getmId).collect(Collectors.toList()), orderDeliveryQuery.getMname());
        if (CollectionUtil.isEmpty(merchantStore)){
            return CommonResult.ok();
        }
        List<Long> mIdList = merchantStore.stream().map(MerchantStoreResultResp::getMId).collect(Collectors.toList());
        followUpRelationList = followUpRelationList.stream().filter(fur -> mIdList.contains(fur.getmId())).collect(Collectors.toList());
        if (!isBD() || CollectionUtils.isEmpty(followUpRelationList)) {
            return CommonResult.ok();
        }
        List<Long> mIds = followUpRelationList.stream().map(FollowUpRelation::getmId).collect(Collectors.toList());
        List<OrderDeliveryVO> removeList = new ArrayList<>();
        List<OrderDeliveryVO> orderDeliveryVOList =  ordersMapper.selectOrderDeliveryByMid(mIds,LocalDate.now());
        // 配送路线
        List<DistributionStoreVO> storeVOS = new ArrayList<>();

        if (!CollectionUtils.isEmpty(orderDeliveryVOList)){
            List<List<OrderDeliveryVO>> splitOrder = ListUtil.split(orderDeliveryVOList, 100);
            List<DistOrderResp> distOrderRespList = new ArrayList<>();
            // 分批查询配送单信息，接口限量100个
            for (List<OrderDeliveryVO> orderDeliveryVos : splitOrder) {
                DistOrderQueryReq distOrderReq = new DistOrderQueryReq();
                List<String> orderNoList = orderDeliveryVos.stream().map(OrderDeliveryVO::getOrderNo).distinct().collect(Collectors.toList());
                List<String> contactIdList = orderDeliveryVos.stream().map(OrderDeliveryVO::getContactId).distinct().map(String::valueOf).collect(Collectors.toList());
                distOrderReq.setOuterOrderIds(orderNoList);
                distOrderReq.setOuterContactIds(contactIdList);
                List<Integer> sources = new ArrayList<>();
                sources.add(DistOrderSourceEnum.XM_MALL.getCode());
                sources.add(DistOrderSourceEnum.XM_MALL_TIMING.getCode());
                distOrderReq.setSources(sources);
                distOrderReq.setExpectBeginTime(LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
                logger.info("请求TMS订单参数：{}", JSON.toJSONString(distOrderReq));
                DubboResponse<List<DistOrderResp>> interceptResponse = tmsDistOrderQueryProvider.queryDistOrderList(distOrderReq);
                if (Objects.nonNull(interceptResponse) && interceptResponse.getCode().equals(DubboResponse.COMMON_SUCCESS_CODE)) {
                    distOrderRespList.addAll(interceptResponse.getData());
                    logger.info("请求TMS订单返回：{}", JSON.toJSONString(distOrderRespList));
                } else {
                    throw new DefaultServiceException(interceptResponse.getMsg());
                }
            }
            Map<String,DistOrderResp> orderRespMap = distOrderRespList.stream().collect(Collectors.toMap(DistOrderResp::getOutOrderId, Function.identity(),(entity1,entity2) -> entity1));
            List<Integer> deliveryPlanIds = orderDeliveryVOList.stream().map(OrderDeliveryVO::getDeliveryPlanId).collect(Collectors.toList());
            List<OrderDefectInfo> defectInfos =  orderDefectInfoMapper.selectByDeliveryId(deliveryPlanIds);
            //获取联系人集合
            List<Long> contactIds = orderDeliveryVOList.stream().map(OrderDeliveryVO::getContactId).distinct().collect(Collectors.toList());
            Map<Long, MerchantStoreAndExtendResp> merchantExtendMap = new HashMap<>();
            Map<Long, Contact> contactHashMap = new HashMap<>();

            //获取门店集合
            List<Long> allMids = orderDeliveryVOList.stream().map(OrderDeliveryVO::getMId).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(allMids)){
                List<MerchantStoreAndExtendResp> merchantExtends = merchantQueryFacade.getMerchantExtendsByMid(allMids);
                merchantExtendMap = merchantExtends.stream().collect(Collectors.toMap(MerchantStoreAndExtendResp::getMId, Function.identity(), (entity1, entity2) -> entity1));
            }
            if (!CollectionUtils.isEmpty(contactIds)){
                List<Contact> contacts = contactMapper.selectByPrimaryKeys(contactIds);
                contactHashMap.putAll(contacts.stream().collect(Collectors.toMap(Contact::getContactId, Function.identity(), (entity1, entity2) -> entity1)));
            }

            for (OrderDeliveryVO deliveryVO : orderDeliveryVOList){
                Integer outStockQuantity = 0;
                //MerchantVO merchantVO =  merchantMapper.selectByPrimaryKey(deliveryVO.getMId());
                Long mId = deliveryVO.getMId();
                MerchantStoreAndExtendResp merchantExtend = merchantExtendMap.get(mId);
                if (!ObjectUtils.isEmpty(merchantExtend)){
                    deliveryVO.setMName(merchantExtend.getStoreName());
                }
                Contact contact = contactHashMap.get(deliveryVO.getContactId()) ;
                deliveryVO.setContactId(deliveryVO.getContactId());
                if (!ObjectUtils.isEmpty(contact)){
                    deliveryVO.setAddress(contact.getProvince()+contact.getCity()+contact.getArea()+contact.getAddress());
                }
                for (OrderDefectInfo defectInfo :defectInfos){
                    if (defectInfo.getOrderNo().equals(deliveryVO.getOrderNo()) && defectInfo.getDefectAmount() > 0){
                        outStockQuantity = outStockQuantity + defectInfo.getDefectAmount();
                    }
                }
                deliveryVO.setOutStockQuantity(outStockQuantity);
                DistOrderResp distOrderResp =  orderRespMap.get(deliveryVO.getOrderNo());
                if (!ObjectUtils.isEmpty(distOrderResp) && (distOrderResp.getStatus().equals(DistOrderStatusEnum.TO_BE_PICKED.getCode()) ||
                        distOrderResp.getStatus().equals(DistOrderStatusEnum.IN_DELIVERY.getCode()) ||
                        distOrderResp.getStatus().equals(DistOrderStatusEnum.COMPLETE_DELIVERY.getCode()) ||
                        distOrderResp.getStatus().equals(DistOrderStatusEnum.COMPLETE_DELIVERY_SHORT.getCode()))){
                    deliveryVO.setDeliveryStatus(distOrderResp.getStatus());
                    if (CollectionUtil.isNotEmpty(distOrderResp.getDeliveryBatchList())) {
                        deliveryVO.setPathId(distOrderResp.getDeliveryBatchList().get(0).getPathId());
                    }
                    deliveryVO.setCancelType(distOrderResp.getCancelType().getCode());
                }else {
                    removeList.add(deliveryVO);
                }
            }

            if (!CollectionUtils.isEmpty(removeList)){
                orderDeliveryVOList.removeAll(removeList);
            }
            // 解析配送路线
            List<DeliveryBatchResp> deliveryBatchResp=new ArrayList<>();
            distOrderRespList.forEach(d->deliveryBatchResp.addAll(d.getDeliveryBatchList()));
            Map<String, List<DeliveryBatchResp>> distOrderMap = deliveryBatchResp.stream().collect(Collectors.groupingBy(DeliveryBatchResp::getBeginSiteName));
            for (Map.Entry<String, List<DeliveryBatchResp>> entry : distOrderMap.entrySet()) {
                DistributionStoreVO storeVO=new DistributionStoreVO();
                List<DistributionPathVO> pathVOS = entry.getValue().stream().filter(distinctByKey(DeliveryBatchResp::getPathId)).map(d -> {
                    DistributionPathVO pathVO = new DistributionPathVO();
                    pathVO.setPathId(d.getPathId());
                    if (StrUtil.isNotEmpty(d.getPathName())) {
                        pathVO.setPathName(d.getPathCode() + "(" + d.getPathName() + ")");
                    } else {
                        pathVO.setPathName(d.getPathCode());
                    }
                    return pathVO;
                }).collect(Collectors.toList());
                storeVO.setStoreName(entry.getKey());
                storeVO.setPathList(pathVOS);
                storeVOS.add(storeVO);
            }
        }

        OrderDeliveryRouteVO deliveryRouteVO = new OrderDeliveryRouteVO();
        deliveryRouteVO.setOrderDeliverVo(orderDeliveryVOList);
        deliveryRouteVO.setDistributionStoreVO(storeVOS);

        return CommonResult.ok(deliveryRouteVO);
    }

    private <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> concurrentHashMap = new ConcurrentHashMap<>();
        return t -> concurrentHashMap.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public CommonResult<OrderDeliveryDetailDTO> orderDeliveryDetail(OrderDeliveryQuery orderDeliveryQuery) {

        DistOrderQueryReq distOrderReq = new DistOrderQueryReq();
        distOrderReq.setOuterOrderId(orderDeliveryQuery.getOrderNo());
        distOrderReq.setOuterContactId(orderDeliveryQuery.getContactId().toString());
        Orders orders = ordersMapper.selectByOrderNo(orderDeliveryQuery.getOrderNo());
        if (orders.getType().equals(1)){
            distOrderReq.setSource(DistOrderSourceEnum.XM_MALL_TIMING.getCode());
        }else {
            distOrderReq.setSource(DistOrderSourceEnum.XM_MALL.getCode());
        }
        distOrderReq.setDeliveryTime(LocalDate.now());
        DistOrderResp distOrderResp = new DistOrderResp();
        logger.info("请求TMS配送详情参数：{}",JSON.toJSONString(distOrderReq));
        DubboResponse<DistOrderResp> interceptResponse = tmsDistOrderQueryProvider.queryDetail(distOrderReq);
        if (Objects.nonNull(interceptResponse) && interceptResponse.getCode().equals(DubboResponse.COMMON_SUCCESS_CODE)){
            distOrderResp = interceptResponse.getData();
            logger.info("请求TMS配送详情返回：{}",JSON.toJSONString(distOrderResp));
        }else{
            throw new DefaultServiceException(interceptResponse.getMsg());
        }
        OrderDeliveryDetailDTO orderDeliveryDetailDTO = new OrderDeliveryDetailDTO();
        if (!CollectionUtils.isEmpty(distOrderResp.getDeliveryBatchList())){
            DeliveryBatchResp deliveryBatchResp = distOrderResp.getDeliveryBatchList().get(0);
            orderDeliveryDetailDTO.setDriver(deliveryBatchResp.getDriver());
            orderDeliveryDetailDTO.setDriverPhone(deliveryBatchResp.getDriverPhone());
        }
        if (!CollectionUtils.isEmpty(distOrderResp.getDeliverySiteList())){
            DeliverySiteResp deliverySiteResp = distOrderResp.getDeliverySiteList().get(0);
            orderDeliveryDetailDTO.setFinishTime(deliverySiteResp.getSignInTime());
            orderDeliveryDetailDTO.setStatus(deliverySiteResp.getSignInStatus());
            orderDeliveryDetailDTO.setSignInPic(mergePicture(deliverySiteResp));
            if (Objects.equals(deliverySiteResp.getSignInStatus(), 1)) {
                orderDeliveryDetailDTO.setSignInRemark(deliverySiteResp.getSignInRemark());
            }
        }

        List<OrderDefectInfo> defectInfos =  orderDefectInfoMapper.selectByorderNo(orderDeliveryQuery.getOrderNo(),orderDeliveryQuery.getContactId());
        List<OutStockSkuInfo> outStockSkuInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(defectInfos)){
            for (OrderDefectInfo defectInfo : defectInfos){
                OutStockSkuInfo outStockSkuInfo = new OutStockSkuInfo();
                outStockSkuInfo.setSku(defectInfo.getSku());
                outStockSkuInfo.setSkuName(defectInfo.getSkuName());
                outStockSkuInfo.setSkuPic(defectInfo.getSkuPic());
                outStockSkuInfo.setWeight(defectInfo.getWeight());
                outStockSkuInfo.setAmount(defectInfo.getAmount());
                outStockSkuInfo.setDefectAmount(defectInfo.getDefectAmount());
                outStockSkuInfoList.add(outStockSkuInfo);
            }
        }
        orderDeliveryDetailDTO.setOutStockSkuInfoList(outStockSkuInfoList);

        return CommonResult.ok(orderDeliveryDetailDTO);
    }

    @Override
    public CommonResult<MerchantTagListVO> queryBDMerchantTag() {
        List<Long> mIds = followUpRelationMapper.selectMidByBdId(getAdminId());
        if (mIds.isEmpty()) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "没有门店信息，请联系主管");
        }
        DataSynchronizationInformation dataTag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_ATTRIBUTE.getTableName());
        Integer dataFlag = Objects.isNull(dataTag) ? NumberUtils.INTEGER_ZERO : dataTag.getDateFlag();
        List<MerchantTagVO> lifecycleList = crmMerchantDayAttributeMapper.selectBDMerchantTag(mIds, dataFlag, MerchantTagEnum.Tag.LIFECYCLE.ordinal());
        List<MerchantTagVO> lifecycleTagList = new ArrayList<>();

        // 生命周期分类统计
        for (MerchantTagEnum.LifecycleTagType tag: MerchantTagEnum.LifecycleTagType.values()){
            int value = lifecycleList.stream().filter(m -> tag.getTag().contains(m.getName())).mapToInt(MerchantTagVO::getValue).sum();
            if (value == 0) {
                continue;
            }
            lifecycleTagList.add(createVO(tag.getDescription(),value));
        }
        List<MerchantTagVO> rValue = crmMerchantDayAttributeMapper.selectBDMerchantTag(mIds, dataFlag, MerchantTagEnum.Tag.R_VALUE.ordinal());
        List<MerchantTagVO> fValue = crmMerchantDayAttributeMapper.selectBDMerchantTag(mIds, dataFlag, MerchantTagEnum.Tag.F_VALUE.ordinal());
        List<MerchantTagVO> mValue = crmMerchantDayAttributeMapper.selectBDMerchantTag(mIds, dataFlag, MerchantTagEnum.Tag.M_VALUE.ordinal());
        MerchantTagListVO listVO = new MerchantTagListVO();
        listVO.setLifecycle(lifecycleTagList);
        listVO.setRValue(rValue);
        listVO.setFValue(fValue);
        listVO.setMValue(mValue);
        return CommonResult.ok(listVO);
    }

    @Override
    public CommonResult<MerchantVO> selectMerchantDetail(Long mId) {
        //Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        MerchantStoreAndExtendDTO merchantExtendDTO = merchantExtendsRepository.getAuthMerchantExtendDTO(mId,getCompatibleDataPermission());
        if (null == merchantExtendDTO) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"门店：[" + mId + "]不存在");
        }
        //MerchantVO query = new MerchantVO();
        /*query.setmId(mId);
        query.setIslock(merchant.getIslock());*/
        //MerchantVO test = merchantMapper.selectDetail(null);
        MerchantVO merchantVO = MerchantExtendConverter.convertMerchantVo(merchantExtendDTO);
        //补充信息
        Integer adminId = merchantVO.getAdminId();
        Admin admin = adminMapper.selectByPrimaryKey(adminId);
        if (Objects.nonNull(admin)) {
            merchantVO.setRealName(admin.getRealname());
            merchantVO.setContractMethod(admin.getContractMethod());
            merchantVO.setAdminType(admin.getAdminType());
            merchantVO.setNameRemakes(admin.getNameRemakes());
            merchantVO.setAdminId(admin.getAdminId());
        }
        Area area = areaMapper.selectByAreaNo(merchantVO.getAreaNo());
        if (Objects.nonNull(area)) {
            merchantVO.setLargeAreaNo(area.getLargeAreaNo());
            merchantVO.setAreaName(area.getAreaName());
            merchantVO.setMapSection(area.getMapSection());
        }
        //根据状态查询不同信息
        if (merchantVO.getIslock().equals(MerchantEnum.Status.AUDIT_SUCCESS.getCode()) || merchantVO.getIslock().equals(MerchantEnum.Status.PULL_BLACK.getCode())){
            FollowUpRelation followUpRelation = followUpRelationMapper.selectUnReassign(merchantVO.getmId());
            if (Objects.nonNull(followUpRelation)) {
                merchantVO.setAdminRealname(followUpRelation.getAdminName());
            }
        }else if (merchantVO.getIslock().equals(MerchantEnum.Status.IN_AUDIT.getCode()) || merchantVO.getIslock().equals(MerchantEnum.Status.AUDIT_FAIL.getCode())){
            MerchantStoreChangeLogResultResp merchantChangeLog =  merchantQueryFacade.getMerchantChangeLog(merchantVO.getStoreId());
            if (Objects.nonNull(merchantChangeLog)) {
                Admin inviterAdmin = adminMapper.selectByInviterChannelCode(merchantChangeLog.getInviterChannelCode());
                if (Objects.nonNull(inviterAdmin)){
                    merchantVO.setAdminRealname(inviterAdmin.getRealname());
                }
            }
        }

        // 门店主账户信息
        MerchantStoreAccountResultResp merchantAccount = merchantAccountQueryFacade.getPrimaryAccount(mId);
        if (merchantAccount != null) {
            merchantVO.setMcontact(merchantAccount.getAccountName());
            merchantVO.setPhone(merchantAccount.getPhone());
        }

        //查询用户来源
        MerchantStoreResultResp merchantStoreResultResp =  merchantQueryFacade.getMerchantSource(merchantVO.getStoreId());
        if (Objects.nonNull(merchantStoreResultResp)) {
            MerchantStoreAccountResultResp sourceMerchantAccount = merchantAccountQueryFacade.getPrimaryAccount(merchantStoreResultResp.getMId());
            if (sourceMerchantAccount != null) {
                merchantVO.setInviterPhone(merchantStoreResultResp.getStoreName() + "-" + sourceMerchantAccount.getPhone());
            }
        }

        //地址
        List<ContactDto> contactDtos = contactQueryFacade.getDefaultMerchantContact(merchantVO.getmId());
        List<Contact> contacts = ContactConverter.convertContactList(contactDtos);
        for(Contact contact: contacts){
                AreaQueryResp areaQueryResp = bdExtService.matchAreaNo(contact);
                if (areaQueryResp!=null){
                    contact.setArea(areaQueryResp.getAreaName());
                    contact.setAreaNo(areaQueryResp.getAreaNo());
                }
            contact.initAddrRemark();
        }
        merchantVO.setContacts(contacts);
        // 线索池
        MerchantCluePool queryPool = new MerchantCluePool();
        queryPool.setmId(mId);
        MerchantCluePool merchantCluePool = merchantCluePoolMapper.queryMerchantClue(queryPool);
        if (Objects.nonNull(merchantCluePool)) {
            merchantVO.setEsId(merchantCluePool.getEsId());
            merchantVO.setClueMName(merchantCluePool.getmName());
            merchantVO.setClueAddress(merchantCluePool.getAddress());
            merchantVO.setCluePhone(merchantCluePool.getPhone());
        }

       // 商户标签
        List<String> list = this.selectMerchantAllLabelByMid(mId);
        merchantVO.setMerchantLabelList(list);

        //地推人员信息
        FollowUpRelation upRelation = followUpRelationMapper.selectByMid(merchantVO.getmId().intValue());
        if (Objects.nonNull(upRelation)) {
            //todo 名称修改
            merchantVO.setAdminRealname(upRelation.getReassign() ? "默认邀请码" : upRelation.getAdminName());
        }
        return CommonResult.ok(merchantVO);
    }



    @Override
    public CommonResult<MerchantCluePool> selectMerchantPoolDetail(Long mId) {
        // 线索池
        MerchantCluePool queryPool = new MerchantCluePool();
        queryPool.setmId(mId);
        MerchantCluePool merchantCluePool = merchantCluePoolMapper.queryMerchantClue(queryPool);
        return CommonResult.ok(merchantCluePool);
    }

    @Override
    public List<MerchantRecentSpuDTO> queryRecentSpu(Long mId) {
        return ordersMapper.selectSixtyDaySpuByMerchant(mId).stream().map(
                it -> {
                    MerchantRecentSpuDTO vo = new MerchantRecentSpuDTO();
                    vo.setPdName(it);
                    return vo;
                }
        ).collect(Collectors.toList());
    }

    @Override
    public List<MerchantRecentSpuDTO> queryCannotApplyMscSpu(Long mId) {
        // 查询当前活跃期内的spu
        Map<Long, MscActiveSpuDTO> pdIdActivityMap = this.queryMscActiveSpu(mId)
                .stream().collect(Collectors.toMap(MscActiveSpuDTO::getPdId, Function.identity()));

        // 1. 查询30天内下单过的spu
        return ordersMapper.selectXDayPdIdByMId(mId, 30).stream()
                // 2. 过滤在活跃期内的spu
                .filter(pdId -> !pdIdActivityMap.containsKey(pdId.getPdId())).collect(Collectors.toList());
    }

    @Override
    @XmCache(prefixKey = CommonRedisKey.Cache.SHORT_TERM_LOST_SPU_KEY, key = "{mId}",
            cacheEmptyFlag = true, classType = ShortTermLostSpuDTO.class)
    public List<ShortTermLostSpuDTO> queryShortTermLostSpu(Long mId) {
        return ordersMapper.selectShortTermLostSpuByMerchant(mId, 6, 30);
    }

    @Override
    public List<MscActiveSpuDTO> queryMscActiveSpu(Long mId) {
        return merchantSituationActivityLogRepository.selectActiveSpu(mId);
    }

    @Override
    public List<AssignBdResultVo> batchClose(String file) {
        InputStream inputStream = OssGetUtil.getInputStream(file);
        List<CloseMerchantDTO> assignBdList = EasyExcel.read(inputStream).head(CloseMerchantDTO.class).sheet().doReadSync();
        IoUtil.close(inputStream);
        if (CollectionUtils.isEmpty(assignBdList)) {
            throw new BizException("内容为空，请核对后重试");
        }
        List<Integer> mIdList = assignBdList.stream().map(CloseMerchantDTO::getMId)
                .filter(Objects::nonNull).map(Math::toIntExact).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mIdList)) {
            throw new BizException("内容为空，请核对后重试");
        }
        List<AssignBdResultVo> resultVos = new ArrayList<>(mIdList.size());
        List<Merchant> merchants = merchantMapper.selectByMIdList(mIdList);
        List<Long> updateMids = new ArrayList<>(merchants.size());
        Map<Long, Merchant> merchantMap = merchants.stream().collect(Collectors.toMap(Merchant::getmId, Function.identity(), (entity1, entity2) -> entity1));
        //有待收货订单，以及
        mIdList.forEach(
                it -> {
                    Long mId = Long.valueOf(it);
                    Merchant merchant = merchantMap.get(mId);
                    if (merchant == null) {
                        resultVos.add(new AssignBdResultVo(mId, "门店不存在"));
                    } else if (Objects.equals(MerchantOperatingStateEnum.CLOSE_DOWN.getId(), merchant.getOperateStatus())) {
                        resultVos.add(new AssignBdResultVo(mId, "门店已经倒闭"));
                    } else if (ordersMapper.getUnfilledOrderByMid(merchant.getmId()) > 0) {
                        resultVos.add(new AssignBdResultVo(mId, "有待收货订单"));
                    } else if (ordersMapper.getAfterUnfilledOrderByMid(merchant.getmId()) > 0) {
                        resultVos.add(new AssignBdResultVo(mId, "有未完成的售后单"));
                    } else {
                        updateMids.add(mId);
                    }
                }
        );
        if (!CollectionUtils.isEmpty(updateMids)) {
            merchantMapper.updateOperatingStateByMids(updateMids, MerchantOperatingStateEnum.CLOSE_DOWN.getId());
        }
        return resultVos;
    }

    @Override
    public MerchantBusinessVO queryMerchantBusiness(Long mId) {
        if (mId == null) {
            return null;
        }
        List<MerchantQueryResp> merchantInfoList = merchantQueryFacade.queryMerchantInfoList(Collections.singletonList(mId));
        if (CollectionUtils.isEmpty(merchantInfoList)) {
            throw new BizException("查询客户信息失败");
        }
        MerchantQueryResp merchantQueryResp = merchantInfoList.get(0);
        MerchantBusinessVO merchantBusinessVO = new MerchantBusinessVO();
        merchantBusinessVO.setMId(mId);
        merchantBusinessVO.setSize(merchantQueryResp.getSize());
        boolean allowModify = true;
        if (merchantQueryResp.getAdminId() != null) {
            allowModify = !crmConfig.getMerchantBusinessAdminIdWhiteList().contains(merchantQueryResp.getAdminId().intValue());
        }
        merchantBusinessVO.setAllowModify(allowModify);
        merchantBusinessVO.setMainBusinessType(merchantQueryResp.getMainBusinessType());
        merchantBusinessVO.setSideBusinessTypeList(merchantQueryResp.getSideBusinessTypeList());
        merchantBusinessVO.setMerchantChainType(merchantQueryResp.getMerchantChainType());
        // 非白名单大客户下门店的连锁范围需要取大客户的连锁范围
        if (allowModify && MerchantSizeEnum.MAJOR_ACCOUNT.getValue().equals(merchantQueryResp.getSize()) && merchantQueryResp.getAdminId() != null) {
            AdminDTO adminDTO = adminQueryFacade.queryByAdminId(merchantQueryResp.getAdminId());
            if (adminDTO != null && adminDTO.getAdminChain() != null) {
                merchantBusinessVO.setMerchantChainType(adminDTO.getAdminChain());
            }
        }
        return merchantBusinessVO;
    }

    @Override
    public boolean upsertMerchantBusiness(MerchantBusinessUpsertDTO upsertDTO) {
        // 如果参数为空则更新失败
        if (upsertDTO == null || upsertDTO.getMId() == null || StringUtils.isEmpty(upsertDTO.getMainBusinessType())
                || CollectionUtils.isEmpty(upsertDTO.getSideBusinessTypeList()) || upsertDTO.getMerchantChainType() == null) {
            return false;
        }

        // 校验是否允许修改客户行业属性
        MerchantBusinessVO merchantBusinessVO = this.queryMerchantBusiness(upsertDTO.getMId());
        if (!Boolean.TRUE.equals(merchantBusinessVO.getAllowModify())) {
            throw new BizException("不允许修改该客户的行业属性");
        }
        
        // 更新客户行业属性
        merchantCommandFacade.updateMerchantBusiness(upsertDTO.getMId(), upsertDTO.getMainBusinessType(),
                upsertDTO.getSideBusinessTypeList(), upsertDTO.getMerchantChainType());

        // 更新成功则将客户行业属性打标任务置为完成
        crmJobMerchantDetailManager.completeDetailForMerchantBusiness(upsertDTO.getMId());
        return true;
    }

    public MerchantTagVO createVO(String name, Integer value){
        MerchantTagVO merchantTagVO = new MerchantTagVO();
        merchantTagVO.setName(name);
        merchantTagVO.setValue(value);
        return  merchantTagVO;
    }
    private String mergePicture(DeliverySiteResp deliverySiteResp){
        List<String> pictures = new ArrayList<>();
        String signInPics = deliverySiteResp.getSignInPics();
        if (StringUtils.isNotEmpty(signInPics)){
            pictures.addAll(Arrays.asList(signInPics.split(",")));
        }
        String signInSignPic = deliverySiteResp.getSignInSignPic();
        if (StringUtils.isNotEmpty(signInSignPic)){
            pictures.addAll(Arrays.asList(signInSignPic.split(",")));
        }
        String signInProductPic = deliverySiteResp.getSignInProductPic();
        if (StringUtils.isNotEmpty(signInProductPic)){
            pictures.addAll(Arrays.asList(signInProductPic.split(",")));
        }
        return pictures.stream().collect(Collectors.joining(","));
    }

    private void merge(Map<String, Object> openSeaMap) {
        merge(openSeaMap, false);
    }


    private void merge(Map<String, Object> openSeaMap, boolean care) {
        List<KeyCustomerVO> contactList = JSONObject.parseArray(((JSONArray) openSeaMap.get("data")).toJSONString(), KeyCustomerVO.class);
        if (CollectionUtils.isEmpty(contactList)) {
            return;
        }
        Map<Integer, String> nameMap;
        Map<Long, String> midTypeMap = new HashMap<>();
        List<Integer> bdIds = contactList.stream().map(KeyCustomerVO::getBdId).collect(Collectors.toList());
        List<Integer> careBIds = contactList.stream().map(KeyCustomerVO::getCareBdId).collect(Collectors.toList());
        bdIds.addAll(careBIds);
        List<Admin> admins = adminMapper.selectByIds(bdIds);
        nameMap = admins.stream().collect(Collectors.toMap(Admin::getAdminId, Admin::getRealname));
        List<Long> midNames = contactList.stream().map(it -> Long.valueOf(it.getmId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(midNames)) {
            List<MerchantStoreAndExtendResp> merchantExtendsByMid = merchantQueryFacade.getMerchantExtendsByMid(midNames);
            midTypeMap = merchantExtendsByMid.stream().filter(m->ObjectUtil.isNotNull(m.getBusinessType())).collect(Collectors.toMap(MerchantStoreAndExtendResp::getMId, MerchantStoreAndExtendResp::getBusinessType));
        }
        Map<Integer, String> finalNameMap = nameMap;
        Map<Long, String> finalMidTypeMap = midTypeMap;
        contactList.forEach(
                it -> {
                    String bdName = finalNameMap.get(it.getBdId());
                    if (!StringUtils.isEmpty(bdName)) {
                        if (care) {
                            it.setCareAdminRealName(bdName);
                        } else {
                            it.setAdminRealName(bdName);
                        }
                    }
                    it.setMainType(finalMidTypeMap.get(Long.valueOf(it.getmId())));
                }

        );
        openSeaMap.put("data", contactList);
    }

    @Override
    public CommonResult<PageInfo<CrmSkuMerchantGmvVO>> skuMerchantList(SkuMerchantQuery skuMerchantQuery) {
        Integer pageIndex = skuMerchantQuery.getPageIndex();
        Integer pageSize = skuMerchantQuery.getPageSize();
        PageInfo<CrmSkuMerchantGmvVO> merchantGmvVOPageInfo = new PageInfo<>();
        List<CrmSkuMerchantGmvVO> skuMerchantGmvVOList = new ArrayList<>();
        merchantGmvVOPageInfo.setList(skuMerchantGmvVOList);

        boolean isBdSa = super.isSA() || super.isSaleSA() || super.isAreaSA();

        // 非主管视角
        List<String> mIdList;
        if (!isBdSa || Objects.nonNull(skuMerchantQuery.getAdminId())) {
            mIdList = this.bdSkuMerchantList(skuMerchantQuery);
        } else {
            if (Objects.isNull(skuMerchantQuery.getAreaNo())) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "参数错误,主管必须选择区域查看");
            }
            // 主管视角
            mIdList = this.bdSaSkuMerchantList(skuMerchantQuery);
        }
        if (!mIdList.isEmpty()) {
            MerchantStoreQueryReq req = new MerchantStoreQueryReq();
            req.setMIds(mIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
            req.setProvince(skuMerchantQuery.getProvince());
            req.setCity(skuMerchantQuery.getCity());
            req.setArea(skuMerchantQuery.getArea());
            List<Long> merchantExtends = merchantQueryFacade.getMerchantIds(req);
            mIdList = merchantExtends.stream().map(String::valueOf).collect(Collectors.toList());
        }

        merchantGmvVOPageInfo.setTotal(mIdList.size());
        Set<String> mIdSetPage = mIdList.stream().skip((long) (pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(mIdSetPage)) {
            merchantGmvVOPageInfo.setIsLastPage(Boolean.TRUE);
            return CommonResult.ok(merchantGmvVOPageInfo);
        }

        SkuMerchantQueryInfoQuery skuMerchantQueryInfoQuery = new SkuMerchantQueryInfoQuery(skuMerchantQuery.getSku());
        for (String mId : mIdSetPage) {
            skuMerchantQueryInfoQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
            skuMerchantQueryInfoQuery.setEndTime(LocalDateTime.now());
            // 填充商户信息
            skuMerchantQueryInfoQuery.setmId(Long.valueOf(mId));
            CrmSkuMerchantGmvVO crmSkuMerchantGmvVO = this.fillMerchantInfo(skuMerchantQueryInfoQuery);
            skuMerchantGmvVOList.add(crmSkuMerchantGmvVO);
        }
        return CommonResult.ok(merchantGmvVOPageInfo);
    }

    /**
     * bd视角的sku商户下单信息
     *
     * @param skuMerchantQuery 查询条件
     * @return sku商户下单信息
     */
    private List<String> bdSkuMerchantList(SkuMerchantQuery skuMerchantQuery) {
        // 默认本月
        DataSynchronizationInformation name = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_SKU_BD_MONTH_MERCHANT.getTableName());
        Integer dateFlag = Objects.isNull(name) ? NumberUtils.INTEGER_ZERO : name.getDateFlag();
        skuMerchantQuery.setMonthTag(dateFlag);

        if (Objects.isNull(skuMerchantQuery.getAdminId())) {
            skuMerchantQuery.setAdminId(getAdminId());
        }

        Integer skuType = skuMerchantQuery.getMerchantSkuType();
        if (Objects.nonNull(skuType)) {
            // 填充上月表标识
            this.dealWithDateTag(skuMerchantQuery, dateFlag);
        }

        List<String> mIdList = this.getBdMidList(skuMerchantQuery);

        // 本月新开, 本月选购商户 - 上月选购商户
        if (Objects.nonNull(skuType) && Objects.equals(MerchantSkuEnum.Type.NEWLY_OPENED_THIS_MONTH.ordinal(), skuType)) {
            skuMerchantQuery.setMonthTag(dateFlag);
            List<String> thisIds = this.getBdMidList(skuMerchantQuery);
            thisIds.removeAll(mIdList);
            mIdList = new ArrayList<>(thisIds);
        }
        return mIdList;
    }

    /**
     * 获取商户sku购买gmv信息
     *
     * @param skuMerchantQueryInfoQuery 查询条件
     * @return gmv信息
     */
    private CrmSkuMerchantGmvVO fillMerchantInfo(SkuMerchantQueryInfoQuery skuMerchantQueryInfoQuery) {
        // 本月
        CrmSkuMerchantGmvVO crmSkuMerchantGmvVO = merchantMapper.selectSkuGmvByMid(skuMerchantQueryInfoQuery);

        MerchantStoreAndExtendDTO merchantExtendDTO = merchantExtendsRepository.getAuthMerchantExtendDTO(skuMerchantQueryInfoQuery.getmId(), null);
        merchantExtendDTO = Optional.ofNullable(merchantExtendDTO).orElse(new MerchantStoreAndExtendDTO());
        crmSkuMerchantGmvVO.setMerchantId(skuMerchantQueryInfoQuery.getmId());
        crmSkuMerchantGmvVO.setMerchantName(merchantExtendDTO.getStoreName());
        if (Objects.nonNull(merchantExtendDTO.getLastOrderTime())) {
            crmSkuMerchantGmvVO.setLastOrderTime(BaseDateUtils.date2LocalDateTime(merchantExtendDTO.getLastOrderTime()));
        }
        // 上月
        skuMerchantQueryInfoQuery.setEndTime(skuMerchantQueryInfoQuery.getStartTime());
        skuMerchantQueryInfoQuery.setStartTime(skuMerchantQueryInfoQuery.getStartTime().minusMonths(NumberUtils.INTEGER_ONE));
        CrmSkuMerchantGmvVO lastSkuMerchantGmvVO = merchantMapper.selectSkuGmvByMid(skuMerchantQueryInfoQuery);

        BigDecimal gmvSub = crmSkuMerchantGmvVO.getGmv().subtract(lastSkuMerchantGmvVO.getGmv());
        BigDecimal gmvRingRatio = BigDecimal.ONE;
        if (BigDecimal.ZERO.compareTo(lastSkuMerchantGmvVO.getGmv()) < 0) {
            gmvRingRatio = gmvSub.divide(lastSkuMerchantGmvVO.getGmv(), 2, RoundingMode.HALF_UP);
        }
        crmSkuMerchantGmvVO.setGmvRingRatio(gmvRingRatio);

        int salesVolumeSub = crmSkuMerchantGmvVO.getSalesVolume() - lastSkuMerchantGmvVO.getSalesVolume();
        BigDecimal salesVolumeRingRatio = BigDecimal.ONE;
        if (lastSkuMerchantGmvVO.getSalesVolume() > 0) {
            salesVolumeRingRatio = BigDecimal.valueOf(salesVolumeSub).divide(BigDecimal.valueOf(lastSkuMerchantGmvVO.getSalesVolume()), 2, RoundingMode.HALF_UP);
        }
        crmSkuMerchantGmvVO.setSalesVolumeRingRatio(salesVolumeRingRatio);

        int merchantNumSub = crmSkuMerchantGmvVO.getMerchantNum() - lastSkuMerchantGmvVO.getMerchantNum();
        BigDecimal merchantNumRingRatio = BigDecimal.ONE;
        if (lastSkuMerchantGmvVO.getMerchantNum() > 0) {
            merchantNumRingRatio = BigDecimal.valueOf(merchantNumSub).divide(BigDecimal.valueOf(lastSkuMerchantGmvVO.getMerchantNum()), 2, RoundingMode.HALF_UP);
        }
        crmSkuMerchantGmvVO.setMerchantNumRingRatio(merchantNumRingRatio);

        return crmSkuMerchantGmvVO;
    }

    /**
     * 主管视角的sku商户下单信息
     *
     * @param skuMerchantQuery 查询条件
     * @return sku商户下单信息
     */
    private List<String> bdSaSkuMerchantList(SkuMerchantQuery skuMerchantQuery) {
        // 默认本月
        DataSynchronizationInformation name = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_SKU_AREA_MONTH_MERCHANT.getTableName());
        Integer dateFlag = Objects.isNull(name) ? NumberUtils.INTEGER_ZERO : name.getDateFlag();
        skuMerchantQuery.setMonthTag(dateFlag);

        Integer skuType = skuMerchantQuery.getMerchantSkuType();
        if (Objects.nonNull(skuType)) {
            // 填充上月表标识
            this.dealWithDateTag(skuMerchantQuery, dateFlag);
        }

        List<String> mIdList = this.getSaMidList(skuMerchantQuery);

        // 本月新开, 本月选购商户 - 上月选购商户
        if (Objects.nonNull(skuType) && Objects.equals(MerchantSkuEnum.Type.NEWLY_OPENED_THIS_MONTH.ordinal(), skuType)) {
            skuMerchantQuery.setMonthTag(dateFlag);
            List<String> thisIds = this.getSaMidList(skuMerchantQuery);
            thisIds.removeAll(mIdList);
            mIdList = new ArrayList<>(thisIds);
        }
        return mIdList;
    }

    private void dealWithDateTag(SkuMerchantQuery skuMerchantQuery, Integer dateFlag) {
        // 页面传输月份
        int time = dateFlag * 100 + 1;
        LocalDate parse = LocalDate.parse(Integer.toString(time), DateTimeFormatter.ofPattern(BaseDateUtils.MID_DATE_FORMAT));
        LocalDate monthEnd = parse.minusMonths(DateUtils.ONE_DATE);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(BaseDateUtils.MID_DATE_FORMAT);
        Integer lastMonth = (Integer.parseInt(monthEnd.format(fmt)) - 1) / 100;
        skuMerchantQuery.setMonthTag(lastMonth);
    }

    @NotNull
    private List<String> getSaMidList(SkuMerchantQuery skuMerchantQuery) {
        CrmSkuAreaMonthMerchant thisMonthMerchant = crmSkuAreaMonthMerchantMapper.selectByQuery(skuMerchantQuery);
        if (Objects.isNull(thisMonthMerchant)) {
            return Collections.emptyList();
        }
        return dealWithMerchantIdText(thisMonthMerchant.getMerchantIdText());
    }

    @NotNull
    private List<String> getBdMidList(SkuMerchantQuery skuMerchantQuery) {
        CrmSkuBdMonthMerchant crmSkuBdMonthMerchant = crmSkuBdMonthMerchantMapper.selectByQuery(skuMerchantQuery);
        if (Objects.isNull(crmSkuBdMonthMerchant)) {
            return Collections.emptyList();
        }
        return dealWithMerchantIdText(crmSkuBdMonthMerchant.getMerchantIdText());
    }

    @NotNull
    private static List<String> dealWithMerchantIdText(String thisMerchantIdText) {
        if (StringUtils.isEmpty(thisMerchantIdText)) {
            return Collections.emptyList();
        }
        String[] thisMerchantIds = thisMerchantIdText.split(CrmGlobalConstant.SEPARATING_SYMBOL);
        // 数组转集合,需要针对集合操作的话, 需要转类型
        List<String> list = Arrays.asList(thisMerchantIds);
        return new ArrayList<>(list);
    }

    @Override
    public CommonResult<CrmMerchantMallSearchTopVO> mallRecord(MerchantDetailQuery merchantDetailQuery) {
        DataSynchronizationInformation name = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_MALL_SEARCH_TOP.getTableName());
        Integer dateFlag = Objects.isNull(name) ? NumberUtils.INTEGER_ZERO : name.getDateFlag();
        merchantDetailQuery.setDayTag(dateFlag);

        CrmMerchantMallSearchTopVO crmMerchantMallSearchTopVO = new CrmMerchantMallSearchTopVO();
        List<CrmMerchantMallSearchTop> mallSearchTops = crmMerchantMallSearchTopMapper.selectByQuery(merchantDetailQuery);
        crmMerchantMallSearchTopVO.setCrmMerchantMallSearchTopList(mallSearchTops);
//        MerchantStoreAndExtendDTO merchantExtendDTO = merchantExtendsRepository.getAuthMerchantExtendDTO(merchantDetailQuery.getMerchantId(), null);
//        if (Objects.nonNull(merchantExtendDTO) && Objects.nonNull(merchantExtendDTO.getLoginTime())) {
//            crmMerchantMallSearchTopVO.setLastLoginTime(BaseDateUtils.date2LocalDateTime(merchantExtendDTO.getLoginTime()));
//        }
        List<MerchantStoreAccountResultResp> primaryAccountList = merchantAccountQueryFacade.getMerchantAccount(merchantDetailQuery.getMerchantId());
        if (!CollectionUtil.isEmpty(primaryAccountList)){
            LocalDateTime localDateTime = primaryAccountList.stream().map(MerchantStoreAccountResultResp::getLastLoginTime).filter(Objects::nonNull).sorted((Comparator.reverseOrder())).findFirst().orElse(null);
            crmMerchantMallSearchTopVO.setLastLoginTime(localDateTime);

        }

        ShoppingCartReq req = new ShoppingCartReq();
        req.setMId(merchantDetailQuery.getMerchantId());
        DubboResponse<List<ShoppingCartResp>> shoppingCart = shoppingCartProvider.getShoppingCart(req);
        if (!shoppingCart.isSuccess()) {
            throw new BizException(shoppingCart.getMsg());
        }
        List<TrolleyDTO> trolleyDTOList = shoppingCart.getData().stream().map(TrolleyDTO::toTrolley).collect(Collectors.toList());
        crmMerchantMallSearchTopVO.setTrolleyList(trolleyDTOList);


        return CommonResult.ok(crmMerchantMallSearchTopVO);
    }

    @Override
    public CommonResult<CrmMerchantGmvVO> gmvInfo(MerchantDetailQuery merchantDetailQuery) {
        DataSynchronizationInformation name = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_TODAY_GMV.getTableName());
        Integer dateFlag = Objects.isNull(name) ? NumberUtils.INTEGER_ZERO : name.getDateFlag();
        merchantDetailQuery.setDayTag(dateFlag);

        CrmMerchantGmvVO crmMerchantGmvVO = new CrmMerchantGmvVO();
        CrmMerchantTodayGmv crmMerchantTodayGmv = crmMerchantTodayGmvMapper.selectByQuery(merchantDetailQuery);
        crmMerchantGmvVO.setCrmMerchantTodayGmv(crmMerchantTodayGmv);

        CrmMerchantMonthGmvDTO crmMerchantMonthGmvDTO = this.getMerchantThisMonthGmv(merchantDetailQuery);
        crmMerchantGmvVO.setCrmMerchantMonthGmvDTO(crmMerchantMonthGmvDTO);

        return CommonResult.ok(crmMerchantGmvVO);
    }

    /**
     * 获取商户本月gmv信息
     *
     * @param merchantDetailQuery 查询条件
     * @return 本月gmv信息
     */
    private CrmMerchantMonthGmvDTO getMerchantThisMonthGmv(MerchantDetailQuery merchantDetailQuery) {
        CrmMerchantMonthGmvDTO crmMerchantMonthGmvDTO = new CrmMerchantMonthGmvDTO();
        // 本月gmv 本月配送客单价
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_GMV.getTableName());
        Integer dataFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();

        PrivateSeaVO mGmv = crmMerchantDayGmvMapper.selectByMid(merchantDetailQuery.getMerchantId(), dataFlag);
        if (Objects.nonNull(mGmv)) {
            BeanUtils.copyProperties(mGmv,crmMerchantMonthGmvDTO);
            if(StringUtils.isNotEmpty(mGmv.getBrowsingHistory())){
                List<BrowsingHistoryVo> browsingHistoryVos = JSONUtil.toList(mGmv.getBrowsingHistory(), BrowsingHistoryVo.class);
                crmMerchantMonthGmvDTO.setBrowsingHistory(browsingHistoryVos);
            }
            crmMerchantMonthGmvDTO.setThisMonthGmv(mGmv.getThisMonthGmv());
            crmMerchantMonthGmvDTO.setThisMonthDeliveryUnitPrice(mGmv.getThisMonthDeliveryUnitPrice());
            crmMerchantMonthGmvDTO.setThisDistributionGmv(mGmv.getDistributionGmv());
            crmMerchantMonthGmvDTO.setCoreMerchantTag(mGmv.getCoreMerchantTag());
            crmMerchantMonthGmvDTO.setDistributionAmount(mGmv.getDistributionAmount());
        }

        // 跟进城市取核心客户的阈值
        MerchantStoreAndExtendResp merchantVO = merchantQueryFacade.getMerchantExtendsByMid(merchantDetailQuery.getMerchantId());
        Integer areaNo = Objects.isNull(merchantVO) ? NumberUtils.INTEGER_ZERO : merchantVO.getAreaNo();
        CrmCommissionMerchantLevel crmCommissionMerchantLevel = crmCommissionMerchantLevelMapper.selectCoreMerchantByAreaNo(areaNo);
        if (Objects.nonNull(crmCommissionMerchantLevel)) {
            crmMerchantMonthGmvDTO.setGmvThreshold(crmCommissionMerchantLevel.getGmvMinimum());
            crmMerchantMonthGmvDTO.setPriceThreshold(crmCommissionMerchantLevel.getPriceMinimum());
        }

        // 上月gmv,配送客单价,配送gmv
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_MONTH_GMV.getTableName());
        Integer lastDataFlag = Objects.isNull(lastMonth) ? NumberUtils.INTEGER_ZERO : lastMonth.getDateFlag();
        PrivateSeaVO lastGmv = crmMerchantMonthGmvMapper.selectByMid(merchantDetailQuery.getMerchantId(), lastDataFlag);
        if (Objects.nonNull(lastGmv)) {
            crmMerchantMonthGmvDTO.setLastMonthGmv(lastGmv.getThisMonthGmv());
            crmMerchantMonthGmvDTO.setLastMonthDeliveryUnitPrice(lastGmv.getThisMonthDeliveryUnitPrice());
            crmMerchantMonthGmvDTO.setLastDistributionGmv(lastGmv.getDistributionGmv());
        }

        return crmMerchantMonthGmvDTO;
    }


    @Override
    public CommonResult<PageInfo<CrmSkuMonthGmvVO>> merchantProduct(MerchantDetailQuery merchantDetailQuery) {
        LocalDateTime startTime = DateUtils.getAtBeginningOfMonth();
        LocalDateTime endTime = LocalDateTime.now();

        // 本月下单spu, 单个客户最多不超过200个, 放内存中运算
        merchantDetailQuery.setStartTime(startTime);
        merchantDetailQuery.setEndTime(endTime);
        List<CrmSkuMonthGmvVO> thisMonthList = productsRepository.selectProductByQuery(merchantDetailQuery);

        // 上月下单spu
        merchantDetailQuery.setStartTime(startTime.minusMonths(NumberUtils.INTEGER_ONE));
        merchantDetailQuery.setEndTime(startTime);
        List<CrmSkuMonthGmvVO> lastMonthList = productsRepository.selectProductByQuery(merchantDetailQuery);

        PageInfo<CrmSkuMonthGmvVO> monthGmvVOPageInfo = new PageInfo<>();
        this.getMerchantOrderTimeTypeList(merchantDetailQuery, monthGmvVOPageInfo, thisMonthList, lastMonthList);

        return CommonResult.ok(monthGmvVOPageInfo);
    }

    private void getMerchantOrderTimeTypeList(MerchantDetailQuery merchantDetailQuery, PageInfo<CrmSkuMonthGmvVO> monthGmvVOPageInfo,
                                              List<CrmSkuMonthGmvVO> thisMonthList, List<CrmSkuMonthGmvVO> lastMonthList) {
        Integer pageIndex = merchantDetailQuery.getPageIndex();
        Integer pageSize = merchantDetailQuery.getPageSize();
        monthGmvVOPageInfo.setIsLastPage(Boolean.FALSE);

        List<String> thisPdNameList = thisMonthList.stream().map(CrmSkuMonthGmvVO::getPdName).collect(Collectors.toList());
        List<String> lastPdNameList = lastMonthList.stream().map(CrmSkuMonthGmvVO::getPdName).collect(Collectors.toList());

        if (Objects.equals(MerchantSkuEnum.Type.NEWLY_OPENED_THIS_MONTH.ordinal(), merchantDetailQuery.getMerchantOrderTimeType())) {
            monthGmvVOPageInfo.setTotal(thisPdNameList.size());
            thisMonthList = thisMonthList.stream().skip((long) (pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            for (CrmSkuMonthGmvVO crmSkuMonthGmvVO : thisMonthList) {
                crmSkuMonthGmvVO.setOrderTag(MerchantSkuEnum.Type.PLACE_AN_ORDER_LAST_MONTH.ordinal());
                if (lastPdNameList.contains(crmSkuMonthGmvVO.getPdName())) {
                    crmSkuMonthGmvVO.setOrderTag(MerchantSkuEnum.Type.ALL.ordinal());
                }
            }
            if (CollectionUtil.isEmpty(thisMonthList) || thisMonthList.size() < pageSize) {
                monthGmvVOPageInfo.setIsLastPage(Boolean.TRUE);
            }
            monthGmvVOPageInfo.setList(thisMonthList);
        }

        if (Objects.equals(MerchantSkuEnum.Type.PLACE_AN_ORDER_LAST_MONTH.ordinal(), merchantDetailQuery.getMerchantOrderTimeType())) {
            monthGmvVOPageInfo.setTotal(lastPdNameList.size());
            lastMonthList = lastMonthList.stream().skip((long) (pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            for (CrmSkuMonthGmvVO crmSkuMonthGmvVO : lastMonthList) {
                crmSkuMonthGmvVO.setOrderTag(MerchantSkuEnum.Type.NEWLY_OPENED_THIS_MONTH.ordinal());
                if (thisPdNameList.contains(crmSkuMonthGmvVO.getPdName())) {
                    crmSkuMonthGmvVO.setOrderTag(MerchantSkuEnum.Type.ALL.ordinal());
                }
            }
            if (CollectionUtil.isEmpty(lastMonthList) || lastMonthList.size() < pageSize) {
                monthGmvVOPageInfo.setIsLastPage(Boolean.TRUE);
            }
            monthGmvVOPageInfo.setList(lastMonthList);
        }
    }

    @Override
    public CommonResult<CustomerAnalysisVO> detailCategory(MerchantDetailQuery merchantDetailQuery) {
        merchantDetailQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        merchantDetailQuery.setEndTime(LocalDateTime.now());
        CustomerAnalysisVO customerAnalysisVO = new CustomerAnalysisVO();

        // 当前商户下单top10
        List<CustomerAnalysisDTO> merchantTopPdList = productsRepository.selectCategoryTop(merchantDetailQuery);
        customerAnalysisVO.setMerchantOrderPdList(merchantTopPdList);

        // 同区域同行业下单类目top10
        List<CustomerAnalysisDTO> typeAndAreaOrderPdList = this.getAreaAndTypeCategoryTop(merchantDetailQuery, merchantTopPdList);
        customerAnalysisVO.setTypeAndAreaOrderPdList(typeAndAreaOrderPdList);

        return CommonResult.ok(customerAnalysisVO);
    }

    @Override
    public CommonResult<CustomerAnalysisVO> detailSpu(MerchantDetailQuery merchantDetailQuery) {
        CrmMerchantAreaTypeTop crmMerchantAreaTypeTop = this.getCrmMerchantAreaTypeTop(merchantDetailQuery);
        if (Objects.isNull(crmMerchantAreaTypeTop)) {
            return CommonResult.ok();
        }

        String pdIdListStr = crmMerchantAreaTypeTop.getPdIdList();
        String[] pdIdList = pdIdListStr.split(CrmGlobalConstant.SEPARATING_SYMBOL);

        SkuMerchantQuery skuMerchantQuery = new SkuMerchantQuery();
        List<CustomerAnalysisDTO> customerAnalysisDTOList = new ArrayList<>();
        merchantDetailQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        merchantDetailQuery.setEndTime(LocalDateTime.now());
        List<CrmSkuMonthGmvVO> productList = productsRepository.selectProductByQuery(merchantDetailQuery);
        Set<String> pdNameSet = productList.stream().map(CrmSkuMonthGmvVO::getPdName).collect(Collectors.toSet());

        if (pdIdList.length > 0){
            List<Long> pdIds = Arrays.stream(pdIdList).map(Long::valueOf).collect(Collectors.toList());
            skuMerchantQuery.setPdIds(pdIds);
            List<CrmSkuMonthGmvVO> crmSkuMonthGmvVOS = productsRepository.selectByQuery(skuMerchantQuery);
            Map<Long, List<CrmSkuMonthGmvVO>> collect = crmSkuMonthGmvVOS.stream().collect(Collectors.groupingBy(CrmSkuMonthGmvVO::getPdId));
            for (Long pdId : pdIds) {
                List<CrmSkuMonthGmvVO> crmpdIdSkuMonthGmv = collect.get(pdId);
                if (CollectionUtil.isNotEmpty(crmpdIdSkuMonthGmv)) {
                    String pdName = crmpdIdSkuMonthGmv.get(NumberUtils.INTEGER_ZERO).getPdName();
                    CustomerAnalysisDTO customerAnalysisDTO = new CustomerAnalysisDTO();
                    customerAnalysisDTO.setPdId(pdId);
                    customerAnalysisDTO.setPdName(pdName);
                    customerAnalysisDTO.setPurchase(Boolean.FALSE);
                    if (pdNameSet.contains(pdName)) {
                        customerAnalysisDTO.setPurchase(Boolean.TRUE);
                    }
                    customerAnalysisDTOList.add(customerAnalysisDTO);
                }
            }
        }
        CustomerAnalysisVO customerAnalysisVO = new CustomerAnalysisVO();
        customerAnalysisVO.setTypeAndAreaOrderPdList(customerAnalysisDTOList);
        return CommonResult.ok(customerAnalysisVO);
    }

    private List<CustomerAnalysisDTO> getAreaAndTypeCategoryTop(MerchantDetailQuery merchantDetailQuery, List<CustomerAnalysisDTO> merchantTopPdList) {
        CrmMerchantAreaTypeTop crmMerchantAreaTypeTop = this.getCrmMerchantAreaTypeTop(merchantDetailQuery);
        if (Objects.isNull(crmMerchantAreaTypeTop)) {
            return Collections.emptyList();
        }

        Set<String> categoryNameSet = merchantTopPdList.stream().map(CustomerAnalysisDTO::getCategoryName).collect(Collectors.toSet());

        String categoryIdListStr = crmMerchantAreaTypeTop.getCategoryIdList();
        String[] categoryIdList = categoryIdListStr.split(CrmGlobalConstant.SEPARATING_SYMBOL);

        List<CustomerAnalysisDTO> customerAnalysisDTOList = new ArrayList<>();

        List<Long> categoryIds = Arrays.stream(categoryIdList).map(Long::valueOf).collect(Collectors.toList());
        Map<Long, String> collect = productsRepository.selectByCategoryQuery(categoryIds).stream().collect(Collectors.toMap(CategoryResp::getId, CategoryResp::getName));

        for (String categoryId : categoryIdList) {
            String categoryName = collect.get(Long.valueOf(categoryId));
            CustomerAnalysisDTO customerAnalysisDTO = new CustomerAnalysisDTO();
            customerAnalysisDTO.setCategoryId(Long.valueOf(categoryId));
            customerAnalysisDTO.setCategoryName(categoryName);
            customerAnalysisDTO.setPurchase(Boolean.FALSE);
            if (categoryNameSet.contains(categoryName)) {
                customerAnalysisDTO.setPurchase(Boolean.TRUE);
            }
            customerAnalysisDTOList.add(customerAnalysisDTO);
        }

        return customerAnalysisDTOList;
    }

    private CrmMerchantAreaTypeTop getCrmMerchantAreaTypeTop(MerchantDetailQuery merchantDetailQuery) {
        DataSynchronizationInformation name = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_AREA_TYPE_TOP.getTableName());
        Integer dateFlag = Objects.isNull(name) ? NumberUtils.INTEGER_ZERO : name.getDateFlag();
        merchantDetailQuery.setMonthTag(dateFlag);

        MerchantStoreAndExtendResp merchantExtendsByMid = merchantQueryFacade.getMerchantExtendsByMid(merchantDetailQuery.getMerchantId());
        merchantExtendsByMid = Optional.ofNullable(merchantExtendsByMid).orElse(new MerchantStoreAndExtendResp());
        merchantDetailQuery.setType(merchantExtendsByMid.getBusinessType());
        merchantDetailQuery.setAreaNo(merchantExtendsByMid.getAreaNo());

        return crmMerchantAreaTypeTopMapper.selectByQuery(merchantDetailQuery);
    }



    private boolean isBd(){
        return !(super.isSA() || super.isSaleSA() || super.isAreaSA());
    }
}
