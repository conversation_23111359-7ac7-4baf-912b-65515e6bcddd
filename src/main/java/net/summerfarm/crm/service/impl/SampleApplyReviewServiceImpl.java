package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.AddressSnapshotTypeEnum;
import net.summerfarm.crm.enums.ExamineEnum;
import net.summerfarm.crm.enums.SampleApplyStatusEnum;
import net.summerfarm.crm.facade.MallSkuQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.SampleApplyReviewDTO;
import net.summerfarm.crm.model.query.SampleApplyQuery;
import net.summerfarm.crm.model.vo.SampleApplyReviewVO;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import net.summerfarm.crm.model.vo.sample.CheckSampleDto;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.util.Producer;
import net.summerfarm.crm.service.SampleApplyReviewService;
import net.summerfarm.mall.client.resp.MerchantSkuSpuOrderResp;
import net.summerfarm.mall.client.resp.SkuSpuOrderDetailResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Service
@Slf4j
public class SampleApplyReviewServiceImpl extends BaseService implements SampleApplyReviewService {

    private static final Logger logger = LoggerFactory.getLogger(SampleApplyReviewServiceImpl.class);

    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;
    @Resource
    private SampleApplyMapper sampleApplyMapper;
    @Resource
    private SampleSkuMapper sampleSkuMapper;
    @Resource
    private Producer producer;
    @Resource
    OrdersMapper ordersMapper;

    @Resource
    DeliveryPlanRemarkSnapshotMapper deliveryPlanRemarkSnapshotMapper;

    @Resource
    MallSkuQueryFacade mallSkuQueryFacade;
    @NacosValue(value = "${riskAdminds:}",autoRefreshed = true)
    private List<Integer> riskAdminIds;

    @Override
    public AjaxResult selectSampleApplyReview(int pageIndex, int pageSize, SampleApply sampleApply) {
        // BD只能看到自己的申请
        boolean isBd = isBD() && !(isSaleSA() || isAreaSA() || isSA());
        if(isBd){
            sampleApply.setBdId(getAdminId());
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<SampleApplyReviewVO> sampleApplyList = sampleApplyReviewMapper.selectByBdIds(sampleApply.getmName()
                ,sampleApply.getStatus(),sampleApply.getAreaNo(),sampleApply.getBdId());
        // 获取商品信息
        for (SampleApplyReviewVO sampleApplyReviewVO : sampleApplyList) {
            List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleApplyReviewVO.getSampleId());
            sampleApplyReviewVO.setSampleSkuList(sampleSkus);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(sampleApplyList));
    }

    @Override
    public AjaxResult selectSampleApplyReviewVO(Integer sampleApplyId) {
        if(ObjectUtil.isNull(sampleApplyId)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        SampleApplyReviewVO sampleApplyReviewVO = sampleApplyReviewMapper.selectSampleApplyReviewVOById(sampleApplyId);
        List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleApplyReviewVO.getSampleId());
        sampleApplyReviewVO.setSampleSkuList(sampleSkus);
        List<DeliveryPlanRemarkSnapshot> deliveryPlanRemarkSnapshots = deliveryPlanRemarkSnapshotMapper.selectByTypeBusinessIds(AddressSnapshotTypeEnum.SnapshotType.SAMPLE_APPLY.ordinal(), Collections.singletonList(sampleApplyId.toString()));
        if (!CollectionUtil.isEmpty(deliveryPlanRemarkSnapshots)){
            sampleApplyReviewVO.setAddressRemark(deliveryPlanRemarkSnapshots.get(0).getAddressRemark());
        }
        return AjaxResult.getOK(sampleApplyReviewVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult sampleApplyReview(SampleApplyReview sampleApplyReview){
        if(ObjectUtil.isNull(sampleApplyReview)){
            return AjaxResult.getErrorWithMsg("无审核信息");
        }
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleApplyReview.getSampleId());
        if (sampleApply == null){
            return AjaxResult.getError("样品申请不存在");
        }
        if (sampleApply.getRiskLevel()!=null && Objects.equals(sampleApply.getRiskLevel(), ExamineEnum.RISK_LEVER.RISK.ordinal())){
            if (!riskAdminIds.contains(getAdminId())){
                return AjaxResult.getError("FENGKONG","风控申请单需风控人员审批～");
            }
        }
        SampleApplyReview review = sampleApplyReviewMapper.isReview(sampleApplyReview.getSampleId(), null);
        if(Objects.nonNull(review)){
            return AjaxResult.getError("该样品申请已进行过审核操作,请稍后到样品审核列表查看");
        }



        // 更新数据
        sampleApplyReview.setReviewId(getAdminId());
        sampleApplyReview.setReviewName(getAdminName());
        sampleApplyReview.setAuditTime(new Date());

        // 不通过、储存不通过的信息，关闭样品申请
        if(Objects.equals(sampleApplyReview.getStatus(), ExamineEnum.SampleApplyReview.CANCEL.ordinal())){
            sampleApplyReviewMapper.insertSelective(sampleApplyReview);
            sampleApplyMapper.closeSampleApply(sampleApplyReview.getSampleId());
            return AjaxResult.getOK();
        }

        // 查询样品申请的所有信息
        SampleApplyReviewVO sampleApplyReviewVO = sampleApplyReviewMapper.selectSampleApplyReviewVOById(sampleApplyReview.getSampleId());

        if(Objects.isNull(sampleApplyReviewVO)){
            return AjaxResult.getErrorWithMsg("该样品申请单不存在,请确认后操作");
        }

        // 结果过滤
        SampleApplyReviewDTO sampleApplyReviewDTO = new SampleApplyReviewDTO();
        BeanUtils.copyProperties(sampleApplyReviewVO,sampleApplyReviewDTO);
        // 获取sku信息
        List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleApplyReviewVO.getSampleId());
        sampleApplyReviewDTO.setSampleSkuList(sampleSkus);

        // 消息机制,冻结库存
        this.frozenInventory(sampleApplyReviewDTO,sampleApplyReview);
        return AjaxResult.getOK();
    }

    private void frozenInventory(SampleApplyReviewDTO sampleApplyReviewDTO,SampleApplyReview sampleApplyReview){
        MqData mqData = new MqData();
        mqData.setType(MessageType.FROZEN_INVENTORY);
        mqData.setBusiness(MessageBusiness.STOCK);
        JSONObject msgJson = new JSONObject();
        msgJson.put("sampleApplyReviewDTO",sampleApplyReviewDTO);
        msgJson.put("sampleApplyReview",sampleApplyReview);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendMessageInTransaction(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST, mqData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult frozenInventory(JSONObject jsonObject) {
        logger.info("开始执行冻结库存本地事务,message:{}", jsonObject.toJSONString());
        JSONObject data = jsonObject.getJSONObject("data");
        SampleApplyReviewDTO sampleApplyReviewDTO = data.getObject("sampleApplyReviewDTO", SampleApplyReviewDTO.class);
        SampleApplyReview sampleApplyReview = data.getObject("sampleApplyReview", SampleApplyReview.class);
        // 已经审核已通过，库存信息充足，储存样品申请审核信息
        sampleApplyReviewMapper.insertSelective(sampleApplyReview);
        // 将样品申请信息更新
        sampleApplyReviewDTO.setUpdateTime(new Date());
        sampleApplyReviewDTO.setStatus(SampleApplyStatusEnum.WAIT_HANDLE.getId());
        SampleApply sampleApply = new SampleApply();
        sampleApply.setStatus(SampleApplyStatusEnum.WAIT_HANDLE.getId());
        sampleApply.setSampleId(sampleApplyReviewDTO.getSampleId());
        sampleApply.setUpdateTime(new Date());
        //BeanUtils.copyProperties(sampleApplyReviewDTO,sampleApply);
        sampleApplyMapper.updateSampleApply(sampleApply);
        //执行成功，可以提交事务
        return AjaxResult.getOK();
    }

    @Override
    public CheckSampleDto checkMerchant(SampleApplyQuery query) {
        //判断是新店 新店直接放过
        Orders orders = ordersMapper.getOverOrder(query.getMid());
        int count = sampleApplyMapper.selectOverCount(query.getMid());
        logger.info("checkMerchant 门店 {} count {} ",query.getMid(),count);

        if (orders == null){
            if (count == 0){
                logger.info("checkMerchant 新店且没有样品 可以通过 mid:{} count {}" , query.getMid() ,count);
                return new CheckSampleDto(true,"");
            }else {
                logger.info("checkMerchant 新店且没有样品 不可以通过 mid:{} count {}" , query.getMid() ,count);
                return new CheckSampleDto(false,"当前门店不符合申请条件，可引导客户前往商城自行下单");
            }
        }
        if (count <=3){
            logger.info("checkMerchant 门店{}  样品申请个数{}  小于3", query.getMid() , count);
            return new CheckSampleDto(true,"");
        }
        //大于3 去查询
        List<String> skus = sampleApplyMapper.selectSampleApplySkuList(query.getMid()).stream()
                .distinct().collect(Collectors.toList());
        logger.info("checkMerchant 门店{}  sku维度查询spu下sku {}", query.getMid() , skus);

        if (CollectionUtils.isEmpty(skus)){
            return new CheckSampleDto(true,"");
        }

        MerchantSkuSpuOrderResp merchantSkuSpuOrderResp =
                mallSkuQueryFacade.querySkuPriceTotalCount(query.getMid(), skus , true, false);
        if (merchantSkuSpuOrderResp.getExit()!=null && !merchantSkuSpuOrderResp.getExit()){
            logger.info("checkMerchant 门店{}   merchantSkuSpuOrderResp {} ", query.getMid() , JSONObject.toJSON(merchantSkuSpuOrderResp));
            return new CheckSampleDto(false,"历史样品单次数大于3且无复购");
        }
        //判断样品申请总数大于3
        return new CheckSampleDto(true,"");
    }

    @Override
    public CheckSampleDto checkMerchantSample(SampleApplyVO sampleApplyReview) {
        List<String> skus = sampleApplyReview.getSampleSkuList().stream().map(SampleSku::getSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skus)){
            return new CheckSampleDto(false,"sku不能为空");
        }

        // 72小时内是否有申请记录
        LambdaQueryWrapper<SampleApply> wrapper = Wrappers.lambdaQuery(SampleApply.class)
                .eq(SampleApply::getmId, sampleApplyReview.getmId())
                .ge(SampleApply::getAddTime, LocalDateTime.now().minusHours(72))
                .notIn(SampleApply::getStatus, Arrays.asList(SampleApplyStatusEnum.CANCEL.getId(), SampleApplyStatusEnum.CLOSE.getId()));
        Long count = sampleApplyMapper.selectCount(wrapper);
        if (count > 0){
            return new CheckSampleDto(false, "一个门店72h内不能重复申请样品");
        }


        MerchantSkuSpuOrderResp merchantSkuSpuOrderResp =
                mallSkuQueryFacade.querySkuPriceTotalCount(sampleApplyReview.getmId(), skus , true, true);
        if (merchantSkuSpuOrderResp.getExit()!= null && merchantSkuSpuOrderResp.getExit()){
            return new CheckSampleDto(false,"当前门店已有样品SPU购买记录");
        }
        int sum = sampleApplyReview.getSampleSkuList().stream().map(SampleSku::getAmount).collect(Collectors.toList()).stream().mapToInt(Integer::valueOf).sum();
        if (sum >= 3){
            return new CheckSampleDto(true,"当前样品单已超过申请标准,该样品单将流转至风控人员进行审核", true);
        }

        List<SkuSpuOrderDetailResp> resultSKus = merchantSkuSpuOrderResp.getSkus();
        if (CollectionUtils.isEmpty(resultSKus)){
            return new CheckSampleDto(true,"");
        }

        Map<String, BigDecimal> skuPriceMap = resultSKus.stream()
                .filter(skuDetail -> skuDetail.getPrice() != null)
                .collect(Collectors.toMap(SkuSpuOrderDetailResp::getSku, SkuSpuOrderDetailResp::getPrice));

        AtomicReference<BigDecimal> sumPrice = new AtomicReference<>(); // 计算所有价格的总和
        sumPrice.set(BigDecimal.ZERO);
        for (SampleSku it : sampleApplyReview.getSampleSkuList()) {
            BigDecimal price = skuPriceMap.get(it.getSku());
            if (price != null) {
                BigDecimal add = sumPrice.get().add(price.multiply(BigDecimal.valueOf(it.getAmount())));
                sumPrice.set(add.add(sumPrice.get()));
            }
        }
        //超过100
        if (sumPrice.get().compareTo(new BigDecimal(100)) > 0){
            return new CheckSampleDto(true,"温馨提示:当前样品单已超过申请标准,该样品单将流转至风控人员进行审核", true);
        }

        return new CheckSampleDto(true,"");
    }

    /**
     * 定时任务，月底关闭所有样品申请
     */
    @Override
    public void closeMonthSampleApplyReview() {
        logger.info("开始关闭样品申请");
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        // 如果是1号,关闭所有样品申请
        if(!NumberUtils.INTEGER_ONE.equals(LocalDate.now().getDayOfMonth())){
            endTime = endTime.minusDays(NumberUtils.INTEGER_THREE);
        }
        this.closeSampleApply(endTime);
        logger.info("已关闭样品申请{}前的样品申请",endTime);
    }

    /**
     * 获取所有endTime未关闭的样品申请ids
     */
    private void closeSampleApply(LocalDateTime endTime) {
        List<Integer> ids = sampleApplyMapper.querySituationListTime(endTime);
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        // 关闭申请
        SampleApplyReview sampleApplyReview = new SampleApplyReview();
        sampleApplyReview.setReviewName("系统审核");
        sampleApplyReview.setReviewRemark("超时关闭");
        sampleApplyReview.setStatus(1);
        sampleApplyReview.setAuditTime(new Date());
        for (Integer id : ids) {
            sampleApplyMapper.closeSampleApply(id);
            sampleApplyReview.setSampleId(id);
            sampleApplyReviewMapper.insertSelective(sampleApplyReview);
        }
    }
}
