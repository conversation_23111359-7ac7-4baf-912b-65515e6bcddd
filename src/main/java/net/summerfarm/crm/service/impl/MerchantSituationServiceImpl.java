package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.config.POPBLineConfig;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.*;
import net.summerfarm.crm.facade.CouponQueryFacade;
import net.summerfarm.crm.facade.MerchantAccountQueryFacade;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.facade.MerchantSituationFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.CrmMerchantDayLabelMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.mapper.repository.*;
import net.summerfarm.crm.model.bo.DingdingFormBO;
import net.summerfarm.crm.model.bo.ProcessInstanceCreateBO;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.*;
import net.summerfarm.crm.model.dto.merchantsituation.approval.RecentOrderDTO;
import net.summerfarm.crm.model.query.MerchantSituationQuery;
import net.summerfarm.crm.model.vo.MerchantSituationVO;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.constant.MessageKeyEnum;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.util.Producer;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.CrmCouponExpensePoolService;
import net.summerfarm.crm.service.MerchantService;
import net.summerfarm.crm.service.MerchantSituationService;
import net.summerfarm.enums.RedissonLockKey;
import net.summerfarm.manage.client.coupon.CouponProvider;
import net.summerfarm.manage.client.coupon.dto.req.IssueCouponReq;
import net.summerfarm.manage.client.coupon.dto.resp.CouponResp;
import net.summerfarm.pojo.DO.Area;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.AREA_MANAGER;
import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;

/**
 * <AUTHOR> ct
 * create at:  2019/7/29  2:37 PM
 */
@Service("merchantSituationService")
@Slf4j
public class MerchantSituationServiceImpl extends BaseService implements MerchantSituationService {

    @Resource
    private MerchantSituationMapper merchantSituationMapper;
    @Resource
    private MerchantSituationFacade merchantSituationFacade;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private BdInfoExtMapper bdInfoExtMapper;

    @Resource
    private CrmBdConfigMapper crmBdConfigMapper;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Resource
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private MerchantSituationQuotaMapper merchantSituationQuotaMapper;

    @Resource
    private Producer producer;

    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private CoreProductBasePriceMapper basePriceMapper;
    @Resource
    private CategoryCouponQuotaMapper quotaMapper;
    @Resource
    private CategoryCouponQuotaChangeMapper quotaChangeMapper;
    @Resource
    private CrmManageAreaMapper manageAreaMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private ProductsRepository productsRepository;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private CouponQueryFacade couponQueryFacade;
    @Resource
    private CrmBdOrgMapper orgMapper;
    @Resource
    private BdAreaConfigService areaConfigService;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    @Resource
    private MerchantExtendsRepository merchantExtendsRepository;
    @Resource
    private MerchantAccountQueryFacade accountQueryFacade;
    @Resource
    private AreaMapper areaMapper;
    @DubboReference
    private CouponProvider couponProvider;
    @Resource
    private CrmCouponExpensePoolService crmCouponExpensePoolService;
    @Resource
    private CustAfterDlvProfitLabelRepository custAfterDlvProfitLabelRepository;
    @Resource
    private CrmJobMapper crmJobMapper;
    @Resource
    private MerchantSituationActivityLogRepository merchantSituationActivityLogRepository;
    @Resource
    private MerchantService merchantService;
    @Resource
    private DeliveryPlanRepository deliveryPlanRepository;
    @Resource
    private AfterSaleOrderRepository afterSaleOrderRepository;
    @Resource
    private CategoryCouponFeeRateRepository categoryCouponFeeRateRepository;

    @NacosValue(value = "${merchant.situation.category.expand.activate.days:30}", autoRefreshed = true)
    private Integer activateDays;
    @Resource
    private POPBLineConfig popbLineConfig;

    @Override
    public AjaxResult insertMerchantSituation(MerchantSituationDTO merchantSituationDTO) {
        //校验券金额
        boolean couponAmountIsTrue = couponAmountIsTrue(merchantSituationDTO);
        if (!couponAmountIsTrue) {
            return AjaxResult.getErrorWithMsg("请检查券金额设置是否合理!使用阈值需大于券金额");
        }
        //校验销售权限
        MerchantStoreAndExtendDTO merchantExtend = merchantExtendsRepository.getAuthMerchantExtendDTO(merchantSituationDTO.getMerchantId(), getCompatibleDataPermission());
        if (Objects.isNull(merchantExtend)) {
            return AjaxResult.getErrorWithMsg("请联系主管至后台个人中心-权限配置,添加该区域数据权限后进行操作");
        }
        // 校验是否是账期客户
        if (Objects.equals(MerchantEnum.Direct.ACCOUNTING_PERIOD.ordinal(), merchantExtend.getDirect())) {
            return AjaxResult.getErrorWithMsg("账期客户不能使用卡券,请确认客户是否选择正确");
        }
        //检验是否有该城市权限
        boolean areaIsTrue = couponAreaIsTrue(merchantExtend.getAreaNo());
        if (!areaIsTrue) {
            return AjaxResult.getErrorWithMsg("请联系主管至后台销售管理-佣金机制管理-激励指标中,添加该区域权限后操作");
        }
        // 校验是否是顺鹿达库存&限制发券运营服务区
        if (MerchantEnum.BusinessLineEnum.POP.getCode().equals(merchantExtend.getBusinessLine()) &&
                popbLineConfig.getRestrictedAreaNosForMerchantSituationCoupon().contains(merchantExtend.getAreaNo())){
            return AjaxResult.getErrorWithMsg("当前门店所在区域暂不支持发券，如有疑问可咨询M1");
        }

        // 处理参数
        Integer couponCode = merchantSituationDTO.getMonthLivingCoupon();
        CouponEnum.CouponType couponType = CouponEnum.CouponType.getCouponType(couponCode);
        if (Objects.isNull(couponType)) {
            return AjaxResult.getErrorWithMsg("参数错误,请联系管理员看看系统出啥问题了");
        }

        MerchantSituationService situationService = SpringUtil.getBean(MerchantSituationService.class);
        switch (couponType) {
            case MERCHANT_SITUATION_COUPON:
                // 客情券
                return situationService.dealWithMerchantSituationCoupon(merchantExtend, merchantSituationDTO);
            case MONTH_LIVE_COUPON:
                MerchantStoreAccountResultResp primaryAccount = accountQueryFacade.getPrimaryAccount(merchantSituationDTO.getMerchantId());
                if (primaryAccount == null) {
                    return AjaxResult.getErrorWithMsg("请确认该商户是否有主账号");
                }
                // 月活券
                return situationService.dealWithMonthLivingCoupon(merchantSituationDTO, merchantExtend, primaryAccount.getPhone());
            case CATEGORY_COUPON:
                // 品类券-价格补贴
                CrmBdOrg parent = getParent();
                RLock redissonLock = redissonClient.getLock(RedissonLockKey.MERCHANT_SITUATION + parent.getId());
                try {
                    boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
                    if (!flag) {
                        throw new DefaultServiceException("请重新进行客情申请!");
                    }
                    return situationService.dealWithCategoryCoupon(merchantSituationDTO, merchantExtend, parent);
                } catch (InterruptedException e) {
                    log.warn("锁获取异常:{}", e.getMessage(), e);
                    return AjaxResult.getErrorWithMsg("请重新进行客情申请!");
                } finally {
                    if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                        redissonLock.unlock();
                    }
                }
                // 品类券-品类拓宽
            case MERCHANT_SITUATION_CATEGORY_COUPON:
                return situationService.dealWithMerchantSituationCategoryCoupon(merchantSituationDTO, merchantExtend);
            default:
                return AjaxResult.getErrorWithMsg("请联系管理员,并问问他为啥不传券类型!");
        }
    }


    /**
     * 品类券-品类拓宽处理
     *
     * @param merchantSituationDTO 申请信息
     * @param merchant             商户信息
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult dealWithMerchantSituationCategoryCoupon(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant) {
        /*AjaxResult skuResult = parseSku(merchantSituationDTO.getSku());
        if (!skuResult.isSuccess()) {
            return skuResult;
        }*/
        if (CollectionUtils.isEmpty(merchantSituationDTO.getCouponBlackAndWhiteDTOS())) {
            return AjaxResult.getErrorWithMsg("请联系管理员,并问问他为啥不传sku信息!");
        }
        String sku = merchantSituationDTO.getCouponBlackAndWhiteDTOS().get(0).getSku();
        // 门店下单记录判断
        CategoryProductDTO category = productsRepository.getCategoryTypeBySku(sku);
//        int count = 0;

//        SkuMerchantQuery skuMerchantQuery = new SkuMerchantQuery();
//        skuMerchantQuery.setPdId(category.getPdId());
//        List<CrmSkuMonthGmvVO> crmSkuMonthGmvVOS = productsRepository.selectByQuery(skuMerchantQuery);
//        List<String> skus = crmSkuMonthGmvVOS.stream().map(CrmSkuMonthGmvVO::getSku).distinct().collect(Collectors.toList());

//        if (CollectionUtil.isNotEmpty(skus)) {
//            count = ordersMapper.countOrderBySpuAndMerchantDuringXDay(merchantSituationDTO.getMerchantId(), skus, 30);
//        }

        List<Long> cannotApplyForMscSpuList = merchantService.queryCannotApplyMscSpu(merchant.getMId())
                .stream().map(MerchantRecentSpuDTO::getPdId).collect(Collectors.toList());
        if (cannotApplyForMscSpuList.contains(category.getPdId())) {
            return AjaxResult.getErrorWithMsg("当前门店不满足品类拓宽的客户条件");
        }


        AjaxResult ajaxResult = checkCategoryCoupon(merchantSituationDTO, merchant);
        if (!ajaxResult.isSuccess()) {
            return ajaxResult;
        }
        // 远程调用 校验信息并生成卡券
        AjaxResult legitimacyAndGetCoupon = this.checkMonthLivingCouponLegitimacyAndGetCoupon(merchantSituationDTO);
        if (!CrmGlobalConstant.SUCCESS_FLAG.equals(legitimacyAndGetCoupon.getCode())) {
            return legitimacyAndGetCoupon;
        }

        Config rewardRuleConfig = configMapper.selectOne(ConfigValueEnum.MARKETING_QUOTA_RETURN_RULE.getKey());
        CrmBdOrg crmBdOrg = getParent();

        this.saveAndSendApprovalMessage(merchantSituationDTO, merchant, MessageType.CATEGORY_COUPON_APPROVAL);

        // 如果不在活跃期内,则生成活跃信息
        List<Long> mscActiveSpuList = merchantSituationActivityLogRepository.selectActiveSpu(merchant.getMId())
                .stream().map(MscActiveSpuDTO::getPdId).collect(Collectors.toList());
        if (!mscActiveSpuList.contains(category.getPdId())) {
            MerchantSituationActivityLog activityLog = new MerchantSituationActivityLog();
            activityLog.setMerchantSituationId(merchantSituationDTO.getId());
            activityLog.setPdId(category.getPdId());
            activityLog.setActiveEndDate(LocalDate.now().plusDays(activateDays));
            activityLog.setActiveLength(activateDays);
            merchantSituationActivityLogRepository.save(activityLog);
        }

        CategoryCouponQuotaChange quotaChange = generateQuotaChange(crmBdOrg, merchantSituationDTO, (BigDecimal) ajaxResult.getData(), CategoryQuotaEnum.QuotaType.CATEGORY.getCode());
        quotaChange.setRewardRule(Integer.parseInt(rewardRuleConfig.getValue()));
        quotaChange.setType(CouponEnum.CouponQuotaChangeType.Expand.getCode());
        quotaChangeMapper.insertSelective(quotaChange);

        return AjaxResult.getOK();
    }

    public AjaxResult checkCategoryCoupon(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant) {
        if (CollectionUtils.isEmpty(merchantSituationDTO.getCouponBlackAndWhiteDTOS())) {
            return AjaxResult.getErrorWithMsg("请联系管理员,并问问他为啥不传sku信息!");
        }
        // 券后单价不能为负数
        BigDecimal afterCouponPrice = null;
        try {
            afterCouponPrice = new BigDecimal(merchantSituationDTO.getAfterCouponPriceStr());
            if (BigDecimal.ZERO.compareTo(afterCouponPrice) >= 0) {
                return AjaxResult.getErrorWithMsg("请核对下单数量是否正确,目前输入的下单数量将导致券后单价小于0");
            }
        } catch (Exception e) {
            return AjaxResult.getErrorWithMsg("请联系管理员,并问问他为啥券后单价格式搞错了!");
        }
       /* AjaxResult ajaxResult = parseSku(merchantSituationDTO.getSku());
        if (!ajaxResult.isSuccess()) {
            return ajaxResult;
        }
        String sku = (String) ajaxResult.getData();*/

        String sku = merchantSituationDTO.getCouponBlackAndWhiteDTOS().get(0).getSku();

        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        if (area == null) {
            return AjaxResult.getErrorWithMsg("门店运营区域为空，请刷新后重试");
        }

        // 判断底价
        CoreProductBasePrice productBasePrice = basePriceMapper.selectBySkuAreaNo(null, sku, area.getLargeAreaNo());
        BigDecimal basePrice = null;
        if (productBasePrice != null) {
            // 如果是品类拓宽券,则获取品类拓宽红线底价, 否则获取普通底价
            basePrice = Objects.equals(merchantSituationDTO.getMonthLivingCoupon(), CouponEnum.CouponType.MERCHANT_SITUATION_CATEGORY_COUPON.getCode()) ?
                    productBasePrice.getMerchantSituationCategoryRedLinePrice() : productBasePrice.getBasePrice();

            log.info("券类型:{}, 底价:{}, coreProductBasePrice: {}", merchantSituationDTO.getMonthLivingCoupon(), basePrice, productBasePrice);

            if (basePrice.compareTo(afterCouponPrice) > 0) {
                return AjaxResult.getErrorWithMsg("当前商品券后单价已超参考底价，请修改优惠券金额");
            }
        } else {
            // 判断鲜果/非鲜果 获取底价比例
            CategoryProductDTO category = productsRepository.getCategoryTypeBySku(sku);
            boolean isFruit = ObjectUtil.equal(category.getCategoryType(), CategoryTypeEnum.FRUIT.getType());
            String key = isFruit ? ConfigValueEnum.FRUIT_CORE_PRODUCT_BASE_PRICE.getKey() : ConfigValueEnum.CORE_PRODUCT_BASE_PRICE.getKey();
            Config basePriceConfig = configMapper.selectOne(key);
            BigDecimal averagePrice = ordersMapper.twoDaysAveragePrice(sku, merchant.getAreaNo());
            // 如果近2日没有成交记录 查询售价
            if (averagePrice == null) {
                averagePrice = areaSkuMapper.selectPriceBySkuAndAreaNo(sku, merchant.getAreaNo());
            }
            // 近2日当前sku单价
            basePrice = NumberUtil.mul(averagePrice, 1 - Double.parseDouble(basePriceConfig.getValue()) / 100);
            // 击穿底价需要添加备注信息
            if (basePrice.compareTo(afterCouponPrice) > 0) {
                String remarkStr = "当前券后单价已超系统底价，烦请严谨评估";
                String remark = StringUtils.isBlank(merchantSituationDTO.getSituationRemake()) ? remarkStr : merchantSituationDTO.getSituationRemake() + remarkStr;
                merchantSituationDTO.setSituationRemake(remark);
            }
        }
        return AjaxResult.getOK(basePrice);
    }

    private AjaxResult parseSku(String sku) {
        try {
            JSONObject json = JSON.parseObject(sku);
            return AjaxResult.getOK(json.keySet().iterator().next());
        } catch (Exception e) {
            return AjaxResult.getErrorWithMsg("请联系管理员,并问问他为啥sku信息格式搞错了!");
        }
    }

    public static void main(String[] args) {
        String str = "{\"status\":0,\"createLocation\":1,\"couponAmount\":22,\"threshold\":23,\"merchantId\":102658,\"monthLivingCoupon\":2,\"poolId\":11,\"situationRemake\":\"品类拉新\",\"adminId\":10280,\"adminName\":\"陈建欧\",\"couponBlackAndWhiteDTOS\":[{\"sku\":\"5417115233\",\"type\":2}],\"sku\":\"{\"5417115233\":\"测试水果-火龙果\"}\",\"orderQuantity\":\"2\",\"afterCouponPriceStr\":\"98.09\",\"salePrice\":109.09,\"ladderPrice\":\"[{\"adjustType\":3,\"amount\":0.1,\"price\":99.38,\"roundingMode\":0,\"unit\":3},{\"adjustType\":3,\"amount\":0.2,\"price\":89.67,\"roundingMode\":0,\"unit\":4},{\"adjustType\":3,\"amount\":0.3,\"price\":79.96,\"roundingMode\":0,\"unit\":5}]\",\"activityScope\":4}";
        System.out.println("{\"status\":0,\"createLocation\":1,\"couponAmount\":22,\"threshold\":23,\"merchantId\":102658,\"monthLivingCoupon\":2,\"poolId\":11,\"situationRemake\":\"品类拉新\",\"adminId\":10280,\"adminName\":\"陈建欧\",\"couponBlackAndWhiteDTOS\":[{\"sku\":\"5417115233\",\"type\":2}],\"sku\":\"{\"5417115233\":\"测试水果-火龙果\"}\",\"orderQuantity\":\"2\",\"afterCouponPriceStr\":\"98.09\",\"salePrice\":109.09,\"ladderPrice\":\"[{\"adjustType\":3,\"amount\":0.1,\"price\":99.38,\"roundingMode\":0,\"unit\":3},{\"adjustType\":3,\"amount\":0.2,\"price\":89.67,\"roundingMode\":0,\"unit\":4},{\"adjustType\":3,\"amount\":0.3,\"price\":79.96,\"roundingMode\":0,\"unit\":5}]\",\"activityScope\":4}");
    }


    public CategoryCouponQuotaChange generateQuotaChange(CrmBdOrg crmBdOrg, MerchantSituationDTO merchantSituationDTO, BigDecimal basePrice, Integer quotaType) {
        String categoryType = Optional.ofNullable(merchantSituationDTO.getCategoryType())
                .map(ProductCategoryTypeEnum::fromCode)
                .map(ProductCategoryTypeEnum::getDes)
                .orElse(null);
        // 备注格式, "[品类类型] - 备注"
        String remark = Stream.of(categoryType, merchantSituationDTO.getSituationRemake())
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(" - "));

        CategoryCouponQuotaChange quotaChange = new CategoryCouponQuotaChange();
        quotaChange.setAdminId(crmBdOrg.getBdId())
                .setAdminName(crmBdOrg.getBdName())
                .setQuota(merchantSituationDTO.getCouponAmount().negate())
                .setBasePrice(basePrice)
                .setType(CouponEnum.CouponQuotaChangeType.COUPON.getCode())
                .setDingtalkBizId(merchantSituationDTO.getId().intValue())
                .setRemark(remark)
                .setCreator(getAdminId())
                .setQuotaType(quotaType)
                .setCreatorName(getAdminName());
        return quotaChange;
    }

    /**
     * 获取m1 信息
     *
     * @return {@link CrmBdOrg}
     */
    public CrmBdOrg getParent() {
        CrmBdOrg crmBdOrg = orgMapper.selectByBdIdAndRank(getAdminId(), BD);
        if (crmBdOrg == null) {
            throw new BizException("销售组织不存在");
        }
        CrmBdOrg parent = orgMapper.selectByPrimaryKey(crmBdOrg.getParentId());
        if (parent == null) {
            throw new BizException("上级不存在，请指定上级");
        }
        return parent;
    }

    /**
     * 创建审批实例
     *
     * @param merchantSituationDTO 客情申请信息
     * @param merchant             商户信息
     * @return 审批实例
     */
    private ProcessInstanceCreateBO createProcessInstance(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant, String areaName) {
        // 审批业务实例
        ProcessInstanceCreateBO processInstanceCreateBO = new ProcessInstanceCreateBO();
        processInstanceCreateBO.setBizTypeEnum(ProcessInstanceBizTypeEnum.MERCHANT_SITUATION);
        // 发起人adminId
        processInstanceCreateBO.setAdminId(super.getAdminId());
        // 业务数据id 用于标识
        processInstanceCreateBO.setBizId(merchantSituationDTO.getId());
        // 审批表单参数
        List<DingdingFormBO> dingForms = new ArrayList<>(10);

        DingdingFormBO df0 = new DingdingFormBO();
        df0.setFormName("发起人");
        df0.setFormValue(super.getAdminName());
        dingForms.add(df0);

        DingdingFormBO df1 = new DingdingFormBO();
        df1.setFormName("客户名称");
        df1.setFormValue(merchant.getStoreName());
        dingForms.add(df1);

        DingdingFormBO df = new DingdingFormBO();
        df.setFormName("客户归属运营区域");
        df.setFormValue(areaName);
        dingForms.add(df);

        DingdingFormBO df2 = new DingdingFormBO();
        df2.setFormName("优惠券金额");
        df2.setFormValue(merchantSituationDTO.getCouponAmount().toPlainString());
        dingForms.add(df2);

        DingdingFormBO df3 = new DingdingFormBO();
        df3.setFormName("使用门槛");
        df3.setFormValue(merchantSituationDTO.getThreshold().toPlainString());
        dingForms.add(df3);

        if (StringUtils.isNotBlank(merchantSituationDTO.getSituationRemake())) {
            DingdingFormBO ddf = new DingdingFormBO();
            ddf.setFormName("申请备注");
            ddf.setFormValue(merchantSituationDTO.getSituationRemake());
            dingForms.add(ddf);
        }
        boolean isCategoryCoupon = CouponEnum.CouponType.CATEGORY_COUPON.getCode().equals(merchantSituationDTO.getMonthLivingCoupon()) || CouponEnum.CouponType.MERCHANT_SITUATION_CATEGORY_COUPON.getCode().equals(merchantSituationDTO.getMonthLivingCoupon());
        // 品类券才有商品信息
        if (isCategoryCoupon) {
            DingdingFormBO df4 = new DingdingFormBO();
            df4.setFormName("商品名称");
            df4.setFormValue(merchantSituationDTO.getSku());
            dingForms.add(df4);

            // 处理商品单价
            String ladderPriceStr = "";
            if (StringUtils.isNotBlank(merchantSituationDTO.getLadderPrice()) && !"[]".equals(merchantSituationDTO.getLadderPrice())) {
                List<LadderPriceDTO> ladderPriceDTOList = JSON.parseArray(merchantSituationDTO.getLadderPrice(), LadderPriceDTO.class);
                ladderPriceStr = this.dealWithLadderPrice(ladderPriceDTOList);
            }
            String skuPrice = "售价:" + merchantSituationDTO.getSalePrice().toPlainString() + ladderPriceStr;
            DingdingFormBO df5 = new DingdingFormBO();
            df5.setFormName("商品单价");
            df5.setFormValue(skuPrice);
            dingForms.add(df5);

            Integer activityScope = merchantSituationDTO.getActivityScope();
            String activityScopeValue = Objects.isNull(CouponEnum.ActivityScope.getActivityScopeValue(activityScope)) ? CouponEnum.ActivityScope.OTHER.getValue() : CouponEnum.ActivityScope.getActivityScopeValue(activityScope);
            DingdingFormBO df6 = new DingdingFormBO();
            df6.setFormName("订单类型");
            df6.setFormValue(activityScopeValue);
            dingForms.add(df6);

            DingdingFormBO df7 = new DingdingFormBO();
            df7.setFormName("下单数量");
            df7.setFormValue(merchantSituationDTO.getOrderQuantity().toString());
            dingForms.add(df7);

            DingdingFormBO df8 = new DingdingFormBO();
            df8.setFormName("用券后单价");
            df8.setFormValue(merchantSituationDTO.getAfterCouponPriceStr());
            dingForms.add(df8);

            if (StringUtils.isNotBlank(merchantSituationDTO.getFeishuImgCode())) {
                DingdingFormBO df9 = new DingdingFormBO();
                df9.setFormName("附加图片");
                df9.setFormValue(JSONUtil.toJsonStr(StrUtil.split(merchantSituationDTO.getFeishuImgCode(), StrPool.COMMA)));
                dingForms.add(df9);
            }


            DingdingFormBO df10 = new DingdingFormBO();
            df10.setFormName("当前费比");
            // 费比计算公式为 优惠券金额 / (售价 * 下单数量)
            df10.setFormValue(merchantSituationDTO.getCouponAmount().divide(merchantSituationDTO.getSalePrice().multiply(new BigDecimal(merchantSituationDTO.getOrderQuantity())), 4, RoundingMode.HALF_UP).toPlainString());
            dingForms.add(df10);
        }

        //新增费用来源
        if (!StringUtils.isEmpty(merchantSituationDTO.getPoolName())) {
            DingdingFormBO feiyong = new DingdingFormBO();
            feiyong.setFormName("费用来源");
            feiyong.setFormValue(merchantSituationDTO.getPoolName());
            dingForms.add(feiyong);
        }
        //新增是否自动审核标记
        DingdingFormBO shenhe = new DingdingFormBO();
        shenhe.setFormName("自动审核");
        shenhe.setFormValue("否");
        if (merchantSituationDTO.getAutoApprove() != null && merchantSituationDTO.getAutoApprove()) {
            shenhe.setFormValue("是");
        }
        dingForms.add(shenhe);


        // 标签
        StringBuffer labelBuilder = new StringBuffer();
        // 客户利润标签
        labelBuilder
                .append("【ABC客户标签】\n")
                .append(custAfterDlvProfitLabelRepository.selectDlvProfitLabelByCustIdAndNoSaas(merchant.getMId()));

        // 客户当前任务加入标签
        List<String> jobNames = crmJobMapper.selectInProgressJobNameByMId(merchant.getMId());
        if (CollectionUtil.isNotEmpty(jobNames)) {
            String label = CollectionUtil.split(jobNames, 3).stream() // 每行3个元素
                    .map(group -> CollUtil.join(group, "  "))  // 组内元素分隔
                    .collect(Collectors.joining(StrPool.LF));  // 组与组之间换行
            labelBuilder.append("\n\n【当前任务】\n").append(label);
        }

        // 不可申请品类拓宽券spu
        List<MerchantRecentSpuDTO> cannotApplyMscSpu = merchantService.queryCannotApplyMscSpu(merchant.getMId());
        if (CollectionUtil.isNotEmpty(cannotApplyMscSpu)) {
            labelBuilder.append("\n\n【不可申请品类拓宽券SPU】");
            Map<Integer, List<String>> typeSpuNameMap = cannotApplyMscSpu.stream()
                    .collect(Collectors.groupingBy(MerchantRecentSpuDTO::getType,
                            Collectors.mapping(MerchantRecentSpuDTO::getPdName, Collectors.toList())));

            typeSpuNameMap.keySet().stream().sorted().forEach(type -> {
                String label = CollectionUtil.split(typeSpuNameMap.get(type), 3).stream() // 每行3个元素
                        .map(group -> CollUtil.join(group, "  "))  // 组内元素分隔
                        .collect(Collectors.joining(StrPool.LF));  // 组与组之间换行
                labelBuilder.append("\n").append(CategoryTypeEnum.getValue(type)).append(":\n").append(label).append("\n");
            });
        }

        // 设置表单字段
        String label = labelBuilder.toString();
        if (StringUtils.isNotBlank(label)) {
            DingdingFormBO labelForm = new DingdingFormBO();
            labelForm.setFormName("客户标签");
            labelForm.setFormValue(label);
            dingForms.add(labelForm);
        }

        // ### 历史下单情况 ###
        if (CollectionUtil.isNotEmpty(merchantSituationDTO.getCouponBlackAndWhiteDTOS())) {
            String sku = merchantSituationDTO.getCouponBlackAndWhiteDTOS().get(0).getSku();
            DingdingFormBO orderForm = new DingdingFormBO();
            orderForm.setFormName("历史下单情况");
            StringBuilder orderValue = new StringBuilder();
            // 最近一次下单
            orderValue.append("【最近一次下单(交易维度)】");
            RecentOrderDTO latestOrderBySku = ordersMapper.getLatestOrderBySku(merchant.getMId(), sku);
            if (latestOrderBySku == null) {
                orderValue.append("\n无历史下单数据");
            } else {
                String orderMsg = String.format("日期 (%s)\n件数 (%s)\n单件实付金额 (%s)",
                        latestOrderBySku.getOrderTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                        latestOrderBySku.getAmount(),
                        latestOrderBySku.getPrice().toPlainString());
                orderValue.append("\n").append(orderMsg);
            }

            // 最近一次履约
            orderValue.append("\n【最近一次履约数据】");
            RecentOrderDTO lastFulFilledOrder = ordersMapper.getLatestFulfilledOrderBySku(merchant.getMId(), sku);
            if (lastFulFilledOrder == null) {
                orderValue.append("\n无历史履约数据");
            } else {
                LocalDate lastFulFilledTime = null; // 最近一次履约时间
                Integer lastFullFilledAmount = null; // 最近一次履约件数
                BigDecimal lastFulFilledPrice = lastFulFilledOrder.getPrice(); // 单件实付金额
                Integer unFulfilledAmount = null; // 剩余未履约件数

                List<DeliveryPlan> deliveries = deliveryPlanRepository.findByOrderNo(lastFulFilledOrder.getOrderNo());
                // 省心送订单
                final Integer timeOrderType = 1;
                if (timeOrderType.equals(lastFulFilledOrder.getType())) {
                    final Integer fulfilledCode = 6;
                    // 最近一次履约订单
                    DeliveryPlan lastFulFilled = deliveries.stream()
                            .filter(deliveryPlan -> fulfilledCode.equals(deliveryPlan.getStatus()))
                            .max(Comparator.comparing(DeliveryPlan::getDeliveryTime))
                            .orElse(null);

                    if (lastFulFilled != null) {
                        lastFulFilledTime = lastFulFilled.getDeliveryTime();
                        lastFullFilledAmount = lastFulFilled.getQuantity();
                    }

                    // 查找剩余未履约数量
                    // 订单数量 = 已履约数量 + 剩余未履约数量 + 未到货退款数量
                    // 1. 查找已履约数量
                    Integer fulfilledAmount = deliveries.stream()
                            .filter(deliveryPlan -> fulfilledCode.equals(deliveryPlan.getStatus()))
                            .mapToInt(DeliveryPlan::getQuantity)
                            .sum();

                    // 2. 未到货退款数量
                    MPJLambdaWrapper<AfterSaleOrder> queryWrapper = JoinWrappers.lambda(AfterSaleOrder.class)
                            .select(AfterSaleProof::getQuantity)
                            .leftJoin(AfterSaleProof.class, AfterSaleProof::getAfterSaleOrderNo, AfterSaleOrder::getAfterSaleOrderNo)
                            .in(AfterSaleProof::getStatus, Arrays.asList(0, 1, 2))
                            .eq(AfterSaleOrder::getDeliveryed, 0)
                            .eq(AfterSaleOrder::getOrderNo, lastFulFilledOrder.getOrderNo());

                    Integer refundAmount = afterSaleOrderRepository.selectJoinList(Integer.class, queryWrapper)
                            .stream().mapToInt(Integer::intValue).sum();

                    // 剩余未履约数量 = 下单数量 - 已履约数量 - 未到货退款数量
                    unFulfilledAmount = lastFulFilledOrder.getAmount() - fulfilledAmount - refundAmount;
                }
                // 普通订单
                else {
                    // 普通订单只会有一个履约
                    DeliveryPlan deliveryPlan = deliveries.get(0);
                    lastFulFilledTime = deliveryPlan.getDeliveryTime();
                    lastFullFilledAmount = deliveryPlan.getQuantity();
                }

                String fulfilledMsg = String.format("日期 (%s)\n件数 (%s)\n单件实付金额 (%s)\n剩余未履约件数 (%s)",
                        Optional.ofNullable(lastFulFilledTime).map(time -> time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).orElse("无"),
                        Optional.ofNullable(lastFullFilledAmount).map(Object::toString).orElse("0"),
                        Optional.ofNullable(lastFulFilledPrice).map(BigDecimal::toPlainString).orElse("0"),
                        Optional.ofNullable(unFulfilledAmount).map(Object::toString).orElse("0"));
                orderValue.append("\n").append(fulfilledMsg);
            }

            orderForm.setFormValue(orderValue.toString());
            dingForms.add(orderForm);
        }
        // ### 历史下单情况 ###

        processInstanceCreateBO.setDingdingForms(dingForms);
        return processInstanceCreateBO;
    }

    /**
     * 拼接商品售价规则字符串
     *
     * @param ladderPriceDTOList 阶梯价
     * @return 商品售价规则字符串
     */
    private String dealWithLadderPrice(List<LadderPriceDTO> ladderPriceDTOList) {
        StringBuffer ladderPrice = new StringBuffer("。阶梯价:");
        for (LadderPriceDTO ladderPriceDTO : ladderPriceDTOList) {
            ladderPrice.append("购买>=").append(ladderPriceDTO.getUnit()).append("件,单价").append(ladderPriceDTO.getPrice()).append("元。");
        }
        return ladderPrice.toString();
    }

    /**
     * 客情券处理
     *
     * @param merchant             商户信息
     * @param merchantSituationDTO 客情申请详情
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult dealWithMerchantSituationCoupon(MerchantStoreAndExtendDTO merchant, MerchantSituationDTO merchantSituationDTO) {
        Integer areaNo = merchant.getAreaNo();
        // 校验客情额度
        boolean isNoUpperLimit = checkCouponAmount(merchantSituationDTO.getCouponAmount(), super.getAdminId(), areaNo);
        if (!isNoUpperLimit) {
            return AjaxResult.getErrorWithMsg("客情额度不足,请下月再来!");
        }

        AjaxResult legitimacyAndGetCoupon = this.checkMonthLivingCouponLegitimacyAndGetCoupon(merchantSituationDTO);
        if (!CrmGlobalConstant.SUCCESS_FLAG.equals(legitimacyAndGetCoupon.getCode())) {
            return legitimacyAndGetCoupon;
        }
        //更新使用额度
        MerchantSituationQuota querySituationQuota = merchantSituationQuotaMapper.queryOne(super.getAdminId(), areaNo);
        MerchantSituationQuota updateSituationQuota = new MerchantSituationQuota();
        updateSituationQuota.setId(querySituationQuota.getId());
        updateSituationQuota.setAmount(querySituationQuota.getAmount().add(merchantSituationDTO.getCouponAmount()));
        merchantSituationQuotaMapper.updateQuota(updateSituationQuota);

        // 费用比
        Integer restrictedAutoAgreeAmountRatio = popbLineConfig.getRestrictedAutoAgreeAmountRatio();
        // 符合自动审批通过逻辑,POP业务线&费用比
        if (restrictedAutoAgreeAmountRatio != null &&
                MerchantEnum.BusinessLineEnum.POP.getCode().equals(merchant.getBusinessLine()) &&
                merchantSituationDTO.getCouponAmount().compareTo(BigDecimal.ZERO) > 0 &&
                merchantSituationDTO.getThreshold().compareTo(BigDecimal.ZERO) > 0) {


            // 费比=券金额/门槛
            BigDecimal amountRatio = merchantSituationDTO.getCouponAmount().divide(
                            merchantSituationDTO.getThreshold(), 2, RoundingMode.HALF_DOWN)
                    .multiply(new BigDecimal(100));
            if (amountRatio.compareTo(BigDecimal.valueOf(restrictedAutoAgreeAmountRatio)) <= 0) {
                this.saveAndAutoAgreeCoupon(merchantSituationDTO, merchant, MessageType.MERCHANT_SITUATION_APPROVAL);
                return AjaxResult.getOK();
            }
        }

        this.saveAndSendApprovalMessage(merchantSituationDTO, merchant, MessageType.MERCHANT_SITUATION_APPROVAL);

        return AjaxResult.getOK();
    }

    private void saveAndAutoAgreeCoupon(MerchantSituationDTO merchantSituationDTO,
                                        MerchantStoreAndExtendDTO merchant,
                                        String merchantSituationApproval) {
        // 生成审核记录
        merchantSituationDTO.setCreatorId(super.getAdminId());
        merchantSituationDTO.setCreatorName(super.getAdminName());
        merchantSituationDTO.setAdminId(super.getAdminId());
        merchantSituationDTO.setAdminName(super.getAdminName());
        merchantSituationDTO.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_NEW.ordinal());
        merchantSituationDTO.setCreateTime(LocalDateTime.now());
        merchantSituationDTO.setSituationType(merchantSituationDTO.getMonthLivingCoupon());
        merchantSituationDTO.setBasePrice(merchantSituationDTO.getBasePrice());
        merchantSituationDTO.setAmount(merchantSituationDTO.getCouponAmount());
        merchantSituationMapper.insertMerchantSituation(merchantSituationDTO);
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        if (area == null) {
            throw new BizException("获取门店运营区域失败，请重试");
        }

        // 事务后调用
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        merchantSituationFacade.autoAgree(merchantSituationDTO.getId());
                    } catch (Exception ex) {
                        log.error("客情券自动审批失败", ex);
                    }
                }
            });
        } else {
            try {
                merchantSituationFacade.autoAgree(merchantSituationDTO.getId());
            } catch (Exception ex) {
                log.error("客情券自动审批失败", ex);
            }
        }
    }

    /**
     * 品类券-价格补贴处理
     *
     * @param merchantSituationDTO 申请信息
     * @param merchant             商户信息
     * @param crmBdOrg             m1主管信息
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult dealWithCategoryCoupon(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant, CrmBdOrg crmBdOrg) {
        //
        Long poolId = merchantSituationDTO.getPoolId();
        if (poolId == null) {
            return AjaxResult.getErrorWithMsg("请选择相应的余额池");
        }
        // 校验底价
        AjaxResult ajaxResult = checkCategoryCoupon(merchantSituationDTO, merchant);
        if (!ajaxResult.isSuccess()) {
            return ajaxResult;
        }
        merchantSituationDTO.setBasePrice((BigDecimal) ajaxResult.getData());
        merchantSituationDTO.setAmount(merchantSituationDTO.getThreshold());
        crmCouponExpensePoolService.checkPoolSkuCanUse(merchantSituationDTO.getSku(), poolId, crmBdOrg.getBdId(), merchantSituationDTO);
//        // 判断额度
//        CategoryCouponQuota quota = quotaMapper.selectByAdminId(crmBdOrg.getBdId(), CategoryQuotaEnum.QuotaType.CATEGORY.getCode());
//        if (quota == null || quota.getQuota().compareTo(merchantSituationDTO.getCouponAmount()) < 0) {
//            return AjaxResult.getErrorWithMsg("品类额度不足，请联系主管");
//        }

        // 远程调用 校验信息并生成卡券
        AjaxResult legitimacyAndGetCoupon = this.checkMonthLivingCouponLegitimacyAndGetCoupon(merchantSituationDTO);
        if (!CrmGlobalConstant.SUCCESS_FLAG.equals(legitimacyAndGetCoupon.getCode())) {
            return legitimacyAndGetCoupon;
        }

        this.saveAndSendApprovalMessage(merchantSituationDTO, merchant, MessageType.CATEGORY_COUPON_APPROVAL);

//        // 扣减 m1 可用额度，生成额度变化流水
//        CategoryCouponQuotaChange quotaChange = generateQuotaChange(crmBdOrg, merchantSituationDTO, (BigDecimal) ajaxResult.getData(), CategoryQuotaEnum.QuotaType.CATEGORY.getCode());
//        quotaChange.setPoolId(poolId);
//        quotaChangeMapper.insertSelective(quotaChange);

        return AjaxResult.getOK();
    }


    /**
     * 月活券处理
     *
     * @param merchantSituationDTO 申请详情
     * @param merchant
     * @param phone                客户电话
     * @return 月活券处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult dealWithMonthLivingCoupon(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant, String phone) {
        // 合法性校验
        List<CrmBdOrg> crmBdOrgList = areaConfigService.listParentByBdAdminId(getAdminId(), BD, Boolean.FALSE);
        CrmBdOrg crmBdOrg = crmBdOrgList.stream().filter(c -> ObjectUtil.equal(AREA_MANAGER, c.getRank())).findFirst().orElse(null);
        if (crmBdOrg == null) {
            return AjaxResult.getErrorWithMsg("当前用户不存在上级M2");
        }

        AjaxResult illegalInformation = this.checkMonthLivingCouponLegitimacy(merchantSituationDTO, crmBdOrg);
        if (!CrmGlobalConstant.SUCCESS_FLAG.equals(illegalInformation.getCode())) {
            return illegalInformation;
        }

        // 2025.1.13 月活券需要走审批流程,不再直接发券
//        // 生成审核记录
//        this.generateMerchantSituation(merchantSituationDTO);
//        // 发券
//        Integer merchantCouponId = issueCouponDubbo(phone, merchantSituationDTO.getCouponId());

        AjaxResult legitimacyAndGetCoupon = this.checkMonthLivingCouponLegitimacyAndGetCoupon(merchantSituationDTO);
        if (!CrmGlobalConstant.SUCCESS_FLAG.equals(legitimacyAndGetCoupon.getCode())) {
            return legitimacyAndGetCoupon;
        }

        this.saveAndSendApprovalMessage(merchantSituationDTO, merchant, MessageType.MONTHLY_LIVING_APPROVAL);

        // 扣减额度 生成额度记录
        quotaMapper.updateQuota(crmBdOrg.getBdId(), merchantSituationDTO.getCouponAmount().negate(), CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode());
        CategoryCouponQuotaChange quotaChange = generateQuotaChange(crmBdOrg, merchantSituationDTO, BigDecimal.ZERO, CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode());
//        quotaChange.setMerchantCouponId(merchantCouponId);
        quotaChangeMapper.insertSelective(quotaChange);

        return AjaxResult.getOK();
    }

    private AjaxResult checkMonthLivingCouponLegitimacyAndGetCoupon(MerchantSituationDTO merchantSituationDTO) {
        // 远程调用生成券
        DubboResponse<CouponResp> dubboResponse = couponQueryFacade.checkMonthLivingCouponLegitimacy(merchantSituationDTO);
        if (!DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())) {
            return AjaxResult.getErrorWithMsg(dubboResponse.getMsg());
        }
        // 获取卡券id
        CouponResp couponResp = dubboResponse.getData();
        merchantSituationDTO.setCouponId(couponResp.getId());
        return AjaxResult.getOK();
    }

    private Integer issueCouponDubbo(String phone, Integer couponId) {
        IssueCouponReq req = new IssueCouponReq();
        req.setPhone(phone);
        req.setCouponId(couponId);
        req.setSendMsg(false);
        req.setMoneyLimitFlag(false);
        req.setReason("销售月活发券");
        DubboResponse<Integer> resp = couponProvider.issueCoupon(req);
        if (!resp.isSuccess()) {
            throw new BizException(resp.getMsg());
        }
        return resp.getData();
    }

    private void issueCoupon(String phone, Integer couponId, Long mId) {
        MqData mqData = new MqData();
        mqData.setType(MessageType.ISSUE_COUPON);
        mqData.setBusiness(MessageBusiness.COUPON);
        JSONObject msgJson = new JSONObject();
        msgJson.put("phone", phone);
        msgJson.put("couponId", couponId);
        msgJson.put("sendMsg", true);
        msgJson.put("moneyLimitFlag", false);
        msgJson.put("reason", "销售月活发券");
        msgJson.put("mId", mId);

        String producerMsg = msgJson.toJSONString();

        logger.info("发送月活券:{}", producerMsg);
        mqData.setData(producerMsg);
        producer.sendDataToQueue(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST, MessageKeyEnum.ISSUE_COUPON, JSON.toJSONString(mqData));
    }

    private void generateMerchantSituation(MerchantSituationDTO merchantSituationDTO) {
        merchantSituationDTO.setCreateTime(LocalDateTime.now());
        merchantSituationDTO.setCreatorId(getAdminId());
        merchantSituationDTO.setCreatorName(getAdminName());
        merchantSituationDTO.setAdminId(getAdminId());
        merchantSituationDTO.setAdminName(getAdminName());
        merchantSituationDTO.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_PASS.ordinal());
        merchantSituationDTO.setExamineTime(LocalDateTime.now());
        merchantSituationDTO.setExamineId(CrmGlobalConstant.SYSTEM_ID);
        merchantSituationDTO.setExamineName(CrmGlobalConstant.SYSTEM_NAME);
        merchantSituationDTO.setSituationType(merchantSituationDTO.getMonthLivingCoupon());
        merchantSituationMapper.insertMerchantSituation(merchantSituationDTO);
    }

    private AjaxResult checkMonthLivingCouponLegitimacy(MerchantSituationDTO merchantSituationDTO, CrmBdOrg crmBdOrg) {
        // 用户标签
        DataSynchronizationInformation thisMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        Integer dataFlag = Objects.isNull(thisMonth) ? NumberUtils.INTEGER_ZERO : thisMonth.getDateFlag();

        List<String> crmMerchantDayLabels = crmMerchantDayLabelMapper.selectByPrimaryKey(merchantSituationDTO.getMerchantId(), dataFlag);
        boolean isCompliant = CollectionUtil.isNotEmpty(crmMerchantDayLabels) && (crmMerchantDayLabels.contains(CrmGlobalConstant.OLD_MONTH_MERCHANT) || crmMerchantDayLabels.contains(CrmGlobalConstant.NEW_MONTH_MERCHANT));
        if (!isCompliant) {
            return AjaxResult.getErrorWithMsg("客户非月活标签客户,不满足月活券条件");
        }
        // 后台校验 下单次数 未使用的月活&用户召回券
        AjaxResult legitimacyAndGetCoupon = this.checkMonthLivingCouponLegitimacyAndGetCoupon(merchantSituationDTO);
        if (!CrmGlobalConstant.SUCCESS_FLAG.equals(legitimacyAndGetCoupon.getCode())) {
            return legitimacyAndGetCoupon;
        }
        // 每月申请月活&用户召回券次数需要小于3
        merchantSituationDTO.setCreateTime(DateUtils.getAtBeginningOfMonth());
        List<MerchantSituationVO> merchantSituationVOList = merchantSituationMapper.selectCouponByMid(merchantSituationDTO);
        List<MerchantSituationVO> merchantSituationList = merchantSituationVOList.stream().filter(m -> Objects.equals(CouponEnum.CouponType.MONTH_LIVE_COUPON.getCouponName(), m.getCouponName()) || Objects.equals(m.getGrouping(), CouponEnum.Group.USER_RECALL.ordinal())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(merchantSituationList) && merchantSituationList.size() >= 3) {
            return AjaxResult.getErrorWithMsg("客户本月申请已超过3次,不满足月活券条件");
        }
        // 每日申请次数需小于1
        LocalDateTime dayStart = BaseDateUtils.getDayStart(LocalDateTime.now());
        List<MerchantSituationVO> merchantSituationListToday = merchantSituationList.stream().filter(m -> dayStart.isBefore(m.getCreatTime())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(merchantSituationListToday)) {
            return AjaxResult.getErrorWithMsg("客户今日已申请,不满足月活券条件");
        }

        // 获取费比&额度，校验费比&额度信息

        CategoryCouponQuota quota = quotaMapper.selectByAdminId(crmBdOrg.getBdId(), CategoryQuotaEnum.QuotaType.MONTH_LIVING.getCode());
        if (quota == null) {
            return AjaxResult.getErrorWithMsg("M2没有月活额度配置");
        }

//        // 费比校验
        BigDecimal amountRatio = NumberUtil.div(merchantSituationDTO.getCouponAmount(), merchantSituationDTO.getThreshold()).multiply(new BigDecimal(100));
        Integer categoryType = merchantSituationDTO.getCategoryType();
        // 旧版兼容. 旧版没有categoryType
        if (categoryType == null) {
            if (crmMerchantDayLabels.contains(CrmGlobalConstant.OLD_MONTH_MERCHANT)) {
                if (amountRatio.compareTo(quota.getOldCustomerRate()) > 0) {
                    return AjaxResult.getErrorWithMsg("当前优惠券费比已超过设置费比" + quota.getOldCustomerRate().toPlainString() + "%，请修改后重新提交");
                }
            } else {
                if (amountRatio.compareTo(quota.getNewCustomerRate()) > 0) {
                    return AjaxResult.getErrorWithMsg("当前优惠券费比已超过设置费比" + quota.getOldCustomerRate().toPlainString() + "%，请修改后重新提交");
                }
            }
        }
        // 新版用品类类型判定费比
        else {
            CategoryQuotaFeeRateEnum.MerchantType merchantType = crmMerchantDayLabels.contains(CrmGlobalConstant.OLD_MONTH_MERCHANT)
                    ? CategoryQuotaFeeRateEnum.MerchantType.OLD : CategoryQuotaFeeRateEnum.MerchantType.NEW;
            LambdaQueryWrapper<CategoryCouponFeeRate> queryWrapper = Wrappers.lambdaQuery(CategoryCouponFeeRate.class)
                    .eq(CategoryCouponFeeRate::getCategoryCouponQuotaId, quota.getId())
                    .eq(CategoryCouponFeeRate::getMerchantType, merchantType.getCode())
                    .eq(CategoryCouponFeeRate::getCategoryType, categoryType);
            BigDecimal feeRate = Optional.ofNullable(categoryCouponFeeRateRepository.getOne(queryWrapper))
                    .map(CategoryCouponFeeRate::getFeeRate).orElse(BigDecimal.ZERO);
            if (amountRatio.compareTo(feeRate) > 0) {
                return AjaxResult.getErrorWithMsg("当前优惠券费比已超过设置费比" + feeRate.toPlainString() + "%，请修改后重新提交");
            }
        }


        // 额度校验
        if (merchantSituationDTO.getCouponAmount().compareTo(quota.getQuota()) > 0) {
            return AjaxResult.getErrorWithMsg("月活券额度不足，请联系主管添加");
        }

        return AjaxResult.getOK();
    }

    /**
     * 校验销售是否负责该区域
     *
     * @param areaNo 区域no
     * @return 是否负责
     */
    private boolean couponAreaIsTrue(Integer areaNo) {
        BdExt bdExt = new BdExt();
        bdExt.setAdminId(super.getAdminId());
        bdExt.setAreaNo(areaNo);
        BdExt oneBd = bdInfoExtMapper.selectOne(bdExt);
        return Objects.nonNull(oneBd);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult examineMerchantSituation(MerchantSituationVO merchantSituationVO, Integer type) {
        MerchantSituation queryMerchantSituation = merchantSituationMapper.querySituation(merchantSituationVO.getId());
        if (queryMerchantSituation == null || !Objects.equals(queryMerchantSituation.getStatus(), ExamineEnum.MerchantSituation.SITUATION_STATUS_NEW.ordinal())) {
            return AjaxResult.getErrorWithMsg("申请单不存在或申请单已被审核");
        }
        Merchant merchant = merchantMapper.selectByPrimaryKey(queryMerchantSituation.getMerchantId());
        merchantSituationVO.setExamineTime(LocalDateTime.now());
        merchantSituationVO.setExamineId(getAdminId());
        merchantSituationVO.setExamineName(getAdminName());
        merchantSituationVO.setCreatorId(queryMerchantSituation.getCreatorId());
        //审核未通过
        if (Objects.equals(type, ExpenseStatusEnum.NOT_APPROVED.ordinal())) {
            AjaxResult isSuccess = closeSituation(merchantSituationVO, merchant.getAreaNo(), queryMerchantSituation.getCouponId());
            if (ObjectUtil.notEqual(CrmGlobalConstant.SUCCESS_FLAG, isSuccess.getCode())) {
                return isSuccess;
            }
            return AjaxResult.getOK("关闭成功");
        }
        merchantSituationVO.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_PASS.ordinal());
        int update = merchantSituationMapper.updateSituation(merchantSituationVO);
        if (update > 0) {
            //获取券信息并发券
            this.issueCoupon(merchant.getPhone(), queryMerchantSituation.getCouponId(), merchant.getmId());
            return AjaxResult.getOK();
        }
        return AjaxResult.getError("申请单审核失败");
    }

    @Override
    public AjaxResult queryMerchantSituationList(int pageIndex, int pageSize, MerchantSituationQuery merchantSituationQuery) {
        boolean isManage = isSA() || isAreaSA() || isSaleSA();
        if (!isManage && Objects.isNull(merchantSituationQuery.getCreatorId())) {
            merchantSituationQuery.setCreatorId(super.getAdminId());
        }
        // 判断条件是否包含买家域
        if (StrUtil.isNotBlank(merchantSituationQuery.getMname())) {
            List<Long> midList = this.getMidListFromMerchantSituationQuery(merchantSituationQuery);
            if (CollUtil.isEmpty(midList)) {
                return AjaxResult.getOK(PageInfoHelper.createPageInfo(Collections.emptyList()));
            }
            merchantSituationQuery.setMidList(midList);
        }

        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantSituationVO> merchantSituationList = merchantSituationMapper.querySituationList(merchantSituationQuery);
        // 补充买家域数据
        this.warpMerchantSituationVOFromUserCenter(merchantSituationList);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(merchantSituationList));
    }

    @Override
    public AjaxResult queryMerchantSituation(Long id) {
        MerchantSituationQuery merchantSituationQuery = new MerchantSituationQuery();
        merchantSituationQuery.setId(id);
        List<MerchantSituationVO> list = merchantSituationMapper.querySituationList(merchantSituationQuery);
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.getErrorWithMsg("请联系主管至后台个人中心-权限配置,添加该区域数据权限后进行操作");
        }
        this.warpMerchantSituationVOFromUserCenter(list);

        MerchantSituationVO merchantSituationVO = list.get(NumberUtils.INTEGER_ZERO);
        // 处理sku信息
        boolean isSkuJson = StringUtils.isNotBlank(merchantSituationVO.getSku());
        if (isSkuJson) {
            try {
                JSONObject skuJson = JSON.parseObject(merchantSituationVO.getSku());
                for (Map.Entry<String, Object> stringObjectEntry : skuJson.entrySet()) {
                    String sku = stringObjectEntry.getKey();
                    merchantSituationVO.setSku(sku + "/" + stringObjectEntry.getValue());
                }
            } catch (Exception e) {
                return AjaxResult.getErrorWithMsg("请联系管理员,并问问他为啥sku信息格式搞错了!");
            }
        }
        if (Objects.equals(CrmGlobalConstant.BRACE, merchantSituationVO.getSku())) {
            merchantSituationVO.setSku(null);
        }
        // 处理订单类型
        String activityScopeValue = Objects.isNull(CouponEnum.ActivityScope.getActivityScopeValue(merchantSituationVO.getActivityScope())) ? CouponEnum.ActivityScope.OTHER.getValue() : CouponEnum.ActivityScope.getActivityScopeValue(merchantSituationVO.getActivityScope());
        merchantSituationVO.setActivityScopeStr(activityScopeValue);
        return AjaxResult.getOK(merchantSituationVO);
    }


    private List<Long> getMidListFromMerchantSituationQuery(MerchantSituationQuery merchantSituationQuery) {
        List<Integer> compatibleDataPermission = this.getCompatibleDataPermission();
        List<Long> mIdList = new ArrayList<>();
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setAreaNos(compatibleDataPermission);

        if (StrUtil.isNotBlank(merchantSituationQuery.getKeyword())) {
            req.setStoreName(merchantSituationQuery.getKeyword());
            mIdList = merchantQueryFacade.getMerchantIds(req);
        }
        if (StrUtil.isNotBlank(merchantSituationQuery.getMname())) {
            req.setStoreName(merchantSituationQuery.getMname());
            mIdList.addAll(merchantQueryFacade.getMerchantIds(req));
        }
        return mIdList;
    }


    private void warpMerchantSituationVOFromUserCenter(List<MerchantSituationVO> merchantSituationList) {
        if (CollectionUtil.isEmpty(merchantSituationList)) {
            return;
        }
        List<Long> midList = merchantSituationList.stream().map(MerchantSituationVO::getMerchantId).collect(Collectors.toList());
        List<MerchantStoreAndExtendDTO> merchantExtendDTO = merchantExtendsRepository.getAuthMerchantExtendDTO(midList, this.getCompatibleDataPermission());
        MerchantSituationVO.wrapMerchantSituationVo(merchantExtendDTO, merchantSituationList);

    }


    /**
     * 校验券金额是否正确
     *
     * @param merchantSituationDTO 券申请信息
     * @return 是否正确
     */
    private boolean couponAmountIsTrue(MerchantSituationDTO merchantSituationDTO) {
        BigDecimal couponAmount = merchantSituationDTO.getCouponAmount();
        BigDecimal threshold = merchantSituationDTO.getThreshold();
        return threshold.compareTo(couponAmount) >= 0;
    }

    /**
     * 校验是否已达上限,并校验是否第一次创建
     */
    private boolean checkCouponAmount(BigDecimal couponAmount, Integer adminId, Integer areaNo) {
        BdExt bdExt = new BdExt();
        bdExt.setAdminId(adminId);
        bdExt.setAreaNo(areaNo);
        BdExt oneBd = bdInfoExtMapper.selectOne(bdExt);
        if (oneBd == null) {
            return false;
        }
        if (Objects.nonNull(oneBd) && Objects.isNull(oneBd.getPrivateQuota())) {
            return false;
        }
        MerchantSituationQuota merchantSituationQuota = merchantSituationQuotaMapper.queryOne(adminId, areaNo);

        //查不到说明是第一次申请，新增额度记录
        if (merchantSituationQuota == null) {
            merchantSituationQuota = new MerchantSituationQuota();
            merchantSituationQuota.setAdminId(adminId);
            merchantSituationQuota.setAmount(BigDecimal.ZERO);
            merchantSituationQuota.setAreaNo(areaNo);
            merchantSituationQuota.setStatus(1);
            merchantSituationQuotaMapper.insertSituationQuota(merchantSituationQuota);
        }

        //已使用额度
        BigDecimal amount = merchantSituationQuotaMapper.selectAmount(adminId);
        return couponAmount.add(amount).compareTo(oneBd.getPrivateQuota()) <= 0;
    }

    //关闭申请单
    private AjaxResult closeSituation(MerchantSituationVO merchantSituationVO, Integer areaNo, Integer couponId) {
        Integer adminId = merchantSituationVO.getCreatorId();

        // 获取券信息
        CouponResp couponResp = couponQueryFacade.getCouponInfo(couponId);
        BigDecimal couponAmount = couponResp.getMoney();
        merchantSituationVO.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_CLOSE.ordinal());

        //申请归还本次客情金额
        MerchantSituationQuota merchantSituationQuota = new MerchantSituationQuota();
        MerchantSituationQuota querySituationQuota = merchantSituationQuotaMapper.queryOne(adminId, areaNo);
        BigDecimal subtract = querySituationQuota.getAmount().subtract(couponAmount);
        merchantSituationQuota.setAmount(subtract.compareTo(BigDecimal.ZERO) > -1 ? subtract : BigDecimal.ZERO);
        merchantSituationQuota.setId(querySituationQuota.getId());
        merchantSituationQuotaMapper.updateQuota(merchantSituationQuota);

        //关闭申请单
        merchantSituationMapper.updateSituation(merchantSituationVO);
        return AjaxResult.getOK();
    }

    @Override
    public void autoQuota() {
        merchantSituationQuotaMapper.updateQuotaAll();
        log.info("重置客情额度结束:{}", LocalDateTime.now());
    }

    @Override
    public void autoCloseSituation() {
        MerchantSituation merchantSituation = new MerchantSituation();
        merchantSituation.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_NEW.ordinal());
        merchantSituation.setExamineName(CrmGlobalConstant.SYSTEM_NAME);
        merchantSituation.setExamineRemark(CrmGlobalConstant.TIMEOUT_SHUTDOWN);
        merchantSituation.setExamineTime(LocalDateTime.now());
        merchantSituationMapper.updateSituationALl(merchantSituation);
        log.info("关闭所有客情申请结束:{}", LocalDateTime.now());
    }

    @Override
    public void autoTreeDayCloseSituation() {
        LocalDate now = LocalDate.now();
        LocalDateTime of = LocalDateTime.of(now, LocalTime.MIN);
        LocalDateTime endTime = of.minusDays(NumberUtils.INTEGER_THREE);
        MerchantSituationVO merchantSituationQuery = new MerchantSituationVO();
        merchantSituationQuery.setEndTime(endTime);
        merchantSituationQuery.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_NEW.ordinal());
        merchantSituationQuery.setSituationType(CouponEnum.CouponType.MERCHANT_SITUATION_COUPON.getCode());
        List<MerchantSituationVO> merchantSituations = merchantSituationMapper.querySituationListTime(merchantSituationQuery);

        if (CollectionUtils.isEmpty(merchantSituations)) {
            return;
        }

        for (MerchantSituationVO merchantSituation : merchantSituations) {
            MerchantSituation updateSituation = new MerchantSituation();
            updateSituation.setId(merchantSituation.getId());
            if (Objects.equals(merchantSituation.getStatus(), ExamineEnum.MerchantSituation.SITUATION_STATUS_NEW.ordinal())) {
                updateSituation.setExamineName(CrmGlobalConstant.SYSTEM_AUDIT);
                updateSituation.setExamineRemark(CrmGlobalConstant.TIMEOUT_SHUTDOWN);
                updateSituation.setExamineTime(LocalDateTime.now());
            }
            updateSituation.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_CLOSE.ordinal());
            merchantSituationMapper.updateSituation(updateSituation);
            MerchantStoreAndExtendResp merchantExtend = merchantQueryFacade.getMerchantExtendsByMid(merchantSituation.getMerchantId());
            Integer areaNo = merchantExtend == null ? null : merchantExtend.getAreaNo();
            MerchantSituationQuota selectQuota = merchantSituationQuotaMapper.queryOne(merchantSituation.getCreatorId(), areaNo);
            MerchantSituationQuota merchantSituationQuota = new MerchantSituationQuota();
            BigDecimal subtract = selectQuota.getAmount().subtract(merchantSituation.getCouponAmount());
            merchantSituationQuota.setId(selectQuota.getId());
            merchantSituationQuota.setAmount(subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract);
            merchantSituationQuotaMapper.updateQuota(merchantSituationQuota);
        }
        log.info("关闭三天前客情申请结束:{}", LocalDateTime.now());

    }

    @Override
    public AjaxResult merchantSituationQuota() {
        Integer adminId = getAdminId();
        Map<String, BigDecimal> map = new HashMap<>(2);
        // 已使用的客情额度
        BigDecimal bigDecimal = merchantSituationQuotaMapper.selectAmount(adminId);
        map.put("curApplicationNum", Objects.isNull(bigDecimal) ? BigDecimal.ZERO : bigDecimal);
        // 总客情额度
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(adminId);
        map.put("applicationNum", Objects.isNull(crmBdConfig) ? BigDecimal.ZERO : BigDecimal.valueOf(crmBdConfig.getQuotaLimit()));

        return AjaxResult.getOK(map);
    }

    /**
     * 发送审批实例消息
     *
     * @param merchantSituationDTO 申请信息
     * @param merchant             商户信息
     * @param messageType          消息类型
     */
    private void saveAndSendApprovalMessage(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant, String messageType) {
        // 生成审核记录
        merchantSituationDTO.setCreatorId(super.getAdminId());
        merchantSituationDTO.setCreatorName(super.getAdminName());
        merchantSituationDTO.setAdminId(super.getAdminId());
        merchantSituationDTO.setAdminName(super.getAdminName());
        merchantSituationDTO.setStatus(ExamineEnum.MerchantSituation.SITUATION_STATUS_NEW.ordinal());
        merchantSituationDTO.setCreateTime(LocalDateTime.now());
        merchantSituationDTO.setSituationType(merchantSituationDTO.getMonthLivingCoupon());
        merchantSituationDTO.setBasePrice(merchantSituationDTO.getBasePrice());
        merchantSituationDTO.setAmount(merchantSituationDTO.getCouponAmount());
        merchantSituationMapper.insertMerchantSituation(merchantSituationDTO);
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        if (area == null) {
            throw new BizException("获取门店运营区域失败，请重试");
        }

        // 生成审批实例
        ProcessInstanceCreateBO processInstanceCreateBO = this.createProcessInstance(merchantSituationDTO, merchant, area.getAreaName());

        // 发送消息至后台,补充区域及审批类型后 发起钉钉审批
        MqData mqData = new MqData();
        mqData.setBusiness(MessageBusiness.DING_DING);
        mqData.setType(messageType);
        JSONObject msgJson = new JSONObject();
        msgJson.put("params", processInstanceCreateBO);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendDelayQueue(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST, JSON.toJSONString(mqData), 3 * 1000L);
    }

}
