package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.MerchantLabelNacosConfig;
import net.summerfarm.crm.mapper.manage.MerchantLabelCorrelaionMapper;
import net.summerfarm.crm.model.domain.MerchantLabelCorrelaion;
import net.summerfarm.crm.model.input.label.NewYearLabelInput;
import net.summerfarm.crm.model.input.label.SchoolLabelInput;
import net.summerfarm.crm.service.MerchantLabelService;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class MerchantLabelServiceImpl implements MerchantLabelService {

    @Resource
    private MerchantLabelNacosConfig merchantLabelNacosConfig;
    @Resource
    private MerchantLabelCorrelaionMapper merchantLabelCorrelaionMapper;

    @Override
    public void upsertSchool(SchoolLabelInput input) {
        Integer schoolLabelId = Boolean.TRUE.equals(input.getSchool()) ?
                merchantLabelNacosConfig.getSchool() : merchantLabelNacosConfig.getNotSchool();
        this.insertOrUpdateMultipleChoiceLabel(input.getMId(),
                merchantLabelNacosConfig.getSchoolLabels(), schoolLabelId);
    }

    @Override
    public void upsertNewYear(NewYearLabelInput input) {
        // 过年是否营业
        Integer isOpenInNewYearLabelId = Boolean.TRUE.equals(input.getOpenDuringNewYear()) ?
                merchantLabelNacosConfig.getOpenInNewYear() : merchantLabelNacosConfig.getCloseInNewYear();
        this.insertOrUpdateMultipleChoiceLabel(input.getMId(),
                merchantLabelNacosConfig.getIsOpenInNewYearLabels(), isOpenInNewYearLabelId);

        // 开业时间
        String openTime = LocalDateTimeUtil.format(input.getOpenDate(), "MMdd");
        Integer openTimeLabelId = merchantLabelNacosConfig.getOpeningTimeMap().get(openTime);
        if (openTimeLabelId == null) {
            throw new ParamsException("开业时间不在范围内, 请重选");
        }
        this.insertOrUpdateMultipleChoiceLabel(input.getMId(),
                new ArrayList<>(merchantLabelNacosConfig.getOpeningTimeMap().values()), openTimeLabelId);
    }

    /**
     * 插入或更新是否学校/过年是否营业 类似"多选一"性质的标签
     *
     * @param mId          mId
     * @param labelIds     "多选一"标签id列表
     * @param labelIdInput 输入的标签id
     */
    private void insertOrUpdateMultipleChoiceLabel(Long mId, List<Integer> labelIds, Integer labelIdInput) {
        if (mId == null || CollectionUtil.isEmpty(labelIds) || labelIdInput == null) {
            return;
        }
        List<MerchantLabelCorrelaion> currentLabel = merchantLabelCorrelaionMapper.selectByMidAndLabelId(mId, labelIds);
        if (CollectionUtil.isEmpty(currentLabel)) {
            // 当前没有标签,则新建
            merchantLabelCorrelaionMapper.insertByMidAndLabelId(mId, labelIdInput.longValue());
        } else {
            // 否则更新
            merchantLabelCorrelaionMapper.updateLabelIdById(labelIdInput.longValue(), currentLabel.get(0).getId());
        }
    }
}
