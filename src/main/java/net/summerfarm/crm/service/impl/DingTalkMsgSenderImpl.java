package net.summerfarm.crm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.provider.MessageSendProvider;
import com.cosfo.message.client.req.MessageBodyReq;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.crm.enums.DingTalkMsgEnum;
import net.summerfarm.crm.facade.FeiShuPersonalMsgFacade;
import net.summerfarm.crm.model.bo.DingTalkMsgBO;
import net.summerfarm.crm.model.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.constant.MessageKeyEnum;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.util.Producer;
import net.summerfarm.crm.service.DingTalkMsgSender;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.robot.feishu.dto.card.FeishuCard;
import net.xianmu.robot.feishu.dto.card.FeishuCardBasic;
import net.xianmu.robot.feishu.dto.card.FeishuCardElement;
import net.xianmu.robot.feishu.enums.CardTagEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.client.producer.MQProducer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @describe
 * @create 2022-08-28 23:10
 */
@Service
@Slf4j
public class DingTalkMsgSenderImpl implements DingTalkMsgSender {
    @Resource
    private FeiShuPersonalMsgFacade msgFacade;

    @Resource
    private Producer producer;

    @Override
    public void sendMessage(DingTalkMsgBO dingTalkMsgBO) {
        // 发送至后台处理
        MqData mqData = new MqData();
        mqData.setType(MessageType.DING_DING_MESSAGE);
        mqData.setBusiness(MessageBusiness.DING_DING);
        JSONObject msgJson = new JSONObject();
        msgJson.put("dingTalkMsgBO", dingTalkMsgBO);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        producer.sendDataToQueue(CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST, MessageKeyEnum.DING_DING_MESSAGE, JSON.toJSONString(mqData));
    }

    @Override
    public void sendMessageWithFeiShu(DingTalkMsgReceiverIdBO msgBO) {
        log.info("发送消息给个人，dingTalkMsgBO：{}", JSON.toJSONString(msgBO));
        if (CollectionUtils.isEmpty(msgBO.getReceiverIdList())) {
            throw new ParamsException("消息发送失败，未指定消息接收人");
        }

        MessageBodyReq req = new MessageBodyReq();
        req.setContentType(1);

        req.setMsgBodyType(3);
        req.setTitle(msgBO.getTitle());

        String msgStr = msgBO.getText().replaceAll("#", "").replaceAll(">>", ">").replaceAll(">", "\n").replaceAll("\n\n", "\n").replaceAll(" ", "");

        FeishuCardElement feishuCardElement = new FeishuCardElement();
        feishuCardElement.setTag(CardTagEnum.div.getValue());
        feishuCardElement.setText(new FeishuCardBasic(CardTagEnum.lark_md.getValue(), msgStr));

        FeishuCard feishuCard = new FeishuCard();
        feishuCard.setElements(Collections.singletonList(feishuCardElement));

        req.setData(JSON.toJSONString(feishuCard));

        msgFacade.sendFeiShuPersonalMsg(msgBO.getReceiverIdList(), req);
    }
}
