package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.enums.FollowWhiteListEnum;
import net.summerfarm.crm.enums.StoreSizeEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.FollowWhiteListMapper;
import net.summerfarm.crm.mapper.offline.CrmMerchantDayAttributeMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.CrmMerchantDayAttribute;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.summerfarm.crm.model.domain.FollowWhiteList;
import net.summerfarm.crm.model.vo.FollowWhiteListVO;
import net.summerfarm.crm.service.FollowWhiteListService;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Service("followWhiteListService")
public class FollowWhiteListServiceImpl implements FollowWhiteListService {

    @Resource
    private FollowWhiteListMapper followWhiteListMapper;
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private CrmMerchantDayAttributeMapper crmMerchantDayAttributeMapper;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    @Override
    public AjaxResult saveWhiteList(FollowWhiteList followWhiteList) {
        if(Objects.isNull(followWhiteList) || Objects.isNull(followWhiteList.getmId()) ){
            return  AjaxResult.getError();
        }

        followWhiteList.setStatus(FollowWhiteListEnum.Status.IS_WHITELIST.ordinal());
        FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(followWhiteList.getmId());
        if(Objects.nonNull(queryFollowWhite)){
          return  AjaxResult.getError("该用户已在白名单内");
        }

        followWhiteList.setGmtCreate(new Date());
        followWhiteList.setGmtModified(new Date());
        followWhiteListMapper.insertFollowWhite(followWhiteList);
            return AjaxResult.getOK();
    }

    @Override
    public AjaxResult update(Long mId) {
        FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(mId);

        if(Objects.isNull(queryFollowWhite)){
            return AjaxResult.getError("该用户不在白名单内");
        }
        queryFollowWhite.setStatus(0);
        Integer integer = followWhiteListMapper.updateFollowWhite(queryFollowWhite);
        if(integer > 0){
            return AjaxResult.getOK();
        }
        return AjaxResult.getError();
    }

    @Override
    public AjaxResult queryWhiteList(int pageIndex, int pageSize, FollowWhiteListVO followWhiteListVO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FollowWhiteListVO> followWhiteLists = followWhiteListMapper.queryFollowWhiteList(followWhiteListVO);
        // 获取生命周期
        for (FollowWhiteListVO followWhiteList : followWhiteLists) {
            DataSynchronizationInformation dataTag = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_ATTRIBUTE.getTableName());
            Integer dataFlag = Objects.isNull(dataTag) ? NumberUtils.INTEGER_ZERO : dataTag.getDateFlag();
            CrmMerchantDayAttribute crmMerchantDayAttribute = crmMerchantDayAttributeMapper.selectByPrimaryKey(followWhiteList.getmId(), dataFlag);
            crmMerchantDayAttribute = Optional.ofNullable(crmMerchantDayAttribute).orElse(new CrmMerchantDayAttribute());
            followWhiteList.setLifecycle(crmMerchantDayAttribute.getMerchantLifecycle());
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(followWhiteLists));
    }

    @Override
    public AjaxResult deleteWhiteList(Long mId) {
        if(Objects.isNull(mId)){
            return AjaxResult.getError("mId不能为空");
        }
        FollowWhiteList queryFollowWhite = followWhiteListMapper.queryFollowWhiteListOne(mId);
        if(Objects.isNull(queryFollowWhite)){
            AjaxResult.getError("该用户不在白名单内");
        }
        followWhiteListMapper.deleteFollowWhite(mId);
        return AjaxResult.getOK();
    }

    @Override
    public int selectNumByBd(Integer adminId, List<Integer> areaNos) {
        List<FollowWhiteList> followWhiteLists = followWhiteListMapper.selectByBd(adminId);
        if (followWhiteLists.isEmpty()) {
            return 0;
        }
        List<Long> mIds = followWhiteLists.stream().map(FollowWhiteList::getmId).collect(Collectors.toList());
        List<MerchantStoreAndExtendResp> merchantExtend = merchantQueryFacade.getAuthMerchantExtends(mIds, areaNos);
        if (merchantExtend == null) {
            return 0;
        }
        return (int) merchantExtend.stream().filter(me -> ObjectUtil.equal(me.getSize(), RegionalOrganizationEnums.Size.MERCHANT.getCode())).count();
    }


}
