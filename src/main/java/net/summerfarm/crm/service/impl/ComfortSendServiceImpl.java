package net.summerfarm.crm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.enums.ComfortSetTypeEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.AdminMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.mapper.offline.CrmComfortSendOrderMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.dto.CrmComfortSendCountDTO;
import net.summerfarm.crm.model.dto.LadderPriceDTO;
import net.summerfarm.crm.model.query.CrmComfortSendQuery;
import net.summerfarm.crm.model.vo.BdExtVO;
import net.summerfarm.crm.model.vo.ComfortSendOrderVO;
import net.summerfarm.crm.service.ComfortSendService;
import net.summerfarm.pojo.DO.Admin;
import net.summerfarm.pojo.DO.Area;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.DataSynchronizationInformationEnum.*;


@Service
public class ComfortSendServiceImpl extends BaseService implements ComfortSendService {
    @Resource
    private CrmComfortSendOrderMapper crmComfortSendOrderMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;

    @Override
    public CommonResult<PageInfo<ComfortSendOrderVO>> queryComfortSend(CrmComfortSendQuery crmComfortSendQuery) {
        String type = crmComfortSendQuery.getType();
        crmComfortSendQuery.setTableName(null);
        String tableName = ComfortSetTypeEnum.getTableName(type);
        if (tableName == null) {
            return CommonResult.fail();
        }
        crmComfortSendQuery.setTableName(tableName);
        return query(crmComfortSendQuery);
    }


    private CommonResult<PageInfo<ComfortSendOrderVO>> query(CrmComfortSendQuery crmComfortSendQuery) {
        boolean bd = isBd();

        initQuery(bd, crmComfortSendQuery, crmComfortSendQuery.getTableName());
        PageHelper.startPage(crmComfortSendQuery.getPageIndex(), crmComfortSendQuery.getPageSize());
        String tableName = crmComfortSendQuery.getTableName();
        List<ComfortSendOrderVO> comfortSendOrderVOS = (Arrays.asList(CRM_COMFORT_SEND_FINISHED.getTableName(), CRM_COMFORT_SEND_WILL_FINISHED.getTableName()).contains(tableName)) ?
                crmComfortSendOrderMapper.selectByQueryList(crmComfortSendQuery) : crmComfortSendOrderMapper.selectByQuery(crmComfortSendQuery);
        merge(bd, comfortSendOrderVOS);
        return CommonResult.ok(PageInfoHelper.createPageInfo(comfortSendOrderVOS));
    }

    /**
     * init query
     *
     * @param bd                  是否db
     * @param crmComfortSendQuery query
     * @param tableName           离线表名称
     */
    private void initQuery(boolean bd, CrmComfortSendQuery crmComfortSendQuery, String tableName) {
        if (bd) {
            crmComfortSendQuery.setBdId(getAdminId());
        }
        DataSynchronizationInformation merchantDayTag = dataSynchronizationInformationMapper.selectByTableName(tableName);
        if (Objects.isNull(merchantDayTag)) {
            throw new DefaultServiceException("数据暂未更新,请明日再来!");
        }
        crmComfortSendQuery.setDayTag(merchantDayTag.getDateFlag());
    }


    /**
     * merge name
     *
     * @param bd                  是否db
     * @param comfortSendOrderVOS 出参数
     */
    private void merge(boolean bd, List<ComfortSendOrderVO> comfortSendOrderVOS) {
        if (CollectionUtils.isEmpty(comfortSendOrderVOS)) {
            return;
        }
        Map<Integer, String> adMinNameMap = new HashMap<>();
        List<Integer> bdIds = comfortSendOrderVOS.stream().map(ComfortSendOrderVO::getBdId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bdIds)) {
            List<Admin> admins = adminMapper.selectByIds(bdIds);
            adMinNameMap = admins.stream().collect(Collectors.toMap(Admin::getAdminId, Admin::getRealname));
        }
        List<Long> mids = comfortSendOrderVOS.stream().map(ComfortSendOrderVO::getMId).collect(Collectors.toList());
        Map<Long, String> midTypeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(mids)) {
            List<MerchantStoreAndExtendResp> merchantExtends = merchantQueryFacade.getMerchantExtendsByMid(mids);
            midTypeMap = merchantExtends.stream().filter(m -> StrUtil.isNotEmpty(m.getBusinessType())).collect(Collectors.toMap(MerchantStoreAndExtendResp::getMId, MerchantStoreAndExtendResp::getBusinessType));
        }
        Map<Integer, String> finalMap = adMinNameMap;
        Map<Long, String> finalMidTypeMap = midTypeMap;
        comfortSendOrderVOS.forEach(
                it -> {
                    it.setAdminName(finalMap.get(it.getBdId()));
                    it.setMainType(finalMidTypeMap.get(it.getMId()));

                }
        );
    }

    public Integer sumComfortCount(Long bdId, String  province,String city,String area) {
        List<String> tables = Arrays.asList(CRM_COMFORT_SEND_FINISHED.getTableName(), CRM_COMFORT_SEND_7DAY_NO_SEND.getTableName(), CRM_COMFORT_SEND_WILL_FINISHED.getTableName());
        Integer sum = 0;
        for (String table : tables) {
            DataSynchronizationInformation merchantDayTag = dataSynchronizationInformationMapper.selectByTableName(table);
            if (!Objects.isNull(merchantDayTag)) {
                Integer count = crmComfortSendOrderMapper.countByTableBdIdAreaNo(merchantDayTag.getDateFlag(), table, bdId, province,city,area);
                sum = count + sum;
            }
        }
        return sum;
    }


    public CommonResult<CrmComfortSendCountDTO> sumComfortQueryCount(Long bdId, String province,String city,String area) {
        if (bdId == null && isBd()) {
            bdId = getAdminId().longValue();
        }
        if (StrUtil.isEmpty(province)||StrUtil.isEmpty(city)){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"省市不能为空");
        }
        CrmComfortSendCountDTO keyCustomerCountDTO = new CrmComfortSendCountDTO();
        List<String> tables = Arrays.asList(CRM_COMFORT_SEND_FINISHED.getTableName(), CRM_COMFORT_SEND_7DAY_NO_SEND.getTableName(), CRM_COMFORT_SEND_WILL_FINISHED.getTableName());
        List<Integer> counts = new ArrayList<>();
        for (String table : tables) {
            DataSynchronizationInformation merchantDayTag = dataSynchronizationInformationMapper.selectByTableName(table);
            if (!Objects.isNull(merchantDayTag)) {
                Integer count = crmComfortSendOrderMapper.countByTableBdIdAreaNo(merchantDayTag.getDateFlag(), table, bdId,province,city,area);
                counts.add(count);
            }
        }
        keyCustomerCountDTO.setFinishCount(counts.get(0));
        keyCustomerCountDTO.setWithout7DayCount(counts.get(1));
        keyCustomerCountDTO.setWillFinishCount(counts.get(2));
        return CommonResult.ok(keyCustomerCountDTO);
    }



    private boolean isBd() {
        return !(super.isSA() || super.isSaleSA() || super.isAreaSA());
    }
}
