package net.summerfarm.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.schedulerx.common.util.JsonUtil;
import com.alibaba.schedulerx.shade.scala.Int;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.crm.client.dto.TaskDTO;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.CrmTaskEnum;
import net.summerfarm.crm.enums.DingTalkMsgEnum;
import net.summerfarm.crm.enums.DownloadBizTypeEnum;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.task.TimingTaskDTO;
import net.summerfarm.crm.model.query.task.*;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.model.vo.task.*;
import net.summerfarm.crm.mq.util.Producer;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.CrmTaskService;
import net.summerfarm.mall.client.provider.OrderProvider;
import net.summerfarm.manage.client.coupon.CouponProvider;
import net.summerfarm.manage.client.coupon.dto.resp.CouponResp;
import net.summerfarm.pojo.DO.Admin;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.beetl.core.io.IOUtil;
import org.beetl.core.util.ArrayMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import net.summerfarm.crm.client.dto.TaskDetailDTO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static javassist.CtClass.*;
import static net.summerfarm.crm.enums.ConfigValueEnum.TIMING_TASK_MSG;
import static net.summerfarm.crm.enums.ConfigValueEnum.TIMING_TASK_ORDER_DOWNLOAD;
import static net.summerfarm.crm.enums.CrmTaskEnum.Type.VISIT;
import static net.summerfarm.crm.enums.DownloadBizTypeEnum.CRM_TASK_DETAIL;
import static net.summerfarm.crm.enums.DownloadBizTypeEnum.CRM_TASK_IMPORT_RESULT;

/**
 * <AUTHOR>
 * @date 2023/10/7 10:54
 */
@Slf4j
@Service
public class CrmTaskServiceImpl extends BaseService implements CrmTaskService {
    @Resource
    private CrmTaskMapper taskMapper;
    @Resource
    private CrmTaskDetailMapper taskDetailMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;
    @Resource
    private FollowUpRelationMapper relationMapper;
    @DubboReference
    private CouponProvider couponProvider;
    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private DingTalkMsgSenderImpl dingTalkMsgSender;

    public static final String TEMP_FILE_PATH = "java.io.tmpdir";

    @Override
    public PageInfo<TaskListVo> taskList(TaskListQuery query) {
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<TaskListVo> taskListVos = taskMapper.taskList(query);
        taskListVos.forEach(t ->
        {
            CrmTaskEnum.Status status = CrmTaskEnum.Status.ONGOING;
            LocalDateTime now = LocalDateTime.now();
            if (t.getDeleteFlag() == CrmTaskEnum.DeleteFlag.DELETE.getStatus()) {
                status = CrmTaskEnum.Status.CANCELED;
            } else if (now.isBefore(t.getStartTime())) {
                status = CrmTaskEnum.Status.NOT_STARTED;
            } else if (now.isAfter(t.getEndTime())) {
                status = CrmTaskEnum.Status.ENDED;
            }
            t.setStatus(status.getStatus());
        });
        return PageInfoHelper.createPageInfo(taskListVos);
    }

    @Override
    public CrmTask taskInfo(Integer taskId) {
        return taskMapper.selectByPrimaryKey(taskId);
    }

    @Override
    public CommonResult<Integer> taskInsert(TaskInsertInput input) {
        CrmTask task = input.toBean();
        task.setCreator(getAdminId());
        taskMapper.insertSelective(task);
        return CommonResult.ok(task.getId());
    }

    @Override
    public CommonResult<TaskImportResultCountVo> taskDetailInsert(TaskDetailInsertInput input) {
        InputStream inputStream = null;
        TaskImportResultCountVo count = new TaskImportResultCountVo();
        List<TaskImportResultVo> errorList = new ArrayList<>();
        try {
            inputStream = OssGetUtil.getInputStream(input.getOssKey());
            List<TaskImportExcelInput> importList = EasyExcel.read(inputStream).head(TaskImportExcelInput.class).sheet().doReadSync();
            List<Integer> mIds = importList.stream().map(TaskImportExcelInput::getMId).collect(Collectors.toList());
            // 过滤重复的门店
            List<Integer> repeatMerchant = importList.stream().map(TaskImportExcelInput::getMId).collect(Collectors.toMap(e -> e, e -> 1, Integer::sum)).entrySet().stream().filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
            if (!repeatMerchant.isEmpty()) {
                mIds.removeAll(repeatMerchant);
                importList = check(mIds, errorList, importList, "门店重复");
            }

            // 查询存在的门店,过滤不存在的门店
            if (!mIds.isEmpty()) {
                List<Integer> existMids = merchantMapper.selectMIdsByMIds(mIds);
                mIds = existMids;
                importList = check(existMids, errorList, importList, "门店不存在");
            }

            // 查询有销售跟进的门店,过滤没有销售跟进的门店
            if (!mIds.isEmpty()) {
                List<Integer> careMids = relationMapper.selectMIdByMId(mIds);
                mIds = careMids;
                importList = check(careMids, errorList, importList, "门店没有跟进bd");

            }

            // 查询已经存在的门店,过滤任务中已经存在的门店
            if (!mIds.isEmpty()) {
                List<Integer> existTask = taskDetailMapper.listMidByMIdsAndTaskId(input.getTaskId(), mIds);
                if (!existTask.isEmpty()) {
                    mIds.removeAll(existTask);
                    check(mIds, errorList, importList, "任务中已经存在该门店");
                }
            }

            if (!mIds.isEmpty()) {
                count.setSuccessCount(mIds.size());
                taskDetailMapper.insertBatch(mIds, input.getTaskId(), null);
            }
        } catch (Exception e) {
            log.info("导入任务失败,msg:{}", e.getMessage(), e);
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "导入失败，请检查Excel信息是否正确");
        } finally {
            IoUtil.close(inputStream);
        }

        // 上传失败记录 & 插入正确数据
        if (!errorList.isEmpty()) {
            String filename = "新增任务客户导入结果-" + UUID.randomUUID() + ".xlsx";
            Long resId = createDownloadRecord(filename, CRM_TASK_IMPORT_RESULT);
            count.setResourceId(resId);
            count.setErrorCount(errorList.size());
            SpringUtil.getBean(CrmTaskService.class).uploadExportFile(errorList, filename, resId);
        }
        return CommonResult.ok(count);
    }

    @Override
    public CommonResult<Void> taskDetailExport(Integer taskId) {
        CrmTask crmTask = taskMapper.selectByPrimaryKey(taskId);
        if (crmTask.getType() != VISIT.getType()) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "任务类型错误，只能查看拜访任务进展");
        }
        String filename = "拜访进展" + UUID.randomUUID() + ".xlsx";
        Long resId = createDownloadRecord(filename, CRM_TASK_DETAIL);
        SpringUtil.getBean(CrmTaskService.class).taskDetailExportAsync(resId, filename, taskId);
        return CommonResult.ok();
    }

    @Override
    public void taskDetailExportAsync(Long resId, String filename, Integer taskId) {
        File file = new File(System.getProperty(TEMP_FILE_PATH) + File.separator + filename);
        List<TaskDetailExportVo> taskDetail = taskDetailMapper.taskExport(taskId);
        EasyExcel.write(file, TaskDetailExportVo.class).sheet("导入结果").doWrite(taskDetail);
        updateFile(filename, file, resId);
    }

    @Override
    public CommonResult<PageInfo<TaskDetailVo>> taskDetail(TaskDetailQuery query) {
        CrmTask crmTask = taskMapper.selectByPrimaryKey(query.getTaskId());
        if (crmTask == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "任务不存在，请刷新");
        }
        CommonResult<PageInfo<TaskDetailVo>> pageInfoCommonResult = initTaskQuery(query);
        if (pageInfoCommonResult != null) {
            return pageInfoCommonResult;
        }

        PageHelper.startPage(query.getPageIndex(), query.getPageSize());

        List<TaskDetailVo> taskDetailList = null;
        //这里不动原sql逻辑，拜访任务查询新的sql，卡券任务还是走原逻辑
        if (crmTask.getType().equals(CrmTaskEnum.Type.VISIT.getType())){
            taskDetailList = taskDetailMapper.listDetailForVisitTask(query.getBdId(), query.getStatus(), query.getTaskId(), crmTask.getType());
        }else {
            taskDetailList = taskDetailMapper.listDetail(query.getBdId(), query.getStatus(), query.getTaskId(), crmTask.getType());
        }
        if (CollectionUtil.isEmpty(taskDetailList)) {
            return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>()));
        }
        // 发券任务获取券信息
        Map<Integer, CouponResp> couponMap = new HashMap<>();
        if (crmTask.getType() == CrmTaskEnum.Type.COUPON.getType()) {
            DubboResponse<List<CouponResp>> couponListResp = couponProvider.getCouponList(taskDetailList.stream().map(TaskDetailVo::getSourceId).map(Integer::parseInt).collect(Collectors.toList()));
            if (couponListResp.isSuccess()) {
                couponMap = couponListResp.getData().stream().collect(Collectors.toMap(CouponResp::getId, Function.identity()));
            }
        }

        // 获取门店任务
        List<CrmTaskDetail> crmTaskDetails = taskDetailMapper.listByMIdAndType(taskDetailList.stream().map(TaskDetailVo::getMId).collect(Collectors.toList()), crmTask.getType());
        Map<Integer, List<CrmTaskDetail>> taskDetailMap = crmTaskDetails.stream().collect(Collectors.groupingBy(CrmTaskDetail::getMId));

        // 任务是否结束
        boolean isNotEnd = LocalDateTime.now().isBefore(crmTask.getEndTime()) && crmTask.getDeleteFlag() == CrmTaskEnum.DeleteFlag.NORMAL.getStatus();
        LocalDate now = LocalDate.now();
        List<LocalDate> refundDays = Arrays.asList(now.plusDays(3), now.plusDays(7), now.plusDays(30));
        for (TaskDetailVo taskDetail : taskDetailList) {
            taskDetail.setTaskId(crmTask.getId());
            taskDetail.setType(crmTask.getType());
            // 发券任务补充券信息
            if (crmTask.getType() == CrmTaskEnum.Type.COUPON.getType() && !taskDetailList.isEmpty()) {
                if (taskDetail.getSourceId() != null && couponMap.containsKey(Integer.parseInt(taskDetail.getSourceId()))) {
                    CouponResp couponResp = couponMap.get(Integer.parseInt(taskDetail.getSourceId()));
                    taskDetail.setMoney(couponResp.getMoney()).setThreshold(couponResp.getThreshold()).setCouponName(couponResp.getName());
                }
            }
            if (isNotEnd) {
                // 判断是否多任务
                if (taskDetailMap.containsKey(taskDetail.getMId())) {
                    List<CrmTaskDetail> crmTaskDetail = taskDetailMap.get(taskDetail.getMId());
                    taskDetail.setMultiTaskTag(crmTaskDetail.size() > 1 ? 0 : 1);
                }
                // 拜访任务且订单号不为空需判断是否有多订单 倒计时 3/7/30触发多订单提醒
                if (crmTask.getType() == VISIT.getType() && taskDetail.getSourceId() != null) {
                    List<TimingTaskDTO> timingOrders = taskMapper.listOrderByRefundTime(refundDays, taskDetail.getMId(), taskDetail.getSourceId());
                    taskDetail.setMultiOrderTag(!timingOrders.isEmpty() ? 0 : 1);
                }
            }
        }
        return CommonResult.ok(PageInfoHelper.createPageInfo(taskDetailList));
    }

    @Override
    public void createTask(CrmTask task) {
        taskMapper.insertSelective(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTimingTask() {
        LocalDate refundTime = LocalDate.now().plusDays(15);
        List<TimingTaskDTO> taskList = taskMapper.listOrderByRefundTime(Collections.singletonList(refundTime), null, null);

        if (!taskList.isEmpty()) {
            CrmTask crmTask = new CrmTask();
            LocalDateTime startTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            crmTask.setTaskName("省心送到期退款倒计时15天" + startTime.toLocalDate())
                    .setType(VISIT.getType())
                    .setStartTime(startTime)
                    .setEndTime(startTime.plusDays(3).minusSeconds(1));
            taskMapper.insertSelective(crmTask);

            for (TimingTaskDTO task : taskList) {
                taskDetailMapper.insertByMid(task.getMId(), crmTask.getId(), task.getOrderNo());
            }
        }

        List<Integer> mIds = taskList.stream().map(TimingTaskDTO::getMId).distinct().collect(Collectors.toList());
        if (mIds.isEmpty()){
            return;
        }
        StringBuilder textSb = new StringBuilder();
        textSb.append("今日省心送到期倒计时 15 天客户\n\n").append("今日共").append(mIds.size()).append("家客户\n\n");
        if (!mIds.isEmpty()) {
            List<Merchant> merchantIdList = merchantMapper.selectByMIdList(mIds);
            Map<String, List<Merchant>> merchantMap = merchantIdList.stream().collect(Collectors.groupingBy(Merchant::getCity));
            merchantMap.forEach((k, v) -> {
                textSb.append(k).append(":").append(v.size()).append("家\n\n");
            });
        }
        textSb.append("请及时通知客户进行配送~\n\n");
        Config config = configMapper.selectOne(TIMING_TASK_MSG.getKey());


        Config download = configMapper.selectOne(TIMING_TASK_ORDER_DOWNLOAD.getKey());
        textSb.append(download.getValue()).append("?day=").append(refundTime).append("&code=")
                .append(StrUtil.sub(config.getValue(), config.getValue().length() - 36, config.getValue().length())).append(")");

        FeishuBotUtil.sendMarkdownMsg(config.getValue(), textSb.toString());
    }

    @Override
    public CommonResult<String> createTaskDetail(TaskDetailDTO taskDetail) {
        CrmTask crmTask = taskMapper.selectBySourceId(taskDetail.getSourceId());
        if (crmTask == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "卡券 id 不正确");
        }
        taskDetailMapper.insertBatch(taskDetail.getMIds(), crmTask.getId(), taskDetail.getSourceId());
        return CommonResult.ok();
    }

    @Override
    public void cancelTask(String sourceId) {
        taskDetailMapper.cancelTask(sourceId);
    }

    @Override
    public CommonResult<TimingTaskCountVo> timingTaskCount() {
        List<Integer> bdIds;
        // bd 查询自己负责的
        if (isSaleSA()) {
            List<CrmBdOrg> bdOrgList = bdAreaConfigService.listChildrenBd();
            bdIds = bdOrgList.stream().map(CrmBdOrg::getBdId).collect(Collectors.toList());
        } else if (isBD()) {
            bdIds = Collections.singletonList(getAdminId());
        } else {
            return CommonResult.ok(new TimingTaskCountVo());
        }
        if (CollectionUtil.isEmpty(bdIds)){
            return CommonResult.ok();
        }
        TimingTaskCountVo count = taskDetailMapper.timingTaskCount(bdIds);
        return CommonResult.ok(count);
    }

    @Override
    public CommonResult<TaskCountVo> taskCount(Integer taskId) {
        CrmTask crmTask = taskMapper.selectByPrimaryKey(taskId);
        List<Integer> bdIdList = new ArrayList<>();
        if (isSaleSA() || isAreaSA()) {
            bdIdList = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).collect(Collectors.toList());
        } else if (isBD()) {
            bdIdList = Collections.singletonList(getAdminId());
        }
        if (crmTask == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "任务不存在");
        }
        if (crmTask.getType() == CrmTaskEnum.Type.COUPON.getType()) {
            return CommonResult.ok(taskMapper.selectCouponTaskCount(taskId, bdIdList));
        }
        if (crmTask.getType() == VISIT.getType()) {
            return CommonResult.ok(taskMapper.selectFollowTaskCount(taskId, bdIdList));
        }
        return null;
    }

    @Override
    public List<TimingTaskVo> timingTaskDownload(LocalDate day, String code) {
        Config config = configMapper.selectOne(TIMING_TASK_MSG.getKey());
        if (!StrUtil.sub(config.getValue(), config.getValue().length() - 36, config.getValue().length()).equals(code)) {
            throw new BizException("验证失败，请重试");
        }

        List<TimingTaskDTO> timingOrders = taskMapper.listOrderByRefundTime(Collections.singletonList(day), null, null);
        if (timingOrders.isEmpty()) {
            throw new BizException("所选时间没有订单");
        }
        return taskMapper.listTimingOrderByNo(timingOrders.stream().map(TimingTaskDTO::getOrderNo).collect(Collectors.toList()));
    }

    @Override
    public void sendTimingRemindMsg() {
        List<TimingRemindOrder> timingRemindOrders = ordersMapper.listTimingRemindOrder();
        if (timingRemindOrders.isEmpty()){
            return;
        }
        Map<Integer, List<TimingRemindOrder>> timingOrderMap = timingRemindOrders.stream().collect(Collectors.groupingBy(TimingRemindOrder::getBdId));
        timingOrderMap.forEach(this::dingTalk);
    }

    @Override
    public void updateTaskUpStatus(List<Long> taskIds) {
        if (CollectionUtil.isEmpty(taskIds)){
            log.info("任务id为空不跑");
            return;
        }
        for (Long taskId : taskIds) {
            List<Integer> taskDetails = taskDetailMapper.selectOverTask(taskId);
            if (CollectionUtil.isEmpty(taskDetails)){
                continue;
            }
            taskDetails.forEach(
                    it->{
                        CrmTaskDetail crmTaskDetail = new CrmTaskDetail();
                        crmTaskDetail.setId(it);
                        crmTaskDetail.setStatus(1);
                        taskDetailMapper.updateByPrimaryKeySelective(crmTaskDetail);
                    }
            );
        }
    }

    /**
     * 省心送未配送提醒
     *
     * @param adminId 用户 id
     * @param orderList   订单
     */
    private void dingTalk(Integer adminId, List<TimingRemindOrder> orderList) {
        if (adminId==null){
            return;
        }
        String title = "省心送订单48小时未设置配送";
        StringBuilder content = new StringBuilder("#####省心送订单48小时未设置配送\n");
        orderList.forEach(order->{
            content.append("订单号: ").append(order.getOrderNo()).append("\n").append("客户名称: ").append(order.getMname()).append("\n");
        });
        content.append("> ###### 请及时通知客户进行配送，超过90天未配送完将会自动退款哦~");


        DingTalkMsgReceiverIdBO bo = new DingTalkMsgReceiverIdBO();
        bo.setReceiverIdList(Collections.singletonList(Long.valueOf(adminId)));
        bo.setText(content.toString());
        bo.setMsgType(DingTalkMsgEnum.MARKDOWN.getType());
        bo.setTitle(title);
        dingTalkMsgSender.sendMessageWithFeiShu(bo);

    }

    @Override
    public void uploadExportFile(List<TaskImportResultVo> errorImportList, String filename, Long resId) {
        File file = new File(System.getProperty(TEMP_FILE_PATH) + File.separator + filename);
        EasyExcel.write(file, TaskImportResultVo.class).sheet("导入结果").doWrite(errorImportList);
        updateFile(filename, file, resId);
    }

    public CommonResult<PageInfo<TaskDetailVo>> initTaskQuery(TaskDetailQuery query) {
        if (StringUtils.isNotBlank(query.getBdName())) {
            if (isSA()) {
                // 超管查询所有 bd
                List<Admin> admins = adminMapper.selectByRealName(query.getBdName(), null);
                if (admins.isEmpty()) {
                    return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>()));
                }
                query.setBdId(admins.stream().map(Admin::getAdminId).collect(Collectors.toList()));
            } else if (isAreaSA() || isSaleSA()) {
                // 销售查询下属 bd

                List<CrmBdOrg> crmBdOrgList = bdAreaConfigService.listChildrenBd();
                List<CrmBdOrg> org = crmBdOrgList.stream().filter(c -> c.getBdName().equals(query.getBdName())).collect(Collectors.toList());
                if (org.isEmpty()) {
                    return CommonResult.ok(PageInfoHelper.createPageInfo(new ArrayList<>()));
                }
                query.setBdId(org.stream().map(CrmBdOrg::getBdId).collect(Collectors.toList()));
            }
        } else if (isAreaSA() || isSaleSA()) {
            List<CrmBdOrg> crmBdOrgList = bdAreaConfigService.listChildrenBd();
            if (crmBdOrgList != null && !crmBdOrgList.isEmpty()) {
                query.setBdId(crmBdOrgList.stream().map(CrmBdOrg::getBdId).collect(Collectors.toList()));
            }
        } else if (isBD()) {
            // bd 查询自己负责的任务
            query.setBdId(Collections.singletonList(getAdminId()));
            // 主管查询下属 bd 负责的任务
        }
        return null;
    }

    /**
     * 上传文件
     *
     * @param filename 文件名
     * @param file     文件
     * @param resId    res id
     */
    public void updateFile(String filename, File file, Long resId) {
        OssUploadResult upload = OssUploadUtil.upload(filename, file, OSSExpiredLabelEnum.THREE_DAY);
        DownloadCenterUploadReq req = new DownloadCenterUploadReq();
        req.setStatus(DownloadCenterEnum.Status.UPLOADED);
        req.setResId(resId);
        req.setFilePath(upload.getObjectOssKey());
        downloadCenterProvider.uploadFile(req);
        FileUtil.del(file);
    }

    /**
     * 创建下载记录
     *
     * @param filename 文件名
     * @param bizType  商业类型
     * @return {@link Long}
     */
    public Long createDownloadRecord(String filename, DownloadBizTypeEnum bizType) {
        DownloadCenterInitReq req = new DownloadCenterInitReq();
        req.setFileExpiredDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY);
        req.setBizType(bizType.getBizType());
        req.setFileName(filename);
        req.setAdminId(Long.valueOf(getAdminId()));
        DubboResponse<DownloadCenterResp> response = downloadCenterProvider.initRecord(req);
        if (!response.isSuccess()) {
            throw new BizException("网络波动，请重试");
        }
        return response.getData().getResId();
    }


    /**
     * 校验导入结果
     *
     * @param normalIdList 正常id列表
     * @param errorList    错误列表
     * @param importList   导入列表
     * @param reason       原因
     * @return {@link List}<{@link TaskImportExcelInput}>
     */
    public List<TaskImportExcelInput> check(List<Integer> normalIdList, List<TaskImportResultVo> errorList, List<TaskImportExcelInput> importList, String reason) {
        // 错误数据
        List<TaskImportResultVo> notExistList = importList.stream().filter(ip -> !normalIdList.contains(ip.getMId())).map(ip -> {
            TaskImportResultVo detailVo = new TaskImportResultVo();

            detailVo.setMId(ip.getMId());
            detailVo.setMname(ip.getMname());
            detailVo.setReason(reason);
            return detailVo;
        }).collect(Collectors.toList());
        errorList.addAll(notExistList);

        // 正确数据
        return importList.stream().filter(i -> normalIdList.contains(i.getMId())).collect(Collectors.toList());
    }
}
