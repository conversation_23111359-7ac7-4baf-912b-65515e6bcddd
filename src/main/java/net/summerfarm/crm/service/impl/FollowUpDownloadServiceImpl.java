package net.summerfarm.crm.service.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.string.StringStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.ExcelUtils;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.mapper.manage.CrmBdOrgMapper;
import net.summerfarm.crm.mapper.manage.CrmEscortVisitPlanMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRecordMapper;
import net.summerfarm.crm.mapper.manage.MerchantKeyPersonMapper;
import net.summerfarm.crm.mapper.repository.MerchantExtendsRepository;
import net.summerfarm.crm.model.convert.FollowUpRecordDownLoadDtoConverter;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CrmEscortVisitPlan;
import net.summerfarm.crm.model.domain.MerchantKeyPerson;
import net.summerfarm.crm.model.dto.FollowUpRecordDownLoadDto;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.service.FollowUpDownloadService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.constant.SystemConstant.XM_TENANT_ID;

@Service
@Slf4j
public class FollowUpDownloadServiceImpl extends BaseService implements FollowUpDownloadService {


    @Resource(name = "asycExecutor")
    public Executor executor;
    @Resource
    private FollowUpRecordMapper followUpRecordMapper;
    @Resource
    private MerchantKeyPersonMapper merchantKeyPersonMapper;
    @Resource
    private CrmBdOrgMapper crmBdOrgMapper;

    private static final String CRM_FOLLOW_UP_DOWN_EXCEL_NAME = "crm_follow_up_down.xlsx";
    private static final Integer CRM_FOLLOW_UP_DOWN_EXCEL_TYPE = 400;
    @Resource
    private MerchantExtendsRepository merchantExtendsRepository;
    @Resource
    private CrmEscortVisitPlanMapper crmEscortVisitPlanMapper;

    @Override
    public Long exportFollowUpRecord(UserBase userBase,FollowUpRecordVO selectKeys) {
        DownloadCenterRecordDTO recordDTO = new DownloadCenterRecordDTO();
        recordDTO.setSource(DownloadCenterEnum.RequestSource.XIANMU);
        recordDTO.setBizType(CRM_FOLLOW_UP_DOWN_EXCEL_TYPE);
        recordDTO.setTenantId(XM_TENANT_ID);
        recordDTO.setFileName(selectKeys.getFileName());
        recordDTO.setParams(JSON.toJSONString(selectKeys));
        recordDTO.setExpiredDayLabel(OSSExpiredLabelEnum.MONTH);
        recordDTO.setUserId(userBase.getBizUserId().longValue());
        return DownloadCenterHelper.build(executor, recordDTO).asyncWriteWithOssResp(selectKeys, ee -> {
            // 1、表格处理
            String filePath = generateStoreOrderDelayAnalysisReport(selectKeys);

            // 2、文件上传至oss
            OssUploadResult uploadResult = null;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(new File(filePath)), OSSExpiredLabelEnum.MONTH);
            } catch (IOException e) {
                log.error("filePath={}", filePath, e);
                throw new BizException("读取文件报错");
            } finally {
                try {
                    Files.delete(Paths.get(filePath));
                } catch (IOException e) {
                    deleteFile(filePath);
                }
            }
            // 3、返回文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });
    }

    public void deleteFile(String filePath) {
        try {
            Files.delete(Paths.get(filePath));
        } catch (IOException e) {
            throw new DefaultServiceException("文件下载失败！");
        }
    }
    public String generateStoreOrderDelayAnalysisReport(FollowUpRecordVO input) {
        InputStream templateFileInputStream = ExcelUtils.getExcelFileInputStream(this.getClass(), CRM_FOLLOW_UP_DOWN_EXCEL_NAME);
        String filePath = ExcelUtils.tempExcelFilePath();
        ExcelWriter excelWriter = EasyExcel.write(filePath).registerConverter(new StringStringConverter()).withTemplate(templateFileInputStream).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
        int pageIndex = 1;
        //查询数据
        PageInfo<FollowUpRecordDownLoadDto> pageInfo;
        do {
            pageInfo = pageInfo(input, pageIndex, 100);
            excelWriter.fill(pageInfo.getList(), fillConfig, writeSheet);
            pageIndex++;
        } while (pageInfo.isHasNextPage());

        excelWriter.finish();
        return filePath;
    }


    public PageInfo<FollowUpRecordDownLoadDto> pageInfo(FollowUpRecordVO input, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<FollowUpRecordVO> followUpRecordVOS = followUpRecordMapper.selectByStart(input);
        PageInfo<FollowUpRecordVO> pageInfo = PageInfoHelper.createPageInfo(followUpRecordVOS);
        PageInfo<FollowUpRecordDownLoadDto> followUpRecordDownLoadDtoPageInfo = PageInfoConverter.toPageResp(pageInfo, FollowUpRecordDownLoadDtoConverter::convert);
        convert(followUpRecordDownLoadDtoPageInfo.getList());
        return followUpRecordDownLoadDtoPageInfo;
    }

    private void convert(List<FollowUpRecordDownLoadDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        // getEscortAdminId
        List<Integer> escortAdminIds = dtos.stream().map(FollowUpRecordDownLoadDto::getEscortAdminId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> escortPlanIds = dtos.stream().filter(Objects::nonNull).map(FollowUpRecordDownLoadDto::getEscortVisitPlanId).distinct().collect(Collectors.toList());
        List<Long> visitPlanIds = dtos.stream().filter(Objects::nonNull).map(FollowUpRecordDownLoadDto::getVisitPlanId).distinct().collect(Collectors.toList());

        Map<Long, String> visitPlanUserNameMap = getVisitPlanUserNameMap(visitPlanIds);
        Map<Long, String> escortVisitPlanIdUserNameMap = getEscortVisitPlanIdUserNameMap(escortPlanIds);

        Map<Integer, String> bdRealNames = getBdRealNames(escortAdminIds);
        List<Long> mids = dtos.stream().map(FollowUpRecordDownLoadDto::getMerchantId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //门店信息
        Map<Long, MerchantStoreAndExtendDTO> merchantMap = merchantExtendsRepository.getMerchantMap(mids);
        //m1 m2
        List<Integer> adminIds = dtos.stream().map(FollowUpRecordDownLoadDto::getAdminId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //查询他的m1
        Map<Integer, CrmBdOrg> m1BdMap = getCrmBdOrgMap(adminIds, BdAreaConfigEnum.SaleRank.BD);

        //查询他的m2
        List<Integer> orgIdS = m1BdMap.values().stream().map(CrmBdOrg::getParentId).distinct().collect(Collectors.toList());
        Map<Integer, CrmBdOrg> m2BdMap = getCrmBdOrgMap(orgIdS);

        // getKpId
        List<Long> kpIds = dtos.stream().map(FollowUpRecordDownLoadDto::getKpId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, MerchantKeyPerson> kpMap = getKpMap(kpIds);
        dtos.forEach(
                it -> {
                    if (it.getEscortAdminId() != null) {
                        it.setEscortAdminName(bdRealNames.get(it.getEscortAdminId()));
                    }
                    if (it.getKpId() != null) {
                        MerchantKeyPerson merchantKeyPerson = kpMap.get(it.getKpId());
                        if (merchantKeyPerson != null) {
                            it.setKpName(merchantKeyPerson.getPersonName() + "-" + merchantKeyPerson.getPhone());
                        }
                    }
                    if (it.getAdminId() != null) {
                        CrmBdOrg crmBdOrg = m1BdMap.get(it.getAdminId());
                        if (crmBdOrg != null) {
                            it.setM1(crmBdOrg.getParentName());
                            CrmBdOrg m2bd = m2BdMap.get(crmBdOrg.getParentId());
                            if (m2bd != null) {
                                it.setM2(m2bd.getParentName());
                            }
                        }
                    }
                    if (it.getMerchantId()!=null){
                        MerchantStoreAndExtendDTO merchantStoreAndExtendDTO = merchantMap.get(it.getMerchantId());
                        if (merchantStoreAndExtendDTO  != null){
                            it.setMname(merchantStoreAndExtendDTO.getStoreName());
                            it.setAreaName(merchantStoreAndExtendDTO.getCity());
                        }
                    }
                    if (StringUtils.isEmpty(it.getEscortAdminName())) {
                        if (it.getVisitPlanId() != null && it.getVisitPlanId() > 0) {
                            it.setEscortAdminName(visitPlanUserNameMap.get(it.getVisitPlanId()));
                        }
                        if (it.getEscortVisitPlanId() != null && it.getEscortVisitPlanId() > 0) {
                            it.setEscortAdminName(escortVisitPlanIdUserNameMap.get(it.getEscortVisitPlanId()));
                        }
                    }
                }
        );
    }

    public Map<Long, MerchantKeyPerson> getKpMap(List<Long> kpIds) {
        if (CollectionUtils.isEmpty(kpIds)) {
            return new HashMap<>();
        }
        return merchantKeyPersonMapper.selectByIds(kpIds).stream().collect(Collectors.toMap(MerchantKeyPerson::getId, Function.identity()));
    }

    public Map<Integer, CrmBdOrg> getCrmBdOrgMap(List<Integer> kpIds, Integer rank) {
        if (CollectionUtils.isEmpty(kpIds)) {
            return new HashMap<>();
        }
        return crmBdOrgMapper.listAdminIdRank(kpIds, rank).stream().collect(Collectors.toMap(CrmBdOrg::getBdId, Function.identity()));
    }


    public Map<Integer, CrmBdOrg> getCrmBdOrgMap(List<Integer> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return new HashMap<>();
        }
        return crmBdOrgMapper.listByIds(orgIds).stream().collect(Collectors.toMap(CrmBdOrg::getId, Function.identity()));
    }


    public Map<Long, String> getVisitPlanUserNameMap(List<Long> visitPlanIds) {
        if (CollectionUtils.isEmpty(visitPlanIds)) {
            return new HashMap<>();
        }
        List<CrmEscortVisitPlan> visitPlans = crmEscortVisitPlanMapper.selectByVisitPlanIds(visitPlanIds);
        if (CollectionUtils.isEmpty(visitPlans)) {
            return new HashMap<>();
        }
        List<Integer> visitAdminIds = visitPlans.stream().filter(it -> it.getAdminId() != null && it.getAdminId() > 0).map(u -> u.getAdminId().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(visitAdminIds)) {
            return new HashMap<>();
        }
        Map<Integer, String> bdRealNames = getBdRealNames(visitAdminIds);
        Map<Long, String> map = new HashMap<>();
        visitPlans.forEach(
                it -> {
                    if (it.getAdminId() != null && bdRealNames.get(it.getAdminId().intValue()) != null) {
                        map.put(it.getVisitPlanId(), bdRealNames.get(it.getAdminId().intValue()));
                    }
                }
        );
        return map;
    }

    public Map<Long, String> getEscortVisitPlanIdUserNameMap(List<Long> escortVisitPlanIds) {
        if (CollectionUtils.isEmpty(escortVisitPlanIds)) {
            return new HashMap<>();
        }
        List<CrmEscortVisitPlan> crmEscortVisitPlans = crmEscortVisitPlanMapper.selectByIds(escortVisitPlanIds);
        if (CollectionUtils.isEmpty(crmEscortVisitPlans)) {
            return new HashMap<>();
        }
        List<Integer> visitAdminIds = crmEscortVisitPlans.stream().filter(it -> it.getAdminId() != null && it.getAdminId() > 0).map(u -> u.getAdminId().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(visitAdminIds)) {
            return new HashMap<>();
        }
        Map<Integer, String> bdRealNames = getBdRealNames(visitAdminIds);
        Map<Long, String> map = new HashMap<>();
        crmEscortVisitPlans.forEach(
                it -> {
                    if (it.getAdminId() != null && bdRealNames.get(it.getAdminId().intValue()) != null) {
                        map.put(it.getId(), bdRealNames.get(it.getAdminId().intValue()));
                    }
                }
        );
        return map;
    }
}
