package net.summerfarm.crm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.enums.ExamineEnum;
import net.summerfarm.crm.facade.OrderQueryFacade;
import net.summerfarm.crm.facade.WncQueryFacade;
import net.summerfarm.crm.mapper.manage.SampleApplyMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.mapper.offline.MerchantPurchaseDataMapper;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import net.summerfarm.crm.service.BatchApplicationSamplesService;
import net.summerfarm.crm.service.SampleApplyReviewService;
import net.summerfarm.crm.service.SampleApplyService;
import net.summerfarm.mall.client.resp.AutoCreateSampleResp;
import net.summerfarm.wnc.client.enums.SourceEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/29 15:55
 * @PackageName:net.summerfarm.crm.service.impl
 * @ClassName: BatchApplicationSamplesServiceImpl
 * @Description: TODO
 * @Version 1.0
 */
@Service
@Slf4j
public class BatchApplicationSamplesServiceImpl implements BatchApplicationSamplesService {

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Resource
    private MerchantPurchaseDataMapper merchantPurchaseDataMapper;

    @Resource
    private OrderQueryFacade orderQueryFacade;

    @Resource
    private SampleApplyService sampleApplyService;

    @Resource
    private SampleApplyReviewService sampleApplyReviewService;

    @Resource
    private WncQueryFacade wncQueryFacade;

    @Resource
    private SampleApplyMapper sampleApplyMapper;

    @NacosValue(value = "${autoCreateSampleSku:737545156111}",autoRefreshed = true)
    private String sku;

    @NacosValue(value = "${autoCreateSampleAmount:1}",autoRefreshed = true)
    private Integer amount;

    private static final int MAX_SIZE = 50;

    @Override
    public void autoCreateSampleTask() {
        //客户口径：最近90天累计履约【后台类目为搅打型稀奶油】的 SKU超过10件且门店类型为【面包蛋糕】且之前未下单过保温袋【SPU：737545156】，
        //且当日有待配送订单且无对应样品SKU派送记录（SKU：737545156111），且运营区域：杭州大区，上海大区，苏南大区，苏州大区，广州大区

        //配送地址：取待配送订单相同地址，若有多条，则取创建时间最早的地址
        //配送时间：跟随待配送订单配送日期
        //样品SKU：737545156111
        //样品数量：1
        //样品单创建后自动审核通过
        //脚本执行时间：城配仓截单时间前15分钟，如22点截单，则脚本执行时间为：21：45 分
        //当库存不足时，则创建失败
        DataSynchronizationInformation dataSynchronizationInformation = dataSynchronizationInformationMapper.
                selectByTableName(DataSynchronizationInformationEnum.MERCHANT_PURCHASE_DATA.getTableName());
        if (dataSynchronizationInformation == null || dataSynchronizationInformation.getDateFlag() == null) {
            log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]dataSynchronizationInformation is null!");
            return;
        }
        List<MerchantPurchaseData> merchantPurchaseDataList = merchantPurchaseDataMapper.selectByDayTag(dataSynchronizationInformation.getDateFlag());
        if (CollectionUtils.isEmpty(merchantPurchaseDataList)) {
            log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]merchantPurchaseDataList is empty! dateFlag:{}",
                    dataSynchronizationInformation.getDateFlag());
            return;
        }

        //无对应样品SKU派送记录（SKU：737545156111）-- 超过50个分批查询
        List<Long> mIds = merchantPurchaseDataList.stream().map(MerchantPurchaseData::getMId).collect(Collectors.toList());
        List<List<Long>> partitionList = Lists.partition(mIds, MAX_SIZE);
        for (List<Long> subList : partitionList) {
            Set<Long> sampleApplyMidList = sampleApplyMapper.getSampleInfoBySku(subList, sku);
            Set<Long>  mIdsToApplySample = subList.stream().filter(mId -> !sampleApplyMidList.contains(mId)).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(mIdsToApplySample)) {
                log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]mIdsToApplySample is empty!subList:{}", JSON.toJSONString(subList));
                continue;
            }

            //根据客户ID批量过滤--未下单过保温袋【SPU：737545156】、且当日有待配送订单且无对应样品SKU派送记录（SKU：737545156111）
            //配送地址：取待配送订单相同地址，若有多条，则取创建时间最早的地址
            List<AutoCreateSampleResp> autoCreateSampleResps = orderQueryFacade.queryOrderInfoByAutoCreateSampleTask(mIdsToApplySample);
            if (CollectionUtils.isEmpty(autoCreateSampleResps)) {
                log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]autoCreateSampleResps is empty! mIdsToApplySample:{}", JSON.toJSONString(mIdsToApplySample));
                continue;
            }

            //申请样品SKU：737545156111 样品数量：1
            createSamples(merchantPurchaseDataList, autoCreateSampleResps);
        }
    }

    /**
    * @description 批量创建样品单
    * @params [merchantPurchaseDataList, autoCreateSampleResps]
    * @return void
    * <AUTHOR>
    * @date  2024/9/6 10:07
    */
    private void createSamples(List<MerchantPurchaseData> merchantPurchaseDataList, List<AutoCreateSampleResp> autoCreateSampleResps) {
        Map<Long, MerchantPurchaseData> purchaseDataMap = merchantPurchaseDataList.stream().collect(Collectors.toMap(MerchantPurchaseData::getMId,
                Function.identity(), (v1, v2) -> v1));
        LocalTime nowLocalTime = LocalTime.now();
        for (AutoCreateSampleResp autoCreateSampleResp : autoCreateSampleResps) {
            MerchantPurchaseData merchantPurchaseData = purchaseDataMap.get(autoCreateSampleResp.getMId());
            if (merchantPurchaseData == null) {
                log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]merchantPurchaseData is null! autoCreateSampleResp:{}",
                        JSON.toJSONString(autoCreateSampleResp));
                continue;
            }
            try {
                LocalTime localTime = wncQueryFacade.queryCloseTime(autoCreateSampleResp.getContactId(), SourceEnum.XM_SAMPLE_APPLY);
                if (localTime == null) {
                    log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]queryCloseTime is null! autoCreateSampleResp:{}",
                            JSON.toJSONString(autoCreateSampleResp));
                    continue;
                }
                if (nowLocalTime.isAfter(localTime.minusMinutes(10))) {
                    log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]error--now is after localTime! autoCreateSampleResp:{}, nowLocalTime:{}, localTime:{}",
                            JSON.toJSONString(autoCreateSampleResp), JSON.toJSONString(nowLocalTime), JSON.toJSONString(localTime));
                    continue;
                }

                //样品单创建后自动审核通过
                SampleApplyVO sampleApplyVO = new SampleApplyVO();
                sampleApplyVO.setAreaNo(merchantPurchaseData.getAreaNo());
                sampleApplyVO.setmId(autoCreateSampleResp.getMId());
                sampleApplyVO.setContactId(autoCreateSampleResp.getContactId().intValue());
                sampleApplyVO.setmContact(autoCreateSampleResp.getMContact());
                sampleApplyVO.setmPhone(autoCreateSampleResp.getMPhone());
                sampleApplyVO.setRemark("后台定时任务-自动创建样品单");
                SampleSku sampleSku = new SampleSku();
                sampleSku.setSku(sku);
                sampleSku.setAmount(amount);
                sampleApplyVO.setSampleSkuList(Collections.singletonList(sampleSku));
                AjaxResult insertSampleApply = sampleApplyService.insertSampleApply(sampleApplyVO);
                if (!insertSampleApply.isSuccess()) {
                    log.warn("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]insertSampleApply error! sampleApplyVO:{}, insertSampleApply:{}",
                            JSON.toJSONString(sampleApplyVO), JSON.toJSONString(insertSampleApply));
                    continue;
                }
                Integer sampleId = (Integer) insertSampleApply.getData();
                SampleApplyReview sampleApplyReview = new SampleApplyReview();
                sampleApplyReview.setSampleId(sampleId);
                sampleApplyReview.setReviewRemark("后台定时任务-自动审核样品单");
                sampleApplyReview.setStatus(ExamineEnum.SampleApplyReview.SUCCESS.ordinal());
                AjaxResult applyReview = sampleApplyReviewService.sampleApplyReview(sampleApplyReview);
                if (!applyReview.isSuccess()) {
                    log.warn("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]sampleApplyReview error! sampleApplyReview:{}, applyReview:{}",
                            JSON.toJSONString(sampleApplyReview), applyReview.getMsg());
                    continue;
                }

                //将配送日期设置为和订单同一天
                SampleApply sampleApply = new SampleApply();
                sampleApply.setDeliveryTime(autoCreateSampleResp.getDeliveryDate());
                sampleApply.setSampleId(sampleId);
                sampleApply.setUpdateTime(new Date());
                sampleApply.setStoreNo(autoCreateSampleResp.getStoreNo());
                int updateSampleApply = sampleApplyMapper.updateSampleApply(sampleApply);
                if (updateSampleApply > 0) {
                    log.info("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]updateSampleApply success! sampleApply:{}, autoCreateSampleResp:{}",
                            JSON.toJSONString(sampleApply), JSON.toJSONString(autoCreateSampleResp));
                }
            } catch (Exception e) {
                log.warn("BatchApplicationSamplesServiceImpl[]autoCreateSampleTask[]insert or review error! autoCreateSampleResp:{}, cause:{}",
                        JSON.toJSONString(autoCreateSampleResp), e);
                continue;
            }
        }
    }
}
