package net.summerfarm.crm.service;

import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CrmBdTodayHourGmv;
import net.summerfarm.crm.model.domain.CrmCityTodayHourGmv;
import net.summerfarm.crm.model.domain.CrmSalesCity;
import net.summerfarm.crm.model.vo.saledata.*;

import java.util.List;
import java.util.Map;

/**
 * 销售数据服务V2
 */
public interface SalesDataServiceV2 {

    /**
     * 获取BD今日数据
     * 普通销售看的是自己的数据.
     */
    CrmBdTodayHourGmv fetchTodayDataForBd(CrmBdOrg bdOrg);

    /**
     * 获取主管版今日数据
     * M1及以上看的是管辖所有城市数据之和.数据来源于CRM_CITY_TODAY_HOUR_GMV表
     */
    CrmCityTodayHourGmv fetchTodayDataForManager(CrmBdOrg bdOrg);

    /**
     * 为主管获取所管辖销售区域的数据.
     * 区域数据实则为城市数据之和.
     */
    List<CrmAreaDataVO> fetchPerformancePerAreaForManager(String timeType);

    /**
     * 获取区域内每个城市的业绩
     *
     * @param salesAreaId 销售区域ID
     * @param timeType    时间类型 day/month
     */
    List<CrmCityDataVO> fetchPerformancePerCityInArea(Integer salesAreaId, String timeType);

    /**
     * 获取区域内每个BD的业绩
     *
     * @param salesAreaId 销售区域ID
     * @param timeType    时间类型 day/month
     */
    List<CrmBdDataVO> fetchPerformancePerBdInArea(Integer salesAreaId, String timeType);
}
