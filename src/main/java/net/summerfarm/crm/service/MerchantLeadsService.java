package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.MerchantLeads;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.vo.AreaVO;
import net.summerfarm.crm.model.vo.MerchantLeadsVO;
import net.xianmu.common.result.CommonResult;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface MerchantLeadsService {
    /**
     * 查询线索池
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param selectKeys 查询条件
     * @return 线索池信息
     */
    AjaxResult selectMerchantLeads(int pageIndex, int pageSize, MerchantLeads selectKeys);

    /**
     * 更新线索池
     * @param merchantLeads 线索池信息
     * @return ok
     */
    AjaxResult saveMerchantLeads(MerchantLeadsVO merchantLeads);


    CommonResult<LargeAreaDTO> queryPopAreaNo();

}
