package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.FollowUpRecord;
import net.summerfarm.crm.model.dto.AdminTurningConfigDto;
import net.summerfarm.crm.model.dto.FollowUpConfigDto;
import net.summerfarm.crm.model.query.task.TaskFollowUpQuery;
import net.summerfarm.crm.model.vo.AdminTurningConfigVO;
import net.summerfarm.crm.model.vo.FeedBackVO;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import org.apache.poi.ss.usermodel.Workbook;
import java.util.List;
import javax.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface FollowUpRecordService {

    /**
     * 新增工单
     * @param record 拜访记录内容
     * @return ok
     */
    AjaxResult saveRecord(FollowUpRecordVO record, boolean isTask);

    /**
     * 修改工单
     * @param record 拜访内容
     * @return ok
     */
    AjaxResult updateRecord(FollowUpRecordVO record);

    /**
     * 分页查询拜访记录
     * @param pageIndex 页码
     * @param pageSize  数量
     * @param type 来源
     * @param selectKeys 查询条件
     * @return 拜访记录
     */
    AjaxResult selectRecord(int pageIndex, int pageSize, int type, FollowUpRecordVO selectKeys);

    /**
     * 查询拜访记录详情
     * @param id 拜访记录id
     * @return 拜访记录详情
     */
    AjaxResult selectDetail(Long id);

    /**
    * 每天发送拜访记录邮件
    */
    void autoSendMsg();

    /**
     * 获取商户备注
     * @param mId 商户id
     * @param couponId 卡券id
     * @return 商户备注
     */
    AjaxResult noteDetails(int mId, Integer couponId);

    /**
     * 导出拜访记录
     * @param selectKeys 筛选条件
     * @return ok
     */
    AjaxResult exportFollowUpRecord(FollowUpRecordVO selectKeys);


    CommonResult exportFollowUpRecordNew(UserBase userBase, FollowUpRecordVO selectKeys);
    /**
     * 导出拜访记录excel
     * @param followUpRecords 信息
     * @return  excel
     */
    Workbook handleFollowMsg(List<FollowUpRecordVO> followUpRecords);

    /**
     * 定时发拜访记录钉钉群消息
     */
    void sendDingMessage();

    /**
     * 指定时间上一小时拜访记录
     *
     * @param response
     * @param datetime 时间
     * @param token 钉钉通知token，用于参数校验
     */
    void followUpHourRecord(HttpServletResponse response, String datetime,String token);

    CommonResult feedback(FeedBackVO feedBackVO);

    CommonResult<AdminTurningConfigDto> turningConfig(AdminTurningConfigVO adminTurningConfigVO);

    /**
     * 填充门店/运营服务区域名称
     *
     * @param followUpRecordVOList 跟进录音清单
     */
    void fillMerchantAndAreaName(List<FollowUpRecordVO> followUpRecordVOList);

    /**
     * 查询任务拜访
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link FollowUpRecord}>
     */
    CommonResult<FollowUpRecord> selectTaskFollowUp(TaskFollowUpQuery query);

    CommonResult<FollowUpConfigDto> followUpConfigDto(AdminTurningConfigVO adminTurningConfigVO);


    List<String> getFruitTags(Long mId);

    /**
     * 判断用户是否有导入拜访记录的权限
     * 
     * @param adminId 用户id
     * @return
     */
    boolean hasImportFollowUpRecordPermission(Integer adminId);
}
