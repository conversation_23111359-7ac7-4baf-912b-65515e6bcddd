package net.summerfarm.crm.service;

import net.summerfarm.crm.model.query.wecom.WeComBdQuery;
import net.summerfarm.crm.model.vo.weCom.*;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 企微数据看板
 *
 * <AUTHOR>
 * @date 2024/2/23 10:30
 */
public interface WeComDataBoardService {
    /**
     * 同步企微账号状态
     */
    void weComStateSync();

    /**
     * 客户联系数据
     */
    void userBehaviorData();

    /**
     * 群发任务统计
     */
    void groupMsgData();

    /**
     * 企微激活状态
     *
     * @param query 查询。
     * @return {@link List}<{@link WeComActivateVo}>
     */
    List<WeComActivateVo> activateStatus(WeComBdQuery query);

    /**
     * m1 下属激活状态看板
     *
     * @return {@link CommonResult}<{@link List}<{@link WeComActivateStatusVo}>>
     */
    List<WeComActivateStatusVo> activateStatusList(WeComBdQuery query);

    /**
     * 企微拉新用户看板
     *
     * @param query 查询。
     * @return {@link CommonResult}<{@link List}<{@link WeComUserSummaryVo}>>
     */
    List<WeComUserSummaryVo> userSummary(WeComBdQuery query);

    /**
     * 销售&客户沟通看板
     *
     * @param query 查询。
     * @return {@link CommonResult}<{@link List}<{@link WeComCommunicationSummaryVo}>>
     */
    List<WeComCommunicationSummaryVo> communicationSummary(WeComBdQuery query);

    /**
     * 企微营销任务
     *
     * @param query 查询。
     * @return {@link CommonResult}<{@link WeComTaskSummaryVo}>
     */
    List<WeComTaskSummaryVo> taskSummary(WeComBdQuery query);
}
