package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.dto.SkuQueryDTO;
import net.summerfarm.crm.model.vo.ProductsVO;
import net.summerfarm.mall.client.req.HelpOrderProductListQueryReq;
import net.summerfarm.mall.client.resp.HelpOrderProductInfoQueryResp;
import net.summerfarm.manage.client.wms.dto.res.ProductsDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * 商品服务
 *
 * <AUTHOR>
 * @date 2023/1/5 15:30
 */
public interface ProductsService {

    /**
     * 商品名模糊查询pdId
     *
     * @param pdName pdName
     * @return 商品名和pdId
     */
    List<ProductsVO> search(String pdName);

    /**
     * 查询 sku 列表
     *
     * @return {@link CommonResult}<{@link PageInfo}<{@link HelpOrderProductInfoQueryResp}>>
     */
    CommonResult<PageInfo<HelpOrderProductInfoQueryResp>> selectSkuList(HelpOrderProductListQueryReq req);
}
