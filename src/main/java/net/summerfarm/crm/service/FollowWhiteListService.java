package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.FollowWhiteList;
import net.summerfarm.crm.model.vo.FollowWhiteListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface FollowWhiteListService {

    /**
     * 新增白名单
     * @param followWhiteList 内容
     * @return ok
     */
    AjaxResult saveWhiteList(FollowWhiteList followWhiteList);

    /**
     * 更新白名单
     * @param mId mid
     * @return ok
     */
    AjaxResult update(Long mId);

    /**
     * 白名单列表查询
     * @param pageIndex 页数
     * @param pageSize 数量
     * @param followWhiteListVO 查询条件
     * @return 白名单列表
     */
    AjaxResult queryWhiteList(int pageIndex, int pageSize, FollowWhiteListVO followWhiteListVO);

    /**
     * 移除白名单
     * @param mId 商户id
     * @return ok
     */
    AjaxResult deleteWhiteList(Long mId);


    /**
     * 统计bd 白名单客户个数
     *
     * @param adminId 管理员id
     * @param areaNos 区域号
     * @return int
     */
    int selectNumByBd(Integer adminId, List<Integer> areaNos);
}
