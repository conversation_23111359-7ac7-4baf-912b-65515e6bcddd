package net.summerfarm.crm.service;

import net.summerfarm.crm.model.bo.DingTalkMsgBO;
import net.summerfarm.crm.model.bo.DingTalkMsgReceiverIdBO;

/**
 * <AUTHOR>
 * @describe
 * @create 2022-08-28 23:07
 */
public interface DingTalkMsgSender {

    /**
     * 发送钉钉消息 发送给个人,通过mq消息发送
     * @param dingTalkMsgBO
     */
    void sendMessage(DingTalkMsgBO dingTalkMsgBO);

    /**
     * 飞书消息兼容
     * @param msgBO 消息体
     */
    void sendMessageWithFeiShu(DingTalkMsgReceiverIdBO msgBO);
}
