package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.MerchantKeyPerson;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 13:50
 */
public interface MerchantKeyPersonService {

    /**
     * 查询商户的kp信息
     * @param mId 商户id
     * @return 商户的kp信息
     */
    AjaxResult selectKeyPerson(Long mId);

    /**
     * 插入|更新商户kp信息
     * @param merchantKeyPerson 商户kp信息
     * @return 0|1
     */
    AjaxResult updateKeyPerson(MerchantKeyPerson merchantKeyPerson);
}
