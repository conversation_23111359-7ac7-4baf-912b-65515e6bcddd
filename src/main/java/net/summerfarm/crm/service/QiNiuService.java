package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: QiNiuService 七牛云
 * @date 2021/12/15 13:48
 */
public interface QiNiuService {

    /**
     * 七牛云上传文件
     * @param fileName
     * @param workbook
     * @return
     */
    AjaxResult uploadFile(String fileName, Workbook workbook);

    /**
     * 七牛云上传多个excel的压缩文件
     * @param workbooks
     * @param fileName
     * @return
     */
    AjaxResult uploadZip(Map<String, Workbook> workbooks, String fileName) throws IOException;
    

}
