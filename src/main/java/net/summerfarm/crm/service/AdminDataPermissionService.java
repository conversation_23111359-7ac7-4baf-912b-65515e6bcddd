package net.summerfarm.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.crm.mapper.manage.AdminDataPermissionMapper;
import net.summerfarm.pojo.DO.AdminDataPermission;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class AdminDataPermissionService {
    @Resource
    private AdminDataPermissionMapper adminDataPermissionMapper;

    public Set<Integer> selectDataPermissionIds(Integer adminId) {
        List<AdminDataPermission> permissions = adminDataPermissionMapper.selectByAdminId(adminId);

        //无数据权限时默认-1，兼容sql判断逻辑
        if (CollectionUtil.isEmpty(permissions)){
            return CollectionUtil.newHashSet(-1);
        }

        return permissions.stream().map(el -> Integer.valueOf(el.getPermissionValue())).collect(Collectors.toSet());
    }
}
