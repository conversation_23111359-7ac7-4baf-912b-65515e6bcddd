package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.vo.WarehouseBatchProveRecordVO;
import net.summerfarm.crm.model.vo.WarehouseStorageVO;
import net.summerfarm.manage.client.wms.dto.req.WarehouseQueryReq;
import net.summerfarm.manage.client.wms.dto.res.WarehouseBatchProveRecordDTO;
import net.summerfarm.manage.client.wms.dto.res.WarehouseStorageDTO;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * 库存信息
 *
 * <AUTHOR>
 * @date 2023/1/5 15:31
 */
public interface WarehouseService {
    /**
     * 库存列表查询
     *
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param param     查询条件
     * @return 仓库库存信息
     * @Author: yefeng
     **/
    PageInfo<WarehouseStorageVO> inventoryList(Integer pageIndex, Integer pageSize, WarehouseQueryReq param);

    /**
     * 证明信息
     *
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param param     查询条件
     * @return
     * @Author: yefeng
     **/
    PageInfo<WarehouseBatchProveRecordVO> proveList(Integer pageIndex, Integer pageSize, WarehouseQueryReq param);
}
