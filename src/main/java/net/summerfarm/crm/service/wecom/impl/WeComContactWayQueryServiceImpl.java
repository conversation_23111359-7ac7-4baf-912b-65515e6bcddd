package net.summerfarm.crm.service.wecom.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.client.enums.BdQrCodeQueryChannelEnum;
import net.summerfarm.crm.enums.WeComContactWayEnum;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.MerchantLeads;
import net.summerfarm.crm.model.domain.WeComUserContactWay;
import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.service.wecom.WeComContactWayQueryService;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static net.summerfarm.crm.enums.WeComContactWayEnum.State.*;

@Slf4j
@Service
public class WeComContactWayQueryServiceImpl implements WeComContactWayQueryService {

    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private MerchantLeadsMapper merchantLeadsMapper;
    @Resource
    private WeComUserContactWayMapper wecomUserContactWayMapper;
    @Resource
    private WecomUserInfoMapper wecomUserInfoMapper;
    @Resource
    private CrmBdOrgMapper crmBdOrgMapper;

    @Override
    public String getQrCodeForMerchant(Long mId, BdQrCodeQueryChannelEnum channel) {
        if (mId == null) {
            throw new ParamsException("mId不能为空");
        }
        MerchantVO merchant = merchantMapper.selectMerchantByMid(mId);
        if (merchant == null) {
            log.error("未能获取商户信息, mId: {}", mId);
            throw new BizException("未能获取商户信息");
        }

        switch (channel) {
            case HOME_PAGE_POP_UP:
                return handleExistingCustomer(merchant, HOMEPAGE_POP_UP);
            case PERSONAL_CENTER:
                return handleExistingCustomer(merchant, PERSONAL_CENTER);
            default:
                return handleRegister(merchant);
        }
    }


    private String handleRegister(MerchantVO merchant) {
        Integer cluePool = merchant.getCluePool();
        // 自主注册, 返回区域M1BD的二维码
        if (cluePool == null || cluePool == 0) {
            return getAreaM1BdQrCode(merchant.getCity(), merchant.getArea(), SELF_REGISTRATION);
        }
        // 销售地推注册, 根据线索池里的门店和销售关系,获取发放地推码销售的二维码
        // 如果该销售没有二维码, 返回区域M1BD的二维码
        else {
            MerchantLeads merchantLeads = merchantLeadsMapper.selectByMId(merchant.getmId().intValue());

            if (merchantLeads == null || merchantLeads.getAdminId() == null) {
                log.info("获取跟进bd失败，商户id: {}, merchantLeads: {}", merchant.getmId(), merchantLeads);
                return getAreaM1BdQrCode(merchant.getCity(), merchant.getArea(), SALES_INVITATION);
            }

            return getQrCodeForAdmin(merchantLeads.getAdminId(), merchant.getCity(), merchant.getArea(), SALES_INVITATION);
        }
    }


    private String handleExistingCustomer(MerchantVO merchant, WeComContactWayEnum.State state) {
        return merchant.isInPrivateSea() ?
                // 如果是私海客户,返回所属私海销售的企微.
                getQrCodeForAdmin(merchant.getFollowId(), merchant.getCity(), merchant.getArea(), state) :
                // 如果是公海客户,则返回城市M1销售的企微
                getAreaM1BdQrCode(merchant.getCity(), merchant.getArea(), state);
    }


    /**
     * 获取跟进销售企微二维码. 如果该销售没有企微, 则获取城市M1销售的企微
     */
    private String getQrCodeForAdmin(Integer adminId, String city, String area, WeComContactWayEnum.State state) {
        WeComUserContactWay contactWay = wecomUserContactWayMapper.selectByAdminIdAndState(adminId, state.getState());
        if (contactWay == null) {
            log.info("获取跟进销售的企微二维码失败, 请确认该销售是否有企微 adminId: {}", adminId);
            return getAreaM1BdQrCode(city, area, state);
        }
        return contactWay.getQrCode();
    }

    @InMemoryCache(expiryTimeInSeconds = 300)
    private String getAreaM1BdQrCode(String city, String area, WeComContactWayEnum.State state) {
        CrmBdOrg m1Bd = crmBdOrgMapper.selectM1ByCityAndArea(city, area);
        if (m1Bd == null) {
            throw new BizException(String.format("找不到区域: %s %s 的M1销售,请确认该区域是否配置了M1销售", city, area));
        }
        WeComUserContactWay contactWay = wecomUserContactWayMapper.selectByAdminIdAndState(m1Bd.getBdId(), state.getState());
        if (contactWay == null) {
            WecomUserInfo wecomUserInfo = wecomUserInfoMapper.selectByAdminId(Long.valueOf(m1Bd.getBdId()));
            if (wecomUserInfo == null || wecomUserInfo.getStatus() != 1) {
                throw new BizException(String.format("区域: %s %s 的M1销售adminId: %s的企微未激活,请催促相关销售尽快激活企微", city, area, m1Bd.getBdId()));
            }
            throw new BizException(String.format("区域: %s %s 的M1销售adminId: %s没有对应的企微二维码,请关注原因", city, area, m1Bd.getBdId()));
        }
        return contactWay.getQrCode();
    }
}
