package net.summerfarm.crm.service.wecom;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.crm.model.query.wecom.contactway.WeComAddContactWayInput;
import net.summerfarm.crm.model.query.wecom.contactway.ConfigIdInput;
import net.summerfarm.crm.model.vo.weCom.contactway.WeComAddContactWayResp;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;

public interface WeComContactWayService {

    /**
     * 配置客户联系「联系我」方式
     */
    WeComAddContactWayResp addContactWay(WeComAddContactWayInput input);

    /**
     * 删除企业已配置的「联系我」方式
     *
     * @param configId 企业联系方式的配置id
     */
    WeChatBaseResp deleteContactWay(ConfigIdInput configId);

    /**
     * 获取企业已配置的「联系我」方式
     * 返回信息说明:
     * errcode	返回码
     * errmsg	对返回码的文本描述内容
     * config_id	新增联系方式的配置id
     * type	联系方式类型，1-单人，2-多人
     * scene	场景，1-在小程序中联系，2-通过二维码联系
     * is_temp	是否临时会话模式，默认为false，true表示使用临时会话模式
     * remark	联系方式的备注信息，用于助记
     * skip_verify	外部客户添加时是否无需验证
     * state	企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详情”时会返回该参数值
     * style	小程序中联系按钮的样式，仅在scene为1时返回，详见附录
     * qr_code	联系二维码的URL，仅在scene为2时返回
     * user	使用该联系方式的用户userID列表
     * party	使用该联系方式的部门id列表
     * expires_in	临时会话二维码有效期，以秒为单位
     * chat_expires_in	临时会话有效期，以秒为单位
     * unionid	可进行临时会话的客户unionid
     * conclusions	结束语，可参考“结束语定义”
     *
     * @param input 企业联系方式的配置id
     */
    JSONObject getContactWay(ConfigIdInput input);
}
