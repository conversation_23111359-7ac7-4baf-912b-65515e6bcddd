package net.summerfarm.crm.service.wecom;

import net.summerfarm.crm.model.vo.weCom.media.WeComMediaUploadResp;

import java.io.File;

public interface WeComFileManagementService {

    /**
     * 素材上传得到media_id，该media_id仅三天内有效
     * 详情参考 <a href="https://developer.work.weixin.qq.com/document/path/90253">...</a>
     *
     * @param type 媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件（file）
     * @param file 待上传文件
     * @return 返回数据
     */
    WeComMediaUploadResp uploadMedia(String type, File file);

}
