package net.summerfarm.crm.service.wecom.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.mapper.manage.WeComUserContactWayMapper;
import net.summerfarm.crm.mapper.manage.WecomUserInfoMapper;
import net.summerfarm.crm.model.domain.WeComUserContactWay;
import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.model.dto.wecom.contactway.WeComAddContactWayInputDTO;
import net.summerfarm.crm.model.query.wecom.contactway.WeComAddContactWayInput;
import net.summerfarm.crm.model.query.wecom.contactway.ConfigIdInput;
import net.summerfarm.crm.model.vo.weCom.contactway.WeComAddContactWayResp;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;
import net.summerfarm.crm.service.wecom.WeComContactWayService;
import net.summerfarm.crm.service.WechatService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.*;

@Service
@Slf4j
public class WeComContactWayServiceImpl extends BaseService implements WeComContactWayService {

    @Resource
    private WechatService wechatService;
    @Resource
    private WeComUserContactWayMapper weComUserContactWayMapper;
    @Resource
    private WecomUserInfoMapper wecomUserInfoMapper;

    @Override
    public WeComAddContactWayResp addContactWay(WeComAddContactWayInput input) {
        WeComAddContactWayInputDTO inputDTO = WeComAddContactWayInputDTO.buildFrom(input);
        String post = wechatService.post(ADD_CONTACT_WAY, JSON.toJSONString(inputDTO));
        WeComAddContactWayResp resp = JSON.parseObject(post, WeComAddContactWayResp.class);
        if (!resp.success()) {
            log.info("配置客户联系「联系我」方式失败, input: {}", inputDTO);
            return resp;
        }

        int ret = insertToDatabase(inputDTO, resp);
        if (ret == 0) {
            log.info("插入数据库失败, input: {}, resp: {}", inputDTO, resp);
        }
        return resp;
    }

    @Override
    public WeChatBaseResp deleteContactWay(ConfigIdInput configId) {
        String post = wechatService.post(DELETE_CONTACT_WAY, JSON.toJSONString(configId));
        WeChatBaseResp resp = JSON.parseObject(post, WeChatBaseResp.class);
        if (!resp.success()) {
            log.info("删除企业已配置的「联系我」方式失败, configId: {}", configId);
            return resp;
        }
        weComUserContactWayMapper.deleteByConfigId(configId.getConfigId());
        return resp;
    }

    @Override
    public JSONObject getContactWay(ConfigIdInput input) {
        return JSON.parseObject(wechatService.post(GET_CONTACT_WAY, JSON.toJSONString(input)));
    }

    private int insertToDatabase(WeComAddContactWayInputDTO input, WeComAddContactWayResp resp) {
        WecomUserInfo wecomUserInfo = wecomUserInfoMapper.selectByUserId(input.getUserId());
        if (wecomUserInfo == null) {
            log.info("未找到对应的用户信息, userId: {}", input.getUserId());
            return 0;
        }

        WeComUserContactWay contactWay = new WeComUserContactWay();
        contactWay.setUserId(input.getUserId());
        contactWay.setRemark(input.getRemark());
        contactWay.setState(input.getState());
        contactWay.setQrCode(resp.getQrCode());
        contactWay.setConfigId(resp.getConfigId());
        contactWay.setAdminId(wecomUserInfo.getAdminId());
        return weComUserContactWayMapper.insert(contactWay);
    }
}
