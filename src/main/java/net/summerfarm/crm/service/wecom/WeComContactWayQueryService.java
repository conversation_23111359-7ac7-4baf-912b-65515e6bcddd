package net.summerfarm.crm.service.wecom;

import net.summerfarm.crm.client.enums.BdQrCodeQueryChannelEnum;

public interface WeComContactWayQueryService {

    /**
     * 根据mId动态获取销售二维码.
     * 根据渠道获取对应渠道的二维码,如: 注册,首页弹窗,个人中心
     * 对自主注册和公海客户,获取城市m1销售的二维码
     * 私海和地推码注册的客户,获取对应销售的二维码
     *
     * @param mId     mId
     * @param channel 渠道
     * @return 销售二维码
     */
    String getQrCodeForMerchant(Long mId, BdQrCodeQueryChannelEnum channel);

}
