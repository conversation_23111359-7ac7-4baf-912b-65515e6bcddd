package net.summerfarm.crm.service.wecom.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.WeChatConstant;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.MerchantSubAccount;
import net.summerfarm.crm.model.domain.WechatUserInfo;
import net.summerfarm.crm.model.dto.QwChatMessageDTO;
import net.summerfarm.crm.service.impl.WechatServiceImpl;
import net.summerfarm.crm.service.wecom.WeComMsgService;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WeComMsgServiceImpl implements WeComMsgService {

    private static final String AUDIT_SUCCESS_MSG = "您的店铺已通过申请，现在可以点击下方小程序下单啦";
    private static final String AUDIT_FAIL_MSG = "您的店铺未通过审核，可以点击下方小程序重新提交材料";
    private static final String MINI_PROGRAM_TITLE_SUCCESS = "现在开启您的便捷采购之旅吧";
    private static final String MINI_PROGRAM_TITLE_FAIL = "请点击此链接重新提交材料";
    private static final String MINI_PROGRAM_PAGE = "/pages/loading/loading";

    @Resource
    MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    WechatUserInfoMapper wechatUserInfoMapper;
    @Resource
    WechatServiceImpl wechatService;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    @Resource
    private ConfigMapper configMapper;

    @Override
    public void sendAuditResultMsg(Long mid) {
        // 获取账号信息
        List<MerchantSubAccount> mainAccounts = merchantSubAccountMapper.selectByMIdlAll(mid).stream().filter(it -> Objects.equals(it.getType(), 0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainAccounts)) {
            throw new BizException("主账号信息异常");
        }
        MerchantSubAccount merchantSubAccount = mainAccounts.get(0);
        if (StringUtils.isEmpty(merchantSubAccount.getUnionid())) {
            log.warn("主账号unionId为空 {}", merchantSubAccount.getAccountId());
            return;
        }

        // 判断是否加了任意鲜沐企微员工
        List<WechatUserInfo> wechatUserInfos = wechatUserInfoMapper.selectActiveByUnionIds(Collections.singletonList(merchantSubAccount.getUnionid()));
        if (CollectionUtils.isEmpty(wechatUserInfos)) {
            log.info("当前账号未添加鲜沐员工的企业微信 {}", merchantSubAccount.getAccountId());
            return;
        }

        //查询门店的审核情况
        MerchantStoreResultResp merchant = merchantQueryFacade.getMerchantByMid(mid);

        // 发送审核结果消息
        String externalUserid = wechatUserInfos.get(0).getExternalUserid();
        QwChatMessageDTO message = buildAuditResultMsg(
                Collections.singletonList(externalUserid),
                MerchantStoreEnums.Status.AUDIT_SUCCESS.getCode().equals(merchant.getStatus()));
        wechatService.sendMessage(message);

    }

    private QwChatMessageDTO buildAuditResultMsg(List<String> externalUserIds, boolean auditSuccess) {
        String media_id = configMapper.selectOne(ConfigValueEnum.MALL_MINI_PROGRAM_COVER_MEDIA_ID.getKey()).getValue();

        QwChatMessageDTO.Attachment.MiniProgram miniProgram = QwChatMessageDTO.Attachment.MiniProgram.builder()
                .title(auditSuccess ? MINI_PROGRAM_TITLE_SUCCESS : MINI_PROGRAM_TITLE_FAIL)
                .pic_media_id(media_id)
                .appid(WeChatConstant.MALL.APP_ID)
                .page(MINI_PROGRAM_PAGE)
                .build();
        QwChatMessageDTO.Attachment attachment = QwChatMessageDTO.Attachment.builder()
                .msgtype(QwChatMessageDTO.Attachment.MsgType.MINI_PROGRAM.getType())
                .miniprogram(miniProgram)
                .build();

        return QwChatMessageDTO.builder()
                .external_userid(externalUserIds)
                .text(new QwChatMessageDTO.Text(auditSuccess ? AUDIT_SUCCESS_MSG : AUDIT_FAIL_MSG))
                .attachments(Collections.singletonList(attachment))
                .build();
    }
}
