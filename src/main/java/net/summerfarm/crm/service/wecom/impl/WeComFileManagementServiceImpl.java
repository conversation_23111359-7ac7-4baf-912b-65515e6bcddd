package net.summerfarm.crm.service.wecom.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.util.WeChatBaseUtil;
import net.summerfarm.crm.model.vo.weCom.media.WeComMediaUploadResp;
import net.summerfarm.crm.service.WechatService;
import net.summerfarm.crm.service.wecom.WeComFileManagementService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.security.ProviderException;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class WeComFileManagementServiceImpl implements WeComFileManagementService {

    @Resource
    private WechatService wechatService;

    @Override
    public WeComMediaUploadResp uploadMedia(String type, File file) {
        String resp = wechatService.uploadMedia(WeChatBaseUtil.UPLOAD_MEDIA, type, file);
        if (StringUtils.isBlank(resp)) {
            throw new ProviderException("上传文件失败");
        }
        return JSONObject.parseObject(resp, WeComMediaUploadResp.class);
    }
}
