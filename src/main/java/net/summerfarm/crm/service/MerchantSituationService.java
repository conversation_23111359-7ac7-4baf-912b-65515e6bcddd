package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.crm.model.query.MerchantSituationQuery;
import net.summerfarm.crm.model.vo.MerchantSituationVO;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface MerchantSituationService {

    /**
     * 新增客情申请
     * @param merchantSituationDTO 客情申请详情
     * @return ok
     */
    AjaxResult insertMerchantSituation(MerchantSituationDTO merchantSituationDTO);

    /**
     * 审核客情申请单
     * @param merchantSituationVO 客情审核内容
     * @param type 类型
     * @return
     */
    AjaxResult examineMerchantSituation(MerchantSituationVO merchantSituationVO, Integer type);

    /**
     * 查询客情申请单
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param merchantSituationQuery 筛选条件
     * @return 客情申请单列表
     */
    AjaxResult queryMerchantSituationList(int pageIndex, int pageSize, MerchantSituationQuery merchantSituationQuery);

    /**
     * 客情申请详情
     * @param id id
     * @return 客情申请详情
     */
    AjaxResult queryMerchantSituation(Long id);

    /**
    * 定时任务 每月一号重制额度
    */
    void autoQuota();

    /**
    * 定时任务 每月一号关闭所有申请 不用返回额度
    */
    void autoCloseSituation();

    /**
    * 定时任务 每天0点关闭3天前申请单并返还额度
    */
    void autoTreeDayCloseSituation();

    /**
     * 查寻已申请客情金额和总计
     * @return 申请客情金额和总计
     */
    AjaxResult merchantSituationQuota();

    /**
     * 品类券-价格补贴
     *
     * @param merchantSituationDTO 商户情况
     * @param merchant             商人
     * @param crmBdOrg             crm销售org
     * @return {@link AjaxResult}
     */
    AjaxResult dealWithCategoryCoupon(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant, CrmBdOrg crmBdOrg);

    /**
     * 品类券-品类拓宽
     *
     * @param merchantSituationDTO 商户情况
     * @param merchant             商人
     * @return {@link AjaxResult}
     */
    AjaxResult dealWithMerchantSituationCategoryCoupon(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant);

    /**
     * 月活券
     *
     * @param merchantSituationDTO 商户情况
     * @param merchant
     * @param phone                电话
     * @return {@link AjaxResult}
     */
    AjaxResult dealWithMonthLivingCoupon(MerchantSituationDTO merchantSituationDTO, MerchantStoreAndExtendDTO merchant, String phone);

    /**
     * 客情券
     *
     * @param merchant             商人
     * @param merchantSituationDTO 商户情况
     * @return {@link AjaxResult}
     */
    AjaxResult dealWithMerchantSituationCoupon(MerchantStoreAndExtendDTO merchant, MerchantSituationDTO merchantSituationDTO);

}
