package net.summerfarm.crm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.AdminConfigValueEnum;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.MerchantEnum;
import net.summerfarm.crm.enums.OperateStatusEnum;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.repository.FollowUpRelationReleaseDetailRepository;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.followUpRelation.LastOrderDTO;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.task.CrmDelayReleaseRule;
import net.summerfarm.crm.task.CrmReleaseTimeCompareResult;
import net.summerfarm.pojo.DO.Admin;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户释放时间比对服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FollowUpReleaseTimeCompareService {

    private static final LocalDate NONE_RELEASE_FOREVER_DATE = LocalDate.of(9999, 12, 31);

    @Autowired
    private CrmBdOrgMapper crmBdOrgMapper;
    @Autowired
    private FollowWhiteListMapper followWhiteListMapper;
    @Autowired
    private MerchantMapper merchantMapper;
    @Autowired
    private AdminMapper adminMapper;
    @Autowired
    private BigCustomerPropertiesExtMapper bigCustomerPropertiesExtMapper;
    @Autowired
    private OrdersMapper ordersMapper;
    @Autowired
    private FollowUpRecordMapper followUpRecordMapper;
    @Autowired
    private FollowUpRelationCompareMapper followUpRelationCompareMapper;
    @Autowired
    private FollowUpRelationReleaseDetailRepository followUpRelationReleaseDetailRepository;
    @Autowired
    private MerchantLabelCorrelaionMapper merchantLabelCorrelaionMapper;

    public List<CrmReleaseTimeCompareResult> compareReleaseTimeByBdAndMids(Integer bdId,
            List<FollowUpRelation> followUpRelations, LocalDateTime calculateStartTime,
            List<CrmDelayReleaseRule> delayReleaseRules, boolean otherCustomUseMaxReleaseDate,
            boolean firstBuyerAndOpenToPrivateUseMaxReleaseDate) {
        try {
            // 查询销售信息，判断是否M1/M2/M3
            List<CrmBdOrg> crmBdOrgList = crmBdOrgMapper.listByBdId(bdId);
            if (CollectionUtils.isEmpty(crmBdOrgList)) {
                log.error("找不到对应的销售信息, bdId:{}", bdId);
                return Collections.emptyList();
            }
            List<Integer> bdManagerRanks = Lists.newArrayList(BdAreaConfigEnum.SaleRank.CITY_MANAGER,
                    BdAreaConfigEnum.SaleRank.AREA_MANAGER, BdAreaConfigEnum.SaleRank.DEPARTMENT_MANAGER);
            boolean isManager = crmBdOrgList.stream().anyMatch(x -> bdManagerRanks.contains(x.getRank()));

            Map<Long, FollowUpRelation> followUpRelationMap = followUpRelations.stream()
                    .collect(Collectors.toMap(FollowUpRelation::getmId, Function.identity(), (v1, v2) -> v1));
            List<Long> mIds = followUpRelations.stream().map(FollowUpRelation::getmId).distinct()
                    .collect(Collectors.toList());
            List<MerchantVO> merchants = merchantMapper.selectByMidsNew(mIds);
            merchants = merchants.stream().filter(x -> !OperateStatusEnum.CLOSED.getCode().equals(x.getOperateStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(merchants)) {
                log.info("不存在状态正常的客户, mIds:{}", mIds);
                return Collections.emptyList();
            }
            List<Long> normalMids = merchants.stream().map(Merchant::getmId).distinct().collect(Collectors.toList());
            List<CrmReleaseTimeCompareResult> resultList = normalMids.stream()
                    .map(mId -> new CrmReleaseTimeCompareResult(mId, bdId)).collect(Collectors.toList());

            Map<Long, LocalDate> mId2ReleaseDateMap = new HashMap<>();
            // 获取永不掉落客户并将释放时间设置为9999-12-31
            List<Long> noneReleaseMids = this.getNoneReleaseMids(merchants, bdId, isManager, resultList);
            noneReleaseMids.forEach(mId -> mId2ReleaseDateMap.put(mId, NONE_RELEASE_FOREVER_DATE));
            resultList.stream().filter(x -> noneReleaseMids.contains(x.getMId()))
                    .forEach(x -> x.setOriginalReleaseDate(NONE_RELEASE_FOREVER_DATE));

            // 计算会掉落客户的释放时间
            Collection<Long> willReleaseMids = CollectionUtils.subtract(normalMids, noneReleaseMids);
            willReleaseMids.forEach(mId -> mId2ReleaseDateMap.put(mId,
                    this.calculateReleaseDate(mId, calculateStartTime, bdId, followUpRelationMap.get(mId),
                            delayReleaseRules, resultList, otherCustomUseMaxReleaseDate, firstBuyerAndOpenToPrivateUseMaxReleaseDate)));
            // 比对BI计算出来的释放时间
            this.compareReleaseDate(mId2ReleaseDateMap, resultList);
            return resultList;
        } catch (Exception ex) {
            log.error("比对客户释放时间失败，bdId:{}", bdId, ex);
            return Collections.emptyList();
        }
    }

    private List<Long> getNoneReleaseMids(List<MerchantVO> merchants, Integer bdId, boolean isManager,
            List<CrmReleaseTimeCompareResult> resultList) {
        List<Long> mIds = merchants.stream().map(Merchant::getmId).distinct().collect(Collectors.toList());
        // M1/M2/M3的私海客户不掉落
        if (isManager) {
            log.info("M1/M2/M3的私海客户不掉落，bdId:{}，mIds:{}", bdId, mIds);
            resultList.stream().filter(x -> mIds.contains(x.getMId())).forEach(x -> x.setManager(true));
            return mIds;
        }
        Set<Long> noneReleaseMids = new HashSet<>();
        // 大客户门店不掉落（admin表的admin_type等于0或2为大客户）
        Map<Integer, List<Long>> adminId2MidsMap = merchants.stream().filter(x -> x.getAdminId() != null)
                .collect(Collectors.groupingBy(Merchant::getAdminId,
                        Collectors.mapping(Merchant::getmId, Collectors.toList())));
        if (MapUtils.isNotEmpty(adminId2MidsMap)) {
            List<Long> majorMids = new ArrayList<>();
            List<Admin> admins = adminMapper.selectByIds(Lists.newArrayList(adminId2MidsMap.keySet()));
            admins.stream().filter(
                    x -> Integer.valueOf(0).equals(x.getAdminType()) || Integer.valueOf(2).equals(x.getAdminType()))
                    .forEach(admin -> {
                        if (adminId2MidsMap.get(admin.getAdminId()) != null) {
                            majorMids.addAll(adminId2MidsMap.get(admin.getAdminId()));
                        }
                    });
            if (CollectionUtils.isNotEmpty(majorMids)) {
                log.info("大客户门店不掉落，bdId:{}，mIds:{}", bdId, majorMids);
                resultList.stream().filter(x -> majorMids.contains(x.getMId())).forEach(x -> x.setMajor(true));
                noneReleaseMids.addAll(majorMids);
            }
        }
        // 白名单客户不掉落
        List<FollowWhiteList> followWhiteLists = followWhiteListMapper.queryFollowWhiteListByMids(mIds);
        List<Long> followWhiteListMids = followWhiteLists.stream().map(FollowWhiteList::getmId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(followWhiteListMids)) {
            log.info("白名单门店不掉落，bdId:{}，mIds:{}", bdId, followWhiteListMids);
            resultList.stream().filter(x -> followWhiteListMids.contains(x.getMId()))
                    .forEach(x -> x.setWhiteList(true));
            noneReleaseMids.addAll(followWhiteListMids);
        }
        // POP客户不掉落
        List<Long> popMids = merchants.stream()
                .filter(x -> MerchantEnum.BusinessLineEnum.POP.getCode().equals(x.getBusinessLine()))
                .map(Merchant::getmId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(popMids)) {
            log.info("POP门店不掉落，bdId:{}，mIds:{}", bdId, popMids);
            resultList.stream().filter(x -> popMids.contains(x.getMId())).forEach(x -> x.setPop(true));
            noneReleaseMids.addAll(popMids);
        }
        // 绑定销售的客户不掉落
        Map<Long, List<Long>> longAdminId2MidsMap = merchants.stream().filter(x -> x.getAdminId() != null)
                .collect(Collectors.groupingBy(x -> x.getAdminId().longValue(),
                        Collectors.mapping(Merchant::getmId, Collectors.toList())));
        if (MapUtils.isNotEmpty(longAdminId2MidsMap)) {
            List<Long> bindBdMids = new ArrayList<>();
            List<BigCustomerPropertiesExt> bigCustomerPropertiesExts = bigCustomerPropertiesExtMapper
                    .listByBigCustomerIdsAndProKey(
                            Lists.newArrayList(longAdminId2MidsMap.keySet()),
                            AdminConfigValueEnum.ADMIN_TURNING_CONFIG.getKey());
            bigCustomerPropertiesExts.stream()
                    .filter(x -> StringUtils.isNotEmpty(x.getPropValue()) && !"0".equals(x.getPropValue()))
                    .forEach(ext -> {
                        if (longAdminId2MidsMap.get(ext.getBigCustomerId()) != null) {
                            bindBdMids.addAll(longAdminId2MidsMap.get(ext.getBigCustomerId()));
                        }
                    });
            if (CollectionUtils.isNotEmpty(bindBdMids)) {
                log.info("绑定销售的门店不掉落，bdId:{}，mIds:{}", bdId, bindBdMids);
                resultList.stream().filter(x -> bindBdMids.contains(x.getMId())).forEach(x -> x.setBindBd(true));
                noneReleaseMids.addAll(bindBdMids);
            }
        }
        return Lists.newArrayList(noneReleaseMids);
    }

    private LocalDate calculateReleaseDate(Long mId, LocalDateTime calculateStartTime, Integer bdId,
            FollowUpRelation followUpRelation, List<CrmDelayReleaseRule> delayReleaseRules,
            List<CrmReleaseTimeCompareResult> resultList, boolean otherCustomUseMaxReleaseDate,
            boolean firstBuyerAndOpenToPrivateUseMaxReleaseDate) {
        CrmReleaseTimeCompareResult result = resultList.stream().filter(x -> mId.equals(x.getMId())).findFirst()
                .orElse(new CrmReleaseTimeCompareResult(mId, bdId));
        // 1.计算首单客户的释放时间，非首单客户为null
        LocalDate firstBuyerReleaseDate = this.calculateFirstBuyerReleaseDate(mId, calculateStartTime, bdId);
        result.setFirstBuyerReleaseDate(firstBuyerReleaseDate);
        // 2.计算公转私客户的释放时间，非公转私客户为null
        LocalDate openToPrivateReleaseDate = this.calculateOpenToPrivateReleaseDate(mId, calculateStartTime, bdId,
                followUpRelation);
        result.setOpenToPrivateReleaseDate(openToPrivateReleaseDate);
        // 3.计算初步释放时间
        LocalDate releaseDate = null;
        if (firstBuyerReleaseDate != null || openToPrivateReleaseDate != null) {
            // 3.1.如果是首单客户或者公转私客户，根据参数决定取两者中最大或最小的释放时间
            if (firstBuyerAndOpenToPrivateUseMaxReleaseDate) {
                releaseDate = DateUtils.max(firstBuyerReleaseDate, openToPrivateReleaseDate);
            } else {
                releaseDate = DateUtils.min(firstBuyerReleaseDate, openToPrivateReleaseDate);
            }
            log.info("mId：{}，首单客户释放时间：{}，公转私客户的释放时间：{}，使用最大释放时间：{}，初步释放时间：{}", mId, firstBuyerReleaseDate, openToPrivateReleaseDate,
                    firstBuyerAndOpenToPrivateUseMaxReleaseDate, releaseDate);
            result.setOriginalReleaseDate(releaseDate);
        } else {
            // 3.2.计算其他客户的释放时间
            releaseDate = this.calculateOtherReleaseDate(mId, bdId, followUpRelation, result,
                    otherCustomUseMaxReleaseDate);
            log.info("mId：{}，其他客户初步释放时间：{}", mId, releaseDate);
            result.setOriginalReleaseDate(releaseDate);
        }
        // 4.计算延期掉落后的释放时间（春节和高校假期延期掉落）
        releaseDate = this.calculateDelayReleaseDate(releaseDate, mId, delayReleaseRules, result);
        log.info("mId：{}，延期掉落判定后的释放时间：{}", mId, releaseDate);
        result.setDelayReleaseDate(releaseDate);
        // 5.计算保护规则判定后的释放时间
        releaseDate = this.calculateProtectReleaseDate(releaseDate, mId);
        log.info("mId：{}，保护规则判定后的释放时间：{}", mId, releaseDate);
        result.setProtectReleaseDate(releaseDate);
        return releaseDate;
    }

    private LocalDate calculateFirstBuyerReleaseDate(Long mId, LocalDateTime calculateStartTime, Integer bdId) {
        // 首单客户：只下了一个订单（订单状态待配送、待收货、已收货），且下单时间大于等于计算开始时间，且7日内未拜访的客户
        LocalDate firstBuyerReleaseDate = null;
        LastOrderDTO lastOrderInfo = ordersMapper.getLastOrderInfo(mId, Lists.newArrayList(2, 3, 6));
        boolean firstOrder = (lastOrderInfo != null) && (lastOrderInfo.getOrderCount() == 1)
                && !calculateStartTime.isAfter(lastOrderInfo.getLastOrderTime());
        if (firstOrder) {
            // 查询首单后7日内有无拜访
            FollowUpRecordVO input = new FollowUpRecordVO();
            input.setmId(mId);
            input.setAdminId(bdId);
            input.setStartTime(
                    BaseDateUtils.localDateTime2Date(lastOrderInfo.getLastOrderTime().toLocalDate().atStartOfDay()));
            input.setEndTime(BaseDateUtils
                    .localDateTime2Date(lastOrderInfo.getLastOrderTime().toLocalDate().plusDays(8L).atStartOfDay()));
            List<FollowUpRecordVO> followUpRecordVOS = followUpRecordMapper.selectByStart(input);
            if (CollectionUtils.isEmpty(followUpRecordVOS)) {
                // 7日内未拜访，则第8日释放
                firstBuyerReleaseDate = lastOrderInfo.getLastOrderTime().toLocalDate().plusDays(8L);
            }
        }
        return firstBuyerReleaseDate;
    }

    private LocalDate calculateOpenToPrivateReleaseDate(Long mId, LocalDateTime calculateStartTime, Integer bdId,
            FollowUpRelation followUpRelation) {
        // 公转私客户：上次在公海，且进入私海时间大于等于计算开始时间，且5日内未拜访的客户
        LocalDateTime inPrivateSeaTime = followUpRelation.getReassignTime();
        LocalDate openToPrivateReleaseDate = null;
        boolean openToPrivate = Integer.valueOf(0).equals(followUpRelation.getSource())
                && !calculateStartTime.isAfter(inPrivateSeaTime);
        if (openToPrivate) {
            // 查询公转私后5日内有无拜访
            FollowUpRecordVO input = new FollowUpRecordVO();
            input.setmId(mId);
            input.setAdminId(bdId);
            input.setStartTime(BaseDateUtils.localDateTime2Date(inPrivateSeaTime.toLocalDate().atStartOfDay()));
            input.setEndTime(
                    BaseDateUtils.localDateTime2Date(inPrivateSeaTime.toLocalDate().plusDays(6L).atStartOfDay()));
            List<FollowUpRecordVO> followUpRecordVOS = followUpRecordMapper.selectByStart(input);
            if (CollectionUtils.isEmpty(followUpRecordVOS)) {
                // 5日内未拜访，则第6日释放
                openToPrivateReleaseDate = inPrivateSeaTime.toLocalDate().plusDays(6L);
            }
        }
        return openToPrivateReleaseDate;
    }

    private LocalDate calculateOtherReleaseDate(Long mId, Integer bdId, FollowUpRelation followUpRelation,
            CrmReleaseTimeCompareResult result, boolean otherCustomUseMaxReleaseDate) {
        LocalDateTime inPrivateSeaTime = followUpRelation.getReassignTime();
        // 1.取客户进入私海后最近一次下单（订单状态待配送、待收货、已收货）时间，如进入私海后没有下单则取进入私海的时间
        LastOrderDTO lastOrderInfo = ordersMapper.getLastOrderInfo(mId, Lists.newArrayList(2, 3, 6));
        LocalDate lastOrderDateInPrivateSea = (lastOrderInfo != null && lastOrderInfo.getLastOrderTime() != null
                && lastOrderInfo.getLastOrderTime().isAfter(inPrivateSeaTime))
                        ? lastOrderInfo.getLastOrderTime().toLocalDate()
                        : inPrivateSeaTime.toLocalDate();
        result.setLastOrderDate(lastOrderDateInPrivateSea);
        // 2.取客户进入私海后最近一次履约完成时间（配送完成时间和自提时间两者中的最大值），如进入私海后没有完成过履约则取进入私海的时间
        LocalDateTime lastDeliveryFinishTime = followUpRelationCompareMapper
                .selectLastDeliveryFinishTime(String.valueOf(mId));
        LocalDate lastSelfPickTime = followUpRelationCompareMapper.selectLastSelfPickupTime(mId);
        LocalDate lastFulfillmentFinishDate = DateUtils
                .max(lastDeliveryFinishTime != null ? lastDeliveryFinishTime.toLocalDate() : null, lastSelfPickTime);
        LocalDate lastFulfillmentFinishDateInPrivateSea = DateUtils.max(lastFulfillmentFinishDate,
                inPrivateSeaTime.toLocalDate());
        result.setLastFulfillmentFinishDate(lastFulfillmentFinishDateInPrivateSea);
        // 3.取客户进入私海后最近一次拜访时间，如进入私海后没有拜访则取进入私海的时间
        FollowUpRecord lastFollowUpRecord = followUpRecordMapper.selectLatestRecordByMidAndAdminId(mId, bdId);
        LocalDateTime lastFollowUpRecordTime = (lastFollowUpRecord != null)
                ? BaseDateUtils.date2LocalDateTime(lastFollowUpRecord.getAddTime())
                : null;
        LocalDate lastFollowUpRecordDateInPrivateSea = (lastFollowUpRecordTime != null
                && lastFollowUpRecordTime.isAfter(inPrivateSeaTime)) ? lastFollowUpRecordTime.toLocalDate()
                        : inPrivateSeaTime.toLocalDate();
        result.setLastFollowUpRecordDate(lastFollowUpRecordDateInPrivateSea);
        // 4.规则1：计算30天未下单且未履约，且15天未拜访规则的释放时间（同时满足这几个条件的最大时间）
        LocalDate rule1ReleaseDate = DateUtils.max(
                DateUtils.max(lastOrderDateInPrivateSea.plusDays(31L),
                        lastFulfillmentFinishDateInPrivateSea.plusDays(31L)),
                lastFollowUpRecordDateInPrivateSea.plusDays(16L));
        result.setRule1ReleaseDate(rule1ReleaseDate);
        // 5.规则2：计算60天未下单且未履约规则的释放时间（同时满足这几个条件的最大时间）
        LocalDate rule2ReleaseDate = DateUtils.max(lastOrderDateInPrivateSea.plusDays(61L),
                lastFulfillmentFinishDateInPrivateSea.plusDays(61L));
        result.setRule2ReleaseDate(rule2ReleaseDate);
        // 6.根据otherCustomUseMaxReleaseDate参数决定取上述两个规则的最大或最小释放时间
        if (otherCustomUseMaxReleaseDate) {
            return DateUtils.max(rule1ReleaseDate, rule2ReleaseDate);
        } else {
            return DateUtils.min(rule1ReleaseDate, rule2ReleaseDate);
        }
    }

    private LocalDate calculateDelayReleaseDate(LocalDate releaseDate, Long mId,
            List<CrmDelayReleaseRule> delayReleaseRules, CrmReleaseTimeCompareResult result) {
        if (CollectionUtils.isEmpty(delayReleaseRules)) {
            return releaseDate;
        }
        List<MerchantLabelCorrelaion> merchantLabelCorrelaions = merchantLabelCorrelaionMapper.selectByMid(mId);
        List<Long> labelIds = merchantLabelCorrelaions.stream().map(MerchantLabelCorrelaion::getLabelId).distinct()
                .collect(Collectors.toList());
        result.setLabelIds(labelIds);
        LocalDate delayReleaseDate = releaseDate;
        for (CrmDelayReleaseRule delayReleaseRule : delayReleaseRules) {
            if (CollectionUtils.isNotEmpty(delayReleaseRule.getLabelIds())) {
                if (labelIds.stream().noneMatch(labelId -> delayReleaseRule.getLabelIds().contains(labelId))) {
                    log.info("客户标签不满足当前延期掉落规则，客户标签：{}，延期掉落规则要求标签：{}", labelIds, delayReleaseRule.getLabelIds());
                    continue;
                }
            }
            // 客户释放时间在延期规则起止时间内时延期到规则指定时间释放，命中多个延期规则时取最大延期释放时间
            if (releaseDate.compareTo(delayReleaseRule.getRuleStartTime()) >= 0
                    && releaseDate.compareTo(delayReleaseRule.getRuleEndTime()) <= 0) {
                delayReleaseDate = DateUtils.max(delayReleaseDate, delayReleaseRule.getDelayReleaseTime());
            }
        }
        return delayReleaseDate;
    }

    private LocalDate calculateProtectReleaseDate(LocalDate releaseDate, Long mId) {
        // 释放时间大于当前时间，无需顺延保护
        if (releaseDate.isAfter(LocalDate.now())) {
            return releaseDate;
        }
        // 不存在未履约订单（订单状态待配送、待收货），无需顺延保护
        LastOrderDTO lastOrderInfo = ordersMapper.getLastOrderInfo(mId, Lists.newArrayList(2, 3));
        if (lastOrderInfo == null || lastOrderInfo.getOrderCount() == 0) {
            return releaseDate;
        }
        // 释放时间小于等于当前时间，且存在未履约订单则顺延到明天释放
        return LocalDate.now().plusDays(1L);
    }

    private void compareReleaseDate(Map<Long, LocalDate> mId2ReleaseDateMap,
            List<CrmReleaseTimeCompareResult> resultList) {
        if (MapUtils.isEmpty(mId2ReleaseDateMap)) {
            return;
        }
        Map<Long, CrmReleaseTimeCompareResult> mId2CompareResultMap = resultList.stream()
                .collect(Collectors.toMap(CrmReleaseTimeCompareResult::getMId, Function.identity(), (v1, v2) -> v1));
        List<FollowUpRelationReleaseDetail> releaseDetails = followUpRelationReleaseDetailRepository.list(
                new LambdaQueryWrapper<FollowUpRelationReleaseDetail>().in(FollowUpRelationReleaseDetail::getMId,
                        mId2ReleaseDateMap.keySet()));
        Map<Long, LocalDate> mId2ReleaseDateFromBIMap = releaseDetails.stream()
                .filter(x -> x.getMId() != null && x.getReleaseDate() != null).collect(
                        Collectors.toMap(FollowUpRelationReleaseDetail::getMId, x -> x.getReleaseDate().toLocalDate(),
                                (v1, v2) -> v1));
        mId2ReleaseDateMap.forEach((mId, releaseDate) -> {
            LocalDate releaseDateFromBI = mId2ReleaseDateFromBIMap.get(mId);
            if (!Objects.equals(releaseDate, releaseDateFromBI)) {
                log.error("【客户释放时间比对失败】mId：{}，BI计算的释放时间：{}，服务端计算的释放时间：{}", mId, releaseDateFromBI, releaseDate);
            }
            if (mId2CompareResultMap.get(mId) != null) {
                mId2CompareResultMap.get(mId).setReleaseDateFromBI(releaseDateFromBI);
            }
        });
    }

}