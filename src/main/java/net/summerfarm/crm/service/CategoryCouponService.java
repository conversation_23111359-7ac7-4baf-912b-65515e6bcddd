package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.domain.CategoryCouponQuota;
import net.summerfarm.crm.model.domain.CategoryCouponQuotaChange;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.CoreProductBasePrice;
import net.summerfarm.crm.model.dto.BasePriceFileDTO;
import net.summerfarm.crm.model.dto.CategoryQuotaDTO;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.query.BasePriceQuery;
import net.summerfarm.crm.model.query.CategoryQuotaChangeQuery;
import net.summerfarm.crm.model.query.monthLiving.QuotaListQuery;
import net.summerfarm.crm.model.query.monthLiving.QuotaRatioQuery;
import net.summerfarm.crm.model.vo.CategoryCouponQuotaVO;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 品类券
 *
 * <AUTHOR>
 * @Date 2023/3/6 15:45
 */
public interface CategoryCouponService {
    /**
     * 核心品类底价列表
     *
     * @param query
     * @param displayBasePrice                          是否显示底价
     * @param displayMerchantSituationCategoryBasePrice 是否显示品类拓宽保底价
     * @return {@link PageInfo}<{@link CoreProductBasePrice}>
     */
    PageInfo<CoreProductBasePrice> listBasePrice(BasePriceQuery query, boolean displayBasePrice, boolean displayMerchantSituationCategoryBasePrice);

    /**
     * 更新底价
     *
     * @param basePrice                             底价
     * @param setBasePrice                          是否要设置底价
     * @param setMerchantSituationCategoryBasePrice 是否要设置品类拓宽保底价
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> updateBasePrice(CoreProductBasePrice basePrice, boolean setBasePrice, boolean setMerchantSituationCategoryBasePrice);

    /**
     * 删除底价
     *
     * @param id id
     * @return {@link CommonResult}<{@link Void}>
     */
    void delBasePrice(Integer id);

    /**
     * 批量删除底价
     *
     * @param fileDTO
     * @return {@link CommonResult}<{@link String}>
     */
    CommonResult<String> batchDelBasePrice(BasePriceFileDTO fileDTO);

    /**
     * 新增底价
     *
     * @param basePrice                             底价
     * @param setMerchantSituationCategoryBasePrice 是否要品类拓宽保底价
     */
    void insertBasePrice(List<CoreProductBasePrice> basePrice, boolean setMerchantSituationCategoryBasePrice);

    /**
     * 批量新增底价
     *
     * @param fileDTO
     * @param setMerchantSituationCategoryBasePrice 是否要品类拓宽保底价
     */
    CommonResult<String> batchInsertBasePrice(BasePriceFileDTO fileDTO, boolean setMerchantSituationCategoryBasePrice);

    /**
     * 查询有底价的大区信息
     *
     * @return {@link List}<{@link LargeAreaDTO}>
     */
    List<LargeAreaDTO> listLargeArea();

    /**
     * 更新配置
     *
     * @param key
     * @param value
     * @return {@link CommonResult}<{@link Void}>
     */
    void configSetting(String key, String value);

    /**
     * 获取配置
     *
     * @param key
     * @return {@link CommonResult}<{@link Void}>
     */
    Config getConfig(String key);

    /**
     * 额度列表
     *
     * @param pageInput 分页信息
     * @return {@link PageInfo}<{@link CategoryCouponQuotaVO}>
     */
    PageInfo<CategoryCouponQuotaVO> listQuota(QuotaListQuery pageInput);

    /**
     * 有额度的记录
     *
     * @return {@link PageInfo}<{@link CategoryCouponQuota}>
     */
    List<CategoryCouponQuota> listAdmin();

    /**
     * 登录用户额度信息
     *
     * @param quotaType 额度类型
     * @return {@link CategoryCouponQuotaVO}
     */
    List<CategoryCouponQuotaVO> quotaDetail(Integer quotaType);

    /**
     * CRM登录用户额度信息
     *
     * @return {@link CategoryCouponQuotaVO}
     */
    CommonResult<List<CategoryCouponQuotaVO>> crmQuotaDetail();

    /**
     * 额度变更详情
     *
     * @param query 查询
     * @return {@link PageInfo}<{@link CategoryCouponQuotaVO}>
     */
    PageInfo<CategoryCouponQuotaChange> listQuotaChange(CategoryQuotaChangeQuery query);

    /**
     * 设置额度
     *
     * @param quota 额度信息
     * @return {@link PageInfo}<{@link CategoryCouponQuotaVO}>
     */
    CommonResult<Void> quotaSetting(CategoryQuotaDTO quota);

    /**
     * 划分额度
     *
     * @param quota
     * @return {@link PageInfo}<{@link CategoryCouponQuotaVO}>
     */
    CommonResult<Void> divisionSetting(CategoryQuotaDTO quota);

    /**
     * 新增额度信息
     *
     * @param quota
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> quotaInsert(CategoryQuotaDTO quota);

    /**
     * 删除额度信息
     *
     * @param adminId
     * @return
     */
    CommonResult<Void> deleteQuota(Integer adminId, Integer type);

    /**
     * 额度奖励返现定时任务
     */
    void quotaReward();

    /**
     * 费比设置
     *
     * @param ratioQuery 比查询
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> ratioSetting(QuotaRatioQuery ratioQuery);
}
