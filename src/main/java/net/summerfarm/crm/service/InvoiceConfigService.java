package net.summerfarm.crm.service;

import net.summerfarm.crm.model.domain.InvoiceConfig;
import net.summerfarm.crm.model.query.InvoiceConfigListQuery;
import net.summerfarm.crm.model.vo.InvoiceConfigVo;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/20 14:39
 */
public interface InvoiceConfigService {

    /**
     * 门店抬头列表
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link InvoiceConfigVo}>
     */
    CommonResult<List<InvoiceConfigVo>> listInvoiceConfig(InvoiceConfigListQuery query);

    /**
     * 更新发票配置
     *
     * @param  invoiceConfig
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> updateInvoiceConfig(InvoiceConfig invoiceConfig);

    /**
     * 新增发票配置
     *
     * @param invoiceConfig
     * @return {@link CommonResult}<{@link Void}>
     */
    CommonResult<Void> insertInvoiceConfig(InvoiceConfig invoiceConfig);
}
