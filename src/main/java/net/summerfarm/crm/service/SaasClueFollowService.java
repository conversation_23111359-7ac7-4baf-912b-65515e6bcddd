package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;

import net.summerfarm.crm.model.dto.CrmFollowDTO;
import net.summerfarm.crm.model.query.ClueDetailQuery;
import net.summerfarm.crm.model.vo.CreateCrmFollowVO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;

public interface SaasClueFollowService {
     CommonResult<PageInfo<CrmFollowDTO>> queryFollow(ClueDetailQuery cluClueQuery);

    /**
     * 创建跟进记录
     *
     * @param createCrmFollowVO
     * @return
     */
    CommonResult<CrmFollowDTO> create(<PERSON>reateCrmFollowVO createCrmFollowVO, UserBase userBase);

}
