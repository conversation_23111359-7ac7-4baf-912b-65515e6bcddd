package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.query.BdExtQuery;
import net.summerfarm.crm.model.vo.PrivateSeaType;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 销售拓展类
 * @date 2022/6/14 2:00
 */
public interface BdExtService {

    /**
     *
     * @param areaNo 城市id
     * @return BD列表
     */
    AjaxResult selectBdForAreaList(Integer areaNo);

    /**
     * 查询销售信息
     * @param bdExtQuery 查询条件
     * @return 销售信息
     */
    AjaxResult queryBdInfo(BdExtQuery bdExtQuery);

    /**
     * 查询销售区域信息
     * @param bdExtQuery 查询条件
     * @return 区域信息
     */
    AjaxResult queryBdArea(BdExtQuery bdExtQuery);

    /**
     * 登录用户是否具有主管身份
     * @return 是或否
     */
    CommonResult<Boolean> queryBdRole();

    /**
     * 匹配截单围栏
     *
     * @param contact 联系人信息
     * @return 库存仓
     */
    AreaQueryResp matchAreaNo(Contact contact);

    /**
     * 查询所有运营大区
     *
     * @return {@link List}<{@link LargeAreaDTO}>
     */
    List<LargeAreaDTO> selectAllLargeArea();

    PrivateSeaType queryBdPrivateSeaType();
}
