package net.summerfarm.crm.service;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.input.samples.SampleApplyBatchInsertInput;
import net.summerfarm.crm.model.vo.SampleApplyBatchCreateFailExport;
import net.summerfarm.crm.model.vo.SampleApplyBatchInsertResp;
import net.summerfarm.crm.model.vo.SampleApplyUploadMerchantVO;
import net.summerfarm.crm.model.vo.SampleApplyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface SampleApplyService {

    /**
     * 创建样品申请
     * @param sampleApplyVO 样品申请
     * @return ok
     */
    AjaxResult insertSampleApply(SampleApplyVO sampleApplyVO);

    /**
     * 更新用户反馈
     * @param sampleApply 用户反馈信息
     * @return ok
     */
    AjaxResult updateSampleApply(SampleApply sampleApply);

    /**
     * 查询列表信息
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param sampleApply 查询条件
     * @param keyword 关键字
     * @return 列表信息
     */
    AjaxResult selectSampleApplyVO(int pageIndex, int pageSize, SampleApply sampleApply, String keyword);


    /**
     * 发送样品反馈
     */
    void sendDingTalk();

    /**
     * 取消样品申请
     * @param sampleId 样品申请id
     * @return ok
     */
    AjaxResult cancelSampleApply(int sampleId);

    /**
     * 解冻库存的样品申请部分数据库操作
     * @param jsonObject 样品申请信息
     * @return 成功预购
     */
    AjaxResult unFrozenInventory(JSONObject jsonObject);

    /**
     * 批量创建样品单--跟随待配送订单
     * @param input 样品申请信息
     * @return 成功
     */
    SampleApplyBatchInsertResp batchInsertSampleApply(SampleApplyBatchInsertInput input);

    void syncSampleApplyReview(List<Integer> sampleApplyIds);

    SampleApplyUploadMerchantVO checkBatchInsertUploadInfo(SampleApplyBatchInsertInput input);

    Boolean batchCancelSampleApply(SampleApplyBatchInsertInput input);

    void saveSampleApplyInfo(SampleApplyBatchInsertInput input, List<SampleApply> sampleApplyList,
                             List<Integer> sampleApplyIds, List<Long> contactIds);

    Boolean exportSampleApplyFailData(String exportId);

    void asyncDownload(String exportId, String fileName, Long resId, List<SampleApplyBatchCreateFailExport> failList);
}
