package net.summerfarm.crm.service.crmjob;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.crmjob.CrmJobMerchantListQueryDTO;
import net.summerfarm.crm.model.query.crmjob.CrmJobQueryDTO;
import net.summerfarm.crm.model.query.crmjob.JobProductQueryDTO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobMerchantVO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobVO;
import net.summerfarm.crm.model.vo.crmjob.JobProductVO;

import java.util.List;

public interface CrmJobQueryService {

    /**
     * 列出所有任务.一般为manage端使用
     */
    PageInfo<CrmJobVO> queryCrmJobList(CrmJobQueryDTO crmJobQueryDTO);

    /**
     * 列出当前登录bd视角的任务列表
     * 普通销售只能看到私海里客户中有任务的任务
     * 主管视角看到手下销售私海及管辖城市公海客户中有任务的任务
     */
    PageInfo<CrmJobVO> queryCrmJobListForBd(CrmJobQueryDTO crmJobQueryDTO);

    /**
     * 查询任务对应的所有门店列表
     */
    PageInfo<CrmJobMerchantVO> queryMerchantList(CrmJobMerchantListQueryDTO queryDTO);

    /**
     * 查询当前登录bd视角的任务对应的门店列表
     * 普通销售只能看到私海里的门店
     * 主管视角看到手下销售私海及管辖城市公海的门店w
     */
    PageInfo<CrmJobMerchantVO> queryMerchantListForBd(CrmJobMerchantListQueryDTO queryDTO);

    /**
     * 查询任务对应的品详情列表
     */
    @Deprecated // 请使用pageJobProduct
    List<JobProductVO> queryJobProduct(JobProductQueryDTO queryDTO);

    PageInfo<JobProductVO> pageJobProduct(JobProductQueryDTO queryDTO);
}
