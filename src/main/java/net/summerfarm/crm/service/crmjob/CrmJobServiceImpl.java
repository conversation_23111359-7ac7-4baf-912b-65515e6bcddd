package net.summerfarm.crm.service.crmjob;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.factory.CrmJobMerchantDetailStrategyFactory;
import net.summerfarm.crm.mapper.manage.CrmJobCompletionCriteriaMapper;
import net.summerfarm.crm.mapper.manage.CrmJobMapper;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobCompletionCriteria;
import net.summerfarm.crm.model.dto.crmjob.CreateJobDTO;
import net.summerfarm.crm.model.dto.crmjob.CreateJobMerchantDetailDTO;
import net.summerfarm.crm.model.dto.crmjob.JobCompletionCriteriaDTO;
import net.summerfarm.crm.model.dto.crmjob.UpdateJobDTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.strategy.MerchantJobStrategy;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmJobServiceImpl extends BaseService implements CrmJobService {

    @Resource
    private CrmJobMerchantDetailStrategyFactory crmJobMerchantDetailStrategyFactory;
    @Resource
    private CrmJobMapper crmJobMapper;
    @Resource
    private CrmJobCompletionCriteriaMapper crmJobCompletionCriteriaMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskImportResultCountVo createJob(CreateJobDTO createJobDTO) {
        // 1. 创建任务
        CrmJob crmJob = this.buildCrmJob(createJobDTO);
        crmJobMapper.insertSelective(crmJob);
        Long jobId = crmJob.getId();

        // 2. 创建任务完成判定条件
        List<CrmJobCompletionCriteria> criteriaList = this.buildCompletionCriteria(jobId, createJobDTO.getCompletionCriteriaList());
        crmJobCompletionCriteriaMapper.insertList(criteriaList);

        // 3. 根据人群的选择方式,选择合适的策略创建门店任务详情,并返回创建结果
        MerchantJobStrategy merchantJobStrategy = crmJobMerchantDetailStrategyFactory
                .getMerchantJobStrategy(crmJob.getMerchantSelectionType());
        CreateJobMerchantDetailDTO createJobMerchantDetailDTO = new CreateJobMerchantDetailDTO(createJobDTO.getMIdList(), createJobDTO.getOssKey());

        return merchantJobStrategy.createMerchantDetail(crmJob, createJobMerchantDetailDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskImportResultCountVo updateJob(UpdateJobDTO updateJobDTO) {
        CrmJob crmJob = crmJobMapper.selectById(updateJobDTO.getJobId());

        if (crmJob == null || CrmJobEnum.Status.isCanceled(crmJob.getStatus())
                || crmJob.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ParamsException("无法编辑已结束或已停用的任务");
        }
        if (updateJobDTO.getEndTime() != null && updateJobDTO.getEndTime().isBefore(crmJob.getEndTime())) {
            throw new ParamsException("任务结束时间不能早于原任务结束时间");
        }

        log.info("更新任务参数: {}, 更新人: {}", updateJobDTO, this.getAdminId());

        // 更新一些基本信息
        crmJobMapper.updateById(
                CrmJob.builder()
                        .updater(Optional.ofNullable(this.getAdminId()).map(Integer::longValue).orElse(null))
                        .jobName(updateJobDTO.getJobName())
                        .endTime(updateJobDTO.getEndTime())
                        .description(updateJobDTO.getDescription())
                        .updateTime(LocalDateTime.now()) // 防止其它字段是空导致sql错误
                        .build(),
                updateJobDTO.getJobId());
        crmJob = crmJobMapper.selectById(updateJobDTO.getJobId());


        // 选择合适的策略新增门店任务详情,并返回创建结果
        MerchantJobStrategy merchantJobStrategy = crmJobMerchantDetailStrategyFactory
                .getMerchantJobStrategy(crmJob.getMerchantSelectionType());
        CreateJobMerchantDetailDTO createJobMerchantDetailDTO = new CreateJobMerchantDetailDTO(updateJobDTO.getMIdList(), updateJobDTO.getOssKey());
        return merchantJobStrategy.createMerchantDetail(crmJob, createJobMerchantDetailDTO);
    }

    @Override
    public void batchCancelJobs(List<Long> jobIdList) {
        if (CollectionUtils.isEmpty(jobIdList)) {
            return;
        }

        crmJobMapper.updateStatusAndUpdaterByIdInAndStatusNotIn(
                CrmJobEnum.Status.CANCELED.getCode(),
                Optional.ofNullable(this.getAdminId()).map(Integer::longValue).orElse(null),
                jobIdList,
                Collections.singleton(CrmJobEnum.Status.CANCELED.getCode()));
    }

    // ------------------------------------------------ private methods -----------------------------------------------

    private CrmJob buildCrmJob(CreateJobDTO createJobDTO) {
        if (createJobDTO.getEndTime().isBefore(createJobDTO.getStartTime())) {
            throw new ParamsException("任务结束时间不能早于任务开始时间");
        }
        if (createJobDTO.getEndTime().isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("任务结束时间不能早于现在");
        }

        // 发券任务应该是在发券时创建,不应该通过调用接口的方式创建. 发券创建在CrmTaskProviderImpl
        if (Objects.equals(createJobDTO.getType(), CrmJobEnum.Type.COUPON.getCode())) {
            throw new ParamsException("不支持用此接口创建发券任务");
        }

        return CrmJob.builder()
                .businessType(CrmJobEnum.BusinessType.CRM.getCode())
                .jobName(createJobDTO.getJobName())
                .description(createJobDTO.getDescription())
                .type(createJobDTO.getType())
                .startTime(createJobDTO.getStartTime())
                .endTime(createJobDTO.getEndTime())
                .creator(this.getAdminId().longValue())
                .categoryList(JSONObject.toJSONString(createJobDTO.getCategoryList()))
                .merchantSelectionType(createJobDTO.getMerchantSelectionType())
                .build();
    }

    private List<CrmJobCompletionCriteria> buildCompletionCriteria(Long jobId, List<JobCompletionCriteriaDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            throw new ParamsException("任务完成判定条件不能为空");
        }

        return dtoList.stream().map(dto -> {
            CrmJobEnum.CompletionCriteriaType type = CrmJobEnum.CompletionCriteriaType.getByCode(dto.getCompletionType());
            // 不同完成条件的值不同,需要效验值是否合法.详情见枚举里的描述
            if (!type.getValueValidator().test(dto.getCompletionValue())) {
                throw new ParamsException(String.format("任务完成条件值[%s]不合法, 请检查", dto.getCompletionValue()));
            }

            CrmJobCompletionCriteria criteria = new CrmJobCompletionCriteria();
            criteria.setJobId(jobId);
            criteria.setCompletionType(dto.getCompletionType());
            criteria.setCompletionValue(dto.getCompletionValue());

            return criteria;
        }).collect(Collectors.toList());
    }
}
