package net.summerfarm.crm.service.crmjob.strategy;

import com.alibaba.excel.EasyExcel;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjob.CreateJobMerchantDetailDTO;
import net.summerfarm.crm.model.query.crmjob.JobImportExcelInput;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.CrmJobCommonHelper;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssGetUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通过excel导入的任务策略
 * excel包含m_id列和商品id列表列
 * 在此策略下,每个门店的商品/品类列表都是指定的
 */
@Service
public class ExcelWithMIdAndProductStrategy implements MerchantJobStrategy {

    @Resource
    private CrmJobCommonHelper crmJobCommonHelper;

    @Override
    public CrmJobEnum.MerchantSelectionType getMerchantSelectionType() {
        return CrmJobEnum.MerchantSelectionType.UPLOAD_M_ID_PRODUCT_EXCEL;
    }

    @Override
    public TaskImportResultCountVo createMerchantDetail(CrmJob crmJob, CreateJobMerchantDetailDTO dto) {
        if (StringUtils.isEmpty(dto.getOssKey())) {
            return null;
        }

        Map<Long, List<String>> mIdProductListMap;
        try {
            InputStream inputStream = OssGetUtil.getInputStream(dto.getOssKey());
            List<JobImportExcelInput> importList = EasyExcel.read(inputStream)
                    .head(JobImportExcelInput.class).sheet().doReadSync();
            mIdProductListMap = importList.stream()
                    .collect(Collectors.toMap(
                            JobImportExcelInput::getMId,
                            this::parseProductList,
                            (existingList, newList) -> {
                                existingList.addAll(newList);
                                return existingList;
                            }
                    ));
        } catch (Exception e) {
            throw new BizException("读取excel文件失败", e);
        }

        return crmJobCommonHelper.createMerchantJobDetailWithProduct(crmJob, mIdProductListMap);
    }

    private List<String> parseProductList(JobImportExcelInput input) {
        return Optional.ofNullable(input.getProductList())
                .map(productList ->
                        Arrays.stream(productList.split(","))
                                .map(String::trim)
                                .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }
}
