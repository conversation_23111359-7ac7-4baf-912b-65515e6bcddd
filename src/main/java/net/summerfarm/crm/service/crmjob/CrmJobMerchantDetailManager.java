package net.summerfarm.crm.service.crmjob;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantDetailRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CrmJobMerchantDetailManager {

    @Autowired
    private CrmJobRepository crmJobRepository;
    @Autowired
    private CrmJobMerchantDetailRepository crmJobMerchantDetailRepository;

    /**
     * 完成指定客户的行业属性打标任务
     * 
     * @param mId 客户id
     */
    public void completeDetailForMerchantBusiness(Long mId) {
        if (mId == null) {
            return;
        }
        // 查找客户行业属性打标任务
        List<CrmJob> crmJobs = crmJobRepository.lambdaQuery()
                .eq(CrmJob::getType, CrmJobEnum.Type.OTHER.getCode())
                .eq(CrmJob::getSubType, CrmJobEnum.SubType.MERCHANT_BUSINESS.getCode())
                .list();
        if (CollectionUtils.isEmpty(crmJobs)) {
            log.info("找不到客户行业属性打标任务");
            return;
        }
        // 查找客户行业属性打标任务下未完成的门店
        List<Long> crmJobIds = crmJobs.stream().map(CrmJob::getId).collect(Collectors.toList());
        List<CrmJobMerchantDetail> crmJobMerchantDetails = crmJobMerchantDetailRepository.lambdaQuery()
                .in(CrmJobMerchantDetail::getJobId, crmJobIds)
                .eq(CrmJobMerchantDetail::getMId, mId)
                .eq(CrmJobMerchantDetail::getStatus, 0)
                .list();
        if (CollectionUtils.isEmpty(crmJobMerchantDetails)) {
            log.info("该客户没有未完成的行业属性打标任务，mId:{}", mId);
            return;
        }

        // 完成客户行业属性打标任务的门店任务详情
        List<Long> crmJobMerchantDetailIds =
                crmJobMerchantDetails.stream().map(CrmJobMerchantDetail::getId).collect(Collectors.toList());
        crmJobMerchantDetailRepository.lambdaUpdate()
                .set(CrmJobMerchantDetail::getStatus, 1)
                .in(CrmJobMerchantDetail::getId, crmJobMerchantDetailIds)
                .update();
    }

}
