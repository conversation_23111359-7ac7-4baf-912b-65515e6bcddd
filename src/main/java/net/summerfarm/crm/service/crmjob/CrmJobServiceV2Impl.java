package net.summerfarm.crm.service.crmjob;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.enums.CrmJobEnum.JobMerchantLabel;
import net.summerfarm.crm.factory.CrmJobMerchantDetailStrategyFactoryV2;
import net.summerfarm.crm.mapper.repository.job.CrmJobCompletionCriteriaRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.model.convert.crmjobv2.CrmJobV2Converter;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobCompletionCriteria;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobMerchantDetailV2DTO;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobV2DTO;
import net.summerfarm.crm.model.dto.crmjobv2.UpdateJobV2DTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.strategy.v2.MerchantJobStrategyV2;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * CRM任务服务V2实现
 * 使用V2策略模式，不在旧表中存储item信息
 */
@Service
@Slf4j
public class CrmJobServiceV2Impl extends BaseService implements CrmJobServiceV2 {

    @Resource
    private CrmJobRepository crmJobRepository;
    @Resource
    private CrmJobCompletionCriteriaRepository crmJobCompletionCriteriaRepository;
    @Resource
    private CrmJobMerchantDetailStrategyFactoryV2 crmJobMerchantDetailStrategyFactoryV2;
    @Resource
    private CrmJobCommonHelperV2 crmJobCommonHelperV2;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskImportResultCountVo createJob(CreateJobV2DTO params) {
        log.info("开始创建任务V2，任务名称：{}, 任务类型：{}", params.getJobName(), params.getType());

        // 参数校验
        this.validateJobTime(params.getStartTime(), params.getEndTime());

        // 1. 创建任务主记录
        Long creator = Optional.ofNullable(this.getAdminId()).map(Integer::longValue).orElse(null);
        CrmJob crmJob = CrmJobV2Converter.INSTANCE.toEntity(params, creator);
        crmJobRepository.save(crmJob);
        Long jobId = crmJob.getId();
        log.info("创建任务主记录成功，任务ID：{}", jobId);

        // 2. 创建任务完成判定条件
        List<CrmJobCompletionCriteria> criteriaList = params.getCompletionCriteriaList().stream()
                .map(it -> CrmJobV2Converter.INSTANCE.toEntity(it, jobId))
                .collect(Collectors.toList());
        crmJobCompletionCriteriaRepository.saveBatch(criteriaList, 10);

        // 3. 根据人群选择方式，使用V2策略创建门店详情和item记录
        MerchantJobStrategyV2 merchantJobStrategyV2 = crmJobMerchantDetailStrategyFactoryV2
                .getMerchantJobStrategyV2(crmJob.getMerchantSelectionType());
        CreateJobMerchantDetailV2DTO detailDTO = new CreateJobMerchantDetailV2DTO(
                params.getMIdList(),
                params.getMerchantItemMap(),
                params.getOssKey(),
                params.getItemType(),
                params.getJobMerchantLabelMap());
        TaskImportResultCountVo result = merchantJobStrategyV2.createMerchantDetailV2(crmJob, detailDTO);

        // 4. 如果item列表不为空,说明是给"整个任务"创建的item,即,每个门店的任务都相同. 这时候, 只需要储存jobId, mId=-1 + items即可
        this.addJobItems(crmJob, params.getItemType(), params.getItemList(), params.getJobMerchantLabelMap());

        log.info("任务V2创建完成，任务ID：{}", jobId);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskImportResultCountVo updateJob(UpdateJobV2DTO params) {
        log.info("开始更新任务V2，任务ID：{}", params.getJobId());

        // 1. 获取现有任务
        CrmJob existingJob = crmJobRepository.getById(params.getJobId());

        // 2. 参数校验
        if (existingJob == null || CrmJobEnum.Status.isCanceled(existingJob.getStatus())
                || existingJob.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ParamsException("无法编辑已结束或已停用的任务");
        }
        if (params.getEndTime() != null) {
            this.validateJobTime(existingJob.getStartTime(), params.getEndTime());
        }

        // 3. 更新任务基本信息（如果需要）
        Long updater = Optional.ofNullable(this.getAdminId()).map(Integer::longValue).orElse(null);
        crmJobRepository.lambdaUpdate()
                .set(StringUtils.isNotBlank(params.getJobName()), CrmJob::getJobName, params.getJobName())
                .set(params.getEndTime() != null, CrmJob::getEndTime, params.getEndTime())
                .set(StringUtils.isNotBlank(params.getDescription()), CrmJob::getDescription, params.getDescription())
                .set(updater != null, CrmJob::getUpdater, updater)
                .set(CrmJob::getUpdateTime, LocalDateTime.now())
                .eq(CrmJob::getId, params.getJobId())
                .update();


        // 4. 更新门店相关信息（如果需要）
        MerchantJobStrategyV2 merchantJobStrategyV2 = crmJobMerchantDetailStrategyFactoryV2
                .getMerchantJobStrategyV2(existingJob.getMerchantSelectionType());
        CreateJobMerchantDetailV2DTO createJobMerchantDetailV2DTO = new CreateJobMerchantDetailV2DTO(
                params.getMIdList(),
                params.getMerchantItemMap(),
                params.getOssKey(),
                params.getItemType(),
                params.getJobMerchantLabelMap());
        TaskImportResultCountVo result = merchantJobStrategyV2.createMerchantDetailV2(existingJob, createJobMerchantDetailV2DTO);

        // 5. 如果item列表不为空,说明是给"整个任务"新增的item,即,每个门店的任务都相同. 这时候, 只需要储存jobId, mId=-1 + items即可
        this.addJobItems(existingJob, params.getItemType(), params.getItemList(), params.getJobMerchantLabelMap());

        log.info("任务V2更新完成，任务ID：{}", params.getJobId());
        return result;
    }

    // ------------------------- private methods ------------------------

    /**
     * 校验时间范围
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @throws ParamsException 当结束时间早于开始时间时抛出异常
     */
    private void validateJobTime(LocalDateTime startTime, LocalDateTime endTime) {
        if (endTime.isBefore(startTime)) {
            throw new ParamsException("任务结束时间不能早于任务开始时间");
        }
    }

    /**
     * 将itemList添加到crmJob中
     * 如果item列表不为空,说明是给"整个任务"创建的item,即,每个门店的任务都相同. 这时候, 只需要储存jobId, mId=-1 + items即可
     *
     * @param crmJob   任务信息
     * @param itemType item类型
     * @param itemList item列表
     * @param map 
     */
    private void addJobItems(CrmJob crmJob, Integer itemType, List<String> itemList, Map<Long, CrmJobEnum.JobMerchantLabel> jobMerchantLabelMap) {
        if (CollectionUtil.isNotEmpty(itemList)) {
            crmJobCommonHelperV2.createMerchantJobDetailV2(
                    crmJob,
                    itemType,
                    Collections.singletonMap(-1L, itemList),
                    jobMerchantLabelMap
            );
        }
    }
}
