package net.summerfarm.crm.service.crmjob;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.enums.PerformanceOrderSourceEnum;
import net.summerfarm.crm.es.mapper.XianmuMerchantCrmMapper;
import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import net.summerfarm.crm.mapper.manage.CrmSalesCityMapper;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantDetailRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantItemRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.mapper.repository.performance.CustPerformanceCommRepository;
import net.summerfarm.crm.model.convert.crmjobv2.CrmJobV2Converter;
import net.summerfarm.crm.model.convert.salesperformance.HighValueCustomerV2Converter;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.query.crmjobv2.CrmJobMerchantListQueryV2;
import net.summerfarm.crm.model.vo.crmjobv2.CrmJobMerchantV2VO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailV2VO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.xianmu.common.cache.InMemoryCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;

/**
 * CRM任务查询服务V2实现
 */
@Service
@Slf4j
public class CrmJobQueryServiceV2Impl extends BaseService implements CrmJobQueryServiceV2 {

    @Resource
    private CrmSalesCityMapper crmSalesCityMapper;
    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private CrmJobRepository crmJobRepository;
    @Resource
    private CrmJobMerchantDetailRepository crmJobMerchantDetailRepository;
    @Resource
    private CrmJobMerchantItemRepository crmJobMerchantItemRepository;
    @Resource
    private XianmuMerchantCrmMapper xianmuMerchantCrmMapper;
    @Autowired
    private CustPerformanceCommRepository custPerformanceCommRepository;


    @Override
    public PageInfo<CrmJobMerchantV2VO> queryMerchantListForBdV2(CrmJobMerchantListQueryV2 query) {
        // 根据销售级别获取当前登录用户的bdId和管辖城市
        VisibleScope visibleScope = this.getVisibleScope();
        return this.queryMerchantList(query, visibleScope);
    }


    @Override
    public List<String> queryJobItem(Long jobId) {
        return crmJobMerchantItemRepository.queryJobItemByJobIdWithCache(jobId);
    }

    // ------------------------------------------------ private methods ------------------------------------------------

    private VisibleScope getVisibleScope() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            return null;
        }

        List<Integer> bdIds;
        List<String> cities;

        // 普通销售查看自己私海客户的任务
        if (topRankOrg.getRank() == BD) {
            bdIds = Collections.singletonList(topRankOrg.getBdId());
            cities = Collections.emptyList();
        }
        // 主管级别查看下属私海以及管辖城市公海客户的任务
        else {
            Set<CrmBdOrg> childrenBd = new HashSet<>(bdAreaConfigService.listChildrenBd());
            childrenBd.add(topRankOrg);

            bdIds = childrenBd.stream().map(CrmBdOrg::getBdId).collect(Collectors.toList());
            cities = crmSalesCityMapper
                    .listByBdOrgId(childrenBd.stream().map(CrmBdOrg::getId).collect(Collectors.toList()))
                    .stream().map(CrmSalesCity::getCity).collect(Collectors.toList());
        }

        return new VisibleScope(bdIds, cities);
    }

    private PageInfo<CrmJobMerchantV2VO> queryMerchantList(CrmJobMerchantListQueryV2 query, VisibleScope visibleScope) {
        if (query == null || visibleScope == null) {
            return new PageInfo<>();
        }

        CrmJobEnum.Type jobType = CrmJobEnum.Type.getByCode(this.getJobTypeByJobId(query.getJobId()));

        // 查询获取基础数据
        PageInfo<CrmJobMerchantDetail> pageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> {
                    crmJobMerchantDetailRepository.getBaseMapper()
                            .selectMerchantDetailWithItemsByQueryV2(jobType.getCode(), query, visibleScope.getBdIds(), visibleScope.getCities());
                });
        if (pageInfo.getList().isEmpty()) {
            return new PageInfo<>();
        }

        List<Long> mIds = pageInfo.getList().stream().map(CrmJobMerchantDetail::getMId).collect(Collectors.toList());

        // 查询门店的品
        LambdaQueryWrapper<CrmJobMerchantItem> wrapper = Wrappers.lambdaQuery(CrmJobMerchantItem.class)
                .in(CrmJobMerchantItem::getMId, mIds)
                .eq(CrmJobMerchantItem::getJobId, query.getJobId());

        // 品类履约任务的特殊筛选逻辑
        if (Objects.equals(CrmJobEnum.Type.CATEGORY_FULFILLMENT.getCode(), jobType.getCode())
                && query.getCategoryFulfillmentQuery() != null) {
            wrapper.in(
                    CollectionUtil.isNotEmpty(query.getCategoryFulfillmentQuery().getItemFilter()),
                    CrmJobMerchantItem::getItem,
                    query.getCategoryFulfillmentQuery().getItemFilter());
        }
        Map<Long, List<CrmJobMerchantItem>> itemMap = crmJobMerchantItemRepository.list(wrapper)
                .stream().collect(Collectors.groupingBy(CrmJobMerchantItem::getMId, Collectors.toList()));

        // 去es里查询门店详情
        Map<Long, XianmuMerchantCrm> esMerchantDetails = xianmuMerchantCrmMapper.selectByMIdIn(mIds)
                .stream().collect(Collectors.toMap(XianmuMerchantCrm::getMId, Function.identity()));
        
        // 潜力高价值客户任务查询高价值客户详情
        Map<Long, HighValueCustomerDetailV2VO> highValueCustomerDetailMap = new HashMap<>();
        if (Objects.equals(CrmJobEnum.Type.POTENTIAL_HIGH_VALUE_CUSTOMER.getCode(), jobType.getCode())) {
            custPerformanceCommRepository.listByCustIds(mIds, PerformanceOrderSourceEnum.XIANMU.getValue()).stream()
            .forEach(x -> highValueCustomerDetailMap.put(x.getCustId(), HighValueCustomerV2Converter.convertToHighValueCustomerDetailV2VO(x)));
        }

        // 转换为VO并返回
        return PageInfoConverter.toPageResp(pageInfo, detail -> {
            XianmuMerchantCrm esMerchantDetail = esMerchantDetails.get(detail.getMId());
            CrmJobMerchantV2VO crmJobMerchantVO = CrmJobV2Converter.INSTANCE.merchantJobDetailToVO(detail, itemMap.get(detail.getMId()), esMerchantDetail);
            crmJobMerchantVO.setHighValueCustomerDetail(highValueCustomerDetailMap.get(detail.getMId()));
            return crmJobMerchantVO;
        });
    }

    @InMemoryCache
    private Integer getJobTypeByJobId(Long jobId) {
        return Optional.ofNullable(crmJobRepository.lambdaQuery().select(CrmJob::getType).eq(CrmJob::getId, jobId).one())
                .map(CrmJob::getType)
                .orElseThrow(() -> new IllegalArgumentException(String.format("任务不存在, jobId: %s", jobId)));
    }

    /**
     * 可见范围内部类
     */
    @Data
    private static class VisibleScope {
        private final List<Integer> bdIds;
        private final List<String> cities;

        public VisibleScope(List<Integer> bdIds, List<String> cities) {
            this.bdIds = bdIds;
            this.cities = cities;
        }
    }
}
