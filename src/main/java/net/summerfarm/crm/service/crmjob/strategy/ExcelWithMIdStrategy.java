package net.summerfarm.crm.service.crmjob.strategy;

import com.alibaba.excel.EasyExcel;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjob.CreateJobMerchantDetailDTO;
import net.summerfarm.crm.model.query.crmjob.JobImportExcelInput;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.CrmJobCommonHelper;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssGetUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通过excel导入的任务策略
 * excel应该只包含m_id列
 * 在此策略下,由于没有指定每个门店的商品/品类列表,所以门店的商品/品类列表为空
 */
@Service
public class ExcelWithMIdStrategy implements MerchantJobStrategy {

    @Resource
    private CrmJobCommonHelper crmJobCommonHelper;

    @Override
    public CrmJobEnum.MerchantSelectionType getMerchantSelectionType() {
        return CrmJobEnum.MerchantSelectionType.UPLOAD_M_ID_EXCEL;
    }

    @Override
    public TaskImportResultCountVo createMerchantDetail(CrmJob crmJob, CreateJobMerchantDetailDTO dto) {
        if (StringUtils.isEmpty(dto.getOssKey())) {
            return null;
        }

        // excel中获取m_id列表
        List<Long> mIdList;
        try {
            InputStream inputStream = OssGetUtil.getInputStream(dto.getOssKey());
            List<JobImportExcelInput> importList = EasyExcel.read(inputStream)
                    .head(JobImportExcelInput.class).sheet().doReadSync();
            mIdList = importList.stream().map(JobImportExcelInput::getMId).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BizException("读取excel文件失败", e);
        }

        return crmJobCommonHelper.createMerchantJobDetailWithoutSpecifyProduct(crmJob, mIdList);
    }
}
