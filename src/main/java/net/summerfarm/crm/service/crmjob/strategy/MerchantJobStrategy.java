package net.summerfarm.crm.service.crmjob.strategy;

import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjob.CreateJobMerchantDetailDTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;

/**
 * 门店任务策略.
 * 门店详情与门店选择方式息息相关.选择商户的策略基本可以分为以下几种:
 * 1. 指定商户List
 * 2. 上传excel文件,包含m_id列
 * 3. 指定门店命中商品,同样应该是以excel文件的形式上传,包含m_id列和sku_id列
 */
public interface MerchantJobStrategy {

    CrmJobEnum.MerchantSelectionType getMerchantSelectionType();

    /**
     * 创建门店任务详情
     * 门店列表只允许新增,不允许删除/修改
     */
    TaskImportResultCountVo createMerchantDetail(CrmJob crmJob, CreateJobMerchantDetailDTO dto);
}
