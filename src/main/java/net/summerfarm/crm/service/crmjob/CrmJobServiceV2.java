package net.summerfarm.crm.service.crmjob;

import net.summerfarm.crm.model.dto.crmjobv2.CreateJobV2DTO;
import net.summerfarm.crm.model.dto.crmjobv2.UpdateJobV2DTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;

/**
 * CRM任务服务V2接口
 * 使用新的逻辑处理所有任务类型，放在item表里而非存成List
 * 所有任务类型都可以使用新逻辑创建和更新
 */
public interface CrmJobServiceV2 {

    /**
     * 创建任务V2
     * 使用新的逻辑处理所有任务类型，放在item表里而非存成List
     *
     * @param createJobV2DTO 创建任务V2DTO
     * @return 任务创建结果
     */
    TaskImportResultCountVo createJob(CreateJobV2DTO createJobV2DTO);

    /**
     * 更新任务V2
     * 使用新的逻辑处理所有任务类型，放在item表里而非存成List
     *
     * @param updateJobV2DTO 更新任务V2DTO
     * @return 任务更新结果
     */
    TaskImportResultCountVo updateJob(UpdateJobV2DTO updateJobV2DTO);
}
