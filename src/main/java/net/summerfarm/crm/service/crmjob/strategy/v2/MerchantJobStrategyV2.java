package net.summerfarm.crm.service.crmjob.strategy.v2;

import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobMerchantDetailV2DTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;

/**
 * 门店任务策略V2
 * 与V1的区别：不在CrmJobMerchantDetail中存储item信息，只存储在新的item表中
 */
public interface MerchantJobStrategyV2 {

    CrmJobEnum.MerchantSelectionType getMerchantSelectionType();

    /**
     * 创建/更新门店任务详情V2（参考V1设计：天然支持增量更新）
     * 1. 创建CrmJobMerchantDetail记录
     * 2. 创建CrmJobMerchantItem记录（存储item信息）
     *
     * @param crmJob 任务信息
     * @param dto    任务详情
     * @return 创建结果
     */
    TaskImportResultCountVo createMerchantDetailV2(CrmJob crmJob, CreateJobMerchantDetailV2DTO dto);
}
