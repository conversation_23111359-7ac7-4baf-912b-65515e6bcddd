package net.summerfarm.crm.service.crmjob.strategy.v2;

import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobMerchantDetailV2DTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.CrmJobCommonHelperV2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 给门店指定了item的任务策略
 */
@Service
public class MerchantItemMapStrategyV2 implements MerchantJobStrategyV2 {

    @Resource
    private CrmJobCommonHelperV2 crmJobCommonHelperV2;

    @Override
    public CrmJobEnum.MerchantSelectionType getMerchantSelectionType() {
        return CrmJobEnum.MerchantSelectionType.MERCHANT_ITEM_MAP;
    }

    @Override
    public TaskImportResultCountVo createMerchantDetailV2(CrmJob crmJob, CreateJobMerchantDetailV2DTO dto) {
        return crmJobCommonHelperV2.createMerchantJobDetailV2(crmJob, dto.getItemType(), dto.getMerchantItemMap(), dto.getJobMerchantLabelMap());
    }
}
