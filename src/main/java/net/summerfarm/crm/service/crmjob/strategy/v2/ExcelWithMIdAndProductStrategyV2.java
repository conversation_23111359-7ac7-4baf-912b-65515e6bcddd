package net.summerfarm.crm.service.crmjob.strategy.v2;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobMerchantDetailV2DTO;
import net.summerfarm.crm.model.query.crmjob.JobImportExcelInput;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.CrmJobCommonHelperV2;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssGetUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通过excel导入门店+商品的任务策略V2
 * excel包含m_id列和商品id列表列
 * 与V1的区别：不在CrmJobMerchantDetail中存储item信息，而是存储在新的item表中
 */
@Service
public class ExcelWithMIdAndProductStrategyV2 implements MerchantJobStrategyV2 {

    @Resource
    private CrmJobCommonHelperV2 crmJobCommonHelperV2;

    @Override
    public CrmJobEnum.MerchantSelectionType getMerchantSelectionType() {
        return CrmJobEnum.MerchantSelectionType.UPLOAD_M_ID_PRODUCT_EXCEL;
    }

    @Override
    public TaskImportResultCountVo createMerchantDetailV2(CrmJob crmJob, CreateJobMerchantDetailV2DTO dto) {
        if (StrUtil.isEmpty(dto.getOssKey())) {
            throw new BizException("Excel文件OSS Key不能为空");
        }

        // 从Excel中解析门店和商品信息（参考V1设计）
        Map<Long, List<String>> merchantProductMap;
        try {
            InputStream inputStream = OssGetUtil.getInputStream(dto.getOssKey());
            List<JobImportExcelInput> importList = EasyExcel.read(inputStream)
                    .head(JobImportExcelInput.class).sheet().doReadSync();
            merchantProductMap = importList.stream()
                    .collect(Collectors.toMap(
                            JobImportExcelInput::getMId,
                            this::parseProductList,
                            (existingList, newList) -> {
                                existingList.addAll(newList);
                                return existingList;
                            }
                    ));
        } catch (Exception e) {
            throw new BizException("读取excel文件失败", e);
        }

        return crmJobCommonHelperV2.createMerchantJobDetailV2(crmJob, dto.getItemType(), merchantProductMap, dto.getJobMerchantLabelMap());
    }

    /**
     * 解析商品列表（从Excel的商品字段中）
     */
    private List<String> parseProductList(JobImportExcelInput input) {
        if (StrUtil.isEmpty(input.getProductList())) {
            return new ArrayList<>();
        }

        // 假设商品列表是逗号分隔的字符串
        return Arrays.stream(input.getProductList().split(","))
                .map(String::trim)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());
    }
}
