package net.summerfarm.crm.service.crmjob;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.es.mapper.XianmuMerchantCrmMapper;
import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import net.summerfarm.crm.facade.AdminQueryFacade;
import net.summerfarm.crm.facade.dto.AdminDTO;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.repository.ProductsRepository;
import net.summerfarm.crm.model.convert.crmjob.CrmJobConverter;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.SkuActivityPriceDTO;
import net.summerfarm.crm.model.query.crmjob.CrmJobMerchantListQueryDTO;
import net.summerfarm.crm.model.query.crmjob.CrmJobQueryDTO;
import net.summerfarm.crm.model.query.crmjob.JobProductQueryDTO;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobMerchantVO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobVO;
import net.summerfarm.crm.model.vo.crmjob.JobProductVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;

@Service
@Slf4j
public class CrmJobQueryServiceImpl implements CrmJobQueryService {

    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private CrmSalesCityMapper crmSalesCityMapper;
    @Resource
    private CrmJobMapper crmJobMapper;
    @Resource
    private CrmJobMerchantDetailMapper crmJobMerchantDetailMapper;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;
    @Resource
    private ProductsRepository productsRepository;
    @Resource
    private XianmuMerchantCrmMapper xianmuMerchantCrmMapper;
    @Autowired
    private AdminQueryFacade adminQueryFacade;


    @Override
    public PageInfo<CrmJobVO> queryCrmJobList(CrmJobQueryDTO crmJobQueryDTO) {
        VisibleScope visibleScope = new VisibleScope(Collections.emptyList(), Collections.emptyList()); // 不限制可见范围
        return this.queryCrmJobList(crmJobQueryDTO, visibleScope);
    }

    @Override
    public PageInfo<CrmJobVO> queryCrmJobListForBd(CrmJobQueryDTO crmJobQueryDTO) {
        // 根据销售级别获取当前登录用户的bdId和管辖城市
        VisibleScope visibleScope = this.getVisibleScope();
        return this.queryCrmJobList(crmJobQueryDTO, visibleScope);
    }

    @Override
    public PageInfo<CrmJobMerchantVO> queryMerchantList(CrmJobMerchantListQueryDTO queryDTO) {
        VisibleScope visibleScope = new VisibleScope(Collections.emptyList(), Collections.emptyList()); // 不限制可见范围
        return this.queryMerchantList(queryDTO, visibleScope);
    }

    @Override
    public PageInfo<CrmJobMerchantVO> queryMerchantListForBd(CrmJobMerchantListQueryDTO queryDTO) {
        // 根据销售级别获取当前登录用户的bdId和管辖城市
        Integer queryScope = Optional.ofNullable(queryDTO).map(CrmJobMerchantListQueryDTO::getQueryScope).orElse(null);
        VisibleScope visibleScope = this.getVisibleScope(queryScope);
        return this.queryMerchantList(queryDTO, visibleScope);
    }

    @Override
    public List<JobProductVO> queryJobProduct(JobProductQueryDTO queryDTO) {
        CrmJob crmJob = crmJobMapper.selectById(queryDTO.getJobId());
        if (crmJob == null) {
            return Collections.emptyList();
        }

        return this.queryProductDetail(JSON.parseArray(crmJob.getProductList(), String.class), queryDTO.getAreaNo());
    }

    @Override
    public PageInfo<JobProductVO> pageJobProduct(JobProductQueryDTO queryDTO) {
        CrmJob crmJob = crmJobMapper.selectById(queryDTO.getJobId());
        if (crmJob == null) {
            return new PageInfo<>(Collections.emptyList());
        }

        List<String> skuIds = JSON.parseArray(crmJob.getProductList(), String.class);
        if (CollectionUtil.isEmpty(skuIds)) {
            return new PageInfo<>(Collections.emptyList());
        }
        // 分页
        int total = skuIds.size();
        int start = (queryDTO.getPageIndex() - 1) * queryDTO.getPageSize();
        int end = Math.min(start + queryDTO.getPageSize(), total);
        List<String> subSkuIds = skuIds.subList(start, end);

        // 根据skuId批量查找商品详情
        List<JobProductVO> jobProductVOS = this.queryProductDetail(subSkuIds, queryDTO.getAreaNo());
        Page<JobProductVO> page = new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize());
        if (!CollectionUtil.isEmpty(jobProductVOS)) {
            page.addAll(jobProductVOS);
        }
        page.setPages((total + queryDTO.getPageSize() - 1) / queryDTO.getPageSize());
        page.setTotal(total);

        return new PageInfo<>(page);
    }


    // ------------------------------------------------ private methods ------------------------------------------------

    /**
     * 获取销售级别对应的bdId和管辖城市
     * 普通销售只能看到私海里客户中有任务的任务
     * 主管视角看到手下销售私海及管辖城市公海客户中有任务的任务
     */
    private VisibleScope getVisibleScope() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            return null;
        }

        List<Integer> bdIds;
        List<String> cities;

        // 普通销售查看自己私海客户的任务
        if (topRankOrg.getRank() == BD) {
            bdIds = Collections.singletonList(topRankOrg.getBdId());
            cities = Collections.emptyList();
        }
        // 主管级别查看下属私海以及管辖城市公海客户的任务
        else {
            Set<CrmBdOrg> childrenBd = new HashSet<>(bdAreaConfigService.listChildrenBd());
            childrenBd.add(topRankOrg);

            bdIds = childrenBd.stream().map(CrmBdOrg::getBdId).collect(Collectors.toList());
            cities = crmSalesCityMapper
                    .listByBdOrgId(childrenBd.stream().map(CrmBdOrg::getId).collect(Collectors.toList()))
                    .stream()
                    .map(CrmSalesCity::getCity).collect(Collectors.toList());
        }

        return new VisibleScope(bdIds, cities);
    }

    /**
     * 获取销售级别对应的bdId和管辖城市
     * 普通销售只能看到私海里客户中有任务的任务
     * 主管视角根据queryScope取值的不同看到不同范围的任务：
     * 1、当queryScope为0时，只查看手下销售私海的客户
     * 2、当queryScope为1时，只查看管辖城市公海客户的任务
     * 3、其他情况默认查看手下销售私海及管辖城市公海客户中的任务
     */ 
     private VisibleScope getVisibleScope(Integer queryScope) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            return null;
        }

        List<Integer> bdIds;
        List<String> cities;

        // 普通销售查看自己私海客户的任务
        if (topRankOrg.getRank() == BD) {
            bdIds = Collections.singletonList(topRankOrg.getBdId());
            cities = Collections.emptyList();
        }
        // 主管级别默认查看下属私海以及管辖城市公海客户的任务
        else {
            Set<CrmBdOrg> childrenBd = new HashSet<>(bdAreaConfigService.listChildrenBd());
            childrenBd.add(topRankOrg);

            bdIds = childrenBd.stream().map(CrmBdOrg::getBdId).collect(Collectors.toList());
            cities = crmSalesCityMapper
                    .listByBdOrgId(childrenBd.stream().map(CrmBdOrg::getId).collect(Collectors.toList()))
                    .stream()
                    .map(CrmSalesCity::getCity).collect(Collectors.toList());
            // queryScope为0时，只查看手下销售私海的客户
            if (Integer.valueOf(0).equals(queryScope)) {
                cities = Collections.emptyList();
            }
            // queryScope为1时，只查看管辖城市公海客户的任务
            else if (Integer.valueOf(1).equals(queryScope)) {
                bdIds = Collections.emptyList();
            }
        }

        return new VisibleScope(bdIds, cities);
    }

    private PageInfo<CrmJobVO> queryCrmJobList(CrmJobQueryDTO crmJobQueryDTO, VisibleScope visibleScope) {
        if (crmJobQueryDTO == null || visibleScope == null) {
            return new PageInfo<>();
        }

        // 不传入时间参数时,默认查询近3个月的任务
        if (crmJobQueryDTO.getStartTime() == null && crmJobQueryDTO.getEndTime() == null) {
            crmJobQueryDTO.setStartTime(LocalDateTime.now().minusDays(90));
        }

        // 1. 查找符合条件的任务id
        PageHelper.startPage(crmJobQueryDTO.getPageIndex(), crmJobQueryDTO.getPageSize());
        List<Long> jobIds = crmJobMapper.selectDistinctIdByQuery(
                crmJobQueryDTO,
                visibleScope.bdIds,
                visibleScope.cities);
        if (jobIds.isEmpty()) {
            log.info("未找到符合条件的任务, crmJobQueryDTO: {}", crmJobQueryDTO);
            return new PageInfo<>();
        }
        PageInfo<Long> pageInfo = new PageInfo<>(jobIds);

        // 2. 根据任务id查找任务及任务完成条件
        List<CrmJobWithCrmJobCompletionCriteriaList> jobList = crmJobMapper.selectWithCriteriaByIdsIn(jobIds);
        List<CrmJobVO> crmJobVOS = CrmJobConverter.INSTANCE.crmJobWithCriteriaListToVOList(jobList);

        // 3. 补充任务创建人名称
        fillCrmJobCreatorName(crmJobVOS);

        // 复制分页信息
        PageInfo<CrmJobVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        resultPageInfo.setList(crmJobVOS);

        return resultPageInfo;
    }

    private void fillCrmJobCreatorName(List<CrmJobVO> crmJobVOS) {
        if (CollectionUtils.isEmpty(crmJobVOS)) {
            return;
        }
        List<Integer> creatorList = crmJobVOS.stream().map(CrmJobVO::getCreator).filter(Objects::nonNull).distinct()
                .map(Long::intValue).collect(Collectors.toList());
        List<AdminDTO> adminDTOS = adminQueryFacade.batchQueryAdminByIds(creatorList);
        if (CollectionUtils.isEmpty(adminDTOS)) {
            return;
        }
        Map<Integer, String> adminId2RealNameMap = adminDTOS.stream().collect(Collectors.toMap(AdminDTO::getAdminId, AdminDTO::getRealName, (v1, v2) -> v1));
        for (CrmJobVO crmJobVO : crmJobVOS) {
            if (crmJobVO.getCreator() != null && adminId2RealNameMap.get(crmJobVO.getCreator().intValue()) != null) {
                crmJobVO.setCreatorName(adminId2RealNameMap.get(crmJobVO.getCreator().intValue()));
            }
        }
    }

    private PageInfo<CrmJobMerchantVO> queryMerchantList(CrmJobMerchantListQueryDTO queryDTO, VisibleScope visibleScope) {
        if (queryDTO == null || visibleScope == null) {
            return new PageInfo<>();
        }

        // 1. 查找符合条件的门店详情
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        List<CrmJobMerchantDetail> merchantDetails = crmJobMerchantDetailMapper.selectByQuery(
                queryDTO,
                visibleScope.bdIds,
                visibleScope.cities);
        if (merchantDetails.isEmpty()) {
            log.info("未找到符合条件的门店详情, queryDTO: {}", queryDTO);
            return new PageInfo<>(Collections.emptyList());
        }
        PageInfo<CrmJobMerchantDetail> pageInfo = new PageInfo<>(merchantDetails);

        Map<Long, CrmJobMerchantDetail> merchantDetailMap = merchantDetails.stream()
                .collect(Collectors.toMap(CrmJobMerchantDetail::getMId, merchantDetail -> merchantDetail));

        // 2. 去es里查门店详情
        List<XianmuMerchantCrm> esMerchantDetails = xianmuMerchantCrmMapper.selectByMIdIn(new ArrayList<>(merchantDetailMap.keySet()));

        // 3. 组装结果
        List<CrmJobMerchantVO> crmJobMerchantVOS = esMerchantDetails.stream()
                .map(esMerchantDetail ->
                        {
                            CrmJobMerchantDetail crmJobMerchantDetail = merchantDetailMap.get(esMerchantDetail.getMId());
                            // 商品详情
                            List<JobProductVO> jobProductVOS = this.queryProductDetail(JSON.parseArray(crmJobMerchantDetail.getMerchantProductList(), String.class), esMerchantDetail.getAreaNo().intValue());
                            return CrmJobConverter.INSTANCE.merchantJobDetailToVO(esMerchantDetail, crmJobMerchantDetail, jobProductVOS);
                        }
                ).collect(Collectors.toList());

        // 复制分页信息
        PageInfo<CrmJobMerchantVO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        resultPageInfo.setList(crmJobMerchantVOS);

        return resultPageInfo;
    }

    /**
     * 根据skuId列表查询商品详情(包括区域价格,特价)
     */
    private List<JobProductVO> queryProductDetail(List<String> skuIds, Integer areaNo) {
        if (CollectionUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        // 根据skuId批量查找商品详情
        Map<String, CrmSkuMonthGmvVO> productDetailMap = productsRepository.getSkuBySkus(skuIds);

        // area_no为空时,不返回价格信息
        if (areaNo == null) {
            return productDetailMap.keySet().stream()
                    .map(skuId -> this.getJobProductVO(null, skuId, productDetailMap.get(skuId), null, null))
                    .filter(Objects::nonNull).collect(Collectors.toList());
        }

        // 根据skuId和areaNo批量查询商品价格
        Map<String, AreaSku> areaPriceMap = areaSkuMapper.selectBySkuInAndAreaNo(skuIds, areaNo)
                .stream().collect(Collectors.toMap(AreaSku::getSku, areaSku -> areaSku));
        // 根据skuId和areaNo批量查询特价
        Map<String, List<SkuActivityPriceDTO>> activityPriceMap = activityBasicInfoMapper.selectActivityPriceBySkuAndAreaNo(skuIds, areaNo)
                .stream().collect(Collectors.groupingBy(SkuActivityPriceDTO::getSku));

        return productDetailMap.keySet().stream()
                .map(skuId -> this.getJobProductVO(
                        areaNo,
                        skuId,
                        productDetailMap.get(skuId),
                        areaPriceMap.get(skuId),
                        this.findActivityPrice(activityPriceMap.get(skuId))
                )).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 粗略地查找特价. 可能不能准确地反映客户实际看到的价格,仅供销售参考
     */
    private BigDecimal findActivityPrice(List<SkuActivityPriceDTO> activityPriceList) {
        if (CollectionUtil.isEmpty(activityPriceList)) {
            return null;
        }

        // 按照活动结束时间排序,取最新的特价
        // 按isPermanent降序
        // 再按endTime降序
        return activityPriceList.stream()
                .min(Comparator.comparing(
                                SkuActivityPriceDTO::getIsPermanent,
                                Comparator.nullsLast(Comparator.reverseOrder())) // 按isPermanent降序
                        .thenComparing(
                                SkuActivityPriceDTO::getEndTime,
                                Comparator.nullsLast(Comparator.reverseOrder()))) // 按endTime降序
                .map(SkuActivityPriceDTO::getActivityPrice)
                .orElse(null);
    }

    private JobProductVO getJobProductVO(Integer areaNo,
                                         String skuId,
                                         CrmSkuMonthGmvVO crmSkuMonthGmvVO,
                                         AreaSku areaSku,
                                         BigDecimal activityPrice) {
        // area_no为空时,不返回价格信息
        if (areaNo == null) {
            return CrmJobConverter.INSTANCE.productToVO(crmSkuMonthGmvVO, null, null);
        }

        // 查找区域价格
        if (areaSku == null) {
            log.info("skuId: {}, areaNo: {} 未找到对应的区域价格", skuId, areaNo);
            return null;
        }
        // 查找特价
        return CrmJobConverter.INSTANCE.productToVO(crmSkuMonthGmvVO, areaSku, activityPrice);
    }

    @Data
    private static class VisibleScope {
        private final List<Integer> bdIds;
        private final List<String> cities;
    }
}
