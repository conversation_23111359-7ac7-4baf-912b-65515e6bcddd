package net.summerfarm.crm.service.crmjob.strategy;

import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjob.CreateJobMerchantDetailDTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.CrmJobCommonHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 指定门店的任务策略
 * 在此策略下,由于没有指定每个门店的商品/品类列表,所以门店的商品/品类列表为空
 */
@Service
public class ListStrategy implements MerchantJobStrategy {

    @Resource
    private CrmJobCommonHelper crmJobCommonHelper;

    @Override
    public CrmJobEnum.MerchantSelectionType getMerchantSelectionType() {
        return CrmJobEnum.MerchantSelectionType.MERCHANT_LIST;
    }

    @Override
    public TaskImportResultCountVo createMerchantDetail(CrmJob crmJob, CreateJobMerchantDetailDTO dto) {
        return crmJobCommonHelper.createMerchantJobDetailWithoutSpecifyProduct(crmJob, dto.getMIdList());
    }
}
