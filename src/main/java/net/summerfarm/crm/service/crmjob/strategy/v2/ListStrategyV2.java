package net.summerfarm.crm.service.crmjob.strategy.v2;

import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobMerchantDetailV2DTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.CrmJobCommonHelperV2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指定门店列表的任务策略V2
 * 在此策略下,由于没有指定每个门店的商品/品类列表, 所以Map里的商品列表都为空(也就是不会给每个mId创建item)
 */
@Service
public class ListStrategyV2 implements MerchantJobStrategyV2 {

    @Resource
    private CrmJobCommonHelperV2 crmJobCommonHelperV2;

    @Override
    public CrmJobEnum.MerchantSelectionType getMerchantSelectionType() {
        return CrmJobEnum.MerchantSelectionType.MERCHANT_LIST;
    }

    @Override
    public TaskImportResultCountVo createMerchantDetailV2(CrmJob crmJob, CreateJobMerchantDetailV2DTO dto) {
        Map<Long, List<String>> mIdItemMap = Optional.ofNullable(dto.getMIdList())
                .orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(Function.identity(), k -> new ArrayList<>()));

        return crmJobCommonHelperV2.createMerchantJobDetailV2(crmJob, dto.getItemType(), mIdItemMap, dto.getJobMerchantLabelMap());
    }
}
