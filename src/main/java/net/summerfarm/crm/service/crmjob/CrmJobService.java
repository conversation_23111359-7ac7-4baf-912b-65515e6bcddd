package net.summerfarm.crm.service.crmjob;

import net.summerfarm.crm.model.dto.crmjob.CreateJobDTO;
import net.summerfarm.crm.model.dto.crmjob.UpdateJobDTO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;

import java.util.List;

public interface CrmJobService {

    /**
     * 创建任务
     */
    TaskImportResultCountVo createJob(CreateJobDTO createJobDTO);

    /**
     * 更新任务.
     * 新增人群的选择方式应该与创建任务时的选择方式一致
     * 比如,上次使用excel导入的方式,这次也应该使用excel导入的方式
     * 已存在门店任务不会被重复添加或修改,即使指定品列表变动
     */
    TaskImportResultCountVo updateJob(UpdateJobDTO updateJobDTO);

    /**
     * 批量取消任务.
     * 已结束的任务无法被取消
     */
    void batchCancelJobs(List<Long> jobIdList);
}
