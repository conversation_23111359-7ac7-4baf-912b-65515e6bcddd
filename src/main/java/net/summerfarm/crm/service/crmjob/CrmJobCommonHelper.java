package net.summerfarm.crm.service.crmjob;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.common.client.provider.DownloadCenterProvider;
import net.summerfarm.common.client.req.DownloadCenterInitReq;
import net.summerfarm.common.client.req.DownloadCenterUploadReq;
import net.summerfarm.common.client.resp.DownloadCenterResp;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.enums.DownloadBizTypeEnum;
import net.summerfarm.crm.mapper.manage.CrmJobMapper;
import net.summerfarm.crm.mapper.manage.CrmJobMerchantDetailMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.crm.service.impl.CrmTaskServiceImpl.TEMP_FILE_PATH;

/**
 * 任务中心通用帮助类
 */
@Service
@Slf4j
public class CrmJobCommonHelper {

    @Resource
    private CrmJobMerchantDetailMapper crmJobMerchantDetailMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private CrmJobMapper crmJobMapper;
    @DubboReference
    private DownloadCenterProvider downloadCenterProvider;

    /**
     * 创建任务的门店详情,不指定每个门店的商品
     */
    public TaskImportResultCountVo createMerchantJobDetailWithoutSpecifyProduct(CrmJob crmJob, List<Long> mIdList) {
        if (CollectionUtil.isEmpty(mIdList)) {
            return null;
        }

        // 门店详情不指定商品时,商品列表为空
        Map<Long, List<String>> mIdProductMap = mIdList.stream().distinct().collect(Collectors.toMap(Function.identity(), mId -> new ArrayList<>()));
        return createCrmJobMerchantDetailInternal(crmJob, mIdProductMap, false);
    }

    /**
     * 创建任务的门店详情,指定每个门店的商品
     */
    public TaskImportResultCountVo createMerchantJobDetailWithProduct(CrmJob crmJob, Map<Long, List<String>> mIdProductMap) {
        return createCrmJobMerchantDetailInternal(crmJob, mIdProductMap, true);
    }


    /**
     * 创建任务的门店详情. 将结果上传至下载中心并返回结果
     * 基本所有门店上传方式都可以抽象成<商户id, 商品id列表>的形式
     * 门店不指定商品时,商品列表为空
     * 门店被指定商品时, 还需要更新任务的商品列表
     */
    private TaskImportResultCountVo createCrmJobMerchantDetailInternal(CrmJob crmJob,
                                                                       Map<Long, List<String>> mIdProductMap,
                                                                       boolean specifyMerchantProductMode) {
        // 初始校验
        if (crmJob == null) {
            throw new BizException("任务不存在");
        }
        if (CollectionUtil.isEmpty(mIdProductMap)) {
            log.info("任务{}传入的商户列表为空", crmJob.getId());
            return null;
        }

        // 任务详情插入结果初始化
        int successCount = 0;
        List<MerchantJobDetailError> errorDTOList = new ArrayList<>();

        // 用于校验的数据
        List<Long> existJobDetailMIds = crmJobMerchantDetailMapper.selectMIdByJobId(crmJob.getId()); // 已存在任务详情的商户
        // 获取任务的商品列表, 用于更新任务的商品列表
        Set<String> jobProductIds = Optional.ofNullable(JSON.parseArray(crmJob.getProductList(), String.class))
                .map(HashSet::new)
                .orElseGet(HashSet::new);

        // 清洗数据,把map的value中的空字符串去掉并去重
        mIdProductMap.replaceAll((mId, products) ->
                products.stream().distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList())
        );

        // 分批插入
        List<List<Long>> mIdSlices = CollectionUtil.split(new ArrayList<>(mIdProductMap.keySet()), 1000);
        for (List<Long> mIds : mIdSlices) {
            List<Long> validMerchants = merchantMapper.selectMIdByMId(mIds); // 查询有效的商户

            List<CrmJobMerchantDetail> crmJobMerchantDetailList = mIds.stream()
                    // 过滤出有效的商户
                    .filter(mId -> {
                        if (!validMerchants.contains(mId)) {
                            errorDTOList.add(new MerchantJobDetailError(mId, "门店不存在"));
                            return false;
                        }
                        if (existJobDetailMIds.contains(mId)) {
                            errorDTOList.add(new MerchantJobDetailError(mId, "该任务已存在该门店的任务信息,请勿重复创建"));
                            return false;
                        }
                        if (specifyMerchantProductMode && CollectionUtil.isEmpty(mIdProductMap.get(mId))) {
                            errorDTOList.add(new MerchantJobDetailError(mId, "商品列表不能为空"));
                            return false;
                        }
                        return true;
                    })
                    .map(mId -> {
                        CrmJobMerchantDetail crmJobMerchantDetail = new CrmJobMerchantDetail();
                        crmJobMerchantDetail.setJobId(crmJob.getId());
                        crmJobMerchantDetail.setMId(mId);
                        // 门店指定商品时,设置商品列表
                        if (specifyMerchantProductMode) {
                            List<String> products = mIdProductMap.get(mId);
                            crmJobMerchantDetail.setMerchantProductList(JSONObject.toJSONString(products));
                            crmJobMerchantDetail.setMerchantProductCnt(products.size());
                            // 如果任务的商品列表不包含商户商品列表中的商品,则加入到job商品列表中
                            jobProductIds.addAll(products);
                        }
                        return crmJobMerchantDetail;
                    }).collect(Collectors.toList());

            // 插入数据库
            if (CollectionUtil.isNotEmpty(crmJobMerchantDetailList)) {
                successCount += crmJobMerchantDetailMapper.insertList(crmJobMerchantDetailList);
            }
        }

        // 门店指定商品时,更新任务的商品列表
        if (specifyMerchantProductMode) {
            crmJob.setProductList(JSON.toJSONString(jobProductIds));
            crmJobMapper.updateById(
                    CrmJob.builder().productList(crmJob.getProductList()).build()
                    , crmJob.getId());
        }

        // 返回结果
        TaskImportResultCountVo resultCountVo = new TaskImportResultCountVo();
        resultCountVo.setSuccessCount(successCount);
        resultCountVo.setErrorCount(errorDTOList.size());

        // 有错误时,导出错误列表
        if (CollectionUtil.isNotEmpty(errorDTOList)) {
            String filename = String.format("任务%s新增客户列表导入结果.xlsx", crmJob.getId());
            File file = new File(System.getProperty(TEMP_FILE_PATH) + File.separator + filename);
            EasyExcel.write(file, MerchantJobDetailError.class).sheet().doWrite(errorDTOList);
            Long resId = this.createDownloadRecord(filename, crmJob.getCreator());
            this.uploadFile(filename, file, resId);
            resultCountVo.setResourceId(resId);
        }

        return resultCountVo;
    }


    private Long createDownloadRecord(String filename, Long adminId) {
        DownloadCenterInitReq req = new DownloadCenterInitReq();
        req.setFileExpiredDay(DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY);
        req.setBizType(DownloadBizTypeEnum.CRM_TASK_IMPORT_RESULT.getBizType());
        req.setFileName(filename);
        req.setAdminId(adminId);
        DubboResponse<DownloadCenterResp> response = downloadCenterProvider.initRecord(req);
        if (!response.isSuccess()) {
            throw new BizException("网络波动，请重试");
        }
        return response.getData().getResId();
    }

    private void uploadFile(String filename, File file, Long resId) {
        OssUploadResult upload = OssUploadUtil.upload(filename, file, OSSExpiredLabelEnum.THREE_DAY);
        DownloadCenterUploadReq req = new DownloadCenterUploadReq();
        req.setStatus(DownloadCenterEnum.Status.UPLOADED);
        req.setResId(resId);
        req.setFilePath(upload.getObjectOssKey());
        downloadCenterProvider.uploadFile(req);
        FileUtil.del(file);
    }

    @Data
    @AllArgsConstructor
    static class MerchantJobDetailError {
        @ColumnWidth(16)
        @ExcelProperty(value = "客户Id")
        private Long mId;

        @ColumnWidth(16)
        @ExcelProperty(value = "失败原因")
        private String reason;
    }
}
