package net.summerfarm.crm.service.crmjob;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.crm.common.util.DownloadCenterUploadUtils;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.enums.DownloadBizTypeEnum;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantDetailRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantItemRepository;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.domain.CrmJobMerchantItem;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.xianmu.common.exception.BizException;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 任务中心V2通用帮助类
 * <p>
 * V2设计理念：
 * 1. 简化设计：只有一个核心公共方法
 * 2. 分离存储：门店信息存储在CrmJobMerchantDetail，item信息存储在CrmJobMerchantItem
 * 3. 增量更新：支持V1风格的"只能新增不能删除"逻辑
 * 4. 错误处理：完整的错误收集和下载中心上传功能
 */
@Service
@Slf4j
public class CrmJobCommonHelperV2 {

    @Resource
    private CrmJobMerchantDetailRepository crmJobMerchantDetailRepository;
    @Resource
    private CrmJobMerchantItemRepository crmJobMerchantItemRepository;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private DownloadCenterUploadUtils downloadCenterUploadUtils;

    /**
     * 创建/更新任务的门店详情V2
     * 基本所有门店上传方式都可以抽象成<门店id, item列表>的形式
     * 已存在的门店任务详情只会新增item. 未存在的会创建门店任务详情.
     *
     * @param crmJob          任务信息
     * @param itemType        item类型
     * @param merchantItemMap 门店ID到item列表的映射
     * @return 处理结果
     */
    public TaskImportResultCountVo createMerchantJobDetailV2(CrmJob crmJob,
                                                             Integer itemType,
                                                             Map<Long, List<String>> merchantItemMap,
                                                             Map<Long, CrmJobEnum.JobMerchantLabel> jobMerchantLabelMap) {
        // 基础校验
        if (crmJob == null) {
            throw new BizException("任务不存在");
        }
        if (CollectionUtil.isEmpty(merchantItemMap)) {
            log.info("任务{}传入的门店列表为空", crmJob.getId());
            return new TaskImportResultCountVo(0, 0, null);
        }

        // 获取该jobId下, 已存在的门店
        List<Long> existingMerchantIds = crmJobMerchantDetailRepository.lambdaQuery()
                .eq(CrmJobMerchantDetail::getJobId, crmJob.getId()).list().stream()
                .map(CrmJobMerchantDetail::getMId).collect(Collectors.toList());

        // 分批处理门店
        List<MerchantJobDetailError> errorList = new ArrayList<>(merchantItemMap.size());
        int successCount = CollectionUtil.split(merchantItemMap.keySet(), 500).stream().map(
                batch -> {
                    // 查询有效门店
                    List<Long> validMerchants = merchantMapper.selectMIdByMId(batch);

                    // 1. 创建item记录
                    Map<Long, List<String>> itemMap = merchantItemMap.entrySet().stream()
                            .filter(entry -> {
                                Long mId = entry.getKey();
                                if (!validMerchants.contains(mId)) {
                                    errorList.add(new MerchantJobDetailError(mId, "门店不存在"));
                                    return false;
                                }
                                return true;
                            })
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    this.processItemRecords(crmJob.getId(), itemType, itemMap);

                    // 2. 创建门店详情记录
                    List<CrmJobMerchantDetail> detailsToInsert = itemMap.keySet().stream()
                            .filter(merchantId -> !existingMerchantIds.contains(merchantId))
                            .map(merchantId -> {
                                CrmJobMerchantDetail detail = new CrmJobMerchantDetail();
                                detail.setJobId(crmJob.getId());
                                detail.setMId(merchantId);
                                if (jobMerchantLabelMap != null && jobMerchantLabelMap.get(merchantId) != null) {
                                    detail.setJobMerchantLabel(jobMerchantLabelMap.get(merchantId).getValue());
                                }
                                return detail;
                            }).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(detailsToInsert)) {
                        crmJobMerchantDetailRepository.saveBatch(detailsToInsert, 1000);
                    }

                    return itemMap.size();
                }
        ).reduce(0, Integer::sum);


        // 构建返回结果
        TaskImportResultCountVo result = new TaskImportResultCountVo();
        result.setSuccessCount(successCount);
        result.setErrorCount(errorList.size());

        // 处理错误列表：上传到下载中心
        if (CollectionUtil.isNotEmpty(errorList)) {
            String filename = String.format("任务%s门店导入结果.xlsx", crmJob.getId());
            Long resourceId = downloadCenterUploadUtils.uploadToDownloadCenter(
                    filename, errorList, MerchantJobDetailError.class,
                    DownloadBizTypeEnum.CRM_TASK_IMPORT_RESULT,
                    Optional.ofNullable(crmJob.getCreator()).orElse(-1L),
                    DownloadCenterEnum.FileExpiredDayEnum.THREE_DAY
            );

            result.setResourceId(resourceId);
        }

        return result;
    }

    /**
     * 为门店新增item记录
     */
    private void processItemRecords(Long jobId, Integer itemType, Map<Long, List<String>> merchantItemMap) {
        List<Long> mIds = new ArrayList<>(merchantItemMap.keySet());
        if (CollectionUtil.isEmpty(mIds)) {
            return;
        }

        // 查询已存在的item记录
        Map<Long, List<String>> existingItems = crmJobMerchantItemRepository.lambdaQuery()
                .eq(CrmJobMerchantItem::getJobId, jobId)
                .in(CrmJobMerchantItem::getMId, mIds)
                .list().stream()
                .collect(Collectors.groupingBy(CrmJobMerchantItem::getMId, Collectors.mapping(CrmJobMerchantItem::getItem, Collectors.toList())));

        List<CrmJobMerchantItem> itemsToInsert = merchantItemMap.entrySet().stream()
                .map(entry -> {
                    Long mId = entry.getKey();
                    List<String> items = entry.getValue();
                    List<String> existingItemsForMId = existingItems.getOrDefault(mId, new ArrayList<>());
                    // 去除已存在的item
                    items.removeAll(existingItemsForMId);
                    return items.stream().map(item ->
                            buildCrmJobMerchantItem(jobId, mId, item, itemType)).collect(Collectors.toList());
                }).flatMap(List::stream).collect(Collectors.toList());

        // 批量插入item记录
        if (CollectionUtil.isNotEmpty(itemsToInsert)) {
            crmJobMerchantItemRepository.saveBatch(itemsToInsert, 1000);
            log.info("为任务{}创建了{}条item记录", jobId, itemsToInsert.size());
        }
    }

    /**
     * 构建CrmJobMerchantItem对象
     */
    private CrmJobMerchantItem buildCrmJobMerchantItem(Long jobId, Long merchantId, String item, Integer itemType) {
        CrmJobMerchantItem crmJobMerchantItem = new CrmJobMerchantItem();
        crmJobMerchantItem.setJobId(jobId);
        crmJobMerchantItem.setMId(merchantId);
        crmJobMerchantItem.setItem(item);
        crmJobMerchantItem.setItemType(itemType);
        return crmJobMerchantItem;
    }

    @Data
    @AllArgsConstructor
    static class MerchantJobDetailError {
        @ColumnWidth(16)
        @ExcelProperty(value = "客户Id")
        private Long mId;

        @ColumnWidth(16)
        @ExcelProperty(value = "失败原因")
        private String reason;
    }
}
