package net.summerfarm.crm.service.crmjob;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.crmjobv2.CrmJobMerchantListQueryV2;
import net.summerfarm.crm.model.vo.crmjobv2.CrmJobMerchantV2VO;

import java.util.List;

/**
 * CRM任务查询服务V2接口
 * 支持新的item表结构和新任务类型的查询需求
 */
public interface CrmJobQueryServiceV2 {

    /**
     * 查询当前登录bd视角的任务对应的门店列表V2
     * 普通销售只能看到私海里的门店
     * 主管视角看到手下销售私海及管辖城市公海的门店
     */
    PageInfo<CrmJobMerchantV2VO> queryMerchantListForBdV2(CrmJobMerchantListQueryV2 query);

    /**
     * 获取任务item
     * <p>
     * note: 2025-05-26 目前这版比较简单,因为v1版的5个任务都还在用老逻辑
     * 只考虑 [品类履约任务] 的话返回 List<String> 就够了. 未来如果要全面转 v2 版再进行拓展.
     *
     * @return 任务item
     */
    List<String> queryJobItem(Long jobId);
}
