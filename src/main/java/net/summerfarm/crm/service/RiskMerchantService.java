package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.domain.RiskMerchant;
import net.summerfarm.crm.model.query.riskMerchant.AuditRiskMerchantQuery;
import net.summerfarm.crm.model.query.riskMerchant.RiskMerchantDetailQuery;
import net.summerfarm.crm.model.query.riskMerchant.RiskMerchantQuery;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantDetailVO;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantListVO;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:45
 */
public interface RiskMerchantService {
    /**
     * 风控门店列表
     *
     * @param query 查询
     * @return {@link PageInfo}<{@link RiskMerchantListVO}>
     */
    PageInfo<RiskMerchantListVO> list(RiskMerchantQuery query);

    /**
     * 风控门店详情
     *
     * @param query 查询
     * @return {@link RiskMerchantDetailVO}
     */
    CommonResult<RiskMerchantDetailVO> riskMerchantDetail(RiskMerchantDetailQuery query);

    /**
     * 风控门店审核
     *
     * @param query 查询
     * @return {@link RiskMerchantDetailVO}
     */
    CommonResult<Void> auditRiskMerchant(AuditRiskMerchantQuery query);

    /**
     * 风控门店同步
     */
    void riskMerchantSync();

    /**
     * 创建风控门店
     *
     * @param merchantVO 商人签证官
     * @return {@link RiskMerchant}
     */
    RiskMerchant createRiskMerchant(MerchantVO merchantVO);
}
