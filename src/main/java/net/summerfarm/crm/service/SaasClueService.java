package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.dto.CrmClueDTO;
import net.summerfarm.crm.model.query.ClueDetailQuery;
import net.summerfarm.crm.model.query.SaasClueQuery;
import net.summerfarm.crm.model.vo.SaasClueClueVO;
import net.summerfarm.crm.model.vo.CrmClueBindVO;
import net.summerfarm.crm.model.vo.MidVO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;

public interface SaasClueService {
    /**
     * 线索列表
     *
     * @param cluClueQuery
     * @return
     */
    CommonResult<PageInfo<CrmClueDTO>> query(SaasClueQuery cluClueQuery, UserBase userBase);

    /**
     * 新增线索
     *
     * @param cluClueVO
     * @return
     */
    CommonResult<CrmClueDTO> insert(SaasClueClueVO cluClueVO, UserBase userBase);

    /**
     * 修改线索
     *
     * @param cluClueVO
     * @return
     */
    CommonResult<CrmClueDTO> update(SaasClueClueVO cluClueVO, UserBase userBase);

    /**
     * 线索详情
     *
     * @param clueDetailQuery
     * @return
     */
    CommonResult<CrmClueDTO> detail(ClueDetailQuery clueDetailQuery);

    /**
     * 绑定记录
     *
     * @param crmFollowVO
     * @return
     */
    CommonResult<Boolean> bind(CrmClueBindVO crmFollowVO, UserBase userBase);

    /**
     * 根据mid查询绑定的信息
     *
     * @param midVO
     * @return
     */
    CommonResult<Boolean> queryBybId(MidVO midVO, UserBase userBase);

}
