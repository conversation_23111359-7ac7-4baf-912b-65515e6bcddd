package net.summerfarm.crm.service.DataSynchronization.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.summerfarm.crm.service.DataSynchronization.DataSynchronizationInformationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 数据同步信息服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class DataSynchronizationInformationServiceImpl implements DataSynchronizationInformationService {

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Override
    @Transactional(readOnly = true)
    public DataSynchronizationInformation selectByTableName(String tableName) {
        if (!StringUtils.hasText(tableName)) {
            return null;
        }
        return dataSynchronizationInformationMapper.selectByTableName(tableName);
    }



    @Override
    @Transactional(readOnly = true)
    public boolean needSync(String tableName, String dateFlag) {
        if (!StringUtils.hasText(tableName)) {
            return false;
        }

        try {
            DataSynchronizationInformation syncInfo = selectByTableName(tableName);
            if (syncInfo == null) {
                log.debug("表 {} 没有同步记录，需要同步", tableName);
                return true;
            }
            
            // 使用传入的时间标识，如果为空则使用当前日期
            String targetDateFlag = StringUtils.hasText(dateFlag) ? dateFlag : 
                    new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            
            boolean needSync = !targetDateFlag.equals(String.valueOf(syncInfo.getDateFlag()));
            log.debug("表 {} 同步检查结果: {}, 目标日期: {}, 记录日期: {}", 
                    tableName, needSync ? "需要同步" : "无需同步", targetDateFlag, syncInfo.getDateFlag());
            return needSync;
            
        } catch (Exception e) {
            log.error("检查同步状态失败，表名: {}, 日期标识: {}", tableName, dateFlag, e);
            return true;
        }
    }
}