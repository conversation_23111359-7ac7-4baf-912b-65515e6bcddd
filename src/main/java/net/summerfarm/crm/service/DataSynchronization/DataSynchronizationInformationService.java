package net.summerfarm.crm.service.DataSynchronization;

import net.summerfarm.crm.model.domain.DataSynchronizationInformation;

/**
 * 数据同步信息服务接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface DataSynchronizationInformationService {

    /**
     * 根据表名查询同步信息
     *
     * @param tableName 表名
     * @return 同步信息
     */
    DataSynchronizationInformation selectByTableName(String tableName);



    /**
     * 检查是否需要同步
     * @param tableName 表名
     * @param dateFlag 时间标识，格式为yyyyMMddhh，可选参数，为null时使用当前日期
     * @return 是否需要同步
     */
    boolean needSync(String tableName, String dateFlag);

    /**
     * 检查是否需要同步
     * @param tableName 表名
     * @return 是否需要同步
     */
    default boolean needSync(String tableName) {
        return needSync(tableName, null);
    }
}