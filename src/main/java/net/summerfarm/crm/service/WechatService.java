package net.summerfarm.crm.service;

import net.summerfarm.crm.model.domain.MerchantSubAccount;
import net.summerfarm.crm.model.vo.wechat.CustomerCallBackResp;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/8/3 11:53
 */
public interface WechatService {
    /**
     * 客户联系回调
     *
     * @param resp 回调信息
     */
    void customerCallBack(CustomerCallBackResp resp);

    /**
     * 企微post请求
     *
     * @param uri  uri
     * @param data 数据
     * @return {@link String}
     */
    String post(String uri, String data);

    /**
     * 企微上传文件请求
     *
     * @param uri  uri
     * @param type 媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件（file）
     * @param file 文件
     * @return {@link String}
     */
    String uploadMedia(String uri, String type, File file);

    /**
     * 企微get请求
     *
     * @param uri  uri
     * @param data 数据
     * @return {@link String}
     */
    String get(String uri, Map<String, Object> data);


    /**
     * 获取访问令牌
     *
     * @param update 是否强制刷新
     * @return {@link String}
     */
    String getAccessToken(boolean update);

    /**
     * 修改门店的备注和手机号
     */
    void updateRemark(MerchantSubAccount account, String userId, String extendUserId);


    /**
     * 将客户从一个用户转移到另一个用户。 不需要修改维护关系 企业微信会在24小时候回调 删除+新增事件
     *
     * @param formUserId     转出用户的ID，即原始负责人的用户ID。
     * @param toUserId       转入用户的ID，即新负责人的用户ID。
     * @param externalUserId 客户的外部系统用户ID，用于唯一标识客户。
     * @param userName       客户的名字，用于显示或记录。
     */
    void transferCustomerList(String formUserId, String toUserId, String externalUserId, String userName);


    /**
     * 将客户从一个用户转移到另一个用户。 不需要修改维护关系 企业微信会在24小时候回调 删除+新增事件
     *
     * @param fromUserId      转出用户的ID，即原始负责人的用户ID。
     * @param toUserId        转入用户的ID，即新负责人的用户ID。
     * @param externalUserIds 客户的外部系统用户ID，用于唯一标识客户。
     */
    void transferCustomerList(String fromUserId, String toUserId, List<String> externalUserIds);

    /**
     * 调用企微离职分配接口, 将客户从一个用户转移到另一个用户。 不需要修改维护关系 企业微信会在24小时候回调 删除+新增事件
     * fromUser对应的销售的企微为离职状态才可使用
     * <a href="https://developer.work.weixin.qq.com/document/path/94081">企微接口文档</a>
     */
    void transferCustomerListForResignedBd(String fromUserId, String toUserId, List<String> externalUserIds);
}

