package net.summerfarm.crm.service.salesperformance.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.mapper.repository.BdCvalMtdPerformanceRepository;
import net.summerfarm.crm.mapper.repository.BdMtdCommRepository;
import net.summerfarm.crm.mapper.repository.CustMtdPerformanceRepository;
import net.summerfarm.crm.model.convert.salesperformance.HighValueCustomerConverter;
import net.summerfarm.crm.model.domain.BdCvalMtdPerformance;
import net.summerfarm.crm.model.domain.BdMtdComm;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CustMtdPerformance;
import net.summerfarm.crm.model.query.salesperformance.BdHighValueCustomerSummaryListQuery;
import net.summerfarm.crm.model.query.salesperformance.HighValueCustomerQuery;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerSummaryVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.HighValueCustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 高价值客户服务实现类
 */
@Slf4j
@Service
public class HighValueCustomerServiceImpl implements HighValueCustomerService {

    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private BdMtdCommRepository bdMtdCommRepository;
    @Resource
    private CustMtdPerformanceRepository custMtdPerformanceRepository;
    @Resource
    private BdCvalMtdPerformanceRepository bdCvalMtdPerformanceRepository;

    private static final Map<String, SFunction<BdCvalMtdPerformance, ?>> bdSortMap =
            MapUtil.unmodifiable(MapUtil.<String, SFunction<BdCvalMtdPerformance, ?>>builder()
                    .put("customerCount", BdCvalMtdPerformance::getSumDlvCustCnt)
                    .put("fulfillmentGmv", BdCvalMtdPerformance::getSumDlvRealAmt)
                    .put("spuCount", BdCvalMtdPerformance::getSumDlvSpuCnt)
                    .build());

    private static final Map<String, SFunction<CustMtdPerformance, ?>> custSortMap =
            MapUtil.unmodifiable(MapUtil.<String, SFunction<CustMtdPerformance, ?>>builder()
                    .put("fulfillmentGmv", CustMtdPerformance::getDlvRealAmt)
                    .put("spuCount", CustMtdPerformance::getDlvSpuCnt)
                    .build());


    @Override
    public HighValueCustomerSummaryVO getHighValueCustomerSummary() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            log.info("获取高价值客户汇总数据失败：未找到当前用户的职级信息");
            return null;
        }

        switch (topRankOrg.getRank()) {
            case BdAreaConfigEnum.SaleRank.BD:
                // 查询销售的高价值客户汇总数据
                LambdaQueryWrapper<BdMtdComm> queryWrapper = Wrappers.lambdaQuery(BdMtdComm.class)
                        .eq(BdMtdComm::getLastBdId, topRankOrg.getBdId());
                BdMtdComm bdMtdComm = bdMtdCommRepository.getOne(queryWrapper);

                if (bdMtdComm == null) {
                    log.info("未找到销售ID为{}的高价值客户汇总数据", topRankOrg.getBdId());
                    return null;
                }

                // 转换为VO并返回
                return HighValueCustomerConverter.INSTANCE.bdMtdCommToHighValueCustomerSummaryVO(bdMtdComm);
            case BdAreaConfigEnum.SaleRank.CITY_MANAGER:
                return this.getHighValueCustomerSummaryForM1(topRankOrg);
            default:
                log.info("M2及以上级别不返回高价值客户汇总数据, crmBdOrg={}", topRankOrg);
                return null;
        }
    }

    @Override
    public List<HighValueCustomerSummaryVO> listHighValueCustomerSummaryForManager(BdHighValueCustomerSummaryListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || BdAreaConfigEnum.SaleRank.CITY_MANAGER != topRankOrg.getRank()) {
            log.info("非M1级别不返回高价值客户汇总列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }

        // 获取该主管下属的所有销售ID
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream()
                .map(CrmBdOrg::getBdId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(bdIds)) {
            return Collections.emptyList();
        }

        // 构建查询条件
        LambdaQueryWrapper<BdCvalMtdPerformance> queryWrapper = Wrappers.lambdaQuery(BdCvalMtdPerformance.class)
                .in(BdCvalMtdPerformance::getBdId, bdIds);
        Optional.ofNullable(query.getCustomerType()).ifPresent(customerType ->
                queryWrapper.eq(BdCvalMtdPerformance::getCustValueLable, customerType.getDesc()));

        // 获取排序字段对应的数据库字段
        SFunction<BdCvalMtdPerformance, ?> sortField = bdSortMap.getOrDefault(
                query.getSortField(),
                null
        );
        queryWrapper.orderBy(sortField != null, SortDirectionEnum.ASC.equals(query.getSortDirection()), sortField);

        // 查询销售的高价值客户汇总数据
        List<BdCvalMtdPerformance> bdMtdComms = bdCvalMtdPerformanceRepository.list(queryWrapper);
        return HighValueCustomerConverter.INSTANCE.bdCvalMtdPerformanceListToHighValueCustomerSummaryVOList(bdMtdComms);
    }

    @Override
    public PageInfo<HighValueCustomerDetailVO> getBdHighValueCustomerDetail(HighValueCustomerQuery query) {
        // 如果bdId为空，则获取当前登录用户的bdId
        if (query.getBdId() == null) {
            CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                log.info("获取高价值客户详情失败：未找到当前用户的职级信息");
                return new PageInfo<>(Collections.emptyList());
            }
            query.setBdId(Long.valueOf(topRankOrg.getBdId()));
        }

        // 构建查询条件
        LambdaQueryWrapper<CustMtdPerformance> queryWrapper = Wrappers.lambdaQuery(CustMtdPerformance.class)
                .eq(CustMtdPerformance::getLastBdId, query.getBdId())
                .like(StringUtils.isNotBlank(query.getMname()), CustMtdPerformance::getLastCustName, query.getMname())
                .gt(CustMtdPerformance::getDlvSpuCnt, 0) // 只选spu > 0 的
                ;
        if (query.getCustValueLabel() != null) {
            queryWrapper.eq(CustMtdPerformance::getCustValueLable, query.getCustValueLabel().getDesc());
        }
        SFunction<CustMtdPerformance, ?> sortField = custSortMap.getOrDefault(query.getSortField(), null);
        queryWrapper.orderBy(sortField != null, SortDirectionEnum.ASC.equals(query.getSortDirection()), sortField);

        // 设置分页
        PageInfo<CustMtdPerformance> pageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> {
                    custMtdPerformanceRepository.list(queryWrapper);
                });

        return PageInfoConverter.toPageResp(pageInfo, HighValueCustomerConverter.INSTANCE::custMtdPerformanceToHighValueCustomerDetailVO);
    }


    // ------------------------------------ private methods -------------------------------------------


    /**
     * 获取主管下属销售的高价值客户汇总数据
     *
     * @param topRankOrg 主管信息
     * @return 高价值客户汇总数据
     */
    private HighValueCustomerSummaryVO getHighValueCustomerSummaryForM1(CrmBdOrg topRankOrg) {
        // 获取该主管下属的所有销售ID
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream()
                .map(CrmBdOrg::getBdId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(bdIds)) {
            return new HighValueCustomerSummaryVO();
        }

        // 查询销售的高价值客户汇总数据
        LambdaQueryWrapper<BdMtdComm> queryWrapper = Wrappers.lambdaQuery(BdMtdComm.class)
                .in(BdMtdComm::getLastBdId, bdIds);
        List<BdMtdComm> bdMtdComms = bdMtdCommRepository.list(queryWrapper);
        if (CollUtil.isEmpty(bdMtdComms)) {
            return new HighValueCustomerSummaryVO();
        }


        // 转换为VO
        List<HighValueCustomerSummaryVO> summaries = HighValueCustomerConverter.INSTANCE
                .bdMtdCommListToHighValueCustomerSummaryVOList(bdMtdComms);

        // 汇总数据
        HighValueCustomerSummaryVO result = new HighValueCustomerSummaryVO();
        result.setBdId(Long.valueOf(topRankOrg.getBdId()));
        result.setBdName(topRankOrg.getBdName());
        result.setCustomerCount(summaries.stream().mapToInt(HighValueCustomerSummaryVO::getCustomerCount).sum());
        result.setRewardAmount(summaries.stream()
                .map(HighValueCustomerSummaryVO::getRewardAmount)
                .filter(Objects::nonNull)
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add));
        result.setFulfillmentGmv(summaries.stream()
                .map(HighValueCustomerSummaryVO::getFulfillmentGmv)
                .filter(Objects::nonNull)
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add));
        result.setSpuCount(summaries.stream().mapToInt(HighValueCustomerSummaryVO::getSpuCount).sum());

        return result;
    }
}
