package net.summerfarm.crm.service.salesperformance;

import net.summerfarm.crm.model.query.salesperformance.*;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceSummaryVO;
import com.github.pagehelper.PageInfo;
import java.util.List;

public interface PbScorePerformanceService {

    /**
     * 获取PB标品汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @param query 查询参数
     * @return PB标品汇总数据
     */
    PbPerformanceSummaryVO getPbPerformanceSummary(PbPerformanceSummaryQuery query);

    /**
     * 获取销售PB标品汇总列表
     * 为 m1 列出下属 bd PB标品汇总
     * 普通bd, m2及以上不返回
     *
     * @param query 查询参数
     * @return PB标品汇总列表
     */
    List<PbPerformanceSummaryVO> listPbPerformanceSummaryForManager(PbPerformanceSummaryListQuery query);

    /**
     * 分页获取销售维度的PB标品数据详情
     * 如果bdId为空, 则返回当前登录bd的PB标品数据详情
     *
     * @param query 查询参数
     * @return PB标品数据详情分页数据
     */
    PageInfo<PbPerformanceDetailVO> getPbPerformanceData(PbPerformanceQuery query);

    /**
     * 获取利润积分汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @param query 查询参数
     * @return 利润积分汇总数据
     */
    ScorePerformanceSummaryVO getScorePerformanceSummary(ScorePerformanceSummaryQuery query);

    /**
     * 获取销售利润积分汇总列表
     * 为 m1 列出下属 bd 利润积分汇总
     * 普通bd, m2及以上不返回
     *
     * @param query 查询参数
     * @return 利润积分汇总列表
     */
    List<ScorePerformanceSummaryVO> listScorePerformanceSummaryForManager(ScorePerformanceSummaryListQuery query);

    /**
     * 分页获取销售维度的利润积分数据详情
     * 如果bdId为空, 则返回当前登录bd的利润积分数据详情
     *
     * @param query 查询参数
     * @return 利润积分数据详情分页数据
     */
    PageInfo<ScorePerformanceDetailVO> getScorePerformanceData(ScorePerformanceQuery query);
}
