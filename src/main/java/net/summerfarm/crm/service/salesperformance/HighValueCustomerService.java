package net.summerfarm.crm.service.salesperformance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.salesperformance.BdHighValueCustomerSummaryListQuery;
import net.summerfarm.crm.model.query.salesperformance.HighValueCustomerQuery;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerSummaryVO;

import java.util.List;

/**
 * 高价值客户服务接口
 */
public interface HighValueCustomerService {

    /**
     * 获取高价值客户汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @return 高价值客户汇总数据
     */
    HighValueCustomerSummaryVO getHighValueCustomerSummary();

    /**
     * 获取销售高价值客户汇总列表
     * 为 m1 列出下属 bd 高价值客户汇总
     * 普通bd, m2及以上不返回
     *
     * @param query 查询参数
     * @return 高价值客户汇总列表
     */
    List<HighValueCustomerSummaryVO> listHighValueCustomerSummaryForManager(BdHighValueCustomerSummaryListQuery query);

    /**
     * 获取BD的高价值客户详情
     * 如果bdId为空, 则返回当前登录bd的高价值客户详情
     *
     * @param query 查询参数
     * @return 高价值客户详情分页数据
     */
    PageInfo<HighValueCustomerDetailVO> getBdHighValueCustomerDetail(HighValueCustomerQuery query);
}
