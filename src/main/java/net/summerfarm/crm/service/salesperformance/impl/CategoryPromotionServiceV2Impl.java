package net.summerfarm.crm.service.salesperformance.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.mapper.repository.performance.CustCategoryPerformanceCommRepository;
import net.summerfarm.crm.model.convert.salesperformance.CategoryPromotionV2Converter;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CustCategoryPerformanceComm;
import net.summerfarm.crm.model.query.salesperformance.*;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSpuSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionV2VO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.CategoryPromotionServiceV2;
import net.xianmu.common.exception.BizException;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CategoryPromotionServiceV2Impl implements CategoryPromotionServiceV2 {

    @Autowired
    private BdAreaConfigService bdAreaConfigService;
    @Autowired
    private CustCategoryPerformanceCommRepository custCategoryPerformanceCommRepository;

    @Override
    public CategoryPromotionSummaryV2VO getCategoryPromotionSummary(CategoryPromotionSummaryQuery query) {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();
        List<Long> bdIds;
        // 普通销售只能看到自己的数据
        if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.BD) {
            bdIds = Collections.singletonList(Long.valueOf(topRankOrg.getBdId()));
        }
        // m1 看下属销售汇总
        else if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.CITY_MANAGER) {
            bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bdIds)) {
                return null;
            }
        }
        // m2及以上不返回
        else {
            log.info("M2及以上级别不返回品类推广汇总数据, crmBdOrg={}", topRankOrg);
            return null;
        }

        // 获取销售品类推广数据
        String customerType = query.getCustomerType() != null ? query.getCustomerType().getDesc() : null;
        List<CustCategoryPerformanceComm> custCategoryPerformanceComms =
                custCategoryPerformanceCommRepository.summaryByBdIds(bdIds, customerType, query.getSpuGroupList(), null, null);

        // 汇总数据
        CategoryPromotionSummaryV2VO result = this.aggregate(custCategoryPerformanceComms);
        result.setBdId(Long.valueOf(topRankOrg.getBdId()));
        result.setBdName(topRankOrg.getBdName());

        return result;
    }

    @Override
    public List<CategoryPromotionSummaryV2VO> listCategoryPromotionSummaryForManager(BdCategoryPromotionSummaryListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || BdAreaConfigEnum.SaleRank.CITY_MANAGER != topRankOrg.getRank()) {
            log.info("非M1级别不返回品类推广汇总列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }

        // 获取下属销售bdId
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bdIds)) {
            return Collections.emptyList();
        }

        // 获取下属销售的品类推广汇总数据
        String customerType = query.getCustomerType() != null ? query.getCustomerType().getDesc() : null;
        List<CustCategoryPerformanceComm> custCategoryPerformanceComms = custCategoryPerformanceCommRepository
                .summaryByBdIds(bdIds, customerType, query.getSpuGroupList(), query.getSortField(), query.getSortDirection());

        return CategoryPromotionV2Converter.convertToCategoryPromotionSummaryV2VOList(custCategoryPerformanceComms);
    }

    @Override
    public PageInfo<CategoryPromotionDetailV2VO> getCategoryPromotionData(CategoryPromotionQuery query) {
        // 如果bdId为空，则获取当前登录用户的bdId
        if (query.getBdId() == null) {
            query.setBdId(Long.valueOf(this.getCrmBdOrg().getBdId()));
        }

        // 获取品类推广数据
        String customerType = query.getCustomerType() != null ? query.getCustomerType().getDesc() : null;
        PageInfo<CustCategoryPerformanceComm> page = custCategoryPerformanceCommRepository.summaryByCustomer(query.getBdId(),
                customerType, query.getMname(), query.getSpuGroupList(), query.getSortField(), query.getSortDirection(),
                query.getPageIndex(), query.getPageSize());

        return PageInfoConverter.toPageResp(page, CategoryPromotionV2Converter::convertToCategoryPromotionDetailV2VO);
    }

    @Override
    public List<MerchantCategoryPromotionV2VO> getMerchantCategoryPromotion(MerchantCategoryPromotionQuery query) {
        String customerType = query.getCustomerType() != null ? query.getCustomerType().getDesc() : null;
        // 获取客户下品类推广数据
        List<CustCategoryPerformanceComm> custCategoryPerformanceComms = custCategoryPerformanceCommRepository.listByCustomer(
                query.getMId(), Optional.ofNullable(query.getBdId()).orElse(Long.valueOf(this.getCrmBdOrg().getBdId())),
                customerType, query.getSpuGroupList(), query.getSortField(), query.getSortDirection());

        return CategoryPromotionV2Converter.convertToMerchantCategoryPromotionV2VOList(custCategoryPerformanceComms);
    }

    @Override
    public PageInfo<CategoryPromotionSpuSummaryVO> getSpuGroupCategoryPromotionData(SpuGroupCategoryPromotionQuery query) {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();
        List<Long> bdIds;
        // 普通销售只能看到自己的数据
        if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.BD) {
            bdIds = Collections.singletonList(Long.valueOf(topRankOrg.getBdId()));
        }
        // m1 看下属销售或者指定销售的数据
        else if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.CITY_MANAGER) {
            bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(query.getBdIds())) {
                bdIds = Lists.newArrayList(CollectionUtils.intersection(bdIds, query.getBdIds()));
            }
            if (CollectionUtils.isEmpty(bdIds)) {
                log.warn("M1没有下属销售或者指定了非当前M1的下属销售, crmBdOrg={}, 指定的销售列表={}", topRankOrg, query.getBdIds());
                return new PageInfo<>();
            }
        }
        // m2及以上不返回
        else {
            log.info("M2及以上级别不返回SPU推广汇总数据, crmBdOrg={}", topRankOrg);
            return new PageInfo<>();
        }

        // 获取品类推广数据
        String customerType = query.getCustomerType() != null ? query.getCustomerType().getDesc() : null;
        PageInfo<CustCategoryPerformanceComm> page = custCategoryPerformanceCommRepository.summaryBySpuGroup(bdIds, customerType,
                query.getSortField(), query.getSortDirection(), query.getPageIndex(), query.getPageSize());
        return PageInfoConverter.toPageResp(page, CategoryPromotionV2Converter::convertToCategoryPromotionSpuSummaryVO);
    }

    @Override
    public List<String> getCategoryPromotionSpuGroupList() {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();
        List<Long> bdIds;
        // 普通销售只能看到自己的数据
        if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.BD) {
            bdIds = Collections.singletonList(Long.valueOf(topRankOrg.getBdId()));
        }
        // m1 看下属销售的数据
        else if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.CITY_MANAGER) {
            bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bdIds)) {
                log.warn("M1没有下属销售, crmBdOrg={}", topRankOrg);
                return Collections.emptyList();
            }
        }
        // m2及以上不返回
        else {
            log.info("M2及以上级别不返回SPU推广品类列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }
        List<String> spuGroupList = custCategoryPerformanceCommRepository.listSpuGroup(bdIds);
        if (CollectionUtils.isEmpty(spuGroupList)) {
            return Collections.emptyList();
        }
        return spuGroupList;
    }

    // ------------------------------------ private methods -------------------------------------------

    private CrmBdOrg getCrmBdOrg() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            throw new BizException("未找到您的销售身份信息, 请联系销售主管配置");
        }
        return topRankOrg;
    }

    private CategoryPromotionSummaryV2VO aggregate(List<CustCategoryPerformanceComm> custCategoryPerformanceComms) {
        CategoryPromotionSummaryV2VO categoryPromotionSummary = new CategoryPromotionSummaryV2VO();
        if (CollectionUtils.isEmpty(custCategoryPerformanceComms)) {
            return categoryPromotionSummary;
        }

        categoryPromotionSummary
                .setCustCnt(custCategoryPerformanceComms.stream().mapToInt(CustCategoryPerformanceComm::getCustCnt).sum());
        categoryPromotionSummary
                .setCategoryCommAmt(custCategoryPerformanceComms.stream().map(CustCategoryPerformanceComm::getCategoryCommAmt)
                        .filter(java.util.Objects::nonNull).reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add));
        categoryPromotionSummary.setMonthCategoryCustComm(
                custCategoryPerformanceComms.stream().map(CustCategoryPerformanceComm::getMonthCategoryCustComm)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        categoryPromotionSummary.setBigSkuCnt(custCategoryPerformanceComms.stream()
                .mapToDouble(item -> item.getBigSkuCnt() != null ? item.getBigSkuCnt() : 0.0D).sum());
        categoryPromotionSummary.setDlvRealCntToday(custCategoryPerformanceComms.stream()
                .mapToDouble(item -> item.getDlvRealCntToday() != null ? item.getDlvRealCntToday() : 0.0D).sum());
        categoryPromotionSummary.setDlvOrderCntToday(custCategoryPerformanceComms.stream()
                .mapToDouble(item -> item.getDlvOrderCntToday() != null ? item.getDlvOrderCntToday() : 0.0D).sum());
        categoryPromotionSummary.setDlvOtherCntToday(custCategoryPerformanceComms.stream()
                .mapToDouble(item -> item.getDlvOtherCntToday() != null ? item.getDlvOtherCntToday() : 0.0D).sum());
        categoryPromotionSummary.setMonthDlvRealCntToday(custCategoryPerformanceComms.stream()
                .mapToDouble(item -> item.getMonthDlvRealCntToday() != null ? item.getMonthDlvRealCntToday() : 0.0D).sum());
        categoryPromotionSummary.setMtdTxnSkuCnt(custCategoryPerformanceComms.stream()
                .mapToDouble(item -> item.getMtdTxnSkuCnt() != null ? item.getMtdTxnSkuCnt() : 0.0D).sum());
        categoryPromotionSummary.setTodayTxnSkuCnt(custCategoryPerformanceComms.stream()
                .mapToDouble(item -> item.getTodayTxnSkuCnt() != null ? item.getTodayTxnSkuCnt() : 0.0D).sum());
        return categoryPromotionSummary;
    }

}
