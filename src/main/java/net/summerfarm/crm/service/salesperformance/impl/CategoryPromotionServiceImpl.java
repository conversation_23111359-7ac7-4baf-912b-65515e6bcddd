package net.summerfarm.crm.service.salesperformance.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.mapper.repository.CustMtdCategoryCommRepository;
import net.summerfarm.crm.mapper.repository.SalesBdIncrementCategoryMtdRepository;
import net.summerfarm.crm.model.convert.salesperformance.CategoryPromotionConverter;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.SalesBdIncrementCategoryMtd;
import net.summerfarm.crm.model.dto.salesperformance.CategoryPromotionDTO;
import net.summerfarm.crm.model.query.salesperformance.BdCategoryPromotionSummaryListQuery;
import net.summerfarm.crm.model.query.salesperformance.CategoryPromotionQuery;
import net.summerfarm.crm.model.query.salesperformance.CategoryPromotionSummaryQuery;
import net.summerfarm.crm.model.query.salesperformance.MerchantCategoryPromotionQuery;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.CategoryPromotionService;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 品类推广服务实现类
 */
@Slf4j
@Service
public class CategoryPromotionServiceImpl implements CategoryPromotionService {

    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private SalesBdIncrementCategoryMtdRepository salesBdIncrementCategoryMtdRepository;
    @Resource
    private CustMtdCategoryCommRepository custMtdCategoryCommRepository;

    @Override
    public CategoryPromotionSummaryVO getCategoryPromotionSummary(CategoryPromotionSummaryQuery query) {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();

        List<Long> bdIds;

        // 普通销售只能看到自己的数据
        if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.BD) {
            bdIds = Collections.singletonList(Long.valueOf(topRankOrg.getBdId()));
        }
        // m1 看下属销售汇总
        else if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.CITY_MANAGER) {
            bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf).collect(Collectors.toList());
            if (CollUtil.isEmpty(bdIds)) {
                return new CategoryPromotionSummaryVO();
            }
        }
        // m2及以上不返回
        else {
            log.info("M2及以上级别不返回品类推广汇总数据, crmBdOrg={}", topRankOrg);
            return null;
        }

        // 获取销售品类推广数据
        List<CategoryPromotionDTO> dtoList = salesBdIncrementCategoryMtdRepository.getBaseMapper().getCategoryPromotionBySales(
                bdIds,
                query.getCustomerType() != null ? query.getCustomerType().getDesc() : null,
                query.getSpuGroupList(),
                null,
                null);

        // 汇总数据
        CategoryPromotionSummaryVO result = this.aggregate(dtoList);
        result.setBdId(Long.valueOf(topRankOrg.getBdId()));
        result.setBdName(topRankOrg.getBdName());

        return result;
    }

    @Override
    public List<CategoryPromotionSummaryVO> listCategoryPromotionSummaryForManager(BdCategoryPromotionSummaryListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || BdAreaConfigEnum.SaleRank.CITY_MANAGER != topRankOrg.getRank()) {
            log.info("非M1级别不返回品类推广汇总列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }

        // 获取下属销售bdId
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream()
                .map(CrmBdOrg::getBdId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(bdIds)) {
            return Collections.emptyList();
        }

        // 获取下属销售的品类推广汇总数据
        List<CategoryPromotionDTO> dtoList = salesBdIncrementCategoryMtdRepository.getBaseMapper().getCategoryPromotionBySales(
                bdIds,
                query.getCustomerType() != null ? query.getCustomerType().getDesc() : null,
                query.getSpuGroupList(),
                query.getSortField(),
                SortDirectionEnum.ASC.equals(query.getSortDirection()) ? "ASC" : "DESC");

        // 使用MapStruct转换为VO
        return CategoryPromotionConverter.INSTANCE.categoryPromotionDTOListToCategoryPromotionSummaryVOList(dtoList);
    }

    @Override
    public PageInfo<CategoryPromotionDetailVO> getCategoryPromotionData(CategoryPromotionQuery query) {
        // 如果bdId为空，则获取当前登录用户的bdId
        if (query.getBdId() == null) {
            query.setBdId(Long.valueOf(this.getCrmBdOrg().getBdId()));
        }

        // 获取品类推广数据
        PageInfo<CategoryPromotionDTO> page = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> {
                    custMtdCategoryCommRepository.getBaseMapper().getCategoryPromotionByCustomer(
                            query.getBdId(),
                            query.getMname(),
                            query.getCustomerType() != null ? query.getCustomerType().getDesc() : null,
                            query.getSpuGroupList(),
                            query.getSortField(),
                            SortDirectionEnum.ASC.equals(query.getSortDirection()) ? "ASC" : "DESC"
                    );
                });

        return PageInfoConverter.toPageResp(page, CategoryPromotionConverter.INSTANCE::categoryPromotionDTOToCategoryPromotionDetailVO);
    }

    @Override
    public List<MerchantCategoryPromotionVO> getMerchantCategoryPromotion(MerchantCategoryPromotionQuery query) {
        List<CategoryPromotionDTO> dtoList = custMtdCategoryCommRepository.getBaseMapper().getCategoryPromotionByMerchant(
                query.getMId(),
                query.getCustomerType() != null ? query.getCustomerType().getDesc() : null,
                Optional.ofNullable(query.getBdId()).orElse(Long.valueOf(this.getCrmBdOrg().getBdId())),
                query.getSpuGroupList(),
                query.getSortField(),
                SortDirectionEnum.ASC.equals(query.getSortDirection()) ? "ASC" : "DESC"
        );

        // 使用MapStruct转换为VO
        return CategoryPromotionConverter.INSTANCE.categoryPromotionDTOListToMerchantCategoryPromotionVOList(dtoList);
    }

    @Override
    public List<String> getCategoryPromotionSpuGroupList() {

        return this.getDistinctSpuGroup().stream()
                .map(SalesBdIncrementCategoryMtd::getSpuGroup)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    // ------------------------------------ private methods -------------------------------------------

    private CrmBdOrg getCrmBdOrg() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            throw new ParamsException("未找到您的销售身份信息, 请联系销售主管配置");
        }
        return topRankOrg;
    }

    private CategoryPromotionSummaryVO aggregate(List<CategoryPromotionDTO> list) {
        CategoryPromotionSummaryVO dto = new CategoryPromotionSummaryVO();
        if (CollUtil.isEmpty(list)) {
            return dto;
        }

        dto.setCustomerCount(list.stream().mapToInt(CategoryPromotionDTO::getCustomerCount).sum());
        dto.setRewardAmount(list.stream().map(CategoryPromotionDTO::getRewardAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setFulfillmentCount(list.stream().mapToDouble(CategoryPromotionDTO::getFulfillmentCount).sum());
        dto.setTransactionCount(list.stream().mapToDouble(CategoryPromotionDTO::getTransactionCount).sum());
        return dto;
    }

    @InMemoryCache
    private List<SalesBdIncrementCategoryMtd> getDistinctSpuGroup() {
        LambdaQueryWrapper<SalesBdIncrementCategoryMtd> queryWrapper = Wrappers.lambdaQuery(SalesBdIncrementCategoryMtd.class)
                .select(SalesBdIncrementCategoryMtd::getSpuGroup)
                .groupBy(SalesBdIncrementCategoryMtd::getSpuGroup);

        return salesBdIncrementCategoryMtdRepository.list(queryWrapper);
    }
}
