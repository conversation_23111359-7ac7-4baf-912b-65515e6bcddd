package net.summerfarm.crm.service.salesperformance.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.summerfarm.crm.mapper.repository.BdMtdCommRepository;
import net.summerfarm.crm.mapper.repository.CustMtdPerformanceRepository;
import net.summerfarm.crm.model.convert.salesperformance.ExcessSpuCustomerConverter;
import net.summerfarm.crm.model.domain.BdMtdComm;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CustMtdPerformance;
import net.summerfarm.crm.model.query.salesperformance.BdExcessSpuCustomerSummaryListQuery;
import net.summerfarm.crm.model.query.salesperformance.ExcessSpuCustomerQuery;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerSummaryVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.ExcessSpuCustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 超标SPU客户服务实现类
 */
@Slf4j
@Service
public class ExcessSpuCustomerServiceImpl implements ExcessSpuCustomerService {

    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private BdMtdCommRepository bdMtdCommRepository;
    @Resource
    private CustMtdPerformanceRepository custMtdPerformanceRepository;

    private static final Map<String, SFunction<BdMtdComm, ?>> bdSortMap =
            MapUtil.unmodifiable(MapUtil.<String, SFunction<BdMtdComm, ?>>builder()
                    .put("customerCount", BdMtdComm::getACustCnt)
                    .put("excessSpuCount", BdMtdComm::getMoreThanSpuCnt)
                    .put("rewardAmount", BdMtdComm::getASpuCommAmt)
                    .build());

    private static final Map<String, SFunction<CustMtdPerformance, ?>> custSortMap =
            MapUtil.unmodifiable(MapUtil.<String, SFunction<CustMtdPerformance, ?>>builder()
                    .put("excessSpuCount", CustMtdPerformance::getMoreThanSpuCnt)
                    .build());


    @Override
    public ExcessSpuCustomerSummaryVO getExcessSpuCustomerSummary() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            log.info("获取超标SPU客户汇总数据失败：未找到当前用户的职级信息");
            return null;
        }

        switch (topRankOrg.getRank()) {
            case BdAreaConfigEnum.SaleRank.BD:
                // 查询销售的超标SPU客户汇总数据
                LambdaQueryWrapper<BdMtdComm> queryWrapper = Wrappers.lambdaQuery(BdMtdComm.class)
                        .eq(BdMtdComm::getLastBdId, topRankOrg.getBdId());
                BdMtdComm bdMtdComm = bdMtdCommRepository.getOne(queryWrapper);

                if (bdMtdComm == null) {
                    log.info("未找到销售ID为{}的超标SPU客户汇总数据", topRankOrg.getBdId());
                    return null;
                }

                // 转换为VO并返回
                return ExcessSpuCustomerConverter.INSTANCE.bdMtdCommToExcessSpuCustomerSummaryVO(bdMtdComm);
            case BdAreaConfigEnum.SaleRank.CITY_MANAGER:
                return this.getExcessSpuCustomerSummaryForM1(topRankOrg);
            default:
                log.info("M2及以上级别不返回超标SPU客户汇总数据, crmBdOrg={}", topRankOrg);
                return null;
        }
    }

    @Override
    public List<ExcessSpuCustomerSummaryVO> listExcessSpuCustomerSummaryForManager(BdExcessSpuCustomerSummaryListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || BdAreaConfigEnum.SaleRank.CITY_MANAGER != topRankOrg.getRank()) {
            log.info("非M1级别不返回超标SPU客户汇总列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }

        // 获取该主管下属的所有销售ID
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream()
                .map(CrmBdOrg::getBdId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(bdIds)) {
            return Collections.emptyList();
        }

        // 构建查询条件
        LambdaQueryWrapper<BdMtdComm> queryWrapper = Wrappers.lambdaQuery(BdMtdComm.class)
                .in(BdMtdComm::getLastBdId, bdIds);

        // 获取排序字段对应的数据库字段
        SFunction<BdMtdComm, ?> sortField = bdSortMap.getOrDefault(
                query.getSortField(),
                BdMtdComm::getId
        );
        queryWrapper.orderBy(sortField != null, SortDirectionEnum.ASC.equals(query.getSortDirection()), sortField);

        // 查询销售的超标SPU客户汇总数据
        List<BdMtdComm> bdMtdComms = bdMtdCommRepository.list(queryWrapper);
        return ExcessSpuCustomerConverter.INSTANCE.bdMtdCommListToExcessSpuCustomerSummaryVOList(bdMtdComms);
    }

    @Override
    public PageInfo<ExcessSpuCustomerDetailVO> getBdExcessSpuCustomerDetail(ExcessSpuCustomerQuery query) {
        // 如果bdId为空，则获取当前登录用户的bdId
        if (query.getBdId() == null) {
            CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                log.info("获取超标SPU客户详情失败：未找到当前用户的职级信息");
                return new PageInfo<>(Collections.emptyList());
            }
            query.setBdId(Long.valueOf(topRankOrg.getBdId()));
        }

        // 构建查询条件
        LambdaQueryWrapper<CustMtdPerformance> queryWrapper = Wrappers.lambdaQuery(CustMtdPerformance.class)
                .eq(CustMtdPerformance::getLastBdId, query.getBdId())
                .like(StringUtils.isNotBlank(query.getMname()), CustMtdPerformance::getLastCustName, query.getMname())
                .gt(CustMtdPerformance::getMoreThanSpuCnt, 0) // 只查询超标SPU数大于0的客户
                .gt(CustMtdPerformance::getMoreThanSpuComm, 0) // 只查询超标SPU金额大于0的客户
                ;

        SFunction<CustMtdPerformance, ?> sortField = custSortMap.getOrDefault(query.getSortField(), null);
        queryWrapper.orderBy(sortField != null, SortDirectionEnum.ASC.equals(query.getSortDirection()), sortField);

        // 设置分页
        PageInfo<CustMtdPerformance> pageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPageInfo(() -> {
                    custMtdPerformanceRepository.list(queryWrapper);
                });

        return PageInfoConverter.toPageResp(pageInfo, ExcessSpuCustomerConverter.INSTANCE::custMtdPerformanceToExcessSpuCustomerDetailVO);
    }

    // ------------------------------------ private methods -------------------------------------------


    /**
     * 获取主管下属销售的超标SPU客户汇总数据
     *
     * @param topRankOrg 主管信息
     * @return 超标SPU客户汇总数据
     */
    private ExcessSpuCustomerSummaryVO getExcessSpuCustomerSummaryForM1(CrmBdOrg topRankOrg) {
        // 获取该主管下属的所有销售ID
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream()
                .map(CrmBdOrg::getBdId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(bdIds)) {
            return new ExcessSpuCustomerSummaryVO();
        }

        // 查询销售的超标SPU客户汇总数据
        LambdaQueryWrapper<BdMtdComm> queryWrapper = Wrappers.lambdaQuery(BdMtdComm.class)
                .in(BdMtdComm::getLastBdId, bdIds);
        List<BdMtdComm> bdMtdComms = bdMtdCommRepository.list(queryWrapper);
        if (CollUtil.isEmpty(bdMtdComms)) {
            return new ExcessSpuCustomerSummaryVO();
        }


        // 转换为VO
        List<ExcessSpuCustomerSummaryVO> summaries = ExcessSpuCustomerConverter.INSTANCE
                .bdMtdCommListToExcessSpuCustomerSummaryVOList(bdMtdComms);

        // 汇总数据
        ExcessSpuCustomerSummaryVO result = new ExcessSpuCustomerSummaryVO();
        result.setBdId(Long.valueOf(topRankOrg.getBdId()));
        result.setBdName(topRankOrg.getBdName());
        result.setCustomerCount(summaries.stream().mapToInt(ExcessSpuCustomerSummaryVO::getCustomerCount).sum());
        result.setExcessSpuCount(summaries.stream().mapToInt(ExcessSpuCustomerSummaryVO::getExcessSpuCount).sum());
        result.setRewardAmount(summaries.stream()
                .map(ExcessSpuCustomerSummaryVO::getRewardAmount)
                .filter(Objects::nonNull)
                .reduce(java.math.BigDecimal.ZERO, java.math.BigDecimal::add));

        return result;
    }
}
