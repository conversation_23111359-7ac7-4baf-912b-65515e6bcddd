package net.summerfarm.crm.service.salesperformance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.salesperformance.BdExcessSpuCustomerSummaryListQuery;
import net.summerfarm.crm.model.query.salesperformance.ExcessSpuCustomerQuery;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerSummaryVO;

import java.util.List;

/**
 * 超标SPU客户服务接口
 */
public interface ExcessSpuCustomerService {

    /**
     * 获取超标SPU客户汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @return 超标SPU客户汇总数据
     */
    ExcessSpuCustomerSummaryVO getExcessSpuCustomerSummary();

    /**
     * 获取销售超标SPU客户汇总列表
     * 为 m1 列出下属 bd 超标SPU客户汇总
     * 普通bd, m2及以上不返回
     *
     * @param query 查询参数
     * @return 超标SPU客户汇总列表
     */
    List<ExcessSpuCustomerSummaryVO> listExcessSpuCustomerSummaryForManager(BdExcessSpuCustomerSummaryListQuery query);

    /**
     * 获取BD的超标SPU客户详情
     * 如果bdId为空, 则返回当前登录bd的超标SPU客户详情
     *
     * @param query 查询参数
     * @return 超标SPU客户详情分页数据
     */
    PageInfo<ExcessSpuCustomerDetailVO> getBdExcessSpuCustomerDetail(ExcessSpuCustomerQuery query);
}
