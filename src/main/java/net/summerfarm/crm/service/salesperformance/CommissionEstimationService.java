package net.summerfarm.crm.service.salesperformance;

import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationTotalVO;

import java.util.List;

/**
 * 佣金预估服务接口
 */
public interface CommissionEstimationService {

    /**
     * 获取佣金预估总额
     * 普通销售看到的是他自己客户的总额
     * m1 看到的是下属销售总额之和
     * m2及以上不返回
     *
     * @return 佣金预估总额
     */
    CommissionEstimationTotalVO getCommissionEstimationTotal();

    /**
     * 获取销售佣金预估总额列表
     * 为m1 销售列出下属bd 佣金总额
     * 普通bd, m2及以上不返回
     *
     * @return 佣金预估总额列表
     */
    List<CommissionEstimationTotalVO> listCommissionEstimationTotalForManager();

    /**
     * 获取BD的佣金预估信息明细
     * 如果bdId为空, 则返回当前登录bd的佣金预估信息
     *
     * @param bdId 销售ID, 如果为空, 则返回当前登录bd的佣金预估信息
     * @return BD的佣金预估信息明细
     */
    CommissionEstimationDetailVO getBdCommissionEstimation(Long bdId);
}
