package net.summerfarm.crm.service.salesperformance;

import net.summerfarm.crm.model.query.salesperformance.*;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSpuSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionV2VO;
import com.github.pagehelper.PageInfo;
import java.util.List;

public interface CategoryPromotionServiceV2 {

    /**
     * 获取品类推广汇总数据
     * 普通销售看到的是自己客户汇总 
     * m1看到所有下属销售客户汇总之和 m2及以上不返回
     *
     * @param query 查询参数
     * @return 品类推广汇总数据
     */
    CategoryPromotionSummaryV2VO getCategoryPromotionSummary(CategoryPromotionSummaryQuery query);

    /**
     * 获取销售品类推广汇总列表
     * 为 m1 列出下属 bd 品类推广汇总
     * 普通bd, m2及以上不返回
     *
     * @param query 查询参数
     * @return 品类推广汇总列表
     */
    List<CategoryPromotionSummaryV2VO> listCategoryPromotionSummaryForManager(BdCategoryPromotionSummaryListQuery query);

    /**
     * 分页获取销售维度根据客户聚合的品类推广数据详情 
     * 如果bdId为空, 则返回当前登录bd的品类推广数据详情
     *
     * @param query 查询参数
     * @return 品类推广数据详情分页数据
     */
    PageInfo<CategoryPromotionDetailV2VO> getCategoryPromotionData(CategoryPromotionQuery query);

    /**
    * 获取客户维度品类推广数据
    *
    * @param query 查询参数
    * @return 客户维度品类推广数据
    */
    List<MerchantCategoryPromotionV2VO> getMerchantCategoryPromotion(MerchantCategoryPromotionQuery query);

    /**
     * 分页查询SPU维度品类推广数据
     * 普通销售看到的是自己的SPU汇总 
     * m1看到所有下属销售或者指定销售的SPU汇总
     * m2及以上不返回
     * 
     * @param query 查询参数
     * @return SPU维度品类推广数据
     */
    PageInfo<CategoryPromotionSpuSummaryVO> getSpuGroupCategoryPromotionData(SpuGroupCategoryPromotionQuery query);

    /**
     * 获取本期品类推广SPU分组列表, 用于下拉选择
     *
     * @return 品类推广SPU分组
     */
    List<String> getCategoryPromotionSpuGroupList();

}
