package net.summerfarm.crm.service.salesperformance.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.mapper.repository.BdMtdCommRepository;
import net.summerfarm.crm.model.convert.salesperformance.CommissionConverter;
import net.summerfarm.crm.model.domain.BdMtdComm;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationTotalVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.CommissionEstimationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 佣金预估服务实现类
 */
@Service
@Slf4j
public class CommissionEstimationServiceImpl implements CommissionEstimationService {

    @Resource
    private BdMtdCommRepository bdMtdCommRepository;
    @Resource
    private BdAreaConfigService bdAreaConfigService;

    @Override
    public CommissionEstimationTotalVO getCommissionEstimationTotal() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            log.info("获取佣金预估总额失败：未找到当前用户的职级信息");
            return null;
        }

        switch (topRankOrg.getRank()) {
            case BdAreaConfigEnum.SaleRank.BD:
                List<CommissionEstimationTotalVO> vos = this.getCommissionEstimationTotals(Collections.singletonList(topRankOrg.getBdId()));
                return vos.isEmpty() ? null : vos.get(0);
            case BdAreaConfigEnum.SaleRank.CITY_MANAGER:
                return this.getCommissionEstimationTotalForM1(topRankOrg);
            default:
                log.info("M2及以上级别不返回佣金预估总额, crmBdOrg={}", topRankOrg);
                return null;
        }
    }

    @Override
    public List<CommissionEstimationTotalVO> listCommissionEstimationTotalForManager() {
        // 获取当前用户的最高职级
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || topRankOrg.getRank() != BdAreaConfigEnum.SaleRank.CITY_MANAGER) {
            log.info("只有M1级别的销售主管可以查看下属销售的佣金预估总额");
            return Collections.emptyList();
        }

        // 获取下属销售bdId
        List<Integer> bdIds = this.getChildrenBds();

        return this.getCommissionEstimationTotals(bdIds);
    }

    @Override
    public CommissionEstimationDetailVO getBdCommissionEstimation(Long bdId) {
        // 如果bdId为空，则获取当前登录用户的bdId
        if (bdId == null) {
            CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                log.info("获取佣金预估明细失败：未找到当前用户的职级信息");
                return null;
            }
            bdId = Long.valueOf(topRankOrg.getBdId());
        }

        // 查询销售的佣金预估数据
        LambdaQueryWrapper<BdMtdComm> queryWrapper = Wrappers.lambdaQuery(BdMtdComm.class)
                .eq(BdMtdComm::getLastBdId, bdId);
        BdMtdComm bdMtdComm = bdMtdCommRepository.getOne(queryWrapper);

        if (bdMtdComm == null) {
            log.info("未找到销售ID为{}的佣金预估数据", bdId);
            return null;
        }

        // 转换为VO并返回
        return CommissionConverter.INSTANCE.bdMtdCommToCommissionEstimationDetailVO(bdMtdComm);
    }


    // ======================================== private methods ========================================

    private @NotNull List<Integer> getChildrenBds() {
        return bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).collect(Collectors.toList());
    }

    /**
     * 获取销售的佣金预估总额（支持单个和批量）
     *
     * @param bdIds 销售ID列表
     * @return 佣金预估总额列表
     */
    private List<CommissionEstimationTotalVO> getCommissionEstimationTotals(List<Integer> bdIds) {
        if (CollUtil.isEmpty(bdIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BdMtdComm> queryWrapper = Wrappers.lambdaQuery(BdMtdComm.class)
                .in(BdMtdComm::getLastBdId, bdIds);
        List<BdMtdComm> bdMtdCommList = bdMtdCommRepository.list(queryWrapper);
        return Optional.ofNullable(bdMtdCommList)
                .filter(CollUtil::isNotEmpty)
                .map(CommissionConverter.INSTANCE::bdMtdCommListToCommissionEstimationTotalVOList)
                .orElse(Collections.emptyList());
    }

    /**
     * 获取M1销售主管的佣金预估总额（下属销售总额之和）
     *
     * @param managerOrg M1销售主管组织信息
     * @return 佣金预估总额
     */
    private CommissionEstimationTotalVO getCommissionEstimationTotalForM1(CrmBdOrg managerOrg) {
        List<Integer> bdIds = this.getChildrenBds();
        if (CollUtil.isEmpty(bdIds)) {
            return null;
        }

        LambdaQueryWrapper<BdMtdComm> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BdMtdComm::getLastBdId, bdIds);
        List<BdMtdComm> bdMtdCommList = bdMtdCommRepository.list(queryWrapper);
        if (CollUtil.isEmpty(bdMtdCommList)) {
            log.info("未找到下属销售的佣金预估数据");
            return null;
        }
        return this.buildManagerTotalVO(managerOrg, bdMtdCommList);
    }

    /**
     * 构建M1主管的总佣金VO，使用Stream方式聚合各项金额
     */
    private CommissionEstimationTotalVO buildManagerTotalVO(CrmBdOrg managerOrg, List<BdMtdComm> bdMtdCommList) {
        CommissionEstimationTotalVO totalVO = new CommissionEstimationTotalVO();
        totalVO.setBdId(Long.valueOf(managerOrg.getBdId()));
        totalVO.setBdName(managerOrg.getBdName());
        totalVO.setTotalCommAmt(bdMtdCommList.stream().map(BdMtdComm::getTotalCommAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalVO.setACommisstionAmt(bdMtdCommList.stream().map(BdMtdComm::getACommisstionAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalVO.setACustCommAmt(bdMtdCommList.stream().map(BdMtdComm::getACustCommAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalVO.setASpuCommAmt(bdMtdCommList.stream().map(BdMtdComm::getASpuCommAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalVO.setCategoryCommAmt(bdMtdCommList.stream().map(BdMtdComm::getCategoryCommAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalVO.setOldCustComm(bdMtdCommList.stream().map(BdMtdComm::getOldCustComm).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        totalVO.setNewCustComm(bdMtdCommList.stream().map(BdMtdComm::getNewCustComm).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        return totalVO;
    }
}
