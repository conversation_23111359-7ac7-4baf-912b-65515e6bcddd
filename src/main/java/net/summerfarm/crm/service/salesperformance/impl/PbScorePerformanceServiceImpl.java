package net.summerfarm.crm.service.salesperformance.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.PbScoreMerchantTypeEnum;
import net.summerfarm.crm.mapper.repository.performance.CustPbScoreCommRepository;
import net.summerfarm.crm.mapper.repository.performance.M1PerformanceCommRepository;
import net.summerfarm.crm.model.convert.salesperformance.PbScorePerformanceConverter;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CustPbScoreComm;
import net.summerfarm.crm.model.domain.M1PerformanceComm;
import net.summerfarm.crm.model.query.salesperformance.*;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceSummaryVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.PbScorePerformanceService;
import net.xianmu.common.exception.BizException;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PbScorePerformanceServiceImpl implements PbScorePerformanceService {

    @Autowired
    private BdAreaConfigService bdAreaConfigService;
    @Autowired
    private CustPbScoreCommRepository custPbScoreCommRepository;
    @Autowired
    private M1PerformanceCommRepository m1PerformanceCommRepository;

    @Override
    public PbPerformanceSummaryVO getPbPerformanceSummary(PbPerformanceSummaryQuery query) {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();
        switch (topRankOrg.getRank()) {
            case BdAreaConfigEnum.SaleRank.BD:
                String merchantType = query.getMerchantType() != null ? query.getMerchantType().getValue() : null;
                List<CustPbScoreComm> custPbScoreComms = custPbScoreCommRepository
                        .summaryPbByBdIds(Collections.singletonList(Long.valueOf(topRankOrg.getBdId())), merchantType, query.getBdWorkZone(),
                                null, null);
                if (CollectionUtils.isEmpty(custPbScoreComms)) {
                    log.info("未找到销售ID为{}的PB标品汇总数据", topRankOrg.getBdId());
                    return null;
                }
                // 转换为VO并返回（一个BD只会有一条汇总数据）
                return PbScorePerformanceConverter.convertToPbPerformanceSummaryVO(custPbScoreComms.get(0));
            case BdAreaConfigEnum.SaleRank.CITY_MANAGER:
                // M1必须指定销售区域
                if (StringUtils.isEmpty(query.getBdWorkZone())) {
                    throw new BizException("M1查询PB标品汇总数据时必须指定销售区域");
                }
                // 有客户类型获取指定客户类型的M1绩效数据，没有则获取全部客户的M1绩效数据
                String m1MerchantType =
                        Optional.ofNullable(query.getMerchantType()).orElse(PbScoreMerchantTypeEnum.ALL).getValue();
                List<M1PerformanceComm> m1PerformanceComms =
                        m1PerformanceCommRepository.selectByM1Id(Long.valueOf(topRankOrg.getBdId()), m1MerchantType, query.getBdWorkZone());
                if (CollectionUtils.isEmpty(m1PerformanceComms)) {
                    log.info("未找到ID为{}的M1的PB标品汇总数据", topRankOrg.getBdId());
                    return null;
                }
                return PbScorePerformanceConverter.convertToPbPerformanceSummaryVO(m1PerformanceComms.get(0));
            default:
                log.info("M2及以上级别不返回PB标品汇总数据, crmBdOrg={}", topRankOrg);
                return null;
        }
    }

    @Override
    public List<PbPerformanceSummaryVO> listPbPerformanceSummaryForManager(PbPerformanceSummaryListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || BdAreaConfigEnum.SaleRank.CITY_MANAGER != topRankOrg.getRank()) {
            log.info("非M1级别不返回PB标品汇总列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }

        // 获取下属销售bdId
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bdIds)) {
            return Collections.emptyList();
        }

        // 获取下属销售的PB标品汇总数据
        String merchantType = query.getMerchantType() != null ? query.getMerchantType().getValue() : null;
        List<CustPbScoreComm> pbPerformanceSummaries = custPbScoreCommRepository.summaryPbByBdIds(bdIds, merchantType, query.getBdWorkZone(),
                query.getSortField(), query.getSortDirection());

        return PbScorePerformanceConverter.convertToPbPerformanceSummaryVOList(pbPerformanceSummaries);
    }

    @Override
    public PageInfo<PbPerformanceDetailVO> getPbPerformanceData(PbPerformanceQuery query) {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();
        if (query.getBdId() == null) {
            query.setBdId(Long.valueOf(topRankOrg.getBdId()));
        }

        String merchantType = query.getMerchantType() != null ? query.getMerchantType().getValue() : null;
        PageInfo<CustPbScoreComm> page = custPbScoreCommRepository.listPbByBdId(query.getBdId(), query.getMname(),
                merchantType, query.getSortField(), query.getSortDirection(), query.getPageIndex(), query.getPageSize());
        return PageInfoConverter.toPageResp(page, PbScorePerformanceConverter::convertToPbPerformanceDetailVO);
    }

    @Override
    public ScorePerformanceSummaryVO getScorePerformanceSummary(ScorePerformanceSummaryQuery query) {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();
        switch (topRankOrg.getRank()) {
            case BdAreaConfigEnum.SaleRank.BD:
                String merchantType = query.getMerchantType() != null ? query.getMerchantType().getValue() : null;
                List<CustPbScoreComm> custPbScoreComms = custPbScoreCommRepository
                        .summaryScoreByBdIds(Collections.singletonList(Long.valueOf(topRankOrg.getBdId())), merchantType,
                                query.getBdWorkZone(), null, null);
                if (CollectionUtils.isEmpty(custPbScoreComms)) {
                    log.info("未找到销售ID为{}的利润积分汇总数据", topRankOrg.getBdId());
                    return null;
                }
                // 转换为VO并返回（一个BD只会有一条汇总数据）
                return PbScorePerformanceConverter.convertToScorePerformanceSummaryVO(custPbScoreComms.get(0));
            case BdAreaConfigEnum.SaleRank.CITY_MANAGER:
                // M1必须指定销售区域
                if (StringUtils.isEmpty(query.getBdWorkZone())) {
                    throw new BizException("M1查询利润积分汇总数据时必须指定销售区域");
                }
                // 有客户类型获取指定客户类型的M1绩效数据，没有则获取全部客户的M1绩效数据
                String m1MerchantType =
                        Optional.ofNullable(query.getMerchantType()).orElse(PbScoreMerchantTypeEnum.ALL).getValue();
                List<M1PerformanceComm> m1PerformanceComms =
                        m1PerformanceCommRepository.selectByM1Id(Long.valueOf(topRankOrg.getBdId()), m1MerchantType, query.getBdWorkZone());
                if (CollectionUtils.isEmpty(m1PerformanceComms)) {
                    log.info("未找到ID为{}的M1的利润积分汇总数据", topRankOrg.getBdId());
                    return null;
                }
                return PbScorePerformanceConverter.convertToScorePerformanceSummaryVO(m1PerformanceComms.get(0));
            default:
                log.info("M2及以上级别不返回利润积分汇总数据, crmBdOrg={}", topRankOrg);
                return null;
        }
    }

    @Override
    public List<ScorePerformanceSummaryVO> listScorePerformanceSummaryForManager(ScorePerformanceSummaryListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || BdAreaConfigEnum.SaleRank.CITY_MANAGER != topRankOrg.getRank()) {
            log.info("非M1级别不返回利润积分汇总列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }

        // 获取下属销售bdId
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bdIds)) {
            return Collections.emptyList();
        }

        // 获取下属销售的利润积分汇总数据
        String merchantType = query.getMerchantType() != null ? query.getMerchantType().getValue() : null;
        List<CustPbScoreComm> scorePerformanceSummaries = custPbScoreCommRepository.summaryScoreByBdIds(bdIds, merchantType,
                query.getBdWorkZone(), query.getSortField(), query.getSortDirection());

        return PbScorePerformanceConverter.convertToScorePerformanceSummaryVOList(scorePerformanceSummaries);
    }

    @Override
    public PageInfo<ScorePerformanceDetailVO> getScorePerformanceData(ScorePerformanceQuery query) {
        CrmBdOrg topRankOrg = this.getCrmBdOrg();
        if (query.getBdId() == null) {
            query.setBdId(Long.valueOf(topRankOrg.getBdId()));
        }

        String merchantType = query.getMerchantType() != null ? query.getMerchantType().getValue() : null;
        PageInfo<CustPbScoreComm> page = custPbScoreCommRepository.listScoreByBdId(query.getBdId(), query.getMname(),
                merchantType, query.getSortField(), query.getSortDirection(), query.getPageIndex(), query.getPageSize());
        return PageInfoConverter.toPageResp(page, PbScorePerformanceConverter::convertToScorePerformanceDetailVO);
    }

    // ------------------------------------ private methods -------------------------------------------

    private CrmBdOrg getCrmBdOrg() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            throw new BizException("未找到您的销售身份信息, 请联系销售主管配置");
        }
        return topRankOrg;
    }

}
