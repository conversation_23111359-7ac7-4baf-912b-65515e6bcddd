package net.summerfarm.crm.service.salesperformance.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.mapper.repository.performance.CustPerformanceCommRepository;
import net.summerfarm.crm.model.convert.salesperformance.HighValueCustomerV2Converter;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CustPerformanceComm;
import net.summerfarm.crm.model.query.salesperformance.BdHighValueCustomerSummaryListQuery;
import net.summerfarm.crm.model.query.salesperformance.HighValueCustomerQuery;
import net.summerfarm.crm.model.query.salesperformance.HighValueCustomerSummaryQuery;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailV2VO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerSummaryV2VO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.HighValueCustomerServiceV2;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class HighValueCustomerServiceV2Impl implements HighValueCustomerServiceV2 {

    @Autowired
    private BdAreaConfigService bdAreaConfigService;
    @Autowired
    private CustPerformanceCommRepository custPerformanceCommRepository;

    @Override
    public HighValueCustomerSummaryV2VO getHighValueCustomerSummary(HighValueCustomerSummaryQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            log.info("获取高价值客户汇总数据失败：未找到当前用户的职级信息");
            return null;
        }

        String custValueLabel = (query.getCustomerType() != null) ? query.getCustomerType().getDesc() : null;
        switch (topRankOrg.getRank()) {
            case BdAreaConfigEnum.SaleRank.BD:
                // 查询销售的高价值客户汇总数据
                List<CustPerformanceComm> custPerformanceComms = custPerformanceCommRepository
                        .summaryByBdIds(Collections.singletonList(topRankOrg.getBdId().longValue()), custValueLabel, null, null);
                if (CollectionUtils.isEmpty(custPerformanceComms)) {
                    log.info("未找到销售ID为{}的高价值客户汇总数据", topRankOrg.getBdId());
                    return null;
                }
                // 转换为VO并返回（一个BD只会有一条汇总数据）
                return HighValueCustomerV2Converter.convertToHighValueCustomerSummaryV2VO(custPerformanceComms.get(0));
            case BdAreaConfigEnum.SaleRank.CITY_MANAGER:
                return this.getHighValueCustomerSummaryForM1(topRankOrg, custValueLabel);
            default:
                log.info("M2及以上级别不返回高价值客户汇总数据, crmBdOrg={}", topRankOrg);
                return null;
        }
    }

    @Override
    public List<HighValueCustomerSummaryV2VO> listHighValueCustomerSummaryForManager(BdHighValueCustomerSummaryListQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null || BdAreaConfigEnum.SaleRank.CITY_MANAGER != topRankOrg.getRank()) {
            log.info("非M1级别不返回高价值客户汇总列表, crmBdOrg={}", topRankOrg);
            return Collections.emptyList();
        }

        // 获取该主管下属的所有销售ID
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bdIds)) {
            return Collections.emptyList();
        }

        // 查询销售的高价值客户汇总数据
        String custValueLabel = (query.getCustomerType() != null) ? query.getCustomerType().getDesc() : null;
        List<CustPerformanceComm> custPerformanceComms = custPerformanceCommRepository.summaryByBdIds(bdIds, custValueLabel,
                query.getSortField(), query.getSortDirection());
        return HighValueCustomerV2Converter.convertToHighValueCustomerSummaryV2VOList(custPerformanceComms);
    }


    @Override
    public PageInfo<HighValueCustomerDetailV2VO> getBdHighValueCustomerDetail(HighValueCustomerQuery query) {
        // 如果bdId为空，则获取当前登录用户的bdId
        if (query.getBdId() == null) {
            CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
            if (topRankOrg == null) {
                log.info("获取高价值客户详情失败：未找到当前用户的职级信息");
                return new PageInfo<>(Collections.emptyList());
            }
            query.setBdId(Long.valueOf(topRankOrg.getBdId()));
        }

        String custValueLabel = (query.getCustValueLabel() != null) ? query.getCustValueLabel().getDesc() : null;
        PageInfo<CustPerformanceComm> pageInfo = custPerformanceCommRepository.listByBdId(query.getBdId(), custValueLabel,
                query.getMname(), query.getSortField(), query.getSortDirection(), query.getPageIndex(), query.getPageSize());
        return PageInfoConverter.toPageResp(pageInfo, HighValueCustomerV2Converter::convertToHighValueCustomerDetailV2VO);
    }


    private HighValueCustomerSummaryV2VO getHighValueCustomerSummaryForM1(CrmBdOrg topRankOrg, String custValueLabel) {
        // 获取该主管下属的所有销售ID
        List<Long> bdIds = bdAreaConfigService.listChildrenBd().stream().map(CrmBdOrg::getBdId).map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bdIds)) {
            return null;
        }
        // 查询销售的高价值客户汇总数据
        List<CustPerformanceComm> custPerformanceComms =
                custPerformanceCommRepository.summaryByBdIds(bdIds, custValueLabel, null, null);
        if (CollectionUtils.isEmpty(custPerformanceComms)) {
            log.info("未找到主管ID为{}下属销售的高价值客户汇总数据", topRankOrg.getBdId());
            return null;
        }
        // 转换为VO
        List<HighValueCustomerSummaryV2VO> summaries =
                HighValueCustomerV2Converter.convertToHighValueCustomerSummaryV2VOList(custPerformanceComms);

        // 汇总数据，将summaries中的数据合并到result中
        HighValueCustomerSummaryV2VO result = new HighValueCustomerSummaryV2VO();
        result.setBdId(Long.valueOf(topRankOrg.getBdId()));
        result.setBdName(topRankOrg.getBdName());
        result.setCustCnt(summaries.stream().mapToInt(HighValueCustomerSummaryV2VO::getCustCnt).sum());
        result.setCustCommAmt(summaries.stream().map(HighValueCustomerSummaryV2VO::getCustCommAmt).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setDlvRealAmt(summaries.stream().map(HighValueCustomerSummaryV2VO::getDlvRealAmt).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setDlvRealAmtToday(summaries.stream().map(HighValueCustomerSummaryV2VO::getDlvRealAmtToday).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setDlvMonthTodayTotalAmt(summaries.stream().map(HighValueCustomerSummaryV2VO::getDlvMonthTodayTotalAmt).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setDlvOrderAmtToday(summaries.stream().map(HighValueCustomerSummaryV2VO::getDlvOrderAmtToday).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setDlvOtherAmtToday(summaries.stream().map(HighValueCustomerSummaryV2VO::getDlvOtherAmtToday).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setDlvMonthTotalAmt(summaries.stream().map(HighValueCustomerSummaryV2VO::getDlvMonthTotalAmt).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setDlvSpuCnt(summaries.stream().mapToInt(HighValueCustomerSummaryV2VO::getDlvSpuCnt).sum());
        result.setDlvRealSpuCntToday(summaries.stream().mapToInt(HighValueCustomerSummaryV2VO::getDlvRealSpuCntToday).sum());
        result.setDlvMonthTodayTotalSpuCnt(summaries.stream().mapToInt(HighValueCustomerSummaryV2VO::getDlvMonthTodayTotalSpuCnt).sum());
        result.setDlvOrderSpuCntToday(summaries.stream().mapToInt(HighValueCustomerSummaryV2VO::getDlvOrderSpuCntToday).sum());
        result.setDlvOtherSpuCntToday(summaries.stream().mapToInt(HighValueCustomerSummaryV2VO::getDlvOtherSpuCntToday).sum());
        result.setDlvMonthTotalSpuCnt(summaries.stream().mapToInt(HighValueCustomerSummaryV2VO::getDlvMonthTotalSpuCnt).sum());

        return result;
    }
}
