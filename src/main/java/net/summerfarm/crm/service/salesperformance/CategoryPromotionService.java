package net.summerfarm.crm.service.salesperformance;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.salesperformance.BdCategoryPromotionSummaryListQuery;
import net.summerfarm.crm.model.query.salesperformance.CategoryPromotionQuery;
import net.summerfarm.crm.model.query.salesperformance.CategoryPromotionSummaryQuery;
import net.summerfarm.crm.model.query.salesperformance.MerchantCategoryPromotionQuery;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionVO;

import java.util.List;

/**
 * 品类推广服务接口
 */
public interface CategoryPromotionService {

    /**
     * 获取品类推广汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @param query 查询参数
     * @return 品类推广汇总数据
     */
    CategoryPromotionSummaryVO getCategoryPromotionSummary(CategoryPromotionSummaryQuery query);

    /**
     * 获取销售品类推广汇总列表
     * 为 m1 列出下属 bd 品类推广汇总
     * 普通bd, m2及以上不返回
     *
     * @param query 查询参数
     * @return 品类推广汇总列表
     */
    List<CategoryPromotionSummaryVO> listCategoryPromotionSummaryForManager(BdCategoryPromotionSummaryListQuery query);

    /**
     * 获取销售维度品类推广数据详情
     * 如果bdId为空, 则返回当前登录bd的品类推广数据详情
     *
     * @param query 查询参数
     * @return 品类推广数据详情分页数据
     */
    PageInfo<CategoryPromotionDetailVO> getCategoryPromotionData(CategoryPromotionQuery query);

    /**
     * 获取门店维度品类推广数据
     *
     * @param query 查询参数
     * @return 门店维度品类推广数据
     */
    List<MerchantCategoryPromotionVO> getMerchantCategoryPromotion(MerchantCategoryPromotionQuery query);

    /**
     * 获取本期品类推广SPU分组列表, 用于下拉选择
     *
     * @return 品类推广SPU分组
     */
    List<String> getCategoryPromotionSpuGroupList();
}
