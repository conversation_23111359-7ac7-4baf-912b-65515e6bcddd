package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.domain.CrmNews;
import net.summerfarm.crm.model.query.CrmNewsQuery;
import net.summerfarm.crm.model.vo.CrmNewsVO;
import net.xianmu.common.result.CommonResult;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface CrmNewsService {

    /**
     * 新增消息通知
     * @param crmNews 消息实体
     */
    void insert(CrmNews crmNews);

    /**
     * 批量更新消息
     * @param adminId adminId
     */
    void update(Integer adminId);

    /**
     * 查询消息列表
     * @param crmNewsQuery 查询对象
     * @return 消息列表
     */
    CommonResult<PageInfo<CrmNewsVO>> selectList(CrmNewsQuery crmNewsQuery);

    /**
     * 查询是否有未读消息
     * @param adminId adminId
     * @param position 位置：0：主界面 1：列表页
     * @return 未读消息数量
     */
    Integer selectIsRead(Integer adminId, Integer position);

}
