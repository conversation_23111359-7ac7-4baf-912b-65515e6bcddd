package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.domain.CrmCouponExpensePool;
import net.summerfarm.crm.model.domain.MerchantSituation;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpenseAdminDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolExtDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolLogDto;
import net.summerfarm.crm.model.query.crmCouponExpensePool.CrmCouponExpensePoolHaveQuery;
import net.summerfarm.crm.model.query.crmCouponExpensePool.CrmCouponExpensePoolQuery;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolDivideVo;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolExtVo;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolVo;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;

import java.util.List;

public interface CrmCouponExpensePoolService {
    CommonResult<PageInfo<CrmCouponExpensePoolDto>> queryPool(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase);


    CommonResult<PageInfo<CrmCouponExpensePoolLogDto>> queryPoolLog(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase);

    CommonResult<CrmCouponExpensePool> update(CrmCouponExpensePoolVo crmCouponExpensePoolVo, UserBase userBase);

    CommonResult<CrmCouponExpensePool> save(CrmCouponExpensePoolVo crmCouponExpensePoolVo, UserBase userBase);

    CommonResult<Boolean> divide(UserBase userBase, CrmCouponExpensePoolDivideVo divideVo);

    CommonResult<PageInfo<CrmCouponExpenseAdminDto>> adminPool(UserBase userBase, CrmCouponExpensePoolQuery crmCouponExpensePoolHaveQuery);

    CommonResult<List<CrmCouponExpensePoolExtDto>> queryPoolExt(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase);

    CommonResult<Boolean> addPoolExt(UserBase userBase, CrmCouponExpensePoolExtVo vo);

    CommonResult<Boolean> deletePoolExt(UserBase userBase, CrmCouponExpensePoolExtVo vo);

    CommonResult<PageInfo<CrmCouponExpensePoolLogDto>> queryAdminPoolLog(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase);

    CommonResult<CrmCouponExpensePoolDto> queryPoolDetail(CrmCouponExpensePoolQuery cluClueQuery, UserBase userBase);

    void checkPoolSkuCanUse(String sku, Long poolId, Integer adminId, MerchantSituationDTO merchantSituationDTO);

    void successUpdateAdminPool(MerchantSituation queryMerchantSituation);


    void  updatePoolStatus();

}
