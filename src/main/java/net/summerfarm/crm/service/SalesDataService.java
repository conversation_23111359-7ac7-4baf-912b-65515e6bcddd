package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.model.domain.CrmBdTodayDayGmv;
import net.summerfarm.crm.model.domain.CrmCityTodayGmv;
import net.summerfarm.crm.model.domain.CrmNewCustomersMonth;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.query.TeamDataQuery;
import net.summerfarm.crm.model.query.salesdata.NewCustomerQuery;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.model.vo.saledata.CRMDashboardModule;
import net.summerfarm.crm.model.vo.saledata.NewCustomerVo;
import net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo;
import net.xianmu.common.result.CommonResult;

import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface SalesDataService {

    /**
     * 区域gmv信息总览
     * @param salesDataQuery 查询条件
     * @return 区域gmv信息总览
     */
    AjaxResult selectGmvByZoneName(SalesDataQuery salesDataQuery);

    /**
     * 根据区域、时间及bd姓名取bd数据
     * @param salesDataQuery 查询条件
     * @param pageIndex 页码
     * @param pageSize 数量
     * @return bd数据详情
     */
    AjaxResult selectBdDataByZoneName(int pageIndex, int pageSize, SalesDataQuery salesDataQuery);

    /**
     * 根据城市、时间及bd姓名取bd数据
     * @param salesDataQuery 查询条件
     * @return bd数据详情
     */
    CommonResult<PageInfo<AdminInfoVo>> selectBdDataByCity(SalesDataQuery salesDataQuery);

    /**
     * crm小程序:数据看板,销售gmv数据
     * @return 销售gmv数据
     */
    CommonResult<AdminInfoVo> selectBdData(Integer adminId);

    /**
     * 邮件发送
     */
    void sendMail();

    /**
     * 取销售数据
     * @param pageIndex 开始页码
     * @param pageSize 每页数量
     * @param salesDataQuery 查询条件
     * @return 销售GMV数据详情
     */
    AjaxResult selectBdGmvByZoneName(int pageIndex, int pageSize, SalesDataQuery salesDataQuery);

    /**
     * 城市维度销售数据
     * @param salesDataQuery 查询条件
     * @return 销售GMV数据详情
     */
    CommonResult<PageInfo<AdminInfoVo>> selectBdGmvByCity(SalesDataQuery salesDataQuery);

    /**
     * 根据当前登录账号拥有的城市权限查询BD名单
     * @return BD名单详情
     */
    AjaxResult selectBDByArea(String bdName);

    /**
     * 销售业务数据页面
     * @param teamDataQuery 查询条件
     * @return 销售业务数据列表
     */
    AjaxResult salesTeamData(int pageIndex, int pageSize, TeamDataQuery teamDataQuery);

    /**
     * 导出特定数据excel文档
     * @param teamDataQuery 查询条件
     * @return ok
     */
    AjaxResult orderData(TeamDataQuery teamDataQuery);

    /**
     * 显示客户数据详情
     * @param teamDataQuery 查询条件，其中mid为必传，开始、结束时间非必须
     * @return 客户数据详情
     */
    AjaxResult orderDetails(TeamDataQuery teamDataQuery);


    /**
     * 获取销售的客户数据
     * @return 客户数据详情
     */
    CommonResult<AdminInfoVo> selectBdMerchantData(Integer adminId);
    /**
     * 核心客户变动详情
     * @return 客户数据详情
     */
    AjaxResult selectChangeMerchant(Integer adminId);

    /**
     * 根据商品名查询其类目
     * @param name 商品名
     * @return 类目或品牌名称
     */
    AjaxResult selectSkuCategory(int pageIndex,int pageSize,String name);
    /**
     * 查询过完days天的gmv信息
     * @param days 天数
     * @param salesDataQuery 查询条件
     * @return gmv信息
     */
    AjaxResult selectGmvBeforeDays(int days, SalesDataQuery salesDataQuery);

    /**
     * 获取销售商户分布概况
     * @return 绩效详情
     */
    AjaxResult merchantLevelDistribution();

    /**
     * 查询销售的商户分布区间
     * @param proportion 等级系数
     * @return 商户信息
     */
    AjaxResult merchantLevelDistributionInfo(String proportion);

    /**
     * 查询行政城市gmv
     * @param salesDataQuery 查询条件
     * @return 行政城市gmv信息
     */
    CommonResult<SalesDataVo> selectGmvByCity(SalesDataQuery salesDataQuery);

    /**
     * 查询近days天的行政城市gmv变化
     * @param days 多少天
     * @param salesDataQuery 查询条件
     * @return gmv变化
     */
    AjaxResult selectCityGmvBeforeDays(int days, SalesDataQuery salesDataQuery);

    /**
     * 销售当日gmv数据
     * @return 销售日gmv数据
     */
    CommonResult<CrmBdTodayDayGmv> todayGmv();

    /**
     * 收入排行榜
     * @param type 排行榜类型 0:收入;1:拉新;2:拜访
     * @return 当日收入排行榜
     */
    CommonResult<List<RankingListVO>> rankingList(Integer type);

    /**
     * 登录用户默认行政城市
     *
     * @return {@link CommonResult}<{@link String}>
     */
    CommonResult<AdministrativeCityVo> defaultAdministrativeCity();

    /**
     * 按城市区域查询GMV
     *
     * @param salesDataQuery 销售数据查询
     * @return {@link CommonResult}<{@link SalesDataVo}>
     */
    CommonResult<CityDistrictGmvVo> selectGmvByCityDistrict(SalesDataQuery salesDataQuery);

    /**
     * 销售当日 gmv
     *
     * @param salesAreaId 销售区域 id
     * @return {@link List}<{@link CrmBdTodayDayGmv}>
     */
    List<CrmBdTodayDayGmv> bdTodayGmv(Integer salesAreaId);

    /**
     * 城市当日 gmv
     *
     * @param salesAreaId 销售区域 id
     * @return {@link List}<{@link CrmCityTodayGmv}>
     */
    List<CrmCityTodayGmv> cityTodayGmv(Integer salesAreaId);

    /**
     * 销售区域当日 gmv
     *
     * @param type 0:当日;1:当月;
     * @return {@link List}<{@link SalesAreaDataVo}>
     */
    List<SalesAreaDataVo> salesAreaGmv(Integer type);

    /**
     * 销售当月 gmv
     *
     * @param salesAreaId 销售区域 id
     * @return {@link List}<{@link CrmBdTodayDayGmv}>
     */
    List<CrmBdTodayDayGmv> bdMonthGmv(Integer salesAreaId);

    /**
     * 城市当月 gmv
     *
     * @param salesAreaId 销售区域 id
     * @return {@link List}<{@link CrmCityTodayGmv}>
     */
    List<SalesAreaDataVo> cityMonthGmv(Integer salesAreaId);

    /**
     * 当月拉新
     *
     * @return {@link List}<{@link CrmNewCustomersMonth}>
     */
    List<NewCustomerVo> newCustomer(NewCustomerQuery query);
}
