package net.summerfarm.crm.service;

import com.github.pagehelper.StringUtil;
import net.summerfarm.crm.mapper.manage.CrmBdCityMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.model.domain.CrmBdCity;
import net.summerfarm.crm.model.vo.BdCityPermissionVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CityPermissionService {
    @Resource
    CrmBdCityMapper crmBdCityMapper;
    @Resource
    MerchantMapper merchantMapper;

    /**
     * @param adminId adminId
     * @param city    城市
     * @param area    地区
     * @return CrmBdCity城市相关
     */
    public CrmBdCity selectAminIdCityandArea(Integer adminId, String city, String area) {
        if (adminId == null){
            throw new BizException("adminId 不能为空");
        }
        if (StringUtil.isEmpty(city)){
            throw new BizException("城市不能为空");
        }
        List<CrmBdCity> crmBdCities = crmBdCityMapper.selectByBdIdCityArea(adminId, city, area);
        return CollectionUtils.isEmpty(crmBdCities) ? null : crmBdCities.get(0);
    }

    /**
     *
     * @param adminId adminId
     * @param city    城市
     * @param area    地区
     *  检查市区权限
     */
    public void  checkAreaPermission(Integer adminId, String city, String area){
        CrmBdCity crmBdCity = selectAminIdCityandArea(adminId, city, area);
        if (crmBdCity == null){
            throw new BizException("bd没有此区域权限!");
        }
    }

    public Boolean check(BdCityPermissionVO bdCityPermissionVO){
        MerchantVO merchantVO = merchantMapper.selectMerchantByMid(bdCityPermissionVO.getMId());
        if (merchantVO == null){
            throw new BizException("门店不存在");
        }
        checkAreaPermission(bdCityPermissionVO.getAdminId(), merchantVO.getCity(), merchantVO.getArea());
        return true;
    }
}
