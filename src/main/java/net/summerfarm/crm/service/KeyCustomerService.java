package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.dto.keycustomer.KeyCustomer;
import net.summerfarm.crm.model.dto.keycustomer.KeyCustomerCount;
import net.summerfarm.crm.model.query.keycustomer.KeyCustomerCountQuery;
import net.summerfarm.crm.model.query.keycustomer.KeyCustomerQuery;

public interface KeyCustomerService {

    /**
     * 统计重点客户数量
     */
    KeyCustomerCount countKeyCustomer(KeyCustomerCountQuery query);

    /**
     * 分页查询重点客户
     */
    PageInfo<KeyCustomer> pageKeyCustomer(KeyCustomerQuery query);
}
