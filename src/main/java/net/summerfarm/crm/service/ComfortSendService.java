package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.CrmComfortSendQuery;
import net.summerfarm.crm.model.query.CrmKeyCustomerQuery;
import net.summerfarm.crm.model.vo.ComfortSendOrderVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.xianmu.common.result.CommonResult;

/**
 * <AUTHOR>
 * @date 2022/12/1 2:00
 */
public interface ComfortSendService {

    CommonResult<PageInfo<ComfortSendOrderVO>> queryComfortSend(CrmComfortSendQuery crmComfortSendQuery);

    CommonResult sumComfortQueryCount(Long bdId, String province,String city,String area);
}
