package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.dto.VisitPlanDTO;
import net.summerfarm.crm.model.query.FollowUpQuery;
import net.summerfarm.crm.model.query.VisitPlanQuery;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.model.vo.VisitPlanVO;
import net.xianmu.common.input.BasePageInput;
import net.xianmu.common.result.CommonResult;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface VisitPlanService {

    /**
     * 插入拜访计划
     * @param visitPlanDTO 填写的内容
     * @return ok
     */
    AjaxResult insertVisitPlan(VisitPlanDTO visitPlanDTO);


    /**
     * 查询拜访计划列表
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param visitPlanQuery 查询条件
     * @return 拜访计划列表
     */
    AjaxResult queryVisitPlanList(int pageIndex, int pageSize, VisitPlanQuery visitPlanQuery);

    /**
     * 查询拜访计划详情
     * @param visitPlanId 拜访计划id
     * @param type 拜访类型,0拜访 1拉新 2陪访
     * @return  拜访计划详情
     */
    AjaxResult queryVisitPlan(Long visitPlanId, Integer type);

    /**
     * 取消拜访计划
     * @param visitPlanDTO 取消信息
     * @return ok
     */
    AjaxResult cancelPlan(VisitPlanDTO visitPlanDTO);

    /**
    * 每天早上10点30分发送当天的所有拜访计划
    */
    void sendDingTalkMsg();

    /**
     * 根据日期查询任务
     * @param visitPlanQuery 查询条件
     * @return 拜访计划
     */
    CommonResult<List<VisitPlanVO>> queryPlanByDate(VisitPlanQuery visitPlanQuery);

    /**
     * 当天未拜访计划更新为未处理
     * @param time 时间
     * @return 数量
     */
    int updateUnHandlePlan(LocalDateTime time);

    /**
     * 统计任务完成情况
     *
     * @param date     时间
     * @param province 省
     * @param city     城市
     * @param area     区域
     * @return 任务完成情况
     */
    CommonResult<Integer> count(String province, String city, String area, LocalDate date);

    /**
     * 批量创建任务 （crm创建拉新任务）
     *
     * @param count    数量
     * @param date     内容
     * @param province 省
     * @param city     城市
     * @return {@link AjaxResult}
     */
    AjaxResult createBatch(Integer count, String province,String city, String date);

    CommonResult<PageInfo<VisitPlanVO>> queryFollowup(FollowUpQuery input);

    /**
     * 填充拜访计划门店/联系人信息
     *
     * @param visitPlanVOList 跟进录音清单
     */
    void fillMerchantAndAreaName(List<VisitPlanVO> visitPlanVOList);
}
