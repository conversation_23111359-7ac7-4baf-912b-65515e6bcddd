package net.summerfarm.crm.service;

import net.summerfarm.crm.model.domain.InvoiceEmailOverride;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-07-23
 **/
public interface InvoiceEmailOverrideService {

    /**
     * 插入覆盖配置
     * @param invoiceEmailOverride 覆盖配置
     * @return 影响行数
     */
    int insertOrUpdateByUK(InvoiceEmailOverride invoiceEmailOverride);

    /**
     * 根据发票配置ID和门店ID查询覆盖配置
     * @param invoiceConfigId
     * @param merchantId
     * @return
     */
    InvoiceEmailOverride selectByConfigIdAndMId(Long invoiceConfigId, Long merchantId);
}
