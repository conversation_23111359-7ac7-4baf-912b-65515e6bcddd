package net.summerfarm.crm.service;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.query.saasorder.SaasOrderListQuery;
import net.summerfarm.crm.model.vo.OrderOverviewVO;
import net.summerfarm.crm.model.vo.saasorder.SaasOrderDetailVO;

public interface SaasOrderService {

    /**
     * 分页查询saas订单列表
     */
    PageInfo<OrderOverviewVO> pageSaasOrderList(SaasOrderListQuery query);

    /**
     * 查询saas订单详情
     */
    SaasOrderDetailVO getSaasOrderDetail(String orderNo);
}
