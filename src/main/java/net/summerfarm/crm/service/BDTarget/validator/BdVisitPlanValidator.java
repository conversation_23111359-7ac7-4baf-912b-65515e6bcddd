package net.summerfarm.crm.service.BDTarget.validator;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanRepository;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlan;
import net.summerfarm.crm.model.input.BDTarget.AddBdVisitPlanInput;
import net.summerfarm.crm.model.input.BDTarget.AddBdVisitPlanDetailInput;
import net.summerfarm.crm.model.input.BDTarget.GenerateVisitPlanDraftInput;
import net.summerfarm.crm.model.input.BDTarget.ReGenerateVisitPlanInput;
import net.summerfarm.crm.model.input.BDTarget.VisitMerchantInfoInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拜访计划验证器
 * 负责参数验证和重复性检查逻辑
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Component
public class BdVisitPlanValidator {

    // ==================== 错误信息常量 ====================
    
    private static final String ERROR_BD_ID_NULL = "销售bdId不能为空";
    
    // ==================== 依赖注入 ====================
    
    @Resource
    private BdVisitPlanRepository bdVisitPlanRepository;
    
    // ==================== 参数验证方法 ====================
    
    /**
     * 验证重新生成拜访计划的输入参数
     */
    public void validateReGenerateInput(ReGenerateVisitPlanInput input) {
        if (input == null) {
            throw new IllegalArgumentException("重新生成拜访计划的输入参数不能为空");
        }
        if (CollectionUtils.isEmpty(input.getVisitOfflineMIdList()) && CollectionUtils.isEmpty(input.getVisitOnlineMIdList())) {
            throw new IllegalArgumentException("需替换的客户列表不能为空");
        }
        if (!CollectionUtils.isEmpty(input.getVisitOfflineMIdList()) && !CollectionUtils.isEmpty(input.getVisitOnlineMIdList())) {
            throw new IllegalArgumentException("暂不支持同时替换线上客户和线下客户");
        }
        if (!CollectionUtils.isEmpty(input.getVisitOfflineMIdList()) && input.getPoi() == null) {
            throw new IllegalArgumentException("替换线下客户时poi信息不能为空");
        }
    }

    /**
     * 验证并获取销售bdId（重载方法，支持ReGenerateVisitPlanInput）
     */
    public Integer validateAndGetBdId(ReGenerateVisitPlanInput input, Integer adminId) {
        Integer bdId = adminId != null ? adminId : input.getBdId();
        if (bdId == null) {
            log.warn("无法获取销售bdId，重新生成拜访计划失败");
            throw new IllegalArgumentException(ERROR_BD_ID_NULL);
        }
        return bdId;
    }

    /**
     * 验证并获取销售bdId（原方法，支持GenerateVisitPlanDraftInput）
     */
    public Integer validateAndGetBdId(GenerateVisitPlanDraftInput input, Integer adminId) {
        Integer bdId = adminId != null ? adminId : input.getBdId();
        if (bdId == null) {
            log.warn("无法获取销售bdId，生成拜访计划草案失败");
            throw new IllegalArgumentException(ERROR_BD_ID_NULL);
        }

        if (input.getVisitOnlineCount() != null && (input.getVisitOnlineCount() <= 0 || input.getVisitOnlineCount() > 200)) {
            throw new IllegalArgumentException("线上拜访数量不能为空，且不能超过200");
        }
        if (input.getVisitOfflineCount() != null && (input.getVisitOfflineCount() <= 0 || input.getVisitOfflineCount() > 200)) {
            throw new IllegalArgumentException("线下拜访数量不能为空，且不能超过200");
        }
        if (input.getTrafficType() != null && (input.getTrafficType() < 1 || input.getTrafficType() > 6)) {
            throw new IllegalArgumentException("交通方式不能为空，且必须在1-6之间");
        }

        return bdId;
    }
    
    // ==================== 门店重复性检查 ====================

    
    /**
     * 检查门店重复性并返回重复门店详细信息（支持AddBdVisitPlanInput）
     */
    public String checkDuplicateMerchantsWithDetails(AddBdVisitPlanInput input, Integer bdId, LocalDate visitDate) {
        List<BdVisitPlan> existingPlans = getExistingVisitPlans(bdId, visitDate);
        if (CollectionUtils.isEmpty(existingPlans)) {
            return null;
        }
        
        // 获取已存在拜访计划中的门店ID列表
        List<Long> existingMerchantIds = bdVisitPlanRepository.extractMerchantIds(existingPlans);
        
        // 检查输入门店重复性并返回详细信息
        return checkAddInputMerchantDuplicatesWithDetails(input.getAddBdVisitPlanDetails(), existingMerchantIds, bdId, visitDate);
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 获取已存在的拜访计划
     */
    private List<BdVisitPlan> getExistingVisitPlans(Integer bdId, LocalDate visitDate) {
        try {
            return bdVisitPlanRepository.selectByBdIdAndVisitDate(bdId, visitDate);
        } catch (Exception e) {
            log.error("查询已存在的拜访计划失败，bdId: {}, visitDate: {}", bdId, visitDate, e);
            return Collections.emptyList();
        }
    }

    /**
     * 检查添加拜访计划输入门店重复性并返回详细信息
     */
    private String checkAddInputMerchantDuplicatesWithDetails(List<AddBdVisitPlanDetailInput> inputMerchants, 
                                                             List<Long> existingMerchantIds, Integer bdId, LocalDate visitDate) {
        if (CollectionUtils.isEmpty(inputMerchants)) {
            return null;
        }
        
        // 找出重复的门店信息
        List<AddBdVisitPlanDetailInput> duplicateMerchants = inputMerchants.stream()
                .filter(merchant -> existingMerchantIds.contains(merchant.getMId()))
                .collect(Collectors.toList());
        
        if (!CollectionUtils.isEmpty(duplicateMerchants)) {
            // 构建详细的重复门店信息
            List<String> duplicateMerchantDetails = duplicateMerchants.stream()
                    .map(merchant -> String.format("门店ID:%s,门店名称:%s", 
                            merchant.getMId(), 
                            merchant.getMname() != null ? merchant.getMname() : "未知"))
                    .collect(Collectors.toList());
            
            log.warn("销售{}在{}已存在重复的拜访门店: {}", bdId, visitDate, duplicateMerchantDetails);
            return String.join(",", duplicateMerchantDetails);
        }
        return null;
    }
}