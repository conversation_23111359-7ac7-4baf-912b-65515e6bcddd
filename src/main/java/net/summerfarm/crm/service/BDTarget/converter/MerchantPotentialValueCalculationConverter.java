package net.summerfarm.crm.service.BDTarget.converter;

import net.summerfarm.crm.model.bo.BDTarget.MerchantPotentialCalculationData;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;

import java.util.Map;

/**
 * 门店潜力值计算转换器
 * 负责处理门店潜力值计算相关的对象转换逻辑
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public abstract class MerchantPotentialValueCalculationConverter {

    private MerchantPotentialValueCalculationConverter() {
        // 工具类，禁止实例化
    }

    /**
     * 将BdVisitPlanIndicatorSync转换为MerchantPotentialCalculationData
     *
     * @param syncData 同步数据
     * @param targetIndicatorMap 目标指标映射
     * @return 门店潜力值计算数据
     */
    public static MerchantPotentialCalculationData convertSyncDataToCalculationData(BdVisitPlanIndicatorSync syncData, 
                                                                                    Map<Long, BdDailyTargetDetail> targetIndicatorMap) {
        MerchantPotentialCalculationData data = new MerchantPotentialCalculationData();
        data.setMerchantId(syncData.getMId());
        data.setBdVisitPlanId(syncData.getBdVisitPlanId());
        data.setBdDailyTargetDetailId(syncData.getBdDailyTargetDetailId());
        data.setIndicatorCurrentValue(syncData.getIndicatorCurrentValue());
        data.setIndicatorPotentialValue(syncData.getIndicatorPotentialValue());
        
        // 从目标指标映射中获取优先级信息并设置到计算数据中
        BdDailyTargetDetail targetIndicator = targetIndicatorMap.get(syncData.getBdDailyTargetDetailId());
        if (targetIndicator != null) {
            data.setTargetName(targetIndicator.getTargetName());
            data.setTargetIndicatorType(targetIndicator.getTargetType());
            data.setPriority(targetIndicator.getPriority());
            // 注意：这里暂时不设置权重，权重需要通过calculateTargetWeights方法计算
            // 权重计算需要所有目标指标的优先级信息，在调用方处理
        }
        
        return data;
    }

}