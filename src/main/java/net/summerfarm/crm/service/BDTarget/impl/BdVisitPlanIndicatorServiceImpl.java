package net.summerfarm.crm.service.BDTarget.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanIndicatorRepository;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanIndicatorSyncRepository;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailQuery;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlan;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.input.BDTarget.GenerateVisitPlanDraftInput;
import net.summerfarm.crm.model.input.BDTarget.ReGenerateVisitPlanInput;
import net.summerfarm.crm.service.BDTarget.BdDailyTargetDetailService;
import net.summerfarm.crm.service.BDTarget.BdVisitPlanIndicatorService;
import net.summerfarm.crm.service.BDTarget.converter.BdVisitPlanConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 拜访计划指标服务实现类
 * 负责拜访计划指标的生成、保存和管理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class BdVisitPlanIndicatorServiceImpl implements BdVisitPlanIndicatorService {

    // ==================== 常量定义 ====================
    
    /** 错误消息模板 */
    private static final String ERROR_GET_TARGET_INDICATORS_FAILED = "读取拜访目标指标失败";
    private static final String ERROR_GET_SYNC_DATA_FAILED = "读取拜访私海目标指标同步数据失败";
    private static final String ERROR_INSERT_PLAN_INDICATORS_FAILED = "批量插入拜访计划指标失败";
    
    // ==================== 依赖注入 ====================
    
    @Resource
    private BdVisitPlanIndicatorRepository bdVisitPlanIndicatorRepository;
    
    @Resource
    private BdVisitPlanIndicatorSyncRepository bdVisitPlanIndicatorSyncRepository;
    
    @Resource
    private BdDailyTargetDetailService bdDailyTargetDetailService;
    
    // ==================== 公共接口实现 ====================

    @Override
    public List<BdDailyTargetDetail> getTargetIndicators(Long dailyTargetId) {
        try {
            BdDailyTargetDetailQuery query = new BdDailyTargetDetailQuery();
            query.setBdDailyTargetId(dailyTargetId);
            List<BdDailyTargetDetail> indicators = bdDailyTargetDetailService.selectByDailyTargetIds(
                     query.getBdDailyTargetId() != null ? Arrays.asList(query.getBdDailyTargetId()) : new ArrayList<>());
            
            // 使用Mapper的list方法查询
            if (indicators == null || indicators.isEmpty()) {
                // 通过Service查询所有指标，然后过滤
                // 这里需要调用Mapper的list方法
                return new ArrayList<>();
            }
            
            log.debug("读取拜访目标指标，目标ID: {}, 指标数量: {}", dailyTargetId, indicators.size());
            return indicators;
        } catch (Exception e) {
            log.error(ERROR_GET_TARGET_INDICATORS_FAILED + "，目标ID: {}", dailyTargetId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<BdVisitPlanIndicatorSync> getIndicatorSyncData(List<BdDailyTargetDetail> targetIndicators) {
        try {
            if (CollectionUtils.isEmpty(targetIndicators)) {
                return new ArrayList<>();
            }
            
            // 提取查询条件
            List<Integer> bdIds = targetIndicators.stream()
                    .map(BdDailyTargetDetail::getBdId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            
            List<Long> bdDailyTargetIds = targetIndicators.stream()
                    .map(BdDailyTargetDetail::getBdDailyTargetId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            
            List<Long> bdDailyTargetDetailIds = targetIndicators.stream()
                    .map(BdDailyTargetDetail::getId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            
            // 根据多个条件精确查询同步数据
            // 注意：这里暂时不传mIds，因为BdDailyTargetDetail中没有mId字段
            List<BdVisitPlanIndicatorSync> syncData = bdVisitPlanIndicatorSyncRepository.selectByMultipleConditions(
                    bdIds, bdDailyTargetIds, bdDailyTargetDetailIds);
            
            if (CollectionUtils.isEmpty(syncData)) {
                log.debug("读取拜访私海目标指标同步数据，查询结果为空");
                return new ArrayList<>();
            }
            
            // 组装targetIndicators的匹配条件Map，用于精确过滤
            Set<String> targetKeys = targetIndicators.stream()
                    .map(target -> buildTargetKey(target.getBdId(), target.getBdDailyTargetId(), target.getId()))
                    .collect(Collectors.toSet());
            
            // 根据输入的targetIndicators过滤同步数据
            List<BdVisitPlanIndicatorSync> filteredSyncData = syncData.stream()
                    .filter(sync -> {
                        String syncKey = buildTargetKey(sync.getBdId(), sync.getBdDailyTargetId(), sync.getBdDailyTargetDetailId());
                        return targetKeys.contains(syncKey);
                    })
                    .collect(Collectors.toList());
            
            log.debug("读取拜访私海目标指标同步数据，目标指标数量: {}, 查询条件 - bdIds: {}, bdDailyTargetIds: {}, bdDailyTargetDetailIds: {}, 查询到同步数据数量: {}, 过滤后数量: {}", 
                     targetIndicators.size(), bdIds.size(), bdDailyTargetIds.size(), bdDailyTargetDetailIds.size(), 
                     syncData.size(), filteredSyncData.size());
            
            return filteredSyncData;
        } catch (Exception e) {
            log.error(ERROR_GET_SYNC_DATA_FAILED, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 构建目标匹配键，用于精确匹配targetIndicators和同步数据
     * 
     * @param bdId BD ID
     * @param bdDailyTargetId 日常目标ID
     * @param bdDailyTargetDetailId 日常目标详情ID
     * @return 匹配键字符串
     */
    private String buildTargetKey(Integer bdId, Long bdDailyTargetId, Long bdDailyTargetDetailId) {
        return String.format("%s_%s_%s", 
                bdId != null ? bdId : "null",
                bdDailyTargetId != null ? bdDailyTargetId : "null",
                bdDailyTargetDetailId != null ? bdDailyTargetDetailId : "null");
    }

    
    @Override
    public int insertPlanIndicatorsBatch(List<BdVisitPlanIndicator> planIndicators) {
        try {
            if (CollectionUtils.isEmpty(planIndicators)) {
                log.warn("拜访计划指标列表为空，跳过批量插入操作");
                return 0;
            }
            
            int insertCount = bdVisitPlanIndicatorRepository.insertBatch(planIndicators);
            log.debug("批量插入拜访计划指标成功，插入数量: {}", insertCount);
            return insertCount;
        } catch (Exception e) {
            log.error(ERROR_INSERT_PLAN_INDICATORS_FAILED + "，指标数量: {}", planIndicators.size(), e);
            throw new RuntimeException(ERROR_INSERT_PLAN_INDICATORS_FAILED, e);
        }
    }
}