package net.summerfarm.crm.service.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlan;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.input.BDTarget.GenerateVisitPlanDraftInput;
import net.summerfarm.crm.model.input.BDTarget.ReGenerateVisitPlanInput;

import java.util.List;

/**
 * 拜访计划指标服务接口
 * 负责拜访计划指标的生成、保存和管理
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface BdVisitPlanIndicatorService {

    /**
     * 读取拜访目标指标
     *
     * @param dailyTargetId 日目标ID
     * @return 目标指标列表
     */
    List<BdDailyTargetDetail> getTargetIndicators(Long dailyTargetId);

    /**
     * 读取拜访私海目标指标同步数据
     *
     * @param targetIndicators 目标指标列表
     * @return 指标同步数据列表
     */
    List<BdVisitPlanIndicatorSync> getIndicatorSyncData(List<BdDailyTargetDetail> targetIndicators);

    /**
     * 批量插入拜访计划指标
     *
     * @param planIndicators 拜访计划指标列表
     * @return 插入成功的记录数
     */
    int insertPlanIndicatorsBatch(List<BdVisitPlanIndicator> planIndicators);
}