package net.summerfarm.crm.service.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailUpdate;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import java.util.List;

/**
 * 销售拜访目标指标服务接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface BdDailyTargetDetailService {

    /**
     * 根据主键查询销售拜访目标指标
     *
     * @param id 主键ID
     * @return 销售拜访目标指标
     */
    BdDailyTargetDetail selectByPrimaryKey(Long id);

    /**
     * 选择性更新销售拜访目标指标
     *
     * @param record 更新记录
     * @return 更新行数
     */
    int updateByPrimaryKeySelective(BdDailyTargetDetailUpdate record);

    /**
     * 根据每日拜访目标ID列表批量查询销售拜访目标指标
     *
     * @param dailyTargetIds 每日拜访目标ID列表
     * @return 销售拜访目标指标列表
     */
    List<BdDailyTargetDetail> selectByDailyTargetIds(List<Long> dailyTargetIds);


    /**
     * 根据ID列表批量查询销售拜访目标指标
     *
     * @param ids ID列表
     * @return 销售拜访目标指标列表
     */
    List<BdDailyTargetDetail> selectByIds(List<Long> ids);
}