package net.summerfarm.crm.service.BDTarget.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanIndicatorRepository;
import net.summerfarm.crm.mapper.repository.FollowUpRecordRepository;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.service.BDTarget.BdDailyTargetDetailService;
import net.summerfarm.crm.service.BDTarget.MerchantPotentialValueCalculationService;
import net.summerfarm.crm.service.BDTarget.converter.MerchantPotentialValueCalculationConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import net.summerfarm.crm.model.bo.BDTarget.MerchantPotentialCalculationData;


import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店潜力值计算服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Slf4j
@Service
public class MerchantPotentialValueCalculationServiceImpl implements MerchantPotentialValueCalculationService {

    @Resource
    private BdVisitPlanIndicatorRepository bdVisitPlanIndicatorRepository;
    
    @Resource
    private BdDailyTargetDetailService bdDailyTargetDetailService;
    
    @Resource
    private FollowUpRecordRepository followUpRecordRepository;

    // 权重分配常量
    private static final BigDecimal HIGH_PRIORITY_WEIGHT_RATIO = new BigDecimal("0.8"); // 前三优先级总共占80%（按5:3:2分配）
    private static final BigDecimal LOW_PRIORITY_WEIGHT_RATIO = new BigDecimal("0.2");  // 其他优先级占20%
    
    // 前三个优先级的具体权重比例（5:3:2）
    private static final BigDecimal FIRST_PRIORITY_RATIO = new BigDecimal("0.5");   // 第一优先级占50%
    private static final BigDecimal SECOND_PRIORITY_RATIO = new BigDecimal("0.3");  // 第二优先级占30%
    private static final BigDecimal THIRD_PRIORITY_RATIO = new BigDecimal("0.2");   // 第三优先级占20%
    
    private static final BigDecimal VISITED_COEFFICIENT = new BigDecimal("0.2");        // 近7天拜访过的系数
    private static final BigDecimal NOT_VISITED_COEFFICIENT = BigDecimal.ONE;           // 未拜访的系数
    private static final int SCALE = 4; // 计算精度

    @Override
    public Map<Long, BigDecimal> batchCalculateComprehensivePotentialValueFromSyncData(List<BdVisitPlanIndicatorSync> syncDataList) {
        if (CollectionUtils.isEmpty(syncDataList)) {
            log.warn("批量计算门店潜力值（同步数据）：同步数据列表为空");
            return new HashMap<>();
        }

        Map<Long, BigDecimal> result = new HashMap<>();
        
        try {
            // 从同步数据中提取门店ID和目标指标ID
            Set<Long> merchantIds = syncDataList.stream()
                    .map(BdVisitPlanIndicatorSync::getMId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            Set<Long> bdDailyTargetDetailIdSet = syncDataList.stream()
                    .map(BdVisitPlanIndicatorSync::getBdDailyTargetDetailId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            
            // 批量查询目标指标
            List<BdDailyTargetDetail> allTargetIndicators = bdDailyTargetDetailService.selectByIds(
                    new ArrayList<>(bdDailyTargetDetailIdSet));
            if (CollectionUtils.isEmpty(allTargetIndicators)) {
                log.warn("批量计算门店潜力值（同步数据）：未找到目标指标数据");
                return result;
            }
            
            // 将目标指标按ID分组，便于快速查找
            Map<Long, BdDailyTargetDetail> targetIndicatorMap = allTargetIndicators.stream()
                    .collect(Collectors.toMap(BdDailyTargetDetail::getId, indicator -> indicator));
            
            // 从同步数据组装生成MerchantPotentialCalculationData对象列表
            List<MerchantPotentialCalculationData> calculationDataList = syncDataList.stream()
                    .map(syncData -> MerchantPotentialValueCalculationConverter.convertSyncDataToCalculationData(syncData, targetIndicatorMap))
                    .collect(Collectors.toList());
            
            // 计算目标权重
            Map<Long, BigDecimal> targetWeights = calculateTargetWeights(allTargetIndicators);
            
            // 为每个计算数据设置权重
            calculationDataList.forEach(data -> {
                BigDecimal weight = targetWeights.get(data.getBdDailyTargetDetailId());
                data.setTargetIndicatorWeight(weight != null ? weight : BigDecimal.ZERO);
            });
            
            // 按门店ID分组
            Map<Long, List<MerchantPotentialCalculationData>> calculationDataByMerchant = calculationDataList.stream()
                    .collect(Collectors.groupingBy(MerchantPotentialCalculationData::getMerchantId));
            
            // 批量获取拜访系数
            Map<Long, BigDecimal> visitCoefficients = batchGetMerchantVisitCoefficient(new ArrayList<>(merchantIds));
            
            // 为每个门店计算潜力值
            for (Long merchantId : merchantIds) {
                List<MerchantPotentialCalculationData> calculationData = calculationDataByMerchant.get(merchantId);
                if (!CollectionUtils.isEmpty(calculationData)) {
                    BigDecimal potentialValue = calculateComprehensivePotentialValueFromCalculationData(merchantId, calculationData, visitCoefficients);
                    result.put(merchantId, potentialValue);
                } else {
                    log.warn("门店{}未找到拜访计划指标，跳过计算", merchantId);
                    result.put(merchantId, BigDecimal.ZERO);
                }
            }
            
            log.info("批量计算门店潜力值（同步数据）完成，处理门店数量：{}", result.size());
            
        } catch (Exception e) {
            log.error("批量计算门店潜力值（同步数据）失败", e);
        }
        
        return result;
    }

    @Override
    public Map<Long, BigDecimal> calculateTargetWeights(List<BdDailyTargetDetail> targetIndicators) {
        if (CollectionUtils.isEmpty(targetIndicators)) {
            log.warn("计算目标权重：目标指标列表为空");
            return new HashMap<>();
        }

        Map<Long, BigDecimal> weights = new HashMap<>();
        
        try {
            // 按优先级排序（假设优先级数字越小越重要）
            List<BdDailyTargetDetail> sortedIndicators = targetIndicators.stream()
                    .sorted(Comparator.comparing(BdDailyTargetDetail::getPriority,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            
            int totalCount = sortedIndicators.size();
            int highPriorityCount = Math.min(3, totalCount); // 前三个优先级
            int lowPriorityCount = totalCount - highPriorityCount;
            
            // 计算低优先级权重（其余平分20%）
            BigDecimal lowPriorityWeight = BigDecimal.ZERO;
            if (lowPriorityCount > 0) {
                lowPriorityWeight = LOW_PRIORITY_WEIGHT_RATIO
                        .divide(BigDecimal.valueOf(lowPriorityCount), SCALE, RoundingMode.HALF_UP);
            }
            
            // 分配初始权重
            for (int i = 0; i < sortedIndicators.size(); i++) {
                BdDailyTargetDetail indicator = sortedIndicators.get(i);
                BigDecimal weight;
                
                if (i < highPriorityCount) {
                    // 前三个优先级按5:3:2分配80%权重
                    if (i == 0) {
                        weight = HIGH_PRIORITY_WEIGHT_RATIO.multiply(FIRST_PRIORITY_RATIO);  // 80% * 50% = 40%
                    } else if (i == 1) {
                        weight = HIGH_PRIORITY_WEIGHT_RATIO.multiply(SECOND_PRIORITY_RATIO); // 80% * 30% = 24%
                    } else { // i == 2
                        weight = HIGH_PRIORITY_WEIGHT_RATIO.multiply(THIRD_PRIORITY_RATIO);  // 80% * 20% = 16%
                    }
                } else {
                    // 其余优先级平分20%权重
                    weight = lowPriorityWeight;
                }
                
                weights.put(indicator.getId(), weight);
            }
            
            // 权重归一化处理，确保总和严格等于1
            weights = normalizeWeights(weights);
            
            // 验证权重总和
            BigDecimal totalWeight = weights.values().stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            log.debug("目标权重计算完成：总指标数={}, 高优先级数={}, 低优先级数={}, 低优先级权重={}, 权重总和={}", 
                    totalCount, highPriorityCount, lowPriorityCount, lowPriorityWeight, totalWeight);
            
            // 权重总和验证
            if (totalWeight.compareTo(BigDecimal.ONE) != 0) {
                log.warn("权重总和不等于1，实际值：{}", totalWeight);
            }
            
        } catch (Exception e) {
            log.error("计算目标权重失败", e);
        }
        
        return weights;
    }
    
    /**
     * 权重归一化处理，确保权重总和严格等于1
     *
     * @param originalWeights 原始权重映射
     * @return 归一化后的权重映射
     */
    private Map<Long, BigDecimal> normalizeWeights(Map<Long, BigDecimal> originalWeights) {
        if (CollectionUtils.isEmpty(originalWeights)) {
            return originalWeights;
        }
        
        // 计算原始权重总和
        BigDecimal totalWeight = originalWeights.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 如果总和为0，则平均分配权重
        if (totalWeight.compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal averageWeight = BigDecimal.ONE
                    .divide(BigDecimal.valueOf(originalWeights.size()), SCALE, RoundingMode.HALF_UP);
            Map<Long, BigDecimal> normalizedWeights = new HashMap<>();
            for (Long key : originalWeights.keySet()) {
                normalizedWeights.put(key, averageWeight);
            }
            return normalizeWeightsWithRemainder(normalizedWeights);
        }
        
        // 如果总和已经等于1，直接返回
        if (totalWeight.compareTo(BigDecimal.ONE) == 0) {
            return originalWeights;
        }
        
        // 按比例归一化
        Map<Long, BigDecimal> normalizedWeights = new HashMap<>();
        for (Map.Entry<Long, BigDecimal> entry : originalWeights.entrySet()) {
            BigDecimal normalizedWeight = entry.getValue()
                    .divide(totalWeight, SCALE, RoundingMode.HALF_UP);
            normalizedWeights.put(entry.getKey(), normalizedWeight);
        }
        
        // 处理精度误差，确保总和严格等于1
        return normalizeWeightsWithRemainder(normalizedWeights);
    }
    
    /**
     * 处理权重归一化后的精度误差，确保总和严格等于1
     *
     * @param weights 权重映射
     * @return 调整后的权重映射
     */
    private Map<Long, BigDecimal> normalizeWeightsWithRemainder(Map<Long, BigDecimal> weights) {
        if (CollectionUtils.isEmpty(weights)) {
            return weights;
        }
        
        // 计算当前权重总和
        BigDecimal currentTotal = weights.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 计算差值
        BigDecimal remainder = BigDecimal.ONE.subtract(currentTotal);
        
        // 如果差值为0，直接返回
        if (remainder.compareTo(BigDecimal.ZERO) == 0) {
            return weights;
        }
        
        // 将差值分配给权重最大的指标
        Map.Entry<Long, BigDecimal> maxEntry = weights.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .orElse(null);
        
        if (maxEntry != null) {
            BigDecimal adjustedWeight = maxEntry.getValue().add(remainder);
            weights.put(maxEntry.getKey(), adjustedWeight);
        }
        
        return weights;
    }

    @Override
    public Map<Long, BigDecimal> batchGetMerchantVisitCoefficient(List<Long> merchantIds) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            log.warn("批量获取门店拜访系数：门店ID列表为空");
            return new HashMap<>();
        }

        Map<Long, BigDecimal> coefficients = new HashMap<>();
        
        try {
            // 批量查询近7天拜访记录
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            
            // 通过repository查询近7天有拜访记录的门店ID
            List<Long> visitedMerchantIdList = followUpRecordRepository.getVisitedMerchantIds(merchantIds, sevenDaysAgo, LocalDateTime.now());
            Set<Long> visitedMerchantIds = new HashSet<>(visitedMerchantIdList);
            
            // 为每个门店分配系数
            for (Long merchantId : merchantIds) {
                BigDecimal coefficient = visitedMerchantIds.contains(merchantId) ? 
                        VISITED_COEFFICIENT : NOT_VISITED_COEFFICIENT;
                coefficients.put(merchantId, coefficient);
            }
            
            log.debug("批量获取门店拜访系数完成：总门店数={}, 近7天拜访门店数={}", 
                    merchantIds.size(), visitedMerchantIds.size());
            
        } catch (Exception e) {
            log.error("批量获取门店拜访系数失败", e);
            
            // 异常情况下，所有门店都设置为未拜访系数
            for (Long merchantId : merchantIds) {
                coefficients.put(merchantId, NOT_VISITED_COEFFICIENT);
            }
        }
        
        return coefficients;
    }

    /**
     * 基于计算数据计算门店综合潜力值
     * 直接使用MerchantPotentialCalculationData中的权重和潜力值数据
     *
     * @param merchantId 门店ID
     * @param calculationDataList 计算数据列表
     * @return 综合潜力值
     */
    private BigDecimal calculateComprehensivePotentialValueFromCalculationData(Long merchantId, 
                                                                              List<MerchantPotentialCalculationData> calculationDataList,
                                                                              Map<Long, BigDecimal> visitCoefficientsMap) {
        if (merchantId == null || CollectionUtils.isEmpty(calculationDataList)) {
            log.warn("计算门店综合潜力值参数无效，merchantId: {}, calculationDataList: {}", 
                    merchantId, calculationDataList != null ? calculationDataList.size() : 0);
            return BigDecimal.ZERO;
        }

        try {
            // 计算基础潜力值：V = (Score_目标1 * W_目标1) + (Score_目标2 * W_目标2) + ...
            BigDecimal basePotentialValue = BigDecimal.ZERO;
            
            for (MerchantPotentialCalculationData data : calculationDataList) {
                BigDecimal weight = data.getTargetIndicatorWeight();
                BigDecimal potentialValue = data.getIndicatorPotentialValue();
                
                if (weight != null && potentialValue != null) {
                    BigDecimal weightedValue = potentialValue.multiply(weight);
                    basePotentialValue = basePotentialValue.add(weightedValue);
                }
            }
            
            // 获取拜访系数
            BigDecimal visitCoefficient = visitCoefficientsMap.getOrDefault(merchantId, NOT_VISITED_COEFFICIENT);
            
            // 计算最终潜力值：V_final = V * Penalty_Factor
            BigDecimal finalPotentialValue = basePotentialValue.multiply(visitCoefficient)
                    .setScale(SCALE, RoundingMode.HALF_UP);
            
            log.debug("门店{}潜力值计算完成：基础潜力值={}, 拜访系数={}, 最终潜力值={}", 
                    merchantId, basePotentialValue, visitCoefficient, finalPotentialValue);
            
            return finalPotentialValue;
            
        } catch (Exception e) {
            log.error("计算门店{}综合潜力值失败", merchantId, e);
            return BigDecimal.ZERO;
        }
    }


}