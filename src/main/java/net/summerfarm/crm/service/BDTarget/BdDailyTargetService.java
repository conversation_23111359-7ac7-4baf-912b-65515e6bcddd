package net.summerfarm.crm.service.BDTarget;

import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.input.BDTarget.BatchAddBdDailyTargetInput;
import net.summerfarm.crm.model.input.BDTarget.BatchDeleteBdDailyTargetInput;
import net.summerfarm.crm.model.input.BDTarget.ModifyBdDailyTargetInput;
import net.summerfarm.crm.model.input.BDTarget.UpsertBdDailyTargetInput;
import net.summerfarm.crm.model.query.objectiveManagement.BdDailyTargetListBdQuery;
import net.summerfarm.crm.model.query.objectiveManagement.BdDailyTargetPageQuery;
import net.summerfarm.crm.model.query.objectiveManagement.BdDailyTargetQuery;
import net.summerfarm.crm.model.query.objectiveManagement.BdVisitConfigQuery;
import net.summerfarm.crm.model.vo.objectiveManagement.BdDailyTargetVO;
import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.vo.objectiveManagement.BdVisitConfigVO;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * 销售每日目标服务接口
 *
 * <AUTHOR>
 */
public interface BdDailyTargetService {

    /**
     * 批量新增销售每日目标
     *
     * @param input 新增输入参数
     */
    void batchAddBdDailyTarget(BatchAddBdDailyTargetInput input);

    /**
     * 修改销售每日目标
     *
     * @param input 修改销售每日目标入参
     */
    void modifyBdDailyTarget(ModifyBdDailyTargetInput input);

    /**
     * 新增或更新销售每日目标
     *
     * @param input
     */
    void upsertBdDailyTarget(UpsertBdDailyTargetInput input);

    /**
     * 批量删除销售每日目标
     *
     * @param input 批量删除输入参数
     */
    void batchDeleteBdDailyTarget(BatchDeleteBdDailyTargetInput input);

    /**
     * 分页查询每日销售目标
     *
     * @param query 分页查询条件
     * @return 分页结果
     */
    PageInfo<BdDailyTargetVO> listBdDailyTarget(BdDailyTargetPageQuery query);

    /**
     * 查询当前用户有权限查看的每日销售目标制定人列表
     *
     * @return
     */
    List<CrmBdOrg> listBdDailyTargetCreators();

    /**
     * 查询当前用户有权限查看或创建销售目标的销售列表
     *
     * @return
     */
    List<CrmBdOrg> listBdDailyTargetBds(BdDailyTargetListBdQuery query);

    /**
     * 查询每日销售目标
     *
     * @param query
     * @return
     */
    BdDailyTargetVO queryBdDailyTarget(BdDailyTargetQuery query);

    /**
     * 查询bd拜访设置
     * @param queryVO
     * @return
     */
    CommonResult<BdVisitConfigVO> queryBdVisitConfig(BdVisitConfigQuery queryVO);

    /**
     * 根据销售每日目标ID查询销售每日目标详情
     *
     * @param bdDailyTargetId 销售每日目标ID
     * @return 销售每日目标详情
     */
    BdDailyTargetVO queryBdDailyTargetDetail(Long bdDailyTargetId);

}
