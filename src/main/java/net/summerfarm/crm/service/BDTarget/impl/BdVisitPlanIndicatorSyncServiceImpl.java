package net.summerfarm.crm.service.BDTarget.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanIndicatorSyncRepository;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.service.BDTarget.BdVisitPlanIndicatorSyncService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanUpdate;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanIndicatorRepository;
import net.summerfarm.crm.mapper.repository.BDTarget.BdVisitPlanRepository;
import net.summerfarm.crm.service.BDTarget.MerchantPotentialValueCalculationService;
import net.summerfarm.crm.service.BDTarget.MerchantPotentialValueEsUpdateService;

/**
 * 销售拜访计划指标同步服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Slf4j
@Service
public class BdVisitPlanIndicatorSyncServiceImpl implements BdVisitPlanIndicatorSyncService {

    @Resource
    private BdVisitPlanIndicatorRepository bdVisitPlanIndicatorRepository;
    @Resource
    private BdVisitPlanIndicatorSyncRepository bdVisitPlanIndicatorSyncRepository;
    
    @Resource
    private BdVisitPlanRepository bdVisitPlanRepository;
    
    @Resource
    private MerchantPotentialValueCalculationService merchantPotentialValueCalculationService;
    
    @Resource
    private MerchantPotentialValueEsUpdateService merchantPotentialValueEsUpdateService;


    @Override
    @Transactional(readOnly = true)
    public List<Long> selectDistinctMerchantIdsByPage(int offset, int limit) {
        List<Long> result = bdVisitPlanIndicatorRepository.selectDistinctMerchantIdsByPage(offset, limit);
        return result != null ? result : Collections.emptyList();
    }

    @Override
    @Transactional
    public void processSyncDataInBatch(int pageSize) {
        log.info("开始批量处理同步数据，分页大小: {}", pageSize);
        
        int pageNum = 1;
        boolean hasMore = true;
        int totalProcessed = 0;
        int totalCalculated = 0;
        int totalEsUpdated = 0;
        int totalPlanUpdated = 0;
        
        while (hasMore) {
            try {
                // 1. 分页查询去重门店ID（性能优化：直接在数据库层去重）
                int offset = (pageNum - 1) * pageSize;
                List<Long> merchantIds = selectDistinctMerchantIdsByPage(offset, pageSize);
                
                if (merchantIds == null || merchantIds.isEmpty()) {
                    hasMore = false;
                    break;
                }
                
                // 2. 根据门店ID获取完整的同步数据（避免数据丢失）
                List<BdVisitPlanIndicatorSync> completeSyncData = bdVisitPlanIndicatorSyncRepository.selectSyncByMerchantIds(merchantIds);

                if (completeSyncData != null && !completeSyncData.isEmpty()) {
                    // 3. 批量更新拜访计划指标当前值（基于完整同步数据）
                    batchUpdatePlanIndicators(completeSyncData);
                    
                    // 4. 批量计算门店综合潜力值（基于完整同步数据）
                    Map<Long, BigDecimal> potentialValueMap = 
                            merchantPotentialValueCalculationService.batchCalculateComprehensivePotentialValueFromSyncData(completeSyncData);
                    
                    if (!potentialValueMap.isEmpty()) {
                        // 5. 批量更新ES门店潜力值
                        int esUpdateCount = merchantPotentialValueEsUpdateService.batchUpdateMerchantPotentialValueToEs(potentialValueMap);
                        totalEsUpdated += esUpdateCount;
                        
                        // 6. 批量更新拜访计划门店潜力值
                        // 为每个门店从completeSyncData中获取对应的拜访目标ID
                        Map<Long, Long> merchantToTargetIdMap = completeSyncData.stream()
                                .filter(sync -> sync.getBdDailyTargetId() != null)
                                .collect(Collectors.toMap(
                                        BdVisitPlanIndicatorSync::getMId,
                                        BdVisitPlanIndicatorSync::getBdDailyTargetId,
                                        (existing, replacement) -> existing // 如果有重复，保留第一个
                                ));
                        
                        // 构造更新对象列表
                        List<BdVisitPlanUpdate> updateList = potentialValueMap.entrySet().stream()
                                .filter(entry -> merchantToTargetIdMap.containsKey(entry.getKey()))
                                .map(entry -> {
                                    BdVisitPlanUpdate update = new BdVisitPlanUpdate();
                                    update.setMId(entry.getKey());
                                    update.setBdDailyTargetId(merchantToTargetIdMap.get(entry.getKey()));
                                    update.setMerchantPotentialValue(entry.getValue());
                                    return update;
                                })
                                .collect(Collectors.toList());
                        
                        int planUpdateCount = 0;
                        if (!updateList.isEmpty()) {
                            planUpdateCount = bdVisitPlanRepository.batchUpdatePotentialValueByMerchantIdAndTargetId(updateList);
                        } else {
                            log.warn("第{}页数据处理：没有找到有效的门店-拜访目标ID映射，跳过拜访计划更新", pageNum);
                        }
                        totalPlanUpdated += planUpdateCount;
                        totalCalculated += potentialValueMap.size();
                    }
                    
                    log.info("第{}页数据处理完成 - 去重门店数: {}, 完整同步记录: {}, 潜力值计算: {}, ES更新: {}, 计划更新: {}", 
                            pageNum, merchantIds.size(), completeSyncData.size(),
                            potentialValueMap != null ? potentialValueMap.size() : 0, 
                            potentialValueMap != null ? potentialValueMap.size() : 0, 
                            potentialValueMap != null ? potentialValueMap.size() : 0);
                }
                
                totalProcessed += merchantIds.size();
                hasMore = merchantIds.size() == pageSize;
                pageNum++;
                
                // 性能优化：适当休眠，避免对数据库和ES造成过大压力
                if (hasMore && pageNum % 10 == 0) {
                    try {
                        Thread.sleep(200); // 每10页休眠200ms
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("处理过程中线程被中断");
                        break;
                    }
                }
                
            } catch (Exception e) {
                log.error("处理第{}页数据失败", pageNum, e);
                throw e;
            }
        }
        
        log.info("所有数据处理完成 - 总同步记录: {}, 总计算门店: {}, 总ES更新: {}, 总计划更新: {}", 
                totalProcessed, totalCalculated, totalEsUpdated, totalPlanUpdated);
    }

    /**
     * 批量更新计划指标当前值和潜力值
     * @param syncDataList 同步数据列表
     */
    private void batchUpdatePlanIndicators(List<BdVisitPlanIndicatorSync> syncDataList) {
        try {
            if (syncDataList == null || syncDataList.isEmpty()) {
                return;
            }
            
            // 使用组合条件查询现有的拜访计划指标
            List<BdVisitPlanIndicator> existingIndicators = bdVisitPlanIndicatorRepository.selectByCompositeConditions(
                    syncDataList);
            
            // 构建map key进行精确匹配过滤：bdDailyTargetId+bdDailyTargetDetailId+bdId+mId
            Map<String, BdVisitPlanIndicator> existingIndicatorMap = existingIndicators.stream()
                    .collect(Collectors.toMap(
                            indicator -> bdVisitPlanIndicatorRepository.buildMapKey(indicator.getBdDailyTargetId(), indicator.getBdDailyTargetDetailId(),
                                                   indicator.getBdId(), indicator.getMId()),
                            indicator -> indicator,
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));

            // 使用map key过滤出存在对应计划指标的同步数据
            List<BdVisitPlanIndicatorSync> validSyncData = syncDataList.stream()
                    .filter(sync -> {
                        String mapKey = bdVisitPlanIndicatorRepository.buildMapKey(sync.getBdDailyTargetId(), sync.getBdDailyTargetDetailId(),
                                                  sync.getBdId(), sync.getMId());
                        return existingIndicatorMap.containsKey(mapKey);
                    })
                    .collect(Collectors.toList());
            
            if (validSyncData.isEmpty()) {
                log.warn("批量更新计划指标：map key过滤后无有效同步数据，原始数据量: {}, 现有指标数量: {}", 
                        syncDataList.size(), existingIndicators.size());
                return;
            }
            
            // 执行批量更新
            int updateCount = bdVisitPlanIndicatorRepository.batchUpdateIndicatorValueAndPotential(validSyncData);
            
            log.info("批量更新计划指标完成，原始数据: {}, 现有指标: {}, 有效数据: {}, 更新记录数: {}", 
                    syncDataList.size(), existingIndicators.size(), validSyncData.size(), updateCount);
            
        } catch (Exception e) {
            log.error("批量更新计划指标失败", e);
            throw e;
        }
    }
}