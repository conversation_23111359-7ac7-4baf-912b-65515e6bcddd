package net.summerfarm.crm.service.BDTarget.converter;

import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.enums.BDTarget.BdDailyTargetDetailEnum;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTarget;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.input.BDTarget.AddBdDailyTargetDetailInput;
import net.summerfarm.crm.model.vo.objectiveManagement.BdDailyTargetDetailVO;
import net.summerfarm.crm.model.vo.objectiveManagement.BdDailyTargetVO;
import org.apache.commons.collections.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

public abstract class BdDailyTargetConverter {

    public static List<BdDailyTargetVO> convertToBdDailyTargetVOList(List<BdDailyTarget> targetList,
                                                                     Map<Long, List<BdDailyTargetDetail>> targetDetailMap) {
        if (CollectionUtils.isEmpty(targetList)) {
            return Collections.emptyList();
        }
        List<BdDailyTargetVO> targetVOList = new ArrayList<>();
        for (BdDailyTarget target : targetList) {
            List<BdDailyTargetDetail> targetDetails = (targetDetailMap != null) ? targetDetailMap.get(target.getId()) : null;
            targetVOList.add(convertToBdDailyTargetVO(target, targetDetails));
        }
        return targetVOList;
    }

    public static BdDailyTargetVO convertToBdDailyTargetVO(BdDailyTarget target, List<BdDailyTargetDetail> targetDetails) {
        BdDailyTargetVO targetVO = new BdDailyTargetVO();
        targetVO.setBdDailyTargetId(target.getId());
        targetVO.setCreateTime(BaseDateUtils.localDateTimeToString(target.getCreateTime()));
        targetVO.setBdId(target.getBdId());
        targetVO.setBdName(target.getBdName());
        targetVO.setTargetCreator(target.getTargetCreator());
        targetVO.setTargetCreatorName(target.getTargetCreatorName());
        targetVO.setTargetDate(target.getTargetDate());
        targetVO.setVisitOfflineCount(target.getVisitOfflineCount());
        targetVO.setVisitOnlineCount(target.getVisitOnlineCount());
        targetVO.setTrafficType(target.getTrafficType());
        targetVO.setTargetCompleteReminder(target.getTargetCompleteReminder());
        if (CollectionUtils.isNotEmpty(targetDetails)) {
            List<BdDailyTargetDetailVO> targetDetailVOList = targetDetails.stream()
                    .map(BdDailyTargetConverter::convertToBdDailyTargetDetailVO).filter(Objects::nonNull)
                    .sorted(Comparator.comparing(BdDailyTargetDetailVO::getPriority)).collect(Collectors.toList());
            targetVO.setBdDailyTargetDetailVOList(targetDetailVOList);
        }
        return targetVO;
    }

    public static BdDailyTargetDetailVO convertToBdDailyTargetDetailVO(BdDailyTargetDetail targetDetail) {
        if (targetDetail == null) {
            return null;
        }
        BdDailyTargetDetailVO targetDetailVO = new BdDailyTargetDetailVO();
        targetDetailVO.setBdId(targetDetail.getBdId());
        targetDetailVO.setTargetDate(targetDetail.getTargetDate());
        targetDetailVO.setTargetType(targetDetail.getTargetType());
        targetDetailVO.setTargetName(targetDetail.getTargetName());
        targetDetailVO.setPriority(targetDetail.getPriority());
        targetDetailVO.setBusinessType(targetDetail.getBusinessType());
        targetDetailVO.setIndicatorType(targetDetail.getIndicatorType());
        targetDetailVO.setCategoryName(targetDetail.getCategoryName());
        targetDetailVO.setSku(targetDetail.getSku());
        targetDetailVO.setSpu(targetDetail.getSpu());
        if (targetDetail.getIndicatorExpectedValue() != null) {
            targetDetailVO.setIndicatorExpectedValue(targetDetail.getIndicatorExpectedValue().stripTrailingZeros().toPlainString());
        }
        if (targetDetail.getIndicatorCurrentValue() != null) {
            targetDetailVO.setIndicatorCurrentValue(targetDetail.getIndicatorCurrentValue().stripTrailingZeros().toPlainString());
        }
        targetDetailVO.setIndicatorStatus(targetDetail.getIndicatorStatus());
        return targetDetailVO;
    }

    /**
     * 构建销售每日目标明细列表
     *
     * @param bdDailyTarget 销售每日目标
     * @param detailInputs 目标明细输入列表
     * @return 销售每日目标明细列表
     */
    public static List<BdDailyTargetDetail> buildBdDailyTargetDetails(BdDailyTarget bdDailyTarget,
                                                                      List<? extends AddBdDailyTargetDetailInput> detailInputs) {
        if (bdDailyTarget == null || CollectionUtils.isEmpty(detailInputs)) {
            return Collections.emptyList();
        }
        List<BdDailyTargetDetail> details = new ArrayList<>();
        for (AddBdDailyTargetDetailInput detailInput : detailInputs) {
            BdDailyTargetDetail detail = new BdDailyTargetDetail();
            detail.setBdDailyTargetId(bdDailyTarget.getId());
            detail.setBdId(bdDailyTarget.getBdId());
            detail.setBdName(bdDailyTarget.getBdName());
            detail.setPriority(detailInput.getPriority());
            detail.setTargetType(detailInput.getTargetType());
            detail.setTargetName(detailInput.getTargetName());
            detail.setTargetDate(bdDailyTarget.getTargetDate());
            detail.setBusinessType(detailInput.getBusinessType());
            detail.setIndicatorType(detailInput.getIndicatorType());
            detail.setCategoryName(detailInput.getCategoryName());
            detail.setSku(detailInput.getSku());
            detail.setSpu(detailInput.getSpu());
            detail.setIndicatorExpectedValue(detailInput.getIndicatorExpectedValue());
            detail.setIndicatorStatus(BdDailyTargetDetailEnum.IndicatorStatus.INCOMPLETE.getCode());
            details.add(detail);
        }
        return details;
    }

}
