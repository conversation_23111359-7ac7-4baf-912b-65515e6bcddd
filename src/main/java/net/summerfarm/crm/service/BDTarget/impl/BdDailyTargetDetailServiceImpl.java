package net.summerfarm.crm.service.BDTarget.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailUpdate;
import net.summerfarm.crm.mapper.manage.BDTarget.BdDailyTargetDetailMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;

import net.summerfarm.crm.service.BDTarget.BdDailyTargetDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 销售拜访目标指标服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class BdDailyTargetDetailServiceImpl implements BdDailyTargetDetailService {

    @Resource
    private BdDailyTargetDetailMapper bdDailyTargetDetailMapper;

    @Override
    @Transactional(readOnly = true)
    public BdDailyTargetDetail selectByPrimaryKey(Long id) {
        if (id == null) {
            return null;
        }
        return bdDailyTargetDetailMapper.selectByPrimaryKey(id);
    }



    @Override
    @Transactional
    public int updateByPrimaryKeySelective(BdDailyTargetDetailUpdate record) {
        if (record == null || record.getId() == null) {
            log.warn("更新参数无效，record: {}", record);
            return 0;
        }
        
        try {
            int rows = bdDailyTargetDetailMapper.updateByPrimaryKeySelective(record);
            log.debug("选择性更新拜访目标指标，ID: {}, 影响行数: {}", record.getId(), rows);
            return rows;
        } catch (Exception e) {
            log.error("选择性更新拜访目标指标失败，record: {}", record, e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<BdDailyTargetDetail> selectByDailyTargetIds(List<Long> dailyTargetIds) {
        if (CollectionUtils.isEmpty(dailyTargetIds)) {
            log.warn("根据ID列表查询拜访目标指标：ID列表为空");
            return Collections.emptyList();
        }
        
        try {
            // 使用批量查询方法
            List<BdDailyTargetDetail> result = bdDailyTargetDetailMapper.selectByDailyTargetIds(dailyTargetIds);
            
            log.debug("根据ID列表查询拜访目标指标：请求数量：{}，结果数量：{}", 
                    dailyTargetIds.size(), result != null ? result.size() : 0);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据ID列表查询拜访目标指标失败：请求数量：{}", dailyTargetIds.size(), e);
            throw e;
        }
    }

    @Override
    public List<BdDailyTargetDetail> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("根据ID列表查询拜访目标指标：ID列表为空");
            return Collections.emptyList();
        }

        try {
            // 使用批量查询方法
            List<BdDailyTargetDetail> result = bdDailyTargetDetailMapper.selectByIds(ids);

            log.debug("根据ID列表查询拜访目标指标：请求数量：{}，结果数量：{}",
                    ids.size(), result != null ? result.size() : 0);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("根据ID列表查询拜访目标指标失败：请求数量：{}", ids.size(), e);
            throw e;
        }
    }

}