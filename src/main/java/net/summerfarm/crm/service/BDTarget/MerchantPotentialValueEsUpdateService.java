package net.summerfarm.crm.service.BDTarget;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 门店潜力值ES更新服务接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface MerchantPotentialValueEsUpdateService {

    /**
     * 批量更新门店潜力值到ES（根据映射，分批处理每50个一次）
     *
     * @param merchantPotentialMap 门店ID和潜力值的映射
     * @return 更新成功的门店数量
     */
    int batchUpdateMerchantPotentialValueToEs(Map<Long, BigDecimal> merchantPotentialMap);

}