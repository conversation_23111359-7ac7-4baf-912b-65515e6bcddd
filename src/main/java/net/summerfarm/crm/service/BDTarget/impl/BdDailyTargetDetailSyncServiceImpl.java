package net.summerfarm.crm.service.BDTarget.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.offline.BDTarget.BdDailyTargetDetailSyncMapper;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailSync;
import net.summerfarm.crm.service.BDTarget.BdDailyTargetDetailSyncService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 销售拜访目标指标同步服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class BdDailyTargetDetailSyncServiceImpl implements BdDailyTargetDetailSyncService {

    @Resource
    private BdDailyTargetDetailSyncMapper bdDailyTargetDetailSyncMapper;

    @Override
    @Transactional(readOnly = true)
    public List<BdDailyTargetDetailSync> selectByPage(int offset, int limit) {
        if (offset < 0 || limit <= 0) {
            log.warn("分页参数无效，offset: {}, limit: {}", offset, limit);
            return null;
        }
        
        try {
            // 计算页码（MyBatis-Plus分页从1开始）
            int pageNum = (offset / limit) + 1;
            
            // 创建分页对象
            Page<BdDailyTargetDetailSync> page = new Page<>(pageNum, limit);
            
            // 创建查询条件，按ID升序排列
            QueryWrapper<BdDailyTargetDetailSync> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByAsc("id");
            
            // 执行分页查询
            Page<BdDailyTargetDetailSync> result = bdDailyTargetDetailSyncMapper.selectPage(page, queryWrapper);
            
            log.debug("查询同步数据分页成功，offset: {}, limit: {}, 返回记录数: {}", 
                    offset, limit, result.getRecords().size());
            
            return result.getRecords();
            
        } catch (Exception e) {
            log.error("查询同步数据分页失败，offset: {}, limit: {}", offset, limit, e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public int countTotal() {
        try {
            // 使用BaseMapper的selectCount方法
            QueryWrapper<BdDailyTargetDetailSync> queryWrapper = new QueryWrapper<>();
            Long count = bdDailyTargetDetailSyncMapper.selectCount(queryWrapper);
            
            log.debug("统计同步数据总数: {}", count);
            return count.intValue();
            
        } catch (Exception e) {
            log.error("统计同步数据总数失败", e);
            throw e;
        }
    }
}