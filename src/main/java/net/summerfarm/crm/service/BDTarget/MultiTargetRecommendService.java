package net.summerfarm.crm.service.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.ProductRangeTypeEnum;
import net.summerfarm.crm.mapper.repository.BDTarget.MerchantDataRepository;
import net.summerfarm.crm.model.dto.BDTarget.MerchantDataDTO;
import net.summerfarm.crm.model.dto.RecommendContextDTO;
import net.summerfarm.crm.enums.BDTarget.SalesTargetTypeEnum;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 多目标推荐服务
 * 基于门店数据和目标类型生成智能推荐内容
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Slf4j
@Service
public class MultiTargetRecommendService {

    @Resource
    private MerchantDataRepository merchantDataRepository;

    /**
     * 生成推荐脚本
     *
     * @param merchantId 门店ID
     * @param targets 目标列表
     * @return 推荐脚本内容
     */
    public String generateRecommendScript(Long merchantId, List<BdDailyTargetDetail> targets) {
        try {
            log.info("开始为门店[{}]生成推荐脚本，目标数量：{}", merchantId, targets.size());
            
            // 获取门店数据
            MerchantDataDTO merchantData = getMerchantData(merchantId, targets);
            
            // 构建推荐上下文
            RecommendContextDTO context = buildRecommendContext(merchantId, targets, merchantData);
            
            // 生成推荐脚本
            String script = generateScriptContent(context);
            
            log.info("门店[{}]推荐脚本生成完成，脚本长度：{}", merchantId, script.length());
            return script;
            
        } catch (Exception e) {
            log.error("生成推荐脚本失败，门店ID：{}", merchantId, e);
            return "推荐脚本生成失败，请稍后重试";
        }
    }

    /**
     * 获取门店数据
     */
    private MerchantDataDTO getMerchantData(Long mId, List<BdDailyTargetDetail> targets) {
        return merchantDataRepository.getMerchantData(mId, targets);
    }

    /**
     * 构建推荐上下文
     */
    private RecommendContextDTO buildRecommendContext(Long merchantId, List<BdDailyTargetDetail> targets, MerchantDataDTO merchantData) {
        RecommendContextDTO context = new RecommendContextDTO();
        context.setMId(merchantId);
        context.setMerchantData(merchantData);
        
        // 分析目标类型
        List<Integer> targetTypes = targets.stream()
                .map(BdDailyTargetDetail::getTargetType)
                .distinct()
                .collect(Collectors.toList());
        
        // 根据目标类型构建不同的推荐上下文
        for (Integer targetType : targetTypes) {
            SalesTargetTypeEnum.TargetType type = SalesTargetTypeEnum.TargetType.getByCode(targetType);
            if (type != null) {
                if (type.isNewCustomerTarget()) {
                    buildNewCustomerContext(context, merchantData);
                } else if (type.isProductTarget()) {
                    buildProductLimitedContext(context, merchantId, targets, type);
                } else {
                    buildOldCustomerContext(context, merchantData);
                }
            }
        }
        
        return context;
    }

    /**
     * 构建新客户推荐上下文
     */
    private void buildNewCustomerContext(RecommendContextDTO context, MerchantDataDTO merchantData) {
        // 新客户推荐逻辑
        RecommendContextDTO.NewCustomerRecommendContext newCustomerContext = 
                new RecommendContextDTO.NewCustomerRecommendContext();

        // 获取门店类型（从门店数据中获取，如果没有则使用默认值）
        String merchantType = getMerchantType(context.getMId());
        newCustomerContext.setMerchantType(merchantType);

        // 查询该门店类型的产品排行榜
        List<RecommendContextDTO.ProductRankingDTO> productRanking = 
                merchantDataRepository.getProductRankingByMerchantType(newCustomerContext.getMerchantType(), 15);
        newCustomerContext.setTypeRankingProducts(productRanking);
        
        context.setNewCustomerContext(newCustomerContext);
    }

    /**
     * 获取门店类型
     */
    private String getMerchantType(Long mId) {
        // 这里可以根据门店ID查询门店类型
        // 暂时返回默认类型，后续可以从门店基础信息中获取
        return "其他";
    }

    /**
     * 构建老客户推荐上下文
     */
    private void buildOldCustomerContext(RecommendContextDTO context, MerchantDataDTO merchantData) {
        // 老客户推荐逻辑
        RecommendContextDTO.OldCustomerRecommendContext oldCustomerContext = 
                new RecommendContextDTO.OldCustomerRecommendContext();
        
        // 查询门店常购商品
        List<RecommendContextDTO.ProductPurchaseDTO> frequentProducts = 
                merchantDataRepository.getFrequentProducts(context.getMId(), 10);
        oldCustomerContext.setFrequentProducts(frequentProducts);
        
        context.setOldCustomerContext(oldCustomerContext);
    }

    /**
     * 构建产品限制推荐上下文
     */
    private void buildProductLimitedContext(RecommendContextDTO context, Long merchantId, 
                                          List<BdDailyTargetDetail> targets,
                                            SalesTargetTypeEnum.TargetType targetType) {
        RecommendContextDTO.ProductLimitedRecommendContext productContext = 
                new RecommendContextDTO.ProductLimitedRecommendContext();
        
        // 根据目标类型设置限制类型
        String limitType = getLimitTypeByTargetType(targetType);
        if (limitType == null) {
            return;
        }
        productContext.setLimitType(limitType);

        BdDailyTargetDetail targetDetail = targets.stream()
                .filter(t -> t.getTargetType().equals(targetType.getCode()))
                .findFirst()
                .orElse(null);
        if (targetDetail == null) {
            return;
        }

        if (context.getMerchantData() == null) {
            return;
        }


        // 获取限制值（从目标详情中提取）
        String limitValue = extractLimitValueFromTargets(targets, targetType);
        productContext.setLimitValue(limitValue);
        ProductRangeTypeEnum rangeType = ProductRangeTypeEnum.getByDescAndTargetType(targetDetail, targetType);
        // 查询限定范围内的商品列表
        if (limitValue != null && rangeType != null) {
            List<RecommendContextDTO.ProductInRangeDTO> productsInRange = 
                    merchantDataRepository.getProductsInRange(merchantId, rangeType.getCode(), limitValue, 20);
            productContext.setProductsInRange(productsInRange);
        }

        for (MerchantDataDTO.BdDailyTargetDetailDTO dailyTargetDetail : context.getMerchantData().getDailyTargetDetails()) {
            if (dailyTargetDetail.getTargetType().equals(targetType.getCode())) {
                productContext.setLimitValueDesc(
                        dailyTargetDetail.getSkuName() != null ? dailyTargetDetail.getSkuName() :
                                dailyTargetDetail.getSpuName() != null ? dailyTargetDetail.getSpuName() :
                                        dailyTargetDetail.getCategoryName() != null ? dailyTargetDetail.getCategoryName() :
                                                dailyTargetDetail.getTargetName());
                break;
            }
        }
        
        context.setProductLimitedContext(productContext);
    }

    /**
     * 根据目标类型获取限制类型
     */
    private String getLimitTypeByTargetType(SalesTargetTypeEnum.TargetType targetType) {
        switch (targetType) {
            case CATEGORY_POTENTIAL:
                return "category";
            case SKU_POTENTIAL:
                return "sku";
            case SPU_POTENTIAL:
                return "spu";
            default:
                return null;
        }
    }

    /**
     * 从目标详情中提取限制值
     */
    private String extractLimitValueFromTargets(List<BdDailyTargetDetail> targets,
                                                SalesTargetTypeEnum.TargetType targetType) {
        for (BdDailyTargetDetail target : targets) {
            if (target.getTargetType().equals(targetType.getCode())) {
                // 根据目标类型返回相应的限制值
                switch (targetType) {
                    case CATEGORY_POTENTIAL:
                        return target.getCategoryName();
                    case SKU_POTENTIAL:
                        return target.getSku();
                    case SPU_POTENTIAL:
                        return target.getSpu();
                    default:
                        return null;
                }
            }
        }
        return null;
    }

    /**
     * 生成脚本内容
     */
    private String generateScriptContent(RecommendContextDTO context) {
        StringBuilder script = new StringBuilder();
        
        // 生成时间和基本信息
        script.append("销售拜访建议 - ").append(LocalDateTime.now().toString()).append("\n\n");
        
        // 门店基本信息
        if (context.getMerchantData() != null) {
            appendMerchantBasicInfo(script, context.getMerchantData());
        }
        
        // 销售目标分析
        appendTargetAnalysis(script, context);
        
        // 推荐策略
        appendRecommendationStrategy(script, context);
        
        // 销售话术建议
//        appendSalesScript(script, context);
        
        // 执行检查清单
//        appendExecutionChecklist(script);
        
        return script.toString();
    }
    
    /**
     * 添加执行检查清单
     */
    private void appendExecutionChecklist(StringBuilder script) {
        script.append("## 执行检查清单\n\n");
        script.append("### 准备工作\n");
        script.append("- [ ] 确认门店基础信息准确性\n");
        script.append("- [ ] 准备产品介绍资料\n");
        script.append("- [ ] 确认优惠政策和价格\n");
        script.append("- [ ] 准备成功案例和客户证言\n\n");
        
        script.append("### 沟通执行\n");
        script.append("- [ ] 开场问候和自我介绍\n");
        script.append("- [ ] 了解客户当前需求和痛点\n");
        script.append("- [ ] 根据推荐策略介绍产品\n");
        script.append("- [ ] 处理客户疑虑和异议\n");
        script.append("- [ ] 促成订单或约定下次沟通\n\n");
        
        script.append("### 跟进记录\n");
        script.append("- [ ] 记录客户反馈和关注点\n");
        script.append("- [ ] 更新客户状态和标签\n");
        script.append("- [ ] 安排后续跟进计划\n");
        script.append("- [ ] 总结本次沟通效果\n\n");
    }
    
    /**
     * 添加门店基础信息
     */
    private void appendMerchantBasicInfo(StringBuilder script, MerchantDataDTO merchantData) {
        script.append("## 门店基础信息\n\n");
        
        if (merchantData != null) {
            // 基础信息
            script.append("### 基础数据\n");
            script.append("门店ID: ").append(merchantData.getMId() != null ? merchantData.getMId() : "未知").append("\n");
            
            if (merchantData.getMerchantName() != null) {
                script.append("门店名称: ").append(merchantData.getMerchantName()).append("\n");
            }
            
            script.append("客户类型: ").append(merchantData.getIsNewCustomer() != null ? (merchantData.getIsNewCustomer() ? "新客户" : "老客户") : "未知").append("\n");
            script.append("登录状态: ").append(getLoginStatusText(merchantData.getLastLoginTime())).append("\n");
            script.append("活跃度: ").append(getActivityText(merchantData)).append("\n");
            
            // 历史记录统计
            script.append("\n### 历史记录统计\n");
            
            int visitCount = merchantData.getVisitRecords() != null ? merchantData.getVisitRecords().size() : 0;
            script.append("拜访记录: ").append(visitCount).append("条\n");
            for (MerchantDataDTO.VisitRecordDTO visitRecord : merchantData.getVisitRecords()) {
                script.append("- 拜访人: ").append(visitRecord.getAdminName() != null ? visitRecord.getAdminName() : "未知")
                    .append(" 跟进方式: ").append(visitRecord.getFollowUpWay() != null ? visitRecord.getFollowUpWay() : "未知")
                    .append(" 跟进情况描述: ").append(visitRecord.getCondition() != null ? visitRecord.getCondition() : "未知")
                        .append("\n");
            }
            
            int orderCount = merchantData.getOrderRecords() != null ? merchantData.getOrderRecords().size() : 0;
            script.append("订单记录: ").append(orderCount).append("条\n");
            for (MerchantDataDTO.OrderRecordDTO orderRecord : merchantData.getOrderRecords()) {
                script.append("- 订单号: ").append(orderRecord.getOrderNo() != null ? orderRecord.getOrderNo() : "未知")
                    .append(" 下单时间: ").append(orderRecord.getOrderTime() != null ? orderRecord.getOrderTime() : "未知")
                    .append(" 订单类型: ").append(orderRecord.getOrderTypeDesc() != null ? orderRecord.getOrderTypeDesc() : "未知")
                    .append(" 订单状态: ").append(orderRecord.getOrderStatusDesc() != null ? orderRecord.getOrderStatusDesc() : "未知")
                    .append(" 商品名称: ").append(orderRecord.getProductName() != null ? orderRecord.getProductName() : "未知")
                    .append(" 商品购买数量: ").append(orderRecord.getAmount() != null ? orderRecord.getAmount() : "未知")
                        .append("\n");
            }

            int complaintCount = merchantData.getComplaintRecords() != null ? merchantData.getComplaintRecords().size() : 0;
            script.append("客诉记录: ").append(complaintCount).append("条\n");
            for (MerchantDataDTO.ComplaintRecordDTO complaintRecord : merchantData.getComplaintRecords()) {
                script.append("- 订单号: ").append(complaintRecord.getOrderNo() != null ? complaintRecord.getOrderNo() : "未知")
                    .append(" 售后单号: ").append(complaintRecord.getAfterSaleOrderNo() != null ? complaintRecord.getAfterSaleOrderNo() : "未知")
                    .append(" 售后商品sku: ").append(complaintRecord.getSku() != null ? complaintRecord.getSku() : "未知")
                    .append(" 客诉状态: ").append(complaintRecord.getStatusDesc() != null ? complaintRecord.getStatusDesc() : "未知")
                    .append(" 处理方式描述: ").append(complaintRecord.getHandleTypeDesc() != null ? complaintRecord.getHandleTypeDesc() : "未知")
                    .append(" 退款类型: ").append(complaintRecord.getRefundType() != null ? complaintRecord.getRefundType() : "未知")
                    .append(" 售后备注: ").append(complaintRecord.getAfterSaleRemark() != null ? complaintRecord.getAfterSaleRemark() : "未知")
                    .append(" 处理二级备注: ").append(complaintRecord.getHandleSecondaryRemark() != null ? complaintRecord.getHandleSecondaryRemark() : "未知")
                        .append("\n");
            }

        } else {
            script.append("门店基础信息获取失败\n");
        }
        script.append("\n");
    }
    
    /**
     * 添加目标分析
     */
    private void appendTargetAnalysis(StringBuilder script, RecommendContextDTO context) {
        script.append("## 当日销售目标分析\n");
        
        // 新客户目标
        if (context.getNewCustomerContext() != null) {
            script.append("### 新客户拓展目标\n");
            script.append("- **目标类型**: 拉新客户数\n");
            script.append("- **重点策略**: 首次购买转化\n");
            script.append("- **关注指标**: 首单成功率\n\n");
        }
        
        // 老客户目标
        if (context.getOldCustomerContext() != null) {
            script.append("### 老客户维护目标\n");
            script.append("- **目标类型**: 月活客户数、复购提升\n");
            script.append("- **重点策略**: 个性化推荐、增购交叉销售\n");
            script.append("- **关注指标**: 复购率、客单价提升\n\n");
        }
        
        // 产品限制目标
        if (context.getProductLimitedContext() != null) {
            script.append("### 产品推广目标\n");
            script.append("- **目标类型**: 品类目标、SKU目标\n");
            script.append("- **重点策略**: 特定产品推广\n");
            script.append("- **关注指标**: 目标产品销量\n\n");
        }
    }
    
    /**
     * 添加推荐策略
     */
    private void appendRecommendationStrategy(StringBuilder script, RecommendContextDTO context) {
        script.append("推荐策略:\n\n");
        
        // 新客户推荐策略
        if (context.getNewCustomerContext() != null) {
            RecommendContextDTO.NewCustomerRecommendContext newCustomerContext = context.getNewCustomerContext();
            script.append("新客户转化策略:\n");
            script.append("核心目标: 建立信任、降低风险、快速见效\n");
            script.append("客户类型: ").append(newCustomerContext.getMerchantType()).append("\n");
            script.append("策略重点: 性价比 + 易用性 + 服务支持\n");
            
            // 推荐热销产品
            if (newCustomerContext.getTypeRankingProducts() != null && !newCustomerContext.getTypeRankingProducts().isEmpty()) {
                script.append("热销产品推荐:\n");
                for (int i = 0; i < Math.min(10, newCustomerContext.getTypeRankingProducts().size()); i++) {
                    RecommendContextDTO.ProductRankingDTO product = newCustomerContext.getTypeRankingProducts().get(i);
                    script.append((i+1)).append(". ").append(product.getProductName())
                          .append(" - 排名第").append(product.getRanking()).append("名\n");
                }
            }
            script.append("\n");
            
        } else if (context.getOldCustomerContext() != null) {
            RecommendContextDTO.OldCustomerRecommendContext oldCustomerContext = context.getOldCustomerContext();
            script.append("老客户维护策略:\n");
            script.append("核心目标: 深化合作、提升价值、增强粘性\n");
            script.append("策略重点: 个性化推荐 + 增值服务 + 专属优惠\n");
            
            // 常购商品推荐
            if (oldCustomerContext.getFrequentProducts() != null && !oldCustomerContext.getFrequentProducts().isEmpty()) {
                script.append("常购商品推荐:\n");
                for (int i = 0; i < Math.min(10, oldCustomerContext.getFrequentProducts().size()); i++) {
                    RecommendContextDTO.ProductPurchaseDTO product = oldCustomerContext.getFrequentProducts().get(i);
                    script.append((i+1)).append(". ").append(product.getProductName())
                          .append(" - 购买").append(product.getPurchaseCount()).append("次，累计")
                          .append(product.getTotalAmount()).append("元\n");
                }
            }
            script.append("\n");
        }
        
        if (context.getProductLimitedContext() != null) {
            RecommendContextDTO.ProductLimitedRecommendContext productContext = context.getProductLimitedContext();
            script.append("产品推广策略:\n");
            script.append("核心目标: 精准推广、突出优势、快速转化\n");
            script.append("目标产品: ").append(productContext.getLimitValueDesc()).append("\n");
            script.append("策略重点: 产品优势\n");
            
            // 推荐具体产品
            if (productContext.getProductsInRange() != null && !productContext.getProductsInRange().isEmpty()) {
                script.append("重点推荐产品:\n");
                for (int i = 0; i < Math.min(10, productContext.getProductsInRange().size()); i++) {
                    RecommendContextDTO.ProductInRangeDTO product = productContext.getProductsInRange().get(i);
                    script.append((i+1)).append(". ").append(product.getProductName())
                          .append(" - 近7天购买").append(product.getPurchaseCount()).append("次，")
                          .append("购买").append(product.getTotalAmount()).append("元，")
                          .append("\n");
                }
            }
            script.append("\n");
        }
        
    }
    
    /**
     * 添加销售话术
     */
    private void appendSalesScript(StringBuilder script, RecommendContextDTO context) {
        
        // 根据不同目标类型提供针对性话术
        if (context.getNewCustomerContext() != null) {
            script.append("新客户推荐话术:\n");
            RecommendContextDTO.NewCustomerRecommendContext newCustomerContext = context.getNewCustomerContext();
            script.append("基于您").append(newCustomerContext.getMerchantType()).append("类型门店的特点，我推荐以下产品：\n");
            
            if (newCustomerContext.getTypeRankingProducts() != null && !newCustomerContext.getTypeRankingProducts().isEmpty()) {
                for (int i = 0; i < Math.min(10, newCustomerContext.getTypeRankingProducts().size()); i++) {
                    RecommendContextDTO.ProductRankingDTO product = newCustomerContext.getTypeRankingProducts().get(i);
                    script.append("- ").append(product.getProductName())
                          .append("：在同类门店中排名第").append(product.getRanking())
                          .append("\n");
                }
            }
            script.append("\n");
        }
        
        if (context.getOldCustomerContext() != null) {
            script.append("老客户维护话术:\n");
            RecommendContextDTO.OldCustomerRecommendContext oldCustomerContext = context.getOldCustomerContext();
            script.append("感谢您一直以来的信任与支持。基于您的购买历史，我为您推荐：\n");
            
            if (oldCustomerContext.getFrequentProducts() != null && !oldCustomerContext.getFrequentProducts().isEmpty()) {
                for (int i = 0; i < Math.min(10, oldCustomerContext.getFrequentProducts().size()); i++) {
                    RecommendContextDTO.ProductPurchaseDTO product = oldCustomerContext.getFrequentProducts().get(i);
                    script.append("- 基于您对").append(product.getProductName())
                          .append("的").append(product.getPurchaseCount())
                          .append("次购买，推荐相关升级产品，能更好地满足您的需求\n");
                }
            }
            script.append("\n");
        }
        
        if (context.getProductLimitedContext() != null) {
            RecommendContextDTO.ProductLimitedRecommendContext productContext = context.getProductLimitedContext();
            if (productContext.getProductsInRange() != null && !productContext.getProductsInRange().isEmpty()) {
                script.append("产品推广话术:\n");
                script.append("针对").append(productContext.getLimitValue()).append("相关需求，我特别推荐：\n");
                for (int i = 0; i < Math.min(10, productContext.getProductsInRange().size()); i++) {
                    RecommendContextDTO.ProductInRangeDTO product = productContext.getProductsInRange().get(i);
                    script.append("- ").append(product.getProductName())
                          .append("：近7天购买").append(product.getPurchaseCount()).append("次，")
                          .append("购买").append(product.getTotalAmount()).append("元，")
                          .append("\n");
                }
            }
            script.append("\n");
        }
    }
    
    /**
     * 获取登录状态文本描述
     */
    private String getLoginStatusText(LocalDateTime lastLoginTime) {
        if (lastLoginTime == null) {
            return "未知";
        }
        
        LocalDateTime now = LocalDateTime.now();
        long daysSinceLastLogin = java.time.Duration.between(lastLoginTime, now).toDays();
        
        if (daysSinceLastLogin <= 3) {
            return "活跃";
        } else if (daysSinceLastLogin <= 10) {
            return "一般";
        } else if (daysSinceLastLogin <= 30) {
            return "较少";
        } else {
            return "不活跃";
        }
    }
    
    /**
     * 获取活跃度文本描述
     */
    private String getActivityText(MerchantDataDTO merchantData) {
        if (merchantData.getLastLoginTime() == null) {
            return "未知";
        }
        
        LocalDateTime lastLogin = merchantData.getLastLoginTime();
        LocalDateTime now = LocalDateTime.now();
        long daysSinceLastLogin = java.time.Duration.between(lastLogin, now).toDays();
        
        if (daysSinceLastLogin <= 1) {
            return "高";
        } else if (daysSinceLastLogin <= 7) {
            return "中";
        } else if (daysSinceLastLogin <= 30) {
            return "低";
        } else {
            return "极低";
        }
    }
}