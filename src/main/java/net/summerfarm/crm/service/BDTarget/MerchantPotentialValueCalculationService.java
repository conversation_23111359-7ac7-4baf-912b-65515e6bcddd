package net.summerfarm.crm.service.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator;
import net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 门店潜力值计算服务接口
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public interface MerchantPotentialValueCalculationService {

    /**
     * 批量计算门店综合潜力值（基于同步数据）
     *
     * @param syncDataList 拜访计划指标同步数据列表
     * @return 门店ID和潜力值的映射
     */
    Map<Long, BigDecimal> batchCalculateComprehensivePotentialValueFromSyncData(List<BdVisitPlanIndicatorSync> syncDataList);

    /**
     * 计算目标权重
     * 将总权重的80%分配给前三个优先级（P1, P2, P3）的目标
     * 将总权重的20%平均分配给P3之后的所有其他目标
     *
     * @param targetIndicators 目标指标列表
     * @return 目标指标ID和权重的映射
     */
    Map<Long, BigDecimal> calculateTargetWeights(List<BdDailyTargetDetail> targetIndicators);

    /**
     * 批量获取门店拜访系数
     *
     * @param merchantIds 门店ID列表
     * @return 门店ID和拜访系数的映射
     */
    Map<Long, BigDecimal> batchGetMerchantVisitCoefficient(List<Long> merchantIds);
}