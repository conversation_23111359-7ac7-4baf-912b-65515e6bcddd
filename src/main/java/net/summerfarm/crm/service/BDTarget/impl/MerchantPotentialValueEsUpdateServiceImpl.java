package net.summerfarm.crm.service.BDTarget.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.es.mapper.XianmuMerchantCrmMapper;
import net.summerfarm.crm.service.BDTarget.MerchantPotentialValueCalculationService;
import net.summerfarm.crm.service.BDTarget.MerchantPotentialValueEsUpdateService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 门店潜力值ES更新服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Slf4j
@Service
public class MerchantPotentialValueEsUpdateServiceImpl implements MerchantPotentialValueEsUpdateService {

    @Resource
    private XianmuMerchantCrmMapper xianmuMerchantCrmMapper;
    
    @Resource
    private MerchantPotentialValueCalculationService merchantPotentialValueCalculationService;

    @Override
    public int batchUpdateMerchantPotentialValueToEs(Map<Long, BigDecimal> merchantPotentialMap) {
        if (CollectionUtils.isEmpty(merchantPotentialMap)) {
            log.warn("批量更新门店潜力值到ES：门店潜力值映射为空");
            return 0;
        }

        try {
            // 使用XianmuMerchantCrmMapper的分批批量更新方法（每50个一次）
            int successCount = xianmuMerchantCrmMapper.batchUpdatePotentialValue(merchantPotentialMap);
            
            log.info("批量更新门店潜力值到ES完成：总数量={}, 成功数量={}", 
                    merchantPotentialMap.size(), successCount);
            
            return successCount;
            
        } catch (Exception e) {
            log.error("批量更新门店潜力值到ES失败", e);
            return 0;
        }
    }
}