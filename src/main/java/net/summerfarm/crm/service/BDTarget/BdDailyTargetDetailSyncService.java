package net.summerfarm.crm.service.BDTarget;

import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailSync;

import java.util.List;

/**
 * 销售拜访目标指标同步服务接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface BdDailyTargetDetailSyncService {

    /**
     * 分页查询同步数据
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 同步数据列表
     */
    List<BdDailyTargetDetailSync> selectByPage(int offset, int limit);

    /**
     * 统计同步数据总数
     *
     * @return 总数
     */
    int countTotal();
}