package net.summerfarm.crm.service.BDTarget.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 销售每日目标导入Excel数据DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BdDailyTargetImportExcelDataDTO extends ImportExcelBaseDTO {

    /**
     * 销售姓名
     */
    @ExcelProperty("销售姓名")
    @NotBlank(message = "销售姓名不能为空")
    private String bdName;

    /**
     * 日期
     */
    @ExcelProperty("日期")
    @NotBlank(message = "日期不能为空")
    private String targetDateStr;

    /**
     * 目标类型
     */
    @ExcelProperty("目标类型")
    @NotBlank(message = "目标类型不能为空")
    private String targetTypeStr;

    /**
     * 品类
     */
    @ExcelProperty("品类")
    private String categoryName;

    /**
     * SKU商品名称
     */
    @ExcelProperty("SKU商品名称")
    private String sku;

    /**
     * SPU编码
     */
    @ExcelProperty("spuId")
    private String spu;

    /**
     * 业务类型
     */
    @ExcelProperty("业务类型")
    private String businessTypeStr;

    /**
     * 指标类型
     */
    @ExcelProperty("指标类型")
    private String indicatorTypeStr;

    /**
     * 目标值
     */
    @ExcelProperty("目标值")
    @NotNull(message = "目标值不能为空")
    private BigDecimal indicatorExpectedValue;

    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;

    /**
     * 优先级
     */
    @ExcelProperty("优先级")
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    // ========== 以下为内部处理字段 ==========

    /**
     * 销售ID
     */
    @ExcelIgnore
    private Integer bdId;

    /**
     * 目标日期
     */
    @ExcelIgnore
    private LocalDate targetDate;

    /**
     * 目标类型值
     */
    @ExcelIgnore
    private Integer targetType;

    /**
     * 业务类型值
     */
    @ExcelIgnore
    private Integer businessType;

    /**
     * 指标类型值
     */
    @ExcelIgnore
    private Integer indicatorType;

}
