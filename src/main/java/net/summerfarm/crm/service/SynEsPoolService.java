package net.summerfarm.crm.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.crm.mapper.manage.MerchantCluePoolMapper;
import net.summerfarm.crm.mapper.offline.CrmClueDeleteMapper;
import net.summerfarm.crm.mapper.offline.Hive2mysqlCrmShopListPoolMapper;
import net.summerfarm.crm.model.convert.CluePoolHivePoolConverter;
import net.summerfarm.crm.model.domain.CluePool;
import net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@Service
@Slf4j
public class SynEsPoolService {
    /**
     * for循环查询 es的数据
     */
    @Resource
    MerchantCluePoolMapper merchantCluePoolMapper;
    @Resource
    Hive2mysqlCrmShopListPoolMapper hive2mysqlCrmShopListPoolMapper;

    @Resource
    net.summerfarm.crm.common.es.EsClientPool esClientPool;
    @Resource
    CrmClueDeleteMapper crmClueDeleteMapper;

    private static final String index = "xianmu_gaoyan_info_new_v1";
    private static final String BIND = "1";
    private static final String UN_BIND = "0";


//
//    public void deleteESpool() {
//        pageDeleteCluePool();
//    }

    public void addOrUpdatePool() {
        addESpool();
    }

    /**
     * for循环1000条1000条去操作
     * 判断1000条的shopId是否存在
     * 不存在的直接新增
     * 存在的换取esId修改
     */
    @SneakyThrows
    public void addESpool() {
        int count = hive2mysqlCrmShopListPoolMapper.count();
        if (count == 0) {
            return;
        }
        int size = 1000;
        int l = count / size;
        int offset = 0;
        for (int i = 0; i <= l; i++) {
            List<Hive2mysqlCrmShopListPool> followUpRelations = hive2mysqlCrmShopListPoolMapper.selectPage(offset, size);
            offset = offset + size;
            if (CollectionUtil.isEmpty(followUpRelations)) {
                continue;
            }
            log.info("查询Hive2mysqlCrmShopListPool 第i次 {}", i);
            List<String> shopIds = followUpRelations.stream().map(Hive2mysqlCrmShopListPool::getShopid).filter(it->StringUtils.isNotEmpty(it)).collect(Collectors.toList());
            Map<String, String> stringMapMap = getesIdMap(shopIds);
            for (Hive2mysqlCrmShopListPool it : followUpRelations) {
                it.setEsId(stringMapMap.get(it.getShopid()));
            }
            //补充绑定关系
            margeManage(followUpRelations);

            //批量添加
            batchAddEsPool(followUpRelations);
        }
    }

    /**
     * 补充绑定关系
     *
     * @param followUpRelations
     */
    private void margeManage(List<Hive2mysqlCrmShopListPool> followUpRelations) {
        if (CollectionUtil.isEmpty(followUpRelations)) {
            return;
        }
        List<String> esIds = followUpRelations.stream().filter(it -> StringUtils.isNotEmpty(it.getEsId())).map(Hive2mysqlCrmShopListPool::getEsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(esIds)) {
            return;
        }
        List<String> exitEsids = merchantCluePoolMapper.getExitEsids(esIds);
        followUpRelations.forEach(
                it -> {
                    if (StringUtils.isNotEmpty(it.getStoreId())) {
                        it.setManage(BIND);
                    } else if (exitEsids.contains(it.getEsId())) {
                        it.setManage(BIND);
                    } else {
                        it.setManage(UN_BIND);
                    }
                }
        );
    }

    public void batchAddEsPool(List<Hive2mysqlCrmShopListPool> followUpRelations) throws IOException {
        log.info("batchAddEsPool 开始批量添加 i{}条", followUpRelations.size());
        if (CollectionUtil.isEmpty(followUpRelations)) {
            return;
        }
        RestHighLevelClient client = null;
        try {
            client = esClientPool.getClient();
            BulkRequest bulkRequest = new BulkRequest();
            for (Hive2mysqlCrmShopListPool followUpRelation : followUpRelations) {
                CluePool cluePool = CluePoolHivePoolConverter.toCluePool(followUpRelation);
                cluePool.initPoi();
                IndexRequest request = new IndexRequest().index(index)
                        .source(JSONObject.toJSONString(cluePool), XContentType.JSON).type("_doc");
                if (StringUtils.isNotEmpty(followUpRelation.getEsId())) {
                    request.id(followUpRelation.getEsId());
                }
                bulkRequest.add(request);
            }
            BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            bulkResponse.status();
            bulkResponse.getItems();
            if (bulkResponse.getItems() != null && bulkResponse.getItems().length > 0) {
                log.info("批量添加信息 {}", JSONObject.toJSONString(bulkResponse.getItems()[0]));
            }
        } catch (Exception e) {
            log.warn("批量添加线索池数据 error", e);
        } finally {
            esClientPool.returnClient(client);
        }

    }


    public Map<String /** shopId**/,/** esId **/String> getesIdMap(List<String> shopIds) throws Exception {

        if (CollectionUtil.isEmpty(shopIds)) {
            return new HashMap<>();
        }
        Map<String, String> map = new HashMap<>();
        RestHighLevelClient client = esClientPool.getClient();
        BoolQueryBuilder mustQuery = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("shopId", shopIds));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.from(0).size(1000);
        searchSourceBuilder.query(mustQuery);
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = response.getHits();
            for (SearchHit hit : hits) {
                String sourceAsString = hit.getSourceAsString();
                String id = hit.getId();
                CluePool curlPoolDO = JSONObject.parseObject(sourceAsString, CluePool.class);
                map.put(curlPoolDO.getShopid(), id);
            }
        } catch (IOException e) {
            log.warn("es搜索io异常", e);
        } finally {
            esClientPool.returnClient(client);
        }
        return map;
    }

    @SneakyThrows
    private void pageDeleteCluePool() {
        RestHighLevelClient client;
        try {
            client = esClientPool.getClient();
        } catch (Exception e) {
            throw new DefaultServiceException("es连接异常");
        }
        SearchRequest searchRequest = new SearchRequest("xianmu_gaoyan_info_new");
        //初始化scroll
        //值不需要足够长来处理所有数据—它只需要足够长来处理前一批结果。每个滚动请求(带有滚动参数)设置一个新的过期时间。
        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(3L));//设定滚动时间间隔
        searchRequest.scroll(scroll);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(1000);//设定每次返回多少条数据
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = null;
        SearchHit[] searchHits = null;
        try {
            searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            searchHits = searchResponse.getHits().getHits();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            esClientPool.returnClient(client);
        }

        if (searchHits != null && searchHits.length > 0) {
            batchAddEsPool(searchHits);
        }
        if (searchResponse==null){
            return;
        }
        String scrollId = searchResponse.getScrollId();

        //遍历搜索命中的数据，直到没有数据
        while (searchHits != null) {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            RestHighLevelClient queryClient = esClientPool.getClient();
            try {
                searchResponse = queryClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                SearchHits hits = searchResponse.getHits();
                if (hits!=null){
                    searchHits = hits.getHits();
                }
            } catch (Exception e) {
                log.warn("链接异常", e);
            } finally {
                esClientPool.returnClient(queryClient);
            }
            batchAddEsPool(searchHits);
            if (searchHits.length < 1000) {
                break;
            }

        }
        //清除滚屏
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        //也可以选择setScrollIds()将多个scrollId一起使用
        RestHighLevelClient client1 = esClientPool.getClient();
        try {
            client1.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.warn("io异常", e);
        } finally {
            esClientPool.returnClient(client1);
        }

    }

    @SneakyThrows
    public void batchAddEsPool(SearchHit[] searchHits) throws IOException {
        if (searchHits == null || searchHits.length == 0) {
            return;
        }
        List<String> esIds = Arrays.stream(searchHits).map(SearchHit::getId).collect(Collectors.toList());
        List<String> exitEsids = merchantCluePoolMapper.getExitEsids(esIds);
        if (CollectionUtils.isEmpty(exitEsids)) {
            return;
        }
        RestHighLevelClient client = esClientPool.getClient();
        List<SearchHit> collect = Arrays.stream(searchHits).filter(it -> exitEsids.contains(it.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        try {
            BulkRequest bulkRequest = new BulkRequest();
            for (SearchHit searchHit : collect) {
                JSONObject jsonObject = JSONObject.parseObject(searchHit.getSourceAsString());
                CluePool vo = JSONObject.parseObject(jsonObject.toJSONString(), CluePool.class);
                String phone = jsonObject.getString("phone_number");
                vo.setPhone(phone);
                vo.setId(searchHit.getId());
                vo.initPoi();
                vo.setCuisineType(vo.getType());
                IndexRequest request = new IndexRequest().index(index).id(vo.getId())
                        .source(JSONObject.toJSONString(vo), XContentType.JSON).type("_doc");
                bulkRequest.add(request);
            }
            BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            bulkResponse.status();
            bulkResponse.getItems();
        } catch (Exception e) {
            log.warn("批量添加线索池数据 error", e);
        } finally {
            esClientPool.returnClient(client);
        }

    }


    /**
     * 删除线索
     */
    @SneakyThrows
    public void deleteEsPool() {
        int count = crmClueDeleteMapper.count();
        if (count == 0) {
            return;
        }
        int pageIndex = 0;
        int pageSize = 1000;
        // 在这里编写获取shopIds的逻辑
        taskDoGeneric(pageIndex, pageSize,  this::queryDeleteShopList, this::deleteEs);
    }

    private List<String> queryDeleteShopList(Integer offset, Integer offSize){
         return crmClueDeleteMapper.queryDeleteShopList(offset*offSize, offSize);
    }

    /**
     *
     * @param pageIndex 分页参数
     * @param pageSize 分页size
     * @param function 分页函数
     * @param consumer 分页后执行函数
     * @param <T> 泛型
     */
    public <T> void taskDoGeneric(Integer pageIndex, Integer pageSize,
                                  BiFunction<Integer, Integer, List<T>> function,
                                  Consumer<List<T>> consumer) {
        while (true) {
            List<T> shopIds = function.apply(pageIndex , pageSize);
            if (CollectionUtils.isEmpty(shopIds)) {
                break;
            }
            consumer.accept(shopIds);
            if (shopIds.size() < pageSize) {
                break;
            }
            pageIndex++;
        }
    }

    @SneakyThrows
    private void deleteEs(List<String> shopIds)  {
        if (CollectionUtils.isEmpty(shopIds)) {
            return;
        }
        //查询shopId-esId的关联关系
        Map<String /** shopId**/,/** esId **/String> stringMapMap = getesIdMap(shopIds);
        List<String> esIds = new ArrayList<>(stringMapMap.values());
        if (CollectionUtils.isEmpty(esIds)){
            return;
        }
        //查询已经存在的esid
        List<String> exitEsids = merchantCluePoolMapper.getExitEsids(esIds);

        //过滤已经绑定的
        List<String> deleteEsIds = esIds.stream().filter(it -> !exitEsids.contains(it)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deleteEsIds)){
            return;
        }
        RestHighLevelClient client = esClientPool.getClient();
        deleteEsIds(deleteEsIds, client);

    }

    private void deleteEsIds(List<String> esIds, RestHighLevelClient client) {
        log.info("要删除的esId {}", JSONObject.toJSONString(esIds));
        BulkRequest request = new BulkRequest();
        try {
            for (String esId : esIds) {
                //批量删除数据
                DeleteRequest source = new DeleteRequest().index(index).id(esId);
                request.add(source);
            }
            BulkResponse response = client.bulk(request, RequestOptions.DEFAULT);
            log.info(JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("删除es数据错误 esIds{}", esIds);
        }

    }


}
