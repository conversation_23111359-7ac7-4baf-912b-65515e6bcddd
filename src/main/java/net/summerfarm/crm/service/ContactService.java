package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.dto.ContactDto;
import net.xianmu.common.result.CommonResult;

public interface ContactService {
    /**
     * 根据联系人 id获取联系人 db信息
     *
     * @param contactId 联系人id
     * @return 联系人信息
     */
    AjaxResult<ContactDto> getContactDto(Long contactId);

    /**
     * 获取联系人
     *
     * @param contactId 联系人id
     * @return {@link CommonResult}<{@link Contact}>
     */
    CommonResult<Contact> selectContact(Long contactId);
}
