package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.domain.MerchantCluePool;
import net.summerfarm.crm.model.dto.*;
import net.summerfarm.crm.model.query.*;
import net.summerfarm.crm.model.vo.*;
import net.xianmu.common.result.CommonResult;
import com.github.pagehelper.PageInfo;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface MerchantService {

    /**
     * crm 公、私海列表客户详情
     *
     * @param merchantDetailQuery 查询条件，mid必传，reassign为1是来源是公海
     * @return 客户详情
     */
    CommonResult<MerchantVO> selectDetail(MerchantDetailQuery merchantDetailQuery);

    /**
     * 客户管理-客户列表
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param merchantQuery 查询条件：mname（商户名）、grade（等级）、adminId（销售id）、areaNo、size（商户类型）、phone(客户电话)
     * @return 客户列表
     */
    AjaxResult queryMerchantRelease(int pageIndex, int pageSize, MerchantQuery merchantQuery);

    /**
     * 获取分配模板
     *
     * @return {@link CommonResult}<{@link String}>
     */
    CommonResult<String> getAssignTemplate();

    /**
     * 批量分配门店
     *
     * @param file 文件
     * @return {@link CommonResult}<{@link List}<{@link AssignBdResultVo}>>
     * @throws IOException ioexception
     */
    CommonResult<List<AssignBdResultVo>> batchAssign(String file);

    /**
     * 私海人数/私海容量 只计算单店客户
     * @return 私海人数/私海容量
     */
    AjaxResult queryMerchantWightLight();

    /**
     * 查询BD私海,主管查拥有城市权限的用户
     * @param type 类型
     * @param record 查询条件
     * @return 用户信息
     */
    AjaxResult queryBdPrivateSea(int type, MerchantVO record);

    /**
     * 店铺名称查询
     * @param leadId 位置?
     * @param mname 商户名
     * @return
     */
    AjaxResult existMerchantName(Long leadId, String mname);

    /**
     * 查询相似店铺
     * @param merchant 商户信息
     * @return 相似店铺
     */
    AjaxResult querySimilarMerchant(Merchant merchant);


    /**
     * 查询当日私海池注册客户
     * @param mname 商户名称
     * @return 客户信息
     */
    AjaxResult queryTodayRegister(String mname);

    /**
    * 查询线索池数据
     * @param merchant 用户信息
     * @return  线索池数据
    */
    AjaxResult queryCluePool(Merchant merchant);

    /**
     * 查询bd负责城市信息
     * @return 城市信息
     */
    AjaxResult queryBdArea();

    /**
     * 更新客户经营状态 crm-客户详情页-倒闭至正常经营
     * @param operateStatus 经营状态,0正常.1倒闭
     * @param mId 商户id
     * @return ok
     */
    AjaxResult updateOperatingState(Long mId, Integer operateStatus);

    /**
     * 获取商户基础数据信息
     * @param merchantDetailQuery 商户查询信息
     * @return 商户基础数据信息
     */
    CommonResult<MerchantVO> baseDetail(MerchantDetailQuery merchantDetailQuery);

    /**
     * 查询门店详情
     *
     * @param mId m id
     * @return {@link MerchantVO}
     */
    MerchantVO getMerchantVoByMid(Long mId);

    /**
     * 更新线索池信息
     */
    AjaxResult updateCurlPool(MerchantCluePool merchantCluePool, Integer manage, String oldEsId);

    /**
     * 查询商户列表信息
     * @param merchantQuery 查询条件
     * @return 商户列表
     */
    AjaxResult selectMerchantList(MerchantQuery merchantQuery);

    /**
     * 定时任务：发送待审核用户信息至对应区域主管
     */
    void sendMerchantReviewMessage();

    /**
     * 计算待配送订单客户总数
     * @param deliveryMerchantQuery 查询条件
     * @return 客户总数
     */
    CommonResult<DeliveryMerchantVO> deliveryMerchantCount(DeliveryMerchantQuery deliveryMerchantQuery);

    /**
     * 待配送订单客户gmv数据
     * @param deliveryMerchantQuery 查询条件
     * @return 待配送订单客户gmv数据列表
     */
    CommonResult<PageInfo<DeliveryMerchantVO>> deliveryMerchantList(DeliveryMerchantQuery deliveryMerchantQuery);

    /**
     * 商户下单sku列表
     * @param skuMerchantQuery 查询条件
     * @return 商户下单sku列表
     */
    CommonResult<PageInfo<CrmSkuMonthGmvVO>> skuGmvList(SkuMerchantQuery skuMerchantQuery);

    /**
     * 某sku商户下单gmv数据
     * @param skuMerchantQuery sku信息
     * @return
     */
    CommonResult<PageInfo<CrmSkuMerchantGmvVO>> skuMerchantList(SkuMerchantQuery skuMerchantQuery);

    /**
     * 查询商户商城搜索记录信息
     * @param merchantDetailQuery 查询信息
     * @return 商城搜索记录信息
     */
    CommonResult<CrmMerchantMallSearchTopVO> mallRecord(MerchantDetailQuery merchantDetailQuery);

    /**
     * 查询商户gmv信息
     * @param merchantDetailQuery 查询条件
     * @return gmv信息
     */
    CommonResult<CrmMerchantGmvVO> gmvInfo(MerchantDetailQuery merchantDetailQuery);

    /**
     * 商户常购商品记录
     * @param merchantDetailQuery 查询条件
     * @return 商品记录
     */
    CommonResult<PageInfo<CrmSkuMonthGmvVO>> merchantProduct(MerchantDetailQuery merchantDetailQuery);

    /**
     * 商户常购类目分析
     * @param merchantDetailQuery 查询条件
     * @return 常购类目分析
     */
    CommonResult<CustomerAnalysisVO> detailCategory(MerchantDetailQuery merchantDetailQuery);

    /**
     * 商户常购SPU分析
     * @param merchantDetailQuery 查询条件
     * @return SPU分析
     */
    CommonResult<CustomerAnalysisVO> detailSpu(MerchantDetailQuery merchantDetailQuery);

    /**
     * 获取区域sku下单gmv信息
     * @param skuMerchantQuery 查询条件
     * @return sku下单gmv信息
     */
    CommonResult<CrmSkuMonthGmvVO> skuGmvDetail(SkuMerchantQuery skuMerchantQuery);

    CommonResult careCustomer(CrmCareCustomerVO crmCareCustomerVO);

    CommonResult<KeyCustomerCountVO> keyCustomerCount(String province,String city,String area, Long bdId);

    CommonResult<Map<String, Object>> dangerCustomer(CrmKeyCustomerQuery keyCustomerQuery);

    CommonResult<Map<String, Object>> noOrderCustomer(CrmKeyCustomerQuery keyCustomerQuery);

    CommonResult<Map<String, Object>> firstOrderCustomer(CrmKeyCustomerQuery keyCustomerQuery);

    CommonResult<Map<String, Object>> queryCareCustomer(CrmKeyCustomerQuery keyCustomerQuery);

    CommonResult<KeyCustomerCountDTO> keyCustomerCountVO(AreaCodeBdIdQuery codeBdIdQuery);

    /**
     * 今日配送订单数据
     *
     * @param province 省
     * @param city     城市
     * @param area     区域
     * @return OrderDeliveryTodayDTO
     */
    CommonResult<OrderDeliveryTodayDTO> orderDeliveryToday(String province,String city,String area);

    /**
     * 今日配送订单列表
     * @param orderDeliveryQuery 查询条件
     * @return OrderDeliveryListDTO
     */
    CommonResult<OrderDeliveryRouteVO> orderDeliveryList(OrderDeliveryQuery orderDeliveryQuery);

    /**
     * 今日配送订单详情
     * @param orderDeliveryQuery 查询条件
     * @return OrderDeliveryDetailDTO
     */
    CommonResult<OrderDeliveryDetailDTO> orderDeliveryDetail(OrderDeliveryQuery orderDeliveryQuery);

    /**
     * 查询bd所属merchant标签
     *
     * @return {@link CommonResult}<{@link List}<{@link MerchantTagVO}>>
     */
    CommonResult<MerchantTagListVO> queryBDMerchantTag();


    /**
     * 查询客户详情
     *
     * @param mId m id
     * @return {@link CommonResult}<{@link MerchantVO}>
     */
    CommonResult<MerchantVO> selectMerchantDetail(Long mId);

    /**
     *
     * @param url
     * @return
     */
    List<AssignBdResultVo> batchClose(String url);


    /**
     * 客户的线索池详情
     * @param mId
     * @return
     */
    CommonResult<MerchantCluePool> selectMerchantPoolDetail(Long mId);


    /**
     * 查找近60天购买的spu
     */
    List<MerchantRecentSpuDTO> queryRecentSpu(Long mId);


    /**
     * 查找不可申请品类拓宽券的spu
     * 不可申请品类拓宽券满足一下两点:
     * 1. 30天内下单该spu
     * 2. 该spu不在活跃期内
     * 具体prd: <a href="https://summerfarm.feishu.cn/wiki/QeiewtcsviPW8fk6UWzcZRrQnLc">prd</a>
     */
    List<MerchantRecentSpuDTO> queryCannotApplyMscSpu(Long mId);

    /**
     * 查找近6个月下单但近30天未下单SPU
     */
    List<ShortTermLostSpuDTO> queryShortTermLostSpu(Long mId);

    /**
     * 查找已申请且还可重复申请品类拓宽券SPU
     */
    List<MscActiveSpuDTO> queryMscActiveSpu(Long mId);

    /**
     * 查询客户的行业属性
     *
     * @param mId
     * @return
     */
    MerchantBusinessVO queryMerchantBusiness(Long mId);

    /**
     * 更新客户的行业属性
     * 
     * @param upsertDTO
     * @return 是否更新成功
     */
    boolean upsertMerchantBusiness(MerchantBusinessUpsertDTO upsertDTO);
}
