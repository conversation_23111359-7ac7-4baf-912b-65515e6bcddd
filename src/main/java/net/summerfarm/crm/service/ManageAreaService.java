package net.summerfarm.crm.service;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.query.QueryManageAreaQuery;
import net.summerfarm.crm.model.query.SaveManageAreaQuery;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public interface ManageAreaService {

    /**
     * 区域配置列表
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param queryManageAreaQuery 查询条件
     * @return 区域配置列表
     */
    AjaxResult selectManageArea(int pageIndex, int pageSize, QueryManageAreaQuery queryManageAreaQuery);

    /**
     * 区域配置编辑
     * @param saveManageAreaQuery 插入信息
     * @return 执行成功状态
     */
    AjaxResult saveManageArea(SaveManageAreaQuery saveManageAreaQuery);

    /**
     * 区域配置删除
     * @param id id
     * @return ok
     */
    AjaxResult deleteManageArea(int id);

    /**
     * 模糊查询区域名称
     * @param zoneName 区域名称
     * @return 区域信息
     */
    AjaxResult queryZoneName(String zoneName);

    /**
     * 查询已存在运营区域
     * @return 城市名
     */
    AjaxResult queryExistArea();

    /**
     * 查询已存在城市
     * @return 城市名
     */
    AjaxResult queryExistCity();
}
