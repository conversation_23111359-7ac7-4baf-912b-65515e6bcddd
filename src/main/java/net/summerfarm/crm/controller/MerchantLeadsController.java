package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.MerchantLeads;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.vo.AreaVO;
import net.summerfarm.crm.model.vo.MerchantLeadsVO;
import net.summerfarm.crm.service.MerchantLeadsService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 地推码
 *
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@RestController
@RequestMapping(value = "/crm-service/merchant-leads")
public class MerchantLeadsController {

    @Resource
    private MerchantLeadsService merchantLeadsService;

    /**
     * 查询我的线索池
     *
     * @param pageIndex  页面索引
     * @param pageSize   页面大小
     * @param selectKeys 选择键
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"merchant-leads:select", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectMerchantLeads(@PathVariable int pageIndex, @PathVariable int pageSize, MerchantLeads selectKeys) {
        return merchantLeadsService.selectMerchantLeads(pageIndex, pageSize, selectKeys);
    }

    /**
     * 生成地推码
     *
     * @param merchantLeads 商人领导
     * @param bindingResult 绑定结果
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"merchant-leads:update", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult saveMerchantLeads(@Validated @RequestBody MerchantLeadsVO merchantLeads, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        return merchantLeadsService.saveMerchantLeads(merchantLeads);
    }


    /**
     * 获取pop商城可用运营区域
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"merchant-leads:update", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/queryPopAreaNo",  method = RequestMethod.GET)
    public CommonResult<LargeAreaDTO> queryPopAreaNo() {
        return merchantLeadsService.queryPopAreaNo();
    }

}
