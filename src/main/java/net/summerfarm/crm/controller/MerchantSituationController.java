package net.summerfarm.crm.controller;


import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.crm.model.query.MerchantSituationQuery;
import net.summerfarm.crm.model.vo.MerchantSituationVO;
import net.summerfarm.crm.service.MerchantSituationService;
import net.summerfarm.enums.RedissonLockKey;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/merchantSituation")
public class MerchantSituationController {

    @Resource
    private MerchantSituationService merchantSituationService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private BaseService baseService;

    @RequiresPermissions(value = {"merchantSituation:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insertMerchantSituation(@RequestBody @Validated(value = {Add.class}) MerchantSituationDTO merchantSituationDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        // 同一adminId并发申请控制
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.MERCHANT_SITUATION + baseService.getAdminId());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new DefaultServiceException("请重新进行客情申请!");
            }
            return merchantSituationService.insertMerchantSituation(merchantSituationDTO);
        } catch (InterruptedException e) {
            log.warn("锁获取异常:{}", e.getMessage(),e);
            throw new DefaultServiceException("请重新进行客情申请!");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    @RequiresPermissions(value = {"merchantSituation:examine", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/examine", method = RequestMethod.POST)
    public AjaxResult examine( MerchantSituationVO merchantSituationVO, Integer type) {
        return merchantSituationService.examineMerchantSituation(merchantSituationVO,type);
    }

    @RequiresPermissions(value = {"merchantSituation:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public AjaxResult selectOne(@PathVariable Long id) {
        return merchantSituationService.queryMerchantSituation(id);
    }

    /**
     * 查询客情列表
     *
     * @param pageIndex              页面索引
     * @param pageSize               页面大小
     * @param merchantSituationQuery 商户情况查询
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"merchantSituation:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult select(@PathVariable int pageIndex, @PathVariable int pageSize, MerchantSituationQuery merchantSituationQuery) {
        return merchantSituationService.queryMerchantSituationList(pageIndex,pageSize,merchantSituationQuery);
    }

    @RequestMapping(value = "/situationQuota", method = RequestMethod.GET)
    public AjaxResult merchantSituationQuota(){
        return merchantSituationService.merchantSituationQuota();
    }
}
