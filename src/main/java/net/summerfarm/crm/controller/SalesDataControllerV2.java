package net.summerfarm.crm.controller;

import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.model.convert.salesdata.DashboardModuleConverter;
import net.summerfarm.crm.model.convert.salesdata.SalesDataConverter;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.dto.OrderDeliveryTodayDTO;
import net.summerfarm.crm.model.query.OrderDeliveryQuery;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.query.salesdata.AreaPerformanceQueryDTO;
import net.summerfarm.crm.model.query.salesdata.TimeTypeQueryDTO;
import net.summerfarm.crm.model.vo.AdminInfoVo;
import net.summerfarm.crm.model.vo.SalesDataVo;
import net.summerfarm.crm.model.vo.saledata.*;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.MerchantService;
import net.summerfarm.crm.service.SalesDataService;
import net.summerfarm.crm.service.SalesDataServiceV2;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;

/**
 * 销售数据看板V2.
 * 组件模块化,可配置化. 返回的是模块化的数据, 由前端自行组装.
 */
@RestController
@RequestMapping(value = "/crm-service/salesdata")
public class SalesDataControllerV2 {

    @Resource
    private DashboardModuleConverter dashboardModuleConverter;
    @Resource
    private BdAreaConfigService bdAreaConfigService;
    @Resource
    private SalesDataService salesDataService;
    @Resource
    private SalesDataServiceV2 salesDataServiceV2;
    @Resource
    private MerchantService merchantService;

    // ----------------------------------------------------- CRM端 -----------------------------------------------------

    /**
     * 计划tab - 今日数据
     */
    @PostMapping(value = "/daily-data")
    public CommonResult<List<CRMDashboardModule>> dailyData() {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "个人数据信息暂未同步,请检查您的销售身份,并于明日再来查看");
        }

        // 普通销售看的是自己的数据
        if (topRankOrg.getRank() == BD) {
            CrmBdTodayHourGmv crmBdTodayHourGmv = salesDataServiceV2.fetchTodayDataForBd(topRankOrg);
            CrmBdDataVO data = SalesDataConverter.INSTANCE.CrmBdTodayHourGmvToCrmBdDataVO(crmBdTodayHourGmv);

            // 转换为看板模块
            List<CRMDashboardModule> modules = Stream.of(data)
                    .map(d -> dashboardModuleConverter.convertToCRMDashboardModule(
                            d,
                            ConfigValueEnum.CRM_BD_PERFORMANCE_MODULE_CONFIG,
                            String.valueOf(topRankOrg.getBdId()),
                            "今日数据"))
                    .collect(Collectors.toList());
            return CommonResult.ok(modules);
        }
        // 主管级(m1/m2/m3)看的是管辖的所有城市数据之和
        else {
            CrmCityTodayHourGmv crmCityTodayHourGmv = salesDataServiceV2.fetchTodayDataForManager(topRankOrg);
            CrmCityDataVO data = SalesDataConverter.INSTANCE.CrmCityTodayHourGmvToCrmCityDataVO(crmCityTodayHourGmv);

            // 转换为看板模块
            List<CRMDashboardModule> modules = Stream.of(data)
                    .map(d -> dashboardModuleConverter.convertToCRMDashboardModule(
                            d,
                            ConfigValueEnum.CRM_CITY_PERFORMANCE_MODULE_CONFIG,
                            String.valueOf(topRankOrg.getBdId()),
                            "今日数据"))
                    .collect(Collectors.toList());
            return CommonResult.ok(modules);
        }
    }

    /**
     * 计划tab - 今日配送订单
     */
    @PostMapping(value = "/daily-order-delivery")
    public CommonResult<List<CRMDashboardModule>> dailyPlanDelivery(@RequestBody OrderDeliveryQuery qry) {
        CommonResult<OrderDeliveryTodayDTO> orderDeliveryTodayDTOCommonResult = merchantService
                .orderDeliveryToday(qry.getProvince(), qry.getCity(), qry.getArea());
        if (!ResultStatusEnum.OK.getStatus().equals(orderDeliveryTodayDTOCommonResult.getStatus())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, orderDeliveryTodayDTOCommonResult.getMsg());
        }

        // 转换为看板模块
        OrderDeliveryTodayDTO data = orderDeliveryTodayDTOCommonResult.getData();
        List<CRMDashboardModule> modules = dashboardModuleConverter
                .convertToCRMDashboardModuleList(data, ConfigValueEnum.DAILY_ORDER_DELIVERY_DASHBOARD_CONFIG);

        return CommonResult.ok(modules);
    }


    /**
     * "本月销售数据"tab的看板
     * 为BD展示本月的销售数据
     */
    @PostMapping(value = "/crm/monthly-overview")
    public CommonResult<List<CRMDashboardModule>> bdMonthlyOverview() {
        // 查询bd数据
        CommonResult<AdminInfoVo> adminInfoVoCommonResult = salesDataService.selectBdData(null); // 该方法会查询当前登录bd
        if (!ResultStatusEnum.OK.getStatus().equals(adminInfoVoCommonResult.getStatus())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, adminInfoVoCommonResult.getMsg());
        }

        // 转换为看板模块
        AdminInfoVo data = adminInfoVoCommonResult.getData();
        List<CRMDashboardModule> modules = dashboardModuleConverter
                .convertToCRMDashboardModuleList(data, ConfigValueEnum.CRM_BD_DASHBOARD_CONFIG);
        return CommonResult.ok(modules);
    }

    /**
     * 为主管级展示每个区域的业绩
     * 每个区域对应一个Module
     */
    @PostMapping(value = "/crm/performance-per-area")
    public CommonResult<List<CRMDashboardModule>> areaPerformance(@RequestBody @Validated TimeTypeQueryDTO queryDTO) {
        if (Objects.equals("day", queryDTO.getTimeType())) {
            List<CrmAreaDataVO> dataList = salesDataServiceV2.fetchPerformancePerAreaForManager("day");

            List<CRMDashboardModule> modules = dataList.stream()
                    .map(data -> dashboardModuleConverter.convertToCRMDashboardModule(
                            data,
                            ConfigValueEnum.CRM_CITY_PERFORMANCE_MODULE_CONFIG,
                            String.valueOf(data.getSalesAreaId()),
                            data.getSalesAreaName()))
                    .collect(Collectors.toList());

            return CommonResult.ok(modules);
        } else {
            // TODO 月度数据暂时获取旧表数据
            List<SalesAreaDataVo> salesAreaDataVos = salesDataService.salesAreaGmv(1);

            // 转换为看板模块, 设置模块标题为区域名
            List<CRMDashboardModule> modules = salesAreaDataVos.stream()
                    .map(data -> dashboardModuleConverter.convertToCRMDashboardModule(
                            data,
                            ConfigValueEnum.CRM_PERFORMANCE_MODULE_CONFIG,
                            String.valueOf(data.getSalesAreaId()),
                            data.getSalesAreaName()))
                    .collect(Collectors.toList());

            return CommonResult.ok(modules);
        }
    }


    /**
     * 为主管级展示每个城市的业绩
     * 每个城市对应一个Module
     */
    @PostMapping(value = "/crm/performance-per-city")
    public CommonResult<List<CRMDashboardModule>> cityPerformance(@RequestBody @Validated AreaPerformanceQueryDTO queryDTO) {
        if (Objects.equals("day", queryDTO.getTimeType())) {
            List<CrmCityDataVO> dataList = salesDataServiceV2.fetchPerformancePerCityInArea(queryDTO.getSalesAreaId(), "day");

            List<CRMDashboardModule> modules = dataList.stream()
                    .map(data -> dashboardModuleConverter.convertToCRMDashboardModule(
                            data,
                            ConfigValueEnum.CRM_CITY_PERFORMANCE_MODULE_CONFIG,
                            data.getCity(),
                            data.getCity()))
                    .collect(Collectors.toList());

            return CommonResult.ok(modules);
        } else {
            // TODO 月度数据暂时获取旧表数据
            List<SalesAreaDataVo> salesAreaDataVos = salesDataService.cityMonthGmv(queryDTO.getSalesAreaId());

            List<CRMDashboardModule> modules = salesAreaDataVos.stream()
                    .map(data -> dashboardModuleConverter.convertToCRMDashboardModule(
                            data,
                            ConfigValueEnum.CRM_PERFORMANCE_MODULE_CONFIG,
                            data.getCity(),
                            data.getCity()))
                    .collect(Collectors.toList());

            return CommonResult.ok(modules);
        }
    }


    /**
     * 为主管级展示每个销售的业绩
     * 每个销售对应一个Module
     */
    @PostMapping(value = "/crm/performance-per-bd")
    public CommonResult<List<CRMDashboardModule>> bdPerformance(@RequestBody @Validated AreaPerformanceQueryDTO queryDTO) {
        if (Objects.equals("day", queryDTO.getTimeType())) {
            List<CrmBdDataVO> bdDataList = salesDataServiceV2.fetchPerformancePerBdInArea(queryDTO.getSalesAreaId(), "day");

            // 转换为看板模块, 设置模块标题为bd名字
            List<CRMDashboardModule> modules = bdDataList.stream()
                    .map(data -> dashboardModuleConverter.convertToCRMDashboardModule(
                            data,
                            ConfigValueEnum.CRM_BD_PERFORMANCE_MODULE_CONFIG,
                            String.valueOf(data.getBdId()),
                            data.getBdName()))
                    .collect(Collectors.toList());

            return CommonResult.ok(modules);
        }
        // TODO 月度数据暂时获取旧表数据
        else {
            List<CrmBdTodayDayGmv> crmBdTodayDayGMVs = salesDataService.bdMonthGmv(queryDTO.getSalesAreaId());
            List<CrmBdDataVO> bdDataList = crmBdTodayDayGMVs.stream()
                    .map(data -> {
                        CrmBdDataVO vo = new CrmBdDataVO();
                        vo.setBdId(Long.valueOf(data.getAdminId()));
                        vo.setBdName(data.getAdminName());
                        vo.setRealTotalGmv(data.getTotalGmv());
                        vo.setDlvRealTotalGmv(data.getDeliveryGmv());
                        vo.setCustCnt(Long.valueOf(data.getOrderMerchant()));
                        vo.setFruitRealTotalGmv(data.getFruitGmv());
                        vo.setDairyRealTotalGmv(data.getDairyGmv());
                        vo.setOtherRealTotalGmv(data.getNonDairyGmv());
                        vo.setCategoriesRealTotalGmv(data.getAgentGmv());
                        vo.setAnchorRealTotalGmv(data.getRewardGmv());
                        vo.setOrdinaryNum(Long.valueOf(data.getVisitNum()));

                        return vo;
                    }).collect(Collectors.toList());

            List<CRMDashboardModule> modules = bdDataList.stream()
                    .map(data -> dashboardModuleConverter.convertToCRMDashboardModule(
                            data,
                            ConfigValueEnum.CRM_PERFORMANCE_MODULE_CONFIG_V2,
                            String.valueOf(data.getBdId()),
                            data.getBdName()))
                    .collect(Collectors.toList());

            return CommonResult.ok(modules);
        }
    }

    // ---------------------------------------------------- manage端 ----------------------------------------------------

    /**
     * Manage端城市维度看板.
     * 用于manage后台销售数据管理页面的看板
     */
    @PostMapping(value = "/manage/dashboard")
    @RequiresPermissions(value = {"crm-saledata:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<List<BdManagementDashboardItem>> selectGmvByCity(@RequestBody SalesDataQuery salesDataQuery) {
        // 查询数据
        CommonResult<SalesDataVo> salesDataVoCommonResult = salesDataService.selectGmvByCity(salesDataQuery);
        if (!ResultStatusEnum.OK.getStatus().equals(salesDataVoCommonResult.getStatus())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, salesDataVoCommonResult.getMsg());
        }

        // 转换为看板模块
        SalesDataVo data = salesDataVoCommonResult.getData();
        List<BdManagementDashboardItem> items = dashboardModuleConverter
                .convertToBdManagementDashboardItem(data, ConfigValueEnum.MANAGE_DB_MANAGEMENT_CITY_DASHBOARD_CONFIG);
        return CommonResult.ok(items);
    }
}
