package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.model.dto.CrmFollowDTO;
import net.summerfarm.crm.model.query.ClueDetailQuery;
import net.summerfarm.crm.model.vo.CreateCrmFollowVO;
import net.summerfarm.crm.service.impl.SaasClueFollowServiceImpl;
import net.xianmu.authentication.controller.AuthBaseController;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * crm-saas-follow
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/crm-service/follow-up")
public class CrmSaasFollowController extends AuthBaseController {
    @Resource
    SaasClueFollowServiceImpl saasClueFollowService;
    /**
     * 跟进记录列表
     * @param cluClueQuery
     * @return
     */
    @RequestMapping(value = "/follow/query", method = RequestMethod.POST)
    public CommonResult<PageInfo<CrmFollowDTO>> queryFollow(@RequestBody ClueDetailQuery cluClueQuery) {
        return saasClueFollowService.queryFollow(cluClueQuery);
    }

    /**
     * 创建跟进记录
     * @param crmFollowVO
     * @return
     */
    @RequestMapping(value = "/follow/create", method = RequestMethod.POST)
    public CommonResult<CrmFollowDTO> create(@RequestBody @Validated  CreateCrmFollowVO crmFollowVO) {
        return saasClueFollowService.create(crmFollowVO, getUserBase());
    }
}
