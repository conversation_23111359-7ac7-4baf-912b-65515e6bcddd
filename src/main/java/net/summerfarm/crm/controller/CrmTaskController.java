package net.summerfarm.crm.controller;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.URLUtil;
import cn.hutool.log.Log;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.CrmTask;
import net.summerfarm.crm.model.dto.task.TimingTaskDTO;
import net.summerfarm.crm.model.query.task.TaskDetailInsertInput;
import net.summerfarm.crm.model.query.task.TaskDetailQuery;
import net.summerfarm.crm.model.query.task.TaskInsertInput;
import net.summerfarm.crm.model.query.task.TaskListQuery;
import net.summerfarm.crm.model.vo.task.*;
import net.summerfarm.crm.service.CrmTaskService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * 任务中心
 *
 * <AUTHOR>
 * @date 2023/9/28 14:29
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/crm-task")
public class CrmTaskController {
    @Resource
    CrmTaskService taskService;

    /**
     * 任务列表
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<TaskListVo>> taskList(@RequestBody TaskListQuery query) {
        return CommonResult.ok(taskService.taskList(query));
    }

    /**
     * 后台任务详情
     */
    @PostMapping("/query/task-info")
    public CommonResult<CrmTask> taskInfo(@RequestBody TaskDetailInsertInput query) {
        return CommonResult.ok(taskService.taskInfo(query.getTaskId()));
    }

    /**
     * 任务详情
     */
    @PostMapping("/query/task-detail")
    public CommonResult<PageInfo<TaskDetailVo>> taskDetail(@RequestBody TaskDetailQuery query) {
        return taskService.taskDetail(query);
    }


    /**
     * 新增任务
     */
    @PostMapping("/update/insert-task")
    @RequiresPermissions(value = {"crm-task:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<Integer> taskInsert(@Valid @RequestBody TaskInsertInput input) {
        return taskService.taskInsert(input);
    }

    /**
     * 新增客户
     */
    @PostMapping("/update/insert-detail")
    @RequiresPermissions(value = {"crm-task:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<TaskImportResultCountVo> taskDetailInsert(@Valid @RequestBody TaskDetailInsertInput input) {
        return taskService.taskDetailInsert(input);
    }

    /**
     * 任务明细导出
     */
    @PostMapping("/query/detail-export")
    public CommonResult<Void> taskDetailExport(@RequestBody TaskDetailQuery query) {
        return taskService.taskDetailExport(query.getTaskId());
    }

    /**
     * 生效中的省心送任务个数
     */
    @PostMapping("/query/timing-task-count")
    public CommonResult<TimingTaskCountVo> timingTaskCount() {
        return taskService.timingTaskCount();
    }

    /**
     * 已完成/未完成任务个数
     */
    @PostMapping("/query/task-count")
    public CommonResult<TaskCountVo> taskCount(@RequestBody TaskDetailInsertInput query) {
        return taskService.taskCount(query.getTaskId());
    }

    /**
     * 省心送订单下载
     */
    @GetMapping("/query/timing-task-download")
    public void timingTaskDownload(@DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate day,String code, HttpServletResponse response) {
        List<TimingTaskVo> timingOrders = taskService.timingTaskDownload(day,code);
        response.setContentType("application/vnd.ms-excel; charset=utf-8");
        response.setCharacterEncoding("utf-8");
        try {
            String fileName = URLUtil.encode(day + "到期省心送退款订单" + UUID.randomUUID()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream()).head(TimingTaskVo.class).sheet().doWrite(timingOrders);
        } catch (Exception e) {
            log.info("下载省心送退款订单失败:{}", e.getMessage(), e);
        }
    }
}
