package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.CrmBdTodayDayGmv;
import net.summerfarm.crm.model.domain.CrmCityTodayGmv;
import net.summerfarm.crm.model.query.SalesDataQuery;
import net.summerfarm.crm.model.query.TeamDataQuery;
import net.summerfarm.crm.model.query.salesdata.NewCustomerQuery;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.model.query.salesdata.CityGmvQuery;
import net.summerfarm.crm.model.vo.saledata.NewCustomerVo;
import net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo;
import net.summerfarm.crm.service.SalesDataService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 销售数据
 *
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@RestController
@RequestMapping(value = "/crm-service/salesdata")
public class SalesDataController {
    @Resource
    SalesDataService salesDataService;

    /**
     * 区域维度gmv总览
     */
    @RequiresPermissions(value = {"crm-saledata:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectGmvByZoneName", method = RequestMethod.GET)
    public AjaxResult selectGmvByZoneName(SalesDataQuery salesDataQuery) {
        return salesDataService.selectGmvByZoneName(salesDataQuery);
    }

    /**
     * 城市维度gmv总览
     */
    @RequiresPermissions(value = {"crm-saledata:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/selectGmvByCity")
    public CommonResult<SalesDataVo> selectGmvByCity(@RequestBody SalesDataQuery salesDataQuery) {
        return salesDataService.selectGmvByCity(salesDataQuery);
    }

    /**
     * 区域维度查询bd 业绩
     */
    @RequiresPermissions(value = {"crm-saledata:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectBdGmvByZoneName/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectBdGmvByZoneName(@PathVariable int pageIndex, @PathVariable int pageSize, SalesDataQuery salesDataQuery) {
        return salesDataService.selectBdGmvByZoneName(pageIndex, pageSize, salesDataQuery);
    }

    /**
     * 城市维度查询bd 业绩
     */
    @RequiresPermissions(value = {"crm-saledata:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/selectBdGmvByCity")
    public CommonResult<PageInfo<AdminInfoVo>> selectBdGmvByCity(@RequestBody SalesDataQuery salesDataQuery) {
        return salesDataService.selectBdGmvByCity(salesDataQuery);
    }

    /**
     * 城市维度查询bd 客户数据
     */
    @RequiresPermissions(value = {"crm-saledata:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/selectBdDataByCity")
    public CommonResult<PageInfo<AdminInfoVo>> selectBdDataByCity(@RequestBody SalesDataQuery salesDataQuery) {
        return salesDataService.selectBdDataByCity(salesDataQuery);
    }

    /**
     * 区域维度查询bd 客户数据
     */
    @RequiresPermissions(value = {"crm-saledata:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectBdDataByZoneName/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectBdDataByZoneName(@PathVariable int pageIndex, @PathVariable int pageSize, SalesDataQuery salesDataQuery) {
        return salesDataService.selectBdDataByZoneName(pageIndex, pageSize, salesDataQuery);
    }

    /**
     * bd今日gmv数据
     */
    @PostMapping("/gmv-today")
    public CommonResult<CrmBdTodayDayGmv> todayGmv() {
        return salesDataService.todayGmv();
    }

    /**
     * 城市当日 gmv 数据
     */
    @PostMapping("/city-gmv-today/city")
    public CommonResult<List<CrmCityTodayGmv>> cityTodayGmv(@RequestBody CityGmvQuery gmvQuery) {
        return CommonResult.ok(salesDataService.cityTodayGmv(gmvQuery.getSalesAreaId()));
    }

    /**
     * bd 当日 gmv
     */
    @PostMapping("/city-gmv-today/bd")
    public CommonResult<List<CrmBdTodayDayGmv>> bdTodayGmv(@RequestBody CityGmvQuery gmvQuery) {
        return CommonResult.ok(salesDataService.bdTodayGmv(gmvQuery.getSalesAreaId()));
    }

    /**
     * m2 视角当日 gmv 数据
     */
    @PostMapping("/area-gmv")
    public CommonResult<List<SalesAreaDataVo>> areaTodayGmv(@RequestBody CityGmvQuery gmvQuery) {
        return CommonResult.ok(salesDataService.salesAreaGmv(gmvQuery.getType()));
    }

    /**
     * 城市当月 gmv 数据
     */
    @PostMapping("/city-gmv-month/city")
    public CommonResult<List<SalesAreaDataVo>> cityMonthGmv(@RequestBody CityGmvQuery gmvQuery) {
        return CommonResult.ok(salesDataService.cityMonthGmv(gmvQuery.getSalesAreaId()));
    }

    /**
     * bd 当月 gmv
     */
    @PostMapping("/city-gmv-month/bd")
    public CommonResult<List<CrmBdTodayDayGmv>> bdMonthGmv(@RequestBody CityGmvQuery gmvQuery) {
        return CommonResult.ok(salesDataService.bdMonthGmv(gmvQuery.getSalesAreaId()));
    }

    /**
     * 当月拉新
     */
    @PostMapping("/new-customer")
    public CommonResult<List<NewCustomerVo>> bdMonthGmv(@RequestBody NewCustomerQuery query) {
        return CommonResult.ok(salesDataService.newCustomer(query));
    }

    /**
     * crm小程序:数据看板,销售gmv数据
     *
     * @param adminId bdId
     * @return 销售gmv数据
     */
    @RequestMapping(value = "/selectBdData", method = RequestMethod.GET)
    public CommonResult<AdminInfoVo> selectBdData(Integer adminId) {
        return salesDataService.selectBdData(adminId);
    }

    @RequestMapping(value = "/sendMail", method = RequestMethod.GET)
    public void sendMail() {
        salesDataService.sendMail();
    }

    @RequestMapping(value = "/selectBDByArea", method = RequestMethod.GET)
    public AjaxResult selectBDByArea(String bdName) {
        return salesDataService.selectBDByArea(bdName);
    }

    /**
     * 销售团队数据
     *
     */
    @RequiresPermissions(value = {"crm-saledata:teamData", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/salesTeamData/{pageIndex}/{pageSize}")
    public AjaxResult salesTeamData(@PathVariable int pageIndex, @PathVariable int pageSize, @RequestBody TeamDataQuery teamDataQuery) {
        return salesDataService.salesTeamData(pageIndex, pageSize, teamDataQuery);
    }

    @RequestMapping(value = "/orderData", method = RequestMethod.GET)
    public AjaxResult orderData(TeamDataQuery teamDataQuery) {
        return salesDataService.orderData(teamDataQuery);
    }

    @RequiresPermissions(value = {"crm-saledata:teamData", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/orderDetails", method = RequestMethod.GET)
    public AjaxResult orderDetails(TeamDataQuery teamDataQuery) {
        return salesDataService.orderDetails(teamDataQuery);
    }

    /**
     * 销售月数据
     *
     * @param adminId bdId
     * @return 销售月数据
     */
    @RequestMapping(value = "/selectBdMerchantData", method = RequestMethod.GET)
    public CommonResult<AdminInfoVo> selectBdMerchantData(Integer adminId) {
        return salesDataService.selectBdMerchantData(adminId);
    }

    @RequestMapping(value = "/selectChangeMerchant", method = RequestMethod.GET)
    public AjaxResult selectChangeMerchant(Integer adminId) {
        return salesDataService.selectChangeMerchant(adminId);
    }

    @RequestMapping(value = "/selectSkuCategory/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectSkuCategory(@PathVariable int pageIndex, @PathVariable int pageSize, String name) {
        return salesDataService.selectSkuCategory(pageIndex, pageSize, name);
    }

    /**
     * 指定 天数内 业绩明细
     *
     * @param days           天
     * @param salesDataQuery 销售数据查询
     * @return {@link AjaxResult}
     */
    @RequestMapping(value = "/selectGmvBeforeDays/{days}", method = RequestMethod.GET)
    public AjaxResult selectGmvBeforeDays(@PathVariable int days, SalesDataQuery salesDataQuery) {
        return salesDataService.selectGmvBeforeDays(days, salesDataQuery);
    }

    @GetMapping("/selectCityGmvBeforeDays/{days}")
    public AjaxResult selectCityGmvBeforeDays(@PathVariable int days, SalesDataQuery salesDataQuery) {
        return salesDataService.selectCityGmvBeforeDays(days, salesDataQuery);
    }

    @GetMapping("/merchantLevelDistribution")
    public AjaxResult merchantLevelDistribution() {
        return salesDataService.merchantLevelDistribution();
    }

    @GetMapping("/merchantLevelDistributionInfo")
    public AjaxResult merchantLevelDistributionInfo(String proportion) {
        return salesDataService.merchantLevelDistributionInfo(proportion);
    }

    /**
     * 收益排行榜
     * @param type 排行榜类型 0:收入;1:拉新;2:拜访
     */
    @PostMapping("/rankingList/{type}")
    public CommonResult<List<RankingListVO>> incomeRankingList(@PathVariable(value = "type") Integer type) {
        return salesDataService.rankingList(type);
    }

    /**
     * 登录用户默认行政城市
     */
    @PostMapping("/default-administrative-city")
    public CommonResult<AdministrativeCityVo> defaultAdministrativeCity() {
        return salesDataService.defaultAdministrativeCity();
    }

    /**
     * 省市区维度查询gmv信息
     */
    @PostMapping("/query/selectGmvByCityDistrict")
    public CommonResult<CityDistrictGmvVo> selectGmvByCityDistrict(@RequestBody SalesDataQuery salesDataQuery) {
        return salesDataService.selectGmvByCityDistrict(salesDataQuery);
    }
}