package net.summerfarm.crm.controller.bdTarget.assembler;

import net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfig;
import net.summerfarm.crm.model.vo.objectiveManagement.BdTargetIndicatorConfigVO;
import org.springframework.util.CollectionUtils;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 销售目标组装器
 * 负责处理销售目标相关的对象转换逻辑
 *
 * <AUTHOR>
 */
public abstract class BdTargetAssembler {

    /**
     * 将BdTargetIndicatorConfig列表转换为BdTargetIndicatorConfigVO列表
     *
     * @param configList 销售目标指标配置列表
     * @return 销售目标指标配置VO列表
     */
    public static List<BdTargetIndicatorConfigVO> convertToBdTargetIndicatorConfigVOList(List<BdTargetIndicatorConfig> configList) {
        if (CollectionUtils.isEmpty(configList)) {
            return Collections.emptyList();
        }

        return configList.stream().map(BdTargetAssembler::convertToBdTargetIndicatorConfigVO).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 将BdTargetIndicatorConfig转换为BdTargetIndicatorConfigVO
     *
     * @param config 销售目标指标配置
     * @return 销售目标指标配置VO
     */
    public static BdTargetIndicatorConfigVO convertToBdTargetIndicatorConfigVO(BdTargetIndicatorConfig config) {
        if (config == null) {
            return null;
        }

        BdTargetIndicatorConfigVO configVO = new BdTargetIndicatorConfigVO();
        configVO.setTargetType(config.getTargetType());
        configVO.setTargetName(config.getTargetName());
        configVO.setBusinessTypeConfig(config.getBusinessTypeConfig());
        configVO.setIndicatorTypeConfig(config.getIndicatorTypeConfig());
        configVO.setCategoryNameConfig(config.getCategoryNameConfig());
        configVO.setSkuConfig(config.getSkuConfig());
        configVO.setSpuConfig(config.getSpuConfig());
        configVO.setUnitConfig(config.getUnitConfig());

        return configVO;
    }
}
