package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.model.domain.MerchantKeyPerson;
import net.summerfarm.crm.service.MerchantKeyPersonService;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25 13:47
 */
@RestController
@RequestMapping("/crm-service/merchant-key-person")
public class MerchantKeyPersonController {

    @Resource
    private MerchantKeyPersonService merchantKeyPersonService;

    @GetMapping("/select")
    public AjaxResult selectKeyPerson(Long mId) {
        return merchantKeyPersonService.selectKeyPerson(mId);
    }

    @PostMapping()
    public AjaxResult updateKeyPerson(@Validated MerchantKeyPerson merchantKeyPerson, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getErrorWithMsg("kp信息不全");
        }
        return merchantKeyPersonService.updateKeyPerson(merchantKeyPerson);
    }
}
