package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.CrmCommissionCategory;
import net.summerfarm.crm.model.domain.CrmCommissionCoreMerchant;
import net.summerfarm.crm.model.domain.CrmCommissionMerchantLevel;
import net.summerfarm.crm.model.query.*;
import net.summerfarm.crm.model.vo.CrmBdConfigVo;
import net.summerfarm.crm.model.vo.CrmCommissionMerchantVo;
import net.summerfarm.crm.model.vo.CrmCommissionSkuVo;
import net.summerfarm.crm.service.CommissionService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 佣金管理
 *
 * <AUTHOR>
 * @Description 佣金管理controller
 * @date 2022/6/14 2:00
 */
@RestController
@RequestMapping(value = "/crm-service/commission")
public class CommissionController {

    @Resource
    CommissionService commissionService;

    @RequiresPermissions(value = {"crm-commission:select", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectAreaBd", method = RequestMethod.GET)
    public AjaxResult selectAreaBd(){
        return commissionService.selectAreaBd();
    }

    @RequiresPermissions(value = {"crm-newcitybd:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveConfig", method = RequestMethod.POST)
    public AjaxResult saveAreaBd(@RequestBody Config config){
        return commissionService.saveAreaBd(config);
    }

    @RequiresPermissions(value = {"crm-commission:select", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectAreasku/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectRewardSku(@PathVariable int pageIndex, @PathVariable int pageSize, CommissionRewardSkuQuery commissionRewardSkuQuery){
        return commissionService.selectRewardSku(pageIndex,pageSize,commissionRewardSkuQuery);
    }

    @RequiresPermissions(value = {"crm-areasku:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteAreasku", method = RequestMethod.DELETE)
    public AjaxResult deleteRewardSku(int id){
        return commissionService.deleteRewardSku(id);
    }

    @RequiresPermissions(value = {"crm-areasku:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveAreaSku", method = RequestMethod.POST)
    public AjaxResult saveRewardSku(@RequestBody CrmCommissionSkuVo crmCommissionSkuVo){
        return commissionService.saveRewardSku(crmCommissionSkuVo);
    }

    @RequiresPermissions(value = {"crm-areasku:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/copyAreaSku", method = RequestMethod.POST)
    public AjaxResult copyRewardSku(@RequestBody CopyInfoQuery copyInfoQuery){
        return commissionService.copyRewardSku(copyInfoQuery);
    }

    @RequiresPermissions(value = {"crm-commission:select", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectMerchant/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectPullNewMerchantReward(@PathVariable int pageIndex,@PathVariable int pageSize,String zoneName) {
        return commissionService.selectPullNewMerchantReward(pageIndex,pageSize,zoneName);
    }

    @RequiresPermissions(value = {"crm-newmerchant:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteMerchant", method = RequestMethod.DELETE)
    public AjaxResult deletePullNewMerchantReward(int id) {
        return commissionService.deletePullNewMerchantReward(id);
    }

    @RequiresPermissions(value = {"crm-newmerchant:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveMerchant", method = RequestMethod.POST)
    public AjaxResult savePullNewMerchantReward(@RequestBody CrmCommissionMerchantVo crmCommissionMerchantVo) {
        return commissionService.savePullNewMerchantReward(crmCommissionMerchantVo);
    }

    @RequiresPermissions(value = {"crm-newmerchant:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/copyMerchant", method = RequestMethod.POST)
    public AjaxResult copyPullNewMerchantReward(@RequestBody CopyInfoQuery copyInfoQuery) {
        return commissionService.copyPullNewMerchantReward(copyInfoQuery);
    }

    @RequiresPermissions(value = {"crm-newmerchant:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/batchModifyMerchant", method = RequestMethod.POST)
    public AjaxResult batchModifyPullNewMerchantReward(@RequestBody BatchModifyMerchantQuery batchModifyMerchantQuery) {
        return commissionService.batchModifyPullNewMerchantReward(batchModifyMerchantQuery);
    }

    /**
     * 查询激励指标
     *
     * @param pageIndex                页面索引
     * @param pageSize                 页面大小
     * @param commissionRewardSkuQuery 佣金奖励sku查询
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"crm-commission:select", CrmGlobalConstant.SA},logical = Logical.OR)
    @PostMapping(value = "/selectIncentiveIndex/{pageIndex}/{pageSize}")
    public AjaxResult selectIncentiveIndex(@PathVariable int pageIndex, @PathVariable int pageSize,@RequestBody CommissionIncentiveIndexQuery commissionRewardSkuQuery) {
        return commissionService.selectIncentiveIndex(pageIndex,pageSize,commissionRewardSkuQuery);
    }

    /**
     * 删除激励指标
     *
     * @param id id
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"crm-incentiveindex:delete", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteIncentiveIndex", method = RequestMethod.DELETE)
    public AjaxResult deleteIncentiveIndex(int id) {
        return commissionService.deleteIncentiveIndex(id);
    }

    /**
     * 保存激励指数
     *
     * @param crmBdConfigVo CRM bd配置vo
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"crm-incentiveindex:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveIncentiveIndex", method = RequestMethod.POST)
    public AjaxResult saveIncentiveIndex(@RequestBody CrmBdConfigVo crmBdConfigVo) {
        return commissionService.saveIncentiveIndex(crmBdConfigVo);
    }

    /**
     * 复制激励指数
     *
     * @param copyIntInfoQuery 拷贝int信息查询
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"crm-incentiveindex:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/copyIncentiveIndex", method = RequestMethod.POST)
    public AjaxResult copyIncentiveIndex(@RequestBody CopyIntInfoQuery copyIntInfoQuery) {
        return commissionService.copyIncentiveIndex(copyIntInfoQuery);
    }

    @RequiresPermissions(value = {"crm-incentiveindex:edit", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/batchModifyIncentiveIndex", method = RequestMethod.POST)
    public AjaxResult batchModifyIncentiveIndex(@RequestBody BatchModifyIncentiveIndexQuery batchModifyIncentiveIndexQuery) {
        return commissionService.batchModifyIncentiveIndex(batchModifyIncentiveIndexQuery);
    }


    @GetMapping("/queryBdName")
    public AjaxResult queryBdName(Boolean isExist,String bdName) {
        return commissionService.queryBdName(isExist,bdName);
    }

    @GetMapping("/selectNumLastMonth")
    public AjaxResult selectNumLastMonth(Integer adminId){
        return commissionService.selectNumLastMonth(adminId);
    }

    @GetMapping(value = "/selectGmvTarget")
    public AjaxResult selectGmvTarget(){
        return commissionService.selectGmvTarget();
    }

    @PostMapping(value = "/saveGmvTarget")
    public AjaxResult saveGmvTarget(@RequestBody Config config){
        return commissionService.saveGmvTarget(config);
    }

    @GetMapping(value = "/selectCategoryAward")
    public AjaxResult selectCategoryAward(){
        return commissionService.selectCategoryAward();
    }

    @PostMapping(value = "/saveCategoryAward")
    public AjaxResult saveCategoryAward(@RequestBody CrmCommissionCategory crmCommissionCategory){
        return commissionService.saveCategoryAward(crmCommissionCategory);
    }

    @GetMapping(value = "/selectCoreMerchant/{merchantLevelType}")
    public AjaxResult selectCoreMerchant(@PathVariable int merchantLevelType,String grade){
        return commissionService.selectCoreMerchant(merchantLevelType,grade);
    }

    @PostMapping(value = "/saveCoreMerchant")
    public AjaxResult saveCoreMerchant(@RequestBody CrmCommissionMerchantLevel crmCommissionMerchantLevel){
        return commissionService.saveCoreMerchant(crmCommissionMerchantLevel);
    }

    @GetMapping(value = "/selectCoreMerchantsNetGrowth")
    public AjaxResult selectCoreMerchantsNetGrowth(){
        return commissionService.selectCoreMerchantsNetGrowth();
    }

    @PostMapping(value = "/saveCoreMerchantsNetGrowth")
    public AjaxResult saveCoreMerchantsNetGrowth(@RequestBody CrmCommissionCoreMerchant crmCommissionCoreMerchant){
        return commissionService.saveCoreMerchantsNetGrowth(crmCommissionCoreMerchant);
    }

    @GetMapping(value = "/selectMonthLivingCouponQuota")
    public AjaxResult selectMonthLivingCouponQuota(){
        return commissionService.selectMonthLivingCouponQuota();
    }

    @PostMapping(value = "/updateMonthLivingCouponQuota/{monthLivingCouponQuota}")
    public AjaxResult updateMonthLivingCouponQuota(@PathVariable String monthLivingCouponQuota){
        return commissionService.updateMonthLivingCouponQuota(monthLivingCouponQuota);
    }
}
