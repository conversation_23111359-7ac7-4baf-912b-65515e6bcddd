package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.CrmCouponExpensePool;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpenseAdminDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolExtDto;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolLogDto;
import net.summerfarm.crm.model.query.crmCouponExpensePool.CrmCouponExpensePoolHaveQuery;
import net.summerfarm.crm.model.query.crmCouponExpensePool.CrmCouponExpensePoolQuery;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolDivideVo;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolExtVo;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolVo;
import net.summerfarm.crm.service.CrmCouponExpensePoolService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import java.util.List;

import static net.xianmu.authentication.common.utils.AuthUserUtils.getUserBase;

/**
 * <AUTHOR>
 * @Description
 * @date
 */
@RestController
@RequestMapping(value = "/crm-service/coupon-expense")
public class CrmCouponExpensePoolController {

    @Resource
    CrmCouponExpensePoolService crmCouponExpensePoolService;

    /**
     * 费用池管理
     *
     * @param cluClueQuery
     * @return
     */
    @RequestMapping(value = "/pool/query", method = RequestMethod.POST)
    @RequiresPermissions(value = {"crmCoupon:expensePool:query"})
    public CommonResult<PageInfo<CrmCouponExpensePoolDto>> queryPool(@RequestBody CrmCouponExpensePoolQuery cluClueQuery) {
        return crmCouponExpensePoolService.queryPool(cluClueQuery, getUserBase());
    }
    /**
     * 费用池详情
     *
     * @param cluClueQuery
     * @return
     */
    @RequestMapping(value = "/pool/query/detail", method = RequestMethod.POST)
    public CommonResult<CrmCouponExpensePoolDto> queryPoolDetail(@RequestBody CrmCouponExpensePoolQuery cluClueQuery) {
        return crmCouponExpensePoolService.queryPoolDetail(cluClueQuery, getUserBase());
    }

    /**
     * 费用池范围
     *
     * @param cluClueQuery
     * @return
     */
    @RequestMapping(value = "/pool/query/ext", method = RequestMethod.POST)
    public CommonResult<List<CrmCouponExpensePoolExtDto>> queryPoolExt(@RequestBody CrmCouponExpensePoolQuery cluClueQuery) {
        return crmCouponExpensePoolService.queryPoolExt(cluClueQuery, getUserBase());
    }
    /**
     * 新增余额池
     *
     * @param crmCouponExpensePoolVo
     * @return
     */
    @RequestMapping(value = "/pool/save", method = RequestMethod.POST)
    @RequiresPermissions(value = {"crmCoupon:expensePool:save"})
    public CommonResult<CrmCouponExpensePool> save(@RequestBody CrmCouponExpensePoolVo crmCouponExpensePoolVo) {
        return crmCouponExpensePoolService.save(crmCouponExpensePoolVo, getUserBase());
    }

    /**
     * 修改余额池
     *
     * @param crmCouponExpensePoolVo
     * @return
     */
    @RequestMapping(value = "/pool/upsert", method = RequestMethod.POST)
    @RequiresPermissions(value = {"crmCoupon:expensePool:update"})
    public CommonResult<CrmCouponExpensePool> update(@RequestBody CrmCouponExpensePoolVo crmCouponExpensePoolVo) {
        return crmCouponExpensePoolService.update(crmCouponExpensePoolVo, getUserBase());
    }

    /**
     * 余额池日志
     *
     * @param cluClueQuery
     * @return
     */
    @RequestMapping(value = "/pool/query/log", method = RequestMethod.POST)
    public CommonResult<PageInfo<CrmCouponExpensePoolLogDto>> queryPoolLog(@RequestBody CrmCouponExpensePoolQuery cluClueQuery) {
        return crmCouponExpensePoolService.queryPoolLog(cluClueQuery, getUserBase());
    }
    /**
     * 用户余额变动记录
     *
     * @param cluClueQuery
     * @return
     */
    @RequestMapping(value = "/pool/query/admin-record", method = RequestMethod.POST)
    public CommonResult<PageInfo<CrmCouponExpensePoolLogDto>> queryAdminPoolLog(@RequestBody CrmCouponExpensePoolQuery cluClueQuery) {
        return crmCouponExpensePoolService.queryAdminPoolLog(cluClueQuery, getUserBase());
    }

    /**
     * 新增商品
     * @param vo
     * @return
     */
    @RequestMapping(value = "/pool/add/ext", method = RequestMethod.POST)
    public CommonResult<Boolean> addPoolExt(@RequestBody CrmCouponExpensePoolExtVo vo) {
        return crmCouponExpensePoolService.addPoolExt(getUserBase(),vo);
    }
    /**
     * 删除扩展信息
     * @param vo
     * @return
     */
    @RequestMapping(value = "/pool/delete/ext", method = RequestMethod.POST)
    public CommonResult<Boolean> deletePoolExt(@RequestBody CrmCouponExpensePoolExtVo vo) {
        return crmCouponExpensePoolService.deletePoolExt(getUserBase(),vo);
    }


    /**
     * 划分
     * @param divideVo
     * @return
     */
    @RequestMapping(value = "/pool/upsert/divide", method = RequestMethod.POST)
    public CommonResult<Boolean> divide(@RequestBody @Validated CrmCouponExpensePoolDivideVo divideVo) {
        return crmCouponExpensePoolService.divide(getUserBase(), divideVo);
    }

    /**
     * admin 额度详情
     * @return
     */
    @RequestMapping(value = "/admin/query", method = RequestMethod.POST)
    public CommonResult<PageInfo<CrmCouponExpenseAdminDto>> adminPool(@RequestBody CrmCouponExpensePoolQuery cluClueQuery) {
        return crmCouponExpensePoolService.adminPool(getUserBase(), cluClueQuery);
    }


}

