package net.summerfarm.crm.controller;/**
 * <AUTHOR>
 * @date 2023/1/5 15:09
 */

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.dto.OrderDeliveryDetailDTO;
import net.summerfarm.crm.model.query.OrderDeliveryQuery;
import net.summerfarm.crm.model.query.ProductQuery;
import net.summerfarm.crm.model.query.WarehouseQuery;
import net.summerfarm.crm.model.vo.ProductsVO;
import net.summerfarm.crm.model.vo.WarehouseBatchProveRecordVO;
import net.summerfarm.crm.model.vo.WarehouseStorageVO;
import net.summerfarm.crm.service.ProductsService;
import net.summerfarm.crm.service.WarehouseService;
import net.summerfarm.manage.client.wms.ProductsProvider;
import net.summerfarm.manage.client.wms.WarehouseProvider;
import net.summerfarm.manage.client.wms.dto.req.WarehouseQueryReq;
import net.summerfarm.manage.client.wms.dto.res.ProductsDTO;
import net.summerfarm.manage.client.wms.dto.res.WarehouseBatchProveRecordDTO;
import net.summerfarm.manage.client.wms.dto.res.WarehouseStorageDTO;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * WMS
 *
 * <AUTHOR>
 * @date 2023/1/5 15:09
 */

@RestController
@RequestMapping(value = "/crm-service")
public class WMSController {
    @Resource
    WarehouseService warehouseService;
    @Resource
    ProductsService productsService;

    /**
     * 商品名模糊查询id
     *
     * @return
     */
    @PostMapping("/products/search")
    public CommonResult<List<ProductsVO>> searchProduct(@RequestBody ProductQuery qry) {
        return CommonResult.ok(productsService.search(qry.getPdName()));
    }

    /**
     * 库存列表
     *
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param param     查询条件
     * @return 仓库库存信息
     **/
    @PostMapping("/batch-prove/list/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"batch-prove:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<WarehouseStorageVO>> inventoryList(@PathVariable("pageIndex") Integer pageIndex, @PathVariable("pageSize") Integer pageSize, @RequestBody WarehouseQuery param) {
        WarehouseQueryReq req = new WarehouseQueryReq();
        return CommonResult.ok(warehouseService.inventoryList(pageIndex, pageSize, param.toWarehouseQueryReq(param)));
    }

    /**
     * 证明信息
     *
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param param     查询条件
     * @return
     **/
    @PostMapping("/prove/list/{pageIndex}/{pageSize}")
    @RequiresPermissions(value = {"batch-prove:prove-list", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<WarehouseBatchProveRecordVO>> proveList(@PathVariable Integer pageIndex, @PathVariable Integer pageSize, @RequestBody WarehouseQuery param) {
        if (Objects.isNull(param.getWarehouseNo()) || StringUtils.isEmpty(param.getSku())) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "仓库编号和sku不能为空");
        }
        WarehouseQueryReq req = new WarehouseQueryReq();
        return CommonResult.ok(warehouseService.proveList(pageIndex, pageSize, param.toWarehouseQueryReq(param)));
    }

}
