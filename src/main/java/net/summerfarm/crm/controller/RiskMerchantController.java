package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.query.riskMerchant.AuditRiskMerchantQuery;
import net.summerfarm.crm.model.query.riskMerchant.RiskMerchantDetailQuery;
import net.summerfarm.crm.model.query.riskMerchant.RiskMerchantQuery;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantDetailVO;
import net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantListVO;
import net.summerfarm.crm.service.RiskMerchantService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 门店风控
 *
 * <AUTHOR>
 * @date 2023/11/17 17:46
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/riskMerchant")
public class RiskMerchantController {
    @Resource
    private RiskMerchantService riskMerchantService;

    /**
     * 风控门店列表
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link PageInfo}<{@link RiskMerchantListVO}>>
     */
    @PostMapping("/query/list")
    @RequiresPermissions(value = {"risk-merchant:select", CrmGlobalConstant.SA},logical = Logical.OR)
    public CommonResult<PageInfo<RiskMerchantListVO>> list(@RequestBody RiskMerchantQuery query) {
        return CommonResult.ok(riskMerchantService.list(query));
    }

    /**
     * 风控门店详情
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link PageInfo}<{@link RiskMerchantListVO}>>
     */
    @RequiresPermissions(value = {"risk-merchant:select", CrmGlobalConstant.SA},logical = Logical.OR)
    @PostMapping("/query/risk-merchant-detail")
    public CommonResult<RiskMerchantDetailVO> riskMerchantDetail(@RequestBody RiskMerchantDetailQuery query) {
        return riskMerchantService.riskMerchantDetail(query);
    }

    /**
     * 审核风控门店
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link PageInfo}<{@link RiskMerchantListVO}>>
     */
    @RequiresPermissions(value = {"risk-merchant:audit", CrmGlobalConstant.SA},logical = Logical.OR)
    @PostMapping("/query/audit-risk-merchant")
    public CommonResult<Void> auditRiskMerchant(@RequestBody AuditRiskMerchantQuery query) {
        return riskMerchantService.auditRiskMerchant(query);
    }
}
