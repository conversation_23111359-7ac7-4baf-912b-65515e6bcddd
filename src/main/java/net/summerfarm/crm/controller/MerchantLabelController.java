package net.summerfarm.crm.controller;

import net.summerfarm.crm.model.input.label.NewYearLabelInput;
import net.summerfarm.crm.model.input.label.SchoolLabelInput;
import net.summerfarm.crm.service.MerchantLabelService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户标签
 */
@RestController
@RequestMapping("/crm-service/merchant-label")
public class MerchantLabelController {

    @Resource
    private MerchantLabelService merchantLabelService;

    /**
     * 新增/更新学校标签
     */
    @PostMapping("/upsert/school")
    public CommonResult<Void> upsertSchool(@RequestBody @Validated SchoolLabelInput input) {
        merchantLabelService.upsertSchool(input);
        return CommonResult.ok();
    }

    /**
     * 新增/更新新年标签
     */
    @PostMapping("/upsert/new-year")
    public CommonResult<Void> upsertNewYear(@RequestBody @Validated NewYearLabelInput input) {
        merchantLabelService.upsertNewYear(input);
        return CommonResult.ok();
    }
}
