package net.summerfarm.crm.controller.qiyu;


import com.alibaba.fastjson.JSON;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用来接收七鱼的各类回调消息： https://qiyukf.com/docs/guide/server/7-%E5%91%BC%E5%8F%AB%E7%B3%BB%E7%BB%9F.html#%E8%B0%83%E7%94%A8%E8%AF%B4%E6%98%8E 目前主要是外呼业务结束后回调。
 */
@RequestMapping("/crm-service/qiyu")
@RestController
public class QiyuIntegrationController {

    private static final HttpClient HTTP_CLIENT = HttpClients.createDefault();

    @Autowired
    private HttpServletRequest request;

    private static final Logger log = LoggerFactory.getLogger(QiyuIntegrationController.class);

    @PostMapping("/callFinished")
    public QiyuResponseVO<String> callFinished(@RequestBody String jsonStr) {
        log.info("七鱼的回调, header:{}, body:{}", getHeadersAsMap(request), jsonStr);
        sendToSlsLogStore(jsonStr);
        tryParseAndDownloadRecordInfo(jsonStr);
        return QiyuResponseVO.success();
    }

    private static final String SLS_LOG_STORE_URL = "https://xianmu-front-end-log.cn-hangzhou.log.aliyuncs.com/logstores/qiyu-callback/track?APIVersion=0.6.0";


    private static void sendToSlsLogStore(String jsonStr) {
        // PUT jsonStr to URL:https://xianmu-front-end-log.cn-hangzhou.log.aliyuncs.com/logstores/qiyu-callback/track?APIVersion=0.6.0
        try {
            HttpClient httpClient = HttpClientBuilder.create().build();
            HttpGet httpGet = new HttpGet(SLS_LOG_STORE_URL + String.format("&qiyuCallbackBody=%s", URLEncoder.encode(jsonStr, "UTF-8")));
            // Send the request
            HttpResponse response = httpClient.execute(httpGet);
            if (200 != response.getStatusLine().getStatusCode()) {
                log.error("发送七鱼回调到SLS失败了:{}, {}", response.getStatusLine(), jsonStr);
            }
            String slsResponse = EntityUtils.toString(response.getEntity());
            log.info("SLS日志返回结果:{}", slsResponse);
        } catch (Exception e) {
            log.error("发送七鱼回调到SLS失败了:{}", jsonStr, e);
        }
    }

    private static Map<String, String> getHeadersAsMap(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();

        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
        }

        return headers;
    }

    private void tryParseAndDownloadRecordInfo(String jsonStr) {
        try {
            QiyuCallFinishCallbackVO callFinishCallbackVO = JSON.parseObject(jsonStr, QiyuCallFinishCallbackVO.class);
            log.info("尝试下载录音文件:{}, sessionId:{}, callFrom:{}", callFinishCallbackVO.getRecordurl(), callFinishCallbackVO.getSessionid(),
                callFinishCallbackVO.getStaffname());
        } catch (Exception e) {
            log.error("解析JSON异常:{}", jsonStr, e);
        }
    }
}
