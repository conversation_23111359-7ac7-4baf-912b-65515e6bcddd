package net.summerfarm.crm.controller.qiyu;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@Accessors(chain = true)
public class QiyuResponseVO<T> {

    /**
     * code 200 表示成功
     */
    private int code;

    /**
     * 可以为空
     */
    private String message;

    /**
     * 只有在少数几个场景下才需要返回JSON数据给到七鱼. 参考： https://qiyukf.com/docs/guide/server/7-呼叫系统.html#第三方接口需返回的数据的说明
     */
    private T result;

    public static <Result> QiyuResponseVO<Result> success() {
        return new QiyuResponseVO<Result>().setCode(200).setMessage(StringUtils.EMPTY).setResult(null);
    }

    public static <Result> QiyuResponseVO<Result> success(Result resultObject, String message) {
        return new QiyuResponseVO<Result>().setCode(200).setMessage(message).setResult(resultObject);
    }
}
