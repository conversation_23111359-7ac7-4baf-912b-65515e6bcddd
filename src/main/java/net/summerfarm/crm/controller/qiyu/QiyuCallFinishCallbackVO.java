package net.summerfarm.crm.controller.qiyu;

import lombok.Data;

@Data
public class QiyuCallFinishCallbackVO {

    /*
    * JSON格式：
    * {
    "eventtype": 5,
    "sessionid": 216629286,  //通话记录ID
    "direction": "呼入",     //方向，呼入或呼出
    "createtime": "2022-04-22 16:41:15",  //通话创建时间
    "endtime": "2022-04-22 16:41:16",   //通话结束时间
    "connectionbeginetime": "2022-04-22 16:41:16",   //接通时间
    "connectionendtime": "2022-04-22 16:41:16",   //挂断时间
    "from": "15854582215",  //主叫号码
    "to": "05718690766",   //被叫号码
    "user": "客户名称",
    "category": "售后问题/退货", //咨询分类
    "staffid": 642656,   //接待客服ID
    "staffname": "丽娜",  //接待客服名称
    "status": "接通", //会话状态
    "visittimes": 1, //重复咨询次数
    "duration": "10:15", //通话时长
    "evaluation": "满意", //满意度评价
    "recordurl": "https://ysf.nosdn.127.net/9f670ff01dae290ad4bf83401d291069.wav", //通话录音文件地址
    "overflowFrom": "溢出来源",
    "shuntGroupName"："分流客服组",
    "ivrPath":"ivr路径",
    "mobileArea":"号码归属地",
    "waitDuration":"5分10秒",  //排队等待时长
    "ringDuration":"1小时10分",   //振铃时长
    "sessionIdFrom": 216629286,   //转接至该会话的上一通会话id
    "firstEndDirection":"用户",  //挂机方，客服、用户、--
  }
    * */

    /**
     * 通话记录ID
     */
    private long sessionid;

    /**
     * 方向，呼入或呼出
     */
    private String direction;

    /**
     * 通话创建时间
     */
    private String createtime;

    /**
     * 通话结束时间
     */
    private String endtime;

    /**
     * 接通时间
     */
    private String connectionbeginetime;

    /**
     * 挂断时间
     */
    private String connectionendtime;

    /**
     * 主叫号码
     */
    private String from;

    /**
     * 被叫号码
     */
    private String to;

    /**
     * 客户名称
     */
    private String user;

    /**
     * 咨询分类
     */
    private String category;

    /**
     * 接待客服ID
     */
    private long staffid;

    /**
     * 接待客服名称
     */
    private String staffname;

    /**
     * 会话状态
     */
    private String status;

    /**
     * 重复咨询次数
     */
    private int visittimes;

    /**
     * 通话时长
     */
    private String duration;

    /**
     * 满意度评价
     */
    private String evaluation;

    /**
     * 通话录音文件地址
     */
    private String recordurl;

    /**
     * 溢出来源
     */
    private String overflowFrom;

    /**
     * 分流客服组
     */
    private String shuntGroupName;

    /**
     * ivr路径
     */
    private String ivrPath;

    /**
     * 号码归属地
     */
    private String mobileArea;

    /**
     * 排队等待时长
     */
    private String waitDuration;

    /**
     * 振铃时长
     */
    private String ringDuration;

    /**
     * 转接至该会话的上一通会话id
     */
    private long sessionIdFrom;

    /**
     * 挂机方，客服、用户、--
     */
    private String firstEndDirection;

}
