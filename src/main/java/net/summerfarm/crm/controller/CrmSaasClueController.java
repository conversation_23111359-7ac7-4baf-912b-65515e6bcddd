package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;

import net.summerfarm.crm.model.dto.*;
import net.summerfarm.crm.model.query.ClueDetailQuery;
import net.summerfarm.crm.model.query.SaasClueQuery;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.service.SaasClueService;
import net.xianmu.authentication.controller.AuthBaseController;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * crm-saas-clue
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/crm-service/clue")
public class CrmSaasClueController extends AuthBaseController {
    @Resource
    SaasClueService saasClueService;

    /**
     * 线索列表
     *
     * @param cluClueQuery
     * @return
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public CommonResult<PageInfo<CrmClueDTO>> query(@RequestBody SaasClueQuery cluClueQuery) {
        return saasClueService.query(cluClueQuery, getUserBase());
    }

    /**
     * 新增线索
     *
     * @param saasClueClueVO
     * @return
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public CommonResult<CrmClueDTO> insert(@RequestBody SaasClueClueVO saasClueClueVO) {
        return saasClueService.insert(saasClueClueVO, getUserBase());
    }

    /**
     * 修改线索
     *
     * @param cluClueVO
     * @return
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<CrmClueDTO> update(@RequestBody SaasClueClueVO cluClueVO) {
        return saasClueService.update(cluClueVO, getUserBase());
    }

    /**
     * 线索详情 done
     *
     * @param clueDetailQuery
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public CommonResult<CrmClueDTO> detail(@RequestBody ClueDetailQuery clueDetailQuery) {
        return saasClueService.detail(clueDetailQuery);
    }

    /**
     * 绑定记录
     *
     * @param crmFollowVO
     * @return
     */
    @RequestMapping(value = "/update/bind", method = RequestMethod.POST)
    public CommonResult<Boolean> bind(@RequestBody  @Validated CrmClueBindVO crmFollowVO) {
        return saasClueService.bind(crmFollowVO, getUserBase());
    }

    /**
     * 根据mid查询绑定的信息
     *
     * @param midVO
     * @return
     */
    @RequestMapping(value = "/query/bid", method = RequestMethod.POST)
    public CommonResult<Boolean> queryBybId(@RequestBody MidVO midVO) {
        return saasClueService.queryBybId(midVO, getUserBase());
    }


}
