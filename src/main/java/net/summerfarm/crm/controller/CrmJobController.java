package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.dto.crmjob.CreateJobDTO;
import net.summerfarm.crm.model.dto.crmjob.JobIdListDTO;
import net.summerfarm.crm.model.dto.crmjob.UpdateJobDTO;
import net.summerfarm.crm.model.query.crmjob.CrmJobMerchantListQueryDTO;
import net.summerfarm.crm.model.query.crmjob.CrmJobQueryDTO;
import net.summerfarm.crm.model.query.crmjob.JobProductQueryDTO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobMerchantVO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobVO;
import net.summerfarm.crm.model.vo.crmjob.JobProductVO;
import net.summerfarm.crm.model.vo.task.TaskImportResultCountVo;
import net.summerfarm.crm.service.crmjob.CrmJobQueryService;
import net.summerfarm.crm.service.crmjob.CrmJobService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 任务中心V2
 * 替换任务中心V1对应的CrmTaskController
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/crm-job")
public class CrmJobController {

    @Resource
    private CrmJobService crmJobService;
    @Resource
    private CrmJobQueryService crmJobQueryService;

    /**
     * 创建任务
     *
     * @return 任务创建结果, 包括成功数量,失败数量及错误文件resourceId
     */
    @PostMapping("/upsert/create-job")
    @RequiresPermissions(value = {"crm-task:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<TaskImportResultCountVo> createJob(@RequestBody @Validated CreateJobDTO createJobDTO) {
        return CommonResult.ok(crmJobService.createJob(createJobDTO));
    }

    /**
     * 更新任务
     *
     * @return 任务创建结果, 包括成功数量,失败数量及错误文件resourceId
     */
    @PostMapping("/upsert/update-job")
    @RequiresPermissions(value = {"crm-task:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<TaskImportResultCountVo> updateJob(@RequestBody @Validated UpdateJobDTO updateJobDTO) {
        return CommonResult.ok(crmJobService.updateJob(updateJobDTO));
    }

    /**
     * 批量关闭任务
     */
    @PostMapping("/upsert/batch-cancel-job")
    @RequiresPermissions(value = {"crm-task:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<Void> batchCancelJob(@RequestBody JobIdListDTO jobIdListDTO) {
        crmJobService.batchCancelJobs(jobIdListDTO.getJobIdList());
        return CommonResult.ok();
    }

    /**
     * 任务列表
     * 列出所有任务.一般为manage端使用
     * 不传入时间参数时,默认查询近3个月的任务
     */
    @PostMapping("/query/list-all")
    @RequiresPermissions(value = {"crm-task:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<CrmJobVO>> jobList(@RequestBody CrmJobQueryDTO crmJobQueryDTO) {
        return CommonResult.ok(crmJobQueryService.queryCrmJobList(crmJobQueryDTO));
    }

    /**
     * 查询当前登录销售的任务列表
     * m1及以上销售查看自己及下属销售的任务
     * 不传入时间参数时,默认查询近3个月的任务
     */
    @PostMapping("/query/list-for-bd")
    public CommonResult<PageInfo<CrmJobVO>> jobListForBD(@RequestBody CrmJobQueryDTO crmJobQueryDTO) {
        return CommonResult.ok(crmJobQueryService.queryCrmJobListForBd(crmJobQueryDTO));
    }

    /**
     * 任务对应的门店详情列表
     * 列出所有门店详情.一般为manage端使用
     */
    @PostMapping("/query/detail/merchant-list-all")
    @RequiresPermissions(value = {"crm-task:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<PageInfo<CrmJobMerchantVO>> merchantList(@RequestBody @Validated CrmJobMerchantListQueryDTO queryDTO) {
        return CommonResult.ok(crmJobQueryService.queryMerchantList(queryDTO));
    }

    /**
     * 查询当前登录销售的任务对应的门店详情列表
     * 普通销售仅能查看自己私海的门店列表
     * m1及以上销售查看自己及下属销售私海+负责城市公海的门店列表
     */
    @PostMapping("/query/detail/merchant-list-for-bd")
    public CommonResult<PageInfo<CrmJobMerchantVO>> merchantListForBD(@RequestBody @Validated CrmJobMerchantListQueryDTO queryDTO) {
        return CommonResult.ok(crmJobQueryService.queryMerchantListForBd(queryDTO));
    }

    /**
     * 任务对应的品类列表.
     * area_no为空时,不返回价格信息.
     */
    @PostMapping("/query/list-job-product")
    @Deprecated
    public CommonResult<List<JobProductVO>> listJobProduct(@RequestBody @Validated JobProductQueryDTO queryDTO) {
        return CommonResult.ok(crmJobQueryService.queryJobProduct(queryDTO));
    }

    /**
     * 分页查询任务对应的品类列表.
     */
    @PostMapping("/query/page-job-product")
    public CommonResult<PageInfo<JobProductVO>> pageJobProduct(@RequestBody @Validated JobProductQueryDTO queryDTO) {
        return CommonResult.ok(crmJobQueryService.pageJobProduct(queryDTO));
    }
}
