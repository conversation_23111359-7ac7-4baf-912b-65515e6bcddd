package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.dto.keycustomer.KeyCustomer;
import net.summerfarm.crm.model.dto.keycustomer.KeyCustomerCount;
import net.summerfarm.crm.model.query.keycustomer.KeyCustomerCountQuery;
import net.summerfarm.crm.model.query.keycustomer.KeyCustomerQuery;
import net.summerfarm.crm.service.KeyCustomerService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 重点客户
 */
@RestController
@RequestMapping("/crm-service/key-customer-v2")
@Slf4j
public class KeyCustomerController {

    @Resource
    private KeyCustomerService keyCustomerService;

    /**
     * 重点客户个数
     */
    @RequestMapping("/count")
    public CommonResult<KeyCustomerCount> count(@RequestBody @Validated KeyCustomerCountQuery query) {
        return CommonResult.ok(keyCustomerService.countKeyCustomer(query));
    }

    /**
     * 重点客户列表
     */
    @RequestMapping("/page")
    public CommonResult<PageInfo<KeyCustomer>> page(@RequestBody @Validated KeyCustomerQuery query) {
        return CommonResult.ok(keyCustomerService.pageKeyCustomer(query));
    }
}
