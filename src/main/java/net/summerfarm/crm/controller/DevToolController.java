package net.summerfarm.crm.controller;

import net.summerfarm.crm.client.input.AdminTurningConfigInput;
import net.summerfarm.crm.facade.MerchantSituationFacade;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.crm.model.vo.UpdateAdminTurnVO;
import net.summerfarm.crm.service.impl.FollowUpRelationServiceImpl;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping(value = "/crm-service/devtool")
public class DevToolController {
    //devtool/
    @Resource
    FollowUpRelationServiceImpl followUpRelationService;
    @Resource
    private MerchantSituationFacade merchantSituationFacade;

    @RequestMapping(value = "/updateAdminTurn", method = RequestMethod.POST)
    public CommonResult updateAdminTurn(@RequestBody UpdateAdminTurnVO adminTurnVO) {
        List<AdminTurningConfigInput> inputList = adminTurnVO.getInputList();
        for (AdminTurningConfigInput adminTurningConfigInput : inputList) {
            followUpRelationService.updateAdminTurning(adminTurningConfigInput.getAdminId(),
                    adminTurningConfigInput.getBdId());
        }
        return CommonResult.ok();
    }

    @RequestMapping(value = "/tool/autoAgree", method = RequestMethod.POST)
    public CommonResult toolAutoAgree(@RequestBody MerchantSituationDTO merchantSituationDTO){
        merchantSituationFacade.autoAgree(merchantSituationDTO.getId());
        return CommonResult.ok();
    }
}
