package net.summerfarm.crm.controller;


import com.alibaba.fastjson.JSONObject;
import net.summerfarm.crm.model.query.wecom.contactway.WeComAddContactWayInput;
import net.summerfarm.crm.model.query.wecom.contactway.ConfigIdInput;
import net.summerfarm.crm.model.vo.weCom.contactway.WeComAddContactWayResp;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;
import net.summerfarm.crm.service.wecom.WeComContactWayService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/crm-service/weCom/contact-way")
public class WeComContactWayController {

    @Resource
    private WeComContactWayService weComContactWayService;

    @PostMapping(value = "/add")
    public CommonResult<WeComAddContactWayResp> addContactWay(@RequestBody WeComAddContactWayInput input) {
        return CommonResult.ok(weComContactWayService.addContactWay(input));
    }

    @PostMapping(value = "/get")
    public CommonResult<JSONObject> getContactWay(@RequestBody ConfigIdInput input) {
        return CommonResult.ok(weComContactWayService.getContactWay(input));
    }

    @PostMapping(value = "/delete")
    public CommonResult<WeChatBaseResp> deleteContactWay(@RequestBody ConfigIdInput input) {
        return CommonResult.ok(weComContactWayService.deleteContactWay(input));
    }
}
