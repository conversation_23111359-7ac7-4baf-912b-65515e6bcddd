package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.dto.VisitPlanDTO;
import net.summerfarm.crm.model.query.VisitPlanQuery;
import net.summerfarm.crm.model.vo.VisitPlanVO;
import net.summerfarm.crm.service.VisitPlanService;
import net.summerfarm.crm.task.handler.FollowUpRelationTaskHandler;
import net.summerfarm.crm.task.handler.MerchantLabelTaskHandler;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@RestController
@RequestMapping(value = "/crm-service/visitPlan")
public class VisitPlanController {
    @Resource
    private VisitPlanService visitPlanService;

    @RequiresPermissions(value = {"visitPlan:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public AjaxResult insertVisitPlan(@RequestBody @Validated(Add.class) VisitPlanDTO visitPlanDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }

        return visitPlanService.insertVisitPlan(visitPlanDTO);
    }

    @GetMapping(value = {"/detail/{visitPlanId}", "/{visitPlanId}"})
    public AjaxResult selectVisitPlanDetail(@PathVariable Long visitPlanId, Integer type) {
        return visitPlanService.queryVisitPlan(visitPlanId, type);
    }

    /**
     * 拜访计划列表
     */
    @RequiresPermissions(value = {"visitPlan:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult queryVisitPlanList(@PathVariable int pageIndex, @PathVariable int pageSize, VisitPlanQuery visitPlanQuery) {
        return visitPlanService.queryVisitPlanList(pageIndex, pageSize, visitPlanQuery);
    }

    @RequiresPermissions(value = {"visitPlan:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public AjaxResult cancel(VisitPlanDTO visitPlanDTO) {
        return visitPlanService.cancelPlan(visitPlanDTO);
    }

    /**
     * 查询拜访/拉新计划
     *
     */
    @RequiresPermissions(value = {"visitPlan:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @GetMapping("/day")
    public CommonResult<List<VisitPlanVO>> queryVisitPlan(VisitPlanQuery visitPlanQuery) {
        return visitPlanService.queryPlanByDate(visitPlanQuery);
    }

    /**
     * 拜访计划个数
     */
    @RequiresPermissions(value = {"visitPlan:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/count")
    public CommonResult<Integer> count(@RequestBody VisitPlanQuery visitPlanQuery) {
        return visitPlanService.count(visitPlanQuery.getProvince(),visitPlanQuery.getCity(),visitPlanQuery.getArea(), visitPlanQuery.getDate());
    }

    /**
     * 批量新增拉新计划
     *
     */
    @RequiresPermissions(value = {"visitPlan:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/create-batch", method = RequestMethod.POST)
    public AjaxResult createBatch(Integer count, String province,String city, String date) {
        return visitPlanService.createBatch(count, province,city, date);
    }
}
