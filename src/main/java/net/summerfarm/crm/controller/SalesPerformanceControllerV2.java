package net.summerfarm.crm.controller;

import net.summerfarm.crm.enums.BdAreaConfigEnum;
import net.summerfarm.crm.enums.CategoryPromotionRewardModeEnum;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.enums.HighValueCustomerTypeEnum;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.model.convert.salesdata.DashboardModuleConverter;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.query.salesperformance.*;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.model.vo.saledata.CRMDashboardModule;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSpuSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionV2VO;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationTotalVO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailV2VO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerSummaryV2VO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceSummaryVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.summerfarm.crm.service.salesperformance.CategoryPromotionServiceV2;
import net.summerfarm.crm.service.salesperformance.CommissionEstimationService;
import net.summerfarm.crm.service.salesperformance.HighValueCustomerServiceV2;
import net.summerfarm.crm.service.salesperformance.PbScorePerformanceService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 销售绩效二期板块
 */
@RestController
@RequestMapping(value = "/crm-service/sales-performance-v2")
public class SalesPerformanceControllerV2 {

    @Autowired
    private CommissionEstimationService commissionEstimationService;
    @Autowired
    private HighValueCustomerServiceV2 highValueCustomerServiceV2;
    @Autowired
    private CategoryPromotionServiceV2 categoryPromotionServiceV2;
    @Autowired
    private PbScorePerformanceService pbScorePerformanceService;
    @Autowired
    private DashboardModuleConverter dashboardModuleConverter;
    @Autowired
    private BdAreaConfigService bdAreaConfigService;
    @Autowired
    private FollowUpRelationMapper followUpRelationMapper;


    // -------------------------------- 佣金预估 --------------------------------

    /**
     * 佣金预估总额
     * 普通销售看到的是他自己客户的总额
     * m1 看到的是下属销售总额之和
     * m2及以上不返回
     *
     * @return 佣金预估总额
     */
    @PostMapping("/query/commission-estimation-total")
    public CommonResult<CommissionEstimationTotalVO> getCommissionEstimationTotal() {
        return CommonResult.ok(commissionEstimationService.getCommissionEstimationTotal());
    }

    // -------------------------------- 高价值客户 --------------------------------

    /**
     * 获取高价值客户汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @return 高价值客户汇总数据
     */
    @PostMapping("/query/high-value-customer-summary")
    public CommonResult<CRMDashboardModule> getHighValueCustomerSummary(@RequestBody @Validated HighValueCustomerSummaryQuery query) {
        // 获取高价值客户汇总数据
        HighValueCustomerSummaryV2VO summaryVO = highValueCustomerServiceV2.getHighValueCustomerSummary(query);

        // 转换为CRMDashboardModule并返回，使用Optional防止NPE
        ConfigValueEnum configValueEnum = ConfigValueEnum.CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG_V2;
        if (HighValueCustomerTypeEnum.QUASI_HIGH_VALUE.equals(query.getCustomerType())) {
            configValueEnum = ConfigValueEnum.CRM_QUASI_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG;
        }
        CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(summaryVO,
                configValueEnum,
                Optional.ofNullable(summaryVO).map(HighValueCustomerSummaryV2VO::getBdId).map(String::valueOf).orElse(""),
                Optional.ofNullable(summaryVO).map(HighValueCustomerSummaryV2VO::getBdName).orElse(""));

        return CommonResult.ok(module);
    }

    /**
     * 销售高价值客户汇总列表
     * 为 m1 列出下属 bd 高价值客户汇总
     * 普通bd, m2及以上不返回
     */
    @PostMapping("/list/high-value-customer-summary")
    public CommonResult<List<CRMDashboardModule>> listHighValueCustomerSummaryForManager(@RequestBody @Validated BdHighValueCustomerSummaryListQuery query) {
        // 获取高价值客户汇总列表
        List<HighValueCustomerSummaryV2VO> summaryVOs = highValueCustomerServiceV2.listHighValueCustomerSummaryForManager(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules = dashboardModuleConverter.convertToModuleList(summaryVOs,
                (item) -> {
                    if (HighValueCustomerTypeEnum.QUASI_HIGH_VALUE.equals(query.getCustomerType())) {
                        return ConfigValueEnum.CRM_QUASI_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG;
                    }
                    return ConfigValueEnum.CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG_V2;
                },
                HighValueCustomerSummaryV2VO::getBdId,
                HighValueCustomerSummaryV2VO::getBdName);

        return CommonResult.ok(modules);
    }

    /**
     * 高价值客户详情
     * 查询某销售下所有高价值客户, 如果bdId为空, 则返回当前登录bd的高价值客户
     *
     * @param query 查询参数
     * @return 高价值客户汇总数据
     */
    @PostMapping("/query/high-value-customer")
    public CommonResult<PageInfo<CRMDashboardModule>> getBdHighValueCustomerSummary(@RequestBody @Validated HighValueCustomerQuery query) {
        // 获取高价值客户详情
        PageInfo<HighValueCustomerDetailV2VO> pageInfo = highValueCustomerServiceV2.getBdHighValueCustomerDetail(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result = dashboardModuleConverter.convertToModulePageInfo(pageInfo,
                (item) -> {
                    if (HighValueCustomerTypeEnum.QUASI_HIGH_VALUE.equals(query.getCustValueLabel())) {
                        return ConfigValueEnum.CRM_QUASI_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG;
                    }
                    return ConfigValueEnum.CRM_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG_V2;
                },
                HighValueCustomerDetailV2VO::getMId,
                HighValueCustomerDetailV2VO::getMname);

        return CommonResult.ok(result);
    }


    // -------------------------------- 超标SPU客户 --------------------------------


    // -------------------------------- 品类推广 --------------------------------

    /**
     * 获取品类推广汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @return 品类推广汇总数据
     */
    @PostMapping("/query/category-promotion-summary")
    public CommonResult<CRMDashboardModule> getCategoryPromotionSummary(@RequestBody CategoryPromotionSummaryQuery query) {
        // 获取品类推广汇总数据
        CategoryPromotionSummaryV2VO summaryVO = categoryPromotionServiceV2.getCategoryPromotionSummary(query);

        // 转换为CRMDashboardModule并返回，使用Optional防止NPE
        CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(summaryVO,
                ConfigValueEnum.CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG_V2,
                Optional.ofNullable(summaryVO).map(CategoryPromotionSummaryV2VO::getBdId).map(String::valueOf).orElse(""),
                Optional.ofNullable(summaryVO).map(CategoryPromotionSummaryV2VO::getBdName).orElse(""));

        return CommonResult.ok(module);
    }

    /**
     * 销售品类推广汇总列表
     * 为 m1 列出下属 bd 品类推广汇总
     * 普通bd, m2及以上不返回
     */
    @PostMapping("/list/category-promotion-summary")
    public CommonResult<List<CRMDashboardModule>> listCategoryPromotionSummaryForManager(@RequestBody @Validated BdCategoryPromotionSummaryListQuery query) {
        // 获取品类推广汇总列表
        List<CategoryPromotionSummaryV2VO> summaryVOs = categoryPromotionServiceV2.listCategoryPromotionSummaryForManager(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules =
                dashboardModuleConverter.convertToModuleList(summaryVOs, ConfigValueEnum.CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG_V2,
                        CategoryPromotionSummaryV2VO::getBdId, CategoryPromotionSummaryV2VO::getBdName);

        return CommonResult.ok(modules);
    }

    /**
     * 销售维度品类推广数据详情
     * 返回销售下面按客户聚合的品类推广数据
     *
     * @param query 查询参数
     * @return 品类推广数据详情
     */
    @PostMapping("/query/category-promotion-data")
    public CommonResult<PageInfo<CRMDashboardModule>> getCategoryPromotionData(@RequestBody @Validated CategoryPromotionQuery query) {
        // 获取品类推广详情
        PageInfo<CategoryPromotionDetailV2VO> pageInfo = categoryPromotionServiceV2.getCategoryPromotionData(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result =
                dashboardModuleConverter.convertToModulePageInfo(pageInfo, ConfigValueEnum.CRM_CATEGORY_PROMOTION_DATA_CONFIG_V2,
                        CategoryPromotionDetailV2VO::getMId, CategoryPromotionDetailV2VO::getMname);

        return CommonResult.ok(result);
    }

    /**
     * 客户维度品类推广数据详情
     *
     * @param query 查询参数
     * @return 品类推广数据
     */
    @PostMapping("/query/merchant-category-promotion")
    public CommonResult<List<CRMDashboardModule>> getMerchantCategoryPromotion(@RequestBody @Validated MerchantCategoryPromotionQuery query) {
        // 获取客户维度品类推广数据
        List<MerchantCategoryPromotionV2VO> merchantCategoryPromotionVOs =
                categoryPromotionServiceV2.getMerchantCategoryPromotion(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules = dashboardModuleConverter.convertToModuleList(merchantCategoryPromotionVOs, (item) -> {
            if (CategoryPromotionRewardModeEnum.TRANSACTION_COUNT.getCode().equals(item.getIsDlvPayment())) {
                return ConfigValueEnum.CRM_CATEGORY_PROMOTION_MERCHANT_TXN_CONFIG;
            }
            return ConfigValueEnum.CRM_CATEGORY_PROMOTION_MERCHANT_FUL_CONFIG;
        }, MerchantCategoryPromotionV2VO::getSpuGroup, MerchantCategoryPromotionV2VO::getSpuGroup);

        return CommonResult.ok(modules);
    }

    /**
     * 商品维度品类推广数据
     *
     * @param query
     * @return
     */
    @PostMapping("/query/spu-group-category-promotion-data")
    public CommonResult<PageInfo<CRMDashboardModule>> getSpuGroupCategoryPromotionData(@RequestBody @Validated SpuGroupCategoryPromotionQuery query) {
        // 获取商品维度品类推广数据
        PageInfo<CategoryPromotionSpuSummaryVO> pageInfo = categoryPromotionServiceV2.getSpuGroupCategoryPromotionData(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result = dashboardModuleConverter.convertToModulePageInfo(pageInfo, item -> {
            if (query.getCustomerType() == null) {
                // 全部客户
                if (CategoryPromotionRewardModeEnum.TRANSACTION_COUNT.getCode().equals(item.getIsDlvPayment())) {
                    return ConfigValueEnum.CRM_CATEGORY_PROMOTION_SPU_ALL_TXN_CONFIG;
                }
                return ConfigValueEnum.CRM_CATEGORY_PROMOTION_SPU_ALL_FUL_CONFIG;
            }
            // 存量&增量客户
            if (CategoryPromotionRewardModeEnum.TRANSACTION_COUNT.getCode().equals(item.getIsDlvPayment())) {
                return ConfigValueEnum.CRM_CATEGORY_PROMOTION_SPU_CUST_TXN_CONFIG;
            }
            return ConfigValueEnum.CRM_CATEGORY_PROMOTION_SPU_CUST_FUL_CONFIG;
        }, CategoryPromotionSpuSummaryVO::getSpuGroup, CategoryPromotionSpuSummaryVO::getSpuGroup);

        return CommonResult.ok(result);
    }

    /**
     * 获取本期全部推广品类
     *
     * @return
     */
    @PostMapping("/list/category-promotion-spu-group-list")
    public CommonResult<List<String>> getCategoryPromotionSpuGroupList() {
        return CommonResult.ok(categoryPromotionServiceV2.getCategoryPromotionSpuGroupList());
    }

    // -------------------------------- 月度绩效得分 --------------------------------

    /**
     * PB标品得分汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @param query
     * @return
     */
    @PostMapping("/query/pb-performance-summary")
    public CommonResult<CRMDashboardModule> getPbPerformanceSummary(@RequestBody @Validated PbPerformanceSummaryQuery query) {
        // 获取PB标品汇总数据
        PbPerformanceSummaryVO summaryVO = pbScorePerformanceService.getPbPerformanceSummary(query);

        // 转换为CRMDashboardModule并返回，使用Optional防止NPE
        ConfigValueEnum configValueEnum =
                (summaryVO != null && summaryVO.isCityManager()) ? ConfigValueEnum.CRM_PB_PERFORMANCE_M1_SUMMARY_CONFIG
                        : ConfigValueEnum.CRM_PB_PERFORMANCE_SUMMARY_CONFIG;
        CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(summaryVO,
                configValueEnum,
                Optional.ofNullable(summaryVO).map(PbPerformanceSummaryVO::getBdId).map(String::valueOf).orElse(""),
                Optional.ofNullable(summaryVO).map(PbPerformanceSummaryVO::getBdName).orElse(""));

        return CommonResult.ok(module);
    }

    /**
     * PB标品得分汇总列表
     * 为 m1 列出下属 bd PB标品得分汇总
     * 普通bd, m2及以上不返回
     *
     * @param query
     * @return
     */
    @PostMapping("/list/pb-performance-summary")
    public CommonResult<List<CRMDashboardModule>> listPbPerformanceSummaryForManager(@RequestBody @Validated PbPerformanceSummaryListQuery query) {
        // 获取PB标品汇总列表
        List<PbPerformanceSummaryVO> summaryVOs = pbScorePerformanceService.listPbPerformanceSummaryForManager(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules =
                dashboardModuleConverter.convertToModuleList(summaryVOs, ConfigValueEnum.CRM_PB_PERFORMANCE_SUMMARY_CONFIG,
                        PbPerformanceSummaryVO::getBdId, PbPerformanceSummaryVO::getBdName);

        return CommonResult.ok(modules);
    }

    /**
     * 销售维度PB标品得分数据详情
     * 返回销售下面门店的PB标品得分数据
     *
     * @param query
     * @return
     */
    @PostMapping("/query/pb-performance-data")
    public CommonResult<PageInfo<CRMDashboardModule>> getPbPerformanceData(@RequestBody @Validated PbPerformanceQuery query) {
        // 获取PB标品详情
        PageInfo<PbPerformanceDetailVO> pageInfo = pbScorePerformanceService.getPbPerformanceData(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result =
                dashboardModuleConverter.convertToModulePageInfo(pageInfo, ConfigValueEnum.CRM_PB_PERFORMANCE_DATA_CONFIG,
                        PbPerformanceDetailVO::getMId, PbPerformanceDetailVO::getMname);

        return CommonResult.ok(result);
    }

    /**
     * 利润积分得分汇总数据
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @param query
     * @return
     */
    @PostMapping("/query/score-performance-summary")
    public CommonResult<CRMDashboardModule> getScorePerformanceSummary(@RequestBody @Validated ScorePerformanceSummaryQuery query) {
        // 获取利润积分汇总数据
        ScorePerformanceSummaryVO summaryVO = pbScorePerformanceService.getScorePerformanceSummary(query);

        // 转换为CRMDashboardModule并返回，使用Optional防止NPE
        ConfigValueEnum configValueEnum =
                (summaryVO != null && summaryVO.isCityManager()) ? ConfigValueEnum.CRM_SCORE_PERFORMANCE_M1_SUMMARY_CONFIG
                        : ConfigValueEnum.CRM_SCORE_PERFORMANCE_SUMMARY_CONFIG;
        CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(summaryVO,
                configValueEnum,
                Optional.ofNullable(summaryVO).map(ScorePerformanceSummaryVO::getBdId).map(String::valueOf).orElse(""),
                Optional.ofNullable(summaryVO).map(ScorePerformanceSummaryVO::getBdName).orElse(""));

        return CommonResult.ok(module);
    }

    /**
     * 利润积分得分汇总列表
     * 为 m1 列出下属 bd 利润积分得分汇总
     * 普通bd, m2及以上不返回
     *
     * @param query
     * @return
     */
    @PostMapping("/list/score-performance-summary")
    public CommonResult<List<CRMDashboardModule>> listScorePerformanceSummaryForManager(@RequestBody @Validated ScorePerformanceSummaryListQuery query) {
        // 获取利润积分汇总列表
        List<ScorePerformanceSummaryVO> summaryVOs = pbScorePerformanceService.listScorePerformanceSummaryForManager(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules =
                dashboardModuleConverter.convertToModuleList(summaryVOs, ConfigValueEnum.CRM_SCORE_PERFORMANCE_SUMMARY_CONFIG,
                        ScorePerformanceSummaryVO::getBdId, ScorePerformanceSummaryVO::getBdName);
        return CommonResult.ok(modules);
    }

    /**
     * 销售维度利润积分得分数据详情
     * 返回销售下面门店的利润积分得分数据
     *
     * @param query
     * @return
     */
    @PostMapping("/query/score-performance-data")
    public CommonResult<PageInfo<CRMDashboardModule>> getScorePerformanceData(@RequestBody @Validated ScorePerformanceQuery query) {
        // 获取利润积分详情
        PageInfo<ScorePerformanceDetailVO> pageInfo = pbScorePerformanceService.getScorePerformanceData(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result =
                dashboardModuleConverter.convertToModulePageInfo(pageInfo, ConfigValueEnum.CRM_SCORE_PERFORMANCE_DATA_CONFIG,
                        ScorePerformanceDetailVO::getMId, ScorePerformanceDetailVO::getMname);

        return CommonResult.ok(result);
    }

    // -------------------------------- 客户详情 --------------------------------

    /**
     * 客户详情页查询品类推广数据
     *
     * @param query 查询参数
     * @return 品类推广数据
     */
    @PostMapping("/query/merchant-detail-category-promotion")
    public CommonResult<List<CRMDashboardModule>> getMerchantDetailCategoryPromotion(@RequestBody @Validated MerchantDetailCategoryPromotionQuery query) {
        // 获取查询品类推广数据的BD ID
        Long bdId = getMerchantDetailCategoryPromotionBdId(query);
        if (bdId == null) {
            return CommonResult.ok(Collections.emptyList());
        }
        MerchantCategoryPromotionQuery categoryPromotionQuery = new MerchantCategoryPromotionQuery();
        categoryPromotionQuery.setMId(query.getMId());
        categoryPromotionQuery.setBdId(bdId);
        // 获取客户维度品类推广数据
        List<MerchantCategoryPromotionV2VO> merchantCategoryPromotionVOs =
                categoryPromotionServiceV2.getMerchantCategoryPromotion(categoryPromotionQuery);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules = dashboardModuleConverter.convertToModuleList(merchantCategoryPromotionVOs, (item) -> {
            if (CategoryPromotionRewardModeEnum.TRANSACTION_COUNT.getCode().equals(item.getIsDlvPayment())) {
                return ConfigValueEnum.CRM_MERCHANT_DETAIL_CATEGORY_PRO_TXN_CONFIG;
            }
            return ConfigValueEnum.CRM_MERCHANT_DETAIL_CATEGORY_PRO_FUL_CONFIG;
        }, MerchantCategoryPromotionV2VO::getSpuGroup, MerchantCategoryPromotionV2VO::getSpuGroup);

        return CommonResult.ok(modules);
    }

    // ------------------------------------------------ private methods ------------------------------------------------

    private Long getMerchantDetailCategoryPromotionBdId(MerchantDetailCategoryPromotionQuery query) {
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            throw new BizException("未找到您的销售身份信息, 请联系销售主管配置");
        }
        // 如果是BD则直接返回BD ID
        if (topRankOrg.getRank() == BdAreaConfigEnum.SaleRank.BD) {
            return Long.valueOf(topRankOrg.getBdId());
        }
        // M2及以上不返回品类推广数据
        if (topRankOrg.getRank() != BdAreaConfigEnum.SaleRank.CITY_MANAGER) {
            return null;
        }
        // 获取客户当前归属的BD
        MerchantVO merchantVO = followUpRelationMapper.queryFollow(query.getMId());
        if (merchantVO == null || merchantVO.getAdminId() == null) {
            return null;
        }
        // 如果客户当前归属于M1的下属BD（M1可以查看该客户的品类推广数据），则返回客户当前归属BD
        if (bdAreaConfigService.listChildrenBd().stream().anyMatch(x -> merchantVO.getAdminId().equals(x.getBdId()))) {
            return Long.valueOf(merchantVO.getAdminId());
        }
        return null;
    }

}
