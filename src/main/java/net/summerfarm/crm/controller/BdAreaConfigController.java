package net.summerfarm.crm.controller;

import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.domain.CrmSalesCity;
import net.summerfarm.crm.model.dto.areaConfig.*;
import net.summerfarm.crm.model.query.areaConfig.BdAreaConfigDetailQuery;
import net.summerfarm.crm.model.query.areaConfig.BdAreaConfigQuery;
import net.summerfarm.crm.model.vo.areaConfig.BdAreaConfigDetail;
import net.summerfarm.crm.model.vo.areaConfig.BdAreaConfigVo;
import net.summerfarm.crm.model.vo.areaConfig.SalesAreaBaseVo;
import net.summerfarm.crm.model.vo.areaConfig.SalesAreaVo;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.xianmu.common.result.CommonResult;
import com.github.pagehelper.PageInfo;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import javax.annotation.Resource;

/**
 * 销售区域配置
 *
 * <AUTHOR>
 * @date 2023/08/21
 */
@RestController
@RequestMapping(value = "/crm-service/bd-area-config")
public class BdAreaConfigController {
    @Resource
    private BdAreaConfigService bdAreaConfigService;

    /**
     * 区域配置列表
     *
     * @param qry qry
     * @return {@link CommonResult}<{@link BdAreaConfigVo}>
     */
    @RequiresPermissions(value = {"area-config:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/area-config/query/get")
    public CommonResult<PageInfo<BdAreaConfigVo>> getAreaConfig(@RequestBody BdAreaConfigQuery qry) {
        return CommonResult.ok(bdAreaConfigService.listBdAreaConfig(qry));
    }

    /**
     * 区域配置详情
     *
     * @param qry qry
     * @return {@link CommonResult}<{@link BdAreaConfigVo}>
     */
    @PostMapping("/area-config/query/detail")
    @RequiresPermissions(value = {"area-config:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<BdAreaConfigDetail> areaConfigDetail(@RequestBody BdAreaConfigDetailQuery qry) {
        return CommonResult.ok(bdAreaConfigService.areaConfigDetail(qry.getCityAdminId(), qry.getSalesAreaId()));
    }
    /**
     * 删除区域配置
     *
     * @param qry qry
     * @return {@link CommonResult}<{@link BdAreaConfigVo}>
     */
    @PostMapping("/area-config/update/delete")
    @RequiresPermissions(value = {"area-config:delete", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<Void> deleteAreaConfig(@RequestBody BdAreaConfigDetailQuery qry) {
        return bdAreaConfigService.deleteAreaConfig(qry.getCityOrgId(), qry.getSalesAreaId());
    }

    /**
     * 销售组织配置
     *
     * @return {@link CommonResult}<{@link Void}>
     */
    @RequiresPermissions(value = {"bd-org:upsert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/bd-org/upsert")
    public CommonResult<Integer> bdOrgUpsert(@Validated @RequestBody BdOrgUpsertDTO upsertDTO) {
        return bdAreaConfigService.bdOrgUpsert(upsertDTO);
    }


    /**
     * 更换M1.
     * 如果原M1只负责一个城市,自动把原下属划给新M1
     * 如果原M1负责多个城市,需要根据前端传参划分
     */
    @RequiresPermissions(value = {"bd-org:upsert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/bd-org/change-city-manager")
    public CommonResult<Void> changeCityManager(@Validated @RequestBody BdOrgChangeAreaM1DTO upsertDTO) {
        bdAreaConfigService.changeCityManager(upsertDTO);
        return CommonResult.ok();
    }

    /**
     * 根据级别查询销售组织
     *
     * @return {@link CommonResult}<{@link Void}>
     */
    @PostMapping("/bd-org/query/list-by-rank")
    public CommonResult<List<CrmBdOrg>> listByRank(@Validated @RequestBody BdAreaConfigQuery upsertDTO) {
        return CommonResult.ok(bdAreaConfigService.listBdOrgByRank(upsertDTO.getRank()));
    }

    /**
     * 获取当前用户的所有上级
     *
     * @return {@link CommonResult}<{@link Void}>
     */
    @PostMapping("/bd-org/query/get-all-parent")
    public CommonResult<List<CrmBdOrg>> getAllParent() {
        return CommonResult.ok(bdAreaConfigService.listParentOrg());
    }

    /**
     * 获取当前用户下属的bd
     *
     * @return {@link CommonResult}<{@link Void}>
     */
    @PostMapping("/bd-org/query/get-bd")
    public CommonResult<List<CrmBdOrg>> getBd() {
        return CommonResult.ok(bdAreaConfigService.listChildrenBd());
    }

    /**
     * 获取指定用户下属的bd
     *
     * @return {@link CommonResult}<{@link Void}>
     */
    @PostMapping("/bd-org/query/get-all-bd")
    public CommonResult<List<CrmBdOrg>> getAllBd(@RequestBody BdOrgDTO bdOrgDTO) {
        return CommonResult.ok(bdAreaConfigService.listChildrenBd(bdOrgDTO.getBdOrgId()));
    }

    /**
     * 新增区域配置
     *
     * @param upsertDTO upsert dto
     * @return {@link CommonResult}<{@link Integer}>
     */
    @RequiresPermissions(value = {"sales-area:save", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/sales-area/upsert/save")
    public CommonResult<Void> areaConfigSave(@Validated(value = {Add.class}) @RequestBody BdSaleAreaUpsertDTO upsertDTO) {
        return bdAreaConfigService.areaConfigSave(upsertDTO);
    }

    /**
     * 编辑区域配置
     *
     * @param upsertDTO upsert dto
     * @return {@link CommonResult}<{@link Integer}>
     */
    @PostMapping("/sales-area/upsert/update")
    @RequiresPermissions(value = {"sales-area:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<Void> areaConfigUpdate(@Validated(value = {Update.class})@RequestBody BdSaleAreaUpsertDTO upsertDTO) {
        return bdAreaConfigService.areaConfigUpdate(upsertDTO);
    }

    /**
     * 获取销售区域
     */
    @PostMapping("/sales-area/query/get")
    public CommonResult<List<SalesAreaVo>> getSalesArea(@RequestBody(required = false) SalesAreaQueryDTO queryDTO) {
        Integer orgId = queryDTO != null ? queryDTO.getOrgId() : null;
        List<SalesAreaVo> salesAreaVos = bdAreaConfigService.listSalesArea(orgId);
        return CommonResult.ok(salesAreaVos);
    }

    /**
     * 获取当前用户下属的销售区域
     * 当前用户为BD时返回空
     */
    @PostMapping("/sales-area/query/list")
    public CommonResult<List<SalesAreaBaseVo>> listSalesArea() {
        List<SalesAreaBaseVo> salesAreaBaseVos = bdAreaConfigService.listSalesArea();
        return CommonResult.ok(salesAreaBaseVos);
    }

    /**
     * 获取销售城市
     */
    @PostMapping("/sales-city/query/get")
    public CommonResult<List<CrmSalesCity>> getSalesCity() {
        return CommonResult.ok(bdAreaConfigService.selectSalesCity());
    }

    /**
     * 获取当前用户最高层级
     */
    @PostMapping("/bd-org/query/get-top-rank")
    public CommonResult<CrmBdOrg> getTopRank() {
        return CommonResult.ok(bdAreaConfigService.getTopRankOrg());
    }
}
