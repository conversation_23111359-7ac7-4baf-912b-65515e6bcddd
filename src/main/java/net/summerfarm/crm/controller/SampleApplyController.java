package net.summerfarm.crm.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.input.samples.SampleApplyBatchInsertInput;
import net.summerfarm.crm.model.vo.SampleApplyBatchInsertResp;
import net.summerfarm.crm.model.vo.SampleApplyUploadMerchantVO;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import net.summerfarm.crm.service.SampleApplyService;
import net.summerfarm.enums.RedissonLockKey;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Slf4j
@RestController
@RequestMapping("/crm-service/sampleApply")
public class SampleApplyController {

    @Resource
    SampleApplyService sampleApplyService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private BaseService baseService;


    @RequestMapping(value = "/insert" ,method = RequestMethod.POST )
    @RequiresPermissions(value = {"sampleApply:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult insertSampleApply(@RequestBody SampleApplyVO sampleApplyVO){
        // 同一adminId并发申请控制
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.SAMPLE_APPLY + baseService.getAdminId());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new DefaultServiceException("请重新进行样品申请!");
            }
            return sampleApplyService.insertSampleApply(sampleApplyVO);
        } catch (InterruptedException e) {
            log.warn("锁获取异常:{}", e.getMessage(),e);
            throw new DefaultServiceException("请重新进行样品申请");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 批量创建样品单
     */
    @RequestMapping(value = "/upset/batch-insert" ,method = RequestMethod.POST )
    @RequiresPermissions(value = {"sampleApply:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<SampleApplyBatchInsertResp> batchInsertSampleApply(@RequestBody @Valid SampleApplyBatchInsertInput input){
        input.setAdminId(baseService.getAdminId());
        return CommonResult.ok(sampleApplyService.batchInsertSampleApply(input));
    }

    /**
     * 校验批量创建样品单客户信息
     */
    @RequestMapping(value = "/upset/check-upload" ,method = RequestMethod.POST )
    @RequiresPermissions(value = {"sampleApply:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<SampleApplyUploadMerchantVO> checkBatchInsertUploadInfo(@RequestBody SampleApplyBatchInsertInput input){
        return CommonResult.ok(sampleApplyService.checkBatchInsertUploadInfo(input));
    }

    /**
     * 批量创建样品单
     */
    @RequestMapping(value = "/upset/batch-cancel" ,method = RequestMethod.POST )
    @RequiresPermissions(value = {"sampleApply:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<Boolean> batchCancelSampleApply(@RequestBody SampleApplyBatchInsertInput input){
        return CommonResult.ok(sampleApplyService.batchCancelSampleApply(input));
    }

    /**
     * 批量创建样品单
     */
    @RequestMapping(value = "/upset/export" ,method = RequestMethod.POST )
    @RequiresPermissions(value = {"sampleApply:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<Boolean> exportSampleApplyFailData(String exportId){
        return CommonResult.ok(sampleApplyService.exportSampleApplyFailData(exportId));
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @RequiresPermissions(value = {"sampleApply:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult updateSampleApply(@RequestBody SampleApply sampleApply){
        return sampleApplyService.updateSampleApply(sampleApply);
    }

    @RequestMapping(value = "/selectSample/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    @RequiresPermissions(value = {"sampleApply:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult selectSampleApplyVO(@PathVariable int pageIndex, @PathVariable int pageSize, SampleApply sampleApply, String keyword){
       return sampleApplyService.selectSampleApplyVO(pageIndex,pageSize,sampleApply, keyword);
    }

    @GetMapping("/cancel/{sampleId}")
    @RequiresPermissions(value = {"sampleApply:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult cancelSampleApply(@PathVariable int sampleId){
        // 同一adminId并发申请控制
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.SAMPLE_APPLY + baseService.getAdminId());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new DefaultServiceException("请勿重复操作,请稍后确认样品取消是否成功!");
            }
        return sampleApplyService.cancelSampleApply(sampleId);
        } catch (InterruptedException e) {
            log.warn("锁获取异常:{}", e.getMessage(),e);
            throw new DefaultServiceException("请勿重复操作,请稍后确认样品取消是否成功!");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

}
