package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.query.crmjobv2.CrmJobMerchantListQueryV2;
import net.summerfarm.crm.model.query.crmjobv2.JobIdQuery;
import net.summerfarm.crm.model.vo.crmjobv2.CrmJobMerchantV2VO;
import net.summerfarm.crm.service.crmjob.CrmJobQueryServiceV2;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 任务中心V2控制器
 * 支持新的item表结构，所有任务类型都可以使用新逻辑创建
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/crm-job-v2")
public class CrmJobControllerV2 {

    @Resource
    private CrmJobQueryServiceV2 crmJobQueryServiceV2;

    // -------------------------------------- 创建/更新 -------------------------------------- //

    // 暂时还用不到. 未来全面转v2了再建.


    // ---------------------------------------- 查询 ---------------------------------------- //

    /**
     * 查询当前登录销售的任务对应的门店详情列表
     * 普通销售仅能查看自己私海的门店列表
     * m1及以上销售查看自己及下属销售私海+负责城市公海的门店列表
     */
    @PostMapping("/query/merchant-detail-for-bd")
    public CommonResult<PageInfo<CrmJobMerchantV2VO>> queryMerchantJobForBd(@RequestBody @Validated CrmJobMerchantListQueryV2 query) {
        return CommonResult.ok(crmJobQueryServiceV2.queryMerchantListForBdV2(query));
    }


    /**
     * 获取任务item
     * <p>
     * note: 2025-05-26 目前这版比较简单,因为v1版的5个任务都还在用老逻辑
     * 只考虑 [品类履约任务] 的话返回 List<String> 就够了. 未来如果要全面转 v2 版再进行拓展.
     *
     * @return 任务item
     */
    @PostMapping("/query/list-job-item")
    public CommonResult<List<String>> queryJobItem(@RequestBody @Validated JobIdQuery query) {
        return CommonResult.ok(crmJobQueryServiceV2.queryJobItem(query.getJobId()));
    }
}
