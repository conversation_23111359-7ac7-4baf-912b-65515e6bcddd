package net.summerfarm.crm.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.dto.config.BdGrayConfigDTO;
import net.summerfarm.crm.model.query.tool.BdInGrayRangeQuery;
import net.summerfarm.crm.model.vo.tool.BdInGrayRangeVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 工具控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/crm-service/tool")
public class ToolController {

    @Autowired
    private BdAreaConfigService bdAreaConfigService;
    @Autowired
    private CrmConfig crmConfig;

    /**
     * 查询当前登录销售是否在灰度范围
     *
     * @param query
     * @return
     */
    @PostMapping("/query/bd-in-gray-range")
    public CommonResult<BdInGrayRangeVO> queryBdInGrayRange(@RequestBody @Valid BdInGrayRangeQuery query) {
        // 1. 获取当前登录销售的bdId
        CrmBdOrg topRankOrg = bdAreaConfigService.getTopRankOrg();
        if (topRankOrg == null) {
            throw new BizException("获取当前登录销售信息失败");
        }
        Integer bdId = topRankOrg.getBdId();

        // 2. 获取销售灰度配置列表
        List<BdGrayConfigDTO> bdGrayConfigs = crmConfig.getBdGrayConfig();

        // 3. 循环判断销售是否在灰度范围
        List<BdInGrayRangeVO.BdInGrayRangeDetailVO> details = new ArrayList<>();
        List<String> grayProjects = (query != null && query.getGrayProjects() != null) ? query.getGrayProjects() : Collections.emptyList();

        for (BdGrayConfigDTO bdGrayConfig : bdGrayConfigs) {
            if (!CollectionUtils.isEmpty(grayProjects) && !grayProjects.contains(bdGrayConfig.getProject())) {
                continue;
            }
            BdInGrayRangeVO.BdInGrayRangeDetailVO detail = new BdInGrayRangeVO.BdInGrayRangeDetailVO();
            detail.setGrayProject(bdGrayConfig.getProject());
            details.add(detail);

            // 有excludeBds先判断excludeBds
            List<Integer> excludeBds = bdGrayConfig.getExcludeBds();
            if (!CollectionUtils.isEmpty(excludeBds)) {
                boolean inGrayRange = !excludeBds.contains(bdId);
                detail.setInGrayRange(inGrayRange);
                continue;
            }

            // 无excludeBds再判断includeBds
            List<Integer> includeBds = bdGrayConfig.getIncludeBds();
            boolean inGrayRange = !CollectionUtils.isEmpty(includeBds) && includeBds.contains(bdId);
            detail.setInGrayRange(inGrayRange);
        }

        // 4. 构造结果对象返回
        BdInGrayRangeVO result = new BdInGrayRangeVO();
        result.setDetails(details);
        return CommonResult.ok(result);
    }

}