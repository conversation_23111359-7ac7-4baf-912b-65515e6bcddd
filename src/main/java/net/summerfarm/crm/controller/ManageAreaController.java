package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.query.QueryManageAreaQuery;
import net.summerfarm.crm.model.query.SaveManageAreaQuery;
import net.summerfarm.crm.service.ManageAreaService;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@RestController
@RequestMapping(value = "/crm-service/managearea")
public class ManageAreaController {

    @Resource
    ManageAreaService manageAreaService;

    @RequiresPermissions(value = {"manage-area:select", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/selectManageArea/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    public AjaxResult selectManageArea(@PathVariable int pageIndex, @PathVariable int pageSize, QueryManageAreaQuery queryManageAreaQuery){
        return manageAreaService.selectManageArea(pageIndex,pageSize, queryManageAreaQuery);
    }


    @RequiresPermissions(value = {"manage-area:save", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/saveManageArea", method = RequestMethod.POST)
    public AjaxResult saveManageArea(@RequestBody @Validated SaveManageAreaQuery saveManageAreaQuery, BindingResult bindingResult){
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        return manageAreaService.saveManageArea(saveManageAreaQuery);
    }

    @RequiresPermissions(value = {"manage-area:delete", CrmGlobalConstant.SA},logical = Logical.OR)
    @RequestMapping(value = "/deleteManageArea/{id}", method = RequestMethod.DELETE)
    public AjaxResult deleteManageArea(@PathVariable int id){
        return manageAreaService.deleteManageArea(id);
    }

    @RequestMapping(value = "/queryZoneName", method = RequestMethod.GET)
    @RequiresPermissions(value = {"manage-area:select", CrmGlobalConstant.SA},logical = Logical.OR)
    public AjaxResult queryZoneName(String zoneName){
        return manageAreaService.queryZoneName(zoneName);
    }

    @RequestMapping(value = "/queryExistArea", method = RequestMethod.GET)
    @RequiresPermissions(value = {"manage-area:select", CrmGlobalConstant.SA},logical = Logical.OR)
    public AjaxResult queryExistArea(){
        return manageAreaService.queryExistArea();
    }

    @RequestMapping(value = "/queryExistCity", method = RequestMethod.GET)
    public AjaxResult queryExistCity(){
        return manageAreaService.queryExistCity();
    }

}
