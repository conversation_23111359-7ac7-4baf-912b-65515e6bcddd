package net.summerfarm.crm.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.dto.SubAccountDTO;
import net.summerfarm.crm.service.SubAccountService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商城自账号接口
 *
 * <AUTHOR>
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/crm-service/sub-account")
public class SubAccountController {
    @Resource
    SubAccountService subAccountService;

    /**
     * 根据mid获取当前的字账号信息
     *
     * @param mid 店铺id
     * @return
     */
    @RequestMapping(method = RequestMethod.POST)
    public CommonResult<List<SubAccountDTO>> subAccount(Long mid) {
        return subAccountService.subAccount(mid);
    }
}
