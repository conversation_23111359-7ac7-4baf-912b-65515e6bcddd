package net.summerfarm.crm.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.common.util.wechat.AesException;
import net.summerfarm.crm.common.util.wechat.WXBizMsgCrypt;
import net.summerfarm.crm.model.vo.wechat.CustomerCallBackResp;
import net.summerfarm.crm.service.WechatService;
import net.summerfarm.crm.task.handler.WechatTagHandler;
import net.summerfarm.crm.task.handler.WechatTagSyncHandle;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.EscapedErrors;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2023/8/2 15:54
 */
@RestController
@RequestMapping(value = "/crm-service/wechat")
public class WechatCallBackController {
    @Resource
    private CrmConfig config;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private WechatService wechatService;
    @Resource
    WechatTagHandler handler;
    @Resource
    WechatTagSyncHandle syncHandle;
    /**
     * 请求过期时间
     */
    private static final long EXPIRE_TIME = 100;

    @GetMapping("/callback/customer")
    public String getCallBack(@RequestParam("msg_signature") String msgSignature, @RequestParam("timestamp") Long timestamp, @RequestParam("nonce") String nonce, @RequestParam("echostr") String echostr) {
        try {
            // 验证回调结果
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(config.getWechatToken(), config.getWechatEncodingAesKey(), config.getWechatReceiveId());
            String verifyURL = wxcpt.VerifyURL(msgSignature, timestamp.toString(), nonce, echostr);
            String key = "wechat_nonce:" + nonce;
            check(timestamp, key);
            return verifyURL;
        } catch (AesException | BizException e) {
            return e.getMessage();
        }
    }

    @PostMapping("/callback/customer")
    public CommonResult<Void> postCallBack(@RequestParam("msg_signature") String msgSignature, @RequestParam("timestamp") Long timestamp, @RequestParam("nonce") String nonce, @RequestBody String reqData) {
        try {
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(config.getWechatToken(), config.getWechatEncodingAesKey(), config.getWechatReceiveId());
            String msg = wxcpt.DecryptMsg(msgSignature, timestamp.toString(), nonce, reqData);
            String key = "wechat_nonce:" + nonce;
            check(timestamp, key);

            Map<String, Object> xmlMap = XmlUtil.xmlToMap(msg);
            if (CollectionUtils.isEmpty(xmlMap)) {
                return null;
            }
            CustomerCallBackResp resp = BeanUtil.toBean(xmlMap, CustomerCallBackResp.class);
            wechatService.customerCallBack(resp);
        } catch (AesException | BizException e) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, e.getMessage());
        }
        return CommonResult.ok();
    }

    public void check(Long timestamp, String key) {
        if (DateUtil.between(DateUtil.date(timestamp * 1000), DateUtil.date(), DateUnit.SECOND) > EXPIRE_TIME) {
            throw new BizException();
        }
        if (redisTemplate.hasKey(key)) {
            throw new BizException();
        }
    }
}
