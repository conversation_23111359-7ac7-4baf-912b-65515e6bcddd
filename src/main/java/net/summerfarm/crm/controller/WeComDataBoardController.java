package net.summerfarm.crm.controller;

import net.summerfarm.crm.model.query.wecom.WeComBdQuery;
import net.summerfarm.crm.model.vo.weCom.*;
import net.summerfarm.crm.service.WeComDataBoardService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企微数据看板
 *
 * <AUTHOR>
 * @date 2024/2/21 13:46
 */
@RestController
@RequestMapping(value = "/crm-service/weCom/dataBoard")
public class WeComDataBoardController {
    @Resource
    private WeComDataBoardService weComDataBoardService;
    /**
     * 激活状态看板
     *
     * @param query 查询。
     * @return {@link CommonResult}<{@link List}<{@link WeComActivateVo}>>
     */
    @PostMapping(value = "/activate")
    public CommonResult<List<WeComActivateVo>> activateStatus(@RequestBody WeComBdQuery query) {
        return CommonResult.ok(weComDataBoardService.activateStatus(query));
    }

    /**
     * m1 下属激活状态看板
     *
     * @return {@link CommonResult}<{@link List}<{@link WeComActivateStatusVo}>>
     */
    @PostMapping(value = "/activateList")
    public CommonResult<List<WeComActivateStatusVo>> activateStatusList(@RequestBody WeComBdQuery query) {
        return CommonResult.ok(weComDataBoardService.activateStatusList(query));
    }

    /**
     * 企微拉新用户看板
     *
     * @param query 查询。
     * @return {@link CommonResult}<{@link List}<{@link WeComUserSummaryVo}>>
     */
    @PostMapping(value = "/userSummary")
    public CommonResult<List<WeComUserSummaryVo>> userSummary(@RequestBody WeComBdQuery query) {
        return CommonResult.ok(weComDataBoardService.userSummary(query));
    }

    /**
     * 销售&客户沟通看板
     *
     * @param query 查询。
     * @return {@link CommonResult}<{@link List}<{@link WeComCommunicationSummaryVo}>>
     */
    @PostMapping(value = "/communicationSummary")
    public CommonResult<List<WeComCommunicationSummaryVo>> communicationSummary(@RequestBody WeComBdQuery query) {
        return CommonResult.ok(weComDataBoardService.communicationSummary(query));
    }

    /**
     * 企微营销任务
     *
     * @param query 查询。
     * @return {@link CommonResult}<{@link WeComTaskSummaryVo}>
     */
    @PostMapping(value = "/taskSummary")
    public CommonResult<List<WeComTaskSummaryVo>> taskSummary(@RequestBody WeComBdQuery query) {
        return CommonResult.ok(weComDataBoardService.taskSummary(query));
    }
}
