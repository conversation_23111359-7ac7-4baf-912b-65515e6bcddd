package net.summerfarm.crm.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.gaode.GaoDeUtil;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.CluePool;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.domain.MerchantCluePool;
import net.summerfarm.crm.model.dto.*;
import net.summerfarm.crm.model.query.*;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.service.ComfortSendService;
import net.summerfarm.crm.service.MerchantService;
import net.summerfarm.crm.service.VisitPlanService;
import net.xianmu.common.result.CommonResult;
import com.github.pagehelper.PageInfo;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import javax.annotation.Resource;

/**
 * crm客户接口
 *
 * <AUTHOR>
 * @date 2022/6/14 2:00
 */
@RestController
@RequestMapping("/crm-service/merchant")
@Slf4j
public class MerchantController {

    @Resource
    private MerchantService merchantService;
    @Resource
    private VisitPlanService visitPlanService;
    @Resource
    private ComfortSendService comfortSendService;

    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/exist-mname", method = RequestMethod.GET)
    public AjaxResult existMerchantName(Long leadId, String mname) {
        return merchantService.existMerchantName(leadId, mname);
    }

    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/similar", method = RequestMethod.GET)
    public AjaxResult querySimilarMerchant(Merchant merchant) {
        return merchantService.querySimilarMerchant(merchant);
    }

    @GetMapping("/today-register")
    public AjaxResult queryTodayRegister(String mname) {
        return merchantService.queryTodayRegister(mname);
    }

    /**
     * 线索池查询
     * @param merchant
     * @return
     */
    @RequestMapping(value = "/queryCluePool", method = RequestMethod.GET)
    public AjaxResult<List<CluePool>> queryCluePool(Merchant merchant) {
        return merchantService.queryCluePool(merchant);
    }

    @RequestMapping(value = "/queryMerchantAddress", method = RequestMethod.GET)
    public AjaxResult queryMerchantAddress(String keywords, String city) {
        return AjaxResult.getOK(GaoDeUtil.searchAroundAPI(keywords, city, 10));
    }

    /**
     * 待审核门店
     *
     * @param merchantQuery 商人查询
     * @return {@link AjaxResult}
     */
    @PostMapping("/query/pending-review")
    public AjaxResult selectMerchantList(MerchantQuery merchantQuery) {
        return merchantService.selectMerchantList(merchantQuery);
    }

    /**
     * 待配送订单客户总数
     */
    @PostMapping("/query/delivery-merchant-count")
    public CommonResult<DeliveryMerchantVO> deliveryMerchantCount(@RequestBody DeliveryMerchantQuery deliveryMerchantQuery) {
        return merchantService.deliveryMerchantCount(deliveryMerchantQuery);
    }

    /**
     * 待配送订单客户gmv数据
     */
    @PostMapping("/query/delivery-merchant-list")
    public CommonResult<PageInfo<DeliveryMerchantVO>> deliveryMerchantList(@RequestBody DeliveryMerchantQuery deliveryMerchantQuery) {
        return merchantService.deliveryMerchantList(deliveryMerchantQuery);
    }

    /**
     * 商品列表
     */
    @PostMapping("/query/sku-gmv-list")
    public CommonResult<PageInfo<CrmSkuMonthGmvVO>> skuGmvList(@RequestBody SkuMerchantQuery skuMerchantQuery) {
        return merchantService.skuGmvList(skuMerchantQuery);
    }

    @PostMapping("/query/sku-gmv-detail")
    public CommonResult<CrmSkuMonthGmvVO> skuGmvDetail(@RequestBody SkuMerchantQuery skuMerchantQuery) {
        return merchantService.skuGmvDetail(skuMerchantQuery);
    }

    @PostMapping("/query/sku-merchant-list")
    public CommonResult<PageInfo<CrmSkuMerchantGmvVO>> skuMerchantList(@RequestBody SkuMerchantQuery skuMerchantQuery) {
        return merchantService.skuMerchantList(skuMerchantQuery);
    }

    /**
     * 客户详情
     *
     * @param merchantDetailQuery 商户详情查询
     * @return {@link CommonResult}<{@link MerchantVO}>
     */
    @PostMapping("/query/base-detail")
    public CommonResult<MerchantVO> baseDetail(@RequestBody MerchantDetailQuery merchantDetailQuery) {
        return merchantService.selectDetail(merchantDetailQuery);
    }

    /**
     * 查询客户行业属性
     */
    @PostMapping("/query/business")
    public CommonResult<MerchantBusinessVO> queryMerchantBusiness(@RequestBody MIdQuery query) {
        return CommonResult.ok(merchantService.queryMerchantBusiness(query.getMId()));
    }

    /**
     * 设置客户行业属性
     */
    @PostMapping("/upsert/business")
    public CommonResult<Boolean> upsertMerchantBusiness(@RequestBody @Validated MerchantBusinessUpsertDTO upsertDTO) {
        return CommonResult.ok(merchantService.upsertMerchantBusiness(upsertDTO));
    }

    /**
     *
     * 门店详情-加购/搜索记录
     * @param merchantDetailQuery 商户详情查询
     * @return {@link CommonResult}<{@link CrmMerchantMallSearchTopVO}>
     */
    @PostMapping("/query/mall-record")
    public CommonResult<CrmMerchantMallSearchTopVO> mallRecord(@RequestBody MerchantDetailQuery merchantDetailQuery) {
        return merchantService.mallRecord(merchantDetailQuery);
    }

    @PostMapping("/query/gmv-info")
    public CommonResult<CrmMerchantGmvVO> gmvInfo(@RequestBody MerchantDetailQuery merchantDetailQuery) {
        return merchantService.gmvInfo(merchantDetailQuery);
    }

    @PostMapping("/query/merchant-product")
    public CommonResult<PageInfo<CrmSkuMonthGmvVO>> merchantProduct(@RequestBody MerchantDetailQuery merchantDetailQuery) {
        return merchantService.merchantProduct(merchantDetailQuery);
    }

    @PostMapping("/query/detail-category")
    public CommonResult<CustomerAnalysisVO> detailCategory(@RequestBody MerchantDetailQuery merchantDetailQuery) {
        return merchantService.detailCategory(merchantDetailQuery);
    }

    @PostMapping("/query/detail-spu")
    public CommonResult<CustomerAnalysisVO> detailSpu(@RequestBody MerchantDetailQuery merchantDetailQuery) {
        return merchantService.detailSpu(merchantDetailQuery);
    }

    /**
     * 重点客户个数
     *
     * @param keyCustomerQuery
     * @return 个数对象
     */
    @PostMapping("/key-customers/count")
    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<KeyCustomerCountVO> keyCustomerCount(@RequestBody CrmKeyCustomerQuery keyCustomerQuery) {
        return merchantService.keyCustomerCount(keyCustomerQuery.getProvince(),keyCustomerQuery.getCity(),keyCustomerQuery.getArea(), null);
    }



    /**
     * 3天即将掉落
     * ok
     *
     * @param keyCustomerQuery 即将掉落query
     * @return 即将掉落对象
     */
    @PostMapping("/key-customers/danger/query")
    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult dangerCustomer(@RequestBody CrmKeyCustomerQuery keyCustomerQuery) {
        return merchantService.dangerCustomer(keyCustomerQuery);
    }


    /**
     * 注册未下单
     * ok
     *
     * @param keyCustomerQuery 注册未下单query
     * @return 注册未下单对象
     */
    @PostMapping("/key-customers/no-order/query")
    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult noOrderCustomer(@RequestBody CrmKeyCustomerQuery keyCustomerQuery) {
        return merchantService.noOrderCustomer(keyCustomerQuery);
    }

    /**
     * 首单客户
     * ok
     *
     * @param keyCustomerQuery 首单客户query
     * @return 首单客户
     */
    @PostMapping("/key-customers/first-order/query")
    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult firstOrderCustomer(@RequestBody CrmKeyCustomerQuery keyCustomerQuery) {
        return merchantService.firstOrderCustomer(keyCustomerQuery);
    }

    /**
     * 关注客户
     * ok
     *
     * @param keyCustomerQuery 关注客户query
     * @return 关注
     */
    @PostMapping("/key-customers/care/query")
    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult queryCareCustomer(@RequestBody CrmKeyCustomerQuery keyCustomerQuery) {
        return merchantService.queryCareCustomer(keyCustomerQuery);
    }

    /**
     * 关注/取消关注
     *
     * @param crmCareCustomerVO 关注vo
     * @return 返回结果
     */
    @PostMapping("/key-customers/care/update")
    @RequiresPermissions(value = {"crm-newmerchant:edit", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult careCustomer(@RequestBody @Validated CrmCareCustomerVO crmCareCustomerVO) {
        return merchantService.careCustomer(crmCareCustomerVO);
    }

    /**
     * 省心送客户
     *
     * @param crmComfortSendQuery 省心送客户query
     * @return 省心送客户
     */
    @PostMapping("/comfort-send/query")
    @RequiresPermissions(value = {"merchant:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult queryComfortSend(@RequestBody CrmComfortSendQuery crmComfortSendQuery) {
        return comfortSendService.queryComfortSend(crmComfortSendQuery);
    }

    /**
     * 待跟进列表
     *
     * @param input 参数
     * @return 待跟进列表
     */
    @RequiresPermissions(value = {"followUpRecord:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/followup/query")
    public CommonResult<PageInfo<VisitPlanVO>> followup(@RequestBody FollowUpQuery input) {
        return visitPlanService.queryFollowup(input);
    }

    /**
     * 省心送 count
     *
     * @return
     */
    @PostMapping("/comfort-send/count/query")
    public CommonResult sumComfortQueryCount(@RequestBody AreaCodeBdIdQuery codeBdIdQuery) {
        return comfortSendService.sumComfortQueryCount(codeBdIdQuery.getBdId(), codeBdIdQuery.getProvince(),codeBdIdQuery.getCity(),codeBdIdQuery.getArea());
    }

    /**
     * 注册未下单/首单客户/3天内掉落个数
     *
     * @return
     */
    @PostMapping("/key-customers/count/query")
    public CommonResult keyCustomerCount(@RequestBody AreaCodeBdIdQuery codeBdIdQuery) {
        return merchantService.keyCustomerCountVO(codeBdIdQuery);
    }

    /**
     * 今日配送订单数 count
     *
     * @return
     */
    @PostMapping("/query/order-delivery-today")
    public CommonResult<OrderDeliveryTodayDTO> orderDeliveryToday(@RequestBody OrderDeliveryQuery qry) {
        return merchantService.orderDeliveryToday(qry.getProvince(),qry.getCity(),qry.getArea());
    }

    /**
     * 今日配送订单列表
     *
     * @return
     */
    @PostMapping("/query/order-delivery-list")
    public CommonResult<OrderDeliveryRouteVO> orderDeliveryList(@RequestBody OrderDeliveryQuery orderDeliveryQuery) {
        return merchantService.orderDeliveryList(orderDeliveryQuery);
    }

    /**
     * 今日配送订单详情
     *
     * @return
     */
    @PostMapping("/query/order-delivery-detail")
    public CommonResult<OrderDeliveryDetailDTO> orderDeliveryDetail(@RequestBody OrderDeliveryQuery orderDeliveryQuery) {
        return merchantService.orderDeliveryDetail(orderDeliveryQuery);
    }

    /**
     * 查询bd 所属门店标签
     *
     */
    @PostMapping("/query/merchant-tag")
    public CommonResult<MerchantTagListVO> queryBDMerchantTag() {
        return merchantService.queryBDMerchantTag();
    }

    /**
     * 门店详情
     *
     */
    @PostMapping("/detail")
    public CommonResult<MerchantVO> selectMerchantDetail(@RequestBody MerchantVO merchantVO) {
        return merchantService.selectMerchantDetail(merchantVO.getmId());
    }

    /**
     * 门店线索详情
     *
     */
    @PostMapping("/query/pool/detail")
    public CommonResult<MerchantCluePool> selectMerchantPoolDetail(@RequestBody MerchantVO merchantVO) {
        return merchantService.selectMerchantPoolDetail(merchantVO.getmId());
    }

    /**
     * 近60天购买spu
     */
    @PostMapping("/query/recent-spu")
    public CommonResult<List<MerchantRecentSpuDTO>> recentSpu(@RequestBody MIdQuery query) {
        return CommonResult.ok(merchantService.queryRecentSpu(query.getMId()));
    }

    /**
     * 近6个月下单但近30天未下单SPU
     */
    @PostMapping("/query/short-term-lost-spu")
    public CommonResult<List<ShortTermLostSpuDTO>> shortTermLostSpu(@RequestBody MIdQuery query) {
        return CommonResult.ok(merchantService.queryShortTermLostSpu(query.getMId()));
    }

    /**
     * 不可申请品类拓宽券的spu
     */
    @PostMapping("/query/cannot-apply-msc-spu")
    public CommonResult<List<MerchantRecentSpuDTO>> noApplySpu(@RequestBody MIdQuery query) {
        return CommonResult.ok(merchantService.queryCannotApplyMscSpu(query.getMId()));
    }

    /**
     * 已申请且还可重复申请品类拓宽券SPU
     */
    @PostMapping("/query/msc-active-spu")
    public CommonResult<List<MscActiveSpuDTO>> mscActiveSpu(@RequestBody MIdQuery query) {
        return CommonResult.ok(merchantService.queryMscActiveSpu(query.getMId()));
    }

}
