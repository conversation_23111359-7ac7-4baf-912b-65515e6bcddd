package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.mapper.manage.ContactMapper;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.service.ContactService;
import net.xianmu.common.result.CommonResult;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date
 */
@RestController
@RequestMapping(value = "/crm-service/contact")
public class ContactController {
    //内部调用接口 auth防止泄漏
    private static final String AUTH = "XIAN_MU_AUTH_HA1238913O91313123";
    @Resource
    ContactService contactService;

    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public AjaxResult selectContact(Long contactId, String auth) {
        if (!Objects.equals(auth, AUTH)) {
            return AjaxResult.getError();
        }
        return AjaxResult.getOK(contactService.getContactDto(contactId));
    }


    /**
     * 获取联系人信息
     *
     * @param contact 联系
     * @return {@link CommonResult}<{@link Contact}>
     */
    @PostMapping(value = "/select-contact")
    public CommonResult<Contact> selectContact(@RequestBody Contact contact) {
        return contactService.selectContact(contact.getContactId());
    }

}
