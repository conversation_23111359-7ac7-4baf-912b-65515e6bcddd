package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.query.saasorder.SaasOrderDetailQuery;
import net.summerfarm.crm.model.query.saasorder.SaasOrderListQuery;
import net.summerfarm.crm.model.vo.OrderOverviewVO;
import net.summerfarm.crm.model.vo.saasorder.SaasOrderDetailVO;
import net.summerfarm.crm.service.SaasOrderService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 订单管理
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/order")
public class OrderController {

    @Resource
    private SaasOrderService saasOrderService;

    /**
     * 分页查询saas订单列表
     */
    @PostMapping("/query/saas-order-list")
    public CommonResult<PageInfo<OrderOverviewVO>> pageSaasOrder(@RequestBody SaasOrderListQuery query) {
        return CommonResult.ok(saasOrderService.pageSaasOrderList(query));
    }

    /**
     * 查询saas订单详情
     */
    @PostMapping("/query/saas-order-detail")
    public CommonResult<SaasOrderDetailVO> saasOrderDetail(@RequestBody @Validated SaasOrderDetailQuery query) {
        return CommonResult.ok(saasOrderService.getSaasOrderDetail(query.getOrderNo()));
    }
}
