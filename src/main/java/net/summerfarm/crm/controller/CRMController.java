package net.summerfarm.crm.controller;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.enums.FollowUpRelationEnum;
import net.summerfarm.crm.facade.WncQueryFacade;
import net.summerfarm.crm.mapper.manage.ConfigMapper;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.domain.FollowWhiteList;
import net.summerfarm.crm.model.dto.AssignBdUrlDTO;
import net.summerfarm.crm.model.dto.LargeAreaDTO;
import net.summerfarm.crm.model.query.BdExtQuery;
import net.summerfarm.crm.model.query.CrmNewsQuery;
import net.summerfarm.crm.model.query.MerchantQuery;
import net.summerfarm.crm.model.query.NearbyQuery;
import net.summerfarm.crm.model.vo.*;
import net.summerfarm.crm.service.*;
import net.summerfarm.mall.client.req.HelpOrderProductListQueryReq;
import net.summerfarm.mall.client.resp.HelpOrderProductInfoQueryResp;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static net.summerfarm.crm.common.redis.KeyConstant.CLOSE_EXCEL_EXCEL;

/**
 * crm
 *
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Slf4j
@RestController
@RequestMapping(value = "/crm-service/crm")
public class CRMController {

    @Resource
    private FollowUpRelationService followUpRelationService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private BdExtService bdExtService;

    @Resource
    private FollowWhiteListService followWhiteListService;

    @Resource
    private CrmNewsService crmNewsService;
    @Resource
    private ProductsService productsService;
    @Resource
    private CommissionService commissionService;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    CityPermissionService cityPermissionService;
    @Resource
    private WncQueryFacade wncQueryFacade;


    @RequiresPermissions(value = {"merchant-lifecycle:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/detail/operate/{id}", method = RequestMethod.GET)
    public AjaxResult updateOperatingState(@PathVariable int id,Integer operateStatus) {
        return merchantService.updateOperatingState((long)id,operateStatus);
    }

    /**
     * 公海列表
     *
     * @return {@link OpenSeaMerchantVO}
     */
    @RequestMapping(value = "/opensea/{pageIndex}/{pageSize}", method = RequestMethod.GET)
    @RequiresPermissions(value = {"follow-up-relation:list", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult selectOpenSea(@PathVariable int pageIndex, @PathVariable int pageSize, MerchantQuery merchantQuery) {
        return followUpRelationService.selectOpenSea(pageIndex, pageSize, merchantQuery);
    }

    @RequestMapping(value = "/bdassign", method = RequestMethod.POST)
    @RequiresPermissions(value = {"follow-up-relation:bdAssgin", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult bdAssign(String mIds) {
        String[] mIdArray = mIds.split(CrmGlobalConstant.SEPARATING_SYMBOL);
        List<Long> mIdLong = new ArrayList<>();
        for (String str : mIdArray) {
            mIdLong.add(Long.valueOf(str));
        }
        return followUpRelationService.bdAssign(mIdLong);
    }

    /**
     * bd批量分配门店
     */
    @PostMapping(value = "/batch-assign")
    @RequiresPermissions(value = {"follow-up-relation:bdAssgin", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<List<AssignBdResultVo>> batchAssign(@Validated @RequestBody AssignBdUrlDTO file) {
        return merchantService.batchAssign(file.getUrl());
    }

    /**
     * 门店批量倒闭
     */
    @PostMapping(value = "/batch-close")
    public CommonResult<List<AssignBdResultVo>> batchClose(@Validated @RequestBody AssignBdUrlDTO file) {
        return CommonResult.ok(merchantService.batchClose(file.getUrl()));
    }

    /**
     * 检查bd是否有此门店的权限
     */
    @PostMapping(value = "/query/checkBdCityPermission")
    public CommonResult<Boolean> batchClose(@Validated @RequestBody BdCityPermissionVO bdCityPermissionVO) {
        return CommonResult.ok(cityPermissionService.check(bdCityPermissionVO));
    }

    /**
     * 门店批量倒闭excel模版
     */
    @PostMapping(value = "/batch-close/excel")
    public CommonResult<String> batchCloseExcel() {
        return CommonResult.ok(configMapper.selectOne(CLOSE_EXCEL_EXCEL).getValue());
    }


    /**
     * 分配门店模板
     */
    @GetMapping(value = "/assign-template")
    @RequiresPermissions(value = {"follow-up-relation:bdAssgin", CrmGlobalConstant.SA}, logical = Logical.OR)
    public CommonResult<String> assignTemplate() {
        return merchantService.getAssignTemplate();
    }

    @RequestMapping(value = "/user-release",method = RequestMethod.POST)
    @RequiresPermissions(value = {"follow-up-relation:userAssgin", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult userReassign(String mIds, Integer toAdminId, Integer source,  Integer turnAdminId) {
        return reassign(mIds, toAdminId, 0, source, turnAdminId);
    }

    /**
     *  审核分配bd
     * @param mIds 要分配到mid
     * @param toAdminId 要分配到bdid
     * @param type 是否可以跨白名单操作，1：可以
     * @param source 来源是否来自审核 1来自 来自审核要发送钉钉和创建关联关系
     * @return
     */
    @RequestMapping(value = "/release",method = RequestMethod.POST)
    @RequiresPermissions(value = {"follow-up-relation:release", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult reassign(String mIds,Integer toAdminId,Integer type,Integer source, Integer turnAdminId) {
        String[] mIdArray = mIds.split(CrmGlobalConstant.SEPARATING_SYMBOL);
        List<Long> mIdLong = new ArrayList<>(10);
        for (String str : mIdArray) {
            mIdLong.add(Long.valueOf(str));
        }
        return followUpRelationService.reassign(mIdLong, toAdminId, FollowUpRelationEnum.Reason.ACTIVE_SELECTION.getValue(), type, source, turnAdminId);
    }

    @RequestMapping(value = "/whiteList/delete",method = RequestMethod.POST)
    @RequiresPermissions(value = {"follow-up-relation:whiteList-delete", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult updateWhiteList(Long mId){
        return followWhiteListService.deleteWhiteList(mId);
    }

    @RequestMapping(value = "/whiteList/insert",method = RequestMethod.POST)
    @RequiresPermissions(value = {"follow-up-relation:whiteList-insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult saveWhiteList(FollowWhiteList followWhiteList){
        return followWhiteListService.saveWhiteList(followWhiteList);
    }

    @RequestMapping(value = "/whiteList/query/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    @RequiresPermissions(value = {"follow-up-relation:whiteList-query", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult queryWhiteList(@PathVariable int pageIndex, @PathVariable int pageSize, FollowWhiteListVO followWhiteListVO){
        return followWhiteListService.queryWhiteList(pageIndex, pageSize ,followWhiteListVO);
    }

    /**
     * 客户管理-客户列表
     */
    @RequestMapping(value = "/bd/opensea/{pageIndex}/{pageSize}",method = RequestMethod.GET)
    @RequiresPermissions(value = {"merchant-lifecycle:select-all", CrmGlobalConstant.SA}, logical = Logical.OR)
    public AjaxResult queryReassignBdAssign(@PathVariable int pageIndex, @PathVariable int pageSize, MerchantQuery merchantQuery){
        return merchantService.queryMerchantRelease(pageIndex, pageSize, merchantQuery);
    }

    @RequestMapping(value = "/area/bdList",method = RequestMethod.GET)
    public AjaxResult selectBdForAreaList(Integer areaNo){
        return bdExtService.selectBdForAreaList(areaNo);
    }

    @RequestMapping(value = "/bd/whiteList/number",method = RequestMethod.GET)
    public AjaxResult queryWightList(){
        return merchantService.queryMerchantWightLight();
    }

    /**
     * bd 私海列表
     */
    @RequestMapping(value = "/bd/private-sea/{type}",method = RequestMethod.GET)
    public AjaxResult queryBdPrivateSea(@PathVariable int type ,MerchantVO selectKeys){
        return merchantService.queryBdPrivateSea(type,selectKeys);
    }

    @GetMapping("/relation-record/{pageIndex}/{pageSize}")
    public AjaxResult queryRelationRecord(@PathVariable int pageIndex, @PathVariable int pageSize, Integer mId){
        return followUpRelationService.queryRelationRecord(pageIndex,pageSize,mId);
    }


    /**
     * 私海列表
     *
     * @return {@link PrivateSeaVO}
     */
    @GetMapping("/private-sea/{pageIndex}/{pageSize}")
    public AjaxResult queryPrivateSea(@PathVariable int pageIndex, @PathVariable int pageSize, MerchantQuery merchantQuery) {
        return followUpRelationService.queryPrivateSea(pageIndex, pageSize, merchantQuery);
    }

    /**
     * bd负责区域
     *
     * @return {@link AjaxResult}
     */
    @RequestMapping(value = "/bd/area",method = RequestMethod.GET)
    public AjaxResult queryBdArea(){
        return merchantService.queryBdArea();
    }

    /**
     * 附近客户
     *
     * @param req
     * @param pageIndex
     * @param pageSize
     * @return
     */
    @GetMapping("/nearby/{pageIndex}/{pageSize}")
    public AjaxResult<PageInfo<NearbyVO>> nearbyToPage(NearbyQuery req, @PathVariable Integer pageIndex, @PathVariable Integer pageSize){
        return followUpRelationService.merchantNearby(req,pageIndex,pageSize);
    }

    @PostMapping("/query/clue-pool")
    public AjaxResult<NearbyVO> poolDetail(@RequestBody NearbyQuery req){
        return followUpRelationService.queryByEsId(req);
    }

    @PostMapping("/query/newsList")
    public CommonResult<PageInfo<CrmNewsVO>> selectNewsList(@RequestBody CrmNewsQuery crmNewsQuery){
        return crmNewsService.selectList(crmNewsQuery);
    }

    @GetMapping("/selectIsRead")
    public AjaxResult selectIsRead(Integer adminId,Integer position){
        return AjaxResult.getOK(crmNewsService.selectIsRead(adminId,position));
    }

    /**
     * 查询销售信息
     */
    @GetMapping("/queryBdInfo")
    public AjaxResult queryBdInfo(BdExtQuery bdExtQuery){
        return bdExtService.queryBdInfo(bdExtQuery);
    }

    /**
     * 查询销售区域信息
     */
    @GetMapping("/queryBdArea")
    public AjaxResult queryBdArea(BdExtQuery bdExtQuery){
        return bdExtService.queryBdArea(bdExtQuery);
    }

    /**
     * 查询销售城市
     */
    @PostMapping("/query/bd-city")
    public CommonResult<List<TableArea>> queryBdCity(){
        return CommonResult.ok(commissionService.selectBdCities());
    }

    @PostMapping("/query/bd-role")
    public CommonResult<Boolean> queryBdRole( ){
        return bdExtService.queryBdRole();
    }

    /**
     * 服务器当前时间
     */
    @PostMapping(value = "/now")
    public CommonResult<Long> now() {
        return CommonResult.ok(System.currentTimeMillis());
    }

    /**
     * 所有大区
     */
    @PostMapping(value = "/large-area/larges")
    public CommonResult<List<LargeAreaDTO>> listLargeArea() {
        return CommonResult.ok(bdExtService.selectAllLargeArea());
    }

    /**
     * 匹配截单围栏
     */
    @PostMapping(value = "/area/match")
    public CommonResult<Integer> matchAreaNo(@RequestBody Contact contact){
        AreaQueryResp area = bdExtService.matchAreaNo(contact);
        if (area==null){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR,"地址不在配送范围");
        }
        return CommonResult.ok(area.getAreaNo());
    }


    /**
     * 匹配截单围栏
     */
    @PostMapping(value = "/pop/area/match")
    public CommonResult<Integer> popMatchAreaNo(@RequestBody Contact contact) {
        if (contact == null || contact.getCity() == null || contact.getArea() == null) {
            log.error("请求参数异常。contact:{}", JSON.toJSONString(contact));
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "地址信息异常");
        }
        Integer areaNo = wncQueryFacade.selectAreaNoByAddressForPOP(contact.getCity(), contact.getArea());
        if (areaNo == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "地址不在配送范围");
        }
        return CommonResult.ok(areaNo);
    }

    /**
     * 商品列表
     *
     */
    @PostMapping(value = "/inventory")
    public CommonResult<PageInfo<HelpOrderProductInfoQueryResp>> inventory(@RequestBody HelpOrderProductListQueryReq req){
        return productsService.selectSkuList(req);
    }

    /**
     * 私海类型
     * 用于判断销售私海下的客户包含哪些类型的客户(pop还是鲜沐还是都有)
     */
    @PostMapping(value = "/query/bd-private-sea-type")
    public CommonResult<PrivateSeaType> queryBdPrivateSeaType(){
        return CommonResult.ok(bdExtService.queryBdPrivateSeaType());
    }
}
