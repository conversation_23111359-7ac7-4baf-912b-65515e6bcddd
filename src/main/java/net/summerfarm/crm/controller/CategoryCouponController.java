package net.summerfarm.crm.controller;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Del;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.enums.CategoryQuotaEnum;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.model.domain.CategoryCouponQuota;
import net.summerfarm.crm.model.domain.CategoryCouponQuotaChange;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.CoreProductBasePrice;
import net.summerfarm.crm.model.dto.*;
import net.summerfarm.crm.model.query.BasePriceQuery;
import net.summerfarm.crm.model.query.CategoryQuotaChangeQuery;
import net.summerfarm.crm.model.query.monthLiving.QuotaListQuery;
import net.summerfarm.crm.model.query.monthLiving.QuotaRatioQuery;
import net.summerfarm.crm.model.vo.CategoryCouponQuotaVO;
import net.summerfarm.crm.service.CategoryCouponService;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 品类券额度控制
 *
 * <AUTHOR>
 * @Date 2023/2/28 17:55
 */
@RestController
@RequestMapping(value = "/crm-service/category-coupon")
public class CategoryCouponController {

    @Resource
    private BaseService baseService;

    private static class BasePricePermission {
        boolean basePricePermission = false;
        boolean merchantSituationCategoryBasePricePermission = false;

        static BasePricePermission getPermission(String basePricePermission, String merchantSituationCategoryBasePricePermission) {
            Subject shrioSubject = SecurityUtils.getSubject();
            if (shrioSubject == null) {
                return new BasePricePermission();
            }
            BasePricePermission permission = new BasePricePermission();
            ShiroUser user = (ShiroUser) shrioSubject.getPrincipal();
            List<Integer> roleIds = user.getRoleIds();
            boolean isSA = CollUtil.containsAny(roleIds, CrmGlobalConstant.SA_ROLE_ID);;
            permission.basePricePermission = shrioSubject.isPermitted(basePricePermission) || isSA;
            permission.merchantSituationCategoryBasePricePermission = shrioSubject.isPermitted(merchantSituationCategoryBasePricePermission) || isSA;
            return permission;
        }
    }

    @Resource
    CategoryCouponService categoryCouponService;


    /**
     * 核心品类底价列表
     *
     * @param query 查询条件
     * @return 结果
     */
    @RequiresPermissions(value = {"base-price:select", "ms-category-base-price:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price/list")
    public CommonResult<PageInfo<CoreProductBasePrice>> listBasePrice(@RequestBody BasePriceQuery query) {
        BasePricePermission permission = BasePricePermission.getPermission("base-price:select", "ms-category-base-price:select");

        PageInfo<CoreProductBasePrice> basePriceList = categoryCouponService.listBasePrice(query, permission.basePricePermission, permission.merchantSituationCategoryBasePricePermission);
        return CommonResult.ok(basePriceList);
    }

    /**
     * 更新核心品类底价
     *
     * @param basePrice 更新内容
     */
    @RequiresPermissions(value = {"base-price:update", "ms-category-base-price:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price/update")
    public CommonResult<Void> updateBasePrice(@Validated(value = {Update.class}) @RequestBody CoreProductBasePrice basePrice) {
        BasePricePermission permission = BasePricePermission.getPermission("base-price:update", "ms-category-base-price:update");

        return categoryCouponService.updateBasePrice(basePrice, permission.basePricePermission, permission.merchantSituationCategoryBasePricePermission);
    }

    /**
     * 删除核心品类底价
     */
    @RequiresPermissions(value = {"base-price:delete", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price/del")
    public CommonResult<Void> delBasePrice(@Validated(value = {Del.class}) @RequestBody BasePriceDTO basePriceDTO) {
        categoryCouponService.delBasePrice(basePriceDTO.getId());
        return CommonResult.ok();
    }

    /**
     * 批量删除核心品类底价
     *
     * @param fileDTO 导入文件
     */
    @RequiresPermissions(value = {"base-price:delete", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price/upsert/batch-del")
    public CommonResult<String> batchDelBasePrice(@Validated(value = {Del.class}) @RequestBody BasePriceFileDTO fileDTO) {
        return categoryCouponService.batchDelBasePrice(fileDTO);
    }

    /**
     * 核心品类底价新增
     *
     * @param basePriceDTO
     */
    @RequiresPermissions(value = {"base-price:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price/insert")
    public CommonResult<Void> insertBasePrice(@Validated(value = {Add.class}) @RequestBody BasePriceDTO basePriceDTO) {
        BasePricePermission permission = BasePricePermission.getPermission("base-price:insert", "ms-category-base-price:insert");

        categoryCouponService.insertBasePrice(basePriceDTO.getBasePrice(), permission.merchantSituationCategoryBasePricePermission);
        return CommonResult.ok();
    }

    /**
     * 核心品类底价批量新增
     *
     * @param fileDTO 导入文件
     */
    @RequiresPermissions(value = {"base-price:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price/upsert/batch-insert")
    public CommonResult<String> batchInsertBasePrice(@Validated(value = {Add.class}) @RequestBody BasePriceFileDTO fileDTO) {
        BasePricePermission permission = BasePricePermission.getPermission("base-price:insert", "ms-category-base-price:insert");

        return categoryCouponService.batchInsertBasePrice(fileDTO, permission.merchantSituationCategoryBasePricePermission);
    }

    /**
     * 品类拓宽规则设置
     *
     * @param proportion 返还比例
     */
    @RequiresPermissions(value = {"marketing-rebate:setting", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/marketing-rebate/setting")
    public CommonResult<Void> marketingRebateSetting(@Validated(value = {Update.class}) @RequestBody CategoryProportionDTO proportion) {
        categoryCouponService.configSetting(ConfigValueEnum.MARKETING_QUOTA_RETURN_RULE.getKey(), proportion.getRewardProportion().toString());
        return CommonResult.ok();
    }

    /**
     * 获取品类拓宽
     */
    @RequiresPermissions(value = {"marketing-rebate", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/marketing-rebate")
    public CommonResult<String> getMarketingRebate() {
        Config config = categoryCouponService.getConfig(ConfigValueEnum.MARKETING_QUOTA_RETURN_RULE.getKey());
        return CommonResult.ok(config.getValue());
    }

    /**
     * 非鲜果底价比例
     *
     * @param proportion 底价规则比例
     */
    @RequiresPermissions(value = {"base-price-rule:setting", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price-rule/setting")
    public CommonResult<Void> basePriceRule(@Validated(value = {Add.class}) @RequestBody CategoryProportionDTO proportion) {
        categoryCouponService.configSetting(ConfigValueEnum.CORE_PRODUCT_BASE_PRICE.getKey(), proportion.getBasePriceProportion().toString());
        return CommonResult.ok();
    }

    /**
     * 非鲜果获取底价比例规则
     */
    @RequiresPermissions(value = {"base-price-rule", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/base-price-rule")
    public CommonResult<String> getBasePriceRule() {
        Config config = categoryCouponService.getConfig(ConfigValueEnum.CORE_PRODUCT_BASE_PRICE.getKey());
        return CommonResult.ok(config.getValue());
    }

    /**
     * 鲜果底价比例
     *
     * @param proportion 底价规则比例
     */
    @RequiresPermissions(value = {"base-price-rule:setting", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/fruit-base-price-rule/upsert/setting")
    public CommonResult<Void> baseFruitPriceRule(@Validated(value = {Add.class}) @RequestBody CategoryProportionDTO proportion) {
        categoryCouponService.configSetting(ConfigValueEnum.FRUIT_CORE_PRODUCT_BASE_PRICE.getKey(), proportion.getBasePriceProportion().toString());
        return CommonResult.ok();
    }

    /**
     * 鲜果获取底价比例规则
     */
    @RequiresPermissions(value = {"base-price-rule", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/fruit-base-price-rule/query")
    public CommonResult<String> getFruitBasePriceRule() {
        Config config = categoryCouponService.getConfig(ConfigValueEnum.FRUIT_CORE_PRODUCT_BASE_PRICE.getKey());
        return CommonResult.ok(config.getValue());
    }

    /**
     * 品类券额度列表
     *
     * @param page 分页信息
     * @return 额度列表 CategoryCouponQuotaVO.class
     */
    @RequiresPermissions(value = {"category-quota:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/list")
    public CommonResult<PageInfo<CategoryCouponQuotaVO>> quotaList(@RequestBody QuotaListQuery page) {
        PageInfo<CategoryCouponQuotaVO> quotaList = categoryCouponService.listQuota(page);
        return CommonResult.ok(quotaList);
    }

    /**
     * 可被分配人员列表
     *
     * @return 人员列表 CategoryCouponQuota.class
     */
    @RequiresPermissions(value = {"category-quota:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/list-admin")
    public CommonResult<List<CategoryCouponQuota>> listAdmin() {
        List<CategoryCouponQuota> quotaList = categoryCouponService.listAdmin();
        return CommonResult.ok(quotaList);
    }

    /**
     * 登录用户的额度详情
     */
    @RequiresPermissions(value = {"category-quota:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/detail")
    public CommonResult<CategoryCouponQuotaVO> quotaDetail() {
        List<CategoryCouponQuotaVO> couponQuotaList = categoryCouponService.quotaDetail(CategoryQuotaEnum.QuotaType.CATEGORY.getCode());
        return CommonResult.ok(couponQuotaList.stream().findFirst().orElse(null));
    }

    /**
     * CRM登录用户的额度详情
     */
    @RequiresPermissions(value = {"category-quota:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/crm/detail")
    public CommonResult<List<CategoryCouponQuotaVO>> crmQuotaDetail() {
        return categoryCouponService.crmQuotaDetail();
    }

    /**
     * bd 额度变化明细
     *
     * @param query
     * @return 奖励列表 CategoryCouponQuotaVO.class
     */
    @RequiresPermissions(value = {"category-quota:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/change")
    public CommonResult<PageInfo<CategoryCouponQuotaChange>> quotaChange(@Valid @RequestBody CategoryQuotaChangeQuery query) {
        PageInfo<CategoryCouponQuotaChange> quotaChange = categoryCouponService.listQuotaChange(query);
        return CommonResult.ok(quotaChange);
    }

    /**
     * 品类券额度设置
     */
    @RequiresPermissions(value = {"category-quota:setting", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/setting")
    public CommonResult<Void> quotaSetting(@Validated(value = {Update.class}) @RequestBody CategoryQuotaDTO quota) {
        return categoryCouponService.quotaSetting(quota);
    }

    /**
     * 品类券额度划分
     */
    @RequiresPermissions(value = {"category-quota:division", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/division")
    public CommonResult<Void> quotaDivision(@Validated(value = {Update.class}) @RequestBody CategoryQuotaDTO quota) {
        return categoryCouponService.divisionSetting(quota);
    }

    /**
     * 新增人员额度
     */
    @RequiresPermissions(value = {"category-quota:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/insert")
    public CommonResult<Void> quotaInsert(@Validated(value = {Add.class}) @RequestBody CategoryQuotaDTO quota) {
        return categoryCouponService.quotaInsert(quota);
    }

    /**
     * 删除额度
     */
    @RequiresPermissions(value = {"category-quota:delete", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/del")
    public CommonResult<Void> quotaDelete(@Validated(value = {Del.class}) @RequestBody CategoryQuotaDTO quota) {
        return categoryCouponService.deleteQuota(quota.getAdminId(), quota.getQuotaType());
    }

    /**
     * 有底价的大区
     */
    @RequiresPermissions(value = {"base-price-area:list", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/listLargeArea")
    public CommonResult<List<LargeAreaDTO>> listLargeArea() {
        return CommonResult.ok(categoryCouponService.listLargeArea());
    }

    /**
     * 费比设置
     */
    @RequiresPermissions(value = {"category-quota:ratio-setting", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping("/quota/ratio-setting")
    public CommonResult<Void> ratioSetting(@Validated @RequestBody QuotaRatioQuery ratioQuery) {
        categoryCouponService.ratioSetting(ratioQuery);
        return CommonResult.ok();
    }
}
