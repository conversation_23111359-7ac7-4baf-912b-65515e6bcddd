package net.summerfarm.crm.controller.request;

import lombok.Data;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.task.CrmDelayReleaseRule;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 比对客户释放时间请求参数
 *
 * <AUTHOR>
 */
@Data
public class CompareReleaseDateRequest {

    /**
     * mId列表
     */
    @NotEmpty(message = "mId列表不能为空")
    private List<Long> mIds;

    /**
     * 计算开始时间
     */
    private LocalDateTime calculateStartTime = LocalDate.of(2016, 6, 19).atStartOfDay();

    /**
     * 延期释放规则列表
     */
    private List<CrmDelayReleaseRule> delayReleaseRules;

    /**
     * 计算其他掉落客户的释放时间时，是否使用最大释放时间
     * 默认为true（取最大释放时间），false为取最小释放时间
     */
    private boolean otherCustomUseMaxReleaseDate = true;

    /**
     * 计算首单客户和公转私客户的释放时间时，是否使用最大释放时间
     * 默认为true（取最大释放时间），false为取最小释放时间
     */
    private boolean firstBuyerAndOpenToPrivateUseMaxReleaseDate = true;
}