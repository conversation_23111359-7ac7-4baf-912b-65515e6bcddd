package net.summerfarm.crm.controller;

import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Del;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.InvoiceConfig;
import net.summerfarm.crm.model.query.InvoiceConfigListQuery;
import net.summerfarm.crm.model.vo.InvoiceConfigVo;
import net.summerfarm.crm.service.InvoiceConfigService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 门店抬头
 *
 * <AUTHOR>
 * @Date 2023/7/17 16:07
 */
@RestController
@RequestMapping(value = "/crm-service/invoice-config")
public class InvoiceConfigController {
    @Resource
    InvoiceConfigService invoiceConfigService;
    /**
     * 发票抬头列表
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link InvoiceConfigVo}>
     */
    @RequiresPermissions(value = {"invoiceConfig:list", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/query/list-invoice")
    public CommonResult<List<InvoiceConfigVo>> listInvoiceConfig(@RequestBody InvoiceConfigListQuery query) {
        return invoiceConfigService.listInvoiceConfig(query);
    }

    /**
     * 修改发票抬头
     *
     * @return {@link CommonResult}<{@link InvoiceConfigVo}>
     */
    @RequiresPermissions(value = {"invoiceConfig:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/upsert/update")
    public CommonResult<Void> updateInvoiceConfig(@Validated(value = {Update.class}) @RequestBody InvoiceConfig config) {
        return invoiceConfigService.updateInvoiceConfig(config);
    }

    /**
     * 新增发票抬头
     *
     * @return {@link CommonResult}<{@link InvoiceConfigVo}>
     */
    @RequiresPermissions(value = {"invoiceConfig:insert", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/upsert/add")
    public CommonResult<Void> addInvoiceConfig(@Validated(value = {Add.class}) @RequestBody InvoiceConfig config) {
        return invoiceConfigService.insertInvoiceConfig(config);
    }
}
