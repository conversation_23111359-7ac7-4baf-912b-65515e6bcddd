package net.summerfarm.crm.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.model.dto.notice.CrmNoticeDTO;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 通知信息
 */
@Slf4j
@RestController
@RequestMapping("/crm-service/notice")
public class CrmNoticeController {

    @Autowired
    private CrmConfig crmConfig;

    /**
     * 获取通知信息
     */
    @PostMapping("/fetch")
    public CommonResult<List<CrmNoticeDTO>> fetchNoticeInfo() {
        return CommonResult.ok(crmConfig.getWechatNotice());
    }
}
