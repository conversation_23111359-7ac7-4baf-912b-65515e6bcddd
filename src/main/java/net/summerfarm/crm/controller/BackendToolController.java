package net.summerfarm.crm.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.controller.request.CompareReleaseDateRequest;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.service.FollowUpReleaseTimeCompareService;
import net.summerfarm.crm.task.CrmReleaseTimeCompareResult;
import net.xianmu.common.result.CommonResult;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.google.common.collect.Lists;

import javax.validation.Valid;

import java.util.ArrayList;
import java.util.List;

/**
 * 后台工具控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/crm-service/backend-tool")
public class BackendToolController {

    @Autowired
    private FollowUpRelationMapper followUpRelationMapper;

    @Autowired
    private FollowUpReleaseTimeCompareService followUpReleaseTimeCompareService;

    /**
     * 比对客户释放时间
     *
     * @param request 比对请求参数
     * @return 比对结果
     */
    @PostMapping("/compare-release-date")
    public CommonResult<List<CrmReleaseTimeCompareResult>> compareReleaseDate(
            @Valid @RequestBody CompareReleaseDateRequest request) {
        log.info("开始比对客户释放时间，mIds: {}", request.getMIds());

        List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectByReassignAndMIdIn(Boolean.FALSE, request.getMIds());
        if (CollectionUtils.isEmpty(followUpRelations)) {
            return CommonResult.ok(new ArrayList<>());
        }
        List<CrmReleaseTimeCompareResult> result = new ArrayList<>();
        for (FollowUpRelation followUpRelation : followUpRelations) {
            result.addAll(followUpReleaseTimeCompareService.compareReleaseTimeByBdAndMids(followUpRelation.getAdminId(),
                    Lists.newArrayList(followUpRelation), request.getCalculateStartTime(), request.getDelayReleaseRules(),
                    request.isOtherCustomUseMaxReleaseDate(),
                    request.isFirstBuyerAndOpenToPrivateUseMaxReleaseDate()));
        }

        log.info("客户释放时间比对完成");
        return CommonResult.ok(result);
    }
}