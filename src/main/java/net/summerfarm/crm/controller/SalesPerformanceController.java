package net.summerfarm.crm.controller;

import com.github.pagehelper.PageInfo;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.model.convert.salesdata.DashboardModuleConverter;
import net.summerfarm.crm.model.query.BdIdQuery;
import net.summerfarm.crm.model.query.salesperformance.*;
import net.summerfarm.crm.model.vo.saledata.CRMDashboardModule;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionVO;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationTotalVO;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerSummaryVO;
import net.summerfarm.crm.service.salesperformance.CategoryPromotionService;
import net.summerfarm.crm.service.salesperformance.CommissionEstimationService;
import net.summerfarm.crm.service.salesperformance.ExcessSpuCustomerService;
import net.summerfarm.crm.service.salesperformance.HighValueCustomerService;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * 销售绩效板块
 */
@RestController
@RequestMapping(value = "/crm-service/sales-performance")
public class SalesPerformanceController {

    @Resource
    private CommissionEstimationService commissionEstimationService;
    @Resource
    private HighValueCustomerService highValueCustomerService;
    @Resource
    private ExcessSpuCustomerService excessSpuCustomerService;
    @Resource
    private CategoryPromotionService categoryPromotionService;
    @Resource
    private DashboardModuleConverter dashboardModuleConverter;


    // -------------------------------- 佣金预估 --------------------------------

    /**
     * 佣金预估总额.
     * 普通销售看到的是他自己客户的总额.
     * m1 看到的是下属销售总额之和.
     * m2及以上不返回.
     *
     * @return 佣金预估总额
     */
    @PostMapping("/query/commission-estimation-total")
    public CommonResult<CommissionEstimationTotalVO> getCommissionEstimationTotal() {
        return CommonResult.ok(commissionEstimationService.getCommissionEstimationTotal());
    }

    /**
     * 销售佣金预估总额列表.
     * 为m1 销售列出下属bd 佣金总额
     * 普通bd, m2及以上不返回
     *
     * @return 佣金预估总额列表
     */
    @PostMapping("/list/commission-estimation-total")
    public CommonResult<List<CommissionEstimationTotalVO>> listCommissionEstimationTotalForManager() {
        return CommonResult.ok(commissionEstimationService.listCommissionEstimationTotalForManager());
    }

    /**
     * bd的佣金预估信息明细.
     * 如果bdId为空, 则返回当前登录bd的佣金预估信息.
     *
     * @param bdIdQuery bdId, 如果为空, 则返回当前登录bd的佣金预估信息
     * @return bd的佣金预估信息
     */
    @PostMapping("/query/bd-commission-estimation")
    public CommonResult<CommissionEstimationDetailVO> getBdCommissionEstimation(@RequestBody @NotNull BdIdQuery bdIdQuery) {
        return CommonResult.ok(commissionEstimationService.getBdCommissionEstimation(bdIdQuery.getBdId()));
    }

    // -------------------------------- 高价值客户 --------------------------------

    /**
     * 获取高价值客户汇总数据.
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @return 高价值客户汇总数据
     */
    @PostMapping("/query/high-value-customer-summary")
    public CommonResult<CRMDashboardModule> getHighValueCustomerSummary() {
        // 获取高价值客户汇总数据
        HighValueCustomerSummaryVO summaryVO = highValueCustomerService.getHighValueCustomerSummary();

        // 转换为CRMDashboardModule并返回，使用Optional防止NPE
        CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(
                summaryVO,
                ConfigValueEnum.CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG,
                Optional.ofNullable(summaryVO).map(HighValueCustomerSummaryVO::getBdId).map(String::valueOf).orElse(""),
                Optional.ofNullable(summaryVO).map(HighValueCustomerSummaryVO::getBdName).orElse(""));

        return CommonResult.ok(module);
    }

    /**
     * 销售高价值客户汇总列表
     * 为 m1 列出下属 bd 高价值客户汇总
     * 普通bd, m2及以上不返回
     */
    @PostMapping("/list/high-value-customer-summary")
    public CommonResult<List<CRMDashboardModule>> listHighValueCustomerSummaryForManager(@RequestBody @Validated BdHighValueCustomerSummaryListQuery query) {
        // 获取高价值客户汇总列表
        List<HighValueCustomerSummaryVO> summaryVOs = highValueCustomerService.listHighValueCustomerSummaryForManager(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules = dashboardModuleConverter.convertToModuleList(
                summaryVOs,
                ConfigValueEnum.CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG,
                HighValueCustomerSummaryVO::getBdId,
                HighValueCustomerSummaryVO::getBdName);

        return CommonResult.ok(modules);
    }

    /**
     * 高价值客户详情
     * 查询某销售下所有高价值客户, 如果bdId为空, 则返回当前登录bd的高价值客户
     *
     * @param query 查询参数
     * @return 高价值客户汇总数据
     */
    @PostMapping("/query/high-value-customer")
    public CommonResult<PageInfo<CRMDashboardModule>> getBdHighValueCustomerSummary(@RequestBody @Validated HighValueCustomerQuery query) {
        // 获取高价值客户详情
        PageInfo<HighValueCustomerDetailVO> pageInfo = highValueCustomerService.getBdHighValueCustomerDetail(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result = dashboardModuleConverter.convertToModulePageInfo(
                pageInfo,
                ConfigValueEnum.CRM_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG,
                HighValueCustomerDetailVO::getMId,
                HighValueCustomerDetailVO::getMname);

        return CommonResult.ok(result);
    }


    // -------------------------------- 超标SPU客户 --------------------------------

    /**
     * 获取超标SPU客户汇总数据.
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @return 超标SPU客户汇总数据
     */
    @PostMapping("/query/excess-spu-customer-summary")
    public CommonResult<CRMDashboardModule> getExcessSpuCustomerSummary() {
        // 获取超标SPU客户汇总数据
        ExcessSpuCustomerSummaryVO summaryVO = excessSpuCustomerService.getExcessSpuCustomerSummary();

        // 转换为CRMDashboardModule并返回，使用Optional防止NPE
        CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(
                summaryVO,
                ConfigValueEnum.CRM_EXCESS_SPU_CUSTOMER_SUMMARY_CONFIG,
                Optional.ofNullable(summaryVO).map(ExcessSpuCustomerSummaryVO::getBdId).map(String::valueOf).orElse(""),
                Optional.ofNullable(summaryVO).map(ExcessSpuCustomerSummaryVO::getBdName).orElse(""));

        return CommonResult.ok(module);
    }

    /**
     * 销售超标SPU客户汇总列表
     * 为 m1 列出下属 bd 超标SPU客户汇总
     * 普通bd, m2及以上不返回
     */
    @PostMapping("/list/excess-spu-customer-summary")
    public CommonResult<List<CRMDashboardModule>> listExcessSpuCustomerSummaryForManager(@RequestBody @Validated BdExcessSpuCustomerSummaryListQuery query) {
        // 获取超标SPU客户汇总列表
        List<ExcessSpuCustomerSummaryVO> summaryVOs = excessSpuCustomerService.listExcessSpuCustomerSummaryForManager(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules = dashboardModuleConverter.convertToModuleList(
                summaryVOs,
                ConfigValueEnum.CRM_EXCESS_SPU_CUSTOMER_SUMMARY_CONFIG,
                ExcessSpuCustomerSummaryVO::getBdId,
                ExcessSpuCustomerSummaryVO::getBdName);

        return CommonResult.ok(modules);
    }

    /**
     * 超标SPU客户详情
     * 查询某销售下所有超标SPU客户, 如果bdId为空, 则返回当前登录bd的超标SPU客户
     *
     * @param query 查询参数
     * @return 超标SPU客户汇总数据
     */
    @PostMapping("/query/bd-excess-spu-customer")
    public CommonResult<PageInfo<CRMDashboardModule>> getBdExcessSpuCustomerSummary(@RequestBody @Validated ExcessSpuCustomerQuery query) {
        // 获取超标SPU客户详情
        PageInfo<ExcessSpuCustomerDetailVO> pageInfo = excessSpuCustomerService.getBdExcessSpuCustomerDetail(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result = dashboardModuleConverter.convertToModulePageInfo(
                pageInfo,
                ConfigValueEnum.CRM_EXCESS_SPU_CUSTOMER_DETAIL_CONFIG,
                ExcessSpuCustomerDetailVO::getMId,
                ExcessSpuCustomerDetailVO::getMname);

        return CommonResult.ok(result);
    }


    // -------------------------------- 品类推广 --------------------------------

    /**
     * 获取品类推广汇总数据.
     * 普通销售看到的是自己客户汇总
     * m1看到所有下属销售客户汇总之和
     * m2及以上不返回
     *
     * @return 品类推广汇总数据
     */
    @PostMapping("/query/category-promotion-summary")
    public CommonResult<CRMDashboardModule> getCategoryPromotionSummary(@RequestBody CategoryPromotionSummaryQuery query) {
        // 获取品类推广汇总数据
        CategoryPromotionSummaryVO summaryVO = categoryPromotionService.getCategoryPromotionSummary(query);

        // 转换为CRMDashboardModule并返回，使用Optional防止NPE
        CRMDashboardModule module = dashboardModuleConverter.convertToCRMDashboardModule(
                summaryVO,
                ConfigValueEnum.CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG,
                Optional.ofNullable(summaryVO).map(CategoryPromotionSummaryVO::getBdId).map(String::valueOf).orElse(""),
                Optional.ofNullable(summaryVO).map(CategoryPromotionSummaryVO::getBdName).orElse(""));

        return CommonResult.ok(module);
    }

    /**
     * 销售品类推广汇总列表
     * 为 m1 列出下属 bd 品类推广汇总
     * 普通bd, m2及以上不返回
     */
    @PostMapping("/list/category-promotion-summary")
    public CommonResult<List<CRMDashboardModule>> listCategoryPromotionSummaryForManager(@RequestBody @Validated BdCategoryPromotionSummaryListQuery query) {
        // 获取品类推广汇总列表
        List<CategoryPromotionSummaryVO> summaryVOs = categoryPromotionService.listCategoryPromotionSummaryForManager(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules = dashboardModuleConverter.convertToModuleList(
                summaryVOs,
                ConfigValueEnum.CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG,
                CategoryPromotionSummaryVO::getBdId,
                CategoryPromotionSummaryVO::getBdName);

        return CommonResult.ok(modules);
    }

    /**
     * 销售维度(也就是所有门店汇总)品类推广数据详情
     *
     * @param query 查询参数
     * @return 品类推广数据详情
     */
    @PostMapping("/query/category-promotion-data")
    public CommonResult<PageInfo<CRMDashboardModule>> getCategoryPromotionData(@RequestBody @Validated CategoryPromotionQuery query) {
        // 获取品类推广详情
        PageInfo<CategoryPromotionDetailVO> pageInfo = categoryPromotionService.getCategoryPromotionData(query);

        // 使用通用方法转换为CRMDashboardModule分页数据并返回
        PageInfo<CRMDashboardModule> result = dashboardModuleConverter.convertToModulePageInfo(
                pageInfo,
                ConfigValueEnum.CRM_CATEGORY_PROMOTION_DATA_CONFIG,
                CategoryPromotionDetailVO::getMId,
                CategoryPromotionDetailVO::getMname);

        return CommonResult.ok(result);
    }

    /**
     * 获取门店维度(也就是单个门店购买的商品)品类推广数据.
     *
     * @param query 查询参数
     * @return 品类推广数据
     */
    @PostMapping("/query/merchant-category-promotion")
    public CommonResult<List<CRMDashboardModule>> getMerchantCategoryPromotion(@RequestBody @Validated MerchantCategoryPromotionQuery query) {
        // 获取门店维度品类推广数据
        List<MerchantCategoryPromotionVO> merchantCategoryPromotionVOs = categoryPromotionService.getMerchantCategoryPromotion(query);

        // 使用通用方法转换为CRMDashboardModule列表并返回
        List<CRMDashboardModule> modules = dashboardModuleConverter.convertToModuleList(
                merchantCategoryPromotionVOs,
                ConfigValueEnum.CRM_MERCHANT_CATEGORY_PROMOTION_CONFIG,
                MerchantCategoryPromotionVO::getSpuGroup,
                MerchantCategoryPromotionVO::getSpuGroup);

        return CommonResult.ok(modules);
    }

    /**
     * 获取本期品类推广SPU分组列表, 用于下拉选择
     *
     * @return 品类推广SPU分组
     */
    @PostMapping("/list/category-promotion-spu-group-list")
    public CommonResult<List<String>> getCategoryPromotionSpuGroupList() {
        return CommonResult.ok(categoryPromotionService.getCategoryPromotionSpuGroupList());
    }
}
