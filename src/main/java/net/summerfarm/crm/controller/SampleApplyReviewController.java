package net.summerfarm.crm.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.crm.common.base.BaseService;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.domain.SampleApplyReview;
import net.summerfarm.crm.model.query.SampleApplyQuery;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import net.summerfarm.crm.model.vo.sample.CheckSampleDto;
import net.summerfarm.crm.service.SampleApplyReviewService;
import net.summerfarm.enums.RedissonLockKey;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Slf4j
@RestController
@RequestMapping("/crm-service/sampleApplyReview")
public class SampleApplyReviewController {

    @Resource
    private SampleApplyReviewService sampleApplyReviewService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private BaseService baseService;

    @RequiresPermissions(value = {"sampleApplyReview:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/selectSampleByBD/{pageIndex}/{pageSize}" ,method = RequestMethod.GET )
    public AjaxResult selectSampleApplyReviewVO(@PathVariable int pageIndex, @PathVariable int pageSize, SampleApply sampleApply) {
        return sampleApplyReviewService.selectSampleApplyReview(pageIndex,pageSize,sampleApply);
    }

    @RequiresPermissions(value = {"sampleApplyReview:selectOne", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}" ,method = RequestMethod.GET )
    public AjaxResult selectSampleApplyReviewVO(@PathVariable int id) {
        return sampleApplyReviewService.selectSampleApplyReviewVO(id);
    }

    @RequiresPermissions(value = {"sampleApplyReview:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/review" ,method = RequestMethod.POST )
    public AjaxResult sampleApplyReview(@RequestBody SampleApplyReview sampleApplyReview) {
        // 同一adminId并发申请控制
        RLock redissonLock = redissonClient.getLock(RedissonLockKey.SAMPLE_APPLY_REVIEW + baseService.getAdminId());
        try {
            boolean flag = redissonLock.tryLock(0L, 30L, TimeUnit.SECONDS);
            if (!flag) {
                throw new DefaultServiceException("请重新进行样品申请!");
            }
            return sampleApplyReviewService.sampleApplyReview(sampleApplyReview);
        } catch (InterruptedException e) {
            log.warn("锁获取异常:{}", e.getMessage(), e);
            throw new DefaultServiceException("请重新进行样品申请");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    /**
     * 查询门店是否可以被申请
     * @param query
     * @return
     */
    @RequestMapping(value = "/query/check-merchant" ,method = RequestMethod.POST )
    public CommonResult<CheckSampleDto> checkMerchant(@RequestBody SampleApplyQuery query) {
        CheckSampleDto dto = sampleApplyReviewService.checkMerchant(query);
        return CommonResult.ok(dto);
    }

    /**
     * 查询门店样品是否被申请
     * @param sampleApplyReview
     * @return
     */
    @RequestMapping(value = "/query/check-merchant-sample" ,method = RequestMethod.POST )
    public CommonResult<CheckSampleDto> checkMerchantSample(@RequestBody SampleApplyVO sampleApplyReview) {
        CheckSampleDto dto = sampleApplyReviewService.checkMerchantSample(sampleApplyReview);
        return CommonResult.ok(dto);
    }
}
