package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.model.domain.FollowUpRecord;
import net.summerfarm.crm.model.dto.AdminTurningConfigDto;
import net.summerfarm.crm.model.dto.FollowUpConfigDto;
import net.summerfarm.crm.model.query.task.TaskFollowUpQuery;
import net.summerfarm.crm.model.vo.AdminTurningConfigVO;
import net.summerfarm.crm.model.vo.FeedBackVO;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.service.FollowUpRecordService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * 拜访记录
 *
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@RestController
@RequestMapping(value = "/crm-service/follow-up-record")
public class FollowUpRecordController {

    @Resource
    private FollowUpRecordService followUpRecordService;

    /**
     * 创建拜访记录
     *
     * @param record        记录
     * @param bindingResult 绑定结果
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"followUpRecord:save", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(method = RequestMethod.POST)
    public AjaxResult saveRecord(@Validated(Add.class) @RequestBody FollowUpRecordVO record, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        return followUpRecordService.saveRecord(record, false);
    }

    @RequiresPermissions(value = {"followUpRecord:update", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    public AjaxResult updateRecord(@PathVariable int id, @Validated(Update.class) @RequestBody FollowUpRecordVO record, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        record.setId(id);
        return followUpRecordService.updateRecord(record);
    }

    /**
     * 查询拜访记录
     *
     * @param pageIndex  页面索引
     * @param pageSize   页面大小
     * @param type       类型
     * @param selectKeys 选择键
     * @return {@link AjaxResult}
     */
    @RequiresPermissions(value = {"followUpRecord:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @PostMapping(value = "/{pageIndex}/{pageSize}/{type}")
    public AjaxResult selectRecord(@PathVariable int pageIndex, @PathVariable int pageSize, @PathVariable int type, @RequestBody FollowUpRecordVO selectKeys) {
        return followUpRecordService.selectRecord(pageIndex, pageSize, type, selectKeys);
    }

    /**
     * 拜访记录详情
     *
     * @return {@link FollowUpRecordVO}
     */
    @RequiresPermissions(value = {"followUpRecord:select", CrmGlobalConstant.SA}, logical = Logical.OR)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public AjaxResult selectDetail(@PathVariable Long id, FollowUpRecordVO selectKeys) {
        return followUpRecordService.selectDetail(id);
    }

    @RequestMapping(value = "/autoSendMsg", method = RequestMethod.GET)
    public AjaxResult autoSendMsg() {
        followUpRecordService.autoSendMsg();
        return AjaxResult.getOK();
    }

    @RequestMapping(value = "/noteDetails/{id}", method = RequestMethod.GET)
    public AjaxResult noteDetail(@PathVariable int id, Integer couponId) {
        return followUpRecordService.noteDetails(id, couponId);
    }

    @GetMapping("/export")
    public AjaxResult exportFollowUpRecord(FollowUpRecordVO selectKeys) {
        return followUpRecordService.exportFollowUpRecord(selectKeys);
    }
    
    @PostMapping("/query/export")
    @RequiresPermissions(value = {"followUpRecord:export"})
    public CommonResult exportFollowUpRecordNew(@RequestBody FollowUpRecordVO selectKeys) {
        UserBase user = UserInfoHolder.getUser();
        return followUpRecordService.exportFollowUpRecordNew(user,selectKeys);
    }

    /**
     * 发送上一个小时拜访统计钉钉通知
     */
    @GetMapping("/sendDingMessage")
    public CommonResult<Void> sendDingMessage() {
        followUpRecordService.sendDingMessage();
        return CommonResult.ok();
    }

    /**
     * 导出指定时间前一小时的拜访记录
     *
     * @param response
     * @param datetime
     * @return
     */
    @GetMapping("/export/{datetime}/{token}")
    public void followUpHourRecord(HttpServletResponse response, @PathVariable("datetime") String datetime, @PathVariable("token") String token) {
        followUpRecordService.followUpHourRecord(response, datetime, token);
    }


    /**
     * 用户提交反馈
     *
     * @param feedBackVO 提交反馈参数
     * @return
     */
    @PostMapping("/feedback")
    public CommonResult feedback(@Validated @RequestBody FeedBackVO feedBackVO) {
        return followUpRecordService.feedback(feedBackVO);
    }

    /**
     * 查询任务拜访详情
     *
     * @param query 查询
     * @return {@link CommonResult}<{@link FollowUpRecord}>
     */
    @PostMapping("/query/task-followUp")
    public CommonResult<FollowUpRecord> selectTaskFollowUp(@Valid @RequestBody TaskFollowUpQuery query) {
        return followUpRecordService.selectTaskFollowUp(query);
    }
    /**
     * 大客户流转配置
     *
     * @param adminTurningConfigVO 流转配置
     * @return
     */
    @PostMapping("/query/turning-config")
    public CommonResult<AdminTurningConfigDto> turningConfig(@Validated @RequestBody AdminTurningConfigVO adminTurningConfigVO) {
        return followUpRecordService.turningConfig(adminTurningConfigVO);
    }

    /**
     * 拜访记录模版特殊配置
     *
     * @param adminTurningConfigVO 流转配置
     * @return
     */
    @PostMapping("/query/config")
    public CommonResult<FollowUpConfigDto> followUpConfigDto(@Validated @RequestBody AdminTurningConfigVO adminTurningConfigVO) {
        return followUpRecordService.followUpConfigDto(adminTurningConfigVO);
    }

    /**
     * 是否有权限导入拜访记录
     * 
     * @return
     */
    @PostMapping("/allow-import")
    public CommonResult<Boolean> allowImportFollowUpRecord() {
        UserBase user = UserInfoHolder.getUser();
        if (user == null || user.getBizUserId() == null) {
            return CommonResult.fail(ResultStatusEnum.UNAUTHORIZED);
        }
        return CommonResult.ok(followUpRecordService.hasImportFollowUpRecordPermission(user.getBizUserId()));
    }

}
