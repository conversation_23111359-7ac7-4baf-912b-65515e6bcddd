package net.summerfarm.crm.controller;

import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.datacollect.UnCollectByAspect;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.facade.CouponQueryFacade;
import net.summerfarm.manage.client.coupon.dto.resp.CouponResp;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/19 18:39
 */
@RestController
public class HeartbeatController {

    @Resource
    private CouponQueryFacade couponQueryFacade;

    /**
     * 检测服务是否正常
     * @return ok
     */
    @UnCollectByAspect
    @ResponseBody
    @RequestMapping(value = "/ok", method = RequestMethod.GET)
    public AjaxResult ok() {
        return AjaxResult.getOK();
    }

    /**
     * 检测服务是否正常
     * @return ok
     */
    @UnCollectByAspect
    @ResponseBody
    @RequestMapping(value = "/crm-service/ok", method = RequestMethod.GET)
    public AjaxResult crmOk() {
        return AjaxResult.getOK();
    }

    @GetMapping("/crm-service/feignConnect")
    public AjaxResult testFeignConnect(Integer id){
        id = Optional.ofNullable(id).orElse(NumberUtils.INTEGER_TWO);
        CouponResp couponResp = couponQueryFacade.getCouponInfo(id);
        return AjaxResult.getOK(couponResp);
    }
}
