package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class WeComContactWayEnum {

    @Getter
    @AllArgsConstructor
    public enum Type {

        SINGLE(1, "单人"),
        MULTI(2, "多人"),

        ;

        private final int code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum Scene {

        MINI_PROGRAM(1, "小程序"),
        QR_CODE(2, "通过二维码联系"),

        ;

        private final int code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum State {

        SELF_REGISTRATION("self_registration", "用户自主注册"),
        SALES_INVITATION("sales_invitation", "销售地推注册"),
        HOMEPAGE_POP_UP("homepage_pop_up", "商城首页弹窗"),
        PERSONAL_CENTER("personal_center", "个人中心"),

        ;

        private final String state;
        private final String desc;
    }

}
