package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/8 18:35
 */
public class CrmTaskEnum {
    @Getter
    @AllArgsConstructor
    public enum Type {
        COUPON(0, "发券"), VISIT(1, "拜访"),
        ;
        /**
         * 类型
         */
        private final int type;
        /**
         * 描述
         */
        private final String desc;
    }
    @Getter
    @AllArgsConstructor
    public enum DeleteFlag {
        NORMAL(0, "正常"), DELETE(1, "作废"),
        ;
        /**
         * 状态
         */
        private final int status;
        /**
         * 描述
         */
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum Status {
        NOT_STARTED(0, "未开始"), ONGOING(1, "进行中"), ENDED(2, "已结束"), CANCELED(3, "取消"),
        ;
        /**
         * 状态
         */
        private final int status;
        /**
         * 描述
         */
        private final String desc;
    }
}
