package net.summerfarm.crm.enums;

/**
 * <AUTHOR> ct
 * create at:  2021/12/22  10:36
 * 1 全部,2乳制品,3非乳制品,4水果
 * 类目类型
 */
public enum CategoryTypeEnum {

    ALL(1,"全部"),
    DAIRY(2,"乳制品"),
    NON_DAIRY(3,"非乳制品"),
    FRUIT(4,"水果"),
    BRAND(5,"自营品牌"),
    REWARD(6,"奖励sku品类"),
    OTHERS(10,"其他");

    private Integer type;

    private String description;

    CategoryTypeEnum(Integer type, String description){
        this.type = type;
        this.description = description;
    }

    public static String getValue(Integer type) {
        CategoryTypeEnum[] categoryTypeEnums = values();
        for (CategoryTypeEnum categoryTypeEnum : categoryTypeEnums) {
            if (categoryTypeEnum.getType().equals(type)) {
                return categoryTypeEnum.getDescription();
            }
        }
        return null;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
