package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.exception.ParamsException;

@Getter
@AllArgsConstructor
public enum ProductCategoryTypeEnum {

    // 全部
    ALL(1,"all"),
    // 乳制品
    DAIRY(2,"乳制品"),
    // 非乳制品
    NOT_DAIRY(3,"非乳制品"),
    // 水果
    FRUIT(4,"水果"),

    ;

    private final Integer code;
    private final String des;

    public static ProductCategoryTypeEnum fromCode(Integer code) {
        ProductCategoryTypeEnum[] categoryTypeEnums = values();
        for (ProductCategoryTypeEnum categoryTypeEnum : categoryTypeEnums) {
            if (categoryTypeEnum.getCode().equals(code)) {
                return categoryTypeEnum;
            }
        }
        throw new ParamsException(String.format("无效的类别: %s", code));
    }
}
