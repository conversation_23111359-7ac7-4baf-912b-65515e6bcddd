package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang.StringUtils;
import java.util.function.Predicate;

public class CrmJobEnum {

    /**
     * 任务类型
     */
    @Getter
    @AllArgsConstructor
    public enum Type {
        COUPON(0, "发券任务"),
        OTHER(1, "其他任务"),
        PRODUCT_ORDER(2, "指定商品下单"),
        CATEGORY_ORDER(3, "指定品类下单"),
        NORMAL_ORDER(4, "普通下单"),
        POTENTIAL_HIGH_VALUE_CUSTOMER(5, "潜力高价值客户任务"),
        CATEGORY_FULFILLMENT(6, "品类履约任务"),

        ;

        private final Integer code;
        private final String desc;

        public static Type getByCode(Integer code) {
            for (Type type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new ParamsException(String.format("任务类型不存在, code: %s", code));
        }
    }

    // ---------------------------------------------------------------------------------------------------------------

    /**
     * 子任务类型
     */
    @Getter
    @AllArgsConstructor
    public enum SubType {
        MERCHANT_BUSINESS(0, "客户行业属性打标"),
        ;

        private final Integer code;
        private final String desc;

        public static SubType getByCode(Integer code) {
            for (SubType subType : values()) {
                if (subType.code.equals(code)) {
                    return subType;
                }
            }
            throw new ParamsException(String.format("子任务类型不存在, code: %s", code));
        }
    }

    // ---------------------------------------------------------------------------------------------------------------

    /**
     * 任务状态
     */
    @Getter
    @AllArgsConstructor
    public enum Status {
        NOT_STARTED(0, "未开始"),
        IN_PROGRESS(1, "进行中"),
        COMPLETED(2, "已结束"),
        CANCELED(3, "已取消"),

        ;

        private final Integer code;
        private final String desc;

        public static boolean isCanceled(Integer code) {
            return CANCELED.code.equals(code);
        }
    }


    // ---------------------------------------------------------------------------------------------------------------

    /**
     * 任务完成判定类型
     */
    @Getter
    @AllArgsConstructor
    public enum CompletionCriteriaType {

        COUPON_USED(0, "任务完成条件为券核销,值为优惠券id", StringUtils::isNumeric),
        FOLLOW_UP_WAY(1, "拜访方式. 填入文本, 如: 电话拜访", StringUtils::isNotBlank),
        SINGLE_ORDER_PRODUCT_AMOUNT(2, "任意商品单笔下单实付金额>= xx元, 值为金额", str -> str.matches("^\\d{1,8}(\\.\\d{1,2})?$")), // BigDecimal(10, 2)的正则
        CUMULATIVE_PRODUCT_AMOUNT(3, "任意商品累计下单实付金额>= xx元, 值为金额", str -> str.matches("^\\d{1,8}(\\.\\d{1,2})?$")),
        POTENTIAL_HIGH_VALUE(4, "活动期间满足高价值客户条件", StringUtils::isNotBlank),
        CATEGORY_FULFILLMENT(5, "活动期间任务商品完成履约", StringUtils::isNotBlank),
        CUSTOMIZE(6, "自定义. 填入文本, 如:完成客户打标", StringUtils::isNotBlank),

        ;

        private final Integer code;
        private final String desc;
        private final Predicate<String> valueValidator;

        public static CompletionCriteriaType getByCode(Integer code) {
            for (CompletionCriteriaType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new ParamsException(String.format("任务完成判定条件不存在, code: %s", code));
        }
    }

    // ---------------------------------------------------------------------------------------------------------------

    /**
     * 关联人群方式
     */
    @Getter
    @AllArgsConstructor
    public enum MerchantSelectionType {

        MERCHANT_LIST(0, "指定门店"),
        UPLOAD_M_ID_EXCEL(1, "上传门店excel"),
        UPLOAD_M_ID_PRODUCT_EXCEL(2, "上传门店商品关联excel"),
        MERCHANT_ITEM_MAP(3, "指定门店商品关联Map(Map<Long, List<String>>), key为门店id, value为item列表"),

        ;

        private final Integer code;
        private final String desc;
    }

    // ---------------------------------------------------------------------------------------------------------------

    /**
     * 业务类型
     */
    @Getter
    @AllArgsConstructor
    public enum BusinessType {
        CRM(0, "销售任务"),
        MERCHANT(1, "门店任务"),

        ;

        private final Integer code;
        private final String desc;
    }

    // ---------------------------------------------------------------------------------------------------------------

    /**
     * 客户标签
     */
    @Getter
    @AllArgsConstructor
    public enum JobMerchantLabel {
        /**
         * 增量品类推广任务下的新客（本月有效拉新，只看单店）
         */
        CATEGORY_FULFILLMENT_INC_NEW_ACCOUNT("新客"),
        /**
         * 增量品类推广任务下的老客
         */
        CATEGORY_FULFILLMENT_INC_OLD_ACCOUNT("流失召回"),
        /**
         * 客户行业属性打标任务下的新客
         */
        MERCHANT_BUSINESS_NEW_ACCOUNT("新客"),
        /**
         * 客户行业属性打标任务下的老客
         */
        MERCHANT_BUSINESS_OLD_ACCOUNT("老客"),
        ;

        private final String value;
    }

}
