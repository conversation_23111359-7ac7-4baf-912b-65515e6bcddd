package net.summerfarm.crm.enums;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/18 14:10
 */
public enum CrmFollowUpEnum {

    FOLLOW_UP(0,"有跟进人"),
    NO_FOLLOW_UP(1,"无跟进人");

    private Integer id;

    private String status;

    CrmFollowUpEnum(Integer id,String status){
        this.id = id;
        this.status = status;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
