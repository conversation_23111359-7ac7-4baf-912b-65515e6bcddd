package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HighValueCustomerTypeEnum {

    // 高价值
    HIGH_VALUE(0, "高价值客户"),
    // 准高价值
    QUASI_HIGH_VALUE(1, "准高价值客户"),
    // 潜力高价值
    POTENTIAL_HIGH_VALUE(2, "潜力高价值客户"),
    // 普通客户
    COMMON(3, "普通客户"),
    // 预测高价值
    PREDICTED_HIGH_VALUE(4, "预测高价值客户"),
    ;


    private final Integer code;
    private final String desc;
}
