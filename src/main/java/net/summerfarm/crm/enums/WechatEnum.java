package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/8/1 15:59
 */
public class WechatEnum {

    /**
     * 微信标签类型enum
     *
     * <AUTHOR>
     * @date 2023/08/01
     */
    @Getter
    @AllArgsConstructor
    public class WechatCrmTagEnum {
        public static final String SALES_WECHAT = "已加销微";

        public static final String OFFICIAL_WECHAT = "已加官微";
    }

    /**
     * 微信标签类型enum
     *
     * <AUTHOR>
     * @date 2023/08/01
     */
    @Getter
    @AllArgsConstructor
    public enum WechatTagTypeEnum {
        ADD(0, "新增标签"),
        REMOVE(1, "移除标签");
        private int type;
        private String desc;
    }

    /**
     * 微信客户联系回调事件
     *
     * <AUTHOR>
     * @date 2023/08/01
     */
    @Getter
    @AllArgsConstructor
    public enum WechatCustomerEventEnum {
        ADD_EXTERNAL_CONTACT("add_external_contact", "添加联系人"),
        DEL_EXTERNAL_CONTACT("del_external_contact", "bd删除客户"),
        DEL_FOLLOW_USER("del_follow_user", "客户删除bd"),
        ;
        private String event;
        private String desc;

        public static WechatCustomerEventEnum getByEvent(String event) {
            for (WechatCustomerEventEnum eventEnum : values()) {
                if (Objects.equals(eventEnum.getEvent(), event)) {
                    return eventEnum;
                }
            }
            return null;
        }
    }

    /**
     * 企微客户关注状态
     *
     * <AUTHOR>
     * @date 2023/08/01
     */
    @Getter
    @AllArgsConstructor
    public enum WechatFollowStatusEnum{
        FOLLOW(1, "正常"),
        CUSTOMER_NOT_FOLLOW(2, "用户删除员工"),
        BD_NOT_FOLLOW(3, "员工删除客户"),
        NOT_FOLLOW(4, "互删"),
        ;
        private final int status;
        private final String desc;
    }


    /**
     * 群发任务消息发送状态
     *
     * <AUTHOR>
     * @date 2023/08/01
     */
    @Getter
    @AllArgsConstructor
    public enum GroupTaskSendStatusEnum{
        NOT_SEND(0, "未发送"),
        SEND_OK(1, "已发送"),
        SEND_FAILED(2, "发送失败"),
        BEEN_SEND(3, "已经发送过其它消息"),
        ;
        private final int status;
        private final String desc;
    }
}
