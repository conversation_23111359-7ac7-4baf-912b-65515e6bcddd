package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class KeyCustomerEnum {

    @Getter
    @AllArgsConstructor
    public enum Type {

        REGISTER_UNORDERED(1, "注册未下单"),
        FIRST_TIME_BUYER(2, "首单客户"),
        OPEN_TO_PRIVATE(3, "公海转私海"),
        OTHER(4, "其他"), // 目前其它的定义是非以上两种掉落规则且掉落倒计时<=3天的客户
        RECLAIMABLE_AFTER_ON_SITE(5, "上门后可捡回客户")

        ;

        private final Integer code;
        private final String desc;

        private static final Map<Integer, Type> CODE_TO_TYPE_MAP = Arrays.stream(values())
                .collect(Collectors.toMap(Type::getCode, Function.identity()));

        public static Type getByCode(Integer code) {
            return Optional.ofNullable(CODE_TO_TYPE_MAP.get(code))
                    .orElseThrow(() -> new IllegalArgumentException("重点客户未知的code:" + code));
        }
    }
}
