package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public class VisitPlanEnum {
    /**
     * 拜访类型
     */
    public enum Type{
        /**
         * 0-拜访
         */
        VISIT,
        /**
         * 1-拉新
         */
        LEADS,
        /**
         * 2-陪访
         */
        ESCORT
    }

    /**
     * 拜访状态
     */
    public enum Status{
        /**
         * 0-待拜访
         */
        WAIT,
        /**
         * 1-已拜访
         */
        FINISH,
        /**
         * 2-取消
         */
        CANCEL,
        /**
         * 3-未完成
         */
        UN_HANDLE
    }

    /**
     * 客户满意度  0 未评价,  1 满意 ,2 一般, 3 不满意'
     */
    public enum Satisfaction{
        /**
         * 未评价
         */
        NOT_EVALUATED,
        /**
         * 满意
         */
        SATISFIED,
        /**
         * 一般
         */
        COMMONLY,
        /**
         * 不满意
         */
        DISSATISFIED
    }

    /**
     * 客户购买意向
     */
    public enum PurchaseIntention{
        /**
         * 未评价
         */
        NOT_EVALUATED,
        /**
         * 满意
         */
        SATISFIED,
        /**
         * 一般
         */
        COMMONLY,
        /**
         * 不满意
         */
        DISSATISFIED
    }
}
