package net.summerfarm.crm.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/28 17:23
 */
public class MerchantTagEnum {
    public enum Tag {
        /**
         * 生命周期
         */
        LIFECYCLE,

        /**
         * 距今下单天数
         */
        R_VALUE,

        /**
         * 60天下单频次
         */
        F_VALUE,

        /**
         * 60客单价
         */
        M_VALUE
    }

    @Getter
    public enum LifecycleTagType {
        NON_ORDER_REGISTER(Arrays.asList("N0"), "注册未下单"),
        THIRTY_DAY_FIRST_ORDER(Arrays.asList("N1", "N2"), "30日首单"),
        ASCENDING_PERIOD(Arrays.asList("A1", "A2", "A3"), "上升期客户"),
        STABLE_PERIOD(Arrays.asList("S1", "S2"), "稳定期客户"),
        DESCENT_PERIOD(Arrays.asList("B1", "B2"), "下降期客户"),
        ADAPTATION_PERIOD(Arrays.asList("W"), "适应期客户"),
        SLEEP_PERIOD(Arrays.asList("L1"), "睡眠期客户"),
        RESURRECTION(Arrays.asList("W"), "复活"),
        LOSS_PERIOD(Arrays.asList("L2", "L3"), "流失期客户");
        private List<String> tag;

        private String description;

        LifecycleTagType(List<String> tag, String description) {
            this.tag = tag;
            this.description = description;
        }

    }
}
