package net.summerfarm.crm.enums;

/**
 * 离线数据库信息同步表:表名枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/18 10:22
 */
public enum DataSynchronizationInformationEnum {

    CRM_BD_DAY_GMV(0,"crm_bd_day_gmv"),
    CRM_BD_MONTH_GMV(10,"crm_bd_month_gmv"),
    CRM_MERCHANT_DAY_GMV(20,"crm_merchant_day_gmv"),
    CRM_MERCHANT_MONTH_GMV(30,"crm_merchant_month_gmv"),
    WAREHOUSE_ESTIMATED_CONSUMPTION(40,"warehouse_estimated_consumption"),
    WAREHOUSE_PATH_TIME(50,"warehouse_path_time"),
    STOCK_DASHBOARD_HISTORY(60,"stock_dashboard_history"),
    STOCK_DASHBOARD_FUTURE(70,"stock_dashboard_future"),
    CRM_MERCHANT_DAY_LABEL(80,"crm_merchant_day_label"),
    CRM_BD_BIG_CUST_MONTH_GMV(90,"crm_bd_big_cust_month_gmv"),
    CRM_MERCHANT_DAY_ATTRIBUTE(100,"crm_merchant_day_attribute"),
    CRM_CITY_DAY_GMV(110,"crm_city_day_gmv"),
    CRM_CITY_MONTH_GMV(120,"crm_city_month_gmv"),
    CRM_MERCHANT_FUTURE_DAY_GMV(130,"crm_merchant_future_day_gmv"),
    CRM_BD_TODAY_DAY_GMV(140,"crm_bd_today_gmv"),
    CRM_SKU_MONTH_GMV(150,"crm_sku_month_gmv"),
    CRM_SKU_AREA_MONTH_MERCHANT(160,"crm_sku_area_month_merchant"),
    CRM_SKU_BD_MONTH_MERCHANT(170,"crm_sku_bd_month_merchant"),
    CRM_MERCHANT_MALL_SEARCH_TOP(180,"crm_merchant_mall_search_top"),
    CRM_MERCHANT_TODAY_GMV(190,"crm_merchant_today_gmv"),
    CRM_MERCHANT_AREA_TYPE_TOP(200,"crm_merchant_area_type_top"),
    CRM_COMFORT_SEND_7DAY_NO_SEND(210,"crm_comfort_send_7day_no_send"),
    CRM_COMFORT_SEND_FINISHED(220,"crm_comfort_send_finished"),
    CRM_COMFORT_SEND_WILL_FINISHED(230,"crm_comfort_send_will_finished"),
    CATEGORY_COUPON_QUOTA_REWARD(240,"category_coupon_quota_reward"),
    CRM_CITY_DISTRICT_DAY_GMV(250,"crm_city_district_day_gmv"),
    CRM_MERCHANT_INCREMENT_LABEL(260,"crm_merchant_increment_label"),
    CRM_CITY_TODAY_GMV(270,"crm_city_today_gmv"),
    CRM_WECHAT_TAG_GROUP(280,"crm_wechat_tag_group"),
    CRM_RISK_MERCHANT(290,"crm_risk_merchant"),
    MERCHANT_PURCHASE_DATA(291,"merchant_purchase_data"),

    CRM_BD_TODAY_HOUR_GMV(300,"crm_bd_today_hour_gmv"),
    CRM_CITY_TODAY_HOUR_GMV(310,"crm_city_today_hour_gmv"),
    ;

    private Integer code;
    private String tableName;

    DataSynchronizationInformationEnum(String tableName){
        this.tableName = tableName;
    }

    DataSynchronizationInformationEnum(Integer code, String tableName) {
        this.code = code;
        this.tableName = tableName;
    }

    DataSynchronizationInformationEnum() {
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String /**/getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
