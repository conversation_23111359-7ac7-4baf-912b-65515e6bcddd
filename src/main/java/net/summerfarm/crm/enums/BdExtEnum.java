package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/18 22:35
 */
public class BdExtEnum {

    public enum infoType{
        /**
         * 具有销售属性的admin(包括超管,销售主管,区域超管)
         */
        ALL_BD,
        /**
         * 具有销售角色的admin(不包括销售主管,区域超管)
         */
        ONLY_BD,
        /**
         * 获取运营区域的销售主管
         */
        AREA_MANAGER,
        /**
         * 获取销售的销售主管
         */
        BD_MANAGER,
        /**
         * 获取主管管理的销售
         */
        MANAGER_SUBORDINATE
    }

    public enum areaType{
        /**
         * 运营大区-运营区域
         */
        OPERATE_AREA,
        /**
         * 获取 销售区域-运营区域 需销售激励指标配置的区域
         */
        BD_AREA
    }
}
