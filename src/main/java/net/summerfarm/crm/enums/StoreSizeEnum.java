package net.summerfarm.crm.enums;

import net.xianmu.common.exception.ParamsException;
import net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/12/20 16:26
 */
public class StoreSizeEnum {

    public static RegionalOrganizationEnums.Size getSize(Integer code) {
        return Arrays.stream(RegionalOrganizationEnums.Size.values()).filter(size -> size.getCode().equals(code)).findFirst().orElseThrow(() -> new ParamsException("无法找到的客户类型"));
    }

    public static RegionalOrganizationEnums.Size getSizeByDesc(String desc) {
        return Arrays.stream(RegionalOrganizationEnums.Size.values()).filter(size -> size.getDesc().equals(desc)).findFirst().orElseThrow(() -> new ParamsException("无法找到的客户类型"));
    }
}
