package net.summerfarm.crm.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Getter
public enum ProcessInstanceBizTypeEnum {
    /**
     * 客户倒闭审批,bizType = 1
     */
    CUSTOMER_FAIL(1, "CUSTOMER_FAIL_CODE", "客户倒闭审批"),
    FOLLOW_UP_UPDATE_POI(24, "FOLLOW_UP_UPDATE_POI", "销售拜访修改门店定位"),
    MERCHANT_SITUATION(10, "MERCHANT_SITUATION", "客情申请审批");

    /**
     * 业务类型
     */
    private final int bizType;

    /**
     * 钉钉审批模版编码
     */
    private final String processCode;

    /**
     * 描述
     */
    private final String description;

    ProcessInstanceBizTypeEnum(int bizType, String processCode, String description) {
        this.bizType = bizType;
        this.processCode = processCode;
        this.description = description;
    }
}
