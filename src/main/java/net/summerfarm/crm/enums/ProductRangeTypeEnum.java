package net.summerfarm.crm.enums;

import net.summerfarm.crm.enums.BDTarget.SalesTargetTypeEnum;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;

/**
 * 产品查询范围类型枚举
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public enum ProductRangeTypeEnum {
    
    // 全品类
    ALL_CATEGORY(1, "全品类", "sub_type", "1,2", SalesTargetTypeEnum.TargetType.CATEGORY_POTENTIAL),
    // PB商品
    PB_PRODUCT(2, "PB", "sub_type", "3", SalesTargetTypeEnum.TargetType.CATEGORY_POTENTIAL),
    // 指定SKU
    SPECIFIC_SKU(3, "指定SKU", "sku", null, SalesTargetTypeEnum.TargetType.SKU_POTENTIAL),
    // 指定SPU
    SPECIFIC_SPU(4, "指定SPU", "pd_no", null, SalesTargetTypeEnum.TargetType.SPU_POTENTIAL),
    // 乳制品
    DAIRY_PRODUCT(5, "乳制品", "category_type", "2", SalesTargetTypeEnum.TargetType.CATEGORY_POTENTIAL),
    // 非乳制品
    NON_DAIRY_PRODUCT(6, "非乳制品", "category_type", "3", SalesTargetTypeEnum.TargetType.CATEGORY_POTENTIAL),
    // 水果
    FRUIT_PRODUCT(7, "水果", "category_type", "4", SalesTargetTypeEnum.TargetType.CATEGORY_POTENTIAL);

    private final Integer code;
    private final String desc;
    private final String filterField;
    private final String filterValue;
    private final SalesTargetTypeEnum.TargetType targetType;

    ProductRangeTypeEnum(Integer code, String desc, String filterField, String filterValue, SalesTargetTypeEnum.TargetType targetType) {
        this.code = code;
        this.desc = desc;
        this.filterField = filterField;
        this.filterValue = filterValue;
        this.targetType = targetType;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getFilterField() {
        return filterField;
    }

    public String getFilterValue() {
        return filterValue;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 枚举code
     * @return 枚举对象
     */
    public static ProductRangeTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductRangeTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     *
     * @param targetDetail 目标详情
     * @param targetType 目标类型
     * @return 枚举对象
     */
    public static ProductRangeTypeEnum getByDescAndTargetType(BdDailyTargetDetail targetDetail,
                                                              SalesTargetTypeEnum.TargetType targetType) {
        if (targetType == null || targetDetail == null) {
            return null;
        }
        if (targetType == SalesTargetTypeEnum.TargetType.SKU_POTENTIAL) {
            return SPECIFIC_SKU;
        }
        if (targetType == SalesTargetTypeEnum.TargetType.SPU_POTENTIAL) {
            return SPECIFIC_SPU;
        }
        if (targetType == SalesTargetTypeEnum.TargetType.CATEGORY_POTENTIAL) {
            for (ProductRangeTypeEnum typeEnum : values()) {
                if (typeEnum.getDesc().equals(targetDetail.getTargetName())) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}