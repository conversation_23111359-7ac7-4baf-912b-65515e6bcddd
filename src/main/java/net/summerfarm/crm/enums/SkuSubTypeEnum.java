package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2Args;


/**
 * 商品二级性质枚举 1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销、5 鲜果POP
 *
 * <AUTHOR>
 * @date 2025/02/28
 */
@Getter
@AllArgsConstructor
public enum SkuSubTypeEnum implements Enum2Args {
    NULL(0, "无"),
    SELF_NO_WAREHOUSE(1, "自营-代销不入仓"),
    SELF_IN_WAREHOUSE(2, "自营-代销入仓"),
    SELF_DISTRIBUTION(3, "自营-经销"),
    AGENT_DISTRIBUTION(4, "代仓-经销"),
    POP(5, "鲜果POP"),
    ;

    private Integer value;
    private String content;

    public static SkuSubTypeEnum getEnum(Integer value) {
        for (SkuSubTypeEnum enums : SkuSubTypeEnum.values()) {
            if (enums.value.equals(value)) {
                return enums;
            }
        }
        return null;
    }

    public static String getEnumContent(Integer value) {
        for (SkuSubTypeEnum enums : SkuSubTypeEnum.values()) {
            if (enums.value.equals(value)) {
                return enums.content;
            }
        }
        return null;
    }

    /**
     * 是否是「自营-代销不入仓」的货品
     *
     * @param value 二级性质
     * @return boolean
     */
    public static boolean isFullCategorySku(Integer value){
        return SELF_NO_WAREHOUSE.value.equals(value);
    }

    /**
     * 是否是「鲜果POP」的货品
     *
     * @param value 二级性质
     * @return boolean
     */
    public static boolean isPopSku(Integer value){
        return POP.value.equals(value);
    }
}
