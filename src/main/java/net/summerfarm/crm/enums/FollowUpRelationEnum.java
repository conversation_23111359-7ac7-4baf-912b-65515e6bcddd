package net.summerfarm.crm.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/9 18:09
 */
public class FollowUpRelationEnum {
    /**
     * 新购买标签
     */
    public enum FollowType{
        /**
         * 0 无
         */
        FALSE,
        /**
         * 1 新购买
         */
        TRUE,
        /**
         * 2 由定时任务取消
         */
        CANCEL
    }

    /**
     * 省心送标签
     */
    public enum TimingFollowType{
        /**
         * 0 无
         */
        FALSE,
        /**
         * 1 省心送
         */
        TRUE
    }

    public enum Reason{
        CUSTOMER_FAILURE("客户倒闭"),
        ACTIVE_SELECTION("主动选择"),
        BATCH_ADD("批量操作"),
        SUPERVISOR_ASSIGNMENT("主管分配"),
        ADMIN_TURN("大客户流转")
        ;

        private String value;

        Reason() {
        }

        Reason(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum Source{
        APPROVE(1,"APPROVE");

        private Integer key;

        private String value;

        Source() {
        }

        Source(Integer key,String value) {
            this.value = value;
            this.key = key;
        }

        public String getValue() {
            return value;
        }
        public Integer getKey() {
            return key;
        }

    }

    /**
     * 跟进状态
     */
    public enum Status{
        /**
         * 0 无
         */
        FALSE,
        /**
         * 1 省心送
         */
        RELEASE_TYPE
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    public enum DangerDayRule{

        RULE_ONE(1,"30天未下单且15天未拜访"),
        RULE_TWO(2,"60天未下单"),
        FIRST_TIME_BUYER(3,"首单客户"),
        OPEN_TO_PRIVATE(4,"公海转私海"),
        ;

        private Integer code;
        private String value;
    }

    /**
     * 释放保护原因
     */
    @Getter
    public enum ProtectReason {
        UNFINISHED_DELIVERY("未完成履约客户"),
        ;

        ProtectReason(String value) {
            this.value = value;
        }
        private String value;

        public static List<String> getProtectReasonValues() {
            return Lists.newArrayList(UNFINISHED_DELIVERY.getValue());
        }
    }
}
