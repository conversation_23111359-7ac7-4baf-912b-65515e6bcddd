package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 16:10
 */
public enum SampleApplyStatusEnum {

    WAIT_HANDLE(0,"待反馈"),
    FEEDBACK(1,"已反馈"),
    CANCEL(2,"取消"),
    REVIEWING(3,"审核中"),
    CLOSE(4,"关闭");


    private int id;

    private String state;

    SampleApplyStatusEnum(int id, String state) {
        this.id = id;
        this.state = state;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
