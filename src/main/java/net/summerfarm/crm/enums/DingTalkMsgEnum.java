package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/28 14:16
 */
public enum DingTalkMsgEnum {

    /**
     * 普通文本
     */
    TXT("text"),

    /**
     * markdown
     */
    MARKDOWN("markdown"),

    /**
     * 链接类型
     */
    LINK("link");


    private String type;

    DingTalkMsgEnum() {
    }

    DingTalkMsgEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}
