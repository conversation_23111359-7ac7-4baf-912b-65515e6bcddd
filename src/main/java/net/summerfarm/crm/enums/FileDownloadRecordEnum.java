package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/8 16:13
 */
public class FileDownloadRecordEnum {

    public enum Type{
        /**
         * 大客户对账单(1),账单(2),卡券(3),销售数据(4)，优惠券发放详情（5）,采购发票(6),拜访记录(7),业财现结收入(8),业财资金收入(9),
         * 业财账期收入(10),业财账期资金(11),批量下载开票记录处发票信息（12）,配送计划(13)'
         */
        UNDEFINED,
        ADMIN_BILL,
        BILL,
        COUPON,
        SALE_DATA_EXCEL,
        COUPON_DETAIL,
        PURCHASE_INVOICE,
        FOLLOW_UP_RECORD,
        CURRENT_INCOME,
        INCOME_FROM_PROPERTY_AND_CAPITAL,
        REVENUE_IN_FINANCIAL_ACCOUNTING_PERIOD,
        FINANCIAL_ACCOUNTING_PERIOD_FUNDS,
        BILLING_RECORD,
        DELIVERY_PLAN
    }

    public enum Status{
        /**
         * 未定义
         */
        UNDIFINED,
        /**
         * 成功
         */
        SUCCESS,
        /**
         * 失败
         */
        FAIL
    }

}
