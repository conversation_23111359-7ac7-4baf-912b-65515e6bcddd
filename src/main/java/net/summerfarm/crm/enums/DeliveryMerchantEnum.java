package net.summerfarm.crm.enums;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/10/20 15:58
 */
public class DeliveryMerchantEnum {

    public final static BigDecimal DELIVERY_UP_TO_STANDARD = new BigDecimal("600");
    public final static Integer SPU_UP_TO_STANDARD = 5;

    public enum QualifySpu{

    }

    public enum QualifyGmv{

    }

    /**
     * 配送gmv是否达标
     */
    public enum DeliveryQualify{
        /**
         * 未达标
         */
        NO,
        /**
         * 已达标
         */
        YES
    }
}
