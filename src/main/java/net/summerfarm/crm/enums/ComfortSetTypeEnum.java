package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
public enum ComfortSetTypeEnum {

    SEVEN_DAY_NO_SEND("SEVEN_DAY_NO_SEND", "crm_comfort_send_7day_no_send"),
    FINISH("FINISH", "crm_comfort_send_finished"),
    WILL_FINISH("WILL_FINISH", "crm_comfort_send_will_finished");

    public final String name;
    public final String table;

    public static String getTableName(String name) {
        ComfortSetTypeEnum[] values = values();
        for (ComfortSetTypeEnum keyPersonEnum : values) {
            if (Objects.equals(keyPersonEnum.name, name)) {
                return keyPersonEnum.table;
            }
        }
        return null;
    }

}
