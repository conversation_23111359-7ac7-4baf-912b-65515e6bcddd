package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 22:31
 */
public class CouponEnum {

    public enum UsedStatus {
        NOT_USED,
        USED
    }

    public enum Status {
        UNDEFINED,
        EFFECTIVE,
        DELETE
    }

    public enum Type {
        /**
         * 0指固定时间间隔到期
         */
        SPECIFIED_TIME,
        /**
         * 1固定时间点到期
         */
        FIXED_POINT_IN_TIME
    }

    public enum CouponType {
        MERCHANT_SITUATION_COUPON(0, "客情券"),
        MONTH_LIVE_COUPON(1, "鲜沐专属购物券"),
        CATEGORY_COUPON(2, "专属商品优惠券"), //品类卷-价格补贴
        MERCHANT_SITUATION_CATEGORY_COUPON(3, "鲜沐品类客情券"); //品类卷-品类拓宽

        private Integer code;
        private String couponName;

        CouponType() {
        }

        CouponType(Integer code, String couponName) {
            this.code = code;
            this.couponName = couponName;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getCouponName() {
            return couponName;
        }

        public void setCouponName(String couponName) {
            this.couponName = couponName;
        }

        public static CouponType getCouponType(Integer code) {
            for (CouponType couponEnum : CouponType.values()) {
                if (couponEnum.getCode().equals(code)) {
                    return couponEnum;
                }
            }
            return null;
        }
    }

    public enum CouponQuotaChangeType {
        SETTING(0, "设置"),
        DIVISION(1, "划分"),
        COUPON(2, "发券"),
        REWARD(3, "返还"),
        Expand(4, "品类拓宽发券"),
        NEW_CUSTOMER_RATIO(5, "新客费比"),
        OLD_CUSTOMER_RATIO(6, "老客费比"),
        CLOSE(7, "作废"),
        RECALL(8, "撤回"),
        ;

        private Integer code;
        private String changeType;

        CouponQuotaChangeType(Integer code, String changeType) {
            this.code = code;
            this.changeType = changeType;
        }

        public Integer getCode() {
            return code;
        }

        public String getChangeType() {
            return changeType;
        }

        public static String getChangeType(Integer code) {
            for (CouponQuotaChangeType activityScope : CouponQuotaChangeType.values()) {
                if (activityScope.getCode().equals(code)) {
                    return activityScope.getChangeType();
                }
            }
            return null;
        }
    }

    public enum Group {
        /**
         * 0-活动
         */
        ACTIVITY,
        /**
         * 1-售后
         */
        AFTER_SALE,
        /**
         * 2-新人
         */
        NEW_PEOPLE,
        /**
         * 3-权益
         */
        RIGHTS_AND_INTERESTS,
        /**
         * 4-客情券
         */
        MERCHANT_SITUATION,
        /**
         * 5-用户召回
         */
        USER_RECALL,
        /**
         * 6-新品
         */
        NEW_PRODUCTS,
        /**
         * 7-消费返券
         */
        CONSUMPTION_REBATE,
        /**
         * 8-员工福利
         */
        EMPLOYEE_WELFARE
    }

    public enum AgioType {
        /**
         * 0未定义
         */
        UNDEFINED,
        /**
         * 1普通商品优惠券
         */
        ORDINARY,
        /**
         * 2普通运费优惠券
         */
        FREIGHT,
        /**
         * 3精准送优惠券
         */
        PRECISE_DELIVERY,
        /**
         * 4红包
         */
        RED_ENVELOPES
    }

    public enum ActivityScope {
        /**
         * 0 - 未定义
         */
        UNDEFINED(0, "未定义"),
        /**
         * 1 - 仅预售尾款
         */
        PRE_SALE(1, "仅预售尾款"),
        /**
         * 2 - 除预售,省心送,秒杀
         */
        OTHER(2, "普通订单"),
        /**
         * 3 - 全部
         */
        ALL(3, "全部"),
        /**
         * 4 - 仅省心送
         */
        TIMING_DELIVERY(4, "省心送");

        private Integer code;
        private String value;

        ActivityScope() {
        }

        ActivityScope(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public static String getActivityScopeValue(Integer code) {
            for (ActivityScope activityScope : ActivityScope.values()) {
                if (activityScope.getCode().equals(code)) {
                    return activityScope.getValue();
                }
            }
            return null;
        }
    }

}
