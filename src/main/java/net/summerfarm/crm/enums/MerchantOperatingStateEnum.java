package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/15 14:49
 */
public enum MerchantOperatingStateEnum {

    OPERATE_STATE(0,"正常经营"),
    CLOSE_DOWN(1,"倒闭"),
    BANKRUPTCY_CONFIRMATION(2,"倒闭确认中");

    private int id;

    private String status;

    MerchantOperatingStateEnum(int id, String status) {
        this.id = id;
        this.status = status;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
