package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/15 16:23
 */
public class ExamineEnum {

    public enum MerchantSituation{
        /**
         * 待审核
         */
        SITUATION_STATUS_NEW,
        /**
         * 待审批
         */
        SITUATION_STATUS_PENDING,
        /**
         * 已审批/审核通过
         */
        SITUATION_STATUS_PASS,
        /**
         * 关闭
         */
        SITUATION_STATUS_CLOSE
    }

    public enum SampleApplyReview{
        /**
         * 审核成功
         */
        SUCCESS,
        /**
         * 审核取消
         */
        CANCEL
    }
    public enum RISK_LEVER{
        /**
         * 无风险
         */
        COMMON,
        /**
         * 有风险
         */
        RISK
    }
}
