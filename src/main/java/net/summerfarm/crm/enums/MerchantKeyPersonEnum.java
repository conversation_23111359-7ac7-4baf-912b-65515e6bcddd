package net.summerfarm.crm.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 15:56
 */
public enum MerchantKeyPersonEnum {

    STORE_MANAGER(0,"店长"),
    BOSS(1,"老板"),
    PARTNER(2,"合伙人"),
    PURCHASE(3,"采购");

    private int id;

    private String statusName;

    MerchantKeyPersonEnum(int id, String statusName) {
        this.id = id;
        this.statusName = statusName;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public static String getName(Integer id){
        MerchantKeyPersonEnum[] merchantKeyPersonEnum = values();
        for (MerchantKeyPersonEnum keyPersonEnum : merchantKeyPersonEnum) {
            if(Objects.equals(keyPersonEnum.getId(),id)){
                return keyPersonEnum.getStatusName();
            }
        }
        return null;
    }
}
