package net.summerfarm.crm.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/8/30 13:49
 */
public class BdAreaConfigEnum {
    /**
     * 销售级别
     *
     * <AUTHOR>
     * @date 2023/08/30
     */
    public class SaleRank {
        /**
         * 部门主管
         */
        public static final int DEPARTMENT_MANAGER = 1;
        /**
         * 地区经理
         */
        public static final int AREA_MANAGER = 2;
        /**
         * 城市经理
         */
        public static final int CITY_MANAGER = 3;
        /**
         * 销售
         */
        public static final int BD = 4;
    }

    public static boolean isBd(Integer rank) {
        return ObjectUtil.equal(rank, SaleRank.BD);
    }

    public static boolean isCityManager(Integer rank) {
        return ObjectUtil.equal(rank, SaleRank.CITY_MANAGER);
    }

    public static boolean isAreaManager(Integer rank) {
        return ObjectUtil.equal(rank, SaleRank.AREA_MANAGER);
    }

    public static boolean isDepartmentManager(Integer rank) {
        return ObjectUtil.equal(rank, SaleRank.DEPARTMENT_MANAGER);
    }
}
