package net.summerfarm.crm.enums;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用来标注在节点对象上以获得目录节点的各个必要属性值
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.TYPE, ElementType.METHOD})
public @interface NodeValue {
    /**
     * 作用在类上，默认true为树枝节点
     */
    boolean isCatalog() default true;

    /**
     * 作用在类的字段上
     */
    FIELD field() default FIELD.ID;

    /**
     * 作用在类的方法上
     */
    METHOD method() default METHOD.GET_SUBS;

    enum FIELD {
        ID,
        PID
    }
    enum METHOD {
        /**
         * 可缺失该配置*
         */
        GET_INDEX,

        /**
         * 对作为目录树枝节点的对象，必须有该注释和对应的方法，叶子节点对象可缺省*
         */
        GET_SUBS,

        /**
         * 当缺失该注解值时，从注解的GET_SUBS方法上获取类的子集，用来给子集加入一个子节点对象
         */
        ADD_CHILD,

        /**
         * 通过该的方法判断是否有父节点,当缺失该注释时，通过PID注释进行判断
         */
        HAS_PARENT
    }
}
