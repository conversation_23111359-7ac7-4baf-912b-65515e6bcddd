package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @Description 配置类枚举
 * @date 2022/6/14 2:00
 */
public enum ConfigValueEnum {

    GMV_EXCLUDE_ADMIN("gmv_exclude_admin","gmv排除客户编码为121"),
    BD_ACHIEVEMENT_RECEIVE("bd_achievement_receive","bd业绩邮件接收人"),
    GMV_TARGET("gmv_target","gmv目标值"),
    NEW_AREA_MONTH("new_area_month","新城市定义"),
    NEW_BD_MONTH("new_bd_month","新城市定义"),
    MONTHLY_LIVING_POOL_QUOTA("MONTHLY_LIVING_POOL_QUOTA","月活池额度"),
    MARKETING_QUOTA_RETURN_RULE("MARKETING_QUOTA_REWARD_RULE","营销额度返还规则"),
    CORE_PRODUCT_BASE_PRICE("CORE_PRODUCT_BASE_PRICE","普通品核心商品底价规则"),
    FRUIT_CORE_PRODUCT_BASE_PRICE("FRUIT_CORE_PRODUCT_BASE_PRICE","鲜果核心商品底价规则"),
    CRM_ADMIN_AUTH("crm_admin_auth","crm配置管理负责人权限"),
    CBD_ADMIN_ID("CBD_admin_id","茶百道id"),
    SHU_YI_ADMIN_ID("SHU_YI_ADMIN_ID","书亦id"),
    RELEASE_RULES("RELEASE_RULES","释放客户至公海的标签值"),
    ASSIGN_TEMPLATE("ASSIGN_TEMPLATE","批量分配bd模板"),
    REPEATED_JUDGMENT_THRESHOLD("REPEATED_JUDGMENT_THRESHOLD","门店名称重复判断阈值"),
    AUDIT_USER_ID_CONFIG("AUDIT_USER_ID_CONFIG","企微信门店审核id"),
    OFFICIAL_WECHAT_USER_ID("OFFICIAL_WECHAT_USER_ID","官方微信用户列表"),
    OFFICIAL_WECHAT_USER_QR("OFFICIAL_WECHAT_USER_QR","官微信小助手qr"),
    TIMING_TASK_MSG("TIMING_TASK_MSG","省心送任务提醒"),
    TIMING_TASK_ORDER_DOWNLOAD("TIMING_TASK_ORDER_DOWNLOAD","指定日期省心送到期任务下载"),
    WECOM_CORP_ID("WECOM_CORP_ID","企微企业id"),
    WECOM_CHAT_DATA_SECRET("WECOM_CHAT_DATA_SECRET","企微聊天数据secret"),
    WECOM_CHAT_DATA_PRIVATE_KEY("WECOM_CHAT_DATA_PRIVATE_KEY","企微聊天数据privateKey"),
    WECOM_CHAT_DATA_SEQ("WECOM_CHAT_DATA_SEQ","企微聊天数据seq"),
    MALL_MINI_PROGRAM_COVER_MEDIA_ID("MALL_MINI_PROGRAM_COVER_MEDIA_ID", "商城小程序封面media_id"),
    CRM_BD_DASHBOARD_CONFIG("CRM_BD_DASHBOARD_CONFIG", "CRM BD看板配置"),
    CRM_PERFORMANCE_MODULE_CONFIG("CRM_PERFORMANCE_MODULE_CONFIG", "CRM主管视角区域/城市/BD业绩看板配置"),
    CRM_PERFORMANCE_MODULE_CONFIG_V2("CRM_PERFORMANCE_MODULE_CONFIG_V2", "CRM主管视角区域/城市/BD业绩看板配置V2，配合新离线表"),
    DAILY_DATA_DASHBOARD_CONFIG("DAILY_DATA_DASHBOARD_CONFIG", "每日数据看板配置"),
    DAILY_ORDER_DELIVERY_DASHBOARD_CONFIG("DAILY_ORDER_DELIVERY_DASHBOARD_CONFIG", "每日配送订单看板配置"),
    MANAGE_DB_MANAGEMENT_CITY_DASHBOARD_CONFIG("MANAGE_DB_MANAGEMENT_CITY_DASHBOARD_CONFIG", "manage页面，城市维度看板"),
    CRM_BD_PERFORMANCE_MODULE_CONFIG("CRM_BD_PERFORMANCE_MODULE_CONFIG", "CRM BD业绩看板配置"),
    CRM_CITY_PERFORMANCE_MODULE_CONFIG("CRM_CITY_PERFORMANCE_MODULE_CONFIG", "CRM 城市业绩看板配置"),

    // 高价值客户看板配置
    CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG("CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG", "高价值客户汇总看板配置"),
    CRM_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG("CRM_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG", "高价值客户详情看板配置"),
    // 超标SPU客户看板配置
    CRM_EXCESS_SPU_CUSTOMER_SUMMARY_CONFIG("CRM_EXCESS_SPU_CUSTOMER_SUMMARY_CONFIG", "超标SPU客户汇总看板配置"),
    CRM_EXCESS_SPU_CUSTOMER_DETAIL_CONFIG("CRM_EXCESS_SPU_CUSTOMER_DETAIL_CONFIG", "超标SPU客户详情看板配置"),
    // 品类推广看板配置
    CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG("CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG", "品类推广汇总看板配置"),
    CRM_CATEGORY_PROMOTION_DATA_CONFIG("CRM_CATEGORY_PROMOTION_DATA_CONFIG", "品类推广数据详情看板配置"),
    CRM_MERCHANT_CATEGORY_PROMOTION_CONFIG("CRM_MERCHANT_CATEGORY_PROMOTION_CONFIG", "门店维度品类推广数据看板配置"),

    // 高价值客户看板配置V2
    CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG_V2("CRM_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG_V2", "绩效二期高价值客户汇总看板配置"),
    CRM_QUASI_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG("CRM_QUASI_HIGH_VALUE_CUSTOMER_SUMMARY_CONFIG", "绩效二期准高价值客户汇总看板配置"),
    CRM_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG_V2("CRM_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG_V2", "绩效二期高价值客户详情看板配置"),
    CRM_QUASI_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG("CRM_QUASI_HIGH_VALUE_CUSTOMER_DETAIL_CONFIG", "绩效二期准高价值客户详情看板配置"),
    // 品类推广看板配置V2
    CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG_V2("CRM_CATEGORY_PROMOTION_SUMMARY_CONFIG_V2", "绩效二期品类推广汇总看板配置"),
    CRM_CATEGORY_PROMOTION_DATA_CONFIG_V2("CRM_CATEGORY_PROMOTION_DATA_CONFIG_V2", "绩效二期品类推广数据详情看板配置"),
    CRM_CATEGORY_PROMOTION_MERCHANT_FUL_CONFIG("CRM_CATEGORY_PROMOTION_MERCHANT_FUL_CONFIG", "绩效二期门店维度品类推广履约数据看板配置"),
    CRM_CATEGORY_PROMOTION_MERCHANT_TXN_CONFIG("CRM_CATEGORY_PROMOTION_MERCHANT_TXN_CONFIG", "绩效二期门店维度品类推广交易数据看板配置"),
    CRM_CATEGORY_PROMOTION_SPU_ALL_FUL_CONFIG("CRM_CATEGORY_PROMOTION_SPU_ALL_FUL_CONFIG", "绩效二期SPU维度全部客户品类推广履约数据看板配置"),
    CRM_CATEGORY_PROMOTION_SPU_ALL_TXN_CONFIG("CRM_CATEGORY_PROMOTION_SPU_ALL_TXN_CONFIG", "绩效二期SPU维度全部客户品类推广交易数据看板配置"),
    CRM_CATEGORY_PROMOTION_SPU_CUST_FUL_CONFIG("CRM_CATEGORY_PROMOTION_SPU_CUST_FUL_CONFIG", "绩效二期SPU维度存量&增量客户品类推广履约数据看板配置"),
    CRM_CATEGORY_PROMOTION_SPU_CUST_TXN_CONFIG("CRM_CATEGORY_PROMOTION_SPU_CUST_TXN_CONFIG", "绩效二期SPU维度存量&增量客户品类推广交易数据看板配置"),
    // 月度绩效得分看板配置
    CRM_PB_PERFORMANCE_M1_SUMMARY_CONFIG("CRM_PB_PERFORMANCE_M1_SUMMARY_CONFIG", "绩效二期PB标品M1汇总看板配置"),
    CRM_PB_PERFORMANCE_SUMMARY_CONFIG("CRM_PB_PERFORMANCE_SUMMARY_CONFIG", "绩效二期PB标品汇总看板配置"),
    CRM_PB_PERFORMANCE_DATA_CONFIG("CRM_PB_PERFORMANCE_DATA_CONFIG", "绩效二期PB标品数据详情看板配置"),
    CRM_SCORE_PERFORMANCE_M1_SUMMARY_CONFIG("CRM_SCORE_PERFORMANCE_M1_SUMMARY_CONFIG", "绩效二期利润积分M1汇总看板配置"),
    CRM_SCORE_PERFORMANCE_SUMMARY_CONFIG("CRM_SCORE_PERFORMANCE_SUMMARY_CONFIG", "绩效二期利润积分汇总看板配置"),
    CRM_SCORE_PERFORMANCE_DATA_CONFIG("CRM_SCORE_PERFORMANCE_DATA_CONFIG", "绩效二期利润积分数据详情看板配置"),
    // 客户详情页配置
    CRM_MERCHANT_DETAIL_CATEGORY_PRO_FUL_CONFIG("CRM_MERCHANT_DETAIL_CATEGORY_PRO_FUL_CONFIG", "绩效二期门店详情页品类推广履约数据看板配置"),
    CRM_MERCHANT_DETAIL_CATEGORY_PRO_TXN_CONFIG("CRM_MERCHANT_DETAIL_CATEGORY_PRO_TXN_CONFIG", "绩效二期门店详情页品类推广交易数据看板配置"),
    CRM_MERCHANT_DETAIL_HIGH_VALUE_CONFIG("CRM_MERCHANT_DETAIL_HIGH_VALUE_CONFIG", "绩效二期门店详情页高价值履约数据看板配置"),

    ;

    ConfigValueEnum(String key, String remark) {
        this.key = key;
        this.remark = remark;
    }

    private String key;

    private String remark;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
