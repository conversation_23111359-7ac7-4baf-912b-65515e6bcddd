package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/5 11:25
 */
@Getter
@AllArgsConstructor
public enum DownloadBizTypeEnum {

    CRM_TASK_IMPORT_RESULT(43,"任务导入结果"),
    CRM_TASK_DETAIL(44,"任务进展"),
    BATCH_SAMPLE_APPLY_FAIL_DATA(1001,"批量创建样品单失败数据导出")
    ;
    /**
     * 下载业务类型
     */
    private final int bizType;
    /**
     * 描述
     */
    private final String desc;
}
