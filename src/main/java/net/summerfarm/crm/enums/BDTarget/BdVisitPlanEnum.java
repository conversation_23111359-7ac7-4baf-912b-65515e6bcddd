package net.summerfarm.crm.enums.BDTarget;

/**
 * BD拜访计划枚举
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public class BdVisitPlanEnum {

    /**
     * 拜访计划状态枚举
     */
    public enum Status {
        // 草稿
        DRAFT(0, "草稿"),
        // 生效中
        ACTIVE(1, "生效中");

        private final Integer code;
        private final String desc;

        Status(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static Status getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 锁定状态枚举
     */
    public enum LockStatus {
        // 未锁定
        UNLOCKED(0, "未锁定"),
        // 已锁定
        LOCKED(1, "已锁定");

        private final Integer code;
        private final String desc;

        LockStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static LockStatus getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (LockStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 拜访类型枚举
     */
    public enum VisitType {
        // 线下拜访
        OFFLINE(0, "线下拜访"),
        // 线上拜访
        ONLINE(1, "线上拜访");

        private final Integer code;
        private final String desc;

        VisitType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static VisitType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (VisitType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 拜访状态枚举
     */
    public enum VisitStatus {
        // 未拜访
        NOT_VISITED(0, "未拜访"),
        // 已拜访
        VISITED(1, "已拜访");

        private final Integer code;
        private final String desc;

        VisitStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static VisitStatus getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (VisitStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 门店下单状态枚举
     */
    public enum MerchantOrderStatus {
        // 拜访日未下单
        NOT_ORDERED(0, "拜访日未下单"),
        // 拜访日已下单
        ORDERED(1, "拜访日已下单");

        private final Integer code;
        private final String desc;

        MerchantOrderStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static MerchantOrderStatus getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (MerchantOrderStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 添加来源枚举
     */
    public enum AddSource {
        // 初始添加
        INITIAL(0, "初始添加"),
        // 手动添加
        MANUAL(1, "手动添加"),
        // 系统推荐
        SYSTEM_RECOMMEND(2, "系统推荐");

        private final Integer code;
        private final String desc;

        AddSource(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static AddSource getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (AddSource source : values()) {
                if (source.getCode().equals(code)) {
                    return source;
                }
            }
            return null;
        }
    }
}