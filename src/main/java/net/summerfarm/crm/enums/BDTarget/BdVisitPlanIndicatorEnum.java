package net.summerfarm.crm.enums.BDTarget;

/**
 * BD拜访计划指标枚举
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
public class BdVisitPlanIndicatorEnum {

    /**
     * 指标状态枚举
     */
    public enum IndicatorStatus {
        // 未完成
        INCOMPLETE(0, "未完成"),
        // 已完成
        COMPLETED(1, "已完成");

        private final Integer code;
        private final String desc;

        IndicatorStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static IndicatorStatus getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (IndicatorStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 指标类型枚举
     */
    public enum IndicatorType {
        // 拉新客户数
        NEW_CUSTOMER_COUNT(1, "拉新客户数"),
        // 月活客户数
        MONTHLY_ACTIVE_CUSTOMER_COUNT(2, "月活客户数"),
        // 高价值客户数
        HIGH_VALUE_CUSTOMER_COUNT(3, "高价值客户数"),
        // 平台总目标
        PLATFORM_TOTAL_TARGET(4, "平台总目标"),
        // 品类目标
        CATEGORY_TARGET(5, "品类目标"),
        // SKU目标
        SKU_TARGET(6, "SKU目标");

        private final Integer code;
        private final String desc;

        IndicatorType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static IndicatorType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (IndicatorType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        // 未同步
        NOT_SYNCED(0, "未同步"),
        // 已同步
        SYNCED(1, "已同步");

        private final Integer code;
        private final String desc;

        SyncStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static SyncStatus getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (SyncStatus status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 指标优先级枚举
     */
    public enum IndicatorPriority {
        // P1优先级
        P1(1, "P1"),
        // P2优先级
        P2(2, "P2"),
        // P3优先级
        P3(3, "P3"),
        // P4优先级
        P4(4, "P4"),
        // P5优先级
        P5(5, "P5");

        private final Integer code;
        private final String desc;

        IndicatorPriority(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static IndicatorPriority getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (IndicatorPriority priority : values()) {
                if (priority.getCode().equals(code)) {
                    return priority;
                }
            }
            return null;
        }

        /**
         * 判断是否为高优先级（P1, P2, P3）
         */
        public boolean isHighPriority() {
            return this == P1 || this == P2 || this == P3;
        }
    }
}