package net.summerfarm.crm.enums.BDTarget;

import lombok.Getter;

/**
 * 销售每日目标枚举
 *
 * <AUTHOR>
 */
public class BdDailyTargetEnum {

    public static String getMerchantPotentialValueDesc(Double merchantPotentialValue) {
        if (merchantPotentialValue == null || merchantPotentialValue < 0.3 ) {
            return "低";
        }

        if (merchantPotentialValue < 0.6) {
            return "中";
        }

        return "高";
    }

    /**
     * 目标完成提醒
     */
    @Getter
    public enum TargetCompleteReminder {
        // 无提醒
        NO_REMINDER(0, "无提醒"),
        // 有提醒
        HAS_REMINDER(1, "有提醒");

        private final Integer code;
        private final String desc;

        TargetCompleteReminder(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
