package net.summerfarm.crm.enums.BDTarget;

/**
 * 销售目标类型枚举
 * 用于区分不同类型的销售目标及其对应的推荐逻辑
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public class SalesTargetTypeEnum {

    /**
     * 目标类型
     */
    public enum TargetType {
        // 拉新潜力分
        NEW_CUSTOMER_POTENTIAL(1, "拉新潜力分", "针对新客户的拉新目标"),
        // 召回潜力分
        RECALL_POTENTIAL(2, "召回潜力分", "针对流失客户的召回目标"),
        // 高价值客户潜力分
        HIGH_VALUE_CUSTOMER_POTENTIAL(3, "高价值客户潜力分", "针对高价值客户的维护目标"),
        // 下单客户潜力分
        ORDER_CUSTOMER_POTENTIAL(4, "下单客户潜力分", "针对已下单客户的复购目标"),
        // 品类潜力分
        CATEGORY_POTENTIAL(5, "品类潜力分", "针对特定品类的销售目标"),
        // SKU潜力分
        SKU_POTENTIAL(6, "SKU潜力分", "针对特定SKU的销售目标"),
        // SPU潜力分
        SPU_POTENTIAL(7, "SPU潜力分", "针对特定SPU的销售目标");

        private final Integer code;
        private final String desc;
        private final String remark;

        TargetType(Integer code, String desc, String remark) {
            this.code = code;
            this.desc = desc;
            this.remark = remark;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getRemark() {
            return remark;
        }

        /**
         * 根据code获取枚举
         */
        public static TargetType getByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (TargetType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }

        /**
         * 判断是否为新客户目标类型
         */
        public boolean isNewCustomerTarget() {
            return this == NEW_CUSTOMER_POTENTIAL;
        }

        /**
         * 判断是否为已下单客户目标类型
         */
        public boolean isOrderedCustomerTarget() {
            return this == RECALL_POTENTIAL || this == HIGH_VALUE_CUSTOMER_POTENTIAL || this == ORDER_CUSTOMER_POTENTIAL;
        }

        /**
         * 判断是否为商品相关目标类型
         */
        public boolean isProductTarget() {
            return this == CATEGORY_POTENTIAL || this == SKU_POTENTIAL || this == SPU_POTENTIAL;
        }
    }

    /**
     * 客户类型
     */
    public enum CustomerType {
        // 新客户（历史未下过订单）
        NEW_CUSTOMER(0, "新客户"),
        // 老客户（历史下过订单）
        OLD_CUSTOMER(1, "老客户");

        private final Integer code;
        private final String desc;

        CustomerType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 推荐策略类型
     */
    public enum RecommendStrategy {
        // 新客户推荐策略：门店类型排行榜卖的好的品
        NEW_CUSTOMER_STRATEGY("NEW_CUSTOMER", "新客户推荐策略", "根据门店类型排行榜推荐热销商品"),
        // 老客户推荐策略：根据门店常购情况推荐
        OLD_CUSTOMER_STRATEGY("OLD_CUSTOMER", "老客户推荐策略", "根据门店历史购买情况推荐相符商品"),
        // 商品限定推荐策略：在限定范围内推荐
        PRODUCT_LIMITED_STRATEGY("PRODUCT_LIMITED", "商品限定推荐策略", "在指定品类/SKU/SPU范围内推荐最相符商品");

        private final String code;
        private final String desc;
        private final String remark;

        RecommendStrategy(String code, String desc, String remark) {
            this.code = code;
            this.desc = desc;
            this.remark = remark;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getRemark() {
            return remark;
        }
    }
}