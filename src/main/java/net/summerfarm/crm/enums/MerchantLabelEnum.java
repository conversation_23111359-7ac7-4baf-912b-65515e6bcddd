package net.summerfarm.crm.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/5/25 15:55
 */
public enum MerchantLabelEnum {

    WORRY_FREE_DELIVERY("619省心送目标客户"),
    DEPOSIT_PAID("619已付订金"),
    UNPAID_BALANCE("619未付尾款"),
    PARTIAL_BALANCE_PAYMENT("619部分付尾款"),
    FULL_BALANCE_PAYMENT("619全部付尾款");

    private String merchantLabel;

    MerchantLabelEnum() {
    }

    public String getMerchantLabel() {
        return merchantLabel;
    }

    MerchantLabelEnum(String merchantLabel) {
        this.merchantLabel = merchantLabel;
    }

    public static List<String> getAllAnniversaryEnumValue(){
        MerchantLabelEnum[] values = values();
        List<String> allAnniversaryEnumValue = new ArrayList<>();
        for (MerchantLabelEnum labelEnum : values) {
            String merchantLabel = labelEnum.getMerchantLabel();
            allAnniversaryEnumValue.add(merchantLabel);
        }
        return allAnniversaryEnumValue;
    }
}
