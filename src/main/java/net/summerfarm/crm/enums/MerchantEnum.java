package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/29 15:29
 */
public class MerchantEnum {

    public enum Direct{
        /**
         * 未定义
         */
        UNDEFINED,
        /**
         * 账期
         */
        ACCOUNTING_PERIOD,
        /**
         * 现结
         */
        CASH_SETTLEMENT
    }

    @Getter
    @AllArgsConstructor
    public enum Status{
        /**
         * 0-审核成功
         */
        AUDIT_SUCCESS(0, "审核成功"),
        /**
         * 1-审核中
         */
        IN_AUDIT(1, "审核中"),
        /**
         * 2-审核失败
         */
        AUDIT_FAIL(2, "审核失败"),
        /**
         * 4-拉黑
         */
        PULL_BLACK(3, "拉黑"),

        CANCEL(4, "注销");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum BusinessLineEnum {

        /**
         * 鲜沐
         */
        XM(0, "鲜沐"),

        /**
         * pop
         */
        POP(1, "pop");

        private final Integer code;
        private final String description;}
}
