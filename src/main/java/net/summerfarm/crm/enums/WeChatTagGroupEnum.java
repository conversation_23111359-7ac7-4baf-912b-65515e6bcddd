package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Arrays;

@NoArgsConstructor
@AllArgsConstructor
public enum WeChatTagGroupEnum {
    DEFAULT_TAG_GROUP("etndqQCQAAZvOlqJXI-RWdMTSFQuEqGA", "企微标签组"),
    ALL_PLATFORM_HOT_MILK_TAG_GROUP("etndqQCQAAtT3FxnmcjebhwKgFNn7a_Q", "全平台热门商品(乳制品)"),
    ALL_PLATFORM_HOT_FRUIT_TAG_GROUP("etndqQCQAAoZpqZIpJOgnHvda-c0ahCg", "全平台热门商品(鲜果)"),
    ALL_PLATFORM_HOT_OTHER_TAG_GROUP("etndqQCQAAM-5ddaviv1oOhrtvc_70JA", "全平台热门商品(其他)"),
    ALL_BRAND_HOT_GOOD_TAG_GROUP("etndqQCQAAFhOYT0z6zzX8ULK5Lnlypw", "全品类热门商品"),
    MY_BRAND_HOT_GOOD_TAG_GROUP("etndqQCQAA-WMAyqPXadJfYFRjkrRNEQ", "自营品牌热门商品");
    private   String groupId;

    private  String desc;


    public static Boolean exitByName(String name){
        return Arrays.stream(WeChatTagGroupEnum.values()).anyMatch(it -> it.desc.equals(name));
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
