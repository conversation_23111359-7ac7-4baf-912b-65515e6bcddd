package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 品类推广奖励方式枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CategoryPromotionRewardModeEnum {

    /**
     * 交易件数
     */
    TRANSACTION_COUNT(0, "交易件数"),
    /**
     * 履约件数
     */
    FULFILLMENT_COUNT(1, "履约件数");

    private Integer code;
    private String desc;

    public static CategoryPromotionRewardModeEnum getByCode(Integer code) {
        for (CategoryPromotionRewardModeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
