package net.summerfarm.crm.enums;

/**
 * <AUTHOR>
 * @Description 配置类枚举
 * @date 2022/6/14 2:00
 */
public enum AdminConfigValueEnum {

    ADMIN_TURNING_CONFIG("ADMIN_TURNING_CONFIG","admin流转配置"),
            ;
    AdminConfigValueEnum(String key, String remark) {
        this.key = key;
        this.remark = remark;
    }

    private String key;

    private String remark;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
