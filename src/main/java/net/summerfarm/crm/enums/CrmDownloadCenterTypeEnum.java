package net.summerfarm.crm.enums;

import lombok.Getter;

/**
 * CRM上传下载文件类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum CrmDownloadCenterTypeEnum {
    IMPORT_FOLLOW_UP_RECORD(1002, "导入线下拜访记录"),
    IMPORT_BD_DAILY_TARGET(1003, "导入销售每日目标"),
    ;

    private Integer type;
    private String desc;

    CrmDownloadCenterTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
