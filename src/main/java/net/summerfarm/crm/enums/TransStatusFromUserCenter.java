package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public interface TransStatusFromUserCenter {

    @Getter
    @AllArgsConstructor
    public enum Status {
        /**
         * 0-审核中
         */
        IN_AUDIT(0, "审核中"),
        /**
         * 1-审核成功
         */
        AUDIT_SUCCESS(1, "审核成功"),
        /**
         * 2-审核失败
         */
        AUDIT_FAIL(2, "审核失败"),

        CLOSE(3, "关店"),
        /**
         * 4-拉黑
         */
        PULL_BLACK(4, "拉黑"),

        CANCEL(5, "注销");

        /**
         * 状态类型编码
         */
        private Integer code;
        /**
         * 状态类型描述
         */
        private String desc;

        /**
         * 状态描述
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (MerchantStoreEnums.Status statusEnum : MerchantStoreEnums.Status.values()) {
                if (Objects.equals(code, statusEnum.getCode())) {
                    return statusEnum.getDesc();
                }
            }
            return null;
        }

        /**
         * 转换鲜沐门店的状态
         * 默认：审核中
         *
         * @return
         */
        public static Integer transStatusFromUserCenter(String sourceStatus) {
            if (Objects.isNull(sourceStatus)) {
                return MerchantStoreEnums.Status.IN_AUDIT.getCode();
            }
            Integer status;
            switch (sourceStatus) {
                case "0":
                    status = MerchantEnum.Status.IN_AUDIT.getCode();
                    break;
                case "1":
                    status = MerchantEnum.Status.AUDIT_SUCCESS.getCode();
                    break;
                case "2":
                    status = MerchantEnum.Status.AUDIT_FAIL.getCode();
                    break;
                case "4":
                    status = MerchantEnum.Status.PULL_BLACK.getCode();
                    break;
                case "5":
                    status = MerchantEnum.Status.CANCEL.getCode();
                    break;
                default:
                    status = MerchantEnum.Status.IN_AUDIT.getCode();
            }
            return status;
        }
    }


}
