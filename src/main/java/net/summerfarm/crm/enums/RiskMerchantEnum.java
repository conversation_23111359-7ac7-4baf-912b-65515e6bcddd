package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/11/21 16:18
 */
@Data
public class RiskMerchantEnum {
    /**
     * 触发场景
     *
     * <AUTHOR>
     * @date 2023/11/21
     */
    @Getter
    @AllArgsConstructor
    public enum TriggerOccasions{
        REGISTER_MERCHANT(0,"新注册门店"),
        ORDER_MERCHANT(1,"存量动销门店"),
        ;

        private final Integer code;

        private final String desc;

    }

    /**
     * 触发场景
     *
     * <AUTHOR>
     * @date 2023/11/21
     */
    @Getter
    @AllArgsConstructor
    public enum TriggerClassification{
        SUSPECTED_DUPLICATION(0,"疑似重复"),
        SUSPECTED_SPURIOUS(1,"疑似虚假"),
        SUSPECTED_MOLTING(2,"疑似换壳"),
        ;

        private final Integer code;

        private final String desc;

    }

    /**
     * 相似门店来源
     *
     * <AUTHOR>
     * @date 2023/11/21
     */
    @Getter
    @AllArgsConstructor
    public enum SourceType{
        XM(0,"XM"),
        SAAS(1,"SAAS"),

        ;

        private final Integer code;

        private final String desc;

    }
}
