package net.summerfarm.crm.enums;


import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public class FollowRecordEnum {

    /**
     * 拜访类型
     */
    public enum Type{
        /**
         * 0-拜访
         */
        VISIT,
        /**
         * 1-陪访
         */
        ESCORT
    }

    /**
     * 拜访状态
     */
    public enum Status{
        /**
         * (0,"未跟进")
         */
        NO_PAYMENT,
        /**
         * (1,"已跟进")
         */
        WAIT_DELIVERY,
        /**
         * (2,"已跟进且下单")
         */
        DELIVERING,
        /**
         * (3,"联系不上")
         */
        RECEIVED,
        /**
         * (4,"放弃跟进")
         */
        APPLY_FOR_DRAWBACK
    }

    public enum VisitObjective{
        PULL_NEW(0,"拉新"),
        REMINDERS(1,"催月活"),
        CUSTOMER_MAINTENANCE(2,"客户维护"),
        MARKET(3,"拓品"),
        AFTER_SALES(4,"售后处理"),
        COLLECT_THE_GOODS(5,"催省心送"),
        MARKET_RESEARCH(6,"市场调研"),
        CUSTOMER_SERVICE(7,"客户服务"),
        ASSESS_SALES_SKILLS(8,"评估销售技能"),
        MERGE_DUPLICATE_ACCOUNTS(9,"重复账号合并"),
        ;

        private int id;

        private String statusName;

        VisitObjective(int id, String statusName) {
            this.id = id;
            this.statusName = statusName;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getStatusName() {
            return statusName;
        }

        public void setStatusName(String statusName) {
            this.statusName = statusName;
        }

        public static String getName(Integer id){
            VisitObjective[] visitObjectives = values();
            for (VisitObjective visitObjective : visitObjectives) {
                if(Objects.equals(visitObjective.getId(),id)){
                    return visitObjective.getStatusName();
                }
            }
            return null;
        }

        public static VisitObjective getByStatusName(String statusName){
            if (statusName == null) {
                return null;
            }
            VisitObjective[] visitObjectives = values();
            for (VisitObjective visitObjective : visitObjectives) {
                if (Objects.equals(visitObjective.getStatusName(), statusName)) {
                    return visitObjective;
                }
            }
            return null;
        }

        /**
         * 获取线下有效的拜访目的
         * 
         * @return
         */
        public static List<VisitObjective> getOfflineValidVisitObjectives() {
            return Arrays.asList(PULL_NEW, REMINDERS, CUSTOMER_MAINTENANCE, MARKET, AFTER_SALES, COLLECT_THE_GOODS,
                    MARKET_RESEARCH, CUSTOMER_SERVICE);
        }
    }

}
