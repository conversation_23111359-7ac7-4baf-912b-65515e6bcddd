package net.summerfarm.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:32
 */
public class CategoryQuotaEnum {
    @Getter
    @AllArgsConstructor
    public enum QuotaType {
        CATEGORY(0, "品类券"),
        MONTH_LIVING(1, "月活券"),

        CATEGORY_PRICE(2, "品类券-价格补贴"),
        ;
        private final Integer code;

        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum TargetType {
        ALL(0, "全部"),
        TYPE(1, "按类型"),
        SKU(2, "按照sku"),
        ;
        private final Integer code;

        private final String desc;

        public static String getDesc(Integer code) {
            TargetType[] values = values();
            for (TargetType targetType : values) {
                if (Objects.equals(targetType.code, code)) {
                    return targetType.desc;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum PoolStatus {
        COMMON(0, "正常"),
        DELETE(1, "删除"),
        ;
        private final Integer code;

        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum PoolUpdateLimit {
        COMMON(0, "限制"),
        LIMIT(1, "不限"),
        ;
        private final Integer code;

        private final String desc;
    }
    @Getter
    @AllArgsConstructor
    public enum AutoApproveStatus {
        CLOSE(0, "关闭"),
        OPEN(1, "开启"),
        ;
        private final Integer code;

        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum PoolOperateLogType {
        ADD(0, "创建"),
        DIVIDE(1, "划分"),
        DELETE(2, "作废"),
        APPLY(3, "申请")

        ;
        private final Integer code;

        private final String desc;
    }
}
