package net.summerfarm.crm.enums;

import lombok.Getter;

/**
 * 灰度项目枚举
 *
 * <AUTHOR>
 */
@Getter
public enum GrayProjectEnum {
    BD_DAILY_TARGET("BD_DAILY_TARGET", "销售每日目标"),
    ;

    private String code;
    private String desc;

    GrayProjectEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GrayProjectEnum getByCode(String code) {
        for (GrayProjectEnum grayProjectEnum : values()) {
            if (grayProjectEnum.getCode().equals(code)) {
                return grayProjectEnum;
            }
        }
        return null;
    }
}
