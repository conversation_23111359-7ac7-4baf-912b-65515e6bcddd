package net.summerfarm.crm.es.model;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;
import org.dromara.easyes.annotation.IndexName;

import java.time.LocalDateTime;
import java.util.List;

@Data
@IndexName("xianmu_merchant_crm")
public class XianmuMerchantCrm {

    /**
     * es主键
     */
    private String id;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 门店名称英文
     */
    private String mnameEn;

    /**
     * 主营类型
     */
    private String merchantType;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    private Long areaNo;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 最后下单时间
     */
    private LocalDateTime lastOrderTime;

    /**
     * 跟进销售id. 等于0则表示在公海
     */
    private Long bdId;

    /**
     * 跟进销售姓名
     */
    private String bdName;

    /**
     * 销售重新指派时间
     */
    private LocalDateTime reassignTime;

    /**
     * 上次跟进的销售id. 等于0则表示上次在公海
     */
    private Long lastFollowUpBdId;

    /**
     * 销售释放倒计时
     */
    private Integer dangerDay;

    /**
     * 销售释放倒计时原因(规则)
     */
    private String dangerDayReason;

    /**
     * 商户标签
     */
    private List<String> merchantLabel;

    /**
     * 是否添加官微:0:是;1:否；
     */
    private Integer officialWechatFlag;

    /**
     * 是否添加销微:0:是;1:否；
     */
    private Integer bdWechatFlag;

    /**
     * 释放时间
     */
    private LocalDateTime releaseTime;

    /**
     * 释放保护原因
     */
    private String protectReason;

    /**
     * 审核状态:0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑
     */
    private Long islock;

    /**
     * 经营状态:正常经营(0),已确认倒闭(1),倒闭确认中(2)
     */
    private Integer operateStatus;

    /**
     * 门店潜力值
     */
    private Double potentialValue;

    /**
     * 地址
     */
    private String address;

    /**
     * 业务线
     */
    private Long businessLine;

    /**
     * 关怀销售id
     */
    private Long careBdId;

    /**
     * 联系人信息
     */
    private List<Contact> contacts;

    /**
     * 核心商户标签
     */
    private Long coreMerchantTag;

    /**
     * 未登录天数
     */
    private Integer daysNotLoggedIn;

    /**
     * 无订单天数
     */
    private Long daysWithoutOrder;

    /**
     * 无订单跟进天数
     */
    private Long daysWithoutOrderFollow;

    /**
     * F值
     */
    private String fValue;

    /**
     * 等级
     */
    private Long grade;

    /**
     * 高价值标签
     */
    private String highValueLabel;

    /**
     * 高价值标签V2
     */
    private String highValueLabelV2;

    /**
     * 生命周期
     */
    private String lifecycle;

    /**
     * M值
     */
    private String mValue;

    /**
     * 商户生命周期
     */
    private Long merchantLifecycle;

    /**
     * 商户白名单标签
     */
    private Long merchantWhiteListTag;

    /**
     * 未拜访
     */
    private Long notVisited;

    /**
     * 订单频率
     */
    private Long orderFrequency;

    /**
     * R值
     */
    private String rValue;

    /**
     * 规模
     */
    private String size;

    /**
     * 定时跟进类型
     */
    private Long timingFollowType;

    /**
     * 总GMV
     */
    private Double totalGmv;

    /**
     * 价值标签
     */
    private String valueLabel;

    /**
     * 联系人信息内部类
     */
    @Data
    public static class Contact {
        /**
         * 联系人id
         */
        private Long contactId;

        /**
         * 联系人
         */
        private String contact;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 省
         */
        private String province;

        /**
         * 市
         */
        private String city;

        /**
         * 区
         */
        private String area;

        /**
         * 地址
         */
        private String address;

        /**
         * 门牌号
         */
        private String houseNumber;

        /**
         * POI坐标
         */
        private PoiVO poi;

        /**
         * POI备注
         */
        private String poiNote;

        /**
         * 状态
         */
        private Long status;
    }

}