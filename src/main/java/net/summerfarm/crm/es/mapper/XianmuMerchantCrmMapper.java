package net.summerfarm.crm.es.mapper;

import net.summerfarm.crm.enums.OperateStatusEnum;
import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import net.summerfarm.crm.common.util.EsClientPoolUtil;
import net.summerfarm.crm.model.query.objectiveManagement.EsPotentialMerchantQuery;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.BaseEsMapper;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.GeoDistanceQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.common.geo.GeoDistance;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface XianmuMerchantCrmMapper extends BaseEsMapper<XianmuMerchantCrm> {

    Logger log = LoggerFactory.getLogger(XianmuMerchantCrmMapper.class);

    /**
     * ES索引名称
     */
    String INDEX_NAME = "xianmu_merchant_crm";

    default List<XianmuMerchantCrm> selectByMIdIn(Collection<Long> mIds) {
        LambdaEsQueryWrapper<XianmuMerchantCrm> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.in(XianmuMerchantCrm::getMId, mIds);

        return this.selectList(queryWrapper);
    }

    /**
     * 批量更新门店潜力值（分批处理，每50个一次）
     * @param merchantPotentialMap 门店ID和潜力值的映射
     * @return 更新成功的数量
     */
    default int batchUpdatePotentialValue(Map<Long, BigDecimal> merchantPotentialMap) {
        if (merchantPotentialMap == null || merchantPotentialMap.isEmpty()) {
            return 0;
        }

        int totalSuccessCount = 0;
        int batchSize = 50; // 每批处理50个
        
        // 将Map转换为List进行分批处理
        java.util.List<Map.Entry<Long, BigDecimal>> entryList = new java.util.ArrayList<>(merchantPotentialMap.entrySet());
        
        for (int i = 0; i < entryList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, entryList.size());
            java.util.List<Map.Entry<Long, BigDecimal>> batch = entryList.subList(i, endIndex);
            
            RestHighLevelClient client = null;
            try {
                client = EsClientPoolUtil.getClient();
                BulkRequest bulkRequest = new BulkRequest();
                
                // 构建批量更新请求
                for (Map.Entry<Long, BigDecimal> entry : batch) {
                    Long merchantId = entry.getKey();
                    BigDecimal potentialValue = entry.getValue();
                    
                    UpdateRequest updateRequest = new UpdateRequest(INDEX_NAME, merchantId.toString());
                    // 只更新potentialValue字段，不影响其他字段
                    updateRequest.doc("{\"potentialValue\":" + potentialValue + "}", XContentType.JSON);
                    updateRequest.retryOnConflict(3); // 版本冲突重试
                    bulkRequest.add(updateRequest);
                }
                
                // 执行批量更新
                BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
                
                if (bulkResponse.hasFailures()) {
                    log.warn("第{}批次更新门店潜力值部分失败: {}", (i / batchSize + 1), bulkResponse.buildFailureMessage());
                }
                
                // 计算本批次成功数量
                int batchSuccessCount = (int) java.util.Arrays.stream(bulkResponse.getItems())
                        .filter(item -> !item.isFailed())
                        .count();
                        
                totalSuccessCount += batchSuccessCount;
                
                log.info("第{}批次更新门店潜力值完成：批次数量={}, 成功数量={}", 
                        (i / batchSize + 1), batch.size(), batchSuccessCount);
                
            } catch (Exception e) {
                log.error("第{}批次更新门店潜力值失败", (i / batchSize + 1), e);
            } finally {
                if (client != null) {
                    EsClientPoolUtil.returnClient(client);
                }
            }
        }
        
        log.info("批量更新门店潜力值全部完成：总数量={}, 总成功数量={}", 
                merchantPotentialMap.size(), totalSuccessCount);
        
        return totalSuccessCount;
     }

    /**
     * 统一的ES潜力值客户查询方法
     *
     * @param query 统一查询参数
     * @return 分页结果
     */
    default PageInfo<XianmuMerchantCrm> queryPotentialMerchantsUnified(EsPotentialMerchantQuery query) {
        RestHighLevelClient client = null;
        try {
            client = EsClientPoolUtil.getClient();
            
            // 构建ES查询请求
            SearchRequest searchRequest = new SearchRequest(INDEX_NAME);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

            // 构建布尔查询
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            // 基础过滤条件（附近查询需要）
            if (query.getNeedBaseFilter()) {
                boolQuery.must(QueryBuilders.termQuery("islock", 0));
                boolQuery.must(QueryBuilders.termQuery("operateStatus", OperateStatusEnum.NORMAL.getCode()));
            }

            // 潜力值过滤（推荐查询需要）
//            if (query.getNeedPotentialFilter()) {
//                boolQuery.must(QueryBuilders.rangeQuery("potentialValue").gt(0));
//            }

            // bdid条件
            if (query.getBdId() != null) {
                boolQuery.must(QueryBuilders.termQuery(query.getBdFieldName(), query.getBdId()));
            }

            // 地理位置条件
            if (query.getPoi() != null && query.getQueryDistance() != null) {
                GeoDistanceQueryBuilder geoQuery = QueryBuilders.geoDistanceQuery(query.getGeoFieldName())
                        .point(query.getPoi().getLat(), query.getPoi().getLon())
                        .distance(query.getQueryDistance(), DistanceUnit.METERS);
                boolQuery.must(geoQuery);
            }

            // 省市区条件
            if (query.getProvince() != null && !query.getProvince().isEmpty()) {
                String provinceField = query.getNeedKeywordSuffix() ? "province.keyword" : "province";
                boolQuery.must(QueryBuilders.termQuery(provinceField, query.getProvince()));
            }
            if (query.getCity() != null && !query.getCity().isEmpty()) {
                String cityField = query.getNeedKeywordSuffix() ? "city.keyword" : "city";
                boolQuery.must(QueryBuilders.termQuery(cityField, query.getCity()));
            }
            if (query.getArea() != null && !query.getArea().isEmpty()) {
                String areaField = query.getNeedKeywordSuffix() ? "area.keyword" : "area";
                boolQuery.must(QueryBuilders.termQuery(areaField, query.getArea()));
            }

            // 过滤门店mId列表
            if (query.getFilterMIdList() != null && !query.getFilterMIdList().isEmpty()) {
                boolQuery.mustNot(QueryBuilders.termsQuery("mId", query.getFilterMIdList()));
            }

            // 生命周期过滤
            if (query.getLifecycleList() != null && !query.getLifecycleList().isEmpty()) {
                boolQuery.must(QueryBuilders.termsQuery("lifecycle.keyword", query.getLifecycleList()));
            }

            // 高价值客户标签过滤
            if (query.getHighValueLabelList() != null && !query.getHighValueLabelList().isEmpty()) {
                boolQuery.must(QueryBuilders.termsQuery("highValueLabelV2", query.getHighValueLabelList()));
            }

            // 门店名称或地址搜索
            if (query.getStoreNameOrAddressSearch() != null && !query.getStoreNameOrAddressSearch().trim().isEmpty()) {
                String searchText = query.getStoreNameOrAddressSearch().trim();
                boolQuery.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery("mname", "*" + searchText + "*"))
                    .should(QueryBuilders.wildcardQuery("address", "*" + searchText + "*"))
                    .minimumShouldMatch(1)
                );
            }

            sourceBuilder.query(boolQuery);

            // 排序
            applySortingUnified(sourceBuilder, query);

            // 分页
            int pageNum = query.getPageNum() != null ? query.getPageNum() : 1;
            int pageSize = query.getPageSize() != null ? query.getPageSize() : 10;
            sourceBuilder.from((pageNum - 1) * pageSize);
            sourceBuilder.size(pageSize);

            searchRequest.source(sourceBuilder);

            // 执行查询
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

            // 解析结果
            List<XianmuMerchantCrm> merchants = new ArrayList<>();
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                XianmuMerchantCrm merchant = JSON.parseObject(hit.getSourceAsString(), XianmuMerchantCrm.class);
                merchants.add(merchant);
            }

            // 构建分页信息
            PageInfo<XianmuMerchantCrm> pageInfo = new PageInfo<>(merchants);
            pageInfo.setPageNum(pageNum);
            pageInfo.setPageSize(pageSize);
            pageInfo.setTotal(searchResponse.getHits().getTotalHits().value);
            pageInfo.setPages((int) Math.ceil((double) pageInfo.getTotal() / pageSize));

            return pageInfo;
        } catch (Exception e) {
            log.error("ES查询潜力值客户失败", e);
            return new PageInfo<>(new ArrayList<>());
        } finally {
            if (client != null) {
                EsClientPoolUtil.returnClient(client);
            }
        }
    }

    /**
     * 统一的排序方法
     *
     * @param sourceBuilder ES查询构建器
     * @param query 统一查询参数
     */
    default void applySortingUnified(SearchSourceBuilder sourceBuilder, EsPotentialMerchantQuery query) {
        Integer sortType = query.getSortType();
        
        if (sortType == null || sortType == 1) {
            // 潜力值排序（从高到低）
            sourceBuilder.sort(SortBuilders.fieldSort("potentialValue").order(SortOrder.DESC));
            // 二级排序：创建时间（从新到旧）
            sourceBuilder.sort(SortBuilders.fieldSort("mId").order(SortOrder.DESC));
        } else if (sortType == 2 && query.getPoi() != null) {
            // 地理距离排序（从近到远）
            GeoDistanceSortBuilder geoSort = SortBuilders.geoDistanceSort(query.getGeoFieldName(), 
                    query.getPoi().getLat(), query.getPoi().getLon())
                    .unit(DistanceUnit.METERS)
                    .order(SortOrder.ASC)
                    .geoDistance(GeoDistance.ARC);
            sourceBuilder.sort(geoSort);
            // 二级排序：潜力值（从高到低）
            sourceBuilder.sort(SortBuilders.fieldSort("potentialValue").order(SortOrder.DESC));
        } else {
            // 默认排序：潜力值（从高到低）
            sourceBuilder.sort(SortBuilders.fieldSort("potentialValue").order(SortOrder.DESC));
            sourceBuilder.sort(SortBuilders.fieldSort("mId").order(SortOrder.DESC));
        }
    }
}
