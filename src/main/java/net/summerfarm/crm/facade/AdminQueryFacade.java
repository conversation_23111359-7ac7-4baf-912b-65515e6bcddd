package net.summerfarm.crm.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.admin.AdminQueryProvider;
import net.summerfarm.client.req.admin.AdminQueryReq;
import net.summerfarm.client.resp.admin.AdminResp;
import net.summerfarm.crm.common.util.ExceptionUtils;
import net.summerfarm.crm.facade.converter.admin.AdminConvert;
import net.summerfarm.crm.facade.dto.AdminDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class AdminQueryFacade {

    @DubboReference
    private AdminQueryProvider adminQueryProvider;

    public List<AdminDTO> batchQueryAdminByIds(List<Integer> adminIds) {
        if (CollectionUtils.isEmpty(adminIds)) {
            return Collections.emptyList();
        }
        AdminQueryReq req = new AdminQueryReq();
        req.setAdminIds(adminIds);
        DubboResponse<List<AdminResp>> dubboResponse = adminQueryProvider.batchQueryAdmins(req);
        ExceptionUtils.checkDubboResponse(dubboResponse);
        return AdminConvert.convertToAdminDTOList(dubboResponse.getData());
    }

    public AdminDTO queryByAdminId(Long adminId) {
        if (adminId == null) {
            return null;
        }
        DubboResponse<AdminResp> dubboResponse = adminQueryProvider.queryByAdminId(adminId);
        ExceptionUtils.checkDubboResponse(dubboResponse);
        return AdminConvert.convertToAdminDTO(dubboResponse.getData());
    }

}
