package net.summerfarm.crm.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.req.openai.OpenAIChatReq;
import net.summerfarm.common.client.resp.openai.OpenAIChatResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import net.summerfarm.common.client.provider.OpenAIChatProvider;

@Component
@Slf4j
public class OpenAIChatFacade {

    @DubboReference(timeout = 30000)
    private OpenAIChatProvider openAIChatProvider;

    /**
     * 对话
     * @param userMessage
     * @param systemPrompt
     * @param readTimeOut
     * @return
     */
    public String chat(String userMessage, String systemPrompt, Integer readTimeOut) {
        OpenAIChatReq req = new OpenAIChatReq();
        req.setSystemPrompt(systemPrompt);
        req.setUserMessage(userMessage);
        req.setReadTimeOut(readTimeOut == null ? 30 : readTimeOut);
        DubboResponse<OpenAIChatResp> response = openAIChatProvider.chat(req);
        if (!response.isSuccess()) {
            log.error("OpenAIChatFacade.chat error, req: {}, response: {}", req, response);
        }
        return response.getData().getRespMessage();
    }

}
