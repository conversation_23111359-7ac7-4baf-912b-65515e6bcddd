package net.summerfarm.crm.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.merchant.MerchantSituationManageProvider;
import net.summerfarm.manage.client.merchant.req.MerchantSituationAutoAgreeReq;
import net.summerfarm.manage.client.merchant.resp.MerchantSituationAutoAgreeResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MerchantSituationFacade {

    @DubboReference
    private MerchantSituationManageProvider merchantSituationManageProvider;

    public void autoAgree(Long merchantSituationId) {
        if (merchantSituationId == null) {
            throw new RuntimeException("自动审核客情券，请求参数为空");
        }
        MerchantSituationAutoAgreeReq autoAgreeReq = new MerchantSituationAutoAgreeReq();
        autoAgreeReq.setMerchantSituationId(merchantSituationId);
        DubboResponse<MerchantSituationAutoAgreeResp> dubboResponse = merchantSituationManageProvider.autoAgree(autoAgreeReq);
        if (dubboResponse == null || !dubboResponse.isSuccess()) {
            log.error("客情自动同意失败，dubboResponse:{} {}", merchantSituationId, dubboResponse);
        }
    }
}
