package net.summerfarm.crm.facade;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.SystemConstant;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.enums.MerchantAccountEnums;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountPageReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 买家中心门店账号查询接口
 *
 * <AUTHOR>
 * @date 2023/11/14 14:10
 */
@Slf4j
@Component
public class MerchantAccountQueryFacade {
    @DubboReference
    private MerchantStoreAccountQueryProvider merchantStoreAccountQueryProvider;

    /**
     * 根据mid获取门店主账户
     *
     * @param mid 中期
     * @return {@link MerchantStoreAccountResultResp}
     */
    public MerchantStoreAccountResultResp getPrimaryAccount(Long mid){
        List<MerchantStoreAccountResultResp> list = this.getPrimaryAccountList(Collections.singletonList(mid));
        return CollUtil.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 批量获取门店主账户
     *
     * @param midList midList
     * @return {@link List<MerchantStoreAccountResultResp>}
     */
    public List<MerchantStoreAccountResultResp> getPrimaryAccountList(List<Long> midList){
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setMIdList(midList);
        req.setDeleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode());
        req.setType(MerchantAccountEnums.Type.MANAGER.getCode());
        return this.getAccounts(req);
    }

    /**
     * 批量获取门店所有账户
     *
     * @param midList midList
     * @return {@link List<MerchantStoreAccountResultResp>}
     */
    public List<MerchantStoreAccountResultResp> getAllAccountList(List<Long> midList){
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setMIdList(midList);
        req.setDeleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode());
        req.setStatus(MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode());
        return this.getAccounts(req);
    }



    /**
     * 获取指定mid门店下所有正常的账户
     */
    public List<MerchantStoreAccountResultResp> getMerchantAccount(Long mid) {
        MerchantStoreAccountQueryReq req = new MerchantStoreAccountQueryReq();
        req.setMId(mid);
        req.setStatus(MerchantAccountEnums.Status.AUDIT_SUCCESS.getCode());
        req.setDeleteFlag(MerchantAccountEnums.DeleteFlag.NORMAL.getCode());
        return this.getAccounts(req);
    }


    private List<MerchantStoreAccountResultResp> getAccounts(MerchantStoreAccountQueryReq req){
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        DubboResponse<List<MerchantStoreAccountResultResp>> accounts = merchantStoreAccountQueryProvider.getMerchantStoreAccountsByPrimaryKeys(req);
        if (!accounts.isSuccess()) {
            throw new BizException("获取商户联系人信息失败");
        }
        return accounts.getData();
    }
}
