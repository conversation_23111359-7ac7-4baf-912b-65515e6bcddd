package net.summerfarm.crm.facade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.SystemConstant;
import net.summerfarm.crm.model.dto.ContactDto;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.enums.MerchantAddressEnums;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantExtendQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantContactResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.domain.MerchantAddressDomainResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 买家中心地址-联系人信息接口
 *
 * <AUTHOR>
 * @date 2023/11/13 18:02
 */
@Slf4j
@Component
public class ContactQueryFacade {

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;


    /**
     * 根据商户id获取商户状态正常的联系人信息
     * @param mId
     * @return
     */
    public List<ContactDto> getMerchantContact(Long mId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        req.setMId(mId);
        req.setStatus(MerchantAddressEnums.status.NORMAL.getCode());
        return getContact(req);
    }

    /**
     * 获取指定门店下状态正常的默认地址
     * @param mId
     * @return
     */
    public List<ContactDto> getDefaultMerchantContact(Long mId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        req.setMId(mId);
        req.setDefaultFlag(1);
        req.setStatus(MerchantAddressEnums.status.NORMAL.getCode());
        return getContact(req);
    }

    public List<ContactDto> getMerchantContactList(List<Long> contactIds) {
        if (CollectionUtil.isEmpty(contactIds)){
            return new ArrayList<>();
        }
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        req.setXmContactIdList(contactIds);
        return getContact(req);
    }

    public List<ContactDto> getContact(MerchantAddressQueryReq req){
        DubboResponse<List<MerchantAddressDomainResp>> addressAndContacts = merchantAddressQueryProvider.getAddressAndContacts(req);
        if (addressAndContacts.isSuccess()) {
            return this.converterToContactDtoList(addressAndContacts.getData());
        }
        throw new BizException(addressAndContacts.getMsg());
    }


    /**
     * 根据contactId获取地址信息
     * @param contactId
     * @return
     */
    public ContactDto getMerchantContactById(Long contactId) {
        MerchantAddressQueryReq req = new MerchantAddressQueryReq();
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        req.setXmContactId(contactId);
        req.setStatus(MerchantAddressEnums.status.NORMAL.getCode());
        DubboResponse<List<MerchantAddressDomainResp>> addressAndContacts = merchantAddressQueryProvider.getAddressAndContacts(req);
        if (addressAndContacts.isSuccess()) {
            if (CollectionUtil.isEmpty(addressAndContacts.getData())) {
                return null;
            }
            return this.converterToContactDtoList(addressAndContacts.getData()).get(0);
        }
        throw new BizException(addressAndContacts.getMsg());
    }

    private List<ContactDto> converterToContactDtoList(List<MerchantAddressDomainResp> addressDomainResps) {
        if (CollUtil.isEmpty(addressDomainResps)) {
            return Collections.emptyList();
        }
        List<ContactDto> contactEntities = new ArrayList<>();
        addressDomainResps.forEach(address -> {
            ContactDto dto = new ContactDto();
            dto.setTenantId(address.getTenantId());
            dto.setStoreId(address.getStoreId());
            dto.setContactId(address.getXmContactId());
            dto.setmId(address.getMId());
            dto.setProvince(address.getProvince());
            dto.setCity(address.getCity());
            dto.setArea(address.getArea());
            dto.setAddress(address.getAddress());
            dto.setHouseNumber(address.getHouseNumber());
            dto.setPoiNote(address.getPoiNote());
            dto.setIsDefault(address.getDefaultFlag());
            dto.setStatus(address.getStatus());
            dto.setContactId(address.getXmContactId());
            dto.setRemark(address.getRemark());
            dto.setAddressRemark(address.getAddressRemark());
            List<MerchantContactResultResp> list = address.getContactList();
            if (CollUtil.isNotEmpty(list)) {
                MerchantContactResultResp merchantContactResultResp = list.get(0);
                dto.setPhone(merchantContactResultResp.getPhone());
                dto.setContact(merchantContactResultResp.getName());
            }
            contactEntities.add(dto);
        });
        return contactEntities;
    }




}
