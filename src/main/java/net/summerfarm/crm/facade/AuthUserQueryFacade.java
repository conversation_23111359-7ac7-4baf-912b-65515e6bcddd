package net.summerfarm.crm.facade;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static net.summerfarm.crm.facade.FeiShuPersonalMsgFacade.XIANMU_TENANT_ID;

@Component
@Slf4j
public class AuthUserQueryFacade {
    @DubboReference
    AuthUserProvider authUserProvider;
    @DubboReference
    AuthUserAuthProvider authUserAuthProvider;

    public AuthUserAuthResp selectByAdminId(Integer type, Long baseUserId) {
        DubboResponse<AuthUserAuthResp> authUserAuthRespDubboResponse = authUserAuthProvider.queryUserAuth(SystemOriginEnum.ADMIN, baseUserId, type);
        AuthUserAuthResp data = authUserAuthRespDubboResponse.getData();
        return data;
    }



    public List<Long> getUserBaseIdBySourceRoleIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        DubboResponse<List<Long>> response = authUserProvider.getUserBaseIdsBySourceRoleIds(SystemOriginEnum.ADMIN, roleIds);
        return response.getData();
    }


    public List<AuthUserAuthResp> queryAuthResp(AuthTypeEnum type, Integer bizUserId) {
        AuthUserAuthQueryInput userAuthQueryInput = new AuthUserAuthQueryInput();
        userAuthQueryInput.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        userAuthQueryInput.setTenantId(XIANMU_TENANT_ID);
        userAuthQueryInput.setAuthType(type);
        userAuthQueryInput.setBizUserIds(Collections.singletonList(Long.valueOf(bizUserId)));
        DubboResponse<List<AuthUserAuthResp>> authUserAuthRespDubboResponse = authUserAuthProvider.queryAuthUserAuthByInput(userAuthQueryInput);
        if (authUserAuthRespDubboResponse.isSuccess()){
            return authUserAuthRespDubboResponse.getData();
        }
        log.warn("queryAuthResp error bizUserId {}, result{}",bizUserId, JSONUtil.toJsonStr(authUserAuthRespDubboResponse));
        return new ArrayList<>();

    }

}
