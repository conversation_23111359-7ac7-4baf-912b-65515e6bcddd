package net.summerfarm.crm.facade;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.provider.BizLogQueryProvider;
import net.summerfarm.common.client.req.bizlog.QueryBizLogReq;
import net.summerfarm.common.client.resp.bizlog.BizLogListResp;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.input.PageSortInput;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;



import java.util.ArrayList;
import java.util.List;

import static net.summerfarm.crm.common.constant.CrmGlobalConstant.*;
import static net.summerfarm.crm.common.constant.SystemConstant.XM_TENANT_ID;

@Component
@Slf4j
public class CrmOperationLogQueryFacade {
    private static final String ORDER_BY = "bizCreateTimeSort";
    private static final String SORT = "desc";
    @DubboReference
    private BizLogQueryProvider bizLogQueryProvider;
    /**
     * 查询优惠券消费池基础日志信息。
     *
     * @param poolId 优惠券池的ID，不能为空。
     * @param pageIndex 查询的当前页码。
     * @param pageSize 每页显示的记录数。
     * @return PageInfo<String> 返回查询到的优惠券消费池日志信息，包括当前页码、每页记录数、总记录数和日志列表。
     */
    public PageInfo<String> queryCouponExpensePoolBaseLog(Long poolId, Integer pageIndex, Integer pageSize) {
        return queryCouponExpensePoolLog(CRM_COUPON_EXPENSE_POOL_PRE, poolId.toString(), pageIndex, pageSize);
    }
    /**
     * 查询管理员优惠券消费池日志
     *
     * @param recordId 记录ID，用于指定查询的具体记录，不可为null。
     * @param pageIndex 页码，指定查询的页数，用于分页查询。
     * @param pageSize 页面大小，指定每页返回的结果数量。
     * @return PageInfo<String> 返回一个PageInfo对象，包含查询到的页码信息和结果集。
     */
    public PageInfo<String> queryCouponExpenseAdminPoolLog(Long recordId, Integer pageIndex, Integer pageSize) {
        return queryCouponExpensePoolLog(CRM_COUPON_EXPENSE_ADMIN_POOL_PRE, recordId.toString(), pageIndex, pageSize);
    }

    private PageInfo<String> queryCouponExpensePoolLog(String bizKey, String id, Integer pageIndex, Integer pageSize) {
        QueryBizLogReq req = new QueryBizLogReq();
        req.setPageSize(pageSize);
        req.setTenantId(XM_TENANT_ID);
        req.setPageIndex(pageIndex);
        req.setBizKey(bizKey + id);
        req.setOperationName(CRM_COUPON_EXPENSE_POOL_OPERATION_NAME);
        req.setBizDomain(BIZ_DOMAIN);
        req.setEntityType(CRM_COUPON_EXPENSE_POOL_ENTITY_TYPE);
        List<PageSortInput> list = new ArrayList<>();
        PageSortInput pageSortInput = new PageSortInput();
        pageSortInput.setSortBy(SORT);
        pageSortInput.setOrderBy(ORDER_BY);
        list.add(pageSortInput);
        req.setSortList(list);
        DubboResponse<PageInfo<BizLogListResp>> pageInfoDubboResponse = bizLogQueryProvider.listBizLog(req);
        if (!pageInfoDubboResponse.isSuccess()) {
            throw new ProviderException(pageInfoDubboResponse.getMsg());
        }
        PageInfo<BizLogListResp> data = pageInfoDubboResponse.getData();
        PageInfo<String> stringPageInfo = PageInfoConverter.toPageResp(data, BizLogListResp::getLogContent);
        return stringPageInfo;
    }



}

