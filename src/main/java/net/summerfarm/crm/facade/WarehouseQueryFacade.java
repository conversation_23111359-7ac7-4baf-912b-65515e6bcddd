package net.summerfarm.crm.facade;/**
 * <AUTHOR>
 * @date 2023/1/6 11:45
 */

import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.client.wms.WarehouseProvider;
import net.summerfarm.manage.client.wms.dto.req.WarehouseQueryReq;
import net.summerfarm.manage.client.wms.dto.res.WarehouseBatchProveRecordDTO;
import net.summerfarm.manage.client.wms.dto.res.WarehouseStorageDTO;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/6 11:45
 */
@Component
public class WarehouseQueryFacade {
    @DubboReference
    WarehouseProvider warehouseProvider;

    /**
     * 库存列表查询
     *
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param param     查询条件
     * @return 仓库库存信息
     * @Author: yefeng
     **/
    public PageInfo<WarehouseStorageDTO> inventoryList(Integer pageIndex, Integer pageSize, WarehouseQueryReq param) {
        return warehouseProvider.inventoryList(pageIndex, pageSize, param).getData();
    }

    /**
     * 证明信息
     *
     * @param pageIndex 下标
     * @param pageSize  页码
     * @param param     查询条件
     * @return
     * @Author: yefeng
     **/
    public PageInfo<WarehouseBatchProveRecordDTO> proveList(Integer pageIndex, Integer pageSize, WarehouseQueryReq param) {
        return warehouseProvider.proveList(pageIndex, pageSize, param).getData();
    }

}
