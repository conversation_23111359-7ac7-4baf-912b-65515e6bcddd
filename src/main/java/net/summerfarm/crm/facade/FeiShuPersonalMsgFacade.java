package net.summerfarm.crm.facade;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.enums.ChannelTypeEnum;
import com.cosfo.message.client.provider.MessageSendProvider;
import com.cosfo.message.client.req.MessageBodyReq;
import com.cosfo.message.client.req.MessageUserReq;
import com.cosfo.message.client.resp.MsgSendLogResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 飞书私信
 *
 * <AUTHOR>
 * @date 2023/7/24  15:51
 */
@Component
@Slf4j
public class FeiShuPersonalMsgFacade {
    /**
     * 鲜沐tenantId
     */
    public static final Long XIANMU_TENANT_ID = 1L;
    @DubboReference
    private MessageSendProvider messageSendProvider;

    /**
     * 发送消息
     *
     * @param originEnum     账号体系来源
     * @param sendAdminId    发送人
     * @param receiveAdminId 接收人
     * @param mesg           飞书消息内容
     */
    public void sendFeiShuPersonalMsg(SystemOriginEnum originEnum, Long sendAdminId, List<Long> receiveAdminId, MessageBodyReq mesg) {
        MessageUserReq sender = null;
        if (sendAdminId != null) {
            sender = new MessageUserReq();
            sender.setBizUserId(sendAdminId);
            sender.setSystemOriginEnum(originEnum);
        }

        List<MessageUserReq> receiverList = new ArrayList<>();
        for (Long adminId : receiveAdminId) {
            MessageUserReq receiver = new MessageUserReq();
            receiver.setBizUserId(adminId);
            receiver.setSystemOriginEnum(originEnum);
            receiverList.add(receiver);
        }

        DubboResponse<List<MsgSendLogResp>> response = messageSendProvider.batchSendMessage(XIANMU_TENANT_ID, ChannelTypeEnum.FEISHU_SYSTEM, sender, receiverList, mesg);
        if (!response.isSuccess()) {
            log.error("飞书消息发送失败，e：{}", JSONObject.toJSONString(response), new RuntimeException("消息发送失败"));
        }
    }

    /**
     * 发送消息
     *
     * @param sendAdminId    发送人
     * @param receiveAdminId 接收人
     * @param mesg           飞书消息内容
     */
    public void sendFeiShuPersonalMsg(Long sendAdminId, List<Long> receiveAdminId, MessageBodyReq mesg) {
        sendFeiShuPersonalMsg(SystemOriginEnum.ADMIN, sendAdminId, receiveAdminId, mesg);
    }

    /**
     * 发送消息
     *
     * @param receiveAdminId 接收人
     * @param mesg           飞书消息内容
     */
    public void sendFeiShuPersonalMsg(List<Long> receiveAdminId, MessageBodyReq mesg) {
        sendFeiShuPersonalMsg(SystemOriginEnum.ADMIN, null, receiveAdminId, mesg);
    }
}
