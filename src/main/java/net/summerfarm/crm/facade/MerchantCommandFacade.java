package net.summerfarm.crm.facade;

import net.summerfarm.client.provider.merchant.MerchantInfoCommandProvider;
import net.summerfarm.client.req.merchant.MerchantBusinessUpdateReq;
import net.summerfarm.crm.common.util.ExceptionUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 商户命令门面类
 *
 * <AUTHOR>
 */
@Component
public class MerchantCommandFacade {

    @DubboReference
    private MerchantInfoCommandProvider merchantInfoCommandProvider;

    /**
     * 更新客户行业属性
     *
     * @param mId 客户id
     * @param mainBusinessType 客户主业类型
     * @param sideBusinessTypeList 客户副业类型列表
     * @param merchantChainType 客户连锁范围
     * @return 更新结果
     */
    public void updateMerchantBusiness(Long mId, String mainBusinessType,
                                       List<String> sideBusinessTypeList,
                                       Integer merchantChainType) {
        // 参数校验
        if (mId == null || StringUtils.isEmpty(mainBusinessType) || CollectionUtils.isEmpty(sideBusinessTypeList)
                || merchantChainType == null) {
            throw new BizException("更新客户行业属性时参数不能为空");
        }

        // 构建请求对象
        MerchantBusinessUpdateReq req = new MerchantBusinessUpdateReq();
        req.setMId(mId);
        req.setMainBusinessType(mainBusinessType);
        req.setSideBusinessTypeList(sideBusinessTypeList);
        req.setMerchantChainType(merchantChainType);

        DubboResponse<Void> dubboResponse = merchantInfoCommandProvider.updateMerchantBusiness(req);
        ExceptionUtils.checkDubboResponse(dubboResponse);
    }
}
