package net.summerfarm.crm.facade;/**
 * <AUTHOR>
 * @date 2023/1/6 14:01
 */

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.MarketItemDetail4XmQueryReq;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.page.PageResp;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.goods.client.provider.CategoryProvider;
import net.summerfarm.goods.client.resp.CategoryResp;
import net.summerfarm.manage.client.wms.ProductsProvider;
import net.summerfarm.manage.client.wms.dto.res.ProductsDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static net.summerfarm.crm.common.constant.SystemConstant.XM_TENANT_ID;

/**
 * <AUTHOR>
 * @date 2023/1/6 14:01
 */
@Component
public class ProductsQueryFacade {
    @DubboReference
    ProductsProvider productsProvider;

    @DubboReference
    MarketProvider marketProvider;

    @DubboReference
    CategoryProvider categoryProvider;


    public List<ProductsDTO> search(String pdName) {
        return productsProvider.search(pdName).getData();
    }



    /**
     * 根据 pdName skus pdId查询商品信息 最多返回1000个 crm默认参数够用
     *
     * @param skus
     * @return
     */
    public List<MarketItemInfoResp> queryMarketListBySkus(List<String> skus) {
        if (CollectionUtil.isEmpty(skus)){
            throw new BizException("skus 参数不能为空");
        }
       return queryMarketList(null, skus ,new ArrayList<>());
    }

    /**
     * 根据 pdName skus pdId查询商品信息 最多返回1000个 crm默认参数够用
     *
     * @param pdName
     * @param skus
     * @param pdIds
     * @return
     */
    public List<MarketItemInfoResp> queryMarketList(String pdName, List<String> skus, List<Long> pdIds) {
        if (CollectionUtil.isEmpty(skus) && CollectionUtil.isEmpty(pdIds) && StringUtils.isEmpty(pdName)){
            throw new BizException("参数不能为空");
        }
        MarketItemDetail4XmQueryReq req = new MarketItemDetail4XmQueryReq();
        req.setMarketTitle(pdName);
        req.setMarketTitleLikeFlag(false);
        req.setItemCodeList(skus);
        req.setMarketOutIds(pdIds);
        req.setPageNum(1);
        req.setPageSize(1000);
        req.setAdminShow(1);
        return queryMarketListPage(req).getList();
    }

    /**
     * 调用货品中心的接口去查询货品信息
     *
     * @param req
     * @return
     */
    private PageResp<MarketItemInfoResp> queryMarketListPage(MarketItemDetail4XmQueryReq req) {
        req.setTenantId(XM_TENANT_ID);
        DubboResponse<PageResp<MarketItemInfoResp>> pageRespDubboResponse = marketProvider.listItemDetailByItemCodes(req);
        if (pageRespDubboResponse.isSuccess()) {
            return pageRespDubboResponse.getData();
        }
        throw new BizException(pageRespDubboResponse.getMsg());
    }

    /**
     * 根据商品类别查询商品类别响应列表
     * @param categoryIds 商品类别ID列表
     * @return 商品类别响应列表
     * @throws BizException 如果查询失败，抛出BizException异常
     */
    public List<CategoryResp> queryByCategoryIds(List<Long> categoryIds){
        if(CollectionUtils.isEmpty(categoryIds)){
            return new ArrayList<>();
        }
        DubboResponse<List<CategoryResp>> listDubboResponse = categoryProvider.selectCategoryDetail(categoryIds);
        if (listDubboResponse.isSuccess()) {
            return listDubboResponse.getData();
        }
        throw new BizException(listDubboResponse.getMsg());
    }

}
