package net.summerfarm.crm.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.SkuProvider;
import net.summerfarm.mall.client.req.order.SkuSpuOrderQuery;
import net.summerfarm.mall.client.resp.MerchantSkuSpuOrderResp;
import net.summerfarm.manage.client.coupon.CouponProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
@Slf4j
public class MallSkuQueryFacade {
    @DubboReference
    private SkuProvider skuProvider;

    /**
     *
     * @param mid 门店id
     * @param skus sku的集合
     * @param allSpuSku 是否以spu的维度查询
     * @param querySkyPrice
     * @return
     */
    public  MerchantSkuSpuOrderResp querySkuPriceTotalCount(Long mid, List<String> skus , Boolean allSpuSku,
             Boolean querySkyPrice){
        if (mid == null){
            throw new BizException("mid 不能为空");
        }
        if (CollectionUtils.isEmpty(skus)){
            throw new BizException("skus 不能为空");
        }
        SkuSpuOrderQuery skuSpuOrderQuery = new SkuSpuOrderQuery();
        skuSpuOrderQuery.setMId(mid);
        skuSpuOrderQuery.setSkus(skus);
        skuSpuOrderQuery.setQuerySkyPrice(querySkyPrice);
        skuSpuOrderQuery.setAllSpuSku(allSpuSku);
        DubboResponse<MerchantSkuSpuOrderResp> merchantSkuSpuOrderRespDubboResponse = skuProvider.querMerchantOrderSku(skuSpuOrderQuery);
        if (merchantSkuSpuOrderRespDubboResponse.isSuccess()){
            return merchantSkuSpuOrderRespDubboResponse.getData();

        }
        throw new BizException(merchantSkuSpuOrderRespDubboResponse.getMsg());
    }
}
