package net.summerfarm.crm.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.SellingEntityConfig;
import net.xianmu.bms.client.provider.sellingEntity.SellingEntityQueryProvider;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/3/14 18:43
 * @Version 1.0
 */
@Slf4j
@Component
public class SellingEntityQueryFacade {

    @DubboReference
    private SellingEntityQueryProvider sellingEntityQueryProvider;

    @Autowired
    private SellingEntityConfig sellingEntityConfig;

    /**
     * 根据mId获取销售实体
     * @param mId mId
     * @return 销售实体名称
     */
    public String querySellingEntityByMId(Long mId) {
        try {
            return sellingEntityQueryProvider.querySellingEntityByMId(mId).getData().getSellingEntityName();
        } catch (Exception e) {
            log.error("【获取销售实体失败！】mId:{}, >>> {}", mId, e.getMessage(), e);
            return sellingEntityConfig.getDefaultSellingEntityName();
        }
    }
}
