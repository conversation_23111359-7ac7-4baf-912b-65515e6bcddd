package net.summerfarm.crm.facade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.merchant.MerchantInfoQueryProvider;
import net.summerfarm.client.resp.merchant.MerchantQueryResp;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.constant.SystemConstant;
import net.summerfarm.crm.common.util.ExceptionUtils;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantExtendQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreChangeLogQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreChangeLogQueryReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreChangeLogResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 买家中心门店信息查询接口
 *
 * <AUTHOR>
 * @date 2023/11/13 18:02
 */
@Slf4j
@Component
public class MerchantQueryFacade {
    @DubboReference
    private MerchantExtendQueryProvider merchantExtendQueryProvider;
    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;
    @DubboReference
    private MerchantStoreChangeLogQueryProvider changeLogQueryProvider;
    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;
    @DubboReference
    private MerchantInfoQueryProvider merchantInfoQueryProvider;


    /**
     * -----------------------------------     查询门店基本信息信息--------------------------------
     */

    /**
     * 通过门店名称获取门店
     *
     * @param storeName 商店名字
     * @return {@link MerchantStoreAndExtendResp}
     */
    public MerchantStoreResultResp getMerchantByStoreName(String storeName) {
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setExactStoreName(storeName);
        List<MerchantStoreResultResp> merchants = getMerchant(req);
        if (CollectionUtil.isEmpty(merchants)) {
            return null;
        }
        return merchants.get(0);
    }

    /**
     * 通过门店id获取门店
     *
     * @param mId 商店id
     * @return {@link MerchantStoreAndExtendResp}
     */
    public MerchantStoreResultResp getMerchantByMid(Long mId) {
        if (mId == null) {
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMId(mId);
        List<MerchantStoreResultResp> merchants = getMerchant(req);
        if (CollectionUtil.isEmpty(merchants)) {
            return null;
        }
        return merchants.get(0);
    }

    /**
     * 通过门店名称和 mid获取门店
     *
     * @param storeName 商店名字
     * @return {@link MerchantStoreAndExtendResp}
     */
    public List<MerchantStoreResultResp> getMerchantByStoreNameAndId(List<Long> mIdList, String storeName) {
        if (CollectionUtil.isEmpty(mIdList) && StringUtils.isBlank(storeName)) {
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setStoreName(storeName);
        req.setMIds(mIdList);
        List<MerchantStoreResultResp> merchants = getMerchant(req);
        if (CollectionUtil.isEmpty(merchants)) {
            return null;
        }
        return merchants;
    }

    /**
     * 通过门店名称获取门店
     *
     * @param storeName 商店名字
     * @return {@link MerchantStoreAndExtendResp}
     */
    public List<MerchantStoreResultResp> getMerchantByStoreName(List<String> storeName) {
        if (CollectionUtil.isEmpty(storeName)) {
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setExactStoreNameList(storeName);
        List<MerchantStoreResultResp> merchants = getMerchant(req);
        if (CollectionUtil.isEmpty(merchants)) {
            return null;
        }
        return merchants;
    }

    public List<MerchantStoreResultResp> getMerchantByMid(List<Long> mIdList) {
        if (CollectionUtil.isEmpty(mIdList)) {
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMIds(mIdList);
        List<MerchantStoreResultResp> merchants = getMerchant(req);
        if (CollectionUtil.isEmpty(merchants)) {
            return null;
        }
        return merchants;
    }


    /**
     * -----------------------------------     查询门店拓展信息--------------------------------
     */

    /**
     * mId 查询门店信息
     *
     * @param mId m id
     */
    public MerchantStoreAndExtendResp getMerchantExtendsByMid(Long mId) {
        List<MerchantStoreAndExtendResp> merchantExtends = this.getMerchantExtendsByMid(Collections.singletonList(mId));
        return CollUtil.isEmpty(merchantExtends) ? null : merchantExtends.get(0);
    }

    /**
     * 门店名称模糊查询正常门店信息
     *
     * @param mIds      门店id列表
     * @param storeName 门店名称
     */
    public List<MerchantStoreAndExtendResp> fuzzyGetMerchantExtendsByStoreName(List<Long> mIds, String storeName, List<Integer> areaNos) {
        if (StringUtils.isBlank(storeName) && CollectionUtils.isEmpty(mIds)) {
            return new ArrayList<>();
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setStoreName(storeName);
        req.setMIds(mIds);
        req.setAreaNos(areaNos);
        req.setStatus(MerchantStoreEnums.Status.AUDIT_SUCCESS.getCode());
        List<MerchantStoreAndExtendResp> merchantExtends = getMerchantExtends(req);
        if (CollectionUtil.isEmpty(merchantExtends)) {
            return new ArrayList<>();
        }
        return merchantExtends;
    }

    /**
     * 查询门店信息
     *
     * @param mId m id
     * @param areaNos areaNos
     */
    public MerchantStoreAndExtendResp getAuthMerchantExtends(Long mId,List<Integer> areaNos) {
        if (mId == null && CollectionUtil.isEmpty(areaNos)){
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMId(mId);
        req.setAreaNos(areaNos);
        List<MerchantStoreAndExtendResp> merchantExtends = getMerchantExtends(req);
        if (CollectionUtil.isEmpty(merchantExtends)) {
            return null;
        }
        return merchantExtends.get(0);
    }

    /**
     * 查询门店信息
     *
     * @param mId m id
     * @param areaNos areaNos
     */
    public List<MerchantStoreAndExtendResp> getAuthMerchantExtends(List<Long> mId,List<Integer> areaNos) {
        if (mId == null && CollectionUtil.isEmpty(areaNos)){
            return null;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMIds(mId);
        req.setAreaNos(areaNos);
        List<MerchantStoreAndExtendResp> merchantExtends = getMerchantExtends(req);
        if (CollectionUtil.isEmpty(merchantExtends)) {
            return null;
        }
        return merchantExtends;
    }

    /**
     *  批量查询门店信息
     *
     * @param mId m id
     */
    public List<MerchantStoreAndExtendResp> getMerchantExtendsByMid(List<Long> mId) {
        if (CollectionUtil.isEmpty(mId)){
            return new ArrayList<>();
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMIds(mId);
        List<MerchantStoreAndExtendResp> merchantExtends = getMerchantExtends(req);
        if (CollectionUtil.isEmpty(merchantExtends)) {
            return new ArrayList<>();
        }
        return merchantExtends;
    }









    public List<MerchantStoreResultResp> getMerchant(MerchantStoreQueryReq req) {
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        DubboResponse<List<MerchantStoreResultResp>> merchantExtendResp = merchantStoreQueryProvider.getMerchantStoresByPrimaryKeys(req);
        if (!merchantExtendResp.isSuccess()) {
            return new ArrayList<>();
        }
        return merchantExtendResp.getData();
    }




    /**
     * 查询门店信息
     *
     * @param req 要求事情
     * @return {@link List}<{@link MerchantStoreAndExtendResp}>
     */
    public List<MerchantStoreAndExtendResp> getMerchantExtends(MerchantStoreQueryReq req) {
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        DubboResponse<List<MerchantStoreAndExtendResp>> merchantExtendResp = merchantExtendQueryProvider.getMerchantStoreAndExtendsByPrimaryKeys(req);
        if (!merchantExtendResp.isSuccess()) {
            return new ArrayList<>();
        }
        return merchantExtendResp.getData();
    }

    /**
     * 查询门店 ID
     *
     * @param req 要求事情
     * @return {@link List}<{@link Long}>
     */
    public List<Long> getMerchantIds(MerchantStoreQueryReq req) {
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        DubboResponse<List<Long>> merchantExtendResp = merchantExtendQueryProvider.getMidList(req);
        if (!merchantExtendResp.isSuccess()) {
            return new ArrayList<>();
        }
        return merchantExtendResp.getData();
    }

    /**
     * 根据store_id查询门店的客户来源（是哪个门店推荐的）
     *
     * @param storeId
     * @return
     */
    public MerchantStoreResultResp getMerchantSource(Long storeId) {

        // 先查询门店的邀请记录
        MerchantStoreChangeLogQueryReq req = new MerchantStoreChangeLogQueryReq();
        req.setStoreId(storeId);
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        req.setOpType(0);
        DubboResponse<List<MerchantStoreChangeLogResultResp>> changeLogs = changeLogQueryProvider.getMerchantStoreChangeLogs(req);
        if (!changeLogs.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreChangeLogResultResp> data = changeLogs.getData();
        if(CollUtil.isEmpty(data)) {
            return null;
        }
        String channelCode = data.get(0).getMerchantChannelCode();
        if(StringUtils.isBlank(channelCode)) {
            return null;
        }

        // 根据channelCode获取指定的merchant
        MerchantStoreQueryReq queryReq = new MerchantStoreQueryReq();
        queryReq.setTenantId(SystemConstant.XM_TENANT_ID);
        queryReq.setChannelCode(channelCode);
        DubboResponse<List<MerchantStoreResultResp>> stores = merchantStoreQueryProvider.getMerchantStores(queryReq);
        if (!stores.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreResultResp> storesData = stores.getData();
        return CollUtil.isEmpty(storesData) ? null : storesData.get(0);
    }

    /**
     * 查询门店操作日志
     *
     * @param storeId
     * @return
     */
    public MerchantStoreChangeLogResultResp getMerchantChangeLog(Long storeId) {

        // 先查询门店的邀请记录
        MerchantStoreChangeLogQueryReq req = new MerchantStoreChangeLogQueryReq();
        req.setStoreId(storeId);
        req.setTenantId(SystemConstant.XM_TENANT_ID);
        req.setOpType(0);
        DubboResponse<List<MerchantStoreChangeLogResultResp>> changeLogs = changeLogQueryProvider.getMerchantStoreChangeLogs(req);
        if (!changeLogs.isSuccess()) {
            throw new BizException(changeLogs.getMsg());
        }
        List<MerchantStoreChangeLogResultResp> data = changeLogs.getData();
        if(CollUtil.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }

    /**
     * 查询门店信息（包含主业、副业和连锁范围）
     *
     * @param mIds
     * @return
     */
    public List<MerchantQueryResp> queryMerchantInfoList(List<Long> mIds) {
        if (CollectionUtils.isEmpty(mIds)) {
            return Collections.emptyList();
        }
        DubboResponse<List<MerchantQueryResp>> dubboResponse = merchantInfoQueryProvider.queryMerchantInfoList(mIds);
        ExceptionUtils.checkDubboResponse(dubboResponse);
        return dubboResponse.getData();
    }

}
