package net.summerfarm.crm.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.client.provider.OrderProvider;
import net.summerfarm.mall.client.req.order.AutoCreateSampleReq;
import net.summerfarm.mall.client.resp.AutoCreateSampleResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/8/30 15:39
 * @PackageName:net.summerfarm.crm.facade
 * @ClassName: OrderQueryFacade
 * @Description: TODO
 * @Version 1.0
 */
@Component
@Slf4j
public class OrderQueryFacade {

    @DubboReference
    private OrderProvider orderProvider;

    /**
    * @description 定时创建样品申请--过滤配送计划等条件
    * @params [mIdList]
    * @return java.util.List<net.summerfarm.mall.client.resp.AutoCreateSampleResp>
    * <AUTHOR>
    * @date  2024/8/30 15:41
    */
    public List<AutoCreateSampleResp> queryOrderInfoByAutoCreateSampleTask(Set<Long> mIdList) {
        if (CollectionUtils.isEmpty(mIdList)) {
            log.info("OrderQueryFacade[]queryOrderInfoByAutoCreateSampleTask[]mIdList is empty!");
            return Collections.emptyList();
        }
        AutoCreateSampleReq autoCreateSampleReq = new AutoCreateSampleReq();
        autoCreateSampleReq.setMIdList(new ArrayList<>(mIdList));
        DubboResponse<List<AutoCreateSampleResp>> dubboResponse = orderProvider.queryOrderInfoByAutoCreateSampleTask(autoCreateSampleReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

}
