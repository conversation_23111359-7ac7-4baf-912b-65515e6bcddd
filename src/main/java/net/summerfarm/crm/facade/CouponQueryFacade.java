package net.summerfarm.crm.facade;

import net.summerfarm.crm.facade.converter.coupon.CouponConverter;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.manage.client.coupon.CouponProvider;
import net.summerfarm.manage.client.coupon.dto.req.CouponReq;
import net.summerfarm.manage.client.coupon.dto.req.MerchantSituationReq;
import net.summerfarm.manage.client.coupon.dto.resp.CouponResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class CouponQueryFacade {

    @DubboReference
    private CouponProvider couponProvider;

    /**
     * 查询卡券信息
     *
     * @return
     */
    public CouponResp getCouponInfo(Integer id){
        CouponReq couponReq = new CouponReq();
        couponReq.setId(id);
        DubboResponse<CouponResp> dubboResponse = couponProvider.getCouponInfo(couponReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())
                || null == dubboResponse.getData()){
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    /**
     * 校验客情券合法性
     *
     * @return
     */
    public DubboResponse<CouponResp> checkMonthLivingCouponLegitimacy(MerchantSituationDTO merchantSituationDTO){
        MerchantSituationReq merchantSituationReq =  CouponConverter.MerchantSituationDTOtoMerchantSituationReq(merchantSituationDTO);
        DubboResponse<CouponResp> dubboResponse = couponProvider.checkMonthLivingCouponLegitimacy(merchantSituationReq);
        return dubboResponse;
    }

}
