package net.summerfarm.crm.facade;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.ContactMapper;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.provider.fence.PopFenceQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.FenceCloseTimeQueryReq;
import net.summerfarm.wnc.client.req.QueryAreaLegitimacyReq;
import net.summerfarm.wnc.client.req.fence.PopAddressAreaQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.FenceCloseTimeResp;
import net.summerfarm.wnc.client.resp.QueryAreaLegitimacyResp;
import net.summerfarm.wnc.client.resp.fence.PopAreaResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.xml.bind.util.JAXBSource;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WncQueryFacade {

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    @DubboReference
    private PopFenceQueryProvider popFenceQueryProvider;


    @Resource
    private ContactMapper contactMapper;

    private static final Long XM_TENANT_ID = 1L;


    /**
     * pop查询运营区域,根据地址
     *
     * @return
     */
    public Integer selectAreaNoByAddressForPOP(String city, String area){
        PopAddressAreaQueryReq areaQueryReq = new PopAddressAreaQueryReq();
        areaQueryReq.setCity(city);
        areaQueryReq.setArea(area);
        DubboResponse<PopAreaResp> dubboResponse = popFenceQueryProvider.queryPopAreaByAddress(areaQueryReq);
        if (null == dubboResponse || !dubboResponse.isSuccess()
                || null == dubboResponse.getData()){
            log.warn("pop查询wnc获取区域信息失败：city:{}, area:{}, dubboResponse:{}", city, area, JSON.toJSONString(dubboResponse));
            throw new BizException("地址不在配送范围");
        }
        return dubboResponse.getData().getAreaNo();
    }


    /**
     * 查询运营区域,根据地址
     *
     * @return
     */
    public AreaQueryResp selectAreaNoByAddress(String province, String city, String area){
        AreaQueryReq areaQueryReq = new AreaQueryReq();
        areaQueryReq.setCity(city);
        areaQueryReq.setArea(area);
        DubboResponse<AreaQueryResp> dubboResponse = deliveryFenceQueryProvider.queryAreaByAddress(areaQueryReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())
                || null == dubboResponse.getData()){
            throw new BizException("查询不到区域信息");
        }
        return dubboResponse.getData();
    }

    /**
     * 校验所选城市是否在围栏中
     *
     * @return
     */
    public List<QueryAreaLegitimacyResp> checkAreaLegitimacy(List<String> cityList){
        if (CollectionUtils.isEmpty(cityList)){
            return new ArrayList<>();
        }
        QueryAreaLegitimacyReq checkAreaLegitimacyReq = new QueryAreaLegitimacyReq();
        checkAreaLegitimacyReq.setCityList(cityList);
        DubboResponse<List<QueryAreaLegitimacyResp>> dubboResponse = deliveryFenceQueryProvider.queryAreaLegitimacyResp(checkAreaLegitimacyReq);
        if (!DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())){
            throw new ProviderException(dubboResponse.getData() == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }

    public LocalTime queryCloseTime(Long contactId, SourceEnum source){
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        if (contact == null) {
            log.info("WncQueryFacade[]queryCloseTime[]contact is null! contactId:{}", contactId);
            return null;
        }
        FenceCloseTimeQueryReq fenceCloseTimeQueryReq = new FenceCloseTimeQueryReq();
        fenceCloseTimeQueryReq.setCity(contact.getCity());
        fenceCloseTimeQueryReq.setArea(contact.getArea());
        fenceCloseTimeQueryReq.setSource(source);
        fenceCloseTimeQueryReq.setContactId(contactId);
        fenceCloseTimeQueryReq.setTenantId(XM_TENANT_ID);
        DubboResponse<FenceCloseTimeResp> dubboResponse = deliveryFenceQueryProvider.queryCloseTime(fenceCloseTimeQueryReq);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())
                || null == dubboResponse.getData()){
            log.warn("WncQueryFacade[]queryCloseTime[]queryCloseTime error! contactId:{}, dubboResponse:{}", contactId, JSON.toJSONString(dubboResponse));
            throw new BizException("查询不到当前区域的截单时间");
        }
        return dubboResponse.getData().getCloseTime();
    }

}
