package net.summerfarm.crm.facade;

import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class OfcSampleApplyQueryFacade {

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    /**
     * 查询样品订单是否自提
     *
     * @return
     */
    public Boolean hasSampleApplySelfPicked(Integer sampleId){
        DubboResponse<Boolean> dubboResponse = fulfillmentOrderQueryProvider.hasSampleApplyOrderBeenSelfPicked(sampleId);
        if (null == dubboResponse || !DubboResponse.COMMON_SUCCESS_CODE.equals(dubboResponse.getCode())
                || null == dubboResponse.getData()){
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        return dubboResponse.getData();
    }
}
