package net.summerfarm.crm.facade;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.ExceptionUtils;
import net.summerfarm.crm.enums.SkuSubTypeEnum;
import net.summerfarm.crm.facade.converter.product.GoodsCenterConverter;
import net.summerfarm.crm.facade.dto.ProductSkuBaseDTO;
import net.summerfarm.crm.facade.dto.ProductSpuBaseDTO;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.provider.ProductsSpuQueryProvider;
import net.summerfarm.goods.client.req.ProductSkuBasicReq;
import net.summerfarm.goods.client.req.ProductSkuListReq;
import net.summerfarm.goods.client.req.ProductSpuPageQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.goods.client.resp.ProductSkuBasicResp;
import net.summerfarm.goods.client.resp.ProductSpuListResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 货品中心查询接口
 * <AUTHOR>
 * @Date 2025/02/28 10:48
 * @Version 1.0
 */
@Component
@Slf4j
public class GoodsCenterQueryFacade {

    @DubboReference(timeout = 6000)
    private ProductsSkuQueryProvider productsSkuQueryProvider;

    @DubboReference(timeout = 6000)
    private ProductsSpuQueryProvider productsSpuQueryProvider;

    /**
     * 获取sku的二级性质
     *
     * @param skuCodeList sku代码列表
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    public Map<String, Integer> getSkuSubTypeMap(List<String> skuCodeList) {
        if (CollectionUtils.isEmpty(skuCodeList)) {
            return new HashMap<>();
        }
        ProductSkuBasicReq productSkuBasicReq = new ProductSkuBasicReq();
        productSkuBasicReq.setSkuList(skuCodeList.stream().distinct().collect(Collectors.toList()));
        DubboResponse<List<ProductSkuBasicResp>> dubboResponse = productsSkuQueryProvider.selectSkuSubTypeList(productSkuBasicReq);
        if (null == dubboResponse || !dubboResponse.isSuccess() || null == dubboResponse.getData()){
            throw new ProviderException(dubboResponse == null ? ProviderErrorCode.DEFAULT_CODE : dubboResponse.getMsg());
        }
        dubboResponse.getData().forEach(productSkuBasicResp -> {
            if (null == productSkuBasicResp.getSubAgentType()){
                log.error("货品中心返回的sku二级性质为空, skuCode:{}", productSkuBasicResp.getSku());
                productSkuBasicResp.setSubAgentType(SkuSubTypeEnum.NULL.getValue());
            }
        });
        return dubboResponse.getData().stream().collect(Collectors.toMap(ProductSkuBasicResp::getSku, ProductSkuBasicResp::getSubAgentType, (k1, k2) -> k1));
    }

    public List<ProductSkuBaseDTO> queryProductSkuBaseInfo(Long tenantId, List<String> skus) {
        if (tenantId == null) {
            throw new BizException("租户id不能为空");
        }
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.emptyList();
        }
        ProductSkuListReq req = new ProductSkuListReq();
        req.setTenantId(tenantId);
        req.setSkuList(skus);
        DubboResponse<List<ProductSkuBaseResp>> dubboResponse = productsSkuQueryProvider.selectProductSkuBaseList(req);
        ExceptionUtils.checkDubboResponse(dubboResponse);
        return GoodsCenterConverter.convertToProductSkuBaseDTOList(dubboResponse.getData());
    }

    /**
     * 查询spu基础信息
     *
     * @param tenantId 租户id
     * @param spuList spu编码列表
     * @param useFlag 状态 0-停用 1-启用
     * @return
     */
    public List<ProductSpuBaseDTO> queryProductSpuBaseInfo(Long tenantId, List<String> spuList, Integer useFlag) {
        if (tenantId == null) {
            throw new BizException("租户id不能为空");
        }
        if (CollectionUtils.isEmpty(spuList)) {
            return Collections.emptyList();
        }
        spuList = spuList.stream().distinct().collect(Collectors.toList());
        if (spuList.size() > 200) {
            throw new BizException("单次查询spu数量不能超过200");
        }
        ProductSpuPageQueryReq req = new ProductSpuPageQueryReq();
        req.setTenantId(tenantId);
        req.setSpuList(spuList);
        req.setUseFlag(useFlag);
        req.setPageIndex(1);
        req.setPageSize(spuList.size());
        DubboResponse<PageInfo<ProductSpuListResp>> dubboResponse = productsSpuQueryProvider.selectSpuCommonPage(req);
        ExceptionUtils.checkDubboResponse(dubboResponse);
        if (dubboResponse.getData() == null || CollectionUtils.isEmpty(dubboResponse.getData().getList())) {
            return Collections.emptyList();
        }
        return GoodsCenterConverter.convertToProductSpuBaseDTOList(dubboResponse.getData().getList());
    }

}
