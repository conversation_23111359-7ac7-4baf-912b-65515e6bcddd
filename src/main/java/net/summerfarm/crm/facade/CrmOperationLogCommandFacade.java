package net.summerfarm.crm.facade;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.provider.BizLogCommandProvider;
import net.summerfarm.common.client.provider.BizLogQueryProvider;
import net.summerfarm.common.client.req.bizlog.QueryBizLogReq;
import net.summerfarm.common.client.req.bizlog.SaveBizLogRecordReq;
import net.summerfarm.common.client.resp.bizlog.BizLogListResp;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolLogoVo;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

import static net.summerfarm.crm.common.constant.CrmGlobalConstant.*;
import static net.summerfarm.crm.common.constant.SystemConstant.XM_TENANT_ID;

@Component
@Slf4j
public class CrmOperationLogCommandFacade {
    @DubboReference
    private BizLogCommandProvider bizLogCommandProvider;

    public void addCouponExpensePoolLogList(Long authId, String bizKey, String bizId, List<CrmCouponExpensePoolLogoVo> crmCouponExpensePoolLogoVos) {
        if (CollectionUtils.isEmpty(crmCouponExpensePoolLogoVos)) {
            return;
        }
        for (CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo : crmCouponExpensePoolLogoVos) {
            addCouponExpensePoolLog(authId, bizKey, bizId, crmCouponExpensePoolLogoVo);
        }
    }
    public void addCouponExpensePoolLog(Long authId, String bizKey, String bizId, CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo) {
        SaveBizLogRecordReq req = new SaveBizLogRecordReq();
        req.setSource(SystemOriginEnum.ADMIN.getType());
        req.setOperationName(CRM_COUPON_EXPENSE_POOL_OPERATION_NAME);
        req.setBizKey(bizKey + bizId);
        req.setContent(JSONUtil.toJsonStr(crmCouponExpensePoolLogoVo));
        req.setTenantId(XM_TENANT_ID);
        req.setCreatedId(authId);
        req.setCreatedTime(LocalDateTime.now());
        req.setBizKeyTenantId(XM_TENANT_ID);
        req.setIp("127.0.0.1");
        DubboResponse<Boolean> booleanDubboResponse = bizLogCommandProvider.saveBizLogRecord(req);
        if (!booleanDubboResponse.isSuccess()) {
            throw new ProviderException(booleanDubboResponse.getMsg());
        }
    }


}

