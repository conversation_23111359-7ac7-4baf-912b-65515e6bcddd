package net.summerfarm.crm.facade;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.ExceptionUtils;
import net.summerfarm.crm.facade.dto.TravelSiteDTO;
import net.summerfarm.crm.facade.input.TravelSiteInput;
import net.summerfarm.tms.client.ortools.provider.TravelPlanningQueryProvider;
import net.summerfarm.tms.client.ortools.req.TravelSequencePlanningQueryReq;
import net.summerfarm.tms.client.ortools.resp.TravelSequencePlanningResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TmsQueryFacade {

    @DubboReference
    private TravelPlanningQueryProvider travelPlanningQueryProvider;

    /**
     * 根据起始点位和途经点位列表规划最优的点位顺序（总体路径最短）
     *
     * @param beginSiteInput
     * @param wayPointSiteInputs
     * @return
     */
    public List<TravelSiteDTO> queryTravelSequencePlanning(TravelSiteInput beginSiteInput, List<TravelSiteInput> wayPointSiteInputs) {
        if (beginSiteInput == null) {
            throw new BizException("起始点位不能为空");
        }
        if (CollectionUtils.isEmpty(wayPointSiteInputs)) {
            return Collections.emptyList();
        }
        TravelSequencePlanningQueryReq req = new TravelSequencePlanningQueryReq();
        TravelSequencePlanningQueryReq.Site beginSite = new TravelSequencePlanningQueryReq.Site();
        beginSite.setId(beginSiteInput.getId());
        beginSite.setPoi(beginSiteInput.getPoi());
        req.setBeginSite(beginSite);
        List<TravelSequencePlanningQueryReq.Site> wayPointSites = wayPointSiteInputs.stream().map(x -> {
            TravelSequencePlanningQueryReq.Site wayPointSite = new TravelSequencePlanningQueryReq.Site();
            wayPointSite.setId(x.getId());
            wayPointSite.setPoi(x.getPoi());
            return wayPointSite;
        }).collect(Collectors.toList());
        req.setWaypointSite(wayPointSites);
        DubboResponse<TravelSequencePlanningResp> dubboResponse = travelPlanningQueryProvider.queryTravelSequencePlanning(req);
        ExceptionUtils.checkDubboResponse(dubboResponse);
        if (dubboResponse.getData() == null || CollectionUtils.isEmpty(dubboResponse.getData().getTravelSequenceList())) {
            throw new BizException("规划最优途经点位顺序失败");
        }
        List<TravelSiteInput> totalSiteInputs = Lists.newArrayList(wayPointSiteInputs);
        totalSiteInputs.add(beginSiteInput);
        Map<Long, String> siteId2PoiMap = totalSiteInputs.stream().collect(Collectors.toMap(TravelSiteInput::getId, TravelSiteInput::getPoi, (v1, v2) -> v1));
        List<TravelSiteDTO> travelSiteDTOList = dubboResponse.getData().getTravelSequenceList().stream().map(x -> {
            TravelSiteDTO travelSiteDTO = new TravelSiteDTO();
            travelSiteDTO.setId(x.getId());
            travelSiteDTO.setSequence(x.getSequence());
            travelSiteDTO.setPoi(siteId2PoiMap.get(x.getId()));
            return travelSiteDTO;
        }).collect(Collectors.toList());
        return travelSiteDTOList;
    }

}
