package net.summerfarm.crm.facade.converter.product;

import net.summerfarm.crm.facade.dto.ProductSkuBaseDTO;
import net.summerfarm.crm.facade.dto.ProductSpuBaseDTO;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.goods.client.resp.ProductSpuListResp;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class GoodsCenterConverter {

    public static List<ProductSkuBaseDTO> convertToProductSkuBaseDTOList(List<ProductSkuBaseResp> respList) {
        if (CollectionUtils.isEmpty(respList)) {
            return Collections.emptyList();
        }
        return respList.stream().map(GoodsCenterConverter::convertToProductSkuBaseDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ProductSkuBaseDTO convertToProductSkuBaseDTO(ProductSkuBaseResp resp) {
        if (resp == null) {
            return null;
        }
        ProductSkuBaseDTO productSkuBaseDTO = new ProductSkuBaseDTO();
        productSkuBaseDTO.setSku(resp.getSku());
        productSkuBaseDTO.setTitle(resp.getTitle());
        productSkuBaseDTO.setSpecification(resp.getSpecification());
        return productSkuBaseDTO;
    }

    public static List<ProductSpuBaseDTO> convertToProductSpuBaseDTOList(List<ProductSpuListResp> respList) {
        if (CollectionUtils.isEmpty(respList)) {
            return Collections.emptyList();
        }
        return respList.stream().map(GoodsCenterConverter::convertToProductSpuBaseDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ProductSpuBaseDTO convertToProductSpuBaseDTO(ProductSpuListResp resp) {
        if (resp == null) {
            return null;
        }
        ProductSpuBaseDTO productSpuBaseDTO = new ProductSpuBaseDTO();
        productSpuBaseDTO.setSpu(resp.getSpu());
        productSpuBaseDTO.setTitle(resp.getTitle());

        return productSpuBaseDTO;
    }

}
