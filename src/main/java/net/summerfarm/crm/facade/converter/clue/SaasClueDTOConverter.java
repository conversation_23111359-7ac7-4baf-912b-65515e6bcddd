package net.summerfarm.crm.facade.converter.clue;

import net.summerfarm.crm.model.domain.CrmClue;
import net.summerfarm.crm.model.dto.CrmClueDTO;



public class SaasClueDTOConverter {



    private SaasClueDTOConverter() {
        // 无需实现
    }
/*
    public static List<ClueResp> toClueRespList(List<CrmClueDTO> cluClueDTOList) {
        if (cluClueDTOList == null) {
            return Collections.emptyList();
        }
        List<ClueResp> clueRespList = new ArrayList<>();
        for (CrmClueDTO cluClueDTO : cluClueDTOList) {
            clueRespList.add(toClueResp(cluClueDTO));
        }
        return clueRespList;
    }

    public static ClueResp toClueResp(CrmClueDTO cluClueDTO) {
        if (cluClueDTO == null) {
            return null;
        }
        ClueResp clueResp = new ClueResp();
        clueResp.setId(cluClueDTO.getId());
        clueResp.setCustomerSource(cluClueDTO.getCustomerSource());
        clueResp.setCustomerName(cluClueDTO.getCustomerName());
        clueResp.setKp(cluClueDTO.getKp());
        clueResp.setPhone(cluClueDTO.getPhone());
        clueResp.setDept(cluClueDTO.getDept());
        clueResp.setPost(cluClueDTO.getPost());
        clueResp.setClueStatus(cluClueDTO.getClueStatus());
        clueResp.setClueSource(cluClueDTO.getClueSource());
        clueResp.setWantBusiness(cluClueDTO.getWantBusiness());
        clueResp.setAreaCode(cluClueDTO.getAreaCode());
        clueResp.setPurposeRemark(cluClueDTO.getPurposeRemark());
        clueResp.setBusinessType(cluClueDTO.getBusinessType());
        clueResp.setShopType(cluClueDTO.getShopType());
        clueResp.setShopSize(cluClueDTO.getShopSize());
        clueResp.setCustomerType(cluClueDTO.getCustomerType());
        clueResp.setShopSystem(cluClueDTO.getShopSystem());
        clueResp.setHeadSystem(cluClueDTO.getHeadSystem());
        clueResp.setSendRemark(cluClueDTO.getSendRemark());
        clueResp.setRemark(cluClueDTO.getRemark());
        clueResp.setBdId(cluClueDTO.getBdId());
        clueResp.setBId(cluClueDTO.getBId());
        clueResp.setCreateTime(cluClueDTO.getCreateTime());
        clueResp.setUpdateTime(cluClueDTO.getUpdateTime());
        clueResp.setCreateBdId(cluClueDTO.getCreateBdId());
        clueResp.setAssistBdId(cluClueDTO.getAssistBdId());
        clueResp.setLastFollowTime(cluClueDTO.getLastFollowTime());
        clueResp.setAreaCodeName(cluClueDTO.getAreaCodeName());

// Not mapped FROM fields:
// areaCodeName
// bdName
// createBdName
// assistBdName
        return clueResp;
    }*/



    public static CrmClueDTO toCluClueDTO(CrmClue clue) {
        if (clue == null) {
            return null;
        }
        CrmClueDTO cluClueDTO = new CrmClueDTO();
        cluClueDTO.setId(clue.getId());
        cluClueDTO.setCustomerSource(clue.getCustomerSource());
        cluClueDTO.setCustomerName(clue.getCustomerName());
        cluClueDTO.setKp(clue.getKp());
        cluClueDTO.setPhone(clue.getPhone());
        cluClueDTO.setDept(clue.getDept());
        cluClueDTO.setPost(clue.getPost());
        cluClueDTO.setClueStatus(clue.getClueStatus());
        cluClueDTO.setClueSource(clue.getClueSource());
        cluClueDTO.setWantBusiness(clue.getWantBusiness());
        cluClueDTO.setAreaCode(clue.getAreaCode());
        cluClueDTO.setPurposeRemark(clue.getPurposeRemark());
        cluClueDTO.setBusinessType(clue.getBusinessType());
        cluClueDTO.setShopType(clue.getShopType());
        cluClueDTO.setShopSize(clue.getShopSize());
        cluClueDTO.setCustomerType(clue.getCustomerType());
        cluClueDTO.setShopSystem(clue.getShopSystem());
        cluClueDTO.setHeadSystem(clue.getHeadSystem());
        cluClueDTO.setSendRemark(clue.getSendRemark());
        cluClueDTO.setRemark(clue.getRemark());
        cluClueDTO.setBdId(clue.getBdId());
        cluClueDTO.setBId(clue.getBId());
        cluClueDTO.setCreateTime(clue.getCreateTime());
        cluClueDTO.setUpdateTime(clue.getUpdateTime());
        cluClueDTO.setCreateBdId(clue.getCreateBdId());
        cluClueDTO.setAssistBdId(clue.getAssistBdId());
        cluClueDTO.setLastFollowTime(clue.getLastFollowTime());
        cluClueDTO.setAreaCodeName(clue.getAreaCodeName());
// Not mapped TO fields:
// bdName
// createBdName
// assistBdName
        return cluClueDTO;
    }
}
