package net.summerfarm.crm.facade.converter.admin;

import net.summerfarm.client.resp.admin.AdminResp;
import net.summerfarm.crm.facade.dto.AdminDTO;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class AdminConvert {

    public static List<AdminDTO> convertToAdminDTOList(List<AdminResp> adminRespList) {
        if (CollectionUtils.isEmpty(adminRespList)) {
            return Collections.emptyList();
        }
        return adminRespList.stream().map(AdminConvert::convertToAdminDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static AdminDTO convertToAdminDTO(AdminResp adminResp) {
        if (adminResp == null) {
            return null;
        }
        AdminDTO adminDTO = new AdminDTO();
        adminDTO.setAdminId(adminResp.getAdminId());
        adminDTO.setRealName(adminResp.getRealName());
        adminDTO.setPhone(adminResp.getPhone());
        adminDTO.setAdminChain(adminResp.getAdminChain());
        return adminDTO;
    }
}
