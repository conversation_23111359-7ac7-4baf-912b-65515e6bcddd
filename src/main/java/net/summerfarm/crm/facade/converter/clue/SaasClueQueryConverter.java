package net.summerfarm.crm.facade.converter.clue;

import net.summerfarm.crm.model.query.SaasClueQuery;



public class SaasClueQueryConverter {
    private SaasClueQueryConverter() {
        // 无需实现
    }

/*

    public static ClueQueryReq toClueQueryReq(SaasClueQuery saasClueQuery) {
        ClueQueryReq clueQueryReq  = new ClueQueryReq();
        clueQueryReq.setPageIndex(saasClueQuery.getPageIndex());
        clueQueryReq.setPageSize(saasClueQuery.getPageSize());
        clueQueryReq.setBdId(saasClueQuery.getBdId());
        clueQueryReq.setAreaCodes(saasClueQuery.getAreaCodes());
        clueQueryReq.setBusinessType(saasClueQuery.getBusinessType());
        clueQueryReq.setClueStatus(saasClueQuery.getClueStatus());
        clueQueryReq.setCustomerName(saasClueQuery.getCustomerName());
        clueQueryReq.setCustomerSource(saasClueQuery.getCustomerSource());
        clueQueryReq.setWantBusiness(saasClueQuery.getWantBusiness());
        clueQueryReq.setCustomerType(saasClueQuery.getCustomerType());

        return clueQueryReq;
    }
*/

}
