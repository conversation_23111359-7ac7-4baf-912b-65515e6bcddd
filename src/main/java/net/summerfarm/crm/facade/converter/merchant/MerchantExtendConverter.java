package net.summerfarm.crm.facade.converter.merchant;

import cn.hutool.core.util.ObjectUtil;
import net.summerfarm.crm.enums.TransStatusFromUserCenter;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.tms.util.DateUtils;
import net.xianmu.usercenter.client.merchant.enums.MerchantStoreEnums;

import java.sql.Date;
import java.time.ZoneId;
import java.util.Objects;

import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

/**
 * <AUTHOR>
 * @date 2023/11/16 13:41
 */
public class MerchantExtendConverter {
    public static MerchantVO convertMerchantVo(MerchantStoreAndExtendDTO extendDTO){

        if (extendDTO == null) {
            return null;
        }
        MerchantVO merchantVO = new MerchantVO();
        merchantVO.setmId(extendDTO.getMId());
        merchantVO.setMname(extendDTO.getStoreName());
        merchantVO.setAreaNo(extendDTO.getAreaNo());
        merchantVO.setStoreId(extendDTO.getId());
        if (Objects.nonNull(extendDTO.getAdminId())) {
            merchantVO.setAdminId(extendDTO.getAdminId().intValue());
        }

        merchantVO.setProvince(extendDTO.getProvince());
        merchantVO.setCity(extendDTO.getCity());
        merchantVO.setArea(extendDTO.getArea());
        merchantVO.setPoiNote(extendDTO.getPoiNote());
        merchantVO.setRemark(extendDTO.getRemark());
        merchantVO.setAreaNo(extendDTO.getAreaNo());
        merchantVO.setMemberIntegral(extendDTO.getMemberIntegral());
        merchantVO.setSize(ObjectUtil.equal(extendDTO.getSize(), ADMIN.getCode()) ? ADMIN.getDesc() : MERCHANT.getDesc());
        merchantVO.setDirect(extendDTO.getDirect());
        merchantVO.setHouseNumber(extendDTO.getHouseNumber());
        merchantVO.setEnterpriseScale(MerchantStoreEnums.Type.getDesc(extendDTO.getType()));
        merchantVO.setLastOrderTime(extendDTO.getLastOrderTime());
        merchantVO.setRegisterTime(Date.from(extendDTO.getRegisterTime().atZone(ZoneId.systemDefault()).toInstant()));
        merchantVO.setIslock(TransStatusFromUserCenter.Status.transStatusFromUserCenter(extendDTO.getStatus().toString()));
        merchantVO.setGrade(extendDTO.getGrade());
        merchantVO.setInviterChannelCode(extendDTO.getInviterChannelCode());
        merchantVO.setChannelCode(extendDTO.getChannelCode());
        merchantVO.setChangePop(extendDTO.getChangePop());
        merchantVO.setDisplayButton(extendDTO.getDisplayButton());
        merchantVO.setOperateStatus(extendDTO.getOperateStatus());
        merchantVO.setDoorPic(extendDTO.getDoorPic());
        merchantVO.setOpenid(extendDTO.getOpenid());
        merchantVO.setRankId(extendDTO.getRankId());
        merchantVO.setLoginTime(extendDTO.getLoginTime());
        merchantVO.setPullBlackRemark(extendDTO.getPullBlackRemark());
        merchantVO.setPullBlackOperator(extendDTO.getPullBlackOperator());
        merchantVO.setInvitecode(extendDTO.getInviteCode());
        merchantVO.setAuditUser(extendDTO.getAuditUser());
        merchantVO.setServer(extendDTO.getServer());
        merchantVO.setShowPrice(extendDTO.getShowPrice());
        merchantVO.setTradeGroup(extendDTO.getTradeGroup());
        merchantVO.setCompanyBrand(extendDTO.getCompanyBrand());
        merchantVO.setTradeArea(extendDTO.getTradeArea());
        merchantVO.setAuditTime(null != extendDTO.getAuditTime() ? DateUtils.localDateTime2Date(extendDTO.getAuditTime()) : null);
        merchantVO.setCluePool(extendDTO.getCluePool());
        merchantVO.setMerchantType(extendDTO.getMerchantType());
        merchantVO.setExamineType(extendDTO.getExamineType());
        merchantVO.setType(extendDTO.getBusinessType());
        merchantVO.setBusinessLine(extendDTO.getBusinessLine());
        return merchantVO;
    }
}
