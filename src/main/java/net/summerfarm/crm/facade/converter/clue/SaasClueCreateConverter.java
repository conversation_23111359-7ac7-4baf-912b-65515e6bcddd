package net.summerfarm.crm.facade.converter.clue;

import net.summerfarm.crm.model.domain.CrmClue;
import net.summerfarm.crm.model.vo.SaasClueClueVO;
/*import net.xianmu.usercenter.client.clue.req.ClueAddOrUpdateReq;*/

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class SaasClueCreateConverter {
    private SaasClueCreateConverter() {
        // 无需实现
    }

    public static CrmClue toCrmClue(SaasClueClueVO saasClueClueVO) {
        if (saasClueClueVO == null) {
            return null;
        }
        CrmClue clue = new CrmClue();
        clue.setId(saasClueClueVO.getId());
        clue.setCustomerSource(saasClueClueVO.getCustomerSource());
        clue.setCustomerName(saasClueClueVO.getCustomerName());
        clue.setKp(saasClueClueVO.getKp());
        clue.setPhone(saasClueClueVO.getPhone());
        clue.setDept(saasClueClueVO.getDept());
        clue.setPost(saasClueClueVO.getPost());
        clue.setClueStatus(saasClueClueVO.getClueStatus());
        clue.setClueSource(saasClueClueVO.getClueSource());
        clue.setWantBusiness(saasClueClueVO.getWantBusiness());
        clue.setAreaCode(saasClueClueVO.getAreaCode());
        clue.setAreaCodeName(saasClueClueVO.getAreaCodeName());
        clue.setPurposeRemark(saasClueClueVO.getPurposeRemark());
        clue.setBusinessType(saasClueClueVO.getBusinessType());
        clue.setShopType(saasClueClueVO.getShopType());
        clue.setShopSize(saasClueClueVO.getShopSize());
        clue.setCustomerType(saasClueClueVO.getCustomerType());
        clue.setShopSystem(saasClueClueVO.getShopSystem());
        clue.setHeadSystem(saasClueClueVO.getHeadSystem());
        clue.setSendRemark(saasClueClueVO.getSendRemark());
        clue.setRemark(saasClueClueVO.getRemark());
        clue.setBId(saasClueClueVO.getBId());
        clue.setAssistBdId(saasClueClueVO.getAssistBdId());
// Not mapped TO fields:
// bdId
        return clue;
    }

    /*public static List<SaasClueClueVO> toSaasClueClueVOList(List<ClueAddOrUpdateReq> clueAddOrUpdateReqList) {
        if (clueAddOrUpdateReqList == null) {
            return Collections.emptyList();
        }
        List<SaasClueClueVO> saasClueClueVOList = new ArrayList<>();
        for (ClueAddOrUpdateReq clueAddOrUpdateReq : clueAddOrUpdateReqList) {
            saasClueClueVOList.add(toSaasClueClueVO(clueAddOrUpdateReq));
        }
        return saasClueClueVOList;
    }

    public static SaasClueClueVO toSaasClueClueVO(ClueAddOrUpdateReq clueAddOrUpdateReq) {
        if (clueAddOrUpdateReq == null) {
            return null;
        }
        SaasClueClueVO saasClueClueVO = new SaasClueClueVO();
        saasClueClueVO.setId(clueAddOrUpdateReq.getId());
        saasClueClueVO.setCustomerSource(clueAddOrUpdateReq.getCustomerSource());
        saasClueClueVO.setCustomerName(clueAddOrUpdateReq.getCustomerName());
        saasClueClueVO.setKp(clueAddOrUpdateReq.getKp());
        saasClueClueVO.setPhone(clueAddOrUpdateReq.getPhone());
        saasClueClueVO.setDept(clueAddOrUpdateReq.getDept());
        saasClueClueVO.setPost(clueAddOrUpdateReq.getPost());
        saasClueClueVO.setClueStatus(clueAddOrUpdateReq.getClueStatus());
        saasClueClueVO.setClueSource(clueAddOrUpdateReq.getClueSource());
        saasClueClueVO.setWantBusiness(clueAddOrUpdateReq.getWantBusiness());
        saasClueClueVO.setAreaCode(clueAddOrUpdateReq.getAreaCode());
        saasClueClueVO.setAreaCodeName(clueAddOrUpdateReq.getAreaCodeName());
        saasClueClueVO.setPurposeRemark(clueAddOrUpdateReq.getPurposeRemark());
        saasClueClueVO.setBusinessType(clueAddOrUpdateReq.getBusinessType());
        saasClueClueVO.setShopType(clueAddOrUpdateReq.getShopType());
        saasClueClueVO.setShopSize(clueAddOrUpdateReq.getShopSize());
        saasClueClueVO.setCustomerType(clueAddOrUpdateReq.getCustomerType());
        saasClueClueVO.setShopSystem(clueAddOrUpdateReq.getShopSystem());
        saasClueClueVO.setHeadSystem(clueAddOrUpdateReq.getHeadSystem());
        saasClueClueVO.setSendRemark(clueAddOrUpdateReq.getSendRemark());
        saasClueClueVO.setRemark(clueAddOrUpdateReq.getRemark());
        saasClueClueVO.setBId(clueAddOrUpdateReq.getBId());
        saasClueClueVO.setAssistBdId(clueAddOrUpdateReq.getAssistBdId());
// Not mapped FROM fields:
// bdId
        return saasClueClueVO;
    }
    //SaasClueClueVO ClueAddOrUpdateReq
*/


}
