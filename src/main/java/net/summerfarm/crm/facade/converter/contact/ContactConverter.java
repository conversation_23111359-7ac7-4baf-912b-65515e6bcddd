package net.summerfarm.crm.facade.converter.contact;

import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.dto.ContactDto;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ContactConverter {

    public static List<Contact> convertContactList(List<ContactDto> contactDto) {
        if (contactDto == null) {
            return null;
        }
        return contactDto.stream().map(ContactConverter::convertContact).collect(Collectors.toList());
    }



    public static Contact convertContact(ContactDto contactDto) {

        if (contactDto == null) {
            return null;
        }

        Contact contact = new Contact();
        contact.setmId(contactDto.getmId());
        contact.setContactId(contactDto.getContactId());
        contact.setContact(contactDto.getContact());
        contact.setPosition(contactDto.getPosition());
        contact.setGender(contactDto.getGender());
        contact.setPhone(contactDto.getPhone());
        contact.setEmail(contactDto.getEmail());
        contact.setProvince(contactDto.getProvince());
        contact.setCity(contactDto.getCity());
        contact.setArea(contactDto.getArea());
        contact.setAddress(contactDto.getAddress());
        contact.setStatus(contactDto.getStatus());
        contact.setRemark(contactDto.getRemark());
        contact.setDeliveryCar(contactDto.getDeliveryCar());
        contact.setIsDefault(contactDto.getIsDefault());
        contact.setPoiNote(contactDto.getPoiNote());
        contact.setDistance(contactDto.getDistance());
        contact.setPath(contactDto.getPath());
        contact.setHouseNumber(contactDto.getHouseNumber());
        contact.setStoreNo(contactDto.getStoreNo());
        contact.setPoi(contactDto.getPoi());
        contact.setDeliveryFrequent(contactDto.getDeliveryFrequent());
        contact.setAreaNo(contactDto.getAreaNo());
        contact.setAreaName(contactDto.getAreaName());
        contact.setModify(contactDto.getModify());
        contact.setUseNew(contactDto.getUseNew());
        contact.setNextDeliveryDate(contactDto.getNextDeliveryDate());
        contact.setAddressRemark(contactDto.getAddressRemark());
        contact.setContactAddressRemark(contactDto.getContactAddressRemark());
        return contact;

    }
}
