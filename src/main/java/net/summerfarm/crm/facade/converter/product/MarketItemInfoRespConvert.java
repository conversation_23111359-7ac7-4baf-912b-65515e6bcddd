package net.summerfarm.crm.facade.converter.product;

import com.cofso.item.client.resp.MarketItemInfoResp;
import net.summerfarm.crm.model.dto.CategoryProductDTO;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;

public class MarketItemInfoRespConvert {

    public static CrmSkuMonthGmvVO convert(MarketItemInfoResp marketItemInfoResp){
        if (marketItemInfoResp == null){
            return  null;
        }
        CrmSkuMonthGmvVO crmSkuMonthGmvVO = new CrmSkuMonthGmvVO();
        crmSkuMonthGmvVO.setSku(marketItemInfoResp.getItemCode());
        crmSkuMonthGmvVO.setPdId(marketItemInfoResp.getMarketOutId());
        crmSkuMonthGmvVO.setPdName(marketItemInfoResp.getMarketTitle());
        crmSkuMonthGmvVO.setWeight(marketItemInfoResp.getSpecification());
        crmSkuMonthGmvVO.setPicturePath(marketItemInfoResp.getMarketMainPicture());
        return crmSkuMonthGmvVO;
    }

    public static CategoryProductDTO convertCategoryProductDTO(MarketItemInfoResp marketItemInfoResp){
        if (marketItemInfoResp == null){
            return null;
        }
        CategoryProductDTO categoryProductDTO = new CategoryProductDTO();
        categoryProductDTO.setCategoryId(marketItemInfoResp.getCategoryId());
        categoryProductDTO.setPdId(marketItemInfoResp.getMarketOutId());
        categoryProductDTO.setPdName(marketItemInfoResp.getMarketTitle());
        categoryProductDTO.setWeight(marketItemInfoResp.getSpecification());
        return categoryProductDTO;
    }
}
