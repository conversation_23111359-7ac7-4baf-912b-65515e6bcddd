package net.summerfarm.crm.facade.converter.coupon;

import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import net.summerfarm.manage.client.coupon.dto.req.CouponBlackAndWhiteDTO;
import net.summerfarm.manage.client.coupon.dto.req.MerchantSituationReq;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CouponConverter {

    public static MerchantSituationReq MerchantSituationDTOtoMerchantSituationReq(MerchantSituationDTO merchantSituationDTO) {
        if (merchantSituationDTO == null) {
            return null;
        }
        MerchantSituationReq merchantSituationReq = new MerchantSituationReq();
        merchantSituationReq.setId(merchantSituationDTO.getId());
        merchantSituationReq.setStatus(merchantSituationDTO.getStatus());
        merchantSituationReq.setCouponAmount(merchantSituationDTO.getCouponAmount());
        merchantSituationReq.setThreshold(merchantSituationDTO.getThreshold());
        merchantSituationReq.setMerchantId(merchantSituationDTO.getMerchantId());
        merchantSituationReq.setMonthLivingCoupon(merchantSituationDTO.getMonthLivingCoupon());
        merchantSituationReq.setSituationRemake(merchantSituationDTO.getSituationRemake());
        merchantSituationReq.setAdminId(merchantSituationDTO.getAdminId());
        merchantSituationReq.setAdminName(merchantSituationDTO.getAdminName());
        merchantSituationReq.setActivityScope(merchantSituationDTO.getActivityScope());
        merchantSituationReq.setSku(merchantSituationDTO.getSku());
        merchantSituationReq.setCategoryType(merchantSituationDTO.getCategoryType());
        merchantSituationReq.setCouponId(merchantSituationDTO.getCouponId());
        merchantSituationReq.setCreatorId(merchantSituationDTO.getCreatorId());
        merchantSituationReq.setCreatorName(merchantSituationDTO.getCreatorName());
        merchantSituationReq.setExamineId(merchantSituationDTO.getExamineId());
        merchantSituationReq.setExamineTime(merchantSituationDTO.getExamineTime());
        merchantSituationReq.setExamineName(merchantSituationDTO.getExamineName());
        merchantSituationReq.setLadderPrice(merchantSituationDTO.getLadderPrice());
        merchantSituationReq.setSalePrice(merchantSituationDTO.getSalePrice());
        merchantSituationReq.setAfterCouponPriceStr(merchantSituationDTO.getAfterCouponPriceStr());
        merchantSituationReq.setOrderQuantity(merchantSituationDTO.getOrderQuantity());
        merchantSituationReq.setSituationType(merchantSituationDTO.getSituationType());
        merchantSituationReq.setCreateTime(merchantSituationDTO.getCreateTime());
        merchantSituationReq.setUpdateTime(merchantSituationDTO.getUpdateTime());
        merchantSituationReq.setAttachedImage(merchantSituationDTO.getAttachedImage());
        merchantSituationReq.setFeishuImgCode(merchantSituationDTO.getFeishuImgCode());
        merchantSituationReq.setAreaNo(merchantSituationDTO.getAreaNo());
        if (!CollectionUtils.isEmpty(merchantSituationDTO.getCouponBlackAndWhiteDTOS())) {
            List<CouponBlackAndWhiteDTO> couponBlackAndWhiteDTOS = new ArrayList<>();
            merchantSituationDTO.getCouponBlackAndWhiteDTOS().stream().forEach(couponBlackAndWhiteDTO -> {
                CouponBlackAndWhiteDTO couponBlackAndWhiteDTO1 = new CouponBlackAndWhiteDTO();
                couponBlackAndWhiteDTO1.setSku(couponBlackAndWhiteDTO.getSku());
                couponBlackAndWhiteDTO1.setType(couponBlackAndWhiteDTO.getType());
                couponBlackAndWhiteDTO1.setCreator(merchantSituationReq.getAdminName());
                couponBlackAndWhiteDTOS.add(couponBlackAndWhiteDTO1);
            });
            merchantSituationReq.setCouponBlackAndWhiteDTOS(couponBlackAndWhiteDTOS);
        }
        return merchantSituationReq;
    }
}
