package net.summerfarm.crm.mq.constant;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
public class MessageType {
    /**
     * 默认处理
     */
    public static final String DEFAULT = "DEFAULT";

    /**
     * 拜访记录处理
     */
    public static final String FOLLOW_UP_RECORD = "FOLLOW_UP_RECORD";
    /**
     * 销售EXCEL数据处理
     */
    public static final String SALE_DATA_EXCEL = "SALE_DATA_EXCEL";
    /**
     * 客户倒闭审批
     */
    public static final String CUSTOMER_BANKRUPTCY_APPROVAL = "CUSTOMER_BANKRUPTCY_APPROVAL";
    /**
     * 更新POI
     */
    public static final String UPDATE_POI = "UPDATE_POI";
    /**
     * 发券
     */
    public static final String ISSUE_COUPON = "ISSUE_COUPON";
    /**
     * 冻结库存
     */
    public static final String FROZEN_INVENTORY = "FROZEN_INVENTORY";
    /**
     * 解冻库存
     */
    public static final String UNFREEZE_INVENTORY = "UNFREEZE_INVENTORY";
    /**
     * 发送样品反馈消息
     */
    public static final String DING_DING_MESSAGE = "DING_DING_MESSAGE";
    /**
     * 客情券审批
     */
    public static final String MERCHANT_SITUATION_APPROVAL = "MERCHANT_SITUATION_APPROVAL";
    /**
     * 品类券审批
     */
    public static final String CATEGORY_COUPON_APPROVAL = "CATEGORY_COUPON_APPROVAL";
    /**
     * 月活券审批
     */
    public static final String MONTHLY_LIVING_APPROVAL = "MONTHLY_LIVING_APPROVAL";
    /**
     * 样品消息消费失败回滚
     */
    public static final String SAMPLE_ROLLBACK = "SAMPLE_ROLLBACK";
    /**
     * 更新样品申请配送时间
     */
    public static final String SAMPLE_UPDATE_DELIVERY_TIME = "SAMPLE_UPDATE_DELIVERY_TIME";

    /**
     * 门店poi更新审核
     */
    public static final String MERCHANT_POI_UPDATE = "MERCHANT_POI_UPDATE";

    /**
     * 门店疑似重复判断
     */
    public static final String MERCHANT_REPEATED_JUDGMENT = "MERCHANT_REPEATED_JUDGMENT";
    /**
     * 门店 OCR 风控
     */
    public static final String RISK_MERCHANT = "RISK_MERCHANT";

    /**
     * 企业微信推送
     */
    public static final String ENTERPRISE_WX_PUSH = "ENTERPRISE_WX_PUSH";

    /**
     * 企微用户创建
     */
    public static final String WECOM_USER_CREATE = "WECOM_USER_CREATE";

    /**
     * 后台批量创建样品单自动审核
     */
    public static final String AUTO_REVIEW_SAMPLE_APPLY = "AUTO_REVIEW_SAMPLE_APPLY";
}
