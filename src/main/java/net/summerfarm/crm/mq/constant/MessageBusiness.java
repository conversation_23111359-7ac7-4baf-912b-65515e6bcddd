package net.summerfarm.crm.mq.constant;

/**
 * <AUTHOR>
 * @Description 消息业务处理枚举
 * @date 2022/6/18 16:23
 */
public class MessageBusiness {
    /**
     * 默认处理
     */
    public static final String DEFAULT = "DEFAULT";
    /**
     * EXCEL
     */
    public static final String EXCEL = "FOLLOW_UP_EXCEL";
    /**
     * DING_DING
     */
    public static final String DING_DING = "DING_DING";
    /**
     * CONTACT
     */
    public static final String CONTACT = "CONTACT";
    /**
     * COUPON
     */
    public static final String COUPON = "COUPON";
    /**
     * 库存
     */
    public static final String STOCK = "STOCK";
    /**
     * 客情
     */
    public static final String MERCHANT_SITUATION = "MERCHANT_SITUATION";
    /**
     * 样品
     */
    public static final String SAMPLE = "SAMPLE";
    /**
     * poi更新
     */
    public static final String POI_UPDATE = "POI_UPDATE";

    /**
     * 门店重复判断
     */
    public static final String REPEATED_MERCHANT = "REPEATED_MERCHANT";
    /**
     * 风控门店
     */
    public static final String RISK_MERCHANT = "RISK_MERCHANT";

    /**
     * 企业微信消息推送
     */
    public static final String  ENTERPRISE_WX_PUSH= "ENTERPRISE_WX_PUSH";

    /**
     * 企微用户创建
     */
    public static final String WECOM_USER_CREATE = "WECOM_USER_CREATE";
}
