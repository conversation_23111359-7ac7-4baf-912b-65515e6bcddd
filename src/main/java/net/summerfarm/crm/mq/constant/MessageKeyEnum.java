package net.summerfarm.crm.mq.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/6/8 11:35
 */
@Getter
@AllArgsConstructor
public enum MessageKeyEnum {
    DING_DING_MESSAGE("ding_talk_msg", "发送样品反馈消息"), DING_DING("ding_talk_msg", "钉钉消息"), UPDATE_POI("update_poi", "更新POI"), FOLLOW_UP_RECORD("follow_up_record", "拜访记录消息"), CUSTOMER_BANKRUPTCY_APPROVAL("customer_bankruptcy_approval", "客户倒闭审批"), ISSUE_COUPON("issue_coupon", "发券"), SALE_DATA_EXCEL("sale_data_excel", "销售EXCEL数据处理"),
    AUTO_REVIEW_SAMPLE_APPLY("auto_review_sample_apply", "批量自动审核样品消息");
    private String key;

    private String desc;
}
