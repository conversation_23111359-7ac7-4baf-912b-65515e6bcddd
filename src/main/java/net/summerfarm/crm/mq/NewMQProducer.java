package net.summerfarm.crm.mq;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.MD5Util;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class NewMQProducer {
    /**
     * 微信推送相关topic
     */
    public static final String TOPIC_CRM_WX_OFFICIAL_PUSH = "topic_crm_wx_push";
    /**
     * 微信推送相关tag
     */
    public static final String TAG_CRM_WX_FEEDBACK = "tag_crm_wx_feedback_push";

    /**
     * 微信推送相关group :GID_tms_purchase_order_create
     */
    public static final String GID_CRM_WX_FEEDBACK = "GID_crm_wx_feedback_push";

    @Resource
    MqProducer mqProducer;

    public void sendMessage(String topic, String tag, Object message) {
        log.info("消息发送 topic:{} ,tag:{}, message:{}", topic, tag, message);
        //普通消息,没有tag则传null
        mqProducer.send(topic, tag, message);
        //普通消息带业务key
    }

}
