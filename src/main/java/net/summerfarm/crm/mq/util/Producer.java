package net.summerfarm.crm.mq.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageKeyEnum;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.rocketmq.support.producer.SendResultDTO;
import org.apache.rocketmq.client.producer.TransactionSendResult;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static net.summerfarm.common.util.rocketmq.RocketMqMessageConstant.GID_CRM;
import static net.summerfarm.common.util.rocketmq.RocketMqMessageConstant.GID_MANAGE;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Component
@Slf4j
public class Producer {
    @Resource
    private MqProducer mqProducer;
    private static final Logger LOGGER = LoggerFactory.getLogger(Producer.class);

    public void sendDataToQueue(String queueKey, MessageKeyEnum key, String data) {
        mqProducer.send(queueKey, null, key.getKey(), data);
        LOGGER.info("发送mq消息,队列:{},消息内容:{}", queueKey, data);
    }

    /**
     * 发送延迟队列
     *
     * @param queueKey  topic
     * @param data      数据
     * @param delayTime 延迟时间
     */
    public void sendDelayQueue(String queueKey, String data, Long delayTime) {
        mqProducer.sendDelay(queueKey, null, data, delayTime);
        LOGGER.info("发送mq消息,队列:{},消息内容:{}", queueKey, data);
    }

    public void sendDataToQueue(String queueKey,MessageKeyEnum key, Object data) {
        mqProducer.send(queueKey,null,key.getKey(),data);
        LOGGER.info("发送mq消息,队列:{},消息内容:{}", queueKey, JSONUtil.toJsonStr(data));
    }


    public void sendMessageInTransaction(String queueKey, Object data) {
        SendResultDTO sendResultDTO = mqProducer.sendTransaction(GID_MANAGE, queueKey, null, data);

        LOGGER.info("准备发送事务消息,body:{},result:{}", JSONUtil.toJsonStr(data), sendResultDTO.getSendStatus());
    }
}
