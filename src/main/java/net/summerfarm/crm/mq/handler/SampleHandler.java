package net.summerfarm.crm.mq.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.enums.SampleApplyStatusEnum;
import net.summerfarm.crm.mapper.manage.SampleApplyMapper;
import net.summerfarm.crm.mapper.manage.SampleApplyReviewMapper;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.domain.SampleApplyReview;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.service.SampleApplyService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @describe
 * @create 2022-08-24 15:45
 */
@Service
@Slf4j
public class SampleHandler implements CrmBusiness {

    private static final Logger logger = LoggerFactory.getLogger(SampleHandler.class);

    @Resource
    private SampleApplyMapper sampleApplyMapper;

    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;

    @Resource
    private SampleApplyService sampleApplyService;

    @MqMethodHandle(param = MessageType.SAMPLE_ROLLBACK)
    public void sampleRollback(JSONObject jsonObject){
        Integer sampleId = jsonObject.getInteger("sampleId");

        SampleApply sampleApply = new SampleApply();
        sampleApply.setStatus(SampleApplyStatusEnum.CLOSE.getId());
        sampleApply.setSampleId(sampleId);
        logger.info("关闭样品申请:{}", sampleApply);
        sampleApplyMapper.updateSampleApply(sampleApply);

        SampleApplyReview sampleApplyReview = new SampleApplyReview();
        sampleApplyReview.setSampleId(sampleId);
        sampleApplyReview.setStatus(NumberUtils.INTEGER_ONE);
        sampleApplyReview.setReviewRemark("申请商品暂无库存或当前区域已停配,请联系仓配同学了解详情");
        sampleApplyReview.setReviewId(CrmGlobalConstant.SYSTEM_ID);
        sampleApplyReview.setReviewName(CrmGlobalConstant.SYSTEM_NAME);
        logger.info("样品审核不通过:{}", sampleApplyReview);
        sampleApplyReviewMapper.updateBySampleId(sampleApplyReview);
    }

    @MqMethodHandle(param = MessageType.SAMPLE_UPDATE_DELIVERY_TIME)
    public void updateDeliveryTime(JSONObject jsonObject){
        String sampleApplyStr = jsonObject.getString("sampleApply");
        SampleApply sampleApply = JSON.parseObject(sampleApplyStr,SampleApply.class);

        //系统创建样品单不更新配送时间--定时任务创建完后会直接更新配送时间（跟随订单时间一起）
        SampleApply sampleById = sampleApplyMapper.selectSampleById(sampleApply.getSampleId());
        if (sampleById == null) {
            logger.info("更新样品配送时间失败！样品单信息为空！sampleApply:{}",sampleApply);
            return;
        }
        if (sampleById.getCreateId() == null || Objects.equal(sampleById.getCreateName(), "系统默认")) {//NOSONAR
            logger.info("系统批量创建样品单无需更新样品单配送日期！sampleApply:{}",sampleApply);
            return;
        }
        logger.info("更新样品配送时间：{}",sampleApply);
        sampleApplyMapper.updateSampleApply(sampleApply);
    }

    @MqMethodHandle(param = MessageType.AUTO_REVIEW_SAMPLE_APPLY)
    public void autoReviewSampleApply(JSONObject jsonObject){
        logger.info("后台批量创建样品单自动审核开始：{}",jsonObject);
        String sampleApplyList = jsonObject.getString("sampleApplyIds");

        List<Integer> sampleApplyIds = JSON.parseArray(sampleApplyList, Integer.class);
        sampleApplyService.syncSampleApplyReview(sampleApplyIds);
    }
}
