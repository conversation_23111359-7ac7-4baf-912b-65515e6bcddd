package net.summerfarm.crm.mq.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.MerchantSizeEnum;
import net.summerfarm.crm.enums.RiskMerchantEnum;
import net.summerfarm.crm.mapper.manage.AreaMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.mapper.manage.RiskMerchantMapper;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.domain.RiskMerchant;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.service.RiskMerchantService;
import net.summerfarm.pojo.DO.Area;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static net.summerfarm.crm.enums.RiskMerchantEnum.TriggerClassification.SUSPECTED_DUPLICATION;
import static net.summerfarm.crm.enums.RiskMerchantEnum.TriggerClassification.SUSPECTED_SPURIOUS;

/**
 * 门店 ocr 风控
 *
 * <AUTHOR>
 * @Date 2023/11/21 14:45
 */
@Slf4j
@Service
public class RiskMerchantHandler implements CrmBusiness {
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private RiskMerchantMapper riskMerchantMapper;
    @Resource
    private RiskMerchantService riskMerchantService;

    @MqMethodHandle(param = MessageType.RISK_MERCHANT)
    public void riskMerchant(JSONObject jsonObject) {
        log.info("风控门店input:{}", jsonObject.toJSONString());
        Long mId = jsonObject.getLong("mId");
        MerchantVO merchantVO = merchantMapper.selectMerchantByMid(mId);
        if (merchantVO == null) {
            log.info("找不到风控门店,Mid:{}", mId);
            return;
        }
        // 只处理单店
        if (!merchantVO.getSize().equals(MerchantSizeEnum.SINGGLE_STORE.getValue())) {
            log.info("风控门店非单店:{}", merchantVO.getSize());
            return;
        }
        String ocrName = jsonObject.getString("doorPicOcr");
        // 门店 ocr 相似度大于 30%
        double similar = 0;
        if (StrUtil.isNotEmpty(ocrName) && (similar = StrUtil.similar(ocrName, merchantVO.getMname())) > 0.3) {
            log.info("门店{} ocrName:{},mname:{},similar:{}", mId, ocrName, merchantVO.getMname(), similar);
            return;
        }
        RiskMerchant risk = riskMerchantService.createRiskMerchant(merchantVO);
        risk.setDoorPicOcr(ocrName);
        risk.setTriggerClassification(SUSPECTED_DUPLICATION.getCode());
        riskMerchantMapper.insertSelective(risk);
    }
}
