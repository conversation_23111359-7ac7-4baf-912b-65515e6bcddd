package net.summerfarm.crm.mq.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.req.MessageBodyReq;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.CouponEnum;
import net.summerfarm.crm.enums.ExamineEnum;
import net.summerfarm.crm.facade.FeiShuPersonalMsgFacade;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.CategoryCouponQuotaChange;
import net.summerfarm.crm.model.domain.MerchantSituation;
import net.summerfarm.crm.model.domain.MerchantSituationQuota;
import net.summerfarm.crm.model.vo.MerchantSituationVO;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.service.CrmCouponExpensePoolService;
import net.summerfarm.pojo.DO.Admin;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/19 16:41
 */
@Service
@Slf4j
public class MerchantSituationHandler implements CrmBusiness {

    private static final Logger logger = LoggerFactory.getLogger(MerchantSituationHandler.class);

    @Resource
    private MerchantSituationMapper merchantSituationMapper;

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private MerchantSituationQuotaMapper merchantSituationQuotaMapper;

    @Resource
    CategoryCouponQuotaChangeMapper quotaChangeMapper;
    @Resource
    CategoryCouponQuotaMapper quotaMapper;
    @Resource
    MerchantQueryFacade merchantQueryFacade;
    @Resource
    CrmCouponExpensePoolService crmCouponExpensePoolService;
    @Resource
    FeiShuPersonalMsgFacade feiShuPersonalMsgFacade;
    @MqMethodHandle(param = MessageType.MERCHANT_SITUATION_APPROVAL)
    public void saveMerchantSituationApprovalResult(JSONObject jsonObject){
        Integer merchantSituationId = jsonObject.getInteger("merchantSituationId");
        String successFlag = jsonObject.getString("successFlag");
        String adminIdStr = jsonObject.getString("adminId");
        Integer merchantCouponId = jsonObject.getInteger("merchantCouponId");
        logger.info("客情审批结束,开始执行客情流程,merchantSituationId:{},successFlag:{},adminId:{},merchantCouponId:{}", merchantSituationId, successFlag, adminIdStr, merchantCouponId);

        Admin admin = null;
        try {
            long adminId = Long.parseLong(adminIdStr);
            admin = adminMapper.selectByPrimaryKey((int) adminId);
        } catch (Exception e) {
            logger.info("审批人钉钉未绑定鲜沐后台账号,id:{}",adminIdStr);
        }

        MerchantSituation queryMerchantSituation = merchantSituationMapper.querySituation(merchantSituationId);
        if(Objects.isNull(queryMerchantSituation)
                || ObjectUtil.notEqual(queryMerchantSituation.getStatus(), ExamineEnum.MerchantSituation.SITUATION_STATUS_NEW.ordinal())){
            logger.info("申请单不存在或申请单已被审核");
            return;
        }

        // 更新客情审核记录
        Integer adminId = Objects.isNull(admin) ? CrmGlobalConstant.SYSTEM_ID : admin.getAdminId();
        String adminName = Objects.isNull(admin) ? CrmGlobalConstant.SYSTEM_AUDIT : admin.getRealname();
        queryMerchantSituation.setExamineTime(LocalDateTime.now());
        queryMerchantSituation.setExamineId(adminId);
        queryMerchantSituation.setExamineName(adminName);
        queryMerchantSituation.setGmtModified(new Date());
        queryMerchantSituation.setMerchantCouponId(merchantCouponId);
        boolean isSuccess=CrmGlobalConstant.SUCCESS_FLAG.equals(successFlag);
        // 状态
        int status =isSuccess ? ExamineEnum.MerchantSituation.SITUATION_STATUS_PASS.ordinal() : ExamineEnum.MerchantSituation.SITUATION_STATUS_CLOSE.ordinal();
        queryMerchantSituation.setStatus(status);
        merchantSituationMapper.updateSituation(queryMerchantSituation);

        CouponEnum.CouponType couponType = CouponEnum.CouponType.getCouponType(queryMerchantSituation.getSituationType());

        // 如果是客情申请,且审批被拒绝,还需要返回额度
        if(!isSuccess && ObjectUtil.equal(CouponEnum.CouponType.MERCHANT_SITUATION_COUPON,couponType)){
            logger.info("客情审批拒绝,开始返回客情额度,{}",queryMerchantSituation.getId());
            this.returnAmount(queryMerchantSituation);
        }
        boolean isCategoryType = CouponEnum.CouponType.CATEGORY_COUPON.equals(couponType) || CouponEnum.CouponType.MERCHANT_SITUATION_CATEGORY_COUPON.equals(couponType)
                || CouponEnum.CouponType.MONTH_LIVE_COUPON.equals(couponType);
        // 品类券-绑定券id
        if (isSuccess && isCategoryType) {
            //品类拓展
            if ( CouponEnum.CouponType.MERCHANT_SITUATION_CATEGORY_COUPON.equals(couponType) || CouponEnum.CouponType.MONTH_LIVE_COUPON.equals(couponType)){
                quotaChangeMapper.updateCouponIdByBizId(merchantSituationId, merchantCouponId);
            }else {
                try {
                    crmCouponExpensePoolService.successUpdateAdminPool(queryMerchantSituation);
                }catch (Exception e){
                    errorMsg(queryMerchantSituation, e.getMessage());
                }
            }
        } else if (!isSuccess && (CouponEnum.CouponType.CATEGORY_COUPON.equals(couponType) || CouponEnum.CouponType.MONTH_LIVE_COUPON.equals(couponType))) {
            log.info("品类券审批拒绝，返还品类券额度,merchantSituationId:{}", merchantSituationId);
            if (CouponEnum.CouponType.CATEGORY_COUPON.equals(couponType)){
                log.info("品类券-价格补贴审批拒绝，啥也不干,merchantSituationId:{}", merchantSituationId);
            }else {
                CategoryCouponQuotaChange quotaChange = quotaChangeMapper.selectByBizId(merchantSituationId);
                quotaMapper.updateQuota(quotaChange.getAdminId(), quotaChange.getQuota().negate(), couponType.ordinal());
                quotaChangeMapper.delByBizId(merchantSituationId);
            }
        } else if (!isSuccess && CouponEnum.CouponType.MERCHANT_SITUATION_CATEGORY_COUPON.equals(couponType)) {
            log.info("品类拓展券审批拒绝，返还品类券额度,merchantSituationId:{}", merchantSituationId);
            quotaChangeMapper.delByBizId(merchantSituationId);
        }
    }



    private void returnAmount(MerchantSituation queryMerchantSituation){
        Integer adminId = queryMerchantSituation.getCreatorId();

        // 获取申请金额
        MerchantSituationVO merchantSituationVO = new MerchantSituationVO();
        merchantSituationVO.setId(queryMerchantSituation.getId());
        List<MerchantSituationVO> merchantSituationVOList = merchantSituationMapper.querySituationListTime(merchantSituationVO);
        if(CollectionUtils.isEmpty(merchantSituationVOList)){
            logger.warn("客情审批拒绝后获取申请金额信息异常,{}",queryMerchantSituation.getId());
        }

        BigDecimal couponAmount = merchantSituationVOList.get(NumberUtils.INTEGER_ZERO).getCouponAmount();
        couponAmount = Objects.isNull(couponAmount) ? BigDecimal.ZERO : couponAmount;
        MerchantStoreAndExtendResp merchantExtend = merchantQueryFacade.getMerchantExtendsByMid(queryMerchantSituation.getMerchantId());
        if(Objects.isNull(merchantExtend)){
            logger.warn("客情审批拒绝后获取客户信息异常,{}",queryMerchantSituation.getMerchantId());
            return;
        }
        //申请归还本次客情金额
        MerchantSituationQuota merchantSituationQuota = new MerchantSituationQuota();
        MerchantSituationQuota querySituationQuota = merchantSituationQuotaMapper.queryOne(adminId ,merchantExtend.getAreaNo());
        if(Objects.isNull(querySituationQuota)){
            logger.warn("客情审批拒绝后获取销售客情额度异常,{},{}",adminId ,merchantExtend.getAreaNo());
            return;
        }
        BigDecimal subtract = querySituationQuota.getAmount().subtract(couponAmount);
        merchantSituationQuota.setAmount(subtract.compareTo(BigDecimal.ZERO) >= 0 ? subtract : BigDecimal.ZERO);
        merchantSituationQuota.setId(querySituationQuota.getId());
        merchantSituationQuotaMapper.updateQuota(merchantSituationQuota);
    }


    public void errorMsg(MerchantSituation queryMerchantSituation, String message){
        Integer adminId = queryMerchantSituation.getCreatorId();
        log.info("品类拓展 卡卷异常message {}",adminId);
        Admin admin = adminMapper.selectByPrimaryKey(adminId);
        if (admin == null){
            return;
        }
        MerchantStoreAndExtendResp merchantExtendsByMid = merchantQueryFacade.getMerchantExtendsByMid(queryMerchantSituation.getMerchantId());
        if (merchantExtendsByMid == null){
            return;
        }
        MessageBodyReq req = new MessageBodyReq();
        req.setContentType(1);
        req.setMsgBodyType(0);
        req.setTitle("品类拓展-客情卷申请异常提醒");
        StringBuilder content = new StringBuilder();
        content.append("申请异常提醒：").append(message).append("\n");
        content.append("> ###### 申请人：").append(admin.getRealname()).append("\n");
        content.append("> ###### 审批时间：").append(DateUtils.localDateTimeToString(queryMerchantSituation.getApprovalTime())).append("\n");
        content.append("> ###### 客户名称：").append(merchantExtendsByMid.getStoreName()).append("\n");
        String msgStr = content.toString().replaceAll("#","")
                .replaceAll(">>", ">")
                .replaceAll(">", "\n")
                .replaceAll("\n\n", "\n")
                .replaceAll(" ","");
        JSONObject text = new JSONObject();
        text.put("text", msgStr);
        req.setData(text.toJSONString());
        feiShuPersonalMsgFacade.sendFeiShuPersonalMsg(Collections.singletonList(Long.valueOf(adminId)), req);
    }
}
