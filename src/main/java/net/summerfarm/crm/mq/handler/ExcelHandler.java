package net.summerfarm.crm.mq.handler;


import com.alibaba.fastjson.JSONObject;
import com.cosfo.summerfarm.model.dto.order.OrderItemVO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.QiNiuConstant;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.enums.FileDownloadRecordEnum;
import net.summerfarm.crm.mapper.manage.FileDownloadRecordMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRecordMapper;
import net.summerfarm.crm.model.domain.FileDownloadRecord;
import net.summerfarm.crm.model.query.TeamDataQuery;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.model.vo.TeamDataVO;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.service.FollowUpRecordService;
import net.summerfarm.crm.service.QiNiuService;
import net.summerfarm.crm.service.SalesDataService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 拜访记录处理
 * @date 2022/6/18 14:57
 */
@Service
@Slf4j
public class ExcelHandler implements CrmBusiness {

    private static final Logger logger = LoggerFactory.getLogger(ExcelHandler.class);

    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;

    @Resource
    private FollowUpRecordMapper followUpRecordMapper;

    @Resource
    private QiNiuService qiNiuService;

    @Resource
    private FollowUpRecordService followUpRecordService;

    @MqMethodHandle(param = MessageType.FOLLOW_UP_RECORD)
    public void downRecord(JSONObject jsonObject){
        String params = jsonObject.getString("params");
        String uId = jsonObject.getString("UUID");
        Integer adminId = jsonObject.getInteger("adminId");
        FollowUpRecordVO selectKeys = JSONObject.parseObject(params, FollowUpRecordVO.class);
        if (Objects.isNull(uId)) {
            logger.warn("导出拜访记录,uId为空,发起人id:{}", adminId);
            return;
        }
        try {
            FileDownloadRecord fileDownloadRecord = this.fileDownloadRecordMapper.selectByUid(uId);
            if (Objects.isNull(fileDownloadRecord)) {
                logger.warn("导出拜访记录,文件信息为空,uid:{} 发起人id:{}", uId, adminId);
                return;
            }
            String name = fileDownloadRecord.getFileName();

            List<FollowUpRecordVO> followUpRecordVOList = followUpRecordMapper.selectByStart(selectKeys);
            followUpRecordService.fillMerchantAndAreaName(followUpRecordVOList);

            Workbook workbook = followUpRecordService.handleFollowMsg(followUpRecordVOList);
            //根据文件名获得token
            Map<String, String> data = UploadTokenFactory.createToken(name, QiNiuConstant.DEFAULT_EXPIRES);
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            // 上传至七牛云
            AjaxResult result = qiNiuService.uploadFile(name, workbook);
            if (Objects.equals(CrmGlobalConstant.SUCCESS_FLAG, result.getCode())) {
                FileDownloadRecord record = new FileDownloadRecord();
                record.setuId(uId);
                record.setStatus(FileDownloadRecordEnum.Status.SUCCESS.ordinal());
                this.fileDownloadRecordMapper.update(record);
            }
        } catch (Exception e) {
            FileDownloadRecord record = new FileDownloadRecord();
            record.setuId(uId);
            record.setStatus(FileDownloadRecordEnum.Status.FAIL.ordinal());
            fileDownloadRecordMapper.update(record);
            logger.error("导出拜访记录异常,uid:{},adminId:{},栈信息:{}", uId, adminId, e.getMessage(), e);
        }
    }

}
