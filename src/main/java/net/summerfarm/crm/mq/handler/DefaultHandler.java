package net.summerfarm.crm.mq.handler;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.MessageType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/18 15:07
 */
@Service
@Slf4j
public class DefaultHandler implements CrmBusiness {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultHandler.class);

    @MqMethodHandle()
    public void defaultMethod(JSONObject jsonObject){
        LOGGER.info("DEFAULT" + jsonObject.toJSONString());
    }
}
