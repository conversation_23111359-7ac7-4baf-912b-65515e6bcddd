package net.summerfarm.crm.mq.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.util.WeChatBaseUtil;
import net.summerfarm.crm.mapper.manage.WecomUserInfoMapper;
import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.model.query.wecom.WeComUserCreateInput;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.MessageType;
import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.EnterpriseWechatProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.USER_CREATE;
import static net.summerfarm.crm.common.util.WeChatBaseUtil.getApiUrl;
import static net.summerfarm.crm.facade.FeiShuPersonalMsgFacade.XIANMU_TENANT_ID;

/**
 * 企微账号创建
 *
 * <AUTHOR>
 * @date 2024/2/28 16:04
 */
@Slf4j
@Service
public class WeComUserCreateHandle implements CrmBusiness {
    @Resource
    private WecomUserInfoMapper userInfoMapper;
    @Resource
    private CrmConfig crmConfig;
    @DubboReference
    private EnterpriseWechatProvider wechatProvider;

    @MqMethodHandle(param = MessageType.WECOM_USER_CREATE)
    public void createUser(JSONObject jsonObject) {
        log.info("企微账号创建，参数：{}", jsonObject.toJSONString());
        // 默认不创建企微账号
        if (!crmConfig.getWecomCreateSwitch()) {
            log.info("企微创建结束，企微账号创建开关未开启");
            return;
        }
        WeComUserCreateInput createInput = jsonObject.toJavaObject(WeComUserCreateInput.class);
        if (StrUtil.isBlank(createInput.getMobile()) || StrUtil.isBlank(createInput.getName())) {
            throw new BizException("创建企微账号失败，用户名称或者手机号为空");
        }
        createInput.setDepartment(crmConfig.getDepartment());
        String userid = convertToPinyin(createInput.getName());
        int count = 1;
        // 重名用户新增编号
        while (userInfoMapper.selectByUserId(userid) != null) {
            userid = userid + count;
            count++;
        }
        createInput.setUserid(userid);
        String input = createInput.toJson();

        DubboResponse<String> resp = wechatProvider.queryEnterpriseWeChatToken(SystemOriginEnum.ADMIN, XIANMU_TENANT_ID, EnterpriseWeChatTokenTypeEnum.TXL_ACCESS_TOKEN, Boolean.TRUE);
        if (!resp.isSuccess()) {
            log.error("获取企微token失败:{}", JSONUtil.toJsonPrettyStr(resp));
            throw new BizException("获取企微token失败");
        }
        String post = WeChatBaseUtil.post(getApiUrl(USER_CREATE), resp.getData(), input);
        WeChatBaseResp baseResp = WeChatBaseResp.fromJson(post);
        if (!baseResp.success()) {
            log.error("创建企微账号失败，msg：{}", post);
            throw new BizException("创建企微账号失败，msg：" + post);
        }
        createUserInfo(createInput);
    }

    public void createUserInfo(WeComUserCreateInput createInput) {
        WecomUserInfo userInfo = new WecomUserInfo();
        userInfo.setUserId(createInput.getUserid());
        userInfo.setPhone(createInput.getMobile());
        userInfo.setAdminName(createInput.getName());
        userInfo.setAdminId(createInput.getAdminId());
        userInfo.setDepartment(createInput.getDepartment());
        userInfoMapper.insertSelective(userInfo);
    }

    public static String convertToPinyin(String str) {
        if (StrUtil.isBlank(str)) {
            throw new BizException("转换拼音失败，字符串为空");
        }
        StringBuilder sb = new StringBuilder();
        String pinyin = PinyinUtil.getPinyin(str);
        for (String s : pinyin.split(StrUtil.SPACE)) {
            String split = Character.toUpperCase(s.charAt(0)) + s.substring(1);
            sb.append(split);
        }
        return sb.toString();
    }
}
