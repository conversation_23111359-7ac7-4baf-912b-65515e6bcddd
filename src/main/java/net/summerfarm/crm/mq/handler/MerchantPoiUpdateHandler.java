package net.summerfarm.crm.mq.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.PoiUpdateStatusEnum;
import net.summerfarm.crm.mapper.manage.ContactMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRecordMapper;
import net.summerfarm.crm.mapper.manage.MerchantPoiUpdateRecordMapper;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.domain.FollowUpRecord;
import net.summerfarm.crm.model.domain.MerchantPoiUpdateRecord;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.constant.MessageBusiness;
import net.summerfarm.crm.mq.constant.MessageKeyEnum;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.util.Producer;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;

/**
 * poi更新
 *
 * <AUTHOR>
 * @Date 2023/6/27 18:05
 */
@Slf4j
@Service
public class MerchantPoiUpdateHandler implements CrmBusiness {
    @Resource
    private MerchantPoiUpdateRecordMapper poiUpdateRecordMapper;
    @Resource
    private FollowUpRecordMapper followUpRecordMapper;
    @Resource
    private Producer producer;
    @Resource
    private ContactMapper contactMapper;

    @Transactional(rollbackFor = Exception.class)
    @MqMethodHandle(param = MessageType.MERCHANT_POI_UPDATE)
    public void poiUpdateCallBack(JSONObject jsonObject) {
        log.info("poi更新：{}", jsonObject.toJSONString());

        Long bizId = jsonObject.getLong("bizId");
        Integer status = jsonObject.getInteger("status");
        MerchantPoiUpdateRecord record = poiUpdateRecordMapper.selectById(bizId);
        if (!ObjectUtil.equal(record.getStatus(), PoiUpdateStatusEnum.TO_BE_APPROVAL.getStatus())) {
            log.info("当前poi更新状态已为最终状态");
            return;
        }

        FollowUpRecord followUpRecord = new FollowUpRecord();
        followUpRecord.setId(record.getFollowUpRecordId().intValue());
        followUpRecord.setPoiUpdateFlag(status);
        followUpRecordMapper.updateById(followUpRecord);

        record.setStatus(status);
        poiUpdateRecordMapper.updateByPrimaryKeySelective(record);
        // 更新poi
        if (ObjectUtil.equal(record.getStatus(), PoiUpdateStatusEnum.PASS.getStatus())) {
            contactMapper.updatePoiById(record.getContactId(), record.getPoiAfterChange());
        }
    }
}
