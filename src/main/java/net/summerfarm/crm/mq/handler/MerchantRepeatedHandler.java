package net.summerfarm.crm.mq.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.EsUtil;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.domain.MerchantLabel;
import net.summerfarm.crm.model.domain.MerchantLabelCorrelaion;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import net.summerfarm.crm.mq.constant.MessageType;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.GeoDistanceQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 门店疑似重复
 *
 * <AUTHOR>
 * @Date 2023/6/28 10:45
 */
@Slf4j
@Service
public class MerchantRepeatedHandler implements CrmBusiness {
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private MerchantLabelMapper merchantLabelMapper;
    @Resource
    private MerchantLabelCorrelaionMapper relationMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private MerchantQueryFacade merchantQueryFacade;
    public static String LABEL = "疑似重复";

    @MqMethodHandle(param = MessageType.MERCHANT_REPEATED_JUDGMENT)
    public void repeatedJudgment(JSONObject jsonObject) {
        log.info("门店重复判断：{}", jsonObject.toJSONString());
        Long mId = jsonObject.getLong("mId");
        if (mId == null) {
            log.error("门店重复判断 mid为空：input{}", jsonObject.toJSONString());
            return;
        }
        MerchantStoreQueryReq req = new MerchantStoreQueryReq();
        req.setMId(mId);
        req.setQueryManageAccount(true);
        List<MerchantStoreAndExtendResp> merchantExtendList = merchantQueryFacade.getMerchantExtends(req);
        if (CollUtil.isEmpty(merchantExtendList)) {
            log.error("门店重复判断 mid不正确：input{}", jsonObject.toJSONString());
            return;
        }
        MerchantStoreAndExtendResp merchantExtend = merchantExtendList.get(0);
        if (StrUtil.isEmpty(merchantExtend.getPoiNote())) {
            log.info("门店poi为空，mid:{}", mId);
            return;
        }
        List<MerchantVO> merchantList = merchantMapper.selectByPhone(merchantExtend.getPhone(), mId);
        List<Double> poiList = StrUtil.split(merchantExtend.getPoiNote(), StrUtil.COMMA).stream().map(Double::parseDouble).collect(Collectors.toList());

        // 判断注册手机号重复
        List<Contact> contacts = contactMapper.selectByPhone(merchantExtend.getPhone(), mId);
        if (!merchantList.isEmpty() || !contacts.isEmpty()) {
            insertLabel(mId);
            return;
        }

        // 判断名称重复
        Map<String, Object> nearbyMerchant = EsUtil.queryByGeoDistance(poiList.get(1), poiList.get(0), mId);
        String key="data";
        if (nearbyMerchant.containsKey(key)) {
            Config config = configMapper.selectOne(ConfigValueEnum.REPEATED_JUDGMENT_THRESHOLD.getKey());
            JSONArray array = JSONUtil.parseArray(nearbyMerchant.get(key));
            for (Object json : array) {
                cn.hutool.json.JSONObject object = JSONUtil.parseObj(json);
                String mname = object.getStr("mname");
                double similar = StrUtil.similar(merchantExtend.getStoreName(), mname);
                log.info("门店:{}和门店:{}，相似度匹配:{}",merchantExtend.getStoreName(),mname,similar);
                if (similar > Double.parseDouble(config.getValue())) {
                    insertLabel(mId);
                    return;
                }
            }
        }
    }

    /**
     * 插入标签
     *
     * @param mId m id
     */
    public void insertLabel(Long mId) {
        MerchantLabel merchantLabel = merchantLabelMapper.selectByName(LABEL);
        MerchantLabelCorrelaion relation = new MerchantLabelCorrelaion();
        relation.setLabelId(merchantLabel.getId()).setMId(mId);
        relationMapper.insertSelective(relation);
    }
}
