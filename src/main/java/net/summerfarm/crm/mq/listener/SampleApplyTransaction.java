package net.summerfarm.crm.mq.listener;

/**
 * <AUTHOR>
 * @Date 2023/6/7 16:57
 */

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.enums.SampleApplyStatusEnum;
import net.summerfarm.crm.mapper.manage.SampleApplyMapper;
import net.summerfarm.crm.mapper.manage.SampleApplyReviewMapper;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.domain.SampleApplyReview;
import net.summerfarm.crm.model.vo.SampleApplyReviewVO;
import net.summerfarm.crm.mq.constant.MessageType;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.service.SampleApplyReviewService;
import net.summerfarm.crm.service.SampleApplyService;
import net.xianmu.rocketmq.support.annotation.MqTransactionProducer;
import net.xianmu.rocketmq.support.producer.AbstractMqTransactionProducer;
import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;


import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

import static net.summerfarm.common.util.rocketmq.RocketMqMessageConstant.*;
import static net.summerfarm.crm.mq.constant.CrmMqConstant.Topic.TOPIC_CRM_MALL_LIST;

@Slf4j
@MqTransactionProducer(producerGroup = GID_MANAGE, topic = TOPIC_CRM_MALL_LIST, tag = "*")
public class SampleApplyTransaction extends AbstractMqTransactionProducer<MessageExt> {
    @Resource
    private SampleApplyReviewService sampleApplyReviewService;
    @Resource
    private SampleApplyService sampleApplyService;
    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;
    @Resource
    private SampleApplyMapper sampleApplyMapper;

    @Override
    public LocalTransactionState getLocalTransaction(Object o) {
        JSONObject jsonObject = JSONObject.parseObject(JSONUtil.toJsonStr(o));
        log.info("开始执行本地事务:{}", jsonObject.toJSONString());
        MqData mqData = JSONObject.toJavaObject(jsonObject, MqData.class);
        try {
            if (MessageType.FROZEN_INVENTORY.equals(mqData.getType())) {
                return frozenInventory(jsonObject);
            } else if (MessageType.UNFREEZE_INVENTORY.equals(mqData.getType())) {
                return unFrozenInventory(jsonObject);
            } else {
                log.error("未找到该类型事务消息:{}", mqData.getType());
            }
        } catch (Exception e) {
            log.error("执行本地事务异常,堆栈信息:{}", e.getMessage(), e);
        }
        return LocalTransactionState.ROLLBACK_MESSAGE;
    }

    @Override
    public LocalTransactionState checkLocalTransaction(MessageExt message) {
        JSONObject jsonObject = getJsonObject(message);
        log.info("开始执行事务回查操作:{}", jsonObject.toJSONString());
        MqData mqData = JSONObject.toJavaObject(jsonObject, MqData.class);
        try {
            if (MessageType.FROZEN_INVENTORY.equals(mqData.getType())) {
                JSONObject data = jsonObject.getJSONObject("data");
                SampleApplyReview sampleApplyReview = data.getObject("sampleApplyReview", SampleApplyReview.class);
                SampleApplyReviewVO sampleApplyReviewVO = data.getObject("sampleApplyReviewVO", SampleApplyReviewVO.class);
                // 判断本地事务是否执行成功
                SampleApplyReview review = sampleApplyReviewMapper.isReview(sampleApplyReview.getSampleId(), sampleApplyReview.getStatus());
                SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleApplyReviewVO.getSampleId());
                boolean isSuccess = Objects.nonNull(review) && Objects.nonNull(sampleApply) && Objects.equals(SampleApplyStatusEnum.WAIT_HANDLE.getId(), sampleApply.getStatus());
                if (isSuccess) {
                    return LocalTransactionState.COMMIT_MESSAGE;
                }
            } else if (MessageType.UNFREEZE_INVENTORY.equals(mqData.getType())) {
                JSONObject data = jsonObject.getJSONObject("data");
                Integer sampleId = data.getInteger("sampleId");
                SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleId);
                if (Objects.equals(SampleApplyStatusEnum.CANCEL.getId(), sampleApply.getStatus())) {
                    return LocalTransactionState.COMMIT_MESSAGE;
                }
            } else {
                log.error("未找到该类型事务消息:{}", mqData.getType());
            }
        } catch (Exception e) {
            log.error("执行本地事务异常,堆栈信息:{}", e.getMessage(), e);
        }
        return LocalTransactionState.ROLLBACK_MESSAGE;
    }

    /**
     * 冻结库存本地事务
     *
     * @param jsonObject 消息内容
     * @return 是否提交半事务消息
     */
    private LocalTransactionState frozenInventory(JSONObject jsonObject) {
        try {
            // spring调用避免本地事务失效
            AjaxResult isSuccess = sampleApplyReviewService.frozenInventory(jsonObject);
            if (CrmGlobalConstant.SUCCESS_FLAG.equals(isSuccess.getCode())) {
                return LocalTransactionState.COMMIT_MESSAGE;
            }
        } catch (Exception e) {
            log.error("冻结库存本地事务报错:{}", e.getMessage(), e);
            return LocalTransactionState.ROLLBACK_MESSAGE;
        }
        return LocalTransactionState.ROLLBACK_MESSAGE;
    }

    /**
     * 解冻库存本地事务
     *
     * @param jsonObject 消息内容
     * @return 是否提交半事务消息
     */
    private LocalTransactionState unFrozenInventory(JSONObject jsonObject) {
        try {
            AjaxResult isSuccess = sampleApplyService.unFrozenInventory(jsonObject);
            if (CrmGlobalConstant.SUCCESS_FLAG.equals(isSuccess.getCode())) {
                return LocalTransactionState.COMMIT_MESSAGE;
            }
        } catch (Exception e) {
            log.error("解冻库存本地事务报错:{}", e.getMessage(), e);
            return LocalTransactionState.ROLLBACK_MESSAGE;
        }
        return LocalTransactionState.ROLLBACK_MESSAGE;
    }

    private JSONObject getJsonObject(Message message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        return JSONObject.parseObject(body);

    }
}
