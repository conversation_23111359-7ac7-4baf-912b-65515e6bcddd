//package net.summerfarm.crm.mq.listener;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.common.AjaxResult;
//import net.summerfarm.crm.common.constant.CrmGlobalConstant;
//import net.summerfarm.crm.enums.SampleApplyStatusEnum;
//import net.summerfarm.crm.mapper.manage.SampleApplyMapper;
//import net.summerfarm.crm.mapper.manage.SampleApplyReviewMapper;
//import net.summerfarm.crm.model.domain.SampleApply;
//import net.summerfarm.crm.model.domain.SampleApplyReview;
//import net.summerfarm.crm.model.vo.SampleApplyReviewVO;
//import net.summerfarm.crm.mq.constant.MessageType;
//import net.summerfarm.crm.mq.domain.MqData;
//import net.summerfarm.crm.service.SampleApplyReviewService;
//import net.summerfarm.crm.service.SampleApplyService;
//import org.apache.rocketmq.spring.annotation.RocketMQTransactionListener;
//import org.apache.rocketmq.spring.core.RocketMQLocalTransactionListener;
//import org.apache.rocketmq.spring.core.RocketMQLocalTransactionState;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeanUtils;
//import org.springframework.messaging.Message;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.nio.charset.StandardCharsets;
//import java.util.Date;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @Description
// * @date 2022/6/28 17:04
// */
//@Component
//@Slf4j
//@RocketMQTransactionListener()
//public class ProducerMsgListener implements RocketMQLocalTransactionListener {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(ProducerMsgListener.class);
//
//    @Resource
//    private SampleApplyReviewService sampleApplyReviewService;
//    @Resource
//    private SampleApplyService sampleApplyService;
//    @Resource
//    private SampleApplyReviewMapper sampleApplyReviewMapper;
//    @Resource
//    private SampleApplyMapper sampleApplyMapper;
//
//    /**
//     * 消息发送成功回调此方法，此方法执行本地事务
//     * @param message 消息体
//     * @param o 参数
//     * @return 是否发送消息
//     */
//    @Override
//    public RocketMQLocalTransactionState executeLocalTransaction(Message message, Object o) {
//        JSONObject jsonObject = getJsonObject(message);
//        LOGGER.info("开始执行本地事务:{}",jsonObject.toJSONString());
//        MqData mqData = JSONObject.toJavaObject(jsonObject, MqData.class);
//        try{
//            if(MessageType.FROZEN_INVENTORY.equals(mqData.getType())){
//                return frozenInventory(jsonObject);
//            }else if(MessageType.UNFREEZE_INVENTORY.equals(mqData.getType())){
//                return unFrozenInventory(jsonObject);
//            }else {
//                LOGGER.error("未找到该类型事务消息:{}",mqData.getType());
//            }
//        }catch (Exception e){
//            LOGGER.error("执行本地事务异常,堆栈信息:{}", e.getMessage(), e);
//        }
//        return RocketMQLocalTransactionState.ROLLBACK;
//    }
//
//    /**
//     * 此方法检查事务执行状态
//     * @param message 消息体
//     * @return 是否发送消息
//     */
//    @Override
//    public RocketMQLocalTransactionState checkLocalTransaction(Message message) {
//        JSONObject jsonObject = getJsonObject(message);
//        LOGGER.info("开始执行事务回查操作:{}",jsonObject.toJSONString());
//        MqData mqData = JSONObject.toJavaObject(jsonObject, MqData.class);
//        try{
//            if(MessageType.FROZEN_INVENTORY.equals(mqData.getType())){
//                JSONObject data = jsonObject.getJSONObject("data");
//                SampleApplyReview sampleApplyReview = data.getObject("sampleApplyReview", SampleApplyReview.class);
//                SampleApplyReviewVO sampleApplyReviewVO = data.getObject("sampleApplyReviewVO", SampleApplyReviewVO.class);
//                // 判断本地事务是否执行成功
//                SampleApplyReview review = sampleApplyReviewMapper.isReview(sampleApplyReview.getSampleId(), sampleApplyReview.getStatus());
//                SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleApplyReviewVO.getSampleId());
//                boolean isSuccess = Objects.nonNull(review) && Objects.nonNull(sampleApply) && Objects.equals(SampleApplyStatusEnum.WAIT_HANDLE.getId(), sampleApply.getStatus());
//                if(isSuccess){
//                    return RocketMQLocalTransactionState.COMMIT;
//                }
//            }else if(MessageType.UNFREEZE_INVENTORY.equals(mqData.getType())){
//                JSONObject data = jsonObject.getJSONObject("data");
//                Integer sampleId = data.getInteger("sampleId");
//                SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleId);
//                if(Objects.equals(SampleApplyStatusEnum.CANCEL.getId(),sampleApply.getStatus())){
//                    return RocketMQLocalTransactionState.COMMIT;
//                }
//            }else {
//                LOGGER.error("未找到该类型事务消息:{}",mqData.getType());
//            }
//        }catch (Exception e){
//            LOGGER.error("执行本地事务异常,堆栈信息:{}", e.getMessage(), e);
//        }
//        return RocketMQLocalTransactionState.ROLLBACK;
//    }
//
//    /**
//     * 冻结库存本地事务
//     * @param jsonObject 消息内容
//     * @return 是否提交半事务消息
//     */
//    private RocketMQLocalTransactionState frozenInventory(JSONObject jsonObject) {
//        try {
//            // spring调用避免本地事务失效
//            AjaxResult isSuccess = sampleApplyReviewService.frozenInventory(jsonObject);
//            if(CrmGlobalConstant.SUCCESS_FLAG.equals(isSuccess.getCode())){
//                return RocketMQLocalTransactionState.COMMIT;
//            }
//        } catch (Exception e) {
//            LOGGER.error("冻结库存本地事务报错:{}",e.getMessage(), e);
//            return RocketMQLocalTransactionState.ROLLBACK;
//        }
//        return RocketMQLocalTransactionState.ROLLBACK;
//    }
//
//    /**
//     * 解冻库存本地事务
//     * @param jsonObject 消息内容
//     * @return 是否提交半事务消息
//     */
//    private RocketMQLocalTransactionState unFrozenInventory(JSONObject jsonObject) {
//        try {
//            AjaxResult isSuccess = sampleApplyService.unFrozenInventory(jsonObject);
//            if(CrmGlobalConstant.SUCCESS_FLAG.equals(isSuccess.getCode())){
//                return RocketMQLocalTransactionState.COMMIT;
//            }
//        } catch (Exception e) {
//            LOGGER.error("解冻库存本地事务报错:{}",e.getMessage(),e);
//            return RocketMQLocalTransactionState.ROLLBACK;
//        }
//        return RocketMQLocalTransactionState.ROLLBACK;
//    }
//
//    private JSONObject getJsonObject(Message message) {
//        String body = new String((byte[]) message.getPayload(), StandardCharsets.UTF_8);
//        return JSONObject.parseObject(body);
//
//    }
//}
