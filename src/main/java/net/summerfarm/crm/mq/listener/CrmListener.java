package net.summerfarm.crm.mq.listener;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.crm.factory.BusinessFactory;
import net.summerfarm.crm.factory.MethodFactory;
import net.summerfarm.crm.mq.CrmBusiness;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.domain.MqData;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/18 11:49
 */
@Slf4j
@Component
@MqListener(topic = CrmMqConstant.Topic.CRM_LIST, consumerGroup = CrmMqConstant.ConsumeGroup.GID_CRM_LIST, maxReconsumeTimes = 2)
public class CrmListener extends AbstractMqListener<MqData> {
    @Resource
    private BusinessFactory businessFactory;
    @Resource
    private MethodFactory methodFactory;


    @Override
    public void process(MqData mqData) {
        LogConfigHolder.putInboundFlag("Topic:" + RocketMqMessageConstant.CRM_LIST + ":" + mqData.getType());
        log.info("rocketmq receive：{}", JSONObject.toJSONString(mqData));

        // 解析
        String dataStr;
        if (mqData.getData() instanceof String) {
            dataStr = String.valueOf(mqData.getData());
        } else {
            dataStr = JSONObject.toJSONString(mqData.getData());
        }
        JSONObject jsonObject = JSONObject.parseObject(dataStr);

        // 获取业务对象
        CrmBusiness creator = businessFactory.creator(mqData.getBusiness());
        if (Objects.nonNull(creator)) {
            // 执行业务方法
            Method method = methodFactory.getMethod(mqData.getType());
            if (Objects.nonNull(method)) {
                try {
                    method.invoke(creator, jsonObject);
                } catch (Exception e) {
                    log.error("mq消息解析执行失败:{}", e.getMessage(), e);
                }
            }
        } else {
            log.info("未找到业务处理对象,是否是没注册?,业务类型:{}", mqData.getBusiness());
        }

        LogConfigHolder.removeInboundFlag();
    }
}
