package net.summerfarm.crm.mq.listener;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mq.constant.CrmMqConstant;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.service.wecom.WeComMsgService;
import net.xianmu.log.helper.LogConfigHolder;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static net.summerfarm.crm.mq.constant.CrmMqConstant.WXMsgConsumeGroup.CRM_ENTERPRISE_WX_TOPIC;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/10/07 11:49
 */
@Slf4j
@Component
@MqListener(topic = CRM_ENTERPRISE_WX_TOPIC , consumerGroup = CrmMqConstant.WXMsgConsumeGroup.CRM_ENTERPRISE_WX_GROUP, maxReconsumeTimes = 2)
public class WxMessageListener extends AbstractMqListener<MqData> {

    @Resource
    WeComMsgService weComMsgService;
    @Override
    public void process(MqData mqData) {
        LogConfigHolder.putInboundFlag("Topic:" + CRM_ENTERPRISE_WX_TOPIC + ":" + mqData.getType());
        log.info("rocketmq receive：{}", JSONObject.toJSONString(mqData));
        log.info("门店审核消息 crm接受消息 {}",JSONObject.toJSONString(mqData));

        Object data = mqData.getData();
        if (data == null){
            return;
        }

        weComMsgService.sendAuditResultMsg(Long.valueOf(data.toString()));

        LogConfigHolder.removeInboundFlag();
    }
}
