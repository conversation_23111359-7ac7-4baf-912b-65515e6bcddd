package net.summerfarm.crm.model.bo;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import net.summerfarm.crm.enums.ProcessInstanceBizTypeEnum;

import java.util.List;


/**
 * 钉钉审批工作流-流程实例参数
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2021-12-21
 */
@Data
public class ProcessInstanceCreateBO {

    /**
     * 发起审批的业务
     */
    private ProcessInstanceBizTypeEnum bizTypeEnum;

    /**
     * 发起审批的业务数据id
     */
    private Long bizId;

    /**
     * 发起审批的系统用户id
     */
    private Integer adminId;

    /**
     * 钉钉审批流表单信息-必传
     */
    private List<DingdingFormBO> dingdingForms;

    /**
     * 审批实例发起人的userid-钉钉中的用户id
     */
    private String originatorUserId;

    /**
     * 发起人所在的部门，如果发起人属于根部门，传-1。
     */
    private Long deptId;

    public ProcessInstanceCreateBO() {
    }

    public ProcessInstanceCreateBO(ProcessInstanceBizTypeEnum bizTypeEnum, Long bizId, Integer adminId, List<DingdingFormBO> dingdingForms) {
        this.bizTypeEnum = bizTypeEnum;
        this.bizId = bizId;
        this.adminId = adminId;
        this.dingdingForms = dingdingForms;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
