package net.summerfarm.crm.model.bo.BDTarget;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 门店潜力值计算数据对象
 * 合并了BdVisitPlanIndicatorSync和BdDailyTargetDetail的相关字段
 */
@Data
public class MerchantPotentialCalculationData {
    
    /**
     * 门店ID
     */
    private Long merchantId;
    
    /**
     * 拜访计划ID
     */
    private Long bdVisitPlanId;
    
    /**
     * 拜访每日目标明细ID
     */
    private Long bdDailyTargetDetailId;
    
    /**
     * 指标当前值
     */
    private BigDecimal indicatorCurrentValue;
    
    /**
     * 指标潜力值
     */
    private BigDecimal indicatorPotentialValue;
    
    /**
     * 目标名称
     */
    private String targetName;
    
    /**
     * 目标指标权重
     */
    private BigDecimal targetIndicatorWeight;
    
    /**
     * 目标指标类型
     */
    private Integer targetIndicatorType;
    
    /**
     * 目标指标单位
     */
    private String targetIndicatorUnit;
    
    /**
     * 优先级, 1,2,3,4,5
     */
    private Integer priority;
    
    /**
     * 构造方法
     */
    public MerchantPotentialCalculationData() {
    }
}