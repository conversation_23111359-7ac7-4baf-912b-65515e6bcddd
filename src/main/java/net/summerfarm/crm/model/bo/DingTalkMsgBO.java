package net.summerfarm.crm.model.bo;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.crm.enums.DingTalkMsgEnum;

/**
 * <AUTHOR>
 * @describe
 * @create 2022-08-28 23:09
 */
@Data
@NoArgsConstructor
public class DingTalkMsgBO {

    /**
     * 接收人userId
     */
    private String userIdList;

    /**
     * text、markdown、link
     */
    private String msgType;

    /**
     * markdown 标题
     */
    private String title;

    /**
     * markdown 文本
     */
    private String text;
    /**
     * 方法类型,方法的唯一标识
     */
    private String type;

    public DingTalkMsgBO(String userIdList, String title, String text) {
        this.userIdList = userIdList;
        this.msgType = DingTalkMsgEnum.MARKDOWN.getType();
        this.title = title;
        this.text = text;
    }
}
