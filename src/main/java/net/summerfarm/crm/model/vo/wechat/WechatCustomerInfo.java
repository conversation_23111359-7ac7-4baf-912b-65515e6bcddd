package net.summerfarm.crm.model.vo.wechat;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/1 15:13
 */
@Data
public class WechatCustomerInfo {
    /**
     * unionId
     */
    private String unionId;

    /**
     * 企微外部用户id
     */
    private String externalUserid;

    /**
     * 企微用户id
     */
    private String userId;

    /**
     * adminId
     */
    private Long bizId;

    /**
     * 标签id
     */
    private List<String> tagId;

    /**
     * 添加时间
     */
    private LocalDateTime createtime;
    /**
     * 添加渠道
     */
    private String state;
}
