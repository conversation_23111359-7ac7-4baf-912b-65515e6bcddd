package net.summerfarm.crm.model.vo.salesperformance.categorypromotion;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 品类推广汇总数据VO
 * 销售维度汇总
 */
@Data
public class CategoryPromotionSummaryVO {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 客户数
     */
    private Integer customerCount;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 履约件数
     */
    private Double fulfillmentCount;

    /**
     * 交易件数
     */
    private Double transactionCount;
}
