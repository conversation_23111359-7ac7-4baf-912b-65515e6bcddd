package net.summerfarm.crm.model.vo.tool;

import lombok.Data;

import java.util.List;

/**
 * 销售是否在灰度范围的VO
 *
 * <AUTHOR>
 */
@Data
public class BdInGrayRangeVO {

    /**
     * 详情列表
     */
    private List<BdInGrayRangeDetailVO> details;

    @Data
    public static class BdInGrayRangeDetailVO {

        /**
         * 灰度项目
         * @see net.summerfarm.crm.enums.GrayProjectEnum#getCode()
         */
        private String grayProject;

        /**
         * 销售是否在灰度范围
         */
        private Boolean inGrayRange;
    }
}
