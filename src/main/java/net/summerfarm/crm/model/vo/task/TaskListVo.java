package net.summerfarm.crm.model.vo.task;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/28 14:36
 */
@Data
public class TaskListVo implements Serializable {
    /**
     * id
     */
    private Integer id;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 类型:0:发券;1:拜访;
     */
    private Integer type;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 卡券 id
     */
    private String sourceId;
    /**
     * 取消标记
     */
    private Integer deleteFlag;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
