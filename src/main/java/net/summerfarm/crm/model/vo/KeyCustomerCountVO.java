package net.summerfarm.crm.model.vo;



import lombok.Data;

/**
 * 重点客户个数
 * <AUTHOR>
 * @date 2022/6/14 1:44
 */
@Data
@Deprecated
public class KeyCustomerCountVO {
    /**
     * 总共个数
     */
    private Integer sumCount;

    /**
     * 即将掉落的个数
     */
    private Integer dangerCount;
    /**
     * 注册为下单个数
     */
    private Integer noOrderCount;

    /**
     * 首单个数
     */
    private Integer firstOrderCount;


    /**
     * 省心送总个数
     */
    private Integer comfortSendCount;

    public void sum(){
        sumCount = dangerCount + noOrderCount+ firstOrderCount ;
    }

    public void init() {
        dangerCount = 0;
        noOrderCount = 0;
        firstOrderCount =0;
        comfortSendCount= 0;
        sum();
    }
}
