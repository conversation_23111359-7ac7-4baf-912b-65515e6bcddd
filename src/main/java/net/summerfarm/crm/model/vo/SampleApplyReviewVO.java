package net.summerfarm.crm.model.vo;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.model.domain.ContactAddressRemark;
import net.summerfarm.crm.model.domain.SampleSku;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SampleApplyReviewVO {

    private Integer sampleId;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * '创建人id'
     */
    private Integer createId;
    /**
     * '创建人名称'
     */
    private String createName;

    /**
     * '试样用户id'
     */
    private Long mId;

    /**
     * '试样用户名称'
     */
    private String mName;

    /**
     * '试样用户收货地址id'
     */
    private Integer contactId;

    private Integer areaNo;

    /**
     * '客户所属用户bdid'
     */
    private Integer bdId;

    /**
     * 用户会员等级
     */
    private Integer grade;

    /**
     * 用户类型
     */
    private String mSize;

    /**
     * 用户联系方式
     */
    private String mPhone;

    /**
     * 用户联系人
     */
    private String mContact;

    /**
     * '客户归属bd名称'
     */
    private String bdName;

    /**
     * '状态 0 待反馈 1 已反馈 2-取消 3-待审核、4-已关闭'
     */
    private Integer status;

    /**
     * '客户满意度 0 未评价,  1 满意 ,2 一般, 3 不满意'
     */
    private Integer satisfaction;

    /**
     * '客户购买意向 0 未评价 1 乐意购买 ,2 考虑购买, 3 不购买'
     */
    private Integer purchaseIntention;

    /**
     * '备注'
     */
    private String remark;

    /**
     * 配送时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;

    /**
     * 仓库编号
     */
    private Integer storeNo;

    private String areaName;
    /**
     *  省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区域
     */
    private String area;
    /**
     * 地址
     */
    private String address;
    /**
     * 门牌号
     */
    private String houseNumber;

    List<SampleSku> sampleSkuList;
    /**
     * 审核人
     */
    private String reviewName;
    /**
     *审核备注
     */
    private String reviewRemark;
    /**
     *审核时间
     */
    private Date auditTime;

    private String addressRemark;

    private ContactAddressRemark contactAddressRemark;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getmName() {
        return mName;
    }

    public void setmName(String mName) {
        this.mName = mName;
    }

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }

    public String getmPhone() {
        return mPhone;
    }

    public void setmPhone(String mPhone) {
        this.mPhone = mPhone;
    }

    public String getmContact() {
        return mContact;
    }

    public void setmContact(String mContact) {
        this.mContact = mContact;
    }

    public ContactAddressRemark getContactAddressRemark() {
        if (!StringUtils.isEmpty(addressRemark)){
            return JSONUtil.toBean(addressRemark, ContactAddressRemark.class);
        }
        return contactAddressRemark;
    }
}
