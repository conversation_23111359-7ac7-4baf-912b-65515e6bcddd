package net.summerfarm.crm.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderOverviewVO {

    private String orderNo;

    /**
     * 订单状态
     *
     * @see net.summerfarm.crm.enums.SaasOrderEnum.Status
     */
    private Integer status;

    private String mname;

    /**
     * 应付金额
     */
    private BigDecimal totalPrice;

    private LocalDateTime orderTime;

    private LocalDateTime deliveryTime;

    private Long bdId;

    private String bdName;
}
