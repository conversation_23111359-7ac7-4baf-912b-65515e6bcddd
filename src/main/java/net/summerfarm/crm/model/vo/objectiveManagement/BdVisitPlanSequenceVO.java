package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import java.util.List;

/**
 * 销售拜访计划顺序
 *
 * <AUTHOR>
 */
@Data
public class BdVisitPlanSequenceVO {

    /**
     * 按顺序排列的拜访点位列表
     */
    private List<VisitSite> sequencedVisitSites;

    @Data
    public static class VisitSite {

        /**
         * 点位id
         */
        private Long id;

        /**
         * 点位poi
         */
        private PoiVO poi;
    }
}
