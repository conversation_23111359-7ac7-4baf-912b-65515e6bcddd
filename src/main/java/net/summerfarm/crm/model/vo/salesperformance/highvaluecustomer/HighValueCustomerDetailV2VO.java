package net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 高价值客户详情V2的VO
 * 客户维度汇总
 */
@Data
public class HighValueCustomerDetailV2VO {

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 奖励金额
     */
    private BigDecimal custCommAmt;

    /**
     * 本月截止昨晚履约GMV
     */
    private BigDecimal dlvRealAmt;

    /**
     * 今日待履约GMV
     */
    private BigDecimal dlvRealAmtToday;

    /**
     * 本月截止今天累计履约GMV(离线+今日)
     */
    private BigDecimal dlvMonthTodayTotalAmt;

    /**
     * 今日交易本月履约GMV
     */
    private BigDecimal dlvOrderAmtToday;

    /**
     * 其余待履约GMV
     */
    private BigDecimal dlvOtherAmtToday;

    /**
     * 本月预计总履约GMV
     */
    private BigDecimal dlvMonthTotalAmt;

    /**
     * 本月截止昨晚履约SPU
     */
    private Integer dlvSpuCnt;

    /**
     * 今日待履约SPU
     */
    private Integer dlvRealSpuCntToday;

    /**
     * 本月截止今天累计履约SPU(离线+今日 去重)
     */
    private Integer dlvMonthTodayTotalSpuCnt;

    /**
     * 今日交易本月履约SPU
     */
    private Integer dlvOrderSpuCntToday;

    /**
     * 其余待履约SPU
     */
    private Integer dlvOtherSpuCntToday;

    /**
     * 本月预计总履约SPU
     */
    private Integer dlvMonthTotalSpuCnt;

}
