package net.summerfarm.crm.model.vo.weCom;

import lombok.Data;
import net.summerfarm.crm.model.domain.WecomCommunicationSummary;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/2/21 14:22
 */
@Data
public class WeComCommunicationSummaryVo {
    /**
     * 销售 id
     */
    private Long bdId;
    /**
     * 销售名字。
     */
    private String bdName;
    /**
     * 会话数
     */
    private Integer conversationCount;
    /**
     * 消息数
     */
    private Integer messageCount;
    /**
     * 回复比例
     */
    private BigDecimal answerProportion;
    /**
     * 回复时间
     */
    private Integer answerDuration;

    public static WeComCommunicationSummaryVo toVo(WecomCommunicationSummary summary) {
        if (summary == null) {
            return null;
        }
        WeComCommunicationSummaryVo weComCommunicationSummaryVo = new WeComCommunicationSummaryVo();
        weComCommunicationSummaryVo.setBdId(summary.getBdId());
        weComCommunicationSummaryVo.setBdName(summary.getBdName());
        weComCommunicationSummaryVo.setConversationCount(summary.getConversationCount());
        weComCommunicationSummaryVo.setMessageCount(summary.getMessageCount());
        weComCommunicationSummaryVo.setAnswerDuration(summary.getAnswerDuration());
        weComCommunicationSummaryVo.setAnswerProportion(summary.getAnswerProportion());
        return weComCommunicationSummaryVo;
    }
}
