package net.summerfarm.crm.model.vo.couponQuota;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 月活券列表
 *
 * <AUTHOR>
 * @date 2023/11/28 16:12
 */
@Data
public class MonthLivingListVo {
    /**
     * id
     */
    private Integer id;
    /**
     * 销售 id
     */
    private Integer bdId;

    /**
     * 销售名字
     */
    private String bdName;

    /**
     * 新客户费比
     */
    private BigDecimal newCustomerRate;

    /**
     * 老客户费比
     */
    private BigDecimal oldCustomerRate;

    /**
     * 额度
     */
    private BigDecimal quota;

    /**
     * 已申请额度
     */
    private BigDecimal appliedQuota;

    /**
     * 已使用配额
     */
    private BigDecimal usedQuota;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
