package net.summerfarm.crm.model.vo.wechat;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/3 13:48
 */
@Data
public class ExternalContactDetail extends WeChatBaseResp {
    @JSONField(name = "external_contact")
    private ExternalContact externalContact;
    @JSONField(name = "follow_info")
    private FollowInfo followInfo;

    /**
     * 外部联系人
     *
     * <AUTHOR>
     * @date 2023/08/25
     */
    @Data
    public class ExternalContact {
        /**
         * 外部联系人的userid
         */
        private String externalUserid;

        /**
         * 外部联系人的名称
         */
        private String name;

        /**
         * 职位
         */
        private String position;

        /**
         * 外部联系人头像
         */
        private String avatar;

        /**
         * 企业简称
         */
        private String corpName;

        /**
         * 企业全称
         */
        private String corpFullName;

        /**
         * 外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
         */
        private Integer type;

        /**
         * 外部联系人性别 0-未知 1-男性 2-女性
         */
        private Integer gender;

        /**
         * unionId
         */
        private String unionid;
    }

    /**
     * 企业成员信息
     *
     * <AUTHOR>
     * @date 2023/08/25
     */
    @Data
    public class FollowInfo {
        /**
         * 用户标识
         */
        private String userid;
        /**
         * 添加时间
         */
        private Long createtime;
        /**
         * 标签id
         */
        @JSONField(name = "tag_id")
        private List<String> tagId;

        /**
         * 添加渠道
         */
        private String state;
    }

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static ExternalContactDetail fromJson(String json) {
        return JSONUtil.toBean(json, ExternalContactDetail.class);
    }
}
