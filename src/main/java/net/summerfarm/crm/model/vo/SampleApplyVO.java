package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.domain.SampleSku;

import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/26  11:08
 */
@Data
public class SampleApplyVO extends SampleApply {


    private String areaName;
    /**
     *  省份
     */
    private String province;
    /**
     *  城市
     */
    private String city;
    /**
     * 区域
     */
    private String area;
    /**
     * 地址
     */
    private String address;
    /**
     * 门牌号
     */
    private String houseNumber;

    List<SampleSku> sampleSkuList;

}
