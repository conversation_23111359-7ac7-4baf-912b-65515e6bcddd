package net.summerfarm.crm.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/6 14:28
 */
@Data
public class OpenSeaMerchantVO {
    /**
     * 地址
     */
    private String address;
    /**
     * 区域
     */
    private String area;
    /**
     * 区域编号
     */
    private Integer areaNo;
    /**
     * 城市
     */
    private String city;
    /**
     * 核心客户标记:0否1是
     */
    private Integer coreMerchantTag;
    /**
     * 倒计时
     */
    private Integer dangerDay;
    /**
     * 最近下单时间 ms
     */
    private Long lastOrderTime;
    /**
     * 最近下单时间 yyyy-MM-dd
     */
    private LocalDate latestOrderTime;
    /**
     * mid
     */
    private Long mId;
    /**
     * 白名单标识:0否1是
     */
    private Integer merchantWhiteListTag;
    /**
     * 商户名
     */
    private String mname;
    /**
     * 商户类型
     */
    private String size;
    /**
     * 未完结省心送标识:0否1是
     */
    private String timingFollowType;
    /**
     * 本月gmv 字符
     */
    private String totalGmv;
    /**
     * 本月gmv 数字
     */
    private BigDecimal thisMonthGmv;
    /**
     * 本月SPU数
     */
    private Integer thisMonthSpu;
    /**
     * 客户详情页面权限控制,0简略,1详细
     */
    private Integer roleTag;

    /**
     * r值
     */
    private String rValue;

    /**
     * f值
     */
    private String fValue;

    /**
     * m值
     */
    private String mValue;

    /**
     * 未登录天数
     */
    private Integer daysNotLoggedIn;

    /**
     * 生命周期
     */
    private String lifecycle;

    /**
     * 是否添加官微:0:是;1:否；
     */
    private Integer officialWechatFlag;
    /**
     *  是否添加销微:0:是;1:否；
     */
    private Integer bdWechatFlag;

    /**
     * 运营状态:正常(0),倒闭(1)，待提交核验（2），待核验（3），核验拒绝（4）
     */
    private Integer operateStatus;

    /**
     * 客户价值标签
     */
    private String valueLabel;

    /**
     * 高价值标签
     */
    private String highValueLabel;

    /**
     * 绩效二期高价值客户标签
     */
    private String highValueLabelV2;

    public Integer getOperateStatus() {
        return operateStatus;
    }

    public void setOperateStatus(Integer operateStatus) {
        this.operateStatus = operateStatus;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }
}
