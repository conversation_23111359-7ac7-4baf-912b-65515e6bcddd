package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.FollowUpRelationEnum;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 重点客户
 *
 * <AUTHOR>
 * @date 2022/6/14 1:44
 */
@Data
public class KeyCustomerVO {
    /**
     * 店铺名称
     */
    private String mid;
    /**
     * 店铺名称
     */
    private String mname;
    /**
     * 规格
     */
    private String size;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 地区
     */
    private String area;
    /**
     * 城市
     */
    private String city;
    /**
     * 最近下单时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime lastOrderTime;
    /**
     * 注册时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime registerTime;
    /**
     * 关注人bdID
     */
    private Integer careBdId;
    /**
     * 所归属bdID
     */
    private Integer bdId;
    /**
     * BD实际名称
     */
    private String adminRealName;
    /**
     * 关注BD实际名称
     */
    private String careAdminRealName;
    /**
     * @see FollowUpRelationEnum.DangerDayRule
     */
    private String dangerDayRule;
    /**
     * 未拜访天数
     */
    private Integer notVisited;
    /**
     * 未下单 天
     */
    private Integer daysWithoutOrder;
    /**
     * 已注册 天
     */
    private Long registerDay;

    /**
     * 上次下单 天
     */
    private Long lastOrderDay;

    private Long dangerDay;

    private String mId;

    private String mainType;

    public String getmId() {
        return mid;
    }

    public Long getRegisterDay() {
        if (registerTime == null) {
            return 0L;
        }
        return Duration.between(registerTime, LocalDateTime.now()).toDays();
    }

    public Long getLastOrderDay() {
        if (lastOrderTime == null) {
            return 0L;
        }
        return Duration.between(lastOrderTime, LocalDateTime.now()).toDays();
    }

    public String getDangerDayRule() {
        int notFollow = getNotVisited() == null?0:getNotVisited();
        int notOrder = getDaysWithoutOrder() == null?0:getDaysWithoutOrder() ;
        if (notFollow <= 15 && notOrder <= 30) {
           return FollowUpRelationEnum.DangerDayRule.RULE_ONE.getValue();
        } else if (notFollow <= 15) {
            return "15天未拜访";
        } else if (notOrder <= 30) {
            return "30天未下单";
        }
        return FollowUpRelationEnum.DangerDayRule.RULE_TWO.getValue();
    }
}
