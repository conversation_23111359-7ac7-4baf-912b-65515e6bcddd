package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 门店拜访潜力信息
 * 
 * <AUTHOR>
 */
@Data
public class MerchantVisitPotentialVO {

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 联系人地址id
     */
    private Long contactId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 位置信息
     */
    private PoiVO poi;

    /**
     * 是否系统推荐门店
     */
    private Boolean systemRecommendMerchant;

    /**
     * 生命周期
     */
    private String lifecycle;

    /**
     * 高价值客户标签
     */
    private String highValueLabel;

    /**
     * 门店潜力值
     */
    private BigDecimal merchantPotentialValue;

    /**
     * 门店潜力值描述/拜访价值，高/中/低
     */
    private String merchantPotentialValueDesc;

    /**
     * 门店下单状态，0：拜访日未下单，1：拜访日已下单
     */
    private Integer merchantOrderStatus;

    /**
     * 拜访指标列表
     */
    private List<MerchantVisitIndicatorVO> visitIndicators;

    @Data
    public static class MerchantVisitIndicatorVO {
        /**
         * 拜访目标名称
         */
        private String targetName;
        /**
         * 拜访指标优先级
         */
        private Integer indicatorPriority;
        /**
         * 拜访指标潜力值
         */
        private BigDecimal indicatorPotentialValue;

        /**
         * 拜访潜力值描述/拜访价值，高/中/低
         */
        private String indicatorPotentialValueDesc;

        /**
         * 拜访指标当前值
         */
        private BigDecimal indicatorCurrentValue;

        /**
         * 拜访指标是否完成
         */
        private Boolean indicatorCompleted;

        /**
         * 门店高价值详情，指标类型为高价值客户数时才会有数据
         */
        private MerchantHighValueDetailVO merchantHighValueDetail;
        
    }
}
