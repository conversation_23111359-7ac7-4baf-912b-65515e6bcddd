package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;

@Data
public class BdVisitConfigVO {

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 线下拜访数量, 大于1，小于200
     */
    private Integer visitOfflineCount;

    /**
     * 线上拜访数量, 大于1，小于200
     */
    private Integer visitOnlineCount;

    /**
     * 交通方式, 1-步行, 2-骑行 3-驾车，4-公交，5-地铁，6-电动车
     */
    private Integer trafficType;
}
