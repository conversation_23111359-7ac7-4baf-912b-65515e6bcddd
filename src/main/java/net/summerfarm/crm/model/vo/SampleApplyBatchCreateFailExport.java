package net.summerfarm.crm.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/5/19 17:26
 * @PackageName:net.summerfarm.crm.model.vo
 * @ClassName: SampleApplyBatchCreateFailVO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class SampleApplyBatchCreateFailExport implements Serializable {

    /**
     * 门店ID
     */
    @ColumnWidth(10)
    @ExcelProperty(value = "mId", index = 0)
    private Long mId;

    /**
     * 失败原因
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "失败原因", index = 1)
    private String failReason;
}
