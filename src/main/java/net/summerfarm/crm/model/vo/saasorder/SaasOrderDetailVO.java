package net.summerfarm.crm.model.vo.saasorder;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.model.vo.OrderOverviewVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SaasOrderDetailVO extends OrderOverviewVO {

    /**
     * 子账号联系人
     */
    private String subAccountContact;

    /**
     * 子账号联系电话
     */
    private String subAccountPhone;

    /**
     * 收货人
     */
    private String orderAddressContactName;

    /**
     * 订单收货地址-省
     */
    private String orderAddressProvince;

    /**
     * 订单收货地址-市
     */
    private String orderAddressCity;

    /**
     * 订单收货地址-区
     */
    private String orderAddressArea;

    /**
     * 订单收货地址-详细地址
     */
    private String orderAddress;

    private LocalDateTime payTime;

    private BigDecimal deliveryFee;

    private List<SaasOrderItemVO> orderItems;
}
