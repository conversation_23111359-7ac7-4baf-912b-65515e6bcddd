package net.summerfarm.crm.model.vo.salesperformance.pbscore;

import lombok.Data;

@Data
public class ScorePerformanceDetailVO {

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 上月利润积分
     */
    private String lastCateGroupScore;

    /**
     * 本月截止昨晚履约利润积分
     */
    private String cateGroupScore;

    /**
     * 今日待履约利润积分
     */
    private String cateGroupScoreToday;

    /**
     * 今日交易本月待履约利润积分
     */
    private String orderGroupScoreToday;

    /**
     * 其余待履约利润积分
     */
    private String otherGroupScoreToday;

    /**
     * 本月预计总履约利润积分
     */
    private String totalCateGroupScore;

    /**
     * 设置上月利润积分
     */
    public void setLastCateGroupScore(Double lastCateGroupScore) {
        this.lastCateGroupScore = (lastCateGroupScore != null) ? String.format("%.2f", lastCateGroupScore) : null;
    }

    /**
     * 设置本月截止昨晚履约利润积分
     */
    public void setCateGroupScore(Double cateGroupScore) {
        this.cateGroupScore = (cateGroupScore != null) ? String.format("%.2f", cateGroupScore) : null;
    }

    /**
     * 设置今日待履约利润积分
     */
    public void setCateGroupScoreToday(Double cateGroupScoreToday) {
        this.cateGroupScoreToday = (cateGroupScoreToday != null) ? String.format("%.2f", cateGroupScoreToday) : null;
    }

    /**
     * 设置今日交易本月待履约利润积分
     */
    public void setOrderGroupScoreToday(Double orderGroupScoreToday) {
        this.orderGroupScoreToday = (orderGroupScoreToday != null) ? String.format("%.2f", orderGroupScoreToday) : null;
    }

    /**
     * 设置其余待履约利润积分
     */
    public void setOtherGroupScoreToday(Double otherGroupScoreToday) {
        this.otherGroupScoreToday = (otherGroupScoreToday != null) ? String.format("%.2f", otherGroupScoreToday) : null;
    }

    /**
     * 设置本月预计总履约利润积分
     */
    public void setTotalCateGroupScore(Double totalCateGroupScore) {
        this.totalCateGroupScore = (totalCateGroupScore != null) ? String.format("%.2f", totalCateGroupScore) : null;
    }

    // ---------------------------------- 下面的setter方法需要保留，避免CRMDashboardModule取值时报错 --------------------------

    public void setLastCateGroupScore(String lastCateGroupScore) {
        this.lastCateGroupScore = lastCateGroupScore;
    }

    public void setCateGroupScore(String cateGroupScore) {
        this.cateGroupScore = cateGroupScore;
    }

    public void setCateGroupScoreToday(String cateGroupScoreToday) {
        this.cateGroupScoreToday = cateGroupScoreToday;
    }

    public void setOrderGroupScoreToday(String orderGroupScoreToday) {
        this.orderGroupScoreToday = orderGroupScoreToday;
    }

    public void setOtherGroupScoreToday(String otherGroupScoreToday) {
        this.otherGroupScoreToday = otherGroupScoreToday;
    }

    public void setTotalCateGroupScore(String totalCateGroupScore) {
        this.totalCateGroupScore = totalCateGroupScore;
    }
}
