package net.summerfarm.crm.model.vo.salesperformance.categorypromotion;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 品类推广详情VO
 * 客户维度汇总
 */
@Data
public class CategoryPromotionDetailVO {

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 履约件数
     */
    private Double fulfillmentCount;

    /**
     * 交易件数
     */
    private Double transactionCount;

    /**
     * 单件奖励
     */
    private BigDecimal rewardPerItem;
}
