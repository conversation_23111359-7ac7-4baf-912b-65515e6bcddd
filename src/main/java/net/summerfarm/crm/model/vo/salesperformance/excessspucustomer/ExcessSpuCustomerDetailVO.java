package net.summerfarm.crm.model.vo.salesperformance.excessspucustomer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 超标SPU客户详情VO
 * 客户维度汇总
 */
@Data
public class ExcessSpuCustomerDetailVO {

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * SPU数
     */
    private Integer excessSpuCount;
}
