package net.summerfarm.crm.model.vo.riskMerchant;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 18:06
 */
@Data
public class RiskMerchantDetailVO extends RiskMerchantVO{
    /**
     * 风控门店 id
     */
    private Integer riskMerchantId;

    /**
     * 状态 0未审核 1审核通过 2审核不通过
     */
    private Integer status;

    /**
     * 触发场景:0:新注册;1:存量动销;
     */
    private String triggerOccasions;

    /**
     * 命中分类:0:疑似重复;1:疑似虚假;2:疑似换壳;
     */
    private Integer triggerClassification;

    /**
     * 相似门店信息
     */
    private List<RiskMerchantVO> similarRiskMerchantList;

    /**
     * 收货地址列表
     */
    private List<DeliveryAddressVO> deliveryAddressList;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 处理时间
     */
    private LocalDateTime updateTime;

    /**
     * 处理人
     */
    private String auditorName;

    /**
     * 门头照 OCR 结果
     */
    private String doorPicOcr;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * poi
     */
    private String poi;
}
