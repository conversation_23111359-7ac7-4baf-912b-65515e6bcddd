package net.summerfarm.crm.model.vo.wechat;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class WeChatCustomerRemarkVo {
    /**
     *      userid	是	企业成员的userid
     */
    private String userid;
    /**
     * 外部联系人userid
     */
    @JSONField(name = "external_userid")
    private String externalUserid;
    /**
     * 此用户对外部联系人的备注，最多20个字符
     */
    private String remark;
    /**
     * 此用户对外部联系人的描述，最多150个字符
     */
    private String description;
    /**
     * remark_mobiles
     */
    @JSONField(name = "remark_mobiles")
    private List<String> remarkMobiles;
}
