package net.summerfarm.crm.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description crm_clue
 * @date 2023-05-17
 */
@Data
public class SaasClueClueVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id 新增不传递
     */
    private Long id;

    /**
     * 客户来源 0 鲜沐大客户 1新增品牌
     */
    private Integer customerSource;

    /**
     * 客户名称 可以做唯一索引
     */
    private String customerName;

    /**
     * kp名称
     */
    private String kp;

    /**
     * 手机电话
     */
    private String phone;

    /**
     * 部门
     */
    private String dept;

    /**
     * 职务
     */
    private String post;

    /**
     * 线索状态
     */
    private String clueStatus;

    /**
     * 线索来源
     */
    private String clueSource;

    /**
     * 意向业务
     */
    private String wantBusiness;

    /**
     * 总部所在地code码
     */
    private Integer areaCode;

    /**
     * 地区名称
     */
    private String areaCodeName;

    /**
     * 需求情况
     */
    private String purposeRemark;

    /**
     * 经营类型
     */
    private String businessType;

    /**
     * 门店类型
     */
    private String shopType;

    /**
     * 门店规模
     */
    private String shopSize;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 店铺系统
     */
    private String shopSystem;

    /**
     * 总部系统
     */
    private String headSystem;

    /**
     * 物料备注
     */
    private String sendRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * mid 关联大客户的时候传
     */
    private Long bId;


    /**
     * 协助bdid
     */
    private Integer assistBdId;


    public SaasClueClueVO() {
    }
}