package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;

import java.time.LocalDate;

/**
 * 销售拜访计划汇总信息
 *
 * <AUTHOR>
 */
@Data
public class BdVisitPlanSummaryVO {

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 拜访日期
     */
    private LocalDate visitDate;

    /**
     * 线上客户总数量
     */
    private Integer onlineTotalCount;

    /**
     * 线上已拜访客户数量
     */
    private Integer onlineVisitedCount;

    /**
     * 线下客户总数量
     */
    private Integer offlineTotalCount;

    /**
     * 线下已拜访客户数量
     */
    private Integer offlineVisitedCount;

}
