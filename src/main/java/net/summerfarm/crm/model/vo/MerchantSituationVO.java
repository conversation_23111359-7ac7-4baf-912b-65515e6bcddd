package net.summerfarm.crm.model.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.common.util.DataMappingUtil;
import net.summerfarm.crm.model.domain.MerchantSituation;
import lombok.Data;
import net.summerfarm.crm.model.dto.MerchantStoreAndExtendDTO;
import net.summerfarm.pojo.DO.Area;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.ADMIN;
import static net.xianmu.usercenter.client.merchant.enums.RegionalOrganizationEnums.Size.MERCHANT;

/**
 * <AUTHOR> ct
 * create at:  2019/8/2  2:11 PM
 */
@Data
public class MerchantSituationVO extends MerchantSituation {

    /**
    * 券金额
    */
    @NotNull
    private BigDecimal couponAmount;

    /**
    * 券门槛
    */
    @NotNull
    private BigDecimal threshold;

    /**
    * 申请单备注
    */
    private String couponRemake;

    /**
    * 客户名称
    */
    private String mname;

    /**
    * 客户类型
    */
    private String size;

    /**
    * 客户等级
    */
    private Integer grade;
    /**
    * 城市编号
    */
    private Integer areaNo;

    /**
    * 城市名称
    */
    private String areaName;
    /**
     * 月活券类型:0客情,1月活
     */
    private Integer monthLivingCoupon;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime creatTime;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;
    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券分组
     */
    private Integer grouping;
    /**
     * 卡券id
     */
    private Integer couponId;

    /**
     * 关联卡券sku信息
     */
    private String sku;

    /**
     * 2:普通订单,4:仅省心送
     */
    private Integer activityScope;

    /**
     * 卡券适用类型str:普通订单/省心送
     */
    private String activityScopeStr;


    /**
     * 根据门店数据补充客情数据
     * @param sourceList
     * @param targetList
     * @param <T>
     * @param <U>
     */
    public static void wrapMerchantSituationVo(List<MerchantStoreAndExtendDTO> sourceList, List<MerchantSituationVO> targetList) {
        if(CollUtil.isEmpty(sourceList) || CollUtil.isEmpty(targetList)) {
            return;
        }
        Map<Long, MerchantStoreAndExtendDTO> collect = sourceList.stream().collect(Collectors.toMap(MerchantStoreAndExtendDTO::getMId, Function.identity()));
        for (MerchantSituationVO vo : targetList) {
            if (!collect.containsKey(vo.getMerchantId())) {
                continue;
            }
            MerchantStoreAndExtendDTO dto = collect.get(vo.getMerchantId());
            vo.setMname(dto.getStoreName());
            vo.setAreaNo(dto.getAreaNo());
            vo.setGrade(dto.getGrade());
            vo.setSize(ObjectUtil.equal(dto.getSize(), ADMIN.getCode()) ? ADMIN.getDesc() : MERCHANT.getDesc());
        }
    }
}
