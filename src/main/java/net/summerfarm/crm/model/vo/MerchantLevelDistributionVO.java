package net.summerfarm.crm.model.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 商户等级分布
 * @date 2022/6/26 15:31
 */
@Data
@NoArgsConstructor
public class MerchantLevelDistributionVO {

    /**
     * 等级系数
     */
    private BigDecimal levelProportion;


    /**
     * 等级系数所占数量
     */
    private Integer levelProportionNum;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 商户名
     */
    private String merchantName;

    public MerchantLevelDistributionVO(BigDecimal levelProportion, Integer levelProportionNum) {
        this.levelProportion = levelProportion;
        this.levelProportionNum = levelProportionNum;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}
