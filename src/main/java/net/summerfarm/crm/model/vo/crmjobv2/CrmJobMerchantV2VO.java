package net.summerfarm.crm.model.vo.crmjobv2;

import lombok.Data;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailV2VO;

import java.math.BigDecimal;
import java.util.List;

/**
 * CRM任务门店V2VO
 */
@Data
public class CrmJobMerchantV2VO {

    /**
     * 任务Id FK crm_job
     */
    private Long jobId;


    /**
     * 门店id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 电话
     */
    private String phone;

    /**
     * 是否添加官微:0:是;1:否；
     */
    private Integer officialWechatFlag;

    /**
     * 是否添加销微:0:是;1:否；
     */
    private Integer bdWechatFlag;

    /**
     * 销售id
     */
    private Long bdId;

    /**
     * 销售名字
     */
    private String bdName;

    /**
     * 任务完成状态
     */
    private Integer status;

    /**
     * 累计实付金额
     */
    private BigDecimal realTotalAmt;

    /**
     * 已达成数量
     */
    private Integer fulfilledQty;

    /**
     * 任务下客户的标签
     */
    private String jobMerchantLabel;

    /**
     * 高价值客户详情
     */
    private HighValueCustomerDetailV2VO highValueCustomerDetail;

    /**
     * item列表
     */
    private List<CrmJobMerchantItemV2VO> items;
}
