package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;

import java.math.BigDecimal;

/**
 * CRM BD 今日数据页面VO
 */
@Data
public class CrmTodayDataVO {

    /**
     * 交易实付GMV(下单gmv)
     */
    private BigDecimal realTotalGmv;

    /**
     * 履约实付GMV(配送gmv)
     */
    private BigDecimal dlvRealTotalGmv;

    /**
     * 交易客户数(下单客户数)
     */
    private Long custCnt;

    /**
     * 全品类交易实付GMV(代售gmv)
     */
    private BigDecimal categoriesRealTotalGmv;

    /**
     * 鲜果交易实付GMV(鲜果gmv)
     */
    private BigDecimal fruitRealTotalGmv;

    /**
     * 乳制品（不含AT）交易实付GMV(乳制品gmv非AT)
     */
    private BigDecimal dairyRealTotalGmv;

    /**
     * 其他交易实付GMV(非乳制品gmv)
     */
    private BigDecimal otherRealTotalGmv;

    /**
     * 安佳铁塔交易实付GMV(奖励sku)
     */
    private BigDecimal anchoRealTotalGmv;

    /**
     * 拜访数(新增拜访数)
     */
    private Long ordinaryNum;

}
