package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用于模板化配置CRM端的BD销售数据看板的模块
 */
@Data
public class CRMDashboardModule implements Serializable {

    String title;

    String description;

    /**
     * id, 用于前端展示时的唯一标识
     * 也用于定位domain object的字段名,以用来取值
     */
    String id;

    /**
     * 值
     * 一些模版可能有个值,比如今日数据的总GMV或者area_id之类的
     * 可以用来供前端进行页面跳转以查看详情
     */
    String value;

    List<CRMDashboardItem> items;
}
