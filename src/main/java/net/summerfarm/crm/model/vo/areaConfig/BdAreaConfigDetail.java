package net.summerfarm.crm.model.vo.areaConfig;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import net.summerfarm.crm.model.vo.TableArea;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/22 15:17
 */
@Data
public class BdAreaConfigDetail {
    /**
     * 主管组织 id
     */
    private Integer bdOrgId;
    /**
     * 城市负责人 id
     */
    private Integer cityAdminId;
    /**
     * 城市负责人名称
     */
    private String cityAdminName;
    /**
     * 销售区域id
     */
    private Integer salesAreaId;
    /**
     * 销售区域名称
     */
    private String salesAreaName;
    /**
     * 主管负责城市列表
     */
    private List<BdSalesCityVo> salesCityList;
    /**
     * 省市区级联
     */
    private List<TableArea> regionTable;;
    /**
     * 团队销售人员
     */
    private List<CrmBdOrg> bdList;
}
