package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;
import java.time.LocalDate;

/**
 * 销售每日目标明细信息
 *
 * <AUTHOR>
 */
@Data
public class BdDailyTargetDetailVO {

    /**
     * 销售ID
     */
    private Integer bdId;

    /**
     * 目标日期
     */
    private LocalDate targetDate;

    /**
     * 目标类型，1：拉新客户数，2：月活客户数，3：高价值客户数，4：平台总目标，5：品类目标，6：sku目标，7：spu目标
     */
    private Integer targetType;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 指标优先级，1,2,3,4,5，越小越高
     */
    private Integer priority;

    /**
     * 业务类型，0：交易，1：履约
     */
    private Integer businessType;

    /**
     * 指标类型，1：GMV，2：客户数，3：件数
     */
    private Integer indicatorType;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * SKU编码
     */
    private String sku;

    /**
     * SPU编码
     */
    private String spu;

    /**
     * 指标期望值
     */
    private String indicatorExpectedValue;

    /**
     * 指标当前值
     */
    private String indicatorCurrentValue;

    /**
     * 指标状态，0：未完成，1：已完成
     */
    private Integer indicatorStatus;
}
