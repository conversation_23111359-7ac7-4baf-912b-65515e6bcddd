package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-08-13
 */
@Data
public class PrivateSeaVO {

    /**
     * mId
     */
    private Long mId;
    /**
     * 客户名称
     */
    private String mname;
    /**
     * 城市编号
     */
    private Integer areaNo;
    /**
     * 城市名称
     */
    private String areaName;
    /**
     * 会员等级
     */
    private Integer grade;
    /**
     * 生命周期
     */
    private String lifecycle;
    /**
     * 上月GMV
     */
    private BigDecimal lastMonthGmv;
    /**
     * 本月GMV
     */
    private BigDecimal thisMonthGmv;
    /**
     * 今日GMV
     */
    private BigDecimal todayGmv;
    /**
     * 今日配送GMV
     */
    private BigDecimal todayDeliveryGmv;
    /**
     * 今日spu数
     */
    private Integer todayDeliverySpu;
    /**
     * 上月下单品类数
     */
    private Integer lastMonthSkuCount;
    /**
     * 本月下单品类数
     */
    private Integer thisMonthSkuCount;
    /**
     * 倒计时
     */
    private Integer dangerDay;
    /**
     * 释放时间
     */
    private String releaseTime;
    /**
     * 释放保护原因
     */
    private String protectReason;
    /**
     * 当月是否下单
     */
    private Boolean orderCurrentMonth;
    /**
     * 商户等级
     */
    private String size;
    /**
     * BD
     */
    private Integer adminId;
    /**
     * 最近下单时间
     */
    private LocalDateTime lastOrderTime;
    /**
     * 未下单天数
     */
    private Integer notOrder;
    /**
     * 未拜访天数
     */
    private Integer notFollow;
    /**
     * 下单预警标识
     */
    private Boolean orderCycleWarn;
    /**
     * 上月客单价
     */
    private BigDecimal lastMonthDeliveryUnitPrice;
    /**
     * 本月客单价
     */
    private BigDecimal thisMonthDeliveryUnitPrice;
    /**
     * 本月配送gmv
     */
    private BigDecimal distributionGmv;
    /**
     * 上月配送gmv
     */
    private BigDecimal lastDistributionGmv;
    /**
     * 配送次数
     */
    private Integer distributionAmount;
    /**
     * 核心客户标识
     */
    private Integer coreMerchantTag;
    /**
     * 商户注册时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime registerTime;

    /**
     * 30天订购spu
     */
    private Integer thirtyDaysOrderSpu;

    /**
     * 30-60天订购spu
     */
    private Integer thirtySixtyDaysOrderSpu;

    /**
     * 七天水果GMV
     */
    private BigDecimal sevenDaysFruitGmv;

    /**
     * 7天乳制品GMV
     */
    private BigDecimal sevenDaysDairyGmv;

    /**
     * 七天自营品牌GMV
     */
    private BigDecimal sevenDaysBrandGmv;

    /**
     * 30天水果GMV
     */
    private BigDecimal thirtyDaysFruitGmv;

    /**
     * 30天乳制品GMV
     */
    private BigDecimal thirtyDaysDairyGmv;

    /**
     * 30天自营品牌GMV
     */
    private BigDecimal thirtyDaysBrandGmv;

    /**
     * 浏览历史记录
     */
    private String browsingHistory;
    /**
     * 客户详情页面权限控制,0简略,1详细
     */
    private Integer roleTag;
    /**
     * r值
     */
    private String rValue;

    /**
     * f值
     */
    private String fValue;

    /**
     * m值
     */
    private String mValue;

    /**
     * 未登录天数
     */
    private Integer daysNotLoggedIn;

    /**
     * 是否添加官微:0:是;1:否；
     */
    private Integer officialWechatFlag;

    /**
     *  是否添加销微:0:是;1:否；
     */
    private Integer bdWechatFlag;

    /**
     * 新年营业状态标签
     */
    private List<String> newYearBusinessTag;

    /**
     * 客户价值标签
     */
    private String valueLabel;

    /**
     * 业务线. 0: 鲜沐. 1: pop
     */
    private Integer businessLine;

    /**
     * 高价值客户标签
     */
    private String highValueLabel;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }
}
