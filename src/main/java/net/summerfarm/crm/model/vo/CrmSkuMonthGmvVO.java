package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmSkuMonthGmv;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/1 18:24
 */
@Data
public class CrmSkuMonthGmvVO extends CrmSkuMonthGmv {

    /**
     * 商品id
     */
    private Long pdId;

    /**
     * sku
     */
    private String sku;

    /**
     * spu
     */
    private String spu;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 商品图片路径
     */
    private String picturePath;

    /**
     * 商品规格
     */
    private String weight;

    /**
     * 0 上月未下单, 1 本月未下单, 2 上月本月均下单
     */
    private Integer orderTag;
    /**
     * 下单gmv环比
     */
    private BigDecimal gmvRingRatio;
    /**
     * 下单销量环比
     */
    private BigDecimal salesVolumeRingRatio;
    /**
     * 下单客户数环比
     */
    private BigDecimal merchantNumRingRatio;


}
