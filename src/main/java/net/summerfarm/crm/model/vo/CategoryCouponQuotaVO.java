package net.summerfarm.crm.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.model.domain.CategoryCouponQuota;

import java.math.BigDecimal;
import java.util.List;

/**
 * 品类券额度vo
 *
 * <AUTHOR>
 * @Date 2023/3/1 10:17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CategoryCouponQuotaVO extends CategoryCouponQuota {
    /**
     * 已申请额度
     */
    private BigDecimal appliedQuota;
    /**
     * 已使用额度
     */
    private BigDecimal usedQuota;
    /**
     * 累计奖励额度
     */
    private BigDecimal awardedQuota;
    /**
     * 费比
     */
    private List<CategoryCouponFeeRateVO> feeRateList;
}
