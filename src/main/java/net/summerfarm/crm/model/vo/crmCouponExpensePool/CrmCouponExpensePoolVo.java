package net.summerfarm.crm.model.vo.crmCouponExpensePool;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmBdCity;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;
import net.summerfarm.crm.model.vo.TableArea;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CrmCouponExpensePoolVo implements Serializable {

    private static final long serialVersionUID = -6072226146375295851L;

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 上传url
     */
    private String url;

    /**
     * 费用名称
     */
    private String name;
    /**
     * 状态 0正常 1失效
     */
    private Byte status;

    /**
     * 有效期 开始
     */
    private LocalDateTime startDate;

    /**
     * 有效期 结束
     */
    private LocalDateTime endDate;
    /**
     * 自动审批状态 0关闭 1开启
     */
    private Byte autoApprove;
    /**
     * 0 不限 1按照品类 2按照sku
     */
    private Byte productRange;
    /**
     * 费比限制
     */
    private Integer costLimit;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * skus or 种类s    2乳制品 4水果 10其他
     */
    private List<String> limits;

    private Long poolId;

    private Integer costDisabled;

    private Integer dateDisabled;
}
