package net.summerfarm.crm.model.vo.salesperformance.pbscore;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class PbPerformanceSummaryVO {

    /**
     * BD_ID
     */
    private Long bdId;

    /**
     * 归属BD名称
     */
    private String bdName;

    /**
     * 是否M1
     */
    private boolean isCityManager;

    /**
     * PB标品得分（M1才会有数据）
     */
    private String pbCommRate;

    /**
     * PB标品增长率（M1才会有数据）
     */
    private String pbIncreaseRate;

    /**
     * 设置PB标品得分
     */
    public void setPbCommRate(Double pbCommRate) {
        this.pbCommRate = (pbCommRate != null) ? String.format("%.2f", pbCommRate) : null;
    }

    /**
     * 设置PB标品增长率（百分比形式）
     */
    public void setPbIncreaseRate(Double pbIncreaseRate) {
        this.pbIncreaseRate = (pbIncreaseRate != null) ? String.format("%.2f%%", pbIncreaseRate * 100) : null;
    }

    /**
     * PBGMV基数（M1才会有数据）
     */
    private BigDecimal pbGmvBase;

    /**
     * PB月累计履约客户数
     */
    private Integer custCnt;

    /**
     * 上月PB履约GMV
     */
    private BigDecimal lastDlvRealAmtPb;

    /**
     * PB本月截止昨晚履约GMV
     */
    private BigDecimal dlvRealAmtPb;

    /**
     * PB今日待履约GMV
     */
    private BigDecimal dlvRealAmtTodayPb;

    /**
     * PB今日交易本月待履约GMV
     */
    private BigDecimal dlvOrderAmtTodayPb;

    /**
     * PB其余待履约GMV
     */
    private BigDecimal dlvOtherAmtTodayPb;

    /**
     * PB本月预计总履约GMV
     */
    private BigDecimal totalCateGroupAmtPb;

    // ---------------------------------- 下面的setter方法需要保留，避免CRMDashboardModule取值时报错 --------------------------

    public void setPbCommRate(String pbCommRate) {
        this.pbCommRate = pbCommRate;
    }

    public void setPbIncreaseRate(String pbIncreaseRate) {
        this.pbIncreaseRate = pbIncreaseRate;
    }
}
