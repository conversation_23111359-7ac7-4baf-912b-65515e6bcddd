package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 用于模板化配置CRM端的BD销售数据看板的模块里的每个数据项
 */
@Data
public class CRMDashboardItem {

    /**
     * id, 用于前端展示时的唯一标识
     * 也用于定位domain object的字段名,以用来取值
     */
    String id;

    /**
     * 描述.
     * 注: 特使样式, value1/value2格式的组件, 由于每个item只能取一个值,与前端规定,
     * 把两个相邻item的title设置为一样,由前端拼接成一个组件
     */
    String title;

    /**
     * 值
     */
    String value;

    /**
     * 环比
     */
    BigDecimal changeRate;

    /**
     * 增长率字段id, 用于定位domain object的字段名,以用来取值
     */
    String changeRateFieldId;

    /**
     * 单位
     */
    String unit;

    /**
     * 跳转页面的链接, 由前端提供
     */
    String linkUrl;

    /**
     * 前端排版样式. 用于控制前端展示时的布局. 默认值为3
     * 例如, 1,表示该组件占一整行; 2,表示占一半的宽度, 以此类推
     */
    Integer layoutHint = 3;
}
