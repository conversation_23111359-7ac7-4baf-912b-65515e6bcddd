package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.domain.BdExt;
import net.summerfarm.pojo.DO.Area;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Data
public class BdExtVO extends BdExt {

    /**
     * 城市负责人id
     */
    private Integer manageAdminId;
    /**
     * 区域负责人id
     */
    private Integer parentAdminId;
    /**
     * 总负责人id
     */
    private Integer departmentAdminId;
    /**
     * 城市负责人名称
     */
    private String manageAdminName;
    /**
     * 区域负责人名称
     */
    private String parentAdminName;
    /**
     * 总负责人名称
     */
    private String departmentAdminName;
    /**
     * 姓名
     */
    private String realname;
    /**
     * 销售区域no
     */
    private Integer zoneNo;
    /**
     * 销售区域名称
     */
    private String zoneName;
    /**
     * 销售区域下的运营区域
     */
    List<Area> childrenArea;
    /**
     * 运营区域编号
     */
    private Integer largeAreaNo;

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        if (!super.equals(o)) {return false;}

        BdExtVO bdExtVO = (BdExtVO) o;

        if (getManageAdminId() != null ? !getManageAdminId().equals(bdExtVO.getManageAdminId()) : bdExtVO.getManageAdminId() != null){
            return false;}
        if (getParentAdminId() != null ? !getParentAdminId().equals(bdExtVO.getParentAdminId()) : bdExtVO.getParentAdminId() != null){
            return false;}
        if (getDepartmentAdminId() != null ? !getDepartmentAdminId().equals(bdExtVO.getDepartmentAdminId()) : bdExtVO.getDepartmentAdminId() != null){
            return false;}
        if (getManageAdminName() != null ? !getManageAdminName().equals(bdExtVO.getManageAdminName()) : bdExtVO.getManageAdminName() != null){
            return false;}
        if (getParentAdminName() != null ? !getParentAdminName().equals(bdExtVO.getParentAdminName()) : bdExtVO.getParentAdminName() != null){
            return false;}
        if (getDepartmentAdminName() != null ? !getDepartmentAdminName().equals(bdExtVO.getDepartmentAdminName()) : bdExtVO.getDepartmentAdminName() != null){
            return false;}
        if (getRealname() != null ? !getRealname().equals(bdExtVO.getRealname()) : bdExtVO.getRealname() != null){
            return false;}
        if (getZoneNo() != null ? !getZoneNo().equals(bdExtVO.getZoneNo()) : bdExtVO.getZoneNo() != null) {
            return false;}
        if (getZoneName() != null ? !getZoneName().equals(bdExtVO.getZoneName()) : bdExtVO.getZoneName() != null){
            return false;}
        return getChildrenArea() != null ? getChildrenArea().equals(bdExtVO.getChildrenArea()) : bdExtVO.getChildrenArea() == null;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + (getManageAdminId() != null ? getManageAdminId().hashCode() : 0);
        result = 31 * result + (getParentAdminId() != null ? getParentAdminId().hashCode() : 0);
        result = 31 * result + (getDepartmentAdminId() != null ? getDepartmentAdminId().hashCode() : 0);
        result = 31 * result + (getManageAdminName() != null ? getManageAdminName().hashCode() : 0);
        result = 31 * result + (getParentAdminName() != null ? getParentAdminName().hashCode() : 0);
        result = 31 * result + (getDepartmentAdminName() != null ? getDepartmentAdminName().hashCode() : 0);
        result = 31 * result + (getRealname() != null ? getRealname().hashCode() : 0);
        result = 31 * result + (getZoneNo() != null ? getZoneNo().hashCode() : 0);
        result = 31 * result + (getZoneName() != null ? getZoneName().hashCode() : 0);
        result = 31 * result + (getChildrenArea() != null ? getChildrenArea().hashCode() : 0);
        return result;
    }
}
