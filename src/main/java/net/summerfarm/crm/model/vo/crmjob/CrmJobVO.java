package net.summerfarm.crm.model.vo.crmjob;

import lombok.Data;
import net.summerfarm.crm.model.vo.crmjobv2.CrmJobMerchantItemV2VO;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CrmJobVO {

    /**
     * 任务id
     */
    private Long id;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务类型
     *
     * @see net.summerfarm.crm.enums.CrmJobEnum.Type
     */
    private Integer type;

    /**
     * 子任务类型
     *
     * @see net.summerfarm.crm.enums.CrmJobEnum.SubType
     */
    private Integer subType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建人admin_id
     */
    private Long creator;

    /**
     * 创建人adminName
     */
    private String creatorName;

    /**
     * 关联人群方式
     *
     * @see net.summerfarm.crm.enums.CrmJobEnum.MerchantSelectionType
     */
    private Integer merchantSelectionType;

    /**
     * 任务状态
     *
     * @see net.summerfarm.crm.enums.CrmJobEnum.Status
     */
    private Integer status;

    /**
     * 发券任务关联的优惠券id
     */
    private Integer couponId;

    /**
     * 任务品类列表
     * 是个二维数组
     */
    private List<List<Long>> categoryList;

    /**
     * 任务完成判定条件列表
     */
    private List<CrmJobCompletionCriteriaVO> completionCriteriaList;

}
