package net.summerfarm.crm.model.vo.weCom;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/26 15:24
 */
@Data
public class WeComGroupMsgListResp extends WeChatBaseResp {
    @JSONField(name = "group_msg_list")
    private List<GroupMsg> groupMsgList;

    @Data
    public class GroupMsg {
        /**
         * 消息idd
         */
        private String msgid;

        private String creator;
    }


    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static WeComGroupMsgListResp fromJson(String json) {
        return JSONUtil.toBean(json, WeComGroupMsgListResp.class);
    }

}
