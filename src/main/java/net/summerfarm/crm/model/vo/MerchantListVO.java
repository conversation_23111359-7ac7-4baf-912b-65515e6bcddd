package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.dto.LargeAreaDTO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe
 * @create 2022-08-24 11:09
 */
@Data
public class MerchantListVO {

    /**
     * 地址
     */
    private String address;
    /**
     * 区域
     */
    private String area;
    /**
     * 销售id
     */
    private Integer bdId;
    /**
     * 销售名称
     */
    private String bdName;
    /**
     * 城市
     */
    private String city;
    /**
     * 客户等级
     */
    private Integer grade;
    /**
     * 商户id
     */
    private Long mId;
    /**
     * 白名单标识
     */
    private Integer merchantWhiteListTag;
    /**
     * 省心送标识
     */
    private Integer timingFollowType;
    /**
     * 商户名
     */
    private String mname;
    /**
     * 商户类型
     */
    private String size;
    /**
     * 商户详情权限控制:0简略,1详细
     */
    private Integer roleTag;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 注册时间页面展示
     */
    private String registerTimeStr;

    /**
     * 注册时间距今
     */
    private String registerTimeToNowStr;

    /**
     * 运营区域
     */
    private Integer areaNo;

    /**
     * 行政区域
     */
    private LargeAreaDTO largeAreaDTO;

    /**
     * 警示等级：0(绿)，1(黄)，2(红)
     */
    private Integer warnIngSign;

    /**
     * 上月GMV
     */
    private BigDecimal lastMonthGmv;
    /**
     * 本月GMV
     */
    private BigDecimal thisMonthGmv;

    /**
     * 上月下单品类数
     */
    private Integer lastMonthSkuCount;
    /**
     * 本月下单品类数
     */
    private Integer thisMonthSkuCount;

    /**
     * r值
     */
    private String rValue;

    /**
     * f值
     */
    private String fValue;

    /**
     * m值
     */
    private String mValue;

    /**
     * 未登录天数
     */
    private Integer daysNotLoggedIn;

    /**
     * 生命周期
     */
    private String lifecycle;

    /**
     * 运营状态
     */
    private Integer operateStatus;

    /**
     * 客户价值标签
     */
    private String valueLabel;

    /**
     * 高价值标签
     */
    private String highValueLabel;

    /**
     * 绩效二期高价值客户标签
     */
    private String highValueLabelV2;

}
