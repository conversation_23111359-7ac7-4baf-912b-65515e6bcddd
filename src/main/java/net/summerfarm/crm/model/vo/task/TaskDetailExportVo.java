package net.summerfarm.crm.model.vo.task;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/10/8 18:51
 */
@Data
public class TaskDetailExportVo {
    @ColumnWidth(16)
    @ExcelProperty(value = "客户Id")
    private Integer mId;
    @ColumnWidth(16)
    @ExcelProperty(value = "客户名称")
    private String mname;
    @ColumnWidth(16)
    @ExcelProperty(value = "所在城市")
    private String city;
    @ColumnWidth(16)
    @ExcelProperty(value = "任务状态")
    private String result;
}
