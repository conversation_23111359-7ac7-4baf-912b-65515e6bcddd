package net.summerfarm.crm.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 大客户流转配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminTurningConfigVO {
    /**
     * bdId
     */
    private Long adminId;


    /**
     * mid
     */
    private Long mId;



    public AdminTurningConfigVO(Long mId) {
        this.mId = mId;
    }
}
