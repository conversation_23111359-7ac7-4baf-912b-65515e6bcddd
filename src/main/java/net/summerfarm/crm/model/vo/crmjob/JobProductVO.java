package net.summerfarm.crm.model.vo.crmjob;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobProductVO {

    /**
     * sku编码
     */
    private String sku;

    /**
     * 商品id
     */
    private Long pdId;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 规格
     */
    private String weight;

    /**
     * 区域编号
     */
    private Integer areaNo;

    /**
     * 销售价
     */
    private BigDecimal price;

    /**
     * 特价金额
     */
    private BigDecimal activityPrice;
}
