package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.enums.FollowUpRelationEnum;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.domain.FollowUpRecord;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.domain.MerchantCluePool;
import net.summerfarm.crm.model.vo.saledata.CRMDashboardModule;
import org.springframework.format.annotation.DateTimeFormat;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2016/8/24
 */

@Data
public class MerchantVO extends Merchant {
    /**
     * BD实际名称
     */
    private String adminRealname;
    /**
     * 跟进者
     */
    private String adminName;
    /**
     * 城市名称
     */
    private String areaName;
    /**
     * 1代表多地址 2代表更换账号审核
     */
    private Integer chat;
    /**
     * 邀请者手机号
     */
    private String inviterPhone;
    /**
     * 跟进记录
     */
    private List<FollowUpRecord> followUpRecords;
    /**
     * 联系人列表
     */
    private List<Contact> contacts;
    /**
     * state
     */
    private Integer state;
    /**
     * 商户类型
     */
    private String merchantType;
    /**
     * 0无，1又购物，2 无
     */
    private Integer followType;
    /**
     * 加入公海池的时间，没有释放 按照审核时间
     */
    private LocalDateTime reassignTime;
    /**
     * 释放原因
     */
    private String reason;
    /**
     * 跟进关系id
     */
    private Integer followId;
    /**
     * 累计没下单天数
     */
    private Integer notOrder;

    /**
     * notOrder
     */
    private Integer notOrderFollow;
    /**
     * notFollow
     */
    private Integer notFollow;
    /**
     * dangerDay
     */
    private Integer dangerDay;
    /**
     * @see FollowUpRelationEnum.DangerDayRule
     */
    private String dangerDayRule;
    /**
     * 掉落时间
     */
    private String releaseTime;

    /**
     * 掉落保护原因
     */
    private String protectReason;
    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    private String contractMethod;

    /**
     * 生命周期
     */
    private String lifecycle;

    private String mSize;

    private String tag;
    /**
     *
     */
    private BigDecimal deliveryFee;

    //所属大客户名称
    private String realName;
    /**
     * 是否在流转白名单,1在、0或者空，不在
     */
    private Integer whiteListType;

    /**
     * 省心送标签 0 无 1 是省心送
     */
    private Integer timingFollowType;

    /**
     * 是否是在公海 私海0,公海1
     */
    private Integer reassign;

    /**
     * 是否存在标签
     */
    private Integer coreMerchantTag;
    /**
     * 今天是否有拜访计划
     */
    private Boolean hasVisitPlanToday;
    /**
     * 历史下单
     */
    private Integer historyOrderCount;
    /**
     * 历史商品数
     */
    private Integer historySkuAmount;
    /**
     * 历史售后
     */
    private Integer historyAfterSaleCount;
    /**
     * 历史流转
     */
    private Integer historyRelation;

    private MerchantCluePool merchantCluePool;

    /**
     * 截单区域
     */
    private String mapSection;

    /**
     * 是否在截单区域内
     */
    private Boolean inMapSection;

    /**
     * es线索池Id
     */
    private String esId;

    /**
     * 线索池地址
     */
    private String clueAddress;

    /**
     * 线索池门店名称
     */
    private String clueMName;

    /**
     * 线索池 手机号
     */
    private String cluePhone;

    /**
     * 当月是否下单
     */
    private Boolean orderCurrentMonth;

    /**
     * poi
     */
    private PoiVO poi;

    /**
     * 店铺名称集合
     */
    private List<String> mnameList;

    /**
     * 手机号码集合
     */
    private List<String> phoneList;


    private LocalDate deliveryTime;

    /**
     * 店铺名称集合
     */
    private List<Long> mIdList;

    /**
     * 拆分城市信息
     */
    private Integer splitAreaNo;

    /**
     * 大客户截单时间
     */
    private String closeOrderTime;

    /**
     * 地址变更id
     */
    private Integer contactAdjustId;

    /**
     * 月度采购额
     */
    private String monthPurmoney;

    /**
     * 最新的下单
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime latestOrderTime;

    /**
     * 上月gmv
     */
    private BigDecimal lastMonthGmv;
    /**
     * 本月gmv
     */
    private BigDecimal thisMonthGmv;
    /**
     * 上月客单价
     */
    private BigDecimal lastMonthDeliveryUnitPrice;
    /**
     * 本月客单价
     */
    private BigDecimal thisMonthDeliveryUnitPrice;
    /**
     * 上月配送gmv
     */
    private BigDecimal lastDistributionGmv;
    /**
     * 本月配送gmv
     */
    private BigDecimal thisDistributionGmv;
    /**
     * 本月配送次数
     */
    private Integer distributionAmount;
    /**
     * 数据更新时间
     */
    private Integer updateTime;
    /**
     * 是否是销售
     */
    private Boolean roleTag;
    /**
     * bd维度-城市等级
     */
    private String areaGrade;
    /**
     * 核心客户定义之本月gmv阈值
     */
    private Integer gmvThreshold;
    /**
     * 核心客户定义之本月配送客单价阈值
     */
    private Integer priceThreshold;


    /**
     * 客户类型
     */
    private Integer adminType;

    /**
     * 客户备注
     */
    private String nameRemakes;

    /**
     * 近三个月平均下单周期
     */
    private Integer orderCycle;

    /**
     * 下单预警标识
     */
    private Boolean orderCycleWarn;

    /**
     * 已超过平均下单周期天数
     */
    private Integer moreThanOrderCycle;
    /**
     * 搜索关键字：CRM：客户名称及手机号
     */
    private String keyword;

    /**
     * 单店工商名称
     */
    private String invoiceTitle;
    /**
     * 是否需要显示FreeDay 0不展示
     */
    private Integer showFreeDay;
    /**
     * 配送规则
     */
    private String deliveryRule;

    /**
     * 门店所属BD id
     */
    private Long salerId;

    /**
     * 门店所属BD名称
     */
    private String salerName;
    /**
     * 客户标签集合
     */
    private List<String> merchantLabelList;

    /**
     * 客户标签
     */
    private String merchantLabel;

    /**
     * 未下单天数区间值 例如0~7天未下单:[0,7]
     */
    private List<Integer> notOrderList;
    /**
     * areaNos
     */
    private Set<Integer> areaNoSet;

    /**
     * 点位id
     */
    private Long contactId;

    /**
     * 距离月底天数
     */
    private Integer dayNumFromMonthEnd;

    /**
     * 关注bd id 0未关注 1关注
     */
    private Long careBdId;
    /**
     * 跟进着手机号
     */
    private String adminPhone;

    /**
     * 是否存在奶油卡 0不存在 1存在
     */
    private Integer haveDiscountCard;

    /**
     * 奶油卡状态：0、失效 1、有效
     */
    private Integer discountCardStatus;

    /**
     * 奶油卡总次数
     */
    private Integer discountTotalTimes;

    /**
     * 奶油卡使用次数
     */
    private Integer discountUsedTimes;

    /**
     * 奶油卡有效期
     */
    private String discountDeadline;

    /**
     * 大区编号
     */
    private Integer largeAreaNo;

    /**
     * r值
     */
    private String rValue;

    /**
     * f值
     */
    private String fValue;

    /**
     * m值
     */
    private String mValue;

    /**
     * 未登录天数
     */
    private Integer daysNotLoggedIn;

    /**
     * 官方微信
     */
    private String officialWechat;

    /**
     * 销售微信
     */
    private String salesWechat;

    /**
     * 业务线. 0: 鲜沐. 1: pop
     */
    private Integer businessLine;

    /**
     * 高价值客户标签
     */
    private String highValueLabel;

    /**
     * 高价值客户详情
     */
    private CRMDashboardModule highValueCustomerDetail;

    /**
     * storeId
     */
    private Long storeId;

    private boolean popMerchant;

    private List<String> newYearBusinessTag;

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }

    public boolean isInPublicSea() {
        return reassign == null || reassign == 1;
    }

    public boolean isInPrivateSea() {
        return !isInPublicSea();
    }
}
