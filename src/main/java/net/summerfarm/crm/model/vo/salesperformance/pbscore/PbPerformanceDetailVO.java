package net.summerfarm.crm.model.vo.salesperformance.pbscore;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class PbPerformanceDetailVO {

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 上月PB履约GMV
     */
    private BigDecimal lastDlvRealAmtPb;

    /**
     * PB本月截止昨晚履约GMV
     */
    private BigDecimal dlvRealAmtPb;

    /**
     * PB今日待履约GMV
     */
    private BigDecimal dlvRealAmtTodayPb;

    /**
     * PB今日交易本月待履约GMV
     */
    private BigDecimal dlvOrderAmtTodayPb;

    /**
     * PB其余待履约GMV
     */
    private BigDecimal dlvOtherAmtTodayPb;

    /**
     * PB本月预计总履约GMV
     */
    private BigDecimal totalCateGroupAmtPb;
}
