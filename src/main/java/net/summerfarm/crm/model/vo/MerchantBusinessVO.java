package net.summerfarm.crm.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 客户行业属性VO
 *
 * <AUTHOR>
 */

@Data
public class MerchantBusinessVO {

    /**
     * 客户id
     */
    private Long mId;

    /**
     * 客户类型
     */
    private String size;

    /**
     * 是否允许修改行业属性
     */
    private Boolean allowModify;

    /**
     * 客户主业类型（仅有一个）
     */
    private String mainBusinessType;

    /**
     * 客户副业类型：多个
     */
    private List<String> sideBusinessTypeList;

    /**
     * 客户连锁范围:0（NKA-全国连锁）1(LKA-区域连锁) 2(其他连锁) 3- 跨区域连锁 4- 工厂 99-单店
     */
    private Integer merchantChainType;

}
