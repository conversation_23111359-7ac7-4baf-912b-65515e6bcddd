package net.summerfarm.crm.model.vo.weCom;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;

/**
 * <AUTHOR>
 * @date 2024/2/26 14:02
 */
@Data
public class WeComUserInfoRes extends WeChatBaseResp {
    /**
     * 企微状态 1=已激活，2=已禁用，4=未激活，5=退出企业。
     */
    private Integer status;

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static WeComUserInfoRes fromJson(String json) {
        return JSONUtil.toBean(json, WeComUserInfoRes.class);
    }
}
