package net.summerfarm.crm.model.vo.wechat;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 企微基础返回
 *
 * <AUTHOR>
 * @date 2023/07/31
 */
@Data
public class WeChatBaseResp implements Serializable {
    private static final long serialVersionUID = -4301684507150486556L;

    /**
     * 错误码
     */
    protected Long errcode;

    /**
     * 错误信息
     */
    protected String errmsg;

    /**
     * 结果标识
     *
     * @return
     */
    public boolean success() {
        return ObjectUtil.equal(getErrcode(), 0L);
    }

    public boolean isTokenInvalid() {
        return getErrcode() == 42001L || getErrcode() == 40014L;
    }

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static WeChatBaseResp fromJson(String json) {
        return JSONUtil.toBean(json, WeChatBaseResp.class);
    }

    /**
     * @return {@link String}
     */
    public String toJson() {
        return JSONUtil.toJsonStr(this);
    }

}
