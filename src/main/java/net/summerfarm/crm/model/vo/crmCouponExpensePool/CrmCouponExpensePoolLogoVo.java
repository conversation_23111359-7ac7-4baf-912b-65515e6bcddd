package net.summerfarm.crm.model.vo.crmCouponExpensePool;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CrmCouponExpensePoolLogoVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 备注
     */
    private String remark;
    /**
     * 划分的bd
     */
    private String toAdminName;
    /**
     * 划分的金额
     */
    private BigDecimal amount;
    /**
     * 操作时间
     */
    private LocalDateTime localDateTime;
    /**
     * 操作人
     */
    private String adminName;
    /**
     * 类型
     */
    private String type;

}
