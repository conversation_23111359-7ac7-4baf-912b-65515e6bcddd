package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class ManageAreaVo implements Serializable {
    private static final long serialVersionUID = -8432656680366715346L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 城市负责人id
     */
    private Integer manageAdminId;
    /**
     * 区域负责人id
     */
    private Integer parentAdminId;
    /**
     * 总负责人id
     */
    private Integer departmentAdminId;
    /**
     * 城市负责人名称
     */
    private String manageAdminName;
    /**
     * 区域负责人名称
     */
    private String parentAdminName;
    /**
     * 总负责人名称
     */
    private String departmentAdminName;
    /**
     * 区域名称
     */
    private String zoneName;
    /**
     * 下属城市
     */
    private String subCity;
    /**
     * 销售城市
     */
    private List<CrmBdAreaDTO> areaCity;
    /**
     * 所属行政城市
     */
    private List<String> administrativeCitys;
}
