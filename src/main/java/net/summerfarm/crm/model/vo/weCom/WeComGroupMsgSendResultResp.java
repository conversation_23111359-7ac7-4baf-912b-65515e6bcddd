package net.summerfarm.crm.model.vo.weCom;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/26 15:24
 */
@Data
public class WeComGroupMsgSendResultResp extends WeChatBaseResp {
    @JSONField(name = "send_list")
    private List<SendList> sendList;

    @Data
    public class SendList {
        /**
         * 外部用户标识
         */
        @JSONField(name = "external_userid")
        private String externalUserid;

        /**
         * 外部客户群id，群发消息到客户不返回该字段
         */
        @JSONField(name = "chat_id")
        private String chatId;

        /**
         * 用户标识
         */
        private String userid;

        /**
         * 发送状态：0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败
         */
        private Integer status;

        /**
         * 发送时间
         */
        @JSONField(name = "send_time")
        private Long sendTime;
    }

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static WeComGroupMsgSendResultResp fromJson(String json) {
        return JSONUtil.toBean(json, WeComGroupMsgSendResultResp.class);
    }

}
