package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmBdTodayDayGmv;
import net.summerfarm.crm.model.domain.CrmCityTodayGmv;
import net.summerfarm.crm.model.vo.BdSalesCityVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售区域数据vo
 *
 * <AUTHOR>
 * @date 2023/10/31 15:50
 */
@Data
public class SalesAreaDataVo {
    /**
     * 销售区域id
     */
    private Integer salesAreaId;
    /**
     * 销售区域名称
     */
    private String salesAreaName;

    private String city;

    /**
     * gmv 信息
     */
    private CrmCityTodayGmv areaTodayGmv;

    /**
     * 下单 gmv
     */
    private BigDecimal orderGmv;

    /**
     * 配送 gmv
     */
    private BigDecimal deliveryGmv;

    /**
     * 下单客户数
     */
    private Integer orderMerchant;

    /**
     * 鲜果 mgv
     */
    private BigDecimal fruitGmv;

    /**
     * 乳制品 gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 非乳gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 自营品 gmv
     */
    private BigDecimal brandGmv;

    /**
     * 代仓 gmv
     */
    private BigDecimal agentGmv;

    /**
     * 奖励 gmv
     */
    private BigDecimal rewardGmv;

    /**
     * 拉新数
     */
    private Integer pullNewAmount;

    /**
     * 拜访数
     */
    private Integer visitNum;

    /**
     * 月活
     */
    private Integer monthLivingAmount;


    public static CrmBdTodayDayGmv todayDayGmv(SalesAreaDataVo data){

        if (data == null) {
            return null;
        }
        CrmBdTodayDayGmv crmBdTodayDayGmv = new CrmBdTodayDayGmv();
        crmBdTodayDayGmv.setTotalGmv(data.getOrderGmv());
        crmBdTodayDayGmv.setDeliveryGmv(data.getDeliveryGmv());
        crmBdTodayDayGmv.setFruitGmv(data.getFruitGmv());
        crmBdTodayDayGmv.setDairyGmv(data.getDairyGmv());
        crmBdTodayDayGmv.setNonDairyGmv(data.getNonDairyGmv());
        crmBdTodayDayGmv.setBrandGmv(data.getBrandGmv());
        crmBdTodayDayGmv.setRewardGmv(data.getRewardGmv());
        crmBdTodayDayGmv.setPullNewAmount(data.getPullNewAmount());
        crmBdTodayDayGmv.setVisitNum(data.getVisitNum());
        crmBdTodayDayGmv.setAgentGmv(data.getAgentGmv());
        crmBdTodayDayGmv.setOrderMerchant(data.getOrderMerchant());
        return crmBdTodayDayGmv;
    }
}
