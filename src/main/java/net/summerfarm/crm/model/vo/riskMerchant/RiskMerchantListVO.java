package net.summerfarm.crm.model.vo.riskMerchant;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门店风控查询条件
 *
 * <AUTHOR>
 * @date 2023/11/17 17:47
 */
@Data
public class RiskMerchantListVO {
    /**
     * 风控门店id    不是门店 id
     */
    private Integer riskMerchantId;

    /**
     * 门店 id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 门店类型
     */
    private String size;

    /**
     * 电话
     */
    private String phone;

    /**
     * 省 省+市+区=注册地址
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 销售名字
     */
    private String bdName;

    /**
     * 触发场景:0:新注册;1:存量动销;
     */
    private String triggerOccasions;

    /**
     * 命中分类:0:疑似重复;1:疑似虚假;2:疑似换壳;
     */
    private String triggerClassification;

    /**
     * 状态 0:待处理 1:审核通过 2:审核不通过
     */
    private Integer status;

    /**
     * 更新时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
