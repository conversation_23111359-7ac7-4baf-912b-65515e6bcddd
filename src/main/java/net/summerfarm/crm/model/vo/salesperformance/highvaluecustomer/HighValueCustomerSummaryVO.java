package net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 高价值客户汇总数据VO
 * 销售维度汇总
 */
@Data
public class HighValueCustomerSummaryVO {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 客户数
     */
    private Integer customerCount;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 履约GMV
     */
    private BigDecimal fulfillmentGmv;

    /**
     * SPU数
     */
    private Integer spuCount;
}
