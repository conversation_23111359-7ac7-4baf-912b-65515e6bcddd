package net.summerfarm.crm.model.vo.weCom;

import lombok.Data;

/**
 * 企微激活状态
 *
 * <AUTHOR>
 * @date 2024/2/21 13:49
 */
@Data
public class WeComActivateVo {
    /**
     * 销售 id
     */
    private Integer bdId;

    /**
     * 销售名字。
     */
    private String bdName;

    /**
     * org id
     */
    private Integer orgId;

    /**
     * 在职人数
     */
    private Integer onJobCount;

    /**
     * 离职人数
     */
    private Integer offJobCount;

    /**
     * 在职且激活企微人数
     */
    private Integer onJobActivateCount;

    /**
     * 离职且激活企微人数
     */
    private Integer offJobActivateCount;

    /**
     * 在职且激活企微占比
     */
    private Integer onJobActivateProportion;

    /**
     * 级别
     */
    private Integer rank;
}
