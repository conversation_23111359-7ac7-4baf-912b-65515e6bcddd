package net.summerfarm.crm.model.vo.saasorder;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SaasOrderItemVO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单项-SKU
     */
    private String skuId;

    /**
     * 订单项-商品pd_id
     */
    private Long spuId;

    /**
     * 订单项-商品名称
     */
    private String spuName;

    /**
     * 订单项-规格
     */
    private String weight;

    /**
     * 订单项-数量
     */
    private Long skuCnt;

    /**
     * 订单项-单价
     */
    private BigDecimal payablePrice;


    /**
     * 订单项-图片路径
     */
    private String picturePath;
}
