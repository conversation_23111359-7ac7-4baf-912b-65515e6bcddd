package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/2 10:08
 */
@Data
public class CrmSkuMerchantGmvVO {

    /**
     * mId
     */
    private Long merchantId;
    /**
     * 商户名
     */
    private String merchantName;
    /**
     * 最近下单时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime lastOrderTime;

    /*
     * 下单gmv
     */
    private BigDecimal gmv;
    /**
     * 下单gmv环比
     */
    private BigDecimal gmvRingRatio;
    /**
     * 下单数量
     */
    private Integer salesVolume;
    /**
     * 下单数量环比
     */
    private BigDecimal salesVolumeRingRatio;

    /**
     * 下单频次
     */
    private Integer merchantNum;
    /**
     * 下单频次环比
     */
    private BigDecimal merchantNumRingRatio;



}
