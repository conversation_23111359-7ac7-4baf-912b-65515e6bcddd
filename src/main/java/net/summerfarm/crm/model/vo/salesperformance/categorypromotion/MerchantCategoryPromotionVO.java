package net.summerfarm.crm.model.vo.salesperformance.categorypromotion;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 门店品类推广详情VO
 * 单个门店购买的商品
 */
@Data
public class MerchantCategoryPromotionVO {

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 商品分组
     */
    private String spuGroup;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 履约件数
     */
    private Double fulfillmentCount;

    /**
     * 交易件数
     */
    private Double transactionCount;

    /**
     * 单件奖励
     */
    private BigDecimal rewardPerItem;
}
