package net.summerfarm.crm.model.vo.areaConfig;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmSalesCity;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import net.summerfarm.crm.model.vo.TableArea;

import java.util.List;

/**
 * 销售区域配置
 *
 * <AUTHOR>
 * @date 2023/08/21
 */
@Data
public class BdAreaConfigVo {
    /**
     * m3 id
     */
    private Integer departmentAdminId;
    private Integer departmentOrgId;
    /**
     * m3 名称
     */
    private String departmentAdminName;
    /**
     * m2 id
     */
    private Integer managerAdminId;
    private Integer managerOrgId;
    /**
     * m2 名称
     */
    private String managerAdminName;
    /**
     * m1 id
     */
    private Integer cityAdminId;
    private Integer cityOrgId;
    /**
     * m1 名称
     */
    private String cityAdminName;
    /**
     * 销售区域id
     */
    private Integer salesAreaId;
    /**
     * 销售区域名称
     */
    private String salesAreaName;
    /**
     * 下属销售城市
     */
    private List<BdSalesCityVo> salesCityList;
    /**
     * 下属销售城市 级联列表
     */
    private List<TableArea> regionTable;
}
