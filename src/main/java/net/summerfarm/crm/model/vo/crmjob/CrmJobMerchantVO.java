package net.summerfarm.crm.model.vo.crmjob;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CrmJobMerchantVO {

    /**
     * 任务Id FK crm_job
     */
    private Long jobId;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 电话
     */
    private String phone;

    /**
     * 是否添加官微:0:是;1:否；
     */
    private Integer officialWechatFlag;

    /**
     * 是否添加销微:0:是;1:否；
     */
    private Integer bdWechatFlag;

    /**
     * 销售id
     */
    private Long bdId;

    /**
     * 销售名字
     */
    private String bdName;

    /**
     * 品列表
     */
    private List<JobProductVO> productList;

    /**
     * 任务完成状态
     */
    private Integer status;

    /**
     * 累计实付金额
     */
    private BigDecimal realTotalAmt;

    /**
     * 任务门店标签
     */
    private String jobMerchantLabel;
}
