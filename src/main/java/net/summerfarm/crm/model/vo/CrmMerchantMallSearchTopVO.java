package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmMerchantMallSearchTop;
import net.summerfarm.crm.model.dto.TrolleyDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/2 10:58
 */
@Data
public class CrmMerchantMallSearchTopVO {

    /**
     * 搜索top10
     */
    private List<CrmMerchantMallSearchTop> crmMerchantMallSearchTopList;

    /**
     * 加购未下单列表
     */
    private List<TrolleyDTO> trolleyList;

    /**
     * 商城最终登录时间
     */
    private LocalDateTime lastLoginTime;

}
