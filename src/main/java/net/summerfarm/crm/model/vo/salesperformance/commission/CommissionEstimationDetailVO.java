package net.summerfarm.crm.model.vo.salesperformance.commission;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 佣金预估VO - 销售视角
 */
@Data
public class CommissionEstimationDetailVO {

    /**
     * 销售Id
     */
    private Long bdId;

    /**
     * 销售姓名
     */
    private String bdName;


    // ------------------ 预估明细 ------------------

    /**
     * 高价值客户单客户奖金
     */
    private BigDecimal highValueCustomerBonus;

    /**
     * 私海高价值客户数
     */
    private Integer highValueCustomerCount;

    /**
     * 利润积分
     */
    private Integer totalScoreNum;

    /**
     * 利润积分系数
     */
    private Double bdPerformanceRate;

    /**
     * 超过标准的SPU数
     */
    private Integer excessSpuCount;

    /**
     * 单客户SPU奖励
     */
    private BigDecimal spuRewardPerCustomer;

    /**
     * 指定品类推广奖金
     */
    private BigDecimal categoryPromotionBonus;
}
