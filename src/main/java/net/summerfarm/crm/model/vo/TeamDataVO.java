package net.summerfarm.crm.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.model.domain.Merchant;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 16:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TeamDataVO extends Merchant {

    /**
     * 所属二级城市No
     */
    private Integer areaNo;

    /**
     * bd名
     */
    private String bdName;

    /**
     * 城市负责人姓名（M1）
     */
    private String manageName;

    /**
     * 区域负责人姓名（M2）
     */
    private String parentName;

    /**
     * 商户类别：单店，普通大客户，茶百道
     */
    private String mSize;

    /**
     * bd-id
     */
    private Integer bdId;

    /**
     * 订单开始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
     * 订单结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    /**
     * 注册时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime registrationTime;

    /**
     * 最近下单时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime recentOrderTime;

    /**
     * 最近下单实付GMV
     */
    private Double recentGmv;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 所属二级城市名
     */
    private String areaName;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 客户归属团队:1:平台销售，2:大客户销售
     */
    private Integer salesTeam;

    /**
     * 店铺主营类型
     */
    private String mainType;

    /**
     * 客户是否为公海客户
     */
    private Integer reassign;

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }
}
