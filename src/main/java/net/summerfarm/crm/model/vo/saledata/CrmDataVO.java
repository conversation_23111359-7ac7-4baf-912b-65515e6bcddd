package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;

import java.math.BigDecimal;

/**
 * CRM数据VO.
 * 大坑警告!!!
 * bd维度,城市维度的小时表和月维度表,相同字段的字段名可能不一样(重点关注"安佳铁塔"相关字段),加字段的时候,要注意converter是否需要手动改映射
 * 这里的字段名以crm_city_today_hour_gmv表的字段名为基准
 * @see net.summerfarm.crm.model.domain.CrmCityTodayHourGmv
 *
 * @see net.summerfarm.crm.model.convert.salesdata.SalesDataConverter
 */
@Data
public class CrmDataVO {

    /**
     * 交易实付GMV
     */
    private BigDecimal realTotalGmv;

    /**
     * 交易客户数
     */
    private Long custCnt;

    /**
     * 全品类交易实付GMV
     */
    private BigDecimal categoriesRealTotalGmv;

    /**
     * 鲜果交易实付GMV
     */
    private BigDecimal fruitRealTotalGmv;

    /**
     * 安佳铁塔交易实付GMV
     */
    private BigDecimal anchorRealTotalGmv;

    /**
     * 乳制品（不含AT）交易实付GMV
     */
    private BigDecimal dairyRealTotalGmv;

    /**
     * 其他交易实付GMV
     */
    private BigDecimal otherRealTotalGmv;

    /**
     * 非AT交易实付GMV
     */
    private BigDecimal noAnchorRealTotalGmv;

    /**
     * 履约实付GMV
     */
    private BigDecimal dlvRealTotalGmv;

    /**
     * 省心送明日履约实付GMV
     */
    private BigDecimal dlvTimingRealTotalGmv;

    /**
     * 履约客户数
     */
    private Long dlvCustCnt;

    /**
     * 全品类履约实付GMV
     */
    private BigDecimal dlvCategoriesRealTotalGmv;

    /**
     * 鲜果履约实付GMV
     */
    private BigDecimal dlvFruitRealTotalGmv;

    /**
     * 安佳铁塔履约实付GMV
     */
    private BigDecimal dlvAnchorRealTotalGmv;

    /**
     * 安佳铁塔省心送履约实付GMV
     */
    private BigDecimal dlvTimingAnchorRealTotalGmv;

    /**
     * 乳制品（不含AT）履约实付GMV
     */
    private BigDecimal dlvDairyRealTotalGmv;

    /**
     * 乳制品（不含AT）省心送履约实付GMV
     */
    private BigDecimal dlvTimingDairyRealTotalGmv;

    /**
     * 其他履约实付GMV
     */
    private BigDecimal dlvOtherRealTotalGmv;

    /**
     * 其他省心送履约实付GMV
     */
    private BigDecimal dlvTimingOtherRealTotalGmv;

    /**
     * 非AT履约实付GMV
     */
    private BigDecimal dlvNoAnchorRealTotalGmv;

    /**
     * 非AT省心送实付GMV
     */
    private BigDecimal dlvTimingNoAnchorRealTotalGmv;

    /**
     * 非AT履约实付GMV+全品类交易实付GMV
     */
    private BigDecimal noAnchorCategoriesKpiGmv;
}
