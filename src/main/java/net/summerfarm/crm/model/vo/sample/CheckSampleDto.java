package net.summerfarm.crm.model.vo.sample;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckSampleDto {
    /**
     * 是否可以被申请
     */
    private Boolean  canApply;
    /**
     * 提示的message
     */
    private String message;

    /**
     * 风险标签
     */
    private Boolean riskLevel;

    public CheckSampleDto(Boolean canApply, String message) {
        this.canApply = canApply;
        this.message = message;
    }
}
