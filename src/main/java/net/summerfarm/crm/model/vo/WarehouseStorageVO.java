package net.summerfarm.crm.model.vo;/**
 * <AUTHOR>
 * @date 2023/1/5 13:54
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.manage.client.wms.dto.res.WarehouseStorageDTO;

import java.math.BigDecimal;

/**
 * 仓库库存信息
 *
 * <AUTHOR>
 * @date 2023/1/5 13:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseStorageVO {
    /**
     * 仓库编号
     */
    private Integer areaNo;

    private int count;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * sku
     */
    private String sku;

    /**
     * 存储区域
     */
    private Integer storageLocation;

    /**
     * 是否同步
     */
    private Integer sync;

    /**
     * timingQuantity
     */
    private int timingQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 体积
     */
    private String volume;

    /**
     * 库存仓名称
     */
    private String warehouseName;

    /**
     * 规格
     */
    private String weight;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    public static WarehouseStorageVO toVO(WarehouseStorageDTO dto) {
        WarehouseStorageVO warehouseStorageVo = new WarehouseStorageVO();
        warehouseStorageVo.setAreaNo(dto.getAreaNo());
        warehouseStorageVo.setCount(dto.getCount());
        warehouseStorageVo.setPdName(dto.getPdName());
        warehouseStorageVo.setSku(dto.getSku());
        warehouseStorageVo.setStorageLocation(dto.getStorageLocation());
        warehouseStorageVo.setSync(dto.getSync());
        warehouseStorageVo.setTimingQuantity(dto.getTimingQuantity());
        warehouseStorageVo.setUnit(dto.getUnit());
        warehouseStorageVo.setVolume(dto.getVolume());
        warehouseStorageVo.setWarehouseName(dto.getWarehouseName());
        warehouseStorageVo.setWeight(dto.getWeight());
        warehouseStorageVo.setWeightNum(dto.getWeightNum());
        return warehouseStorageVo;
    }
}
