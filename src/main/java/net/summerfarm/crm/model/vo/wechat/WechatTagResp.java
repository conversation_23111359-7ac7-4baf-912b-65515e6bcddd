package net.summerfarm.crm.model.vo.wechat;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.crm.model.domain.CrmMerchantDayAttribute;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/8/4 15:53
 */
@Data
public class WechatTagResp extends WeChatBaseResp {
    @JSONField(name = "tag_group")
    private List<TagGroup> tagGroup;

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static WechatTagResp fromJson(String json) {
        return JSONUtil.toBean(json, WechatTagResp.class);
    }

    public static Map<String,String> getNameIdMap(WechatTagResp wechatTagResp){
        if (wechatTagResp ==  null || CollectionUtils.isEmpty(wechatTagResp.tagGroup)){
            return new HashMap<>();
        }
        return wechatTagResp.getTagGroup().stream().collect(Collectors.toMap(TagGroup::getGroupName, TagGroup::getGroupId,(entity1, entity2) -> entity1));
    }

    public static Map<String,String> getIdNameMap(WechatTagResp wechatTagResp){
        if (wechatTagResp ==  null || CollectionUtils.isEmpty(wechatTagResp.tagGroup)){
            return new HashMap<>();
        }
        return wechatTagResp.getTagGroup().stream().collect(Collectors.toMap(TagGroup::getGroupId, TagGroup::getGroupName,(entity1, entity2) -> entity1));
    }
}
