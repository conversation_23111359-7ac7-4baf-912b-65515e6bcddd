package net.summerfarm.crm.model.vo.salesperformance.categorypromotion;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class CategoryPromotionSpuSummaryVO {

    /**
     * 商品分组
     */
    private String spuGroup;

    /**
     * 客户数量
     */
    private Integer custCnt;

    /**
     * 本月截止昨晚品类推广佣金
     */
    private BigDecimal categoryCommAmt;

    /**
     * 本月预计品类推广总佣金
     */
    private BigDecimal monthCategoryCustComm;

    /**
    * 单件奖励
    */
    private BigDecimal rewardPerItem;

    /**
    * 是否履约结算:1 履约，0 交易
    */
    private Integer isDlvPayment;

    /**
    * 奖励方式：履约件数，交易件数
    */
    private String rewardMode;

    /**
     * 本月截止昨晚履约件数
     */
    private String bigSkuCnt;

    /**
     * 今日待履约件数
     */
    private String dlvRealCntToday;

    /**
     * 今日交易本月待履约件数
     */
    private String dlvOrderCntToday;

    /**
     * 其余待履约件数
     */
    private String dlvOtherCntToday;

    /**
     * 本月预计总履约件数
     */
    private String monthDlvRealCntToday;

    /**
     * 本月截止昨晚交易件数
     */
    private String mtdTxnSkuCnt;

    /**
     * 今日交易件数
     */
    private String todayTxnSkuCnt;

    /**
     * 设置本月截止昨晚履约件数
     */
    public void setBigSkuCnt(Double bigSkuCnt) {
        this.bigSkuCnt = (bigSkuCnt != null) ? String.format("%.2f", bigSkuCnt) : null;
    }

    /**
     * 设置今日待履约件数
     */
    public void setDlvRealCntToday(Double dlvRealCntToday) {
        this.dlvRealCntToday = (dlvRealCntToday != null) ? String.format("%.2f", dlvRealCntToday) : null;
    }

    /**
     * 设置今日交易本月待履约件数
     */
    public void setDlvOrderCntToday(Double dlvOrderCntToday) {
        this.dlvOrderCntToday = (dlvOrderCntToday != null) ? String.format("%.2f", dlvOrderCntToday) : null;
    }

    /**
     * 设置其余待履约件数
     */
    public void setDlvOtherCntToday(Double dlvOtherCntToday) {
        this.dlvOtherCntToday = (dlvOtherCntToday != null) ? String.format("%.2f", dlvOtherCntToday) : null;
    }

    /**
     * 设置本月预计总履约件数
     */
    public void setMonthDlvRealCntToday(Double monthDlvRealCntToday) {
        this.monthDlvRealCntToday = (monthDlvRealCntToday != null) ? String.format("%.2f", monthDlvRealCntToday) : null;
    }

    /**
     * 设置本月截止昨晚交易件数
     */
    public void setMtdTxnSkuCnt(Double mtdTxnSkuCnt) {
        this.mtdTxnSkuCnt = (mtdTxnSkuCnt != null) ? String.format("%.2f", mtdTxnSkuCnt) : null;
    }

    /**
     * 设置今日交易件数
     */
    public void setTodayTxnSkuCnt(Double todayTxnSkuCnt) {
        this.todayTxnSkuCnt = (todayTxnSkuCnt != null) ? String.format("%.2f", todayTxnSkuCnt) : null;
    }

    // ---------------------------------- 下面的setter方法需要保留，避免CRMDashboardModule取值时报错 --------------------------

    public void setBigSkuCnt(String bigSkuCnt) {
        this.bigSkuCnt = bigSkuCnt;
    }

    public void setDlvRealCntToday(String dlvRealCntToday) {
        this.dlvRealCntToday = dlvRealCntToday;
    }

    public void setDlvOrderCntToday(String dlvOrderCntToday) {
        this.dlvOrderCntToday = dlvOrderCntToday;
    }

    public void setDlvOtherCntToday(String dlvOtherCntToday) {
        this.dlvOtherCntToday = dlvOtherCntToday;
    }

    public void setMonthDlvRealCntToday(String monthDlvRealCntToday) {
        this.monthDlvRealCntToday = monthDlvRealCntToday;
    }

    public void setMtdTxnSkuCnt(String mtdTxnSkuCnt) {
        this.mtdTxnSkuCnt = mtdTxnSkuCnt;
    }

    public void setTodayTxnSkuCnt(String todayTxnSkuCnt) {
        this.todayTxnSkuCnt = todayTxnSkuCnt;
    }
}
