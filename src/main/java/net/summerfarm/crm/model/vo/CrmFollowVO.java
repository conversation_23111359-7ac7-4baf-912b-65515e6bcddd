package net.summerfarm.crm.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description crm_clue_follow
 * @date 2023-05-17
 */
@Data
public class CrmFollowVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主题id 也可以看作是线索id
     */
    private Long subjectId;

    /**
     * 跟进目的
     */
    private String followGoal;

    /**
     * 客户反馈
     */
    private String customerFeedback;

    /**
     * 下次跟进
     */
    private String nextFollow;

    /**
     * 图片
     */
    private String images;

    public CrmFollowVO() {
    }
}