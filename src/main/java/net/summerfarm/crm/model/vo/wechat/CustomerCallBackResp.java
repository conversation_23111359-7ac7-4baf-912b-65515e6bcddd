package net.summerfarm.crm.model.vo.wechat;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/8/3 10:08
 */
@Data
public class CustomerCallBackResp implements Serializable {
    /**
     * corpid
     */
    private String ToUserName;
    /**
     * 固定sys
     */
    private String FromUserName;
    /**
     * 创建时间 unix 时间
     */
    private Long CreateTime;
    /**
     * 消息类型 此接口固定event
     */
    private String MsgType;
    /**
     * 事件类型 客户联系固定change_external_contact
     */
    private String Event;
    /**
     * 事件类型
     */
    private String ChangeType;
    /**
     * bd userid
     */
    private String UserID;
    /**
     * 客户userid
     */
    private String ExternalUserID;
    /**
     * 添加渠道
     */
    private String State;
    /**
     * 欢迎语代码
     */
    private String WelcomeCode;
}
