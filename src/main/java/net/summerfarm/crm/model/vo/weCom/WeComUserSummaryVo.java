package net.summerfarm.crm.model.vo.weCom;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/2/21 14:22
 */
@Data
public class WeComUserSummaryVo {
    /**
     * 销售 id
     */
    private Integer bdId;

    /**
     * org id
     */
    private Integer orgId;

    /**
     * 级别
     */
    private Integer rank;

    /**
     * 销售名字。
     */
    private String bdName;
    /**
     * 私海客户数
     */
    private Integer privateSeaCount;

    /**
     * 有效企微客户数
     */
    private Integer effectiveWeComUserCount;

    /**
     * 有效企微客户占比
     */
    private Integer effectiveWeComUserProportion;

    /**
     * 企微客户数
     */
    private Integer weComUserCount;

    /**
     * 企微客户占比
     */
    private Integer weComUserProportion;

    /**
     * 销售删除客户数量
     */
    private Integer bdDeleteWeComCount;

    /**
     * 用户删除客户数量
     */
    private Integer userDeleteWeComCount;

    /**
     * 互删
     */
    private Integer deleteWeComCount;
}
