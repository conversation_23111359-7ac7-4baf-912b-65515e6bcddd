package net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class HighValueCustomerSummaryV2VO {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 当前高价值客户标签的客户数
     */
    private Integer custCnt;

    /**
     * 客户数佣金
     */
    private BigDecimal custCommAmt;

    /**
     * 本月截止昨晚履约GMV
     */
    private BigDecimal dlvRealAmt;

    /**
     * 今日待履约GMV
     */
    private BigDecimal dlvRealAmtToday;

    /**
     * 本月截止今天累计履约GMV(离线+今日)
     */
    private BigDecimal dlvMonthTodayTotalAmt;

    /**
     * 今日交易本月履约GMV
     */
    private BigDecimal dlvOrderAmtToday;

    /**
     * 其余待履约GMV
     */
    private BigDecimal dlvOtherAmtToday;

    /**
     * 本月预计总履约GMV
     */
    private BigDecimal dlvMonthTotalAmt;

    /**
     * 本月截止昨晚履约SPU
     */
    private Integer dlvSpuCnt;

    /**
     * 今日待履约SPU
     */
    private Integer dlvRealSpuCntToday;

    /**
     * 本月截止今天累计履约spu数(离线+今日 去重)
     */
    private Integer dlvMonthTodayTotalSpuCnt;

    /**
     * 今日交易本月履约SPU
     */
    private Integer dlvOrderSpuCntToday;

    /**
     * 其余待履约SPU
     */
    private Integer dlvOtherSpuCntToday;

    /**
     * 本月预计总履约SPU
     */
    private Integer dlvMonthTotalSpuCnt;

}
