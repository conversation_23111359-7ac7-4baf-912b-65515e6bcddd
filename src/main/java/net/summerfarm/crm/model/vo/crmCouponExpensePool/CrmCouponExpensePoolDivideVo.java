package net.summerfarm.crm.model.vo.crmCouponExpensePool;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CrmCouponExpensePoolDivideVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 备注
     */
    private String remark;
    /**
     * 划分的bd
     */
    private Integer toAdminId;
    /**
     * 划分的金额
     */
    private BigDecimal amount;
    /**
     * 池子id
     */
    @NotNull
    private Long poolId;
}
