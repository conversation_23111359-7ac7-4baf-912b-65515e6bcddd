package net.summerfarm.crm.model.vo.wechat;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/1 14:11
 */
@Data
public class CustomerDetailResp extends WeChatBaseResp {
    @JSONField(name = "external_contact_list")
    private List<ExternalContactDetail> externalContactList;
    @JSONField(name = "next_cursor")
    private String nextCursor;

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static CustomerDetailResp fromJson(String json) {
        return JSONUtil.toBean(json, CustomerDetailResp.class);
    }
}
