package net.summerfarm.crm.model.vo.areaConfig;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmSalesCity;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import net.summerfarm.crm.model.vo.TableArea;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/30 17:13
 */
@Data
public class SalesAreaVo {
    /**
     * 销售区域id
     */
    private Integer salesAreaId;
    /**
     * 销售区域名称
     */
    private String salesAreaName;
    /**
     * 销售城市清单
     */
    private List<BdSalesCityVo> salesCityList;
    /**
     * 区域配置
     */
    private List<TableArea> regionTable;
}
