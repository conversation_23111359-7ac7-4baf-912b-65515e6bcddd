package net.summerfarm.crm.model.vo.weCom;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/26 14:36
 */
@Data
public class WeComUserBehaviorResp extends WeChatBaseResp{
    @JSONField(name = "behavior_data")
    private List<BehaviorData> behaviorData;

    @Data
    public class BehaviorData {
        /**
         * 统计时间 当日0点时间戳
         */
        @JSONField(name = "stat_time")
        private Long statTime;

        /**
         * 聊天总数
         */
        @JSONField(name = "chat_cnt")
        private Integer chatCnt;

        /**
         * 发送消息数
         */
        @JSONField(name = "message_cnt")
        private Integer messageCnt;

        /**
         * 已回复聊天占比
         */
        @JSONField(name = "reply_percentage")
        private BigDecimal replyPercentage;

        /**
         * 平均回复时间
         */
        @JSONField(name = "avg_reply_time")
        private Integer avgReplyTime;

        /**
         * 删除/拉黑成员的客户数，即将成员删除或加入黑名单的客户数。
         */
        @JSONField(name = "negative_feedback_cnt")
        private Integer negativeFeedbackCnt;

        /**
         * 发起申请数，成员通过「搜索手机号」、「扫一扫」、「从微信好友中添加」、「从群聊中添加」、「添加共享、分配给我的客户」、「添加单向、双向删除好友关系的好友」、「从新的联系人推荐中添加」等渠道主动向客户发起的好友申请数量。
         */
        @JSONField(name = "new_apply_cnt")
        private Integer newApplyCnt;

        /**
         * 新增客户数，成员新添加的客户数量。
         */
        @JSONField(name = "new_contact_cnt")
        private Integer newContactCnt;
    }

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static WeComUserBehaviorResp fromJson(String json) {
        return JSONUtil.toBean(json, WeComUserBehaviorResp.class);
    }
}
