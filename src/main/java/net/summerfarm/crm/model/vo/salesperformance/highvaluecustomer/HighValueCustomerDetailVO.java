package net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 高价值客户详情VO
 * 客户维度汇总
 */
@Data
public class HighValueCustomerDetailVO {

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 履约GMV
     */
    private BigDecimal fulfillmentGmv;

    /**
     * SPU数
     */
    private Integer spuCount;
}
