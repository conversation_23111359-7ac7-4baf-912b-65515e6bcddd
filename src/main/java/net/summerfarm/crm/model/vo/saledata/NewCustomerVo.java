package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;
import net.summerfarm.crm.model.vo.MerchantVO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;

/**
 * <AUTHOR>
 * @date 2023/11/1 16:00
 */
@Data
public class NewCustomerVo {
    /**
     * 销售id
     */
    private Integer bdId;
    /**
     * 门店 Id
     */
    private Long mId;
    /**
     * 门店名称
     */
    private String mname;

    public static NewCustomerVo toVo(MerchantStoreAndExtendResp merchantExtend) {
        if (merchantExtend == null) {
            return null;
        }
        NewCustomerVo newCustomerVo = new NewCustomerVo();
        newCustomerVo.setMId(merchantExtend.getMId());
        newCustomerVo.setMname(merchantExtend.getStoreName());
        return newCustomerVo;
    }
}
