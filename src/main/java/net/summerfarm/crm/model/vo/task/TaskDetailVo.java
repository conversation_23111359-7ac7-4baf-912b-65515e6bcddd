package net.summerfarm.crm.model.vo.task;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/28 15:58
 */
@Data
@Accessors(chain = true)
public class TaskDetailVo {
    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 任务详细信息id
     */
    private Integer taskDetailId;
    /**
     * 门店 Id
     */
    private Integer mId;
    /**
     * 门店名称
     */
    private String mname;
    /**
     * 电话
     */
    private String phone;
    /**
     * 销售id
     */
    private Integer bdId;
    /**
     * 销售名字
     */
    private String bdName;
    /**
     * 卡券 id
     */
    private String sourceId;
    /**
     * 多订单标签:0;是1:否;
     */
    private Integer multiOrderTag;
    /**
     * 多任务标签:0;是1:否;
     */
    private Integer multiTaskTag;

    /**
     * 拜访标志:0:已拜访;1:未拜访;
     */
    private Integer followUpFlag;

    /**
     * 任务类型:0:卡券;1:拜访;
     */
    private Integer type;

    /**
     * 优惠券额度
     */
    private BigDecimal money;
    /**
     * 使用阈值
     */
    private BigDecimal threshold;
    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 状态 0:未完成;1:已完成
     */
    private Integer status;
}
