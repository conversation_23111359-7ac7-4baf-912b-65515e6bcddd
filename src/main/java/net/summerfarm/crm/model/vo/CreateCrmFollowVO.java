package net.summerfarm.crm.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17
 */
@Data
public class CreateCrmFollowVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id 自增
     */
    private Long id;

    /**
     * 主题id 也可以看作是线索id
     */
    @NotNull
    private Long subjectId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 跟进目的
     */
    private String followGoal;

    /**
     * 客户反馈
     */
    private String customerFeedback;

    /**
     * 下次跟进
     */
    private String nextFollow;

    /**
     * 图片
     */
    private String images;
    /**
     * 跟进方式
     */
    private String  followModel;

    public CreateCrmFollowVO() {}
}