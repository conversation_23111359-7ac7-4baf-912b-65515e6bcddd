package net.summerfarm.crm.model.vo.weCom.contactway;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;

@Data
@EqualsAndHashCode(callSuper = false)
public class WeComAddContactWayResp extends WeChatBaseResp {

    @JSONField(name = "config_id")
    private String configId;

    @JSONField(name = "qr_code")
    private String qrCode;

}
