package net.summerfarm.crm.model.vo.crmjobv2;

import lombok.Data;

import java.math.BigDecimal;

/**
 * CRM任务门店item V2VO
 * 对应CrmJobMerchantItem表的数据
 */
@Data
public class CrmJobMerchantItemV2VO {

    /**
     * 任务item,比如品或品类
     */
    private String item;

    /**
     * item类型 0 - sku; 1 - 品类; 2 - 其它(自定义)
     */
    private Integer itemType;

    /**
     * 达成状态 0.未完成 1.已完成
     */
    private Integer status;
}
