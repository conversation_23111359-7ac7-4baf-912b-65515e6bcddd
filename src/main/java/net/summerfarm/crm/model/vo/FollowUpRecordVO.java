package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.FollowRecordEnum;
import net.summerfarm.crm.model.domain.FollowUpEvaluation;
import net.summerfarm.crm.model.domain.FollowUpRecord;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Package: net.summerfarm.model.vo
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/3/10
 */
@Data
public class FollowUpRecordVO extends FollowUpRecord {
    /**
     * 商户名称
     */
    private String mname;
    /**
     * 联系人
     */
    private String mcontact;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 跟进入名称
     */
    private String adminName;
    /**
     * 地址
     */
    private String address;
    /**
     * 城市编号
     */
    private Integer areaNo;
    /**
     * 城市名称
     */
    private String areaName;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date endTime;
    /**
     * 会员等级
     */
    private Integer grade;
    /**
     * 客户类型
     */
    private String mSize;

    /**
     * 拜访的kp姓名
     */
    private String personName;
    /**
     * 陪访人
     */
    private String escortAdminName;
    /**
     * 是否上门拜访
     */
    private Integer visitToHome;
    /**
     * 是否校准poi
     */
    private Boolean checkPoi;

    /**
     * 打卡定位
     */
    private String location;

    /**
     * 拜访方式集合
     */
    private List<String> followUpWayList;

    /**
     * 陪访人ids
     */
    private List<Long> escortAdminIdList;

    /**
     * 子账号信息 手机号+名称
     */
    private String accountInfo;

    private String doorPic;


    /**
     * 变化后Poi
     */
    private String poiAfterChange;

    /**
     * 距离
     */
    private BigDecimal distance;

    /**
     * 运营城市,多选
     */
    private List<Integer> areaNos;

    /**
     * es线索池Id
     */
    private String esId;

    /**
     * 销售评价
     */
    private FollowUpEvaluation followUpEvaluation;

    /**
     * 审批图片多个用逗号隔开
     */
    private String followUpImage;

    private List<BdSalesCityVo> salesCityList;

    /**
     * 飞书门头照门code
     */
    private String feishuHeaderImageCode;

    /**
     * 飞书门店铺code
     */
    private List<String> feishuMerchantImageCode;

    /**
     * 新年是否营业
     */
    private List<String> newYearBusinessTag;
    /**
     * fileName
     */
    private String fileName;

    private String M1;

    private String M2;


    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public String getMcontact() {
        return mcontact;
    }

    public void setMcontact(String mcontact) {
        this.mcontact = mcontact;
    }

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }

}
