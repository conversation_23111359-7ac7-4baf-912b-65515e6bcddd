package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.model.vo.PoiVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售拜访计划
 *
 * <AUTHOR>
 */
@Data
public class BdVisitPlanVO {

    /**
     * 销售拜访计划id
     */
    private Long id;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 拜访日期
     */
    private LocalDate visitDate;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 销售每日目标id
     */
    private Long bdDailyTargetId;

    /**
     * 拜访状态，0：未拜访，1：已拜访
     */
    private Integer visitStatus;

    /**
     * 拜访记录id
     */
    private Integer followUpRecordId;

    /**
     * 拜访记录详情
     */
    private FollowUpRecordVO followUpRecordDetail;

    /**
     * 门店下单状态，0：拜访日未下单，1：拜访日已下单
     */
    private Integer merchantOrderStatus;

    /**
     * 门店poi
     */
    private PoiVO poi;

    /**
     * 门店潜力值
     */
    private Double merchantPotentialValue;

    /**
     * 门店潜力值描述/拜访价值
     */
    private String merchantPotentialValueDesc;

    /**
     * 门店是否在该销售私海
     */
    private Boolean inPrivateSea;

    /**
     * 联系人地址是否有效
     */
    private Boolean validContact;

    /**
     * 联系人地址id
     */
    private Long contactId;

    /**
     * 生命周期
     */
    private String lifecycle;

    /**
     * 高价值客户标签
     */
    private String highValueLabel;

    /**
     * 销售拜访计划指标列表
     */
    private List<BdVisitPlanIndicatorVO> visitPlanIndicators;
}
