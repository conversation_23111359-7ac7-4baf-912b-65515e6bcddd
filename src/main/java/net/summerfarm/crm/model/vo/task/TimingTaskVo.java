package net.summerfarm.crm.model.vo.task;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/11 20:37
 */
@Data
public class TimingTaskVo {
    @ColumnWidth(16)
    @ExcelProperty(value = "门店名称")
    private String mname;
    @ExcelIgnore
    private Integer mId;
    @ColumnWidth(16)
    @ExcelProperty(value = "注册省")
    private String province;
    @ColumnWidth(16)
    @ExcelProperty(value = "注册市")
    private String city;
    @ColumnWidth(16)
    @ExcelProperty(value = "订单号")
    private String orderNo;
    @ColumnWidth(16)
    @ExcelProperty(value = "所属销售")
    private String bdName;
    @ColumnWidth(16)
    @ExcelProperty(value = "到期时间")
    private LocalDate refundTime;
    @ExcelIgnore
    private LocalDateTime startTime;
    @ExcelIgnore
    private LocalDateTime endTime;
    @ExcelIgnore
    private Integer deleteFlag;
    @ColumnWidth(16)
    @ExcelProperty(value = "任务状态")
    private String status;
}
