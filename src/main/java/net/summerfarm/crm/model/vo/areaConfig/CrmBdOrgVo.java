package net.summerfarm.crm.model.vo.areaConfig;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/30 18:51
 */
@Data
@Accessors(chain = true)
public class CrmBdOrgVo {
    private Integer id;
    /**
     * 销售id
     */
    private Integer bdId;
    /**
     * 销售名字
     */
    private String bdName;
    /**
     * 父id
     */
    private Integer parentId;
    /**
     * 上级名称
     */
    private String parentName;
}
