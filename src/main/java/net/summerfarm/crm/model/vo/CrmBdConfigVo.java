package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmBdCity;
import net.summerfarm.crm.model.dto.CrmBdAreaDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CrmBdConfigVo implements Serializable {

    private static final long serialVersionUID = -6072226146375295851L;

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 销售名称
     */
    private String adminName;

    /**
     * bd id
     */
    private Integer adminId;

    /**
     * 销售负责的城市
     */
    private List<CrmBdAreaDTO> areaCity;
    /**
     * 销售城市
     */
    private List<Integer> areaCityIds;

    private List<TableArea> areaCities;


    /**
     * gmv基础值
     */
    private BigDecimal gmvBase;

    /**
     * gmv目标值
     */
    private BigDecimal gmvTarget;

    /**
     * gmv目标值(非AT)
     */
    private BigDecimal gmvTargetExcludeAT;

    /**
     * 月活目标
     */
    private Integer monthOrderTarget;

    /**
     * 私海上限
     */
    private Integer privateSeaLimit;

    /**
     * 客情上限
     */
    private Integer quotaLimit;

    /**
     * 类型
     */
    private Integer type;
    /**
     * 核心客户数基础值
     */
    private Integer coreMerchantAmount;
    /**
     * 所属行政城市
     */
    private String administrativeCity;
    /**
     * 所属行政城市(省份+市)
     */
    private List<String> administrativeCitys;

    /**
     * 自营gmv目标
     */
    private BigDecimal brandGmvTarget;

    /**
     * bd负责城市
     */
    private List<CrmBdCity> bdCities;

    /**
     * bd负责城市
     */
    private String bdCity;

    /**
     * 省市区及联
     */
    private List<TableArea> tableArea;

    /**
     * 上级 id
     */
    private Integer parentId;

    /**
     * 上级 id
     */
    private String parentName;

}
