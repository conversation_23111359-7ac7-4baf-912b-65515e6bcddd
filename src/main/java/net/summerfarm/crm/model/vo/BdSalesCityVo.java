package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmSalesCity;

/**
 * <AUTHOR>
 * @date 2023/8/22 13:41
 */
@Data
public class BdSalesCityVo {
    private Integer id;
    /**
     * 省
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区域
     */
    private String area;

    public static BdSalesCityVo beanToVo(CrmSalesCity city){
        if (city == null) {
            return null;
        }
        BdSalesCityVo bdSalesCityVo = new BdSalesCityVo();
        bdSalesCityVo.setId(city.getId());
        bdSalesCityVo.setProvince(city.getProvince());
        bdSalesCityVo.setCity(city.getCity());
        bdSalesCityVo.setArea(city.getArea());
        return bdSalesCityVo;
    }
}
