package net.summerfarm.crm.model.vo;/**
 * <AUTHOR>
 * @date 2023/1/5 13:40
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.manage.client.wms.dto.res.WarehouseBatchProveRecordDTO;

import java.time.LocalDate;

/**
 * 证明信息
 *
 * <AUTHOR>
 * @date 2023/1/5 13:40
 */
@Data
public class WarehouseBatchProveRecordVO {

    /**
     * id
     */
    private Integer id;

    /**
     * 批次号
     */
    private String batch;

    /**
     * 是否存在报告  0 没有 1 有
     */
    private Integer haveRedisReport;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 质检报告
     */
    private String qualityInspectionReport;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 是否有农药残留报告
     */
    private Integer havepesticideRedisReport;

    /**
     * 农药残留报告图片
     */
    private String pesticideResiduePictures;

    /**
     * sku
     */
    private String sku;

    /**
     * 任务类型编号 9预约入库  10调拨入库 11采购入库
     */
    private Integer type;


    /**
     * 报关证明
     */
    private String customsDeclarationCertificate;

    /**
     * 监管仓证明
     */
    private String supervisionWarehouseCertificate;

    /**
     * 消毒证明
     */
    private String disinfectionCertificate;

    /**
     * 核酸检测
     */
    private String nucleicAcidDetection;

    public static WarehouseBatchProveRecordVO toVO(WarehouseBatchProveRecordDTO dto) {
        WarehouseBatchProveRecordVO warehouseBatchProveRecordVO = new WarehouseBatchProveRecordVO();
        warehouseBatchProveRecordVO.setId(dto.getId());
        warehouseBatchProveRecordVO.setBatch(dto.getBatch());
        warehouseBatchProveRecordVO.setHaveRedisReport(dto.getHaveRedisReport());
        warehouseBatchProveRecordVO.setProductionDate(dto.getProductionDate());
        warehouseBatchProveRecordVO.setQualityDate(dto.getQualityDate());
        warehouseBatchProveRecordVO.setQualityInspectionReport(dto.getQualityInspectionReport());
        warehouseBatchProveRecordVO.setQuantity(dto.getQuantity());
        warehouseBatchProveRecordVO.setHavepesticideRedisReport(dto.getHavepesticideRedisReport());
        warehouseBatchProveRecordVO.setPesticideResiduePictures(dto.getPesticideResiduePictures());
        warehouseBatchProveRecordVO.setSku(dto.getSku());
        warehouseBatchProveRecordVO.setType(dto.getType());
        warehouseBatchProveRecordVO.setCustomsDeclarationCertificate(dto.getCustomsDeclarationCertificate());
        warehouseBatchProveRecordVO.setSupervisionWarehouseCertificate(dto.getSupervisionWarehouseCertificate());
        warehouseBatchProveRecordVO.setDisinfectionCertificate(dto.getDisinfectionCertificate());
        warehouseBatchProveRecordVO.setNucleicAcidDetection(dto.getNucleicAcidDetection());
        return warehouseBatchProveRecordVO;
    }
}
