package net.summerfarm.crm.model.vo;



import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 省心送订单vo
 *
 * <AUTHOR>
 * @date 2022/6/14 1:44
 */
@Data
public class ComfortSendOrderVO {
    /**
     * 实付总额
     */
    private BigDecimal payAmount;
    /**
     * 商户id
     */
    private Long mId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 商品个数
     */
    private Integer pdAmount;
    /**
     * 商品规格
     */
    private String pdWeight;
    /**
     * bdId
     */
    private Integer bdId;
    /**
     * 下单时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime orderTime;
    /**
     * 店铺名称
     */
    private String mname;

    /**
     * 所属bd名称
     */
    private String adminName;


    private String mName;
    private String imageUrl;

    private String size;

    private String mainType;

    public String getMname() {
        return mName;
    }
}
