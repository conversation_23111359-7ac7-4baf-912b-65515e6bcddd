package net.summerfarm.crm.model.vo.weCom;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/26 15:35
 */
@Data
public class WeComGroupTaskResp extends WeChatBaseResp {
    /**
     * 任务列表
     */
    @JSONField(name = "task_list")
    private List<TaskList> taskList;

    @Data
    public class TaskList {
        /**
         * 用户标识
         */
        private String userid;
        /**
         * 发送状态：0-未发送 2-已发送
         */
        private Integer status;
        /**
         * 发送时间
         */
        private Long sendTime;
    }

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static WeComGroupTaskResp fromJson(String json) {
        return JSONUtil.toBean(json, WeComGroupTaskResp.class);
    }

}
