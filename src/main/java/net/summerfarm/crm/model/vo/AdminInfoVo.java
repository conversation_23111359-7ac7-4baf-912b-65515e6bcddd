package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.model.domain.CrmBdConfig;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:01
 */
@Data
public class AdminInfoVo {

    /**
     * 销售激励配置
     */
    private CrmBdConfig crmBdConfig;

    private Integer adminId;
    /**
     * 姓名
     */
    private String adminName;
    /**
     * 指定商品销量
     */
    private Integer desGoodsSales;
    /**
     * 指定商品GMV
     */
    private BigDecimal desGoodsSalesGmv;
    /**
     * 总GMV,下单gmv
     */
    private BigDecimal totalGmv;
    /**
     * 总GMV(非AT),下单gmv(非AT)
     */
    private BigDecimal totalGmvExcludeAT;
    /**
     * GMV目标
     */
    private String gmvCurrentProportion;

    /**
     * 大客户GMV
     */
    private BigDecimal vipGmv;
    /**
     * 大客户门店数
     */
    private Integer vipNum;
    /**
     * 大客户下单门店数
     */
    private Integer vipOrderNum;
    /**
     * 大客户未下单门店数
     */
    private Integer vipNotOrderNum;
    /**
     * 单店GMV
     */
    private BigDecimal singleShopGmv;
    /**
     * 单店客户数
     */
    private Integer singleShopNum;
    /**
     * 单店下单客户数
     */
    private Integer singleShopOrderNum;
    /**
     * 单店未下单客户数
     */
    private Integer singleShopNotOrderNum;
    /**
     * 品牌客户数
     */
    private Integer brandNum;
    /**
     * 活跃客户数
     */
    private Integer monthLiving;
    /**
     * 拜访数(不去重)
     */
    private Integer visitTotal;
    /**
     * 拜访数(去重)
     */
    private Integer visitNum;
    /**
     * 新客户数
     */
    private Integer newAdminNum;
    /**
     * 新客户下单数
     */
    private Integer newAdminOrderNum;
    /**
     * 私海
     */
    private String privateSeaProportion;
    /**
     * 客情
     */
    private String guestSentimentProportion;
    /**
     * 当前GMV
     */
    private BigDecimal gmvCurrent;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = DateUtils.MID_DATE_FORMAT)
    private LocalDate updateTime;
    /**
     * 是否被锁定
     */
    private Integer isDisabled;
    /**
     * gmv基础值,gmv目标值,下单gmv目标
     */
    private BigDecimal gmvTarget;
    /**
     *gmv目标值(非AT)
     */
    private BigDecimal gmvTargetExcludeAT;

    /**
     * 本月核心客户数
     */
    private Integer coreMerchantNum;
    /**
     * 上月核心客户数
     */
    private Integer lastCoreMerchantNum;
    /**
     * 上月gmv
     */
    private BigDecimal lastTotalGmv;
    /**
     * 上月自营gmv
     */
    private BigDecimal lastBrandGmv;
    /**
     * 核心客户净增长数
     */
    private Integer coreMerchantGrowNum;
    /**
     * 距离下一牌级差值
     */
    private Integer gradeDifference;
    /**
     * 鲜果GMV
     */
    private BigDecimal fruitGmv;
    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;
    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;
    /**
     * 自有品牌gmv
     */
    private BigDecimal brandGmv;
    /**
     * 绩效
     */
    private BigDecimal performance;
    /**
     * 普通拜访数
     */
    private Integer ordinaryNum;
    /**
     * 普通上门拜访数
     */
    private Integer dropInVisitNum;
    /**
     * 有效拜访数
     */
    private Integer efficientNum;
    /**
     * 价值拜访数
     */
    private Integer worthNum;
    /**
     * 大客户总业绩
     */
    private BigDecimal merchantTotalGmv;
    /**
     * 大客户账期总业绩
     */
    private BigDecimal creditPaidGmv;
    /**
     * 大客户现结总业绩
     */
    private BigDecimal cashSettlementGmv;
    /**
     * 总业绩(除安佳56217)
     */
    private BigDecimal merchantTotalGmvEx;
    /**
     * 账期总业绩(除安佳56217)
     */
    private BigDecimal creditPaidGmvEx;
    /**
     * 现结总业绩(除安佳56217)
     */
    private BigDecimal cashSettlementGmvEx;
    /**
     * 绩效
     */
    private String performanceStr;
    /**
     * 品类提成
     */
    private BigDecimal categoryAward;
    /**
     * 核心客户净增长数牌级
     */
    private BigDecimal coreMerchantCardLevel;
    /**
     * 配送gmv
     */
    private BigDecimal deliveryGmv;
    /**
     * spu均值
     */
    private BigDecimal spuAverage;
    /**
     * 周期品类提成*配送金额
     */
    private BigDecimal categoryMultiplyGmv;

    private String categoryMultiplyGmvStr;
    /**
     * 单店月活
     */
    private Integer singleMonthLiveNum;
    /**
     * 品牌客户月活
     */
    private Integer vipMonthLiveNum;
    /**
     * 单店spu均值
     */
    private BigDecimal singleSpuAverage;
    /**
     * 品牌客户spu均值
     */
    private BigDecimal vipSpuAverage;
    /**
     * 单店gmv环比
     */
    private BigDecimal singleGmvRingRatio;
    /**
     * 单店月活环比
     */
    private BigDecimal singleMonthRingRatio;
    /**
     * 单店spu环比
     */
    private BigDecimal singleSpuRingRatio;
    /**
     * 大客户gmv环比
     */
    private BigDecimal vipGmvRingRatio;
    /**
     * 大客户月活环比
     */
    private BigDecimal vipMonthRingRatio;
    /**
     * 大客户spu均值环比
     */
    private BigDecimal vipSpuRingRatio;
    /**
     * 月活完成度占比
     */
    private BigDecimal monthlyActivityCompletion;
    /**
     * GMV占比
     */
    private BigDecimal gmvCompletion;
    /**
     * 自营gmv占比
     */
    private BigDecimal brandGmvCompletion;

    /**
     * 本月已配送订单
     */
    private Integer orderShipped;

    /**
     * 普通拉新
     */
    private Integer ordinaryPullNewAmount;
    /**
     * 普通拉新
     */
    private Integer pullNewAmount;

    /**
     * 注册未下单
     */
    private Integer noOrderRegister;

    /**
     * 代售商品gmv/全品类gmv
     */
    private BigDecimal agentGoodsGmv;

    /**
     * saas 总gmv
     */
    private BigDecimal saasTotalGmv;

    /**
     * saas 代仓gmv
     */
    private BigDecimal saasDepotGmv;

    /**
     * saas 自营gmv
     */
    private BigDecimal saasMyselfGmv;

    /**
     * saas鲜沐商品加价gmv
     */
    private BigDecimal saasAddGoodGvm;

    /**
     * saas 品牌数
     */
    private Integer saasBrandCount;

    /**
     * saas 注册门店数
     */
    private Integer saasShopRegisterCount;

    /**
     * saas 下单门店数
     */
    private Integer saasOrderShopCount;

    /**
     * saas 品牌门店总数
     */
    private Integer saasBrandTotalCount;

    /**
     * saas鲜沐gmv
     */
    private BigDecimal saasXianmuGvm;

    /**
     * 月活目标
     */
    private Integer monthOrderTarget;
}
