package net.summerfarm.crm.model.vo.wechat;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/4 17:33
 */
@Data
public class TagGroup {
    @JSONField(name = "group_id")
    private String groupId;

    @JSONField(name = "group_name")
    private String groupName;

    @JSONField(name = "create_time")
    private Long createTime;

    private Integer order;

    private Boolean deleted;

    private List<Tag> tag;
}
