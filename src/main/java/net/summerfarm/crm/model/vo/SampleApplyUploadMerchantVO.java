package net.summerfarm.crm.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/15 10:35
 * @PackageName:net.summerfarm.crm.model.vo
 * @ClassName: SampleApplyUpoladMerchantVO
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class SampleApplyUploadMerchantVO implements Serializable {

    /**
     * 有效数量
     */
    private Integer effectiveCount;

    /**
     * 无效数量
     */
    private Integer invalidCount;

    /**
     * 有效门店信息
     */
    private List<MerchantVO> effectiveMerchantVOS;
}
