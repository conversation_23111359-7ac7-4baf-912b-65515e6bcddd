package net.summerfarm.crm.model.vo.objectiveManagement;

import lombok.Data;

/**
 * 销售拜访目标指标配置VO
 *
 * <AUTHOR>
 */
@Data
public class BdTargetIndicatorConfigVO {

    /**
     * 目标类型
     */
    private Integer targetType;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 业务类型配置
     */
    private String businessTypeConfig;

    /**
     * 指标类型配置
     */
    private String indicatorTypeConfig;

    /**
     * 品类名称配置
     */
    private String categoryNameConfig;

    /**
     * sku配置
     */
    private String skuConfig;

    /**
     * spu配置
     */
    private String spuConfig;

    /**
     * 单位配置
     */
    private String unitConfig;
}
