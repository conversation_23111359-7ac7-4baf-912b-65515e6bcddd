package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;

import java.util.List;

/**
 * 用于模板化配置manage后台销售数据管理页面的看板的每个数据项
 */
@Data
public class BdManagementDashboardItem {

    /**
     * id, 用于前端展示时的唯一标识
     * 也用来定位到domain object的字段名称, 以便从数据库中取值
     */
    String id;

    String description;

    String title;

    String value;

    /**
     * 单位
     */
    String unit;

    /**
     * 组件里的额外数据项, 在前端以副标题的样式展现
     * 通常只包含title + value + unit
     */
    List<BdManagementDashboardItem> subItems;
}
