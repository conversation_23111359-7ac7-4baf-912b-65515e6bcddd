package net.summerfarm.crm.model.vo.saledata;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 销售区域数据VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmAreaDataVO extends CrmDataVO {

    /**
     * 销售区域ID
     */
    private Integer salesAreaId;

    /**
     * 销售区域名称
     */
    private String salesAreaName;

    /**
     * m1陪访数
     */
    private Long accompanyingVisitsM1;
}
