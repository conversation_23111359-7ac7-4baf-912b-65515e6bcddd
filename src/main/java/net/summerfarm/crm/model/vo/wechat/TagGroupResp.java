package net.summerfarm.crm.model.vo.wechat;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/8/4 18:38
 */
@Data
public class TagGroupResp extends WeChatBaseResp{
    @J<PERSON>NField(name = "tag_group")
    private TagGroup tagGroup;

    /**
     * @param json
     * @return {@link WeChatBaseResp}
     */
    public static TagGroupResp fromJson(String json) {
        return JSONUtil.toBean(json, TagGroupResp.class);
    }
}
