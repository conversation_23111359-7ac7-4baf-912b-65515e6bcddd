package net.summerfarm.crm.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/10/20 14:55
 */
@Data
public class DeliveryMerchantVO {

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 点位id
     */
    private Long contactId;

    /**
     * 商户名
     */
    private String merchantName;
    /**
     * 收货地址
     */
    private String deliveryAddress;
    /**
     * 配送GMV
     */
    private BigDecimal deliveryGmv;
    /**
     * spu数
     */
    private Integer spuNum;
    /**
     * 是否达标: 0否1是
     */
    private Integer deliveryUpToStandard;
    /**
     * 未达标spu差额
     */
    private Integer lackSpuNum;
    /**
     * 未达标gmv差额
     */
    private BigDecimal lackDeliveryGmv;
    /**
     * 归属bd名称
     */
    private String bdName;
    /**
     * 待配送订单数
     */
    private Integer /**/deliveryOrderNum;
    /**
     * 主管标识,0否1是
     */
    private Integer saleTag;
    /**
     * 店铺类型 大客户\\大连锁\\小连锁\\单点
     */
    private String size;


    /**
     * 预估收益
     */
    private BigDecimal estimatedIncome;

    /**
     * 达标后预估最低收益
     */
    private BigDecimal minimumIncomeAfterReachingStandard;

    /**
     * 达标后预估最高收益
     */
    private BigDecimal maximumIncomeAfterReachingStandard;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}
