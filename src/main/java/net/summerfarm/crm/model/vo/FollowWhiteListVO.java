package net.summerfarm.crm.model.vo;

import net.summerfarm.crm.model.domain.FollowWhiteList;
import lombok.Data;

/**
 * <AUTHOR> ct
 * create at:  2019/7/23  5:29 PM
 */
@Data
public class FollowWhiteListVO extends FollowWhiteList {

    /**
    * 客户名称
    */
    private String mname;

    /**
    * 客户类型
    */
    private String mSize;

    /**
     * 客户归属id
     */
    private Integer adminId;

    /**
    * 客户归属人名称
    */
    private String adminName;

    /**
    * 城市编号
    */
    private Integer areaNo;

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * 是否在私海
     */
    private Integer reassign;
    /**
     * 商户会员等级
     */
    private Integer grade;
    /**
     * 商户生命周期
     */
    private Integer lifecycle;

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }
}
