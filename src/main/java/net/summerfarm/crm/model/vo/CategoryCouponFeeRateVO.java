package net.summerfarm.crm.model.vo;

import lombok.Data;
import net.summerfarm.crm.enums.CategoryQuotaFeeRateEnum;
import net.summerfarm.crm.enums.ProductCategoryTypeEnum;

import java.math.BigDecimal;

@Data
public class CategoryCouponFeeRateVO {

    /**
     * 客户类型
     * @see CategoryQuotaFeeRateEnum.MerchantType#getCode()
     */
    private Integer merchantType;

    /**
     * 品类类型
     * @see ProductCategoryTypeEnum#getCode()
     */
    private Integer categoryType;

    /**
     * 费比
     */
    private BigDecimal feeRate;
}
