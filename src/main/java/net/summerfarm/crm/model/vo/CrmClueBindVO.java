package net.summerfarm.crm.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description crm_clue_follow
 * @date 2023-05-17
 */
@Data
public class CrmClueBindVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * mid
     */
    @NotNull(message = "bId is  not null")
    private Long bId;

    /**
     * clueId
     */
    @NotNull(message = "clueId is not null")
    private Long clueId;
    /**
     * 客户名称
     */
    @NotNull(message = "customerName is not null")
    private  String customerName;
}