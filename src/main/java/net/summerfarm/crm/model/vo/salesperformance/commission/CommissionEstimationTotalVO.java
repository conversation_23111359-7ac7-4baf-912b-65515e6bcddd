package net.summerfarm.crm.model.vo.salesperformance.commission;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 佣金预估总额VO
 */
@Data
public class CommissionEstimationTotalVO {
    /**
     * BD ID
     */
    private Long bdId;

    /**
     * BD 名称
     */
    private String bdName;

    /**
     * 佣金总额
     */
    private BigDecimal totalCommAmt;

    /**
     * 高价值客户总佣金
     */
    private BigDecimal aCommisstionAmt;

    /**
     * 高价值客户数佣金
     */
    private BigDecimal aCustCommAmt;

    /**
     * 高价值超额spu佣金
     */
    private BigDecimal aSpuCommAmt;

    /**
     * 品类推广总佣金
     */
    private BigDecimal categoryCommAmt;

    /**
     * 存量客户品类佣金
     */
    private BigDecimal oldCustComm;

    /**
     * 新增客户品类佣金
     */
    private BigDecimal newCustComm;
}
