package net.summerfarm.crm.model.vo;

import cn.hutool.core.util.StrUtil;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.model.domain.VisitPlan;
import lombok.Data;
import net.summerfarm.crm.model.dto.ContactDto;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 跟进计划vo
 * <AUTHOR> ct
 * create at:  2019/8/2  10:03 AM
 */
@Data
public class VisitPlanVO extends VisitPlan {

    /**
    * 所属bd名称
    */
    private String adminName;

    /**
    * 联系人地址
    */
    private String address;

    /**
    * 联系人手机号
    */
    private String phone;

    /**
    * 客户类型
    */
    private String mSize;

    /**
    * 会员等级
    */
    private Integer grade;

    /**
     * 客户名称
     */
    private String mname;

    /**
    * 跟进方式
    */
    private String followUpWay;

    /**
    * 跟进图片
    */
    private String followUpPic;

    /**
    * 跟进记录状态:0未跟进，1已跟进，2已跟进且下单，3联系不上，4放弃跟进,9重置
    */
    @InRange(rangeNums = {0,1,2,3,4})
    private Integer fStatus;

    /**
    * 优先级
    */
    private Integer priority;

    /**
    * 跟进情况描述
    */
    @NotNull(message = "fur.record.null", groups = {Update.class})
    private String condition;


    /**
    * 开始时间*
    */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
    * 结束时间
    */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    /**
    * 城市编号
    */
    private Integer areaNo;

    private String contact;

    /**
     * 前端调用位置
     */
    private Integer vType;

    private LocalDate date;
    /**
     * 商户定位
     */
    private String poiNote;
    /**
     * 商户经纬度信息
     */
    private PoiVO poiVO;
    /**
     * 陪访主管姓名
     */
    private String escortAdminName;
    /**
     * 陪访人id
     */
    private Integer escortAdminId;

    /**
     * 拜访记录id
     */
    private Integer followUpRecordId;

    private String mainType;
    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;


    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public Integer getfStatus() {
        return fStatus;
    }

    public void setfStatus(Integer fStatus) {
        this.fStatus = fStatus;
    }

    public Integer getvType() {
        return vType;
    }

    public void setvType(Integer vType) {
        this.vType = vType;
    }

    public void setByContact(ContactDto contactDto) {
        if (contactDto == null) {
            return;
        }
        String address = contactDto.getProvince() + contactDto.getCity() + contactDto.getArea() + contactDto.getAddress() + contactDto.getHouseNumber();
        this.address = StrUtil.replace(address, "null", "");
        this.phone = contactDto.getPhone();
        this.contact = contactDto.getContact();
        this.poiNote=contactDto.getPoiNote();
    }
}
