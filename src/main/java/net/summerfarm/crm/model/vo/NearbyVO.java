package net.summerfarm.crm.model.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-01-06
 * @description
 */
@Data
public class NearbyVO {
    private String mId;

    private String esId;

    private Long contactId;

    private String mname;

    private String contact;

    private String province;

    private String city;

    private String area;

    private String address;

    private String phone;

    private PoiVO poi;

    /**
     * 是否是公海客户
     */
    private Boolean openSeaFlag;

    /**
     * 是否是我的客户
     */
    private Boolean myMerchant;

    /**
     * 是否注册
     */
    private Boolean registerFlag;

    /**
     * 是否生成地推码
     */
    private Boolean leadFlag;

    /**
     * 距离（单位：米）
     */
    private Integer distance;
    /**
     *
     */
    private String ischain;

    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 餐饮类型
     */
    private String cuisineType;
    /**
     * 店龄
     */
    private Long shopAge;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 当月拜访数
     */
    private Integer visitCount;
    /**
     * 网评热度
     */
    private String popularityIndex;
    /**
     * 相关度评分
     */
    private transient Float score;
    /**
     * 商场
     */
    private String shoppingMall;
    /**
     * 商圈
     */
    private String shopRegion;

    /**
     * 客户详情页面权限控制,0简略,1详细
     */
    private Integer roleTag;

    /**
     * 业务线. 0: 鲜沐. 1: pop
     */
    private Integer businessLine;

    /**
     * 今日是否已拜访
     */
    private Boolean todayVisited;

    /**
     * 生命周期
     */
    private String lifecycle;

    /**
     * 高价值客户标签
     */
    private String highValueLabel;

    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }
}
