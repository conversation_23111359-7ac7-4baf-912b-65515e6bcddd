package net.summerfarm.crm.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.manage.client.wms.dto.req.WarehouseQueryReq;
import net.summerfarm.manage.client.wms.dto.res.ProductsDTO;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ProductsVO implements Serializable {

    /**
     * 商品id
     */
    private Long pdId;

    /**
     * 商品名称
     */
    private String pdName;

    public static ProductsVO toVO(ProductsDTO dto) {
        ProductsVO productsVO = new ProductsVO();
        productsVO.setPdId(dto.getPdId());
        productsVO.setPdName(dto.getPdName());
        return productsVO;
    }


}
