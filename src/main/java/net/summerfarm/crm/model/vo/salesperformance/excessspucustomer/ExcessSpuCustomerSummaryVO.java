package net.summerfarm.crm.model.vo.salesperformance.excessspucustomer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 超标SPU客户汇总数据VO
 * 销售维度汇总
 */
@Data
public class ExcessSpuCustomerSummaryVO {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 客户数
     */
    private Integer customerCount;

    /**
     * 超标SPU数
     */
    private Integer excessSpuCount;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;
}
