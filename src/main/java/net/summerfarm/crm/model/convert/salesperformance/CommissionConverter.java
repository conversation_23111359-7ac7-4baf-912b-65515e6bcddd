package net.summerfarm.crm.model.convert.salesperformance;

import net.summerfarm.crm.model.domain.BdMtdComm;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.commission.CommissionEstimationTotalVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 佣金相关数据转换器
 */
@Mapper
public interface CommissionConverter {

    CommissionConverter INSTANCE = Mappers.getMapper(CommissionConverter.class);

    /**
     * 将 BdMtdComm 转换为 CommissionEstimationTotalVO
     *
     * @param bdMtdComm 佣金数据
     * @return 佣金预估总额VO
     */
    @Mapping(target = "ASpuCommAmt", source = "ASpuCommAmt")
    @Mapping(target = "ACustCommAmt", source = "ACustCommAmt")
    @Mapping(target = "ACommisstionAmt", source = "ACommisstionAmt")
    @Mapping(target = "bdId", source = "lastBdId")
    @Mapping(target = "bdName", source = "lastBdName")
    CommissionEstimationTotalVO bdMtdCommToCommissionEstimationTotalVO(BdMtdComm bdMtdComm);

    /**
     * 将 BdMtdComm 列表转换为 CommissionEstimationTotalVO 列表
     *
     * @param bdMtdCommList 佣金数据列表
     * @return 佣金预估总额VO列表
     */
    List<CommissionEstimationTotalVO> bdMtdCommListToCommissionEstimationTotalVOList(List<BdMtdComm> bdMtdCommList);

    /**
     * 将 BdMtdComm 转换为 CommissionEstimationDetailVO
     *
     * @param bdMtdComm 佣金数据
     * @return 佣金预估明细VO
     */
    @Mapping(target = "bdId", source = "lastBdId")
    @Mapping(target = "bdName", source = "lastBdName")
    @Mapping(target = "highValueCustomerBonus", expression = "java(calculateHighValueCustomerBonus(bdMtdComm))")
    @Mapping(target = "highValueCustomerCount", source = "ACustCnt")
    @Mapping(target = "excessSpuCount", source = "moreThanSpuCnt")
    @Mapping(target = "spuRewardPerCustomer", expression = "java(calculateSpuRewardPerCustomer(bdMtdComm))")
    @Mapping(target = "categoryPromotionBonus", source = "categoryCommAmt")
    @Mapping(target = "totalScoreNum", expression = "java((int) Math.round(bdMtdComm.getTotalScoreNum()))")
    CommissionEstimationDetailVO bdMtdCommToCommissionEstimationDetailVO(BdMtdComm bdMtdComm);

    /**
     * 计算每个客户的SPU奖励金额
     * @param bdMtdComm 佣金数据
     * @return 四舍五入后的整数值
     */
    default BigDecimal calculateSpuRewardPerCustomer(BdMtdComm bdMtdComm) {
        if (bdMtdComm == null || bdMtdComm.getMoreThanSpuCnt() == null || bdMtdComm.getMoreThanSpuCnt() == 0) {
            return new BigDecimal(0);
        }
        return bdMtdComm.getASpuCommAmt()
                .divide(new java.math.BigDecimal(bdMtdComm.getMoreThanSpuCnt()), 0, java.math.RoundingMode.HALF_UP);
    }
    
    /**
     * 计算高价值客户奖励金额
     * @param bdMtdComm 佣金数据
     * @return 四舍五入后的整数值
     */
    default BigDecimal calculateHighValueCustomerBonus(BdMtdComm bdMtdComm) {
        if (bdMtdComm == null || bdMtdComm.getACommisstionAmt() == null 
                || bdMtdComm.getACustCnt() == null
                || bdMtdComm.getBdPerformanceRate() == null || bdMtdComm.getBdPerformanceRate() == 0.0) {
            return BigDecimal.ZERO;
        }
        
        try {
            BigDecimal customerCount = new BigDecimal(bdMtdComm.getACustCnt());
            if (customerCount.compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            }
            
            BigDecimal rate = BigDecimal.valueOf(bdMtdComm.getBdPerformanceRate());
            if (rate.compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            }
            
            BigDecimal result = bdMtdComm.getACustCommAmt()
                    .divide(customerCount, 10, RoundingMode.HALF_UP)
                    .divide(rate, 0, RoundingMode.HALF_UP);
                    
            return result.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : result;
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
}
