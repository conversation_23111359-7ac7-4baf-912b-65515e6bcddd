package net.summerfarm.crm.model.convert.salesperformance;

import net.summerfarm.crm.model.domain.BdMtdComm;
import net.summerfarm.crm.model.domain.CustMtdPerformance;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.excessspucustomer.ExcessSpuCustomerSummaryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 超标SPU客户数据转换器
 */
@Mapper
public interface ExcessSpuCustomerConverter {

    ExcessSpuCustomerConverter INSTANCE = Mappers.getMapper(ExcessSpuCustomerConverter.class);

    /**
     * 将 BdMtdComm 转换为 ExcessSpuCustomerSummaryVO
     *
     * @param bdMtdComm 销售维度数据
     * @return 超标SPU客户汇总VO
     */
    @Mapping(target = "bdId", source = "lastBdId")
    @Mapping(target = "bdName", source = "lastBdName")
    @Mapping(target = "customerCount", source = "moreThanSpuCustCnt")
    @Mapping(target = "excessSpuCount", source = "moreThanSpuCnt")
    @Mapping(target = "rewardAmount", source = "ASpuCommAmt")
    ExcessSpuCustomerSummaryVO bdMtdCommToExcessSpuCustomerSummaryVO(BdMtdComm bdMtdComm);

    /**
     * 将 BdMtdComm 列表转换为 ExcessSpuCustomerSummaryVO 列表
     *
     * @param bdMtdComms 销售维度数据列表
     * @return 超标SPU客户汇总VO列表
     */
    List<ExcessSpuCustomerSummaryVO> bdMtdCommListToExcessSpuCustomerSummaryVOList(List<BdMtdComm> bdMtdComms);

    /**
     * 将 CustMtdPerformance 转换为 ExcessSpuCustomerDetailVO
     *
     * @param custMtdPerformance 客户维度数据
     * @return 超标SPU客户详情VO
     */
    @Mapping(target = "MId", source = "custId")
    @Mapping(target = "mname", source = "lastCustName")
    @Mapping(target = "rewardAmount", source = "moreThanSpuComm")
    @Mapping(target = "excessSpuCount", source = "moreThanSpuCnt")
    ExcessSpuCustomerDetailVO custMtdPerformanceToExcessSpuCustomerDetailVO(CustMtdPerformance custMtdPerformance);
}
