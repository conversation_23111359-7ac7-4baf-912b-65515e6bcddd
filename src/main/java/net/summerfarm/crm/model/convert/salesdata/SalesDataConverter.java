package net.summerfarm.crm.model.convert.salesdata;

import net.summerfarm.crm.model.domain.CrmBdTodayHourGmv;
import net.summerfarm.crm.model.domain.CrmCityTodayHourGmv;
import net.summerfarm.crm.model.vo.saledata.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


@Mapper
public interface SalesDataConverter {

    SalesDataConverter INSTANCE = Mappers.getMapper( SalesDataConverter.class );

    @Mapping(target = "dlvTimingAnchorRealTotalGmv", source = "dlvTimingAnchoRealTotalGmv")
    @Mapping(target = "dlvAnchorRealTotalGmv", source = "dlvAnchoRealTotalGmv")
    @Mapping(target = "anchorRealTotalGmv", source = "anchoRealTotalGmv")
    CrmBdDataVO CrmBdTodayHourGmvToCrmBdDataVO(CrmBdTodayHourGmv crmBdTodayHourGmv);

    CrmCityDataVO CrmCityTodayHourGmvToCrmCityDataVO(CrmCityTodayHourGmv crmCityTodayHourGmv);

    @Mapping(target = "salesAreaName", ignore = true)
    @Mapping(target = "salesAreaId", ignore = true)
    CrmAreaDataVO CrmCityTodayHourGmvToCrmAreaDataVO(CrmCityTodayHourGmv crmCityTodayHourGmv);
}
