package net.summerfarm.crm.model.convert;

import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.CouponEnum;
import net.summerfarm.crm.model.domain.CategoryCouponQuotaChange;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolLogDto;
import net.summerfarm.crm.model.vo.crmCouponExpensePool.CrmCouponExpensePoolLogoVo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CrmCouponExpensePoolLogDtoConverter {
    private CrmCouponExpensePoolLogDtoConverter() {
        // 无需实现
    }

    public static List<CrmCouponExpensePoolLogoVo> toCrmCouponExpensePoolLogoVoList(List<CrmCouponExpensePoolLogDto> crmCouponExpensePoolLogDtoList) {
        if (crmCouponExpensePoolLogDtoList == null) {
            return Collections.emptyList();
        }
        List<CrmCouponExpensePoolLogoVo> crmCouponExpensePoolLogoVoList = new ArrayList<>();
        for (CrmCouponExpensePoolLogDto crmCouponExpensePoolLogDto : crmCouponExpensePoolLogDtoList) {
            crmCouponExpensePoolLogoVoList.add(toCrmCouponExpensePoolLogoVo(crmCouponExpensePoolLogDto));
        }
        return crmCouponExpensePoolLogoVoList;
    }

    public static CrmCouponExpensePoolLogoVo toCrmCouponExpensePoolLogoVo(CrmCouponExpensePoolLogDto crmCouponExpensePoolLogDto) {
        if (crmCouponExpensePoolLogDto == null) {
            return null;
        }
        CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo = new CrmCouponExpensePoolLogoVo();
        crmCouponExpensePoolLogoVo.setRemark(crmCouponExpensePoolLogDto.getRemark());
        crmCouponExpensePoolLogoVo.setAdminName(crmCouponExpensePoolLogDto.getAdminName());
        crmCouponExpensePoolLogoVo.setType(crmCouponExpensePoolLogDto.getType());
// Not mapped TO fields:
// toAdminName
// amount
// localDateTime
// Not mapped FROM fields:
// changeExpense
// createTime
// autoApprove
// productRange
// costLimit
        return crmCouponExpensePoolLogoVo;
    }

    public static List<CrmCouponExpensePoolLogDto> toCrmCouponExpensePoolLogDtoList(List<CrmCouponExpensePoolLogoVo> crmCouponExpensePoolLogoVoList) {
        if (crmCouponExpensePoolLogoVoList == null) {
            return Collections.emptyList();
        }
        List<CrmCouponExpensePoolLogDto> crmCouponExpensePoolLogDtoList = new ArrayList<>();
        for (CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo : crmCouponExpensePoolLogoVoList) {
            crmCouponExpensePoolLogDtoList.add(toCrmCouponExpensePoolLogDto(crmCouponExpensePoolLogoVo));
        }
        return crmCouponExpensePoolLogDtoList;
    }

    public static CrmCouponExpensePoolLogDto toCrmCouponExpensePoolLogDto(CrmCouponExpensePoolLogoVo crmCouponExpensePoolLogoVo) {
        if (crmCouponExpensePoolLogoVo == null) {
            return null;
        }
        CrmCouponExpensePoolLogDto crmCouponExpensePoolLogDto = new CrmCouponExpensePoolLogDto();
        crmCouponExpensePoolLogDto.setAdminName(crmCouponExpensePoolLogoVo.getAdminName());
        crmCouponExpensePoolLogDto.setRemark(crmCouponExpensePoolLogoVo.getRemark());
        crmCouponExpensePoolLogDto.setType(crmCouponExpensePoolLogoVo.getType());
        crmCouponExpensePoolLogDto.setCreateTime(crmCouponExpensePoolLogoVo.getLocalDateTime());
        crmCouponExpensePoolLogDto.setChangeExpense(crmCouponExpensePoolLogoVo.getAmount());
// Not mapped TO fields:
// changeExpense
// createTime
// autoApprove
// productRange
// costLimit
// Not mapped FROM fields:
// toAdminName
// amount
// localDateTime
        return crmCouponExpensePoolLogDto;
    }
    //CrmCouponExpensePoolLogDto

         //   CrmCouponExpensePoolLogoVo

    public static CrmCouponExpensePoolLogDto toCrmCouponExpensePoolLogDto(CategoryCouponQuotaChange categoryCouponQuotaChange) {
        if (categoryCouponQuotaChange == null) {
            return null;
        }
        CrmCouponExpensePoolLogDto crmCouponExpensePoolLogDto = new CrmCouponExpensePoolLogDto();
        crmCouponExpensePoolLogDto.setAdminName(categoryCouponQuotaChange.getAdminName());
        crmCouponExpensePoolLogDto.setRemark(categoryCouponQuotaChange.getRemark());
        crmCouponExpensePoolLogDto.setType(CouponEnum.CouponQuotaChangeType.getChangeType(categoryCouponQuotaChange.getType()));
        crmCouponExpensePoolLogDto.setCreateTime(DateUtils.date2LocalDateTime(categoryCouponQuotaChange.getCreateTime()));
        crmCouponExpensePoolLogDto.setChangeExpense(categoryCouponQuotaChange.getQuota());
        crmCouponExpensePoolLogDto.setPoolId(categoryCouponQuotaChange.getPoolId());
        crmCouponExpensePoolLogDto.setChangeCreateId(categoryCouponQuotaChange.getCreator());
// Not mapped TO fields:
// changeExpense
// createTime
// autoApprove
// productRange
// costLimit
// Not mapped FROM fields:
// toAdminName
// amount
// localDateTime
        return crmCouponExpensePoolLogDto;
    }
}
