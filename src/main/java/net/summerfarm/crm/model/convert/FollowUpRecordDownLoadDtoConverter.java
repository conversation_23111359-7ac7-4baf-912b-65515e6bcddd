package net.summerfarm.crm.model.convert;

import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.FollowRecordEnum;
import net.summerfarm.crm.model.dto.FollowUpRecordDownLoadDto;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;

public class FollowUpRecordDownLoadDtoConverter {
    public static FollowUpRecordDownLoadDto convert(FollowUpRecordVO followUpRecordVO) {
        FollowUpRecordDownLoadDto followUpRecordDownLoadDto = new FollowUpRecordDownLoadDto();
        followUpRecordDownLoadDto.setAdminName(followUpRecordVO.getAdminName());
        followUpRecordDownLoadDto.setMname(followUpRecordVO.getMname());
        followUpRecordDownLoadDto.setAreaName(followUpRecordVO.getAreaName());
        followUpRecordDownLoadDto.setFollowUpWay(followUpRecordVO.getFollowUpWay());
        followUpRecordDownLoadDto.setAddTimeStr(DateUtils.date2String(followUpRecordVO.getAddTime(), BaseDateUtils.LONG_DATE_FORMAT));
        followUpRecordDownLoadDto.setVisitObjectiveStr(FollowRecordEnum.VisitObjective.getName(followUpRecordVO.getVisitObjective()));
        followUpRecordDownLoadDto.setEscortAdminId(followUpRecordVO.getEscortAdminId());
        followUpRecordDownLoadDto.setKpId(followUpRecordVO.getKpId());
        followUpRecordDownLoadDto.setAdminId(followUpRecordVO.getAdminId());
        followUpRecordDownLoadDto.setMerchantId(followUpRecordVO.getmId());
        //followUpRecordDownLoadDto.setEscortAdminName();
        //followUpRecordDownLoadDto.setKpName();
        String condition = followUpRecordVO.getCondition();
        HashMap<String, String> map = handleCondition(condition);
        followUpRecordDownLoadDto.setBusinessMsg(map.get("客户生意"));
        followUpRecordDownLoadDto.setUseProductMsg(map.get("门店使用产品信息"));
        followUpRecordDownLoadDto.setOrdersMsg(map.get("下单情况"));
        followUpRecordDownLoadDto.setCIMsg(map.get("竞对信息"));
        followUpRecordDownLoadDto.setAfterSaleMsg(map.get("售后情况"));
        followUpRecordDownLoadDto.setSendSaleMsg(map.get("配送情况"));
        followUpRecordDownLoadDto.setLostCustomerMsg(map.get("客户流失原因"));
        followUpRecordDownLoadDto.setOthers(map.get("其他"));
        followUpRecordDownLoadDto.setLocation(followUpRecordVO.getLocation());
        followUpRecordDownLoadDto.setVisitAll(condition);
        followUpRecordDownLoadDto.setEscortVisitPlanId(followUpRecordVO.getEscortVisitPlanId());
        followUpRecordDownLoadDto.setVisitPlanId(followUpRecordVO.getVisitPlanId());
        return followUpRecordDownLoadDto;
    }

    private static HashMap<String, String> handleCondition(String condition) {
        HashMap<String, String> map = new HashMap<>();
        if (StringUtils.isEmpty(condition)) {
            return map;
        }
        Arrays.asList(condition.split("；")).forEach(s -> {
            String[] split = s.split("-");
            if (split.length > 1) {
                map.put(split[0], split[1]);
            }
        });
        return map;
    }
}
