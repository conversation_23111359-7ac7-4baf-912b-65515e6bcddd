package net.summerfarm.crm.model.convert.salesdata;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.util.PageInfoConverter;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.mapper.manage.ConfigMapper;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.vo.saledata.BdManagementDashboardItem;
import net.summerfarm.crm.model.vo.saledata.CRMDashboardItem;
import net.summerfarm.crm.model.vo.saledata.CRMDashboardModule;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * DashboardModuleConverter
 * <p>
 * 本类用于根据配置（如CRM_BD_DASHBOARD_CONFIG）将任意数据对象（如VO、DTO等）动态转换为CRM端数据看板模块（CRMDashboardModule）。
 * 支持模块化、可配置化的数据展示，便于前端灵活组装多样化的看板页面。
 * 技术文档请 <a href="https://summerfarm.feishu.cn/wiki/OH3TwREAYi3MmOkgDbScqBpvnhc">参考</a>
 * <p>
 * 【核心用途】
 * - 通过配置中心（Config表）维护各类看板模块的结构与字段（如title、items、id、layoutHint等），无需硬编码即可扩展/调整模块内容。
 * - 支持单模块和多模块（分页）转换，适配不同业务场景（如区域业绩、城市维度、门店维度等）。
 * - 常配合ConfigValueEnum中的配置项key（如CRM_BD_DASHBOARD_CONFIG）使用。
 * <p>
 * 【配置格式说明】
 * 配置内容可以是JSON数组（多模块）或单个JSON对象（单模块）：
 * <p>
 * 1. 多模块（module数组）示例：
 * [
 * {
 * "title": "配送数据",
 * "items": [
 * {"title": "本月已配送订单", "id": "orderShipped", "layoutHint": 3},
 * {"title": "本月配送GMV(元)", "id": "deliveryGmv", "value": 0, "layoutHint": 3}
 * ]
 * },
 * ...
 * ]
 * <p>
 * 2. 单模块（单module对象）示例：
 * {
 * "description": "以下数据都按小时级频率更新",
 * "items": [
 * {"id": "orderGmv", "title": "下单GMV", "value": 0, "layoutHint": 3},
 * {"id": "orderMerchant", "title": "下单客户数", "value": "/", "layoutHint": 3},
 * {"id": "deliveryGmv", "title": "履约实付GMV", "value": 0, "layoutHint": 3},
 * {"id": "fruitGmv", "title": "鲜果GMV", "value": 0, "layoutHint": 3},
 * {"id": "dairyGmv", "title": "乳制品GMV(非AT)", "value": 0, "layoutHint": 3},
 * {"id": "nonDairyGmv", "title": "非乳制品GMV", "value": 0, "layoutHint": 3},
 * {"id": "agentGmv", "title": "全品类GMV", "value": 0, "layoutHint": 3},
 * {"id": "rewardGmv", "title": "奖励SKUGMV", "value": 0, "layoutHint": 3},
 * {"id": "visitNum", "title": "新增拜访数", "value": "/", "layoutHint": 3}
 * ]
 * }
 * <p>
 * 其中每个module可包含：
 * - title: 模块标题
 * - description: 模块描述（可选）
 * - items: 指标项数组，每项包含title、id、value、layoutHint等
 * - 其他自定义字段
 * <p>
 * 【如何使用】
 * 1. 在Controller层注入本类，如：@Resource private DashboardModuleConverter dashboardModuleConverter;
 * 2. 调用如下方法进行转换：
 * - convertToModulePageInfo(pageInfo, configKey, idExtractor, nameExtractor): 批量分页转换，适用于多module配置
 * - convertToCRMDashboardModule(data, configKey, customValue, customTitle): 单个对象转换，适用于单module配置
 * 3. configKey需传入ConfigValueEnum中定义的配置项key（如CRM_BD_DASHBOARD_CONFIG）。
 * 4. 确保配置中心已正确维护对应的JSON结构，字段id需与业务数据字段一一对应。
 * <p>
 * 【示例】
 * 1. 多模块：
 * [
 * {
 * "title": "配送数据",
 * "items": [
 * {"title": "本月已配送订单", "id": "orderShipped", "layoutHint": 3},
 * {"title": "本月配送GMV(元)", "id": "deliveryGmv", "value": 0, "layoutHint": 3}
 * ]
 * },
 * ...
 * ]
 * 2. 单模块：
 * {
 * "description": "以下数据都按小时级频率更新",
 * "items": [
 * {"id": "orderGmv", "title": "下单GMV", "value": 0, "layoutHint": 3},
 * ...
 * ]
 * }
 * 调用convertToCRMDashboardModule(data, ConfigValueEnum.CRM_BD_PERFORMANCE_MODULE_CONFIG, ...)，即可自动映射data中的字段到配置中的id，实现动态看板。
 * <p>
 * 【注意事项】
 * - 配置中的id字段需与数据对象的getter方法一致，否则无法自动填充值。
 * - 若需自定义title/value，可通过参数传入覆盖默认配置。
 * <p>
 * 详细用法可参考以下相关Controller。
 *
 * @see net.summerfarm.crm.controller.SalesPerformanceController
 */
@Slf4j
@Service
public class DashboardModuleConverter {

    @Resource
    private ConfigMapper configMapper;

    /**
     * 通用方法：将分页数据转换为CRMDashboardModule分页数据
     *
     * @param pageInfo      原始分页数据
     * @param configKey     配置项key
     * @param idExtractor   ID提取函数（用于获取对象中的ID值）
     * @param nameExtractor 名称提取函数（用于获取对象中的名称值）
     * @param <T>           原始数据类型
     * @param <I>           ID类型
     * @return 转换后的CRMDashboardModule分页数据
     */
    public <T, I> PageInfo<CRMDashboardModule> convertToModulePageInfo(
            PageInfo<T> pageInfo,
            ConfigValueEnum configKey,
            Function<T, I> idExtractor,
            Function<T, String> nameExtractor) {

        if (pageInfo == null || pageInfo.getList() == null) {
            return new PageInfo<>();
        }

        return PageInfoConverter.toPageResp(pageInfo,
                item -> convertToCRMDashboardModule(
                        item,
                        configKey,
                        idExtractor.apply(item) != null ? String.valueOf(idExtractor.apply(item)) : null,
                        nameExtractor.apply(item)));
    }

      /**
     * 通用方法：将分页数据转换为CRMDashboardModule分页数据
     *
     * @param pageInfo              原始分页数据
     * @param configKeyExtractor     配置项key提取函数（用于根据数据动态获取配置项key）
     * @param idExtractor           ID提取函数（用于获取对象中的ID值）
     * @param nameExtractor        名称提取函数（用于获取对象中的名称值）
     * @param <T>                   原始数据类型
     * @param <I>                   ID类型
     * @return 转换后的CRMDashboardModule分页数据
     */
    public <T, I> PageInfo<CRMDashboardModule> convertToModulePageInfo(
            PageInfo<T> pageInfo,
            Function<T, ConfigValueEnum> configKeyExtractor,
            Function<T, I> idExtractor,
            Function<T, String> nameExtractor) {

        if (pageInfo == null || pageInfo.getList() == null) {
            return new PageInfo<>();
        }

        return PageInfoConverter.toPageResp(pageInfo,
                item -> convertToCRMDashboardModule(
                        item,
                        configKeyExtractor.apply(item),
                        idExtractor.apply(item) != null ? String.valueOf(idExtractor.apply(item)) : null,
                        nameExtractor.apply(item)));
    }

    /**
     * 通用方法：将列表数据转换为CRMDashboardModule列表
     *
     * @param dataList       原始数据列表
     * @param configKey      配置项key
     * @param valueExtractor value提取函数（用于获取对象中的value值）
     * @param titleExtractor title提取函数（用于获取对象中的title值）
     * @param <T>            原始数据类型
     * @param <I>            value类型
     * @return 转换后的CRMDashboardModule列表
     */
    public <T, I> List<CRMDashboardModule> convertToModuleList(
            List<T> dataList,
            ConfigValueEnum configKey,
            Function<T, I> valueExtractor,
            Function<T, String> titleExtractor) {

        if (CollectionUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream()
                .map(item -> convertToCRMDashboardModule(
                        item,
                        configKey,
                        valueExtractor.apply(item) != null ? String.valueOf(valueExtractor.apply(item)) : null,
                        titleExtractor.apply(item)))
                .collect(Collectors.toList());
    }

    /**
     * 通用方法：将列表数据转换为CRMDashboardModule列表
     *
     * @param dataList            原始数据列表
     * @param configKeyExtractor   配置项key提取函数（用于根据数据动态获取配置项key）
     * @param valueExtractor       value提取函数（用于获取对象中的value值）
     * @param titleExtractor       title提取函数（用于获取对象中的title值）
     * @param <T>                 原始数据类型
     * @param <I>                 value类型
     * @return 转换后的CRMDashboardModule列表
     */
    public <T, I> List<CRMDashboardModule> convertToModuleList(
            List<T> dataList,
            Function<T, ConfigValueEnum> configKeyExtractor,
            Function<T, I> valueExtractor,
            Function<T, String> titleExtractor) {

        if (CollectionUtil.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream()
                .map(item -> convertToCRMDashboardModule(
                        item,
                        configKeyExtractor.apply(item),
                        valueExtractor.apply(item) != null ? String.valueOf(valueExtractor.apply(item)) : null,
                        titleExtractor.apply(item)))
                .collect(Collectors.toList());
    }

    /**
     * 把一个类转换成CRM端数据看板模块
     * 用于转换配置项里一次只配置了一个module的情况
     *
     * @param data        数据来源
     * @param configKey   配置项key
     * @param customValue 模块的值
     * @param customTitle 自定义标题
     * @return 数据看板模块
     */
    public CRMDashboardModule convertToCRMDashboardModule(Object data, ConfigValueEnum configKey, String customValue, String customTitle) {
        String config = getConfig(configKey);
        CRMDashboardModule module = JSONObject.parseObject(config, CRMDashboardModule.class);

        this.fillValuesForModule(data, module);

        // 自定义标题和值(如果有)
        if (StringUtils.isNotBlank(customTitle)) {
            module.setTitle(customTitle);
        }
        if (StringUtils.isNotBlank(customValue)) {
            module.setValue(customValue);
        }

        return module;
    }


    /**
     * 把一个类转换成CRM端数据看板模块列表
     * 用于转换配置项里一次配置了多个module的情况
     *
     * @param data      数据来源
     * @param configKey 配置项key. 配置应该是一个json数组,每个元素是一个CRMDashboardModule
     * @return 数据看板模块列表
     */
    public List<CRMDashboardModule> convertToCRMDashboardModuleList(Object data, ConfigValueEnum configKey) {
        String config = getConfig(configKey);
        List<CRMDashboardModule> modules = JSONObject.parseArray(config, CRMDashboardModule.class);

        // 遍历模块,把数据填充进去
        modules.forEach(module -> this.fillValuesForModule(data, module));

        return modules;
    }


    /**
     * 把一个类转换成Manage端BD管理页的数据看板模块
     *
     * @param data      数据来源
     * @param configKey 配置项key. 应该是一个json数组,每个元素是一个BdManagementDashboardItem
     * @return 数据看板模块列表
     */
    public List<BdManagementDashboardItem> convertToBdManagementDashboardItem(Object data, ConfigValueEnum configKey) {
        String config = this.getConfig(configKey);
        List<BdManagementDashboardItem> items = JSONObject.parseArray(config, BdManagementDashboardItem.class);

        // 遍历模块,把数据填充进去
        if (data == null) {
            return items;
        }
        for (BdManagementDashboardItem item : items) {
            this.setValueFromData(item::setValue, data, item.getId()); // 设置值
            Optional.ofNullable(item.getSubItems()).ifPresent(subItems -> {
                subItems.forEach(subItem -> {
                    this.setValueFromData(subItem::setValue, data, subItem.getId());
                });
            });
        }

        return items;
    }


    private String getConfig(ConfigValueEnum configKey) {
        Config config = configMapper.selectOne(configKey.getKey());
        if (config == null) {
            throw new BizException("模板配置不存在: " + configKey.getKey());
        }
        return config.getValue();
    }


    private void fillValuesForModule(Object data, CRMDashboardModule module) {
        if (data == null) {
            return;
        }
        this.setValueFromData(module::setValue, data, module.getId()); // 设置模块值

        for (CRMDashboardItem item : module.getItems()) {
            this.setValueFromData(item::setValue, data, item.getId()); // 设置item值
            item.setChangeRate((BigDecimal) getFieldValue(data, item.getChangeRateFieldId()));
        }
    }

    private void setValueFromData(Consumer<String> setValue, Object data, String fieldId) {
        Optional.ofNullable(this.getFieldValue(data, fieldId))
                .map(this::convertToString)
                .ifPresent(setValue);
    }

    private Object getFieldValue(Object data, String fieldName) {
        if (data == null || StringUtils.isBlank(fieldName)) {
            return null;
        }
        try {
            PropertyDescriptor pd = new PropertyDescriptor(fieldName, data.getClass());
            Method getter = pd.getReadMethod();
            return getter.invoke(data);
        } catch (IntrospectionException | InvocationTargetException | IllegalAccessException e) {
            log.info("获取字段值失败: {}", fieldName);
            return null;
        }
    }

    /**
     * 把一个对象转换成字符串.
     * 如果对象是BigDecimal,则转换成去掉末尾0的字符串
     */
    private String convertToString(Object value) {
        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).stripTrailingZeros().toPlainString();
        }

        return value == null ? null : value.toString();
    }
}
