package net.summerfarm.crm.model.convert;

import net.summerfarm.crm.model.domain.CrmFollow;
import net.summerfarm.crm.model.dto.CrmFollowDTO;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class CrmFollowConverter {

    private CrmFollowConverter() {
        // 无需实现
    }

    public static List<CrmFollow> toCrmFollowList(List<CrmFollowDTO> crmFollowDTOList) {
        if (crmFollowDTOList == null) {
            return Collections.emptyList();
        }
        List<CrmFollow> crmFollowList = new ArrayList<>();
        for (CrmFollowDTO crmFollowDTO : crmFollowDTOList) {
            crmFollowList.add(toCrmFollow(crmFollowDTO));
        }
        return crmFollowList;
    }

    public static CrmFollow toCrmFollow(CrmFollowDTO crmFollowDTO) {
        if (crmFollowDTO == null) {
            return null;
        }
        CrmFollow crmFollow = new CrmFollow();
        crmFollow.setId(crmFollowDTO.getId());
        crmFollow.setSubjectId(crmFollowDTO.getSubjectId());
        crmFollow.setBdId(crmFollowDTO.getBdId());
        crmFollow.setFollowTime(crmFollowDTO.getFollowTime());
        crmFollow.setCreateTime(crmFollowDTO.getCreateTime());
        crmFollow.setUpdateTime(crmFollowDTO.getUpdateTime());
        crmFollow.setFollowGoal(crmFollowDTO.getFollowGoal());
        crmFollow.setCustomerFeedback(crmFollowDTO.getCustomerFeedback());
        crmFollow.setNextFollow(crmFollowDTO.getNextFollow());
        crmFollow.setImages(crmFollowDTO.getImages());
        crmFollow.setFollowModel(crmFollowDTO.getFollowModel());
// Not mapped TO fields:
// type
// Not mapped FROM fields:
// imageList
// bdName
        return crmFollow;
    }

    public static List<CrmFollowDTO> toCrmFollowDTOList(List<CrmFollow> crmFollowList) {
        if (crmFollowList == null) {
            return Collections.emptyList();
        }
        List<CrmFollowDTO> crmFollowDTOList = new ArrayList<>();
        for (CrmFollow crmFollow : crmFollowList) {
            crmFollowDTOList.add(toCrmFollowDTO(crmFollow));
        }
        return crmFollowDTOList;
    }

    public static CrmFollowDTO toCrmFollowDTO(CrmFollow crmFollow) {
        if (crmFollow == null) {
            return null;
        }
        CrmFollowDTO crmFollowDTO = new CrmFollowDTO();
        crmFollowDTO.setId(crmFollow.getId());
        crmFollowDTO.setSubjectId(crmFollow.getSubjectId());
        crmFollowDTO.setBdId(crmFollow.getBdId());
        crmFollowDTO.setFollowTime(crmFollow.getFollowTime());
        crmFollowDTO.setCreateTime(crmFollow.getCreateTime());
        crmFollowDTO.setUpdateTime(crmFollow.getUpdateTime());
        crmFollowDTO.setFollowGoal(crmFollow.getFollowGoal());
        crmFollowDTO.setCustomerFeedback(crmFollow.getCustomerFeedback());
        crmFollowDTO.setNextFollow(crmFollow.getNextFollow());
        crmFollowDTO.setImages(crmFollow.getImages());
        crmFollowDTO.setFollowModel(crmFollow.getFollowModel());
        if (!StringUtils.isEmpty(crmFollow.getImages())){
            crmFollowDTO.setImageList(Arrays.asList(crmFollow.getImages().split(",")));
        }
        return crmFollowDTO;
    }
}
