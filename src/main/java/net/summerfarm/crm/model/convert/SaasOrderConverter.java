package net.summerfarm.crm.model.convert;

import net.summerfarm.crm.model.domain.SaasOrderCust;
import net.summerfarm.crm.model.domain.SaasOrderItem;
import net.summerfarm.crm.model.vo.OrderOverviewVO;
import net.summerfarm.crm.model.vo.saasorder.SaasOrderDetailVO;
import net.summerfarm.crm.model.vo.saasorder.SaasOrderItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface SaasOrderConverter {

    SaasOrderConverter INSTANCE = Mappers.getMapper(SaasOrderConverter.class);

    @Mapping(target = "totalPrice", source = "payablePrice")
    OrderOverviewVO saasOrderCustToOrderOverviewVO(SaasOrderCust saasOrderCust);

    List<OrderOverviewVO> saasOrderCustListToOrderOverviewVOList(List<SaasOrderCust> saasOrderCustList);

    SaasOrderItemVO saasOrderItemToVO(SaasOrderItem saasOrderItem);

    @Mapping(target = "orderItems", source = "saasOrderItems")
    @Mapping(target = "totalPrice", source = "saasOrderCust.payablePrice")
    SaasOrderDetailVO saasOrderDetailToVO(SaasOrderCust saasOrderCust, List<SaasOrderItem> saasOrderItems);
}
