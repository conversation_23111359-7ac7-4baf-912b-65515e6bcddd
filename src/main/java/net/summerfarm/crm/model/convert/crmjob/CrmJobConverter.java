package net.summerfarm.crm.model.convert.crmjob;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import net.summerfarm.crm.model.domain.AreaSku;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.domain.CrmJobWithCrmJobCompletionCriteriaList;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobMerchantVO;
import net.summerfarm.crm.model.vo.crmjob.CrmJobVO;
import net.summerfarm.crm.model.vo.crmjob.JobProductVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface CrmJobConverter {

    CrmJobConverter INSTANCE = Mappers.getMapper(CrmJobConverter.class);

    @Mapping(target = "completionCriteriaList", source = "crmJobCompletionCriteriaList")
    @Mapping(target = "categoryList", qualifiedByName = "convertCategoryList")
    CrmJobVO crmJobWithCriteriaToVO(CrmJobWithCrmJobCompletionCriteriaList jobWithCriteria);

    List<CrmJobVO> crmJobWithCriteriaListToVOList(List<CrmJobWithCrmJobCompletionCriteriaList> list);

    @Mapping(target = "MId", source = "crmJobMerchantDetail.MId")
    CrmJobMerchantVO merchantJobDetailToVO(XianmuMerchantCrm xianmuMerchantCrm, CrmJobMerchantDetail crmJobMerchantDetail, List<JobProductVO> productList);

    @Mapping(target = "sku", source = "crmSkuMonthGmvVO.sku")
    @Mapping(target = "areaNo", source = "areaSku.areaNo")
    JobProductVO productToVO(CrmSkuMonthGmvVO crmSkuMonthGmvVO, AreaSku areaSku, BigDecimal activityPrice);

    @Named("convertCategoryList")
    default List<List<Long>> convertCategoryList(String categoryList) {
        if (StringUtils.isEmpty(categoryList)) {
            return null;
        }

        return JSON.parseObject(categoryList, new TypeReference<List<List<Long>>>() {
        });
    }

}
