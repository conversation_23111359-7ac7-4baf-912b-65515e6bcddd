package net.summerfarm.crm.model.convert.salesperformance;

import net.summerfarm.crm.model.domain.CustPerformanceComm;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailV2VO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerSummaryV2VO;
import org.apache.commons.collections.CollectionUtils;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class HighValueCustomerV2Converter {

    public static List<HighValueCustomerSummaryV2VO> convertToHighValueCustomerSummaryV2VOList(
            List<CustPerformanceComm> custPerformanceComms) {
        if (CollectionUtils.isEmpty(custPerformanceComms)) {
            return Collections.emptyList();
        }
        return custPerformanceComms.stream().map(HighValueCustomerV2Converter::convertToHighValueCustomerSummaryV2VO)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static HighValueCustomerSummaryV2VO convertToHighValueCustomerSummaryV2VO(CustPerformanceComm custPerformanceComm) {
        if (custPerformanceComm == null) {
            return null;
        }
        HighValueCustomerSummaryV2VO highValueCustomerSummary = new HighValueCustomerSummaryV2VO();
        highValueCustomerSummary.setBdId(custPerformanceComm.getBdId());
        highValueCustomerSummary.setBdName(custPerformanceComm.getBdName());
        highValueCustomerSummary.setCustCnt(custPerformanceComm.getCustCnt());
        highValueCustomerSummary.setCustCommAmt(custPerformanceComm.getCustCommAmt());
        highValueCustomerSummary.setDlvRealAmt(custPerformanceComm.getDlvRealAmt());
        highValueCustomerSummary.setDlvRealAmtToday(custPerformanceComm.getDlvRealAmtToday());
        highValueCustomerSummary.setDlvMonthTodayTotalAmt(custPerformanceComm.getDlvMonthTodayTotalAmt());
        highValueCustomerSummary.setDlvOrderAmtToday(custPerformanceComm.getDlvOrderAmtToday());
        highValueCustomerSummary.setDlvOtherAmtToday(custPerformanceComm.getDlvOtherAmtToday());
        highValueCustomerSummary.setDlvMonthTotalAmt(custPerformanceComm.getDlvMonthTotalAmt());
        highValueCustomerSummary.setDlvSpuCnt(custPerformanceComm.getDlvSpuCnt());
        highValueCustomerSummary.setDlvRealSpuCntToday(custPerformanceComm.getDlvRealSpuCntToday());
        highValueCustomerSummary.setDlvMonthTodayTotalSpuCnt(custPerformanceComm.getDlvMonthTodayTotalSpuCnt());
        highValueCustomerSummary.setDlvOrderSpuCntToday(custPerformanceComm.getDlvOrderSpuCntToday());
        highValueCustomerSummary.setDlvOtherSpuCntToday(custPerformanceComm.getDlvOtherSpuCntToday());
        highValueCustomerSummary.setDlvMonthTotalSpuCnt(custPerformanceComm.getDlvMonthTotalSpuCnt());
        return highValueCustomerSummary;
    }

    // 将CustPerformanceComm转换为HighValueCustomerDetailV2VO
    public static HighValueCustomerDetailV2VO convertToHighValueCustomerDetailV2VO(CustPerformanceComm custPerformanceComm) {
        if (custPerformanceComm == null) {
            return null;
        }
        HighValueCustomerDetailV2VO highValueCustomerDetail = new HighValueCustomerDetailV2VO();
        highValueCustomerDetail.setMId(custPerformanceComm.getCustId());
        highValueCustomerDetail.setMname(custPerformanceComm.getCustName());
        highValueCustomerDetail.setCustCommAmt(custPerformanceComm.getCustCommAmt());
        highValueCustomerDetail.setDlvRealAmt(custPerformanceComm.getDlvRealAmt());
        highValueCustomerDetail.setDlvRealAmtToday(custPerformanceComm.getDlvRealAmtToday());
        highValueCustomerDetail.setDlvMonthTodayTotalAmt(custPerformanceComm.getDlvMonthTodayTotalAmt());
        highValueCustomerDetail.setDlvOrderAmtToday(custPerformanceComm.getDlvOrderAmtToday());
        highValueCustomerDetail.setDlvOtherAmtToday(custPerformanceComm.getDlvOtherAmtToday());
        highValueCustomerDetail.setDlvMonthTotalAmt(custPerformanceComm.getDlvMonthTotalAmt());
        highValueCustomerDetail.setDlvSpuCnt(custPerformanceComm.getDlvSpuCnt());
        highValueCustomerDetail.setDlvRealSpuCntToday(custPerformanceComm.getDlvRealSpuCntToday());
        highValueCustomerDetail.setDlvMonthTodayTotalSpuCnt(custPerformanceComm.getDlvMonthTodayTotalSpuCnt());
        highValueCustomerDetail.setDlvOrderSpuCntToday(custPerformanceComm.getDlvOrderSpuCntToday());
        highValueCustomerDetail.setDlvOtherSpuCntToday(custPerformanceComm.getDlvOtherSpuCntToday());
        highValueCustomerDetail.setDlvMonthTotalSpuCnt(custPerformanceComm.getDlvMonthTotalSpuCnt());

        return highValueCustomerDetail;
    }

}
