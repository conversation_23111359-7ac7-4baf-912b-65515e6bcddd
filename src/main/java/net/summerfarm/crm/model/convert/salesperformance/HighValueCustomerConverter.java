package net.summerfarm.crm.model.convert.salesperformance;

import net.summerfarm.crm.model.domain.BdCvalMtdPerformance;
import net.summerfarm.crm.model.domain.BdMtdComm;
import net.summerfarm.crm.model.domain.CustMtdPerformance;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.highvaluecustomer.HighValueCustomerSummaryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 高价值客户数据转换器
 */
@Mapper
public interface HighValueCustomerConverter {

    HighValueCustomerConverter INSTANCE = Mappers.getMapper(HighValueCustomerConverter.class);

    /**
     * 将 BdMtdComm 转换为 HighValueCustomerSummaryVO
     *
     * @param bdMtdComm 销售维度数据
     * @return 高价值客户汇总VO
     */
    @Mapping(target = "bdId", source = "lastBdId")
    @Mapping(target = "bdName", source = "lastBdName")
    @Mapping(target = "customerCount", source = "ACustCnt")
    @Mapping(target = "rewardAmount", source = "ACustCommAmt")
    @Mapping(target = "fulfillmentGmv", source = "dlvRealAmt")
    @Mapping(target = "spuCount", source = "dlvSpuCnt")
    HighValueCustomerSummaryVO bdMtdCommToHighValueCustomerSummaryVO(BdMtdComm bdMtdComm);

    /**
     * 将 BdMtdComm 列表转换为 HighValueCustomerSummaryVO 列表
     *
     * @param bdMtdCommList 销售维度数据列表
     * @return 高价值客户汇总VO列表
     */
    List<HighValueCustomerSummaryVO> bdMtdCommListToHighValueCustomerSummaryVOList(List<BdMtdComm> bdMtdCommList);

    /**
     * 将 BdCvalMtdPerformance 转换为 HighValueCustomerSummaryVO
     *
     * @param bdCvalMtdPerformance 销售客户价值维度数据
     * @return 高价值客户汇总VO
     */
    @Mapping(target = "bdId", source = "bdId")
    @Mapping(target = "bdName", source = "bdName")
    @Mapping(target = "customerCount", source = "sumDlvCustCnt")
    @Mapping(target = "rewardAmount", source = "sumCustCommAmt")
    @Mapping(target = "fulfillmentGmv", source = "sumDlvRealAmt")
    @Mapping(target = "spuCount", source = "sumDlvSpuCnt")
    HighValueCustomerSummaryVO bdCvalMtdPerformanceToHighValueCustomerSummaryVO(BdCvalMtdPerformance bdCvalMtdPerformance);

    /**
     * 将 BdCvalMtdPerformance 列表转换为 HighValueCustomerSummaryVO 列表
     *
     * @param bdCvalMtdPerformanceList 销售客户价值维度数据列表
     * @return 高价值客户汇总VO列表
     */
    List<HighValueCustomerSummaryVO> bdCvalMtdPerformanceListToHighValueCustomerSummaryVOList(List<BdCvalMtdPerformance> bdCvalMtdPerformanceList);

    /**
     * 将 CustMtdPerformance 转换为 HighValueCustomerDetailVO
     *
     * @param custMtdPerformance 客户维度数据
     * @return 高价值客户详情VO
     */
    @Mapping(target = "MId", source = "custId")
    @Mapping(target = "mname", source = "lastCustName")
    @Mapping(target = "rewardAmount", source = "custCommAmt")
    @Mapping(target = "fulfillmentGmv", source = "dlvRealAmt")
    @Mapping(target = "spuCount", source = "dlvSpuCnt")
    HighValueCustomerDetailVO custMtdPerformanceToHighValueCustomerDetailVO(CustMtdPerformance custMtdPerformance);
}
