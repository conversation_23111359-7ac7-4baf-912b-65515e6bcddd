package net.summerfarm.crm.model.convert.crmjobv2;

import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobCompletionCriteria;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.domain.CrmJobMerchantItem;
import net.summerfarm.crm.model.dto.crmjob.JobCompletionCriteriaDTO;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobV2DTO;
import net.summerfarm.crm.model.vo.crmjobv2.CrmJobMerchantV2VO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * CRM任务V2转换器
 */
@Mapper
public interface CrmJobV2Converter {

    CrmJobV2Converter INSTANCE = Mappers.getMapper(CrmJobV2Converter.class);

    /**
     * CreateJobV2DTO转CrmJob
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updater", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "couponId", ignore = true)
    @Mapping(target = "categoryList", ignore = true) // V2不使用JSON存储
    @Mapping(target = "productList", ignore = true)  // V2不使用JSON存储
    @Mapping(target = "businessType", constant = "0") // CRM业务类型
    @Mapping(target = "creator", source = "creatorId")
    CrmJob toEntity(CreateJobV2DTO dto, Long creatorId);

    /**
     * JobCompletionCriteriaDTO转CrmJobCompletionCriteria
     */
    @Mapping(target = "orderTypeList", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    CrmJobCompletionCriteria toEntity(JobCompletionCriteriaDTO dto, Long jobId);

    @Mapping(target = "MId", source = "detail.MId")
    CrmJobMerchantV2VO merchantJobDetailToVO(CrmJobMerchantDetail detail, List<CrmJobMerchantItem> items, XianmuMerchantCrm es);
}
