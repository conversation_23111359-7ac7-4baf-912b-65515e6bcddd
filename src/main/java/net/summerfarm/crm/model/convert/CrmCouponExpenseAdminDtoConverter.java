package net.summerfarm.crm.model.convert;

import net.summerfarm.crm.model.domain.CrmCouponExpensePool;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpenseAdminDto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CrmCouponExpenseAdminDtoConverter {

    private CrmCouponExpenseAdminDtoConverter() {
        // 无需实现
    }

    public static List<CrmCouponExpensePool> toCrmCouponExpensePoolList(List<CrmCouponExpenseAdminDto> crmCouponExpenseAdminDtoList) {
        if (crmCouponExpenseAdminDtoList == null) {
            return Collections.emptyList();
        }
        List<CrmCouponExpensePool> crmCouponExpensePoolList = new ArrayList<>();
        for (CrmCouponExpenseAdminDto crmCouponExpenseAdminDto : crmCouponExpenseAdminDtoList) {
            crmCouponExpensePoolList.add(toCrmCouponExpensePool(crmCouponExpenseAdminDto));
        }
        return crmCouponExpensePoolList;
    }

    public static CrmCouponExpensePool toCrmCouponExpensePool(CrmCouponExpenseAdminDto crmCouponExpenseAdminDto) {
        if (crmCouponExpenseAdminDto == null) {
            return null;
        }
        CrmCouponExpensePool crmCouponExpensePool = new CrmCouponExpensePool();
        crmCouponExpensePool.setUpdateTime(crmCouponExpenseAdminDto.getUpdateTime());
        crmCouponExpensePool.setName(crmCouponExpenseAdminDto.getName());
        crmCouponExpensePool.setStartDate(crmCouponExpenseAdminDto.getStartDate());
        crmCouponExpensePool.setEndDate(crmCouponExpenseAdminDto.getEndDate());
// Not mapped TO fields:
// id
// createTime
// totalAmount
// remainingAmount
// autoApprove
// targetType
// status
// costLimit
// createUserId
// createUserName
// Not mapped FROM fields:
// adminName
// totalAmount
// adminId
// remainingAmount
// poolId
        return crmCouponExpensePool;
    }

    public static List<CrmCouponExpenseAdminDto> toCrmCouponExpenseAdminDtoList(List<CrmCouponExpensePool> crmCouponExpensePoolList) {
        if (crmCouponExpensePoolList == null) {
            return Collections.emptyList();
        }
        List<CrmCouponExpenseAdminDto> crmCouponExpenseAdminDtoList = new ArrayList<>();
        for (CrmCouponExpensePool crmCouponExpensePool : crmCouponExpensePoolList) {
            crmCouponExpenseAdminDtoList.add(toCrmCouponExpenseAdminDto(crmCouponExpensePool));
        }
        return crmCouponExpenseAdminDtoList;
    }

    public static CrmCouponExpenseAdminDto toCrmCouponExpenseAdminDto(CrmCouponExpensePool crmCouponExpensePool) {
        if (crmCouponExpensePool == null) {
            return null;
        }
        CrmCouponExpenseAdminDto crmCouponExpenseAdminDto = new CrmCouponExpenseAdminDto();
        crmCouponExpenseAdminDto.setUpdateTime(crmCouponExpensePool.getUpdateTime());
        crmCouponExpenseAdminDto.setName(crmCouponExpensePool.getName());
        crmCouponExpenseAdminDto.setStartDate(crmCouponExpensePool.getStartDate());
        crmCouponExpenseAdminDto.setEndDate(crmCouponExpensePool.getEndDate());
        crmCouponExpenseAdminDto.setAdminName(crmCouponExpensePool.getCreateUserName());
        crmCouponExpenseAdminDto.setPoolId(crmCouponExpensePool.getId());
        crmCouponExpenseAdminDto.setTotalAmount(crmCouponExpensePool.getTotalAmount());
        crmCouponExpenseAdminDto.setRemainingAmount(crmCouponExpensePool.getRemainingAmount());
        crmCouponExpenseAdminDto.setAutoApprove(crmCouponExpensePool.getAutoApprove());
        crmCouponExpenseAdminDto.setProductRange(crmCouponExpensePool.getTargetType());
        crmCouponExpenseAdminDto.setCostLimit(crmCouponExpensePool.getCostLimit());
// Not mapped TO fields:
// Not mapped FROM fields:
// id
// createTime
// totalAmount
// remainingAmount
// autoApprove
// targetType
// status
// costLimit
// createUserId
// createUserName
        return crmCouponExpenseAdminDto;
    }
}
