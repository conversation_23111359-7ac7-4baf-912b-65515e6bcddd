package net.summerfarm.crm.model.convert.salesperformance;

import net.summerfarm.crm.model.domain.CustPbScoreComm;
import net.summerfarm.crm.model.domain.M1PerformanceComm;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.PbPerformanceSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.pbscore.ScorePerformanceSummaryVO;
import org.apache.commons.collections.CollectionUtils;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class PbScorePerformanceConverter {

    public static List<PbPerformanceSummaryVO> convertToPbPerformanceSummaryVOList(List<CustPbScoreComm> custPbScoreComms) {
        if (CollectionUtils.isEmpty(custPbScoreComms)) {
            return Collections.emptyList();
        }
        return custPbScoreComms.stream().map(PbScorePerformanceConverter::convertToPbPerformanceSummaryVO)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static PbPerformanceSummaryVO convertToPbPerformanceSummaryVO(CustPbScoreComm custPbScoreComm) {
        if (custPbScoreComm == null) {
            return null;
        }
        PbPerformanceSummaryVO pbPerformanceSummaryVO = new PbPerformanceSummaryVO();
        pbPerformanceSummaryVO.setBdId(custPbScoreComm.getBdId());
        pbPerformanceSummaryVO.setBdName(custPbScoreComm.getBdName());
        pbPerformanceSummaryVO.setCustCnt(custPbScoreComm.getCustCnt());
        pbPerformanceSummaryVO.setLastDlvRealAmtPb(custPbScoreComm.getLastDlvRealAmtPb());
        pbPerformanceSummaryVO.setDlvRealAmtPb(custPbScoreComm.getDlvRealAmtPb());
        pbPerformanceSummaryVO.setDlvRealAmtTodayPb(custPbScoreComm.getDlvRealAmtTodayPb());
        pbPerformanceSummaryVO.setDlvOrderAmtTodayPb(custPbScoreComm.getDlvOrderAmtTodayPb());
        pbPerformanceSummaryVO.setDlvOtherAmtTodayPb(custPbScoreComm.getDlvOtherAmtTodayPb());
        pbPerformanceSummaryVO.setTotalCateGroupAmtPb(custPbScoreComm.getTotalCateGroupAmtPb());

        return pbPerformanceSummaryVO;
    }

    public static PbPerformanceSummaryVO convertToPbPerformanceSummaryVO(M1PerformanceComm m1PerformanceComm) {
        if (m1PerformanceComm == null) {
            return null;
        }
        PbPerformanceSummaryVO pbPerformanceSummaryVO = new PbPerformanceSummaryVO();
        pbPerformanceSummaryVO.setBdId(m1PerformanceComm.getM1Id());
        pbPerformanceSummaryVO.setBdName(m1PerformanceComm.getM1Name());
        pbPerformanceSummaryVO.setCityManager(true);
        pbPerformanceSummaryVO.setPbCommRate(m1PerformanceComm.getPbCommRate());
        pbPerformanceSummaryVO.setPbIncreaseRate(m1PerformanceComm.getPbIncreaseRate());
        pbPerformanceSummaryVO.setPbGmvBase(m1PerformanceComm.getPbGmvBase());
        pbPerformanceSummaryVO.setCustCnt(m1PerformanceComm.getPbTotalDlvCustCnt());
        pbPerformanceSummaryVO.setLastDlvRealAmtPb(m1PerformanceComm.getPbLastMDlvGmv());
        pbPerformanceSummaryVO.setDlvRealAmtPb(m1PerformanceComm.getPbMtdDlvGmv());
        pbPerformanceSummaryVO.setDlvRealAmtTodayPb(m1PerformanceComm.getPbTodayDlvGmv());
        pbPerformanceSummaryVO.setDlvOrderAmtTodayPb(m1PerformanceComm.getPbTodayTrdGmv());
        pbPerformanceSummaryVO.setDlvOtherAmtTodayPb(m1PerformanceComm.getPbOtherDlvGmv());
        pbPerformanceSummaryVO.setTotalCateGroupAmtPb(m1PerformanceComm.getPbTotalDlvGmv());
        return pbPerformanceSummaryVO;
    }

    public static PbPerformanceDetailVO convertToPbPerformanceDetailVO(CustPbScoreComm custPbScoreComm) {
        if (custPbScoreComm == null) {
            return null;
        }
        PbPerformanceDetailVO pbPerformanceDetail = new PbPerformanceDetailVO();
        pbPerformanceDetail.setMId(custPbScoreComm.getCustId());
        pbPerformanceDetail.setMname(custPbScoreComm.getCustName());
        pbPerformanceDetail.setLastDlvRealAmtPb(custPbScoreComm.getLastDlvRealAmtPb());
        pbPerformanceDetail.setDlvRealAmtPb(custPbScoreComm.getDlvRealAmtPb());
        pbPerformanceDetail.setDlvRealAmtTodayPb(custPbScoreComm.getDlvRealAmtTodayPb());
        pbPerformanceDetail.setDlvOrderAmtTodayPb(custPbScoreComm.getDlvOrderAmtTodayPb());
        pbPerformanceDetail.setDlvOtherAmtTodayPb(custPbScoreComm.getDlvOtherAmtTodayPb());
        pbPerformanceDetail.setTotalCateGroupAmtPb(custPbScoreComm.getTotalCateGroupAmtPb());

        return pbPerformanceDetail;
    }

    public static List<ScorePerformanceSummaryVO> convertToScorePerformanceSummaryVOList(List<CustPbScoreComm> custPbScoreComms) {
        if (CollectionUtils.isEmpty(custPbScoreComms)) {
            return Collections.emptyList();
        }
        return custPbScoreComms.stream().map(PbScorePerformanceConverter::convertToScorePerformanceSummaryVO)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ScorePerformanceSummaryVO convertToScorePerformanceSummaryVO(CustPbScoreComm custPbScoreComm) {
        if (custPbScoreComm == null) {
            return null;
        }
        ScorePerformanceSummaryVO scorePerformanceSummaryVO = new ScorePerformanceSummaryVO();
        scorePerformanceSummaryVO.setBdId(custPbScoreComm.getBdId());
        scorePerformanceSummaryVO.setBdName(custPbScoreComm.getBdName());
        scorePerformanceSummaryVO.setCustCnt(custPbScoreComm.getCustCnt());
        scorePerformanceSummaryVO.setLastCateGroupScore(custPbScoreComm.getLastCateGroupScore());
        scorePerformanceSummaryVO.setCateGroupScore(custPbScoreComm.getCateGroupScore());
        scorePerformanceSummaryVO.setCateGroupScoreToday(custPbScoreComm.getCateGroupScoreToday());
        scorePerformanceSummaryVO.setOrderGroupScoreToday(custPbScoreComm.getOrderGroupScoreToday());
        scorePerformanceSummaryVO.setOtherGroupScoreToday(custPbScoreComm.getOtherGroupScoreToday());
        scorePerformanceSummaryVO.setTotalCateGroupScore(custPbScoreComm.getTotalCateGroupScore());

        return scorePerformanceSummaryVO;
    }

    public static ScorePerformanceSummaryVO convertToScorePerformanceSummaryVO(M1PerformanceComm m1PerformanceComm) {
        if (m1PerformanceComm == null) {
            return null;
        }
        ScorePerformanceSummaryVO scorePerformanceSummaryVO = new ScorePerformanceSummaryVO();
        scorePerformanceSummaryVO.setBdId(m1PerformanceComm.getM1Id());
        scorePerformanceSummaryVO.setBdName(m1PerformanceComm.getM1Name());
        scorePerformanceSummaryVO.setCityManager(true);
        scorePerformanceSummaryVO.setScoreCommRate(m1PerformanceComm.getScoreCommRate());
        scorePerformanceSummaryVO.setScoreIncreaseRate(m1PerformanceComm.getScoreIncreaseRate());
        scorePerformanceSummaryVO.setScoreBase(m1PerformanceComm.getScoreBase());
        scorePerformanceSummaryVO.setCustCnt(m1PerformanceComm.getScoreTotalDlvCustCnt());
        scorePerformanceSummaryVO.setLastCateGroupScore(m1PerformanceComm.getLastMScores());
        scorePerformanceSummaryVO.setCateGroupScore(m1PerformanceComm.getMtdScores());
        scorePerformanceSummaryVO.setCateGroupScoreToday(m1PerformanceComm.getTodayDlvScores());
        scorePerformanceSummaryVO.setOrderGroupScoreToday(m1PerformanceComm.getTodayTrdScores());
        scorePerformanceSummaryVO.setOtherGroupScoreToday(m1PerformanceComm.getOtherDlvScores());
        scorePerformanceSummaryVO.setTotalCateGroupScore(m1PerformanceComm.getTotalScores());
        return scorePerformanceSummaryVO;
    }

    public static ScorePerformanceDetailVO convertToScorePerformanceDetailVO(CustPbScoreComm custPbScoreComm) {
        if (custPbScoreComm == null) {
            return null;
        }
        ScorePerformanceDetailVO scorePerformanceDetail = new ScorePerformanceDetailVO();
        scorePerformanceDetail.setMId(custPbScoreComm.getCustId());
        scorePerformanceDetail.setMname(custPbScoreComm.getCustName());
        scorePerformanceDetail.setLastCateGroupScore(custPbScoreComm.getLastCateGroupScore());
        scorePerformanceDetail.setCateGroupScore(custPbScoreComm.getCateGroupScore());
        scorePerformanceDetail.setCateGroupScoreToday(custPbScoreComm.getCateGroupScoreToday());
        scorePerformanceDetail.setOrderGroupScoreToday(custPbScoreComm.getOrderGroupScoreToday());
        scorePerformanceDetail.setOtherGroupScoreToday(custPbScoreComm.getOtherGroupScoreToday());
        scorePerformanceDetail.setTotalCateGroupScore(custPbScoreComm.getTotalCateGroupScore());

        return scorePerformanceDetail;
    }
}
