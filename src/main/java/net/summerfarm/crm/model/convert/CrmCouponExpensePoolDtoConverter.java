package net.summerfarm.crm.model.convert;

import net.summerfarm.crm.model.domain.CrmCouponExpensePool;
import net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolDto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CrmCouponExpensePoolDtoConverter {


    private CrmCouponExpensePoolDtoConverter() {
        // 无需实现
    }

    public static List<CrmCouponExpensePool> toCrmCouponExpensePoolList(List<CrmCouponExpensePoolDto> crmCouponExpensePoolDtoList) {
        if (crmCouponExpensePoolDtoList == null) {
            return Collections.emptyList();
        }
        List<CrmCouponExpensePool> crmCouponExpensePoolList = new ArrayList<>();
        for (CrmCouponExpensePoolDto crmCouponExpensePoolDto : crmCouponExpensePoolDtoList) {
            crmCouponExpensePoolList.add(toCrmCouponExpensePool(crmCouponExpensePoolDto));
        }
        return crmCouponExpensePoolList;
    }

    public static CrmCouponExpensePool toCrmCouponExpensePool(CrmCouponExpensePoolDto crmCouponExpensePoolDto) {
        if (crmCouponExpensePoolDto == null) {
            return null;
        }
        CrmCouponExpensePool crmCouponExpensePool = new CrmCouponExpensePool();
        crmCouponExpensePool.setName(crmCouponExpensePoolDto.getName());
        crmCouponExpensePool.setTotalAmount(crmCouponExpensePoolDto.getTotalAmount());
        crmCouponExpensePool.setAutoApprove(crmCouponExpensePoolDto.getAutoApprove());
        crmCouponExpensePool.setStatus(crmCouponExpensePoolDto.getStatus());
        crmCouponExpensePool.setStartDate(crmCouponExpensePoolDto.getStartDate());
        crmCouponExpensePool.setEndDate(crmCouponExpensePoolDto.getEndDate());
// Not mapped TO fields:
// id
// createTime
// updateTime
// targetType
// costLimit
// createUserId
// createUserName
// Not mapped FROM fields:
// adminName
// productRange
// costLimit
// poolId
// remainingAmount
        return crmCouponExpensePool;
    }

    public static List<CrmCouponExpensePoolDto> toCrmCouponExpensePoolDtoList(List<CrmCouponExpensePool> crmCouponExpensePoolList) {
        if (crmCouponExpensePoolList == null) {
            return Collections.emptyList();
        }
        List<CrmCouponExpensePoolDto> crmCouponExpensePoolDtoList = new ArrayList<>();
        for (CrmCouponExpensePool crmCouponExpensePool : crmCouponExpensePoolList) {
            crmCouponExpensePoolDtoList.add(toCrmCouponExpensePoolDto(crmCouponExpensePool));
        }
        return crmCouponExpensePoolDtoList;
    }

    public static CrmCouponExpensePoolDto toCrmCouponExpensePoolDto(CrmCouponExpensePool crmCouponExpensePool) {
        if (crmCouponExpensePool == null) {
            return null;
        }
        CrmCouponExpensePoolDto crmCouponExpensePoolDto = new CrmCouponExpensePoolDto();
        crmCouponExpensePoolDto.setName(crmCouponExpensePool.getName());
        crmCouponExpensePoolDto.setStatus(crmCouponExpensePool.getStatus());
        crmCouponExpensePoolDto.setStartDate(crmCouponExpensePool.getStartDate());
        crmCouponExpensePoolDto.setEndDate(crmCouponExpensePool.getEndDate());
        crmCouponExpensePoolDto.setAutoApprove(crmCouponExpensePool.getAutoApprove());
        crmCouponExpensePoolDto.setTotalAmount(crmCouponExpensePool.getTotalAmount());
        crmCouponExpensePoolDto.setAdminName(crmCouponExpensePool.getCreateUserName());
        crmCouponExpensePoolDto.setProductRange(crmCouponExpensePool.getTargetType());
        crmCouponExpensePoolDto.setCostLimit(crmCouponExpensePool.getCostLimit());
        crmCouponExpensePoolDto.setPoolId(crmCouponExpensePool.getId());
        crmCouponExpensePoolDto.setRemainingAmount(crmCouponExpensePool.getRemainingAmount());
        return crmCouponExpensePoolDto;
    }
}
