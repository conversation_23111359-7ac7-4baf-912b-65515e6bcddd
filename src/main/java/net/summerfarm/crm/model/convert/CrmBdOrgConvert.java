package net.summerfarm.crm.model.convert;

import net.summerfarm.crm.client.dto.CrmBdOrgDTO;
import net.summerfarm.crm.model.domain.CrmBdOrg;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/12 13:54
 */
public class CrmBdOrgConvert {
    public static List<CrmBdOrgDTO> convertToDTO(List<CrmBdOrg> crmBdOrgList) {
        if (crmBdOrgList == null) {
            return Collections.emptyList();
        }
        List<CrmBdOrgDTO> crmBdOrgDTOList = new ArrayList<>();
        for (CrmBdOrg crmBdOrg : crmBdOrgList) {
            crmBdOrgDTOList.add(convertToDTO(crmBdOrg));
        }
        return crmBdOrgDTOList;
    }
    public static CrmBdOrgDTO convertToDTO(CrmBdOrg crmBdOrg) {
        if (crmBdOrg == null) {
            return null;
        }
        CrmBdOrgDTO crmBdOrgDTO = new CrmBdOrgDTO();
        crmBdOrgDTO.setId(crmBdOrg.getId());
        crmBdOrgDTO.setBdId(crmBdOrg.getBdId());
        crmBdOrgDTO.setBdName(crmBdOrg.getBdName());
        crmBdOrgDTO.setParentId(crmBdOrg.getParentId());
        crmBdOrgDTO.setParentName(crmBdOrg.getParentName());
        crmBdOrgDTO.setRank(crmBdOrg.getRank());
        crmBdOrgDTO.setCreateTime(crmBdOrg.getCreateTime());
        crmBdOrgDTO.setUpdateTime(crmBdOrg.getUpdateTime());
        return crmBdOrgDTO;
    }
}
