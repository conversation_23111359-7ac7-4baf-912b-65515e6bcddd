package net.summerfarm.crm.model.convert;


import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.domain.DeliveryPlanRemarkSnapshot;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DeliverPlanRemarkConverter {


    private DeliverPlanRemarkConverter() {
        // 无需实现
    }

    public static List<DeliveryPlanRemarkSnapshot> toDeliveryPlanRemarkSnapshotList(List<Contact> contactList) {
        if (contactList == null) {
            return Collections.emptyList();
        }
        List<DeliveryPlanRemarkSnapshot> deliveryPlanRemarkSnapshotList = new ArrayList<>();
        for (Contact contact : contactList) {
            deliveryPlanRemarkSnapshotList.add(toDeliveryPlanRemarkSnapshot(contact));
        }
        return deliveryPlanRemarkSnapshotList;
    }

    public static DeliveryPlanRemarkSnapshot toDeliveryPlanRemarkSnapshot(Contact contact) {
        if (contact == null) {
            return null;
        }
        DeliveryPlanRemarkSnapshot deliveryPlanRemarkSnapshot = new DeliveryPlanRemarkSnapshot();
        deliveryPlanRemarkSnapshot.setAddressRemark(contact.getAddressRemark());
        deliveryPlanRemarkSnapshot.setContact(contact.getContact());
        deliveryPlanRemarkSnapshot.setPhone(contact.getPhone());
        deliveryPlanRemarkSnapshot.setEmail(contact.getEmail());
        deliveryPlanRemarkSnapshot.setProvince(contact.getProvince());
        deliveryPlanRemarkSnapshot.setCity(contact.getCity());
        deliveryPlanRemarkSnapshot.setArea(contact.getArea());
        deliveryPlanRemarkSnapshot.setAddress(contact.getAddress());
        deliveryPlanRemarkSnapshot.setStatus(contact.getStatus());
        deliveryPlanRemarkSnapshot.setRemark(contact.getRemark());
        deliveryPlanRemarkSnapshot.setIsDefault(contact.getIsDefault());
        deliveryPlanRemarkSnapshot.setPoiNote(contact.getPoiNote());
        deliveryPlanRemarkSnapshot.setDistance(contact.getDistance());
        deliveryPlanRemarkSnapshot.setHouseNumber(contact.getHouseNumber());
        deliveryPlanRemarkSnapshot.setStoreNo(contact.getStoreNo());
        deliveryPlanRemarkSnapshot.setDeliveryFrequent(contact.getDeliveryFrequent());
// Not mapped TO fields:
// id
// createTime
// updateTime
// businessId
// type
// acmId
// backStoreNo
// Not mapped FROM fields:
// contactId
// mId
// position
// gender
// weixincode
// deliveryCar
// path
// poi
// areaNo
// areaName
// modify
// useNew
// nextDeliveryDate
// contactAddressRemark
        return deliveryPlanRemarkSnapshot;
    }

    public static List<Contact> toContactList(List<DeliveryPlanRemarkSnapshot> deliveryPlanRemarkSnapshotList) {
        if (deliveryPlanRemarkSnapshotList == null) {
            return Collections.emptyList();
        }
        List<Contact> contactList = new ArrayList<>();
        for (DeliveryPlanRemarkSnapshot deliveryPlanRemarkSnapshot : deliveryPlanRemarkSnapshotList) {
            contactList.add(toContact(deliveryPlanRemarkSnapshot));
        }
        return contactList;
    }

    public static Contact toContact(DeliveryPlanRemarkSnapshot deliveryPlanRemarkSnapshot) {
        if (deliveryPlanRemarkSnapshot == null) {
            return null;
        }
        Contact contact = new Contact();
        contact.setContact(deliveryPlanRemarkSnapshot.getContact());
        contact.setPhone(deliveryPlanRemarkSnapshot.getPhone());
        contact.setEmail(deliveryPlanRemarkSnapshot.getEmail());
        contact.setProvince(deliveryPlanRemarkSnapshot.getProvince());
        contact.setCity(deliveryPlanRemarkSnapshot.getCity());
        contact.setArea(deliveryPlanRemarkSnapshot.getArea());
        contact.setAddress(deliveryPlanRemarkSnapshot.getAddress());
        contact.setStatus(deliveryPlanRemarkSnapshot.getStatus());
        contact.setRemark(deliveryPlanRemarkSnapshot.getRemark());
        contact.setIsDefault(deliveryPlanRemarkSnapshot.getIsDefault());
        contact.setPoiNote(deliveryPlanRemarkSnapshot.getPoiNote());
        contact.setDistance(deliveryPlanRemarkSnapshot.getDistance());
        contact.setHouseNumber(deliveryPlanRemarkSnapshot.getHouseNumber());
        contact.setStoreNo(deliveryPlanRemarkSnapshot.getStoreNo());
        contact.setDeliveryFrequent(deliveryPlanRemarkSnapshot.getDeliveryFrequent());
        contact.setAddressRemark(deliveryPlanRemarkSnapshot.getAddressRemark());
// Not mapped TO fields:
// contactId
// mId
// position
// gender
// weixincode
// deliveryCar
// path
// poi
// areaNo
// areaName
// modify
// useNew
// nextDeliveryDate
// contactAddressRemark
// Not mapped FROM fields:
// id
// createTime
// updateTime
// businessId
// type
// acmId
// backStoreNo
        return contact;
    }
}
