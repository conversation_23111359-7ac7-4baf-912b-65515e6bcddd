package net.summerfarm.crm.model.convert.salesperformance;

import net.summerfarm.crm.model.dto.salesperformance.CategoryPromotionDTO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 品类推广数据转换器
 */
@Mapper
public interface CategoryPromotionConverter {

    CategoryPromotionConverter INSTANCE = Mappers.getMapper(CategoryPromotionConverter.class);

    @Mapping(target = "fulfillmentCount", qualifiedByName = "formatDecimal")
    @Mapping(target = "transactionCount", qualifiedByName = "formatDecimal")
    CategoryPromotionSummaryVO categoryPromotionDTOToCategoryPromotionSummaryVO(CategoryPromotionDTO dto);

    /**
     * 将 CategoryPromotionDTO 列表转换为 CategoryPromotionSummaryVO 列表
     *
     * @param dtoList 品类推广DTO列表
     * @return 品类推广汇总VO列表
     */
    List<CategoryPromotionSummaryVO> categoryPromotionDTOListToCategoryPromotionSummaryVOList(List<CategoryPromotionDTO> dtoList);

    /**
     * 将 CategoryPromotionDTO 转换为 CategoryPromotionDetailVO
     *
     * @param dto 品类推广DTO
     * @return 品类推广详情VO
     */
    @Mapping(source = "custId", target = "MId")
    @Mapping(source = "custName", target = "mname")
    @Mapping(target = "rewardPerItem", qualifiedByName = "formatBigDecimal")
    CategoryPromotionDetailVO categoryPromotionDTOToCategoryPromotionDetailVO(CategoryPromotionDTO dto);

    /**
     * 将 CategoryPromotionDTO 转换为 MerchantCategoryPromotionVO
     *
     * @param dto 品类推广DTO
     * @return 门店品类推广VO
     */
    @Mapping(target = "mname", source = "custName")
    @Mapping(target = "MId", source = "custId")
    @Mapping(target = "rewardPerItem", qualifiedByName = "formatBigDecimal")
    MerchantCategoryPromotionVO categoryPromotionDTOToMerchantCategoryPromotionVO(CategoryPromotionDTO dto);

    /**
     * 将 CategoryPromotionDTO 列表转换为 MerchantCategoryPromotionVO 列表
     *
     * @param dtoList 品类推广DTO列表
     * @return 门店品类推广VO列表
     */
    List<MerchantCategoryPromotionVO> categoryPromotionDTOListToMerchantCategoryPromotionVOList(List<CategoryPromotionDTO> dtoList);

    @Named("formatDecimal")
    default Double formatDecimal(@Nullable Double number) {
        return number != null ? BigDecimal.valueOf(number).setScale(2, RoundingMode.HALF_UP).doubleValue() : null;
    }

    @Named("formatBigDecimal")
    default BigDecimal formatBigDecimal(@Nullable BigDecimal number) {
        return number != null ? number.setScale(2, RoundingMode.HALF_UP) : null;
    }
}
