package net.summerfarm.crm.model.convert.salesperformance;

import net.summerfarm.crm.enums.CategoryPromotionRewardModeEnum;
import net.summerfarm.crm.model.domain.CustCategoryPerformanceComm;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionDetailV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSpuSummaryVO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.CategoryPromotionSummaryV2VO;
import net.summerfarm.crm.model.vo.salesperformance.categorypromotion.MerchantCategoryPromotionV2VO;
import org.apache.commons.collections.CollectionUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class CategoryPromotionV2Converter {

    public static List<CategoryPromotionSummaryV2VO> convertToCategoryPromotionSummaryV2VOList(
            List<CustCategoryPerformanceComm> custCategoryPerformanceCommList) {
        if (CollectionUtils.isEmpty(custCategoryPerformanceCommList)) {
            return Collections.emptyList();
        }
        return custCategoryPerformanceCommList.stream().map(CategoryPromotionV2Converter::convertToCategoryPromotionSummaryV2VO)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static CategoryPromotionSummaryV2VO convertToCategoryPromotionSummaryV2VO(
            CustCategoryPerformanceComm custCategoryPerformanceComm) {
        if (custCategoryPerformanceComm == null) {
            return null;
        }
        CategoryPromotionSummaryV2VO categoryPromotionSummary = new CategoryPromotionSummaryV2VO();
        categoryPromotionSummary.setBdId(custCategoryPerformanceComm.getBdId());
        categoryPromotionSummary.setBdName(custCategoryPerformanceComm.getBdName());
        categoryPromotionSummary.setCustCnt(custCategoryPerformanceComm.getCustCnt());
        categoryPromotionSummary.setCategoryCommAmt(custCategoryPerformanceComm.getCategoryCommAmt());
        categoryPromotionSummary.setMonthCategoryCustComm(custCategoryPerformanceComm.getMonthCategoryCustComm());
        categoryPromotionSummary.setBigSkuCnt(custCategoryPerformanceComm.getBigSkuCnt());
        categoryPromotionSummary.setDlvRealCntToday(custCategoryPerformanceComm.getDlvRealCntToday());
        categoryPromotionSummary.setDlvOrderCntToday(custCategoryPerformanceComm.getDlvOrderCntToday());
        categoryPromotionSummary.setDlvOtherCntToday(custCategoryPerformanceComm.getDlvOtherCntToday());
        categoryPromotionSummary.setMonthDlvRealCntToday(custCategoryPerformanceComm.getMonthDlvRealCntToday());
        categoryPromotionSummary.setMtdTxnSkuCnt(custCategoryPerformanceComm.getMtdTxnSkuCnt());
        categoryPromotionSummary.setTodayTxnSkuCnt(custCategoryPerformanceComm.getTodayTxnSkuCnt());

        return categoryPromotionSummary;
    }

    public static CategoryPromotionDetailV2VO convertToCategoryPromotionDetailV2VO(
            CustCategoryPerformanceComm custCategoryPerformanceComm) {
        if (custCategoryPerformanceComm == null) {
            return null;
        }
        CategoryPromotionDetailV2VO categoryPromotionDetail = new CategoryPromotionDetailV2VO();
        categoryPromotionDetail.setMId(custCategoryPerformanceComm.getCustId());
        categoryPromotionDetail.setMname(custCategoryPerformanceComm.getCustName());
        categoryPromotionDetail.setCategoryCommAmt(custCategoryPerformanceComm.getCategoryCommAmt());
        categoryPromotionDetail.setMonthCategoryCustComm(custCategoryPerformanceComm.getMonthCategoryCustComm());
        categoryPromotionDetail.setBigSkuCnt(custCategoryPerformanceComm.getBigSkuCnt());
        categoryPromotionDetail.setDlvRealCntToday(custCategoryPerformanceComm.getDlvRealCntToday());
        categoryPromotionDetail.setDlvOrderCntToday(custCategoryPerformanceComm.getDlvOrderCntToday());
        categoryPromotionDetail.setDlvOtherCntToday(custCategoryPerformanceComm.getDlvOtherCntToday());
        categoryPromotionDetail.setMonthDlvRealCntToday(custCategoryPerformanceComm.getMonthDlvRealCntToday());
        categoryPromotionDetail.setMtdTxnSkuCnt(custCategoryPerformanceComm.getMtdTxnSkuCnt());
        categoryPromotionDetail.setTodayTxnSkuCnt(custCategoryPerformanceComm.getTodayTxnSkuCnt());

        return categoryPromotionDetail;
    }

    public static List<MerchantCategoryPromotionV2VO> convertToMerchantCategoryPromotionV2VOList(
            List<CustCategoryPerformanceComm> custCategoryPerformanceCommList) {
        if (CollectionUtils.isEmpty(custCategoryPerformanceCommList)) {
            return Collections.emptyList();
        }
        return custCategoryPerformanceCommList.stream().map(CategoryPromotionV2Converter::convertToMerchantCategoryPromotionV2VO)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static MerchantCategoryPromotionV2VO convertToMerchantCategoryPromotionV2VO(
            CustCategoryPerformanceComm custCategoryPerformanceComm) {
        if (custCategoryPerformanceComm == null) {
            return null;
        }
        MerchantCategoryPromotionV2VO merchantCategoryPromotion = new MerchantCategoryPromotionV2VO();
        merchantCategoryPromotion.setMId(custCategoryPerformanceComm.getCustId());
        merchantCategoryPromotion.setMname(custCategoryPerformanceComm.getCustName());
        merchantCategoryPromotion.setCustType(custCategoryPerformanceComm.getCustType());
        merchantCategoryPromotion.setSpuGroup(custCategoryPerformanceComm.getSpuGroup());
        merchantCategoryPromotion.setCategoryCommAmt(custCategoryPerformanceComm.getCategoryCommAmt());
        merchantCategoryPromotion.setMonthCategoryCustComm(custCategoryPerformanceComm.getMonthCategoryCustComm());
        merchantCategoryPromotion.setRewardPerItem(custCategoryPerformanceComm.getRewardPerItem());
        merchantCategoryPromotion.setIsDlvPayment(custCategoryPerformanceComm.getIsDlvPayment());
        // 设置奖励方式
        CategoryPromotionRewardModeEnum rewardMode =
                CategoryPromotionRewardModeEnum.getByCode(custCategoryPerformanceComm.getIsDlvPayment());
        if (rewardMode != null) {
            merchantCategoryPromotion.setRewardMode(rewardMode.getDesc());
        }
        merchantCategoryPromotion.setBigSkuCnt(custCategoryPerformanceComm.getBigSkuCnt());
        merchantCategoryPromotion.setDlvRealCntToday(custCategoryPerformanceComm.getDlvRealCntToday());
        merchantCategoryPromotion.setDlvOrderCntToday(custCategoryPerformanceComm.getDlvOrderCntToday());
        merchantCategoryPromotion.setDlvOtherCntToday(custCategoryPerformanceComm.getDlvOtherCntToday());
        merchantCategoryPromotion.setMonthDlvRealCntToday(custCategoryPerformanceComm.getMonthDlvRealCntToday());
        merchantCategoryPromotion.setMtdTxnSkuCnt(custCategoryPerformanceComm.getMtdTxnSkuCnt());
        merchantCategoryPromotion.setTodayTxnSkuCnt(custCategoryPerformanceComm.getTodayTxnSkuCnt());

        return merchantCategoryPromotion;
    }

    public static CategoryPromotionSpuSummaryVO convertToCategoryPromotionSpuSummaryVO(
            CustCategoryPerformanceComm custCategoryPerformanceComm) {
        if (custCategoryPerformanceComm == null) {
            return null;
        }
        CategoryPromotionSpuSummaryVO categoryPromotionSpuSummary = new CategoryPromotionSpuSummaryVO();
        categoryPromotionSpuSummary.setSpuGroup(custCategoryPerformanceComm.getSpuGroup());
        categoryPromotionSpuSummary.setCustCnt(custCategoryPerformanceComm.getCustCnt());
        categoryPromotionSpuSummary.setCategoryCommAmt(custCategoryPerformanceComm.getCategoryCommAmt());
        categoryPromotionSpuSummary.setMonthCategoryCustComm(custCategoryPerformanceComm.getMonthCategoryCustComm());
        categoryPromotionSpuSummary.setIsDlvPayment(custCategoryPerformanceComm.getIsDlvPayment());
        // 设置奖励方式
        CategoryPromotionRewardModeEnum rewardMode =
                CategoryPromotionRewardModeEnum.getByCode(custCategoryPerformanceComm.getIsDlvPayment());
        if (rewardMode != null) {
            categoryPromotionSpuSummary.setRewardMode(rewardMode.getDesc());
        }
        // 计算单件奖励
        if (CategoryPromotionRewardModeEnum.FULFILLMENT_COUNT.equals(rewardMode)
                && custCategoryPerformanceComm.getBigSkuCnt() != null && custCategoryPerformanceComm.getBigSkuCnt() > 0) {
            categoryPromotionSpuSummary.setRewardPerItem(custCategoryPerformanceComm.getCategoryCommAmt()
                    .divide(new BigDecimal(custCategoryPerformanceComm.getBigSkuCnt()), 2, RoundingMode.HALF_UP));
        } else if (CategoryPromotionRewardModeEnum.TRANSACTION_COUNT.equals(rewardMode)
                && custCategoryPerformanceComm.getMtdTxnSkuCnt() != null && custCategoryPerformanceComm.getMtdTxnSkuCnt() > 0) {
            categoryPromotionSpuSummary.setRewardPerItem(custCategoryPerformanceComm.getCategoryCommAmt()
                    .divide(new BigDecimal(custCategoryPerformanceComm.getMtdTxnSkuCnt()), 2, RoundingMode.HALF_UP));
        }
        categoryPromotionSpuSummary.setBigSkuCnt(custCategoryPerformanceComm.getBigSkuCnt());
        categoryPromotionSpuSummary.setDlvRealCntToday(custCategoryPerformanceComm.getDlvRealCntToday());
        categoryPromotionSpuSummary.setDlvOrderCntToday(custCategoryPerformanceComm.getDlvOrderCntToday());
        categoryPromotionSpuSummary.setDlvOtherCntToday(custCategoryPerformanceComm.getDlvOtherCntToday());
        categoryPromotionSpuSummary.setMonthDlvRealCntToday(custCategoryPerformanceComm.getMonthDlvRealCntToday());
        categoryPromotionSpuSummary.setMtdTxnSkuCnt(custCategoryPerformanceComm.getMtdTxnSkuCnt());
        categoryPromotionSpuSummary.setTodayTxnSkuCnt(custCategoryPerformanceComm.getTodayTxnSkuCnt());
        return categoryPromotionSpuSummary;
    }

}
