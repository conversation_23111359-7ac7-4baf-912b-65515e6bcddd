package net.summerfarm.crm.model.convert;

import net.summerfarm.crm.model.domain.CluePool;
import net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool;

public class CluePoolHivePoolConverter {

    private CluePoolHivePoolConverter() {
        // 无需实现
    }


    public static CluePool toCluePool(Hive2mysqlCrmShopListPool hive2mysqlCrmShopListPool) {
        if (hive2mysqlCrmShopListPool == null) {
            return null;
        }
        CluePool cluePool = new CluePool();
        cluePool.setShopName(hive2mysqlCrmShopListPool.getShopName());
        cluePool.setAddress(hive2mysqlCrmShopListPool.getAddress());
        cluePool.setIsChain(hive2mysqlCrmShopListPool.getIsChain());
        cluePool.setProvince(hive2mysqlCrmShopListPool.getProvince());
        cluePool.setCity(hive2mysqlCrmShopListPool.getCity());
        cluePool.setDistrict(hive2mysqlCrmShopListPool.getDistrict());
        cluePool.setShopRegion(hive2mysqlCrmShopListPool.getShopRegion());
        cluePool.setShoppingMall(hive2mysqlCrmShopListPool.getShoppingMall());
        cluePool.setShopid(hive2mysqlCrmShopListPool.getShopid());
        cluePool.setBrand(hive2mysqlCrmShopListPool.getBrand());
        cluePool.setCommentsCountInc(hive2mysqlCrmShopListPool.getCommentsCountInc());
        cluePool.setLat(hive2mysqlCrmShopListPool.getLat());
        cluePool.setLng(hive2mysqlCrmShopListPool.getLng());
        cluePool.setPhone(hive2mysqlCrmShopListPool.getPhoneNumber());
        cluePool.setType(hive2mysqlCrmShopListPool.getCuisineType());
        cluePool.setPopularityIndex(hive2mysqlCrmShopListPool.getPopularityIndex() == null ? null : hive2mysqlCrmShopListPool.getPopularityIndex().toString());
        cluePool.setManage(hive2mysqlCrmShopListPool.getManage());
        cluePool.setPhone(hive2mysqlCrmShopListPool.getPhoneNumber());
        cluePool.setCuisineType(hive2mysqlCrmShopListPool.getCuisineType());
        cluePool.setShopAge(hive2mysqlCrmShopListPool.getShopAge());
// Not mapped TO fields:
// id
// commonPrice
// popularityIndex
// tasteIndex
// serviceIndex
// environmentIndex
// esId
        return cluePool;
    }
}
