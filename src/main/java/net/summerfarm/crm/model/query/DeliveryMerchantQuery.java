package net.summerfarm.crm.model.query;

import io.swagger.models.auth.In;
import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import net.xianmu.common.input.BasePageInput;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/10/20 14:45
 */
@Data
public class DeliveryMerchantQuery extends BasePageInput {

    /**
     * 配送gmv达标阈值
     */
    private BigDecimal deliveryGmvComplianceThreshold;

    /**
     * 配送时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;
    /**
     * 配送最低gmv
     */
    private BigDecimal deliveryMinimumGmv;
    /**
     * 配送最高gmv, 最高档时不填
     */
    private BigDecimal deliveryHighestGmv;
    /**
     * 下单最低spu数
     */
    private Integer spuMinimumNum;
    /**
     * 下单最高spu数, 最高档时不填
     */
    private Integer spuHighestNum;
    /**
     * bdId
     */
    private Integer adminId;
    /**
     * 是否达标: 0否1是
     */
    private Integer deliveryUpToStandard;
    /**
     * 区域no, 主管必填, 销售可不填
     */
    private Integer areaNo;
    /**
     * 数据所在日标识
     */
    private Integer dayTag;
    /**
     * 主管标识,0否1是
     */
    private Boolean saleTag;

    /**
     * 排序方式 0:预估收益升序;1:预估收益降序;2:达标后预估收益升序;3:达标后预估收益降序
     */
    private Integer orderBy;
    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;

}
