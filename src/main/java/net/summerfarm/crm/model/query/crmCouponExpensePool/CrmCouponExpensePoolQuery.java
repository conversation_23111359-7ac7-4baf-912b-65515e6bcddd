package net.summerfarm.crm.model.query.crmCouponExpensePool;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CrmCouponExpensePoolQuery extends BasePageInput {
    /**
     * 费用名称
     */
    private String name;

    /**
     * 用户名称
     */
    private String adminName;

    /**
     * 当前状态 0 正常 1失效
     */
    private Integer status;

    /**
     * 是否审批
     */
    private Integer autoApprove;
    /**
     * 线索池id
     */
    private Long poolId;
    /**
     * 是否查询父级的余额池 用于小接口查询 默认false
     */
    private Boolean queryParent = false;

    private List<Long> poolIds;

    private Integer adminId;

    private Integer productRange;

    /**
     * 是否查询自己
     */
    private Boolean queryMyself = false;

}

