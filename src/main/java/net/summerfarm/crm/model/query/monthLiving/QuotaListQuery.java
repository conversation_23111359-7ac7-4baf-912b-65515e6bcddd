package net.summerfarm.crm.model.query.monthLiving;

import lombok.Data;
import net.summerfarm.crm.enums.CategoryQuotaEnum;
import net.xianmu.common.input.BasePageInput;

/**
 * <AUTHOR>
 * @date 2023/11/29 13:48
 */
@Data
public class QuotaListQuery extends BasePageInput {
    /**
     * 额度类型: 0:品类券;1:月活券;2品类卷-价格补贴
     */
    private Integer type = CategoryQuotaEnum.QuotaType.CATEGORY.getCode();

    private String adminName;
}
