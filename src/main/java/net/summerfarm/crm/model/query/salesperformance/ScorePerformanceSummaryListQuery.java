package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import net.summerfarm.crm.enums.PbScoreMerchantTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import javax.validation.constraints.Pattern;

/**
 * 利润积分得分汇总列表查询参数
 */
@Data
public class ScorePerformanceSummaryListQuery {

    /**
     * 客户类型: 平台客户（单店），大客户
     */
    private PbScoreMerchantTypeEnum merchantType;

    /**
     * 销售区域
     */
    private String bdWorkZone;

    /**
     * 排序字段: 客户数, 利润积分
     * 客户数 - customerCount
     * 利润积分 - score
     */
    @Pattern(regexp = "^(customerCount|score)$",
            message = "排序字段必须是 customerCount 或 score 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

}
