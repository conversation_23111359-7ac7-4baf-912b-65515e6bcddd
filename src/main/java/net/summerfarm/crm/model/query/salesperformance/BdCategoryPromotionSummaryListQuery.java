package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import net.summerfarm.crm.enums.CategoryPromotionCustomerTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 销售品类推广汇总列表查询参数
 */
@Data
public class BdCategoryPromotionSummaryListQuery {

    /**
     * 客户类型: 存量客户, 增量客户
     */
    private CategoryPromotionCustomerTypeEnum customerType;

    /**
     * 排序字段: 客户数, 奖励金额, 履约件数, 交易件数
     * 客户数 - customerCount
     * 奖励金额 - rewardAmount
     * 履约件数 - fulfillmentCount
     * 交易件数 - transactionCount
     */
    @Pattern(regexp = "^(customerCount|rewardAmount|fulfillmentCount|transactionCount)$",
            message = "排序字段必须是 customerCount, rewardAmount, fulfillmentCount 或 transactionCount 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;
    
    /**
     * 商品
     */
    private List<String> spuGroupList;
}
