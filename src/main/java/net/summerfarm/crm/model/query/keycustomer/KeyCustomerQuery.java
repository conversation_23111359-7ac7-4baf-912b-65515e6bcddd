package net.summerfarm.crm.model.query.keycustomer;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.KeyCustomerEnum;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class KeyCustomerQuery extends BasePageInput {

    /**
     * bdId
     */
    private Integer bdId;

    /**
     * 省
     */
    @NotNull(message = "省不能为空")
    private String province;

    /**
     * 城市
     */
    @NotNull(message = "城市不能为空")
    private String city;

    /**
     * 重点客户类型
     * @see KeyCustomerEnum.Type#getCode()
     */
    @NotNull(message = "重点客户类型不能为空")
    private Integer type;
}
