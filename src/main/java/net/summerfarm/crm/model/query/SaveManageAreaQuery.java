package net.summerfarm.crm.model.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SaveManageAreaQuery implements Serializable {

    private static final long serialVersionUID = -7104516112244419337L;

    private Integer id;
    /**
     * 城市负责人
     */
    @NotNull(message = "城市负责人不能为空!")
    private Integer manageAdminId;
    /**
     * 区域负责人
     */
    @NotNull(message = "区域负责人不能为空!")
    private Integer parentAdminId;
    /**
     * 总负责人
     */
    @NotNull(message = "部门负责人不能为空!")
    private Integer departmentAdminId;
    /**
     * 区域名称
     */
    @NotBlank(message = "区域名称不能为空!")
    @Size(max = 20)
    private String zoneName;
    /**
     * 下属城市no集合
     */
    @NotNull(message = "城市不能为空!")
    private List<Integer> subCity;

    /**
     * 所属行政城市
     */
    @NotNull(message = "区域不能为空!")
    private List<String> administrativeCitys;
}
