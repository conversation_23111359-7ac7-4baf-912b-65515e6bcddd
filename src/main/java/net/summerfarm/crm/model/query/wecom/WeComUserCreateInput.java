package net.summerfarm.crm.model.query.wecom;

import com.alibaba.fastjson.JSON;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/2/28 16:07
 */
@Data
public class WeComUserCreateInput {
    /**
     * 用户标识
     */
    private String userid;

    /**
     * 鲜沐用户id
     */
    private Long adminId;

    /**
     * 名字。
     */
    private String name;

    /**
     * 手机号码。企业内必须唯一，mobile/email二者不能同时为空
     */
    private String mobile;

    /**
     * 部门
     */
    private String department;

    public String toJson() {
        return JSON.toJSONString(this);
    }
}
