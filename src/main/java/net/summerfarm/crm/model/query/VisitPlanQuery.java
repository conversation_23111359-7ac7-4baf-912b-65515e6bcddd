package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/6 15:43
 */
@Data
public class VisitPlanQuery{

    /**
     * 客户id
     */
    private Long id;

    /**
     * 客户id
     */
    private Long mId;

    /**
     * 拜访类型,0拜访 1拉新 2陪访
     */
    private Integer type;

    /**
     * 联系人地址id
     */
    private Integer contactId;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 城市编号即可
     */
    private List<Integer> areaNos;

    /**
     * 客户名称
     */
    private String mname;

    /**
     * 拜访人id
     */
    private Integer adminId;

    /**
     * 状态 0 待拜访 1已拜访,2取消,4系统取消
     */
    private Integer status;

    private Integer source;

    /**
     * 期待拜访时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime expectedTime;

    /**
     * 开始时间*
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    /**
     * 拜访日
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate date;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}
