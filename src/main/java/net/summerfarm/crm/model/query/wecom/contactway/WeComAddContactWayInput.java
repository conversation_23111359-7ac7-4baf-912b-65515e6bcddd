package net.summerfarm.crm.model.query.wecom.contactway;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * 配置客户联系「联系我」方式 - 输入参数
 * 目前只支持配置单人的二维码
 */
@Data
@Builder
@AllArgsConstructor
public class WeComAddContactWayInput {

    /**
     * 联系方式的备注信息，用于助记，不超过30个字符
     */
    private String remark;

    /**
     * @see net.summerfarm.crm.enums.WeComContactWayEnum.State
     */
    private String state;

    @JSONField(name = "user")
    private String userId;
}
