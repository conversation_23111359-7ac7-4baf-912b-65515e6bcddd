package net.summerfarm.crm.model.query.riskMerchant;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店风控查询条件
 *
 * <AUTHOR>
 * @date 2023/11/17 17:47
 */
@Data
public class RiskMerchantQuery extends BasePageInput {
    /**
     * 门店 id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 门店类型
     */
    private String size;

    /**
     * 触发场景
     */
    private Integer triggerOccasions;

    /**
     * 命中分类:0:疑似重复;1:疑似虚假;2:疑似换壳;
     */
    private List<Integer> triggerClassification;

    /**
     * 状态 0:待处理 1:已处理
     */
    private Integer status;

    /**
     * 更新时间开始
     */
    private LocalDateTime updateTimeStart;
    /**
     * 更新时间结束
     */
    private LocalDateTime updateTimeEnd;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;
    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
}
