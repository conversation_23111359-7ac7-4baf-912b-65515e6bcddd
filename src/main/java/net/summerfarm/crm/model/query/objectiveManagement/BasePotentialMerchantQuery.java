package net.summerfarm.crm.model.query.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 潜力值客户查询基础类
 * 包含所有查询的共同字段
 *
 * <AUTHOR>
 */
@Data
public class BasePotentialMerchantQuery {

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String area;

    /**
     * poi位置信息
     */
    @NotNull(message = "poi位置信息不能为空")
    private PoiVO poi;

    /**
     * 查询距离（单位：米）
     */
    private Integer queryDistance;

    /**
     * 页码, 最小为1
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    /**
     * 每页数量, 最大200，最小为1
     */
    @NotNull(message = "每页数量不能为空")
    @Max(value = 200, message = "每页数量不能超过200")
    @Min(value = 1, message = "每页数量不能小于1")
    private Integer pageSize;

    /**
     * 跟进bdid
     */
    private Long bdId;
}