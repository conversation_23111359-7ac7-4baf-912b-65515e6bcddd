package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.enums.MerchantSkuEnum;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 大客户query
 * <AUTHOR>
 * @date 2022/11/2 10:54
 */
@Data
public class CrmKeyCustomerQuery extends BasePageInput {

    /**
     * areaNo
     */
    private Integer areaNo;

    private List<Integer> areaNoList;

    /**
     * bdId
     */
    private Integer bdId;


    /**
     * g=
     */
    private String  tag;

    /**
     * 关注bdId
     */
    private Integer carBdId;

    /**
     * 碉堡天数
     */
    private Integer dangerDay;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;


    private Boolean bd;

    /**
     * 客户类型
     */
    private String size;

    /**
     * 跟进 bd 不为空
     */
    private Boolean notEmptyBd;

    private Integer isLock;

    public void countInit(){
        this.setPageSize(1);
        this.setPageIndex(1);
    }

}
