package net.summerfarm.crm.model.query.task;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/28 14:32
 */
@Data
public class TaskListQuery extends BasePageInput {
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 状态:0:未开始;1:进行中;2:已结束;3:取消;
     */
    private Integer status;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * CRM-首页 当天任务
     */
    private LocalDate taskDay;
}
