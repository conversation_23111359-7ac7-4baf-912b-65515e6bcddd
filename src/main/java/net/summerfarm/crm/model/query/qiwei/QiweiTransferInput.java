package net.summerfarm.crm.model.query.qiwei;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QiweiTransferInput  {
    @JSONField(name = "handover_userid")
    private String handoverUserId;
    @JSONField(name = "takeover_userid")
    private String takeoverUserId;
    @JSONField(name = "external_userid")
    private List<String> externalUserId;
    @JSONField(name = "transfer_success_msg")
    private String transferSuccessMsg;
}
