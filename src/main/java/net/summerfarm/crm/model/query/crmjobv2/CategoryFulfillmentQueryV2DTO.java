package net.summerfarm.crm.model.query.crmjobv2;

import lombok.Data;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 品类履约任务查询条件
 */
@Data
public class CategoryFulfillmentQueryV2DTO {

    /**
     * 商品筛选
     * 筛选特定的商品item
     */
    private List<String> itemFilter;

    /**
     * 任务下的客户类型
     */
    private CrmJobEnum.JobMerchantLabel jobMerchantLabel;


    /**
     * 排序字段（品类履约任务特有）
     * 支持：hitItemCount(命中商品数), unfulfilledItemCount(未履约商品数), fulfilledItemCount(已履约商品数)
     */
    @Pattern(regexp = "^(hitItemCount|unfulfilledItemCount|fulfilledItemCount)$",
             message = "品类履约任务排序字段只支持: hitItemCount, unfulfilledItemCount, fulfilledItemCount")
    private String sortField;

    /**
     * 排序方向：asc(升序), desc(降序)
     */
    private SortDirectionEnum sortDirection = SortDirectionEnum.ASC;
}
