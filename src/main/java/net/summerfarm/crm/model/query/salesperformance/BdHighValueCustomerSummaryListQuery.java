package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import net.summerfarm.crm.enums.HighValueCustomerTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;

import javax.validation.constraints.Pattern;

@Data
public class BdHighValueCustomerSummaryListQuery {

    /**
     * 客户类型: 高价值, 准高价值, 潜力高价值
     */
    private HighValueCustomerTypeEnum customerType;

    /**
     * 排序字段: 客户数, 截止昨晚履约GMV, 截止昨晚履约SPU数, 截止今晚履约GMV, 截止今晚履约SPU数, 本月预计总履约GMV, 本月预计总履约SPU数
     * 客户数 - customerCount
     * 截止昨晚履约GMV - fulfillmentGmv
     * 截止昨晚履约SPU数 - spuCount
     * 截止今晚履约GMV - byTonightFulfillmentGmv
     * 截止今晚履约SPU数 - byTonightSpuCount
     * 本月预计总履约GMV - monthTotalFulfillmentGmv
     * 本月预计总履约SPU数 - monthTotalSpuCount
     */
    @Pattern(regexp = "^(customerCount|fulfillmentGmv|spuCount|byTonightFulfillmentGmv|byTonightSpuCount|monthTotalFulfillmentGmv|monthTotalSpuCount)$",
            message = "排序字段必须是 customerCount, fulfillmentGmv, spuCount, byTonightFulfillmentGmv, byTonightSpuCount, monthTotalFulfillmentGmv, monthTotalSpuCount 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     *
     * @see SortDirectionEnum
     */
    private SortDirectionEnum sortDirection;
}
