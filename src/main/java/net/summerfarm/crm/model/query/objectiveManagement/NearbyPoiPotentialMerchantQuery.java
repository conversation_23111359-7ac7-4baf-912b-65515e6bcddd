package net.summerfarm.crm.model.query.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import java.util.List;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 附近潜力值最高的客户查询VO
 *
 * <AUTHOR>
 */
@Data
public class NearbyPoiPotentialMerchantQuery {

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String area;

    /**
     * poi位置信息
     */
    private PoiVO poi;

    /**
     * 查询距离（单位：米）
     */
    private Integer queryDistance;

    /**
     * 页码, 最小为1
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    /**
     * 每页数量, 最大200，最小为1
     */
    @NotNull(message = "每页数量不能为空")
    @Max(value = 200, message = "每页数量不能超过200")
    @Min(value = 1, message = "每页数量不能小于1")
    private Integer pageSize;

    /**
     * 跟进bdid
     */
    private Long bdId;

    /**
     * 排序方式, 默认-拜访价值从高到底
     * 拜访价值，1：从高到低，2：从低到高
     * 距离近远，3：从近到远，4：从远到近
     */
    private Integer sortType;

    /**
     * 过滤的门店mId列表
     */
    private List<Long> filterMIdList;

    /**
     * 生命周期多选
     */
    private List<String> lifecycleList;

    /**
     * 高价值客户标签多选
     */
    private List<String> highValueLabelList;

    /**
     * 门店名称或地址搜索, 支持部分字符匹配
     */
    private String storeNameOrAddressSearch;

    /**
     * 过滤已添加拜访计划的门店
     */
    private Boolean filterVisitPlanMid;

    /**
     * 拜访类型，0：线下拜访，1：线上拜访
     */
    private Integer visitType;
}
