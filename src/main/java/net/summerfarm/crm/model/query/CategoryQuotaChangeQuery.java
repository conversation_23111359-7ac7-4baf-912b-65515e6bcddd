package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.enums.CategoryQuotaEnum;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2023/3/6 18:01
 */
@Data
public class CategoryQuotaChangeQuery extends BasePageInput {
    @NotNull(message = "admin不能为空")
    private Integer adminId;

    /**
     * 额度类型: 0:品类券;1:月活券
     */
    @NotNull(message = "额度类型不能为空")
    private Integer quotaType = CategoryQuotaEnum.QuotaType.CATEGORY.getCode();
}
