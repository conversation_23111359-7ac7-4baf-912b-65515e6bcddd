package net.summerfarm.crm.model.query.crmjobv2;

import lombok.Data;
import net.summerfarm.crm.enums.SortDirectionEnum;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

/**
 * 潜力高价值客户任务查询条件
 */
@Data
public class PotentialHighValueCustomerQueryV2DTO {

    /**
     * 达成情况筛选
     * 0 - SPU已达标; 1 - 履约GMV已达标
     */
    @Min(value = 0, message = "达成情况筛选值最小为0")
    @Max(value = 1, message = "达成情况筛选值最大为1")
    private Integer achievementFilter;

    /**
     * 准高价值客户筛选
     * 0 - 不看准高价值客户; 1 - 只看准高价值客户
     */
    @Min(value = 0, message = "准高价值客户筛选值最小为0")
    @Max(value = 1, message = "准高价值客户筛选值最大为1")
    private Integer highValueCustomerFilter;

    /**
     * 预测高价值客户筛选
     * 0 - 不看预测高价值客户; 1 - 只看预测高价值客户
     */
    @Min(value = 0, message = "预测高价值客户筛选值最小为0")
    @Max(value = 1, message = "预测高价值客户筛选值最大为1")
    private Integer predictedHighValueCustomerFilter;

    /**
     * 排序字段（潜力高价值客户任务特有）
     * 支持：fulfillmentGmv(履约GMV), spuCount(SPU数)
     */
    @Pattern(regexp = "^(fulfillmentGmv|spuCount)$",
             message = "潜力高价值客户任务排序字段只支持: fulfillmentGmv, spuCount")
    private String sortField;

    /**
     * 排序方向：asc(升序), desc(降序)
     */
    private SortDirectionEnum sortDirection = SortDirectionEnum.ASC;
}
