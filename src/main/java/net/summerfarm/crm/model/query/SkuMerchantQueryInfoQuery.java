package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/2 17:29
 */
@Data
public class SkuMerchantQueryInfoQuery {

    /***
     * mId
     */
    private Long mId;
    /***
     * sku
     */
    private String sku;
    /***
     * startTime
     */
    private LocalDateTime startTime;
    /***
     * endTime
     */
    private LocalDateTime endTime;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public SkuMerchantQueryInfoQuery() {
    }

    public SkuMerchantQueryInfoQuery(String sku) {
        this.sku = sku;
    }
}
