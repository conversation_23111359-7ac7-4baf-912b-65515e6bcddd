package net.summerfarm.crm.model.query.monthLiving;

import lombok.Data;
import net.summerfarm.crm.enums.CategoryQuotaFeeRateEnum;
import net.summerfarm.crm.enums.ProductCategoryTypeEnum;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class QuotaFeeRateInput {

    /**
     * 客户类型
     * @see CategoryQuotaFeeRateEnum.MerchantType#getCode()
     */
    @NotNull(message = "客户类型不能为空")
    private Integer merchantType;

    /**
     * 品类类型
     * @see ProductCategoryTypeEnum#getCode()
     */
    @NotNull(message = "品类类型不能为空")
    private Integer categoryType;

    /**
     * 费比
     */
    @NotNull(message = "费比不能为空")
    private BigDecimal feeRate;

}
