package net.summerfarm.crm.model.query.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * 销售拜访计划顺序查询
 *
 * <AUTHOR>
 */
@Data
public class BdVisitPlanSequenceQuery {

    /**
     * 起始点位poi
     */
    @NotNull(message = "起始点位poi不能为空")
    private PoiVO beginSitePoi;

    /**
     * 拜访点位列表
     */
    @Valid
    @NotEmpty(message = "拜访点位列表不能为空")
    private List<VisitSite> visitSites;

    @Data
    public static class VisitSite {

        /**
         * 点位id
         */
        @NotNull(message = "点位id不能为空")
        @Positive(message = "点位id必须大于0")
        private Long id;

        /**
         * 点位poi
         */
        @NotNull(message = "点位poi不能为空")
        private PoiVO poi;
    }
}
