package net.summerfarm.crm.model.query.wecom;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/**
 * 群发任务查询条件
 *
 * <AUTHOR>
 * @date 2024/2/26 15:25
 */
@Data
@Builder
public class WeComGroupMsgListInput {
    /**
     * 聊天类型 single 单聊 group 群聊
     */
    @JSONField(name = "chat_type")
    private String chatType;

    /**
     * 开始时间
     */
    @JSONField(name = "start_time")
    private Long startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "end_time")
    private Long endTime;

    public String toJson() {
        return JSON.toJSONString(this);
    }
}
