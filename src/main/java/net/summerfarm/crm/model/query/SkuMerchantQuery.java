package net.summerfarm.crm.model.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/1 18:07
 */
@Data
public class SkuMerchantQuery extends BasePageInput {

    /**
     * 运营服务区域
     */
    private Integer areaNo;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * sku
     */
    private String sku;

    /**
     * 信号表标识
     */
    private Integer monthTag;

    /**
     * sku集合
     */
    private List<String> skuList;

    /**
     * 0 上月下过单, 1 本月新开
     */
    private Integer merchantSkuType;

    /**
     * 销售id
     */
    private Integer adminId;
    /**
     * 类目id
     */
    private Long categoryId;
    /**
     * 商品id
     */
    private Long pdId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 商品ids
     */
    private List<Long> pdIds;
}
