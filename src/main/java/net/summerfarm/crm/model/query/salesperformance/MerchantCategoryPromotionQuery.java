package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import net.summerfarm.crm.enums.CategoryPromotionCustomerTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class MerchantCategoryPromotionQuery {

    /**
     * mId
     */
    @NotNull(message = "mId不能为空")
    private Long mId;

    /**
     * bdId
     * 不传时, 默认查看当前登录的销售的id
     */
    private Long bdId;

    /**
     * 客户类型: 存量客户, 增量客户
     */
    private CategoryPromotionCustomerTypeEnum customerType;

    /**
     * 排序字段: 奖励金额, 履约件数, 交易件数
     * 奖励金额 - rewardAmount
     * 履约件数 - fulfillmentCount
     * 交易件数 - transactionCount
     */
    @Pattern(regexp = "^(rewardAmount|fulfillmentCount|transactionCount)$",
            message = "排序字段必须是 rewardAmount, fulfillmentCount 或 transactionCount 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

    /**
     * 商品
     */
    private List<String> spuGroupList;
}
