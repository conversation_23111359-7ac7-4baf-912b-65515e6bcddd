package net.summerfarm.crm.model.query.task;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/9/28 15:06
 */
@Data
public class TaskDetailInsertInput {
    /**
     * 任务id
     */
    @NotNull(message = "任务 id 不能为空")
    private Integer taskId;
    /**
     * 导出场景不上传,上传的oss key
     */
    @NotBlank(message = "上传文件不能为空")
    private String ossKey;
}
