package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.HighValueCustomerTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.xianmu.common.input.BasePageInput;
import javax.validation.constraints.Pattern;

/**
 * 高价值客户查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HighValueCustomerQuery extends BasePageInput {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 客户类型: 高价值, 准高价值, 潜力高价值, 预测高价值
     */
    private HighValueCustomerTypeEnum custValueLabel;

    /**
     * 排序字段: 截止昨晚履约GMV, 截止昨晚履约SPU数, 截止今晚履约GMV, 截止今晚履约SPU数, 本月预计总履约GMV, 本月预计总履约SPU数
     * 截止昨晚履约GMV - fulfillmentGmv
     * 截止昨晚履约SPU数 - spuCount
     * 截止今晚履约GMV - byTonightFulfillmentGmv
     * 截止今晚履约SPU数 - byTonightSpuCount
     * 本月预计总履约GMV - monthTotalFulfillmentGmv
     * 本月预计总履约SPU数 - monthTotalSpuCount
     */
    @Pattern(regexp = "^(fulfillmentGmv|spuCount|byTonightFulfillmentGmv|byTonightSpuCount|monthTotalFulfillmentGmv|monthTotalSpuCount)$",
            message = "排序字段必须是 fulfillmentGmv, spuCount, byTonightFulfillmentGmv, byTonightSpuCount, monthTotalFulfillmentGmv, monthTotalSpuCount 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

    /**
     * 客户名称（用于搜索）
     */
    private String mname;
}
