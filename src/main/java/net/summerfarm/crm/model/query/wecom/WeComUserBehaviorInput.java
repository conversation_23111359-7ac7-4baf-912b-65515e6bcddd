package net.summerfarm.crm.model.query.wecom;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/26 14:45
 */
@Data
@Builder
public class WeComUserBehaviorInput {
    private List<String> userid;

    @JSONField(name = "start_time")
    private Long startTime;

    @JSONField(name = "end_time")
    private Long endTime;

    public String toJson() {
        return JSON.toJSONString(this);
    }
}
