package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.model.vo.BdSalesCityVo;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 16:59
 */
@Data
public class TeamDataQuery {
    /**
     * 商户ID
     */
    private Long mId;

    /**
     * 客户名称
     */
    private String mName;
    /**
     * 商户类别：单店，普通大客户，茶百道
     */
    private String mSize;

    /**
     * bd-id
     */
    private Integer bdId;

    /**
     * bd姓名
     */
    private String bdName;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 商户等级
     */
    private Integer grade;

    /**
     * 订单开始时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate startTime;

    /**
     * 订单结束时间
     */
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate endTime;

    /**
     * 登录ID
     */
    private Integer adminId;

    /**
     * 所属二级城市Nos
     */
    private List<Integer> areaNos;
    /**
     * 大客户ids
     */
    private List<Integer> adminIds;

    private List<BdSalesCityVo> salesCityList;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getmName() {
        return mName;
    }

    public void setmName(String mName) {
        this.mName = mName;
    }

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }
}
