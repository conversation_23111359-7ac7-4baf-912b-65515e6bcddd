package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmBdCity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 佣金激励指标查询query
 * @date 2022/6/14 14:42
 */
@Data
public class CommissionIncentiveIndexQuery {
    /**
     * 区域nos
     */
    private List<Integer> area;
    /**
     * 区域nos
     */
    private List<CrmBdCity> bdCities;
    /**
     * 销售主管id
     */
    private Integer cityAdminId;
    /**
     * 管理员名称
     */
    private String adminName;
}
