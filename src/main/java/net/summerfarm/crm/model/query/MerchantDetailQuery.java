package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.enums.MerchantSkuEnum;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/2 10:54
 */
@Data
public class MerchantDetailQuery extends BasePageInput {

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 信号表标识
     */
    private Integer dayTag;

    /**
     * 信号表标识
     */
    private Integer monthTag;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * areaNo
     */
    private Integer areaNo;

    /**
     * 商户行业
     */
    private String type;

    /**
     * @see MerchantSkuEnum.Type
     */
    private Integer merchantOrderTimeType;

    /**
     * 是否查询绩效二期数据
     */
    private Boolean queryPerformanceV2;

}
