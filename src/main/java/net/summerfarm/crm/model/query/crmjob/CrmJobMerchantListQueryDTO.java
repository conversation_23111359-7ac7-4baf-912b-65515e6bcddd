package net.summerfarm.crm.model.query.crmjob;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class CrmJobMerchantListQueryDTO extends BasePageInput {

    /**
     * 任务id
     */
    @NotNull(message = "任务id不能为空")
    private Long jobId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 查询范围，0：私海客户，1：公海客户。该参数只对M1及以上生效
     */
    private Integer queryScope;

    /**
     * 任务下的客户类型
     */
    private CrmJobEnum.JobMerchantLabel jobMerchantLabel;

    /**
     * 是否已拜访
     */
    private Boolean followedUp;

    /**
     * 完成状态. 0:未完成;1:已完成
     */
    private Integer status;
}
