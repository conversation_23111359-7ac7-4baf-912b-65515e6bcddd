package net.summerfarm.crm.model.query.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 推荐潜力值客户查询VO
 *
 * <AUTHOR>
 */
@Data
public class RecommendPotentialMerchantQuery {

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 查询位置信息
     */
    private PoiVO poi;

    /**
     * 查询距离（单位：米）
     */
    private Integer queryDistance;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    @Min(value = 1, message = "页大小不能小于1")
    @Max(value = 100, message = "页大小不能超过100")
    private Integer pageSize;

    /**
     * BD ID
     */
    private Long bdId;

    /**
     * 推荐数量, 大于1，小于100
     * 同线下拜访数量
     */
    @NotNull(message = "推荐数量不能为空")
    @Max(value = 200, message = "推荐数量不能超过100")
    @Min(value = 1, message = "推荐数量不能小于1")
    private Integer recommendCount;

    /**
     * 交通方式, 1-步行, 2-骑行 3-驾车
     */
    private Integer trafficType;

    /**
     * 过滤的门店mId列表
     */
    private List<Long> filterMIdList;

    /**
     * 生命周期多选
     */
    private List<String> lifecycleList;

    /**
     * 高价值客户标签多选
     */
    private List<String> highValueLabelList;

    /**
     * 门店名称或地址搜索, 支持部分字符匹配
     */
    private String storeNameOrAddressSearch;

    /**
     * 过滤已添加拜访计划的门店
     */
    private Boolean filterVisitPlanMid;

    /**
     * 拜访类型，0：线下拜访，1：线上拜访
     */
    private Integer visitType;
}
