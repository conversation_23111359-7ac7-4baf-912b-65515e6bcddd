package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/17 9:57
 */
@Data
public class PrivateSeaQuery {
    /**
     * 商户id
     */
    private Integer mId;
    /**
     * 城市编号
     */
    private Integer areaNo;
    /**
     * 客户名称
     */
    private String mname;
    /**
     * 会员等级
     */
    private Integer grade;
    /**
     * 生命周期：0新注册，1首单，2非稳，3稳定
     */
    private Integer lifecycle;
    /**
     * 当月是否下单
     */
    private Boolean orderCurrentMonth;
    /**
     * 下单预警标识
     */
    private Boolean orderCycleWarn;
    /**
     * 销售id
     */
    private Integer adminId;
    /**
     * 核心客户标记:否(0),是(1)
     */
    private Integer coreMerchantTag;
    /**
     * 商户ids
     */
    private List<Integer> ids;
    /**
     * 数据所在时间标识
     */
    private Integer dataTag;
    /**
     * 排序方式:2,倒计时升序,3倒计时降序,0 gmv升序,1 gmv降序(默认)
     */
    private Integer sortType;
    /**
     * 商户标签
     */
    private String merchantLabel;
    /**
     * 数据所在时间标识
     */
    private Integer merchantLabelDataTag;

    public Integer getmId() {
        return mId;
    }

    public void setmId(Integer mId) {
        this.mId = mId;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }
}
