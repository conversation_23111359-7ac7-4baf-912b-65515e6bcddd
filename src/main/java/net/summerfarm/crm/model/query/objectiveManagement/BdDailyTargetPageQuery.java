package net.summerfarm.crm.model.query.objectiveManagement;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售每日目标分页查询
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BdDailyTargetPageQuery extends BasePageInput {

    /**
     * 销售id列表
     */
    private List<Integer> bdIds;

    /**
     * 目标起始日期
     */
    private LocalDate targetBeginDate;

    /**
     * 目标截止日期
     */
    private LocalDate targetEndDate;

    /**
     * 目标类型列表，1：拉新客户数，2：月活客户数，3：高价值客户数，4：平台总目标，5：品类目标，6：sku目标，7：spu目标
     */
    private List<Integer> targetTypes;

    /**
     * 目标制定人id列表
     */
    private List<Integer> targetCreators;

    /**
     * 排序字段，bdId：销售名称，targetCreator：目标制定人，targetDate：目标日期
     */
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

}
