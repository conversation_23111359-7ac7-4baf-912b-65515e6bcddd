package net.summerfarm.crm.model.query.task;

import lombok.Data;
import net.summerfarm.crm.model.domain.CrmTask;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/28 14:42
 */
@Data
public class TaskInsertInput {
    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;
    /**
     * 类型
     */
    @NotNull(message = "关联操作不能为空")
    private Integer type;
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    public CrmTask toBean() {
        CrmTask crmTask = new CrmTask();
        crmTask.setTaskName(this.getTaskName());
        crmTask.setType(this.getType());
        crmTask.setStartTime(this.getStartTime());
        crmTask.setEndTime(this.getEndTime());
        return crmTask;
    }
}
