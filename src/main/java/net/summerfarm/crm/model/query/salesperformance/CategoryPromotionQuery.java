package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.CategoryPromotionCustomerTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 品类推广查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CategoryPromotionQuery extends BasePageInput {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 客户类型: 存量客户, 增量客户
     */
    private CategoryPromotionCustomerTypeEnum customerType;

    /**
     * 排序字段: 奖励金额, 履约件数, 交易件数
     * 奖励金额 - rewardAmount
     * 履约件数 - fulfillmentCount
     * 交易件数 - transactionCount
     */
    @Pattern(regexp = "^(rewardAmount|fulfillmentCount|transactionCount)$",
            message = "排序字段必须是 rewardAmount, fulfillmentCount 或 transactionCount 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

    /**
     * 客户名称（用于搜索）
     */
    private String mname;

    /**
     * 商品
     */
    private List<String> spuGroupList;
}
