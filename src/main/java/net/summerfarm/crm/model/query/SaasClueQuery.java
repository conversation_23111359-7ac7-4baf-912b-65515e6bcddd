package net.summerfarm.crm.model.query;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SaasClueQuery extends BasePageInput {
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 线索状态
     */
    private String clueStatus;

    /**
     * 意向业务 ,分割
     */
    private String wantBusiness;

    /**
     * 经营类型
     */
    private String businessType;

    /**
     * bd_id
     */
    private Integer bdId;


    /**
     * 地区码
     */
    private List<Integer> areaCodes;


    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 客户来源 0 鲜沐大客户 1新增品牌
     */
    private Integer customerSource;

    /**
     * 线索来源
     */
    private String clueSource;


    private Long bId;
    public  List<String>  getClueStatusList(){
        if (StringUtils.isEmpty(clueStatus)){
            return new ArrayList<>();
        }
        return Arrays.asList(clueStatus.split(","));
    }

    public  List<String>  getWantBusinessList(){
        if (StringUtils.isEmpty(wantBusiness)){
            return new ArrayList<>();
        }
        return Arrays.asList(wantBusiness.split(","));
    }


}

