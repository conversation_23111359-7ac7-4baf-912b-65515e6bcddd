package net.summerfarm.crm.model.query.objectiveManagement;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import java.util.List;

/**
 * ES潜力值客户查询统一参数模型
 *
 * <AUTHOR>
 */
@Data
public class EsPotentialMerchantQuery {

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 查询位置信息
     */
    private PoiVO poi;

    /**
     * 查询距离（单位：米）
     */
    private Integer queryDistance;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * BD ID
     */
    private Long bdId;

    /**
     * 排序类型：1-潜力值排序，2-地理距离排序
     */
    private Integer sortType;

    /**
     * 查询类型：1-附近查询，2-推荐查询
     */
    private Integer queryType;

    /**
     * 地理位置字段名称（附近查询用contacts.poi，推荐查询用location）
     */
    private String geoFieldName;

    /**
     * 是否需要基础过滤条件（附近查询需要islock和operateStatus过滤）
     */
    private Boolean needBaseFilter;

    /**
     * 是否需要潜力值过滤（推荐查询需要potentialValue > 0）
     */
    private Boolean needPotentialFilter;

    /**
     * BD字段名称（附近查询用bdId，推荐查询用bd_id）
     */
    private String bdFieldName;

    /**
     * 省市区字段是否需要keyword后缀
     */
    private Boolean needKeywordSuffix;

    /**
     * 过滤的门店mId列表
     */
    private List<Long> filterMIdList;

    /**
     * 生命周期多选
     */
    private List<String> lifecycleList;

    /**
     * 高价值客户标签多选
     */
    private List<String> highValueLabelList;

    /**
     * 门店名称或地址搜索, 支持部分字符匹配
     */
    private String storeNameOrAddressSearch;

    /**
     * 从NearbyPoiPotentialMerchantQuery转换
     */
    public static EsPotentialMerchantQuery fromNearbyQuery(NearbyPoiPotentialMerchantQuery query) {
        EsPotentialMerchantQuery esQuery = new EsPotentialMerchantQuery();
        esQuery.setProvince(query.getProvince());
        esQuery.setCity(query.getCity());
        esQuery.setArea(query.getArea());
        esQuery.setPoi(query.getPoi());
        esQuery.setQueryDistance(query.getQueryDistance());
        esQuery.setPageNum(query.getPageNum());
        esQuery.setPageSize(query.getPageSize());
        esQuery.setBdId(query.getBdId());
        esQuery.setFilterMIdList(query.getFilterMIdList());
        esQuery.setLifecycleList(query.getLifecycleList());
        esQuery.setHighValueLabelList(query.getHighValueLabelList());
        esQuery.setStoreNameOrAddressSearch(query.getStoreNameOrAddressSearch());
        
        // 转换排序类型：1,2-拜访价值排序 -> 1，3,4-距离排序 -> 2
        Integer sortType = query.getSortType();
        if (sortType != null && (sortType == 3 || sortType == 4)) {
            esQuery.setSortType(2); // 地理距离排序
        } else {
            esQuery.setSortType(1); // 潜力值排序（默认）
        }
        
        // 附近查询特有配置
        esQuery.setQueryType(1);
        esQuery.setGeoFieldName("contacts.poi");
        esQuery.setNeedBaseFilter(true);
        esQuery.setNeedPotentialFilter(false);
        esQuery.setBdFieldName("bdId");
        esQuery.setNeedKeywordSuffix(false);
        
        return esQuery;
    }

    /**
     * 从RecommendPotentialMerchantQuery转换
     */
    public static EsPotentialMerchantQuery fromRecommendQuery(RecommendPotentialMerchantQuery query, Integer sortType) {
        EsPotentialMerchantQuery esQuery = new EsPotentialMerchantQuery();
        esQuery.setProvince(query.getProvince());
        esQuery.setCity(query.getCity());
        esQuery.setArea(query.getArea());
        esQuery.setPoi(query.getPoi());
        esQuery.setQueryDistance(query.getQueryDistance());
        esQuery.setPageNum(query.getPageNum());
        esQuery.setPageSize(query.getPageSize());
        esQuery.setBdId(query.getBdId());
        esQuery.setFilterMIdList(query.getFilterMIdList());
        esQuery.setLifecycleList(query.getLifecycleList());
        esQuery.setHighValueLabelList(query.getHighValueLabelList());
        esQuery.setStoreNameOrAddressSearch(query.getStoreNameOrAddressSearch());
        esQuery.setSortType(sortType);
        
        // 推荐查询特有配置
        esQuery.setQueryType(2);
        esQuery.setGeoFieldName("contacts.poi");
        esQuery.setNeedBaseFilter(true);
        esQuery.setNeedPotentialFilter(true);
        esQuery.setBdFieldName("bdId");
        esQuery.setNeedKeywordSuffix(true);
        
        return esQuery;
    }
}