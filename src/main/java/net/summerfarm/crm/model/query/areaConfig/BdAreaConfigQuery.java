package net.summerfarm.crm.model.query.areaConfig;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/22 13:59
 */
@Data
public class BdAreaConfigQuery extends BasePageInput {
    /**
     * m3
     */
    private Integer departmentAdminId;
    /**
     * m2 id
     */
    private Integer managerAdminId;
    /**
     * m1 id
     */
    private Integer cityAdminId;
    /**
     * 销售区域名称
     */
    private String salesAreaName;
    /**
     * 省
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区
     */
    private String area;

    /**
     * 级别 1:m3;2:m2;3:m1;4:bd; {@link net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank}
     */
    private List<Integer> rank;
}
