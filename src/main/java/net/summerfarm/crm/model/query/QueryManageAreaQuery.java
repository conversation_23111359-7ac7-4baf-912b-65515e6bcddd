package net.summerfarm.crm.model.query;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QueryManageAreaQuery implements Serializable {

    private static final long serialVersionUID = -6292093125492713737L;

    /**
     * 区域负责人Id
     */
    private String parentAdminId;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 区域编号
     */
    private List<Integer> areaNo;
    /**
     * 部门负责人id
     */
    private Integer departmentAdminId;
}
