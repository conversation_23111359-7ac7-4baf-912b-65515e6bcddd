# CRM任务查询V2系统使用说明

## 概述

V2查询系统采用组合模式设计，支持新任务类型的特殊查询需求。每个任务类型的特有字段（包括排序字段）都在各自的子类中定义，确保类型安全和职责分离。

## 核心设计

### 1. 组合模式结构

```
CrmJobMerchantListQueryV2DTO (主查询类)
├── 通用字段 (jobId, mname, status等)
├── 通用排序字段 (commonSortField: realTotalAmt)
├── 排序方向 (sortDirection: ASC/DESC)
└── 任务类型特有查询条件
    ├── PotentialHighValueCustomerQueryV2DTO (潜力高价值客户)
    └── CategoryFulfillmentQueryV2DTO (品类履约)
```

### 2. 排序字段分布

- **通用排序字段**：在主查询类中，支持 `realTotalAmt`
- **特定排序字段**：在各自的子查询类中
  - 潜力高价值客户：`fulfillmentGmv`, `spuCount`
  - 品类履约：`hitItemCount`, `unfulfilledItemCount`, `fulfilledItemCount`

## 使用示例

### 1. 潜力高价值客户任务查询

```json
{
  "jobId": 123,
  "taskType": 5,
  "commonSortField": "realTotalAmt",
  "sortDirection": "DESC",
  "potentialHighValueCustomerQuery": {
    "achievementFilter": 1,
    "highValueCustomerFilter": 2,
    "spuThreshold": 10,
    "gmvThreshold": 50000.00,
    "sortField": "fulfillmentGmv",
    "sortDirection": "DESC"
  }
}
```

### 2. 品类履约任务查询

```json
{
  "jobId": 456,
  "taskType": 6,
  "commonSortField": "realTotalAmt",
  "sortDirection": "ASC",
  "categoryFulfillmentQuery": {
    "itemFilter": ["商品A", "商品B"],
    "fulfillmentStatusFilter": 1,
    "sortField": "hitItemCount",
    "sortDirection": "DESC"
  }
}
```

### 3. 普通任务查询

```json
{
  "jobId": 789,
  "taskType": 1,
  "commonSortField": "realTotalAmt",
  "sortDirection": "DESC"
}
```

## 验证规则

### 1. 通用字段验证

- `jobId`: 必填
- `taskType`: 必填
- `commonSortField`: 只支持 `realTotalAmt`
- `sortDirection`: 只支持 `ASC` 或 `DESC`

### 2. 潜力高价值客户任务验证

- `achievementFilter`: 0-3之间
- `highValueCustomerFilter`: 0-2之间
- `spuThreshold`: 非负数
- `gmvThreshold`: 非负数
- `sortField`: 只支持 `fulfillmentGmv`, `spuCount`

### 3. 品类履约任务验证

- `itemFilter`: 最多100个商品
- `fulfillmentStatusFilter`: 0-2之间
- `sortField`: 只支持 `hitItemCount`, `unfulfilledItemCount`, `fulfilledItemCount`

## 接口地址

- **管理端任务列表**: `POST /crm-service/crm-job-query-v2/query/list-all`
- **销售端任务列表**: `POST /crm-service/crm-job-query-v2/query/list-for-bd`
- **管理端门店列表**: `POST /crm-service/crm-job-query-v2/query/detail/merchant-list-all`
- **销售端门店列表**: `POST /crm-service/crm-job-query-v2/query/detail/merchant-list-for-bd`

## 排序优先级

1. **子类排序字段**：如果指定了任务类型特有的排序字段，优先使用
2. **通用排序字段**：如果没有指定特有排序字段，使用通用排序字段
3. **默认排序**：如果都没有指定，使用默认排序（通常是创建时间）

## 扩展新任务类型

1. 创建新的查询DTO类（如 `NewTaskTypeQueryV2DTO`）
2. 在主查询类中添加新的字段
3. 创建对应的策略类实现 `MerchantQueryStrategy`
4. 在 `CrmJobQueryServiceV2Impl` 中注册新策略
5. 创建对应的VO数据类

## 注意事项

- 使用 `@Valid` 注解确保子对象的验证生效
- 排序字段使用 `@Pattern` 注解进行严格验证
- 每个任务类型的排序字段都有独立的枚举定义
- 策略模式确保了不同任务类型的查询逻辑隔离
