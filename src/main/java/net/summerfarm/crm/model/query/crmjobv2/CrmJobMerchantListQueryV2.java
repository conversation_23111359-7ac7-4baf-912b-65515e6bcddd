package net.summerfarm.crm.model.query.crmjobv2;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * CRM任务门店列表查询V2DTO - 组合模式
 * 包含通用字段 + 各任务类型特有字段的组合
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CrmJobMerchantListQueryV2 extends BasePageInput {

    // ==================== 通用字段 ====================

    /**
     * 任务id
     */
    @NotNull(message = "任务id不能为空")
    private Long jobId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 是否已拜访
     */
    private Boolean followedUp;

    /**
     * 完成状态. 0:未完成;1:已完成
     */
    private Integer status;

    // ==================== 各任务类型特有字段组合 ====================

    /**
     * 潜力高价值客户任务特有查询条件
     * 仅当任务类型为 [潜力高价值客户] 时有效，其他情况会被忽略
     */
    @Valid
    private PotentialHighValueCustomerQueryV2DTO potentialHighValueCustomerQuery;

    /**
     * 品类履约任务特有查询条件
     * 仅当任务类型为 [品类履约] 时有效，其他情况会被忽略
     */
    @Valid
    private CategoryFulfillmentQueryV2DTO categoryFulfillmentQuery;
}
