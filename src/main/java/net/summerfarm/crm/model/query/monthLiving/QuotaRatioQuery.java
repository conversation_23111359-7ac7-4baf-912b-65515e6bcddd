package net.summerfarm.crm.model.query.monthLiving;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 14:31
 */
@Data
public class QuotaRatioQuery {
    /**
     * 人员 id
     */
    @NotNull(message = "adminId不能为空")
    private Integer adminId;

    /**
     * 额度类型: 0:品类券;1:月活券
     */
    @NotNull(message = "额度类型不能为空")
    private Integer quotaType;

    /**
     * 新客户费比
     */
    // @NotNull(message = "新客费比不能为空")
    private BigDecimal newCustomerRate;

    /**
     * 老客户费比
     */
    private BigDecimal oldCustomerRate;

    /**
     * 备注
     */
    private String remark;

    @Valid
    @NotNull(message = "费比列表不能为空")
    private List<QuotaFeeRateInput> feeRateInputList;
}
