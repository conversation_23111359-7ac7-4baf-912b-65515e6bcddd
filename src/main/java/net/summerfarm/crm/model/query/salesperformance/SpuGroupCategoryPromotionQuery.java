package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.CategoryPromotionCustomerTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.xianmu.common.input.BasePageInput;
import java.util.List;
import javax.validation.constraints.Pattern;

/**
 * 商品维度品类推广查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SpuGroupCategoryPromotionQuery extends BasePageInput {

    /**
     * 销售ID列表
     */
    private List<Long> bdIds;

    /**
     * 客户类型: 存量客户, 增量客户
     */
    private CategoryPromotionCustomerTypeEnum customerType;

    /**
     * 排序字段: 客户数, 奖励金额, 履约件数, 交易件数
     * 客户数 - customerCount
     * 奖励金额 - rewardAmount
     * 履约件数 - fulfillmentCount
     * 交易件数 - transactionCount
     */
    @Pattern(regexp = "^(customerCount|rewardAmount|fulfillmentCount|transactionCount)$",
            message = "排序字段必须是 customerCount, rewardAmount, fulfillmentCount 或 transactionCount 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

}
