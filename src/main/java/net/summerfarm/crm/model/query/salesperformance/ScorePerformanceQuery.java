package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.PbScoreMerchantTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.xianmu.common.input.BasePageInput;
import javax.validation.constraints.Pattern;

/**
 * 利润积分得分查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ScorePerformanceQuery extends BasePageInput {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 客户名称（用于搜索）
     */
    private String mname;

    /**
     * 客户类型: 平台客户（单店），大客户
     */
    private PbScoreMerchantTypeEnum merchantType;

    /**
     * 排序字段: 利润积分
     * 利润积分 - score
     */
    @Pattern(regexp = "^(score)$",
            message = "排序字段必须是 score ")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

}
