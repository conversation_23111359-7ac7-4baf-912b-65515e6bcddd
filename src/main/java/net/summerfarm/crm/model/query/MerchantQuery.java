package net.summerfarm.crm.model.query;

import lombok.Data;
import lombok.ToString;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.CategoryPromotionCustomerTypeEnum;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/24 16:15
 */
@Data
@ToString
public class MerchantQuery {

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 商户名
     */
    private String mname;

    /**
     * 商户id列表
     */
    private List<Long> mIds;

    /**
     * 运营区域
     */
    private Integer areaNo;

    /**
     * 跟进bdId
     */
    private Integer bdId;

    /**
     * 会员等级
     */
    private Integer grade;

    /**
     * 省心送标签 0 无 1 是省心送
     */
    private Integer timingFollowType;

    /**
     * 是否在流转白名单,1在、0或者空，不在
     */
    private Integer whiteListType;

    /**
     * 当月是否下单
     */
    private Boolean orderCurrentMonth;

    /**
     * 0,正常经营 1,倒闭
     */
    private Integer operateStatus;

    /**
     * 0,正常经营 1,倒闭
     */
    private List<Integer> operateStatusList;

    /**
     * 客户标签
     */
    private String merchantLabel;

    /**
     * 核心客户标记:否(0),是(1)
     */
    private Integer coreMerchantTag;

    /**
     * 生命周期
     */
    private String lifecycle;

    /**
     * 排序方式:2,倒计时升序,3倒计时降序,0 gmv升序,1 gmv降序,4 最新下单时间升序,5 最新下单时间降序
     */
    private Integer sortType;

    /**
     * 未下单天数区间值 例如0~7天未下单:[0,7]
     */
    private List<Integer> notOrderList;
    /**
     * areaNos
     */
    private Set<Integer> areaNoSet;

    /**
     * '审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑'
     */
    private Integer isLock;

    /**
     * 注册时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime registerTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 管理员id
     */
    private Integer adminId;
    /**
     * 大客户,单店
     */
    private String size;

    /**
     * 本月是否拜访,0否1是
     */
    private Integer followMerchant;

    /**
     * 倒计时筛选最低值
     */
    private Integer dangerDayMinimum;

    /**
     * 倒计时筛选最高值
     */
    private Integer dangerDayMaximum;

    /**
     * r值
     */
    private String rValue;

    /**
     * f值
     */
    private String fValue;

    /**
     * m值
     */
    private String mValue;

    /**
     * 是否在30天内登录
     */
    private Boolean isLoggedInWithinThirtyDays;

    /**
     * 查询标签 lifecycle:生命周期;r:r值;f:f值;m:m值;
     */
    private String queryTag;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 是否添加官微:0:是;1:否；
     */
    private Integer officialWechatFlag;
    /**
     *  是否添加销微:0:是;1:否；
     */
    private Integer bdWechatFlag;

    /**
     * 是否剔除上门拜访后可捞回
     */
    private Boolean filterReclaimable;

    /**
     * 客户价值标签
     */
    private String valueLabel;

    /**
     * 业务线. 0: 鲜沐. 1: pop
     */
    private Integer businessLine;

    /**
     * 高价值客户标签
     */
    private String highValueLabel;

    /**
     * 是否查询绩效二期的数据
     */
    private Boolean queryPerformanceV2;

    /**
     * 品类推广商品
     */
    private List<String> spuGroupList;

    /**
     * 客户类型: 存量客户, 增量客户
     */
    private CategoryPromotionCustomerTypeEnum customerType;

    /**
     * 是否完成品类推广奖励. 0:未完成;1:已完成
     */
    private Integer completionRewardStatus;

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }
}
