package net.summerfarm.crm.model.query.saasorder;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class SaasOrderListQuery extends BasePageInput {

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime deliveryTime;

    /**
     * 注册城市-省
     */
    private String province;

    /**
     * 注册城市-市
     */
    private String city;

    /**
     * 注册城市-区
     */
    private String area;

    private String mname;

    /**
     * 订单状态
     * @see net.summerfarm.crm.enums.SaasOrderEnum.Status
     */
    private Integer status;
}
