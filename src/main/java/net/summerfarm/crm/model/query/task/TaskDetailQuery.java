package net.summerfarm.crm.model.query.task;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/28 15:57
 */
@Data
public class TaskDetailQuery extends BasePageInput {
    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 状态 0:未完成;1:已完成
     */
    private Integer status;

    /**
     * 销售id
     */
    private List<Integer> bdId;

    /**
     * 销售名字
     */
    private String bdName;
}
