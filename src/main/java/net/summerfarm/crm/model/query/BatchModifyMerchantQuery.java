package net.summerfarm.crm.model.query;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BatchModifyMerchantQuery implements Serializable {
    private static final long serialVersionUID = 4948706801459777220L;

    /**
     * 主键、自增
     */
    private List<Integer> id;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 新销售提成
     */
    private BigDecimal newBdReward;

    /**
     * 其他销售提成
     */
    private BigDecimal normalBdReward;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    private LocalDateTime createTime;


}
