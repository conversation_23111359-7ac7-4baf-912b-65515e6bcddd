package net.summerfarm.crm.model.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/25 14:10
 */
@Data
public class MerchantSituationQuery {

    private Long id;

    /**
     * areaNo
     */
    private Integer areaNo;
    /**
     * 审核状态: 状态 0待审核  2审批通过 3 申请未通过
     */
    private Integer status;
    /**
     * 搜索关键字: 客户名称或发起人名称
     */
    private String keyword;
    /**
     * 客户名称
     */
    private String mname;

    /**
     * 客户mid列表
     */
    private List<Long> midList;

    /**
     * 发起人名称
     */
    private String creatorName;
    /**
     * 发起人id
     */
    private Integer creatorId;
    /**
     * 根据销售id
     */
    private Integer adminId;
    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;

}
