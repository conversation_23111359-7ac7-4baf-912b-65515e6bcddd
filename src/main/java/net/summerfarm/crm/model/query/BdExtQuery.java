package net.summerfarm.crm.model.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 销售拓展信息查询
 * @date 2022/6/18 22:21
 */
@Data
public class BdExtQuery {
    /**
     * id
     */
    private Integer adminId;
    /**
     * 销售ids
     */
    private List<Integer> adminIds;
    /**
     * name
     */
    private String adminName;
    /**
     * 区域no
     */
    private Integer areaNo;
    /**
     * 区域no
     */
    private Integer zoneNo;
    /**
     * 运营大区状态:1开放.0不开放
     */
    private Integer largeStatus;
    /**
     * 销售信息类型
     * 0 获取具有销售属性的admin(包括超管,销售主管,区域超管)
     * 1 获取具有销售角色的(不包括超管,销售主管,区域超管)
     * 2 获取单个运营区域的销售主管
     * 3 获取销售的销售主管
     * 4 获取销售主管管理的销售
     * 5 获取具有销售主管角色的管理员
     */
    private Integer infoType;
    /**
     * 销售区域信息类型
     * 0 获取当前登录角色具有权限的地区,需数据权限与销售激励指标配置的区域重合:运营大区-运营区域
     * 1 获取 销售区域-运营区域 需销售激励指标配置的区域
     */
    private Integer areaType;


    private List<Long> baseUserIds;

}
