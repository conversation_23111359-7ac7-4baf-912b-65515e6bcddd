package net.summerfarm.crm.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 省心送query
 *
 * <AUTHOR>
 * @date 2022/11/2 10:54
 */
@Data
public class CrmComfortSendQuery  implements Serializable {


    /**
     * 配送类型
     * SEVEN_DAY_NO_SEND 近7天为配送
     * FINISH 已配送完成
     * WILL_FINISH 即将配送完成
     */
    private String type;

    private Integer dayTag;

    private Integer pageIndex;

    private Integer pageSize;

    private String tableName;

    private Integer bdId;

    /**
     * areaNo
     */
    private Integer areaNo;

    private List<Integer> areaNos;
    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String area;
}
