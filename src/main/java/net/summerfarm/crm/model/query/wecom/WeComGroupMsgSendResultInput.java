package net.summerfarm.crm.model.query.wecom;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Data;

/**
 * 群发任务查询条件
 *
 * <AUTHOR>
 * @date 2024/2/26 15:25
 */
@Data
@Builder
public class WeComGroupMsgSendResultInput {
    /**
     * 消息id
     */
    private String msgid;

    /**
     * 用户标识
     */
    private String userid;

    public String toJson() {
        return JSON.toJSONString(this);
    }
}
