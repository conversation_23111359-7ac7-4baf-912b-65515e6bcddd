package net.summerfarm.crm.model.query.crmjob;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class CrmJobQueryDTO extends BasePageInput {

    /**
     * 任务id
     */
    private Long id;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务类型
     *
     * @see net.summerfarm.crm.enums.CrmJobEnum.Type
     */
    private Integer type;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务状态
     *
     * @see net.summerfarm.crm.enums.CrmJobEnum.Status
     */
    private Integer status;

    /**
     * 任务创建人
     */
    private Long creator;
}
