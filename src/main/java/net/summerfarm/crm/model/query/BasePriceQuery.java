package net.summerfarm.crm.model.query;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;

/**
 * 核心品类查询条件
 *
 * <AUTHOR>
 * @Date 2023/3/6 15:47
 */
@Data
public class BasePriceQuery extends BasePageInput implements Serializable {

    /**
     * 区域编号
     */
    private String areaNo;
    /**
     * sku
     */
    private String sku;
    /**
     * 产品名称
     */
    private String pdName;
    /**
     * 产品id
     */
    private Integer pdId;
}
