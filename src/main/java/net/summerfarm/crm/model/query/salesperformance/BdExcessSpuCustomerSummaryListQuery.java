package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import net.summerfarm.crm.enums.SortDirectionEnum;

import javax.validation.constraints.Pattern;

/**
 * 销售超标SPU客户汇总列表查询参数
 */
@Data
public class BdExcessSpuCustomerSummaryListQuery {

    /**
     * 排序字段: 客户数, 超标SPU数
     * 客户数 - customerCount
     * 超标SPU数 - excessSpuCount
     */
    @Pattern(regexp = "^(customerCount|excessSpuCount)$",
            message = "排序字段必须是 customerCount 或 excessSpuCount 之一")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;
}
