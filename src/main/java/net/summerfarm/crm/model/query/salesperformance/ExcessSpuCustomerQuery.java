package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.Pattern;

/**
 * 超标SPU客户查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExcessSpuCustomerQuery extends BasePageInput {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 排序字段: SPU数
     * spu数 - spuCount
     */
    @Pattern(regexp = "^(excessSpuCount)$",
            message = "排序字段必须是 excessSpuCount")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

    /**
     * 客户名称（用于搜索）
     */
    private String mname;
}
