package net.summerfarm.crm.model.query;/**
 * <AUTHOR>
 * @date 2023/1/5 16:27
 */

import lombok.Data;
import net.summerfarm.manage.client.wms.dto.req.WarehouseQueryReq;
import net.summerfarm.manage.client.wms.dto.res.WarehouseBatchProveRecordDTO;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/1/5 16:27
 */
@Data
public class WarehouseQuery {

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

    /**
     * 商品ID
     */
    private Long pdId;

    /**
     * 生产日期开始时间
     */
    private LocalDate productionDateStart;

    /**
     * 生产日期结束时间
     */
    private LocalDate productionDateEnd;
    /**
     * sku
     */
    private String sku;

    public WarehouseQueryReq toWarehouseQueryReq(WarehouseQuery param) {
        WarehouseQueryReq warehouseQueryReq = WarehouseQueryReq.builder()
        		.warehouseNo(param.getWarehouseNo())
        		.pdId(param.getPdId())
        		.productionDateStart(param.getProductionDateStart())
        		.productionDateEnd(param.getProductionDateEnd())
                .sku(param.getSku())
        		.build();
        return warehouseQueryReq;
    }
}
