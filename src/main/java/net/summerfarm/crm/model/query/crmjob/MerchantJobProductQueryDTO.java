package net.summerfarm.crm.model.query.crmjob;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class MerchantJobProductQueryDTO extends BasePageInput {

    @NotNull(message = "任务ID不能为空")
    private Long jobId;

    @NotNull(message = "门店ID不能为空")
    private Long mId;
}
