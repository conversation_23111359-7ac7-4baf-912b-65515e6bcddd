package net.summerfarm.crm.model.query.salesdata;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class AreaPerformanceQueryDTO {

    /**
     * 销售区域id 详情跳转区域 id
     */
    @NotNull(message = "销售区域id不能为空")
    private Integer salesAreaId;

    /**
     * 时间类型, day/month
     */
    @Pattern(regexp = "day|month", message = "timeType只能是day或month")
    private String timeType;
}
