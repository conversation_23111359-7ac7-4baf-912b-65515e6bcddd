package net.summerfarm.crm.model.query.salesperformance;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.crm.enums.PbScoreMerchantTypeEnum;
import net.summerfarm.crm.enums.SortDirectionEnum;
import net.xianmu.common.input.BasePageInput;
import javax.validation.constraints.Pattern;

/**
 * PB标品得分查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PbPerformanceQuery extends BasePageInput {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 客户名称（用于搜索）
     */
    private String mname;

    /**
     * 客户类型: 平台客户（单店），大客户
     */
    private PbScoreMerchantTypeEnum merchantType;

    /**
     * 排序字段: 履约GMV
     * 履约GMV - fulfillmentGmv
     */
    @Pattern(regexp = "^(fulfillmentGmv)$",
            message = "排序字段必须是 fulfillmentGmv ")
    private String sortField;

    /**
     * 排序方向: 升序, 降序
     */
    private SortDirectionEnum sortDirection;

}
