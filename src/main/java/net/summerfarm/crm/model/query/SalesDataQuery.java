package net.summerfarm.crm.model.query;

import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;
import net.xianmu.common.input.BasePageInput;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/16 15:46
 */
@Data
public class SalesDataQuery extends BasePageInput {
    /**
     * 区域
     */
    private List<Integer> areaNo;
    /**
     * 销售团队类型:1:平台销售，2:大客户销售
     */
    private Integer type;
    /**
     * 销售姓名
     */
    private String bdName;
    /**
     * 查询时间 格式:yyyyMM/yyyyMMdd
     */
    private Integer queryTime;
    /**
     * 查询时间 格式:yyyy-MM
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime updateTime;
    /**
     * 大客户团队:销售ids
     */
    private List<Integer> vipTeamAdminIds;
    /**
     * 订单开始时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startTime;
    /**
     * 订单结束时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime endTime;
    /**
     * 私海0,公海1
     */
    private Integer reassign;
    /**
     * 是否是本月标记
     */
    private Boolean thisMonth;
    /**
     * 销售id
     */
    private Integer adminId;
    /**
     * 销售ids
     */
    private List<Integer> adminIds;
    /**
     * 销售orgid
     */
    private List<Integer> bdOrgId;
    /**
     * 不计算月活的大客户id：目前仅有书亦及茶百道
     */
    private List<Integer> bigMerchantAdminIds;
    /**
     * 行政城市集合
     */
    private List<String> administrativeCityList;

    /**
     * 销售区域id
     */
    private Integer salesAreaId;
    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private List<String> district;
}
