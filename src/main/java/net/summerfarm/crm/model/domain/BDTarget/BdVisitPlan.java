package net.summerfarm.crm.model.domain.BDTarget;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * crm_bd_visit_plan
 * 销售拜访计划
 * <AUTHOR>
@Data
public class BdVisitPlan implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 销售每日目标id
     */
    private Long bdDailyTargetId;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 拜访顺序
     */
    private Integer visitOrder;

    /**
     * 联系人地址id
     */
    private Long contactId;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String area;

    /**
     * poi坐标
     */
    private String poi;

    /**
     * 拜访计划状态，0：草稿，1：生效中
     */
    private Integer status;

    /**
     * 锁定状态，0：未锁定，1：已锁定
     */
    private Integer lockStatus;

    /**
     * 拜访日期
     */
    private LocalDate visitDate;

    /**
     * 拜访类型，0：线下拜访，1：线上拜访
     */
    private Integer visitType;

    /**
     * 拜访状态，0：未拜访，1：已拜访
     */
    private Integer visitStatus;

    /**
     * 拜访记录id
     */
    private Integer followUpRecordId;

    /**
     * 门店下单状态，0：拜访日未下单，1：拜访日已下单
     */
    private Integer merchantOrderStatus;

    /**
     * 门店潜力值
     */
    private BigDecimal merchantPotentialValue;

    /**
     * 逻辑删除，0-正常，1-被删除
     */
    private Integer isDeleted;

    /**
     * 添加来源，0-初始添加，1-手动添加，2-系统推荐
     */
    private Integer addSource;

    /**
     * 拜访推荐话术
     */
    private String visitRecommendScript;

    /**
     * 催单状态，0：不可二次催单，1：可二次催单
     */
    private Integer urgeOrderStatus;
}