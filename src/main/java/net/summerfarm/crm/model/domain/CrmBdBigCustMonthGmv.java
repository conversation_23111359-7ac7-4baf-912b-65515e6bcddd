package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * crm_bd_big_cust_month_gmv
 * <AUTHOR>
@Data
public class CrmBdBigCustMonthGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * bd编号
     */
    private Integer adminId;

    /**
     * bd名称
     */
    private String bdName;

    /**
     * 大客户名称
     */
    private String bigCust;

    /**
     * 总业绩
     */
    private BigDecimal merchantTotalGmv;

    /**
     * 账期总业绩
     */
    private BigDecimal creditPaidGmv;

    /**
     * 现结总业绩
     */
    private BigDecimal cashSettlementGmv;

    /**
     * 总业绩(除安佳56217)
     */
    private BigDecimal merchantTotalGmvEx;

    /**
     * 账期总业绩(除安佳56217)
     */
    private BigDecimal creditPaidGmvEx;

    /**
     * 现结总业绩(除安佳56217)
     */
    private BigDecimal cashSettlementGmvEx;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer monthTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}