package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * crm_coupon_expense_pool
 * <AUTHOR>
@Data
public class CrmCouponExpensePool implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * primary key
     */
    private Long id;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 名称
     */
    private String name;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 余额
     */
    private BigDecimal remainingAmount;
    /**
     * 自动审批状态 0关闭 1开启
     */
    private Byte autoApprove;
    /**
     * 商品申请卷目标 0 不限 1按照品类 2按照sku
     */
    private Byte targetType;
    /**
     * 状态 0正常 1失效
     */
    private Byte status;
    /**
     * 开始时间 默认2000.01.01
     */
    private LocalDateTime startDate;
    /**
     * 结束时间 默认 9999.01.01
     */
    private LocalDateTime endDate;
    /**
     * 费比限制 整数
     */
    private Integer costLimit;
    /**
     * adminId
     */
    private Long createUserId;
    /**
     * 创建人名称
     */
    private String createUserName;
}