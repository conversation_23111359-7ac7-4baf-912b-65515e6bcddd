package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 鲜果POP项目客户价值标签
 */
@Data
@TableName(value = "fruit_pop_cust_value_lable")
public class FruitPopCustValueLable {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * m_id
     */
    @TableField(value = "cust_id")
    private Long custId;

    /**
     * 客户价值标签
     */
    @TableField(value = "cust_value_lable")
    private String custValueLable;
}