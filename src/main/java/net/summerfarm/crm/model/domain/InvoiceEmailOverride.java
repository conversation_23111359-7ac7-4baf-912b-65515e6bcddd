package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * invoice_email_override
 * 发票邮箱门店覆盖表
 * <AUTHOR>
@Data
public class InvoiceEmailOverride implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 覆盖的品牌抬头配置ID(invoice_config.id)
     */
    private Long invoiceConfigId;

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 门店自定义的接收邮箱
     */
    private String email;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
