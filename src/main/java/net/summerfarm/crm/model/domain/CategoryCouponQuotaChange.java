package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;
import okhttp3.Interceptor;

import javax.persistence.Access;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/2/28 18:52
 */
@Data
@Accessors(chain = true)
public class CategoryCouponQuotaChange {
    /**
     * id
     */
    private Integer id;
    /**
     * 被分配的bd id
     */
    private Integer adminId;
    /**
     * bd name
     */
    private String adminName;
    /**
     * 变化金额
     */
    private BigDecimal quota;
    /**
     * 操作类型: 0设置;1:划分;2:发券;3:返还;4:品类拓宽;5:新客费比;6:老客费比
     */
    private Integer type;
    /**
     * 用户-券关联id
     */
    private Integer merchantCouponId;
    /**
     * 商品底价
     */
    private BigDecimal basePrice;
    /**
     * 返现比例 需要 /100
     */
    private Integer rewardRule;
    /**
     * 钉钉业务id
     */
    private Integer dingtalkBizId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人id
     */
    private Integer creator;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 额度类型: 0:品类券;1:月活券
     */
    private Integer quotaType;
    /**
     * 卡卷id
     */
    private Long poolId;
}
