package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 销售主管负责区域
 *
 * <AUTHOR>
 * @date 2023/8/22 11:54
 */
@Data
@Accessors(chain = true)
public class CrmSalesArea {
    /**
     * id
     */
    private Integer id;
    /**
     * 销售org id
     */
    private Integer bdOrgId;
    /**
     * 销售区域名称
     */
    private String salesAreaName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
