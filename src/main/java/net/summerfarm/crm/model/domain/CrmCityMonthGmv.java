package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 行政城市按月份gmv表
 * @TableName crm_city_month_gmv
 */
@Data
public class CrmCityMonthGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 行政城市
     */
    private String administrativeCity;

    /**
     * 总gmv
     */
    private BigDecimal totalGmv;

    /**
     * 拉新数
     */
    private Integer pullNewAmount;

    /**
     * 普通拉新
     */
    private Integer ordinaryPullNewAmount;


    /**
     * 核心客户数
     */
    private Integer coreMerchantAmount;

    /**
     * 月活数
     */
    private Integer monthLiveAmount;

    /**
     * 公海月活
     */
    private Integer openMerchantMonthLive;

    /**
     * 私海有效月活
     */
    private Integer privateMerchantEffectiveMonthLive;

    /**
     * 公海有效月活
     */
    private Integer openMerchantEffectiveMonthLive;

    /**
     * 私海月活
     */
    private Integer privateMerchantMonthLive;

    /**
     * 公海客户数
     */
    private Integer openMerchantAmount;

    /**
     * 私海客户数
     */
    private Integer privateMerchantAmount;

    /**
     * 城市bd总绩效
     */
    private BigDecimal performance;

    /**
     * 数据所在月份标记:yyyyMM
     */
    private Integer monthTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 全部团队(0),平台销售(1),大客户团队(2)
     */
    private Integer teamTag;

    /**
     * 倒闭客户数
     */
    private Integer operateMerchantNum;

    /**
     * 拜访数
     */
    private Integer visitNum;

    /**
     * 陪访数
     */
    private Integer escortNum;

    /**
     * 自营品牌gmv
     */
    private BigDecimal brandGmv;



    private static final long serialVersionUID = 1L;
}