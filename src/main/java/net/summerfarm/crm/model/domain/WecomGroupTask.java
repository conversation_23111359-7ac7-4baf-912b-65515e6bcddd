package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 企微群发任务
 *
 * <AUTHOR>
 * @date 2024/2/23 15:03
 */
@Data
public class WecomGroupTask implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 销售id
     */
    private Long bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 消息状态: 0-未发送 2-已发送
     */
    private Integer messageStatus;

    /**
     * 消息接受客户数
     */
    private Integer sendCount;

    private static final long serialVersionUID = 1L;
}