package net.summerfarm.crm.model.domain.BDTarget;

import lombok.Data;
import com.alibaba.fastjson.JSON;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * crm_bd_target_indicator_config
 * 销售目标指标配置
 *
 * <AUTHOR>
 */
@Data
public class BdTargetIndicatorConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 目标类型
     */
    private Integer targetType;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 业务类型配置
     */
    private String businessTypeConfig;

    /**
     * 指标类型配置
     */
    private String indicatorTypeConfig;

    /**
     * 品类名称配置
     */
    private String categoryNameConfig;

    /**
     * sku配置
     */
    private String skuConfig;

    /**
     * spu配置
     */
    private String spuConfig;

    /**
     * 单位配置
     */
    private String unitConfig;

    /**
     * 获取业务类型配置项列表
     *
     * @return
     */
    public List<BdTargetIndicatorConfigItem> getBusinessTypeConfigItems() {
        if (this.getBusinessTypeConfig() == null) {
            return null;
        }
        return JSON.parseArray(this.getBusinessTypeConfig(), BdTargetIndicatorConfigItem.class);
    }

    /**
     * 获取指标类型配置项列表
     *
     * @return
     */
    public List<BdTargetIndicatorConfigItem> getIndicatorTypeConfigItems() {
        if (this.getIndicatorTypeConfig() == null) {
            return null;
        }
        return JSON.parseArray(this.getIndicatorTypeConfig(), BdTargetIndicatorConfigItem.class);
    }

    /**
     * 获取品类名称列表
     * 
     * @return
     */
    public List<String> getCategoryNames() {
        if (this.getCategoryNameConfig() == null) {
            return null;
        }
        return JSON.parseArray(this.getCategoryNameConfig(), String.class);
    }

    /**
     * 获取sku列表
     * 
     * @return
     */
    public List<String> getSkus() {
        if (this.getSkuConfig() == null) {
            return null;
        }
        return JSON.parseArray(this.getSkuConfig(), String.class);
    }

    /**
     * 获取spu列表
     * 
     * @return
     */
    public List<String> getSpus() {
        if (this.getSpuConfig() == null) {
            return null;
        }
        return JSON.parseArray(this.getSpuConfig(), String.class);
    }

}
