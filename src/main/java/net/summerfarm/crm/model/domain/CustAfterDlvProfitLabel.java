package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 平台客户履约后利润标签表(不定期更新）
 */
@Data
public class CustAfterDlvProfitLabel {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dateTag;

    /**
     * 客户ID
     */
    private Long custId;

    /**
     * 单客户履约后利润分层标签
     */
    private String dlvProfitLabel;

    /**
     * 履约实付利润率标签：高（>=10%),低(<10%)
     */
    private String dlvProfitRateLabel;

    /**
     * 单客户生命周期
     */
    private String lifeCycle;

    /**
     * 单客户生命周期细
     */
    private String lifeCycleDetail;

    /**
     * 履约后利润分层(大类：A，B，C)
     */
    private String dlvProfitGroup;
}