package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 企微销售客户详情
 * <AUTHOR>
 * @date 2023/8/25 18:15
 */
@Data
@Accessors(chain = true)
public class WechatUserInfo {
    /**
     * primary key
     */
    private Long id;

    /**
     * 企微用户 id
     */
    private String userId;

    /**
     * 企微 unionid
     */
    private String unionid;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 状态:1:正常;2:客户删除员工;3:员工删除客户;4:互删;
     */
    private Integer status;

    /**
     * 用户渠道
     */
    private String state;

    /**
     * 客户 id
     */
    private String externalUserid;

    /**
     * 鲜沐账号 id
     */
    private Long adminId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}