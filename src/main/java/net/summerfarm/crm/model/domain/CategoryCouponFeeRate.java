package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 月活券费比表
 */
@Data
@TableName(value = "category_coupon_fee_rate")
public class CategoryCouponFeeRate {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 客户类型 0. 新客 1. 老客
     */
    @TableField(value = "merchant_type")
    private Integer merchantType;

    /**
     * 品类 1 全部,2乳制品,3非乳制品,4水果
     */
    @TableField(value = "category_type")
    private Integer categoryType;

    /**
     * 费比
     */
    @TableField(value = "fee_rate")
    private BigDecimal feeRate;

    /**
     * FK category_coupon_quota.id
     */
    @TableField(value = "category_coupon_quota_id")
    private Integer categoryCouponQuotaId;
}