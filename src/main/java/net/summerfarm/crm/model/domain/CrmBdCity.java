package net.summerfarm.crm.model.domain;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * bd负责城市
 *
 * <AUTHOR>
 * @TableName crm_bd_city
 * @date 2023/05/16
 */
@Data
public class CrmBdCity implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * bd id
     */
    private Integer bdId;

    /**
     * bd名称
     */
    private String bdName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 地区
     */
    private String area;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 更新人adminId
     */
    private Integer updater;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}