package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * crm_bd_day_gmv
 * <AUTHOR>
@Data
public class CrmBdDayGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 销售id
     */
    private Integer adminId;

    /**
     * 销售姓名
     */
    private String adminName;

    /**
     * 总gmv
     */
    private BigDecimal totalGmv;

    /**
     * 单店gmv
     */
    private BigDecimal singleGmv;

    /**
     * 大客户gmv
     */
    private BigDecimal vipGmv;

    /**
     * 鲜果gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 自营品牌gmv
     */
    private BigDecimal brandGmv;

    /**
     * 固定奖励sku的gmv
     */
    private BigDecimal rewardGmv;

    /**
     * 固定奖励sku销量
     */
    private Integer rewardAmout;

    /**
     * 核心客户数
     */
    private Integer coreMerchantAmout;

    /**
     * 月活数
     */
    private Integer monthLiveAmout;

    /**
     * 拉新数
     */
    private Integer pullNewAmout;

    /**
     * bd绩效
     */
    private BigDecimal performance;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 本月已配送订单
     */
    private Integer orderShipped;

    /**
     * 普通拉新
     */
    private Integer ordinaryPullNewAmount;

    /**
     * 注册未下单
     */
    private Integer noOrderRegister;

    private static final long serialVersionUID = 1L;
}