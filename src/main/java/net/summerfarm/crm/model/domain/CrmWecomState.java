package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @date 2024/2/28 10:20
 */
/**
    * 销售在职&企微激活状态
    */
@Data
public class CrmWecomState implements Serializable {
    /**
    * primary key
    */
    private Long id;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 销售id
    */
    private Long bdId;

    /**
    * 销售名称
    */
    private String bdName;

    /**
    * 上级id
    */
    private Long parentId;

    /**
    * 上级名称
    */
    private String parentName;

    /**
    * 在职状态:0离职；1:在职
    */
    private Integer jobState;

    /**
    * 企微状态:1=已激活，2=已禁用，4=未激活，5=退出企业
    */
    private Integer wecomState;

    private static final long serialVersionUID = 1L;
}