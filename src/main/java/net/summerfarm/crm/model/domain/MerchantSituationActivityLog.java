package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 客情券活跃期
 */
@Data
public class MerchantSituationActivityLog {
    /**
    * primary key
    */
    private Long id;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 客情申请ID - FK merchant_situation.id
    */
    private Long merchantSituationId;

    /**
    * spu id
    */
    private Long pdId;

    /**
    * 活跃期长度
    */
    private Integer activeLength;

    /**
    * 活跃期结束日期
    */
    private LocalDate activeEndDate;
}