package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 风控相似店铺
 *
 * <AUTHOR>
 * @date 2023/11/17 16:58
 */
@Data
public class RiskSimilarMerchant {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 门店 id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 门店联系人
     */
    private String phone;

    /**
     * 区域编号
     */
    private Integer areaNo;

    /**
     * 运营区域
     */
    private String areaName;

    /**
     * 销售 id
     */
    private Integer bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 门头照
     */
    private String doorPic;

    /**
     * 门店类型
     */
    private String size;

    /**
     * 相似店铺 ID
     */
    private Integer riskMerchantId;

    /**
     * 命中条件
     */
    private String triggerCondition;

}