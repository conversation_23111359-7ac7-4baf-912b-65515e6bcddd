package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * crm_follow
 * <AUTHOR>
@Data
public class CrmFollow implements Serializable {
    /**
     * id 自增
     */
    private Long id;

    /**
     * clue 线索
     */
    private String type;

    /**
     * 主题id
     */
    private Long subjectId;

    /**
     * bd id
     */
    private Integer bdId;

    /**
     * 跟进时间
     */
    private LocalDateTime followTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 跟进目的
     */
    private String followGoal;

    /**
     * 客户反馈
     */
    private String customerFeedback;

    /**
     * 下次跟进
     */
    private String nextFollow;

    /**
     * 图片
     */
    private String images;

    /**
     * 跟进方式
     */
    private String followModel;

    private static final long serialVersionUID = 1L;
}