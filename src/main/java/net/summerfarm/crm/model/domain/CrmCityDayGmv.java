package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 行政城市本月gmv表
 * @TableName crm_city_day_gmv
 */
@Data
public class CrmCityDayGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 行政城市
     */
    private String administrativeCity;

    /**
     * 总gmv
     */
    private BigDecimal totalGmv;

    /**
     * 拉新数
     */
    private Integer pullNewAmount;

    /**
     * 核心客户数
     */
    private Integer coreMerchantAmount;

    /**
     * 月活数
     */
    private Integer monthLiveAmount;

    /**
     * 公海月活
     */
    private Integer openMerchantMonthLive;

    /**
     * 私海月活
     */
    private Integer privateMerchantMonthLive;

    /**
     * 公海客户数
     */
    private Integer openMerchantAmount;

    /**
     * 私海客户数
     */
    private Integer privateMerchantAmount;

    /**
     * 城市bd总绩效
     */
    private BigDecimal performance;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * 全部团队(0),平台销售(1),大客户团队(2)
     */
    private Integer teamTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 倒闭客户数
     */
    private Integer operateMerchantNum;

    /**
     * 拜访数
     */
    private Integer visitNum;

    /**
     * 陪访数
     */
    private Integer escortNum;

    /**
     * 自营品牌GMV
     */
    private BigDecimal brandGmv;

    /**
     * 普通拉新
     */
    private Integer ordinaryPullNewAmount;

    /**
     * 私海有效月活
     */
    private Integer privateMerchantEffectiveMonthLive;

    /**
     * 公海有效月活
     */
    private Integer openMerchantEffectiveMonthLive;

    /**
     * 品牌商家数量
     */
    private Integer brandMerchantCount;

    /**
     * 水果gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 鲜果商户数量
     */
    private Integer fruitMerchantCount;

    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 乳制品商户数量
     */
    private Integer dairyMerchantCount;

    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 非乳制品商户数量
     */
    private Integer nonDairyMerchantCount;

    private static final long serialVersionUID = 1L;
}