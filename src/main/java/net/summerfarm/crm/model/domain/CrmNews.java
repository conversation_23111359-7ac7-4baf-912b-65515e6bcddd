package net.summerfarm.crm.model.domain;

import lombok.Data;
import net.summerfarm.crm.enums.CrmNewsStatusEnum;
import net.summerfarm.crm.enums.CrmNewsTypeEnum;
import net.summerfarm.crm.enums.CrmNewsWarningTypeEnum;

import java.time.LocalDateTime;

@Data
public class CrmNews {

    /**
     * id
     */
    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 类型：1：省心送,2:补货通知
     * @see CrmNewsTypeEnum
     */
    private Integer type;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 状态：0:未读 1：已读
     * @see CrmNewsStatusEnum
     */
    private Integer status;

    /**
     * 警示类型
     * @see CrmNewsWarningTypeEnum
     */
    private Integer warningType;

    /**
     * adminId
     */
    private Integer adminId;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     *更新人
     */
    private Integer updater;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
