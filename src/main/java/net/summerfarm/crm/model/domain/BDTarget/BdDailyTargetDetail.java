package net.summerfarm.crm.model.domain.BDTarget;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * crm_bd_daily_target_detail
 * 销售每日目标明细
 * <AUTHOR>
@Data
public class BdDailyTargetDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 销售每日目标id
     */
    private Long bdDailyTargetId;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 优先级, 1,2,3,4,5
     */
    private Integer priority;

    /**
     * 目标类型，1：拉新客户数，2：月活客户数，3：高价值客户数，4：平台总目标，5：品类目标，6：sku目标，7：spu目标
     */
    private Integer targetType;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 目标日期
     */
    private LocalDate targetDate;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 指标类型，1：GMV，2：客户数，3：件数
     */
    private Integer indicatorType;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * SKU编码
     */
    private String sku;

    /**
     * SPU编码
     */
    private String spu;

    /**
     * 指标期望值
     */
    private BigDecimal indicatorExpectedValue;

    /**
     * 指标状态，0：未完成，1：已完成
     */
    private Integer indicatorStatus;

    /**
     * 指标当前值
     */
    private BigDecimal indicatorCurrentValue;

    /**
     * 逻辑删除，0：正常，1：被删除
     */
    private Integer isDeleted;
}