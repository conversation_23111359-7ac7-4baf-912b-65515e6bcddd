package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 品类券返还额度
 *
 * <AUTHOR>
 * @Date 2023/3/1 18:15
 */
@Data
public class CategoryCouponQuotaReward {
    /**
     * id
     */
    private Long id;
    /**
     * m1 id
     */
    private Integer adminId;

    /**
     * m1 名称
     */
    private String adminName;
    /**
     * 奖励金额
     */
    private BigDecimal amount;
    /**
     * 申请人id
     */
    private Long bdId;
    /**
     * 申请人名称
     */
    private String bdName;
    /**
     * 数据更新标记
     */
    private Integer dayTag;
    /**
     * 创建时间
     */
    private Date createTime;
}
