package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Data
public class BdExt implements Serializable {
    /**
     * id
     */
    private Integer id;
    /**
     * adminId
     */
    private Integer adminId;
    /**
     * adminName
     */
    private String adminName;
    /**
     * areaNo
     */
    private Integer areaNo;
    /**
     * areaName
     */
    private String areaName;
    /**
     * 私海数
     */
    private Integer privateNum;

    /**
     * 状态
     */
    private Integer status;

    /**
    * 客情额度上限
    */
    private BigDecimal privateQuota;

    /**
     * 每日计划数
     */
    private Integer visitTarget;
}

