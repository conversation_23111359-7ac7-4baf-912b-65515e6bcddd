package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * crm_manage_bd
 * <AUTHOR>
@Data
public class CrmManageBd implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 区域负责人
     */
    private Integer parentAdminId;

    /**
     * 城市负责人
     */
    private Integer manageAdminId;

    /**
     * 总负责人id
     */
    private Integer departmentAdminId;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 修改人
     */
    private Integer updater;

    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    private LocalDateTime createTime;

    /**
     * 所属行政城市
     */
    private List<String> administrativeCitys;

    private static final long serialVersionUID = 1L;
}