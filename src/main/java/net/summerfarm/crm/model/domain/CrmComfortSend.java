package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * crm_comfort_send
 *
 * <AUTHOR>
@Data
public class CrmComfortSend implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单生成时间
     */
    private Date orderTime;

    /**
     * 商品名称
     */
    private String pdName;

    /**
     * 商品个数
     */
    private Integer pdAmount;

    /**
     * 商品规格
     */
    private String pdWeight;

    /**
     * 实付总额
     */
    private BigDecimal payAmount;

    /**
     * 商户所在运营区域
     */
    private Integer areaNo;

    /**
     * 归属bdid,公海为0
     */
    private Integer bdId;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}