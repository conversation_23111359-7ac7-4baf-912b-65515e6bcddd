package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * data_synchronization_information
 * <AUTHOR>
@Data
public class DataSynchronizationInformation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 同步时间标记(yyyyMMdd/yyyyMM)
     */
    private Integer dateFlag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}