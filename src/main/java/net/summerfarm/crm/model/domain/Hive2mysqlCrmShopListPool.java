package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * hive2mysql_crm_shop_list_pool
 * <AUTHOR>
@Data
public class Hive2mysqlCrmShopListPool implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 经营类型
     */
    private String cuisineType;

    /**
     * 客单价
     */
    private BigDecimal commonPrice;

    /**
     * 网评热度
     */
    private BigDecimal popularityIndex;

    /**
     * 口味指数
     */
    private BigDecimal tasteIndex;

    /**
     * 服务指数
     */
    private BigDecimal serviceIndex;

    /**
     * 环境指数
     */
    private BigDecimal environmentIndex;

    /**
     * 店铺年纪
     */
    private String shopAge;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 商圈
     */
    private String shopRegion;

    /**
     * 商场
     */
    private String shoppingMall;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 联系电话
     */
    private String phoneNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 店铺编号
     */
    private String shopid;

    /**
     * 当季度新增的评论数
     */
    private String commentsCountInc;

    /**
     * 店铺名称
     */
    private String isChain;

    private static final long serialVersionUID = 1L;

    private String esId;

    private String manage;

    private String storeId;

}
