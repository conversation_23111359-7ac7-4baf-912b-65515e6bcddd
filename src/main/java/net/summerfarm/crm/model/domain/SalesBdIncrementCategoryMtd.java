package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售人员按客户增量类型和品类维度的MTD绩效表现
 */
@Data
@TableName(value = "sales_bd_increment_category_mtd")
public class SalesBdIncrementCategoryMtd {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 是否测试BD
     */
    @TableField(value = "is_test_bd")
    private String isTestBd;

    /**
     * 履约归属BD (销售人员姓名)
     */
    @TableField(value = "bd_name")
    private String bdName;

    /**
     * BD_ID (销售人员ID)
     */
    @TableField(value = "bd_id")
    private Long bdId;

    /**
     * 大区 (销售人员所属大区)
     */
    @TableField(value = "bd_region")
    private String bdRegion;

    /**
     * 区域 (销售人员所属区域)
     */
    @TableField(value = "bd_work_zone")
    private String bdWorkZone;

    /**
     * 客户增量类型 (新增、存量)
     */
    @TableField(value = "cust_increment_type")
    private String custIncrementType;

    /**
     * 推广品类
     */
    @TableField(value = "spu_group")
    private String spuGroup;

    /**
     * 一级类目
     */
    @TableField(value = "category1")
    private String category1;

    /**
     * 履约(交易)应付GMV
     */
    @TableField(value = "dlv_ori_amt")
    private BigDecimal dlvOriAmt;

    /**
     * 履约(交易)实付GMV
     */
    @TableField(value = "dlv_real_amt")
    private BigDecimal dlvRealAmt;

    /**
     * 自营商品毛利润
     */
    @TableField(value = "item_profit_amt")
    private BigDecimal itemProfitAmt;

    /**
     * 履约(交易)件数
     */
    @TableField(value = "dlv_sku_cnt")
    private Long dlvSkuCnt;

    /**
     * 小规格履约(交易)件数
     */
    @TableField(value = "small_sku_cnt")
    private Long smallSkuCnt;

    /**
     * 大规格履约(交易)件数
     */
    @TableField(value = "big_sku_cnt")
    private Object bigSkuCnt;

    /**
     * 品类推广佣金 (根据客户增量类型区分汇总)
     */
    @TableField(value = "category_comm_amt")
    private BigDecimal categoryCommAmt;

    /**
     * 贡献客户数 (为此维度组合贡献业绩的去重客户数量)
     */
    @TableField(value = "contributing_cust_count")
    private Long contributingCustCount;

    /**
     * 是否履约结算:1 履约，0 交易
     */
    @TableField(value = "is_dlv_payment")
    private Long isDlvPayment;

    /**
     * 数据日期
     */
    @TableField(value = "ds")
    private String ds;
}