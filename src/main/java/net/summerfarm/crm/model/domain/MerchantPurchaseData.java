package net.summerfarm.crm.model.domain;


import lombok.Data;


/**
 * <AUTHOR>
 * @date 2024-08-29 15:46:59
 * @version 1.0
 *
 */
@Data
public class MerchantPurchaseData {
	/**
	 * 自增主键
	 */
	private Long id;

	/**
	 * 日期标签，格式为YYYYMMDD
	 */
	private String dayTag;

	/**
	 * 商家ID
	 */
	private Long mId;

	/**
	 * 商家名称
	 */
	private String mname;

	/**
	 * 运营区域名称
	 */
	private String areaName;

	/**
	 * 大区名称
	 */
	private String largeAreaName;

	/**
	 * merchant区域编号
	 */
	private Integer areaNo;

	/**
	 * merchant类型，取自merchant.type，目前只有“面包蛋糕”
	 */
	private String type;

	/**
	 * 这段时间内购买的搅打型稀奶油商品总数量
	 */
	private Integer totalPurchasedQuantity;

	/**
	 * 购买的SKU列表
	 */
	private String purchasedSkuList;

	/**
	 * 购买的产品名称列表
	 */
	private String purchasedPdNameList;

	/**
	 * 订单的时间范围
	 */
	private String orderTimeRange;



}