package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.crm.common.util.DateUtils;

import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2019/7/23  11:10 AM
 */

@Data
@NoArgsConstructor
public class FollowWhiteList {

    /**
    * id
    */
    private Integer id;

    /**
     * 用户ID
     */
    private Long mId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 状态 0 不可用 1 可用
     */
    private Integer status;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public FollowWhiteList(Long mId, Integer status) {
        this.mId = mId;
        this.status = status;
        this.gmtCreate = new Date();
        this.gmtModified = new Date();

    }
}
