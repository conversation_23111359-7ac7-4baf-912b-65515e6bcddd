package net.summerfarm.crm.model.domain;


import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class Merchant implements Serializable {
    /**
     *  商户id
     */
    private Long mId;
    /**
     * 商户名称
     */
    @NotNull(message = "merchant.name.null", groups = {Add.class,Update.class})
    private String mname;
    /**
     * 主联系人
     */
    @NotNull(message = "linkman.null", groups = {Add.class,Update.class})
    private String mcontact;
    /**
     * openid
     */
    private String openid;
    /**
     * 手机号
     */
    @NotNull(message = "phone.null", groups = {Add.class,Update.class})
    private String phone;
    /**
     * 审核状态:0审核通过、1待审核、2审核未通过、3拉黑
     */
    private Integer islock;
    /**
     * 等级
     */
    private Byte rankId;
    /**
     * 注册时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date registerTime;
    /**
     * 登录时间
     */
    private Date loginTime;
    /**
     * 6位邀请码
     */
    private String invitecode;
    /**
     * 用户分享码
     */
    private String inviterChannelCode;
    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 审核人
     */
    private Integer auditUser;
    /**
     * 营业执照路径
     */
    private String businessLicense;
    /**
     * 店铺招牌
     */
    private String shopSign;
    /**
     * 其他证明照片
     */
    private String otherProof;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区域
     */
    private String area;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;
    /**
     * 备注
     */
    private String remark;
    /**
     * 上次下单时间
     */
    private Date lastOrderTime;
    /**
     * 所属二级城市No
     */
    private Integer areaNo;
    /**
     * 大客户、大连锁、小连锁、单店
     */
    private String size;
    /**
     * 客户类型
     */
    @NotNull(message = "type is null", groups = {Add.class,Update.class})
    private String type;
    /**
     * 商圈
     */
    private String tradeArea;
    /**
     * 商圈组
     */
    private String tradeGroup;
    /**
     * unionid
     */
    private String unionid;
    /**
     * 小程序openid
     */
    private String mpOpenid;
    /**
     * 1是直营 2是加盟, 1账期 2现结,
     */
    private Integer direct;
    /**
     * 1定量展示 2全量展示
     */
    private Integer skuShow;
    /**
     * 所属于大客户的id
     */
    private Integer adminId;
    /**
     * 1服务区内 2服务区外
     */
    private Integer server;
    /**
     * 会员当月积分
     */
    private BigDecimal memberIntegral;
    /**
     * 会员等级
     */
    private Integer grade;
    /**
     * 余额
     */
    private BigDecimal rechargeAmount;

    private BigDecimal cashAmount;

    private Date cashUpdateTime;

    private LocalDateTime mergeTime;

    private String mergeAdmin;

    private String channelCode;
    /**
     * 配送单是否展示价格信息
     */
    private Boolean showPrice;

    private Integer changePop;
    /**
     * 拉黑备注
     */
    private String pullBlackRemark;
    /**
     * 拉黑操作人
     */
    private String pullBlackOperator;

    /**
    * 门牌号
    */
    private String houseNumber;

    /**
    * 企业规模
    */
    private String enterpriseScale;

    /**
    * 公司品牌
    */
    private String companyBrand;

    /** 外部编码 */
    private String outerNo;

    /**
    * 是否选择了线索池 0 不是 1 是
    */
    private Integer cluePool;

    /**
    * 大客户类型 ka， 批发大客户
    */
    private String merchantType;

    /**
    * 审核类型
    */
    private Integer examineType;

    private Integer displayButton;
    /**
     * 免邮日
     */
    private String freeDay;
    /**
     * 0,正常经营 1,倒闭
     */
    private Integer operateStatus;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 门头照片
     */
    private String doorPic;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname;
    }

    public String getMcontact() {
        return mcontact;
    }

    public void setMcontact(String mcontact) {
        this.mcontact = mcontact;
    }
}
