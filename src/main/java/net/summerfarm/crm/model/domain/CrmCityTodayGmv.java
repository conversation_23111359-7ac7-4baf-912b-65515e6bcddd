package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 城市当日 gmv
 *
 * <AUTHOR>
 * @date 2023/10/31 16:44
 */
@Data
public class CrmCityTodayGmv {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 下单 gmv
     */
    private BigDecimal orderGmv;

    /**
     * 配送 gmv
     */
    private BigDecimal deliveryGmv;

    /**
     * 下单客户数
     */
    private Integer orderMerchant;

    /**
     * 鲜果 mgv
     */
    private BigDecimal fruitGmv;

    /**
     * 乳制品 gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 非乳gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 自营品 gmv
     */
    private BigDecimal brandGmv;

    /**
     * 代仓 gmv
     */
    private BigDecimal agentGmv;

    /**
     * 奖励 gmv
     */
    private BigDecimal rewardGmv;

    /**
     * 拉新数
     */
    private Integer pullNewAmount;

    /**
     * 拜访数
     */
    private Integer visitNum;

    /**
     * 日期标记
     */
    private Integer dayTag;
}