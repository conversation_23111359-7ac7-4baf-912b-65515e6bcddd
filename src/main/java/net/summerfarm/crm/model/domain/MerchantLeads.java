package net.summerfarm.crm.model.domain;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MerchantLeads {
    private Long id;

    @NotNull(message = "name.null")
    private String mname;

    private String phone;
    @NotNull
    private String province;
    @NotNull
    private String city;
    @NotNull
    private String area;

    @NotNull(message = "address.null")
    private String address;

    @NotNull(message = "poi.null")
    private String poiNote;

    @NotNull
    private Integer areaNo;

    private String areaName;

    private String size;

    private String author;

    private Integer adminId;

    private String adminName;

    private String source;

    private String mcontact;

    private Integer status;

    private String remark;

    /**
    * 门牌号
    */
    private String houseNumber;

    /**
     * 企业规模
     */
    private String enterpriseScale;

    /**
     * 公司品牌
     */
    private String companyBrand;

    /**
     * 经营类型
     */
    private String type;

    /**
     * 门头图片
     */
    @NotBlank(message = "门头照不能为空")
    private String doorPic;

    /**
     * 门头照 ocr 结果
     */
    private String doorPicOcr;

    private Integer mId;

    /**
     * 0-鲜沐客户，1-pop客户
     */
    private Integer merchantType;

    public String getMname() {
        return mname;
    }

    public void setMname(String mname) {
        this.mname = mname.trim();
    }
}