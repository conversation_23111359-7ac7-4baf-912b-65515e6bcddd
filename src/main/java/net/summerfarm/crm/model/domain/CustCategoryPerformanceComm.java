package net.summerfarm.crm.model.domain;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户维度品类推广绩效表现
 * 
 * <AUTHOR>
 */
@Data
public class CustCategoryPerformanceComm {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * BD_ID
     */
    private Long bdId;

    /**
     * 履约归属BD
     */
    private String bdName;

    /**
     * 客户ID
     */
    private Long custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 客户类型：存量、增量
     */
    private String custType;

    /**
     * 客户类型细分
     */
    private String custTypeDetail;

    /**
     * 推广品类
     */
    private String spuGroup;

    /**
     * 本月截止昨晚履约(交易)件数
     */
    private Double bigSkuCnt;

    /**
     * 今日待履约件数
     */
    private Double dlvRealCntToday;

    /**
     * 今日交易本月待履约件数(今日交易件数)
     */
    private Double dlvOrderCntToday;

    /**
     * 其余待履约件数
     */
    private Double dlvOtherCntToday;

    /**
     * 本月预计总履约件数
     */
    private Double monthDlvRealCntToday;

    /**
     * 本月截止昨晚品类推广佣金
     */
    private BigDecimal categoryCommAmt;

    /**
     * 本月截止昨晚存量推广佣金
     */
    private BigDecimal oldCustComm;

    /**
     * 本月截止昨晚新客推广佣金
     */
    private BigDecimal newCustComm;

    /**
     * 本月预计品类推广总佣金
     */
    private BigDecimal monthCategoryCustComm;

    /**
     * 是否履约结算:1 履约，0 交易
     */
    private Integer isDlvPayment;

    /**
     * 是否完成：0 否，1 是
     */
    private Integer isComplete;

    /**
     * 客户数量
     */
    private Integer custCnt;

    /**
     * 本月截止昨晚交易件数
     */
    private Double mtdTxnSkuCnt;

    /**
     * 今日交易件数
     */
    private Double todayTxnSkuCnt;

    /**
     * 单件奖励
     */
    private BigDecimal rewardPerItem;

    /**
     * 数据日期
     */
    private String ds;
}
