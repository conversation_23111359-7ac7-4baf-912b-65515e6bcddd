package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * big_customer_properties_ext
 * <AUTHOR>
@Data
@NoArgsConstructor
public class BigCustomerPropertiesExt implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 大客户配置key
     */
    private String propKey;

    /**
     * 大客户配置value
     */
    private String propValue;

    /**
     * adminId
     */
    private Long bigCustomerId;

    private static final long serialVersionUID = 1L;


    public BigCustomerPropertiesExt(String propKey, String propValue, Long adminId) {
        this.propKey = propKey;
        this.propValue = propValue;
        this.bigCustomerId = adminId;
        this.createTime= new Date();
        this.updateTime= new Date();

    }
}