package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * crm客户释放公海规则和倒计时
 */
@Data
@TableName(value = "follow_up_relation_release_detail")
public class FollowUpRelationReleaseDetail {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 客户m_id
     */
    @TableField(value = "m_id")
    private Long mId;

    /**
     * 释放规则 30天未下单且15天未拜访, 60天未下单, 首单客户, 公海转私海
     */
    @TableField(value = "release_rule")
    private String releaseRule;

    /**
     * 释放倒计时
     */
    @TableField(value = "danger_day")
    private Integer dangerDay;

    /**
     * 是否有未履约订单且处于释放保护期
     */
    @TableField(value = "has_unfinished_delivery")
    private Boolean hasUnfinishedDelivery;

    /**
     * 释放时间
     */
    @TableField(value = "release_date")
    private LocalDateTime releaseDate;

    /**
     * 数据所属日期:格式yyyyMMdd
     */
    @TableField(value = "date_flag")
    private String dateFlag;
}