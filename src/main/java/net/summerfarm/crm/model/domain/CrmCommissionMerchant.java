package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * crm_commission_merchant
 * <AUTHOR>
@Data
public class CrmCommissionMerchant implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 新销售提成
     */
    private BigDecimal newBdReward;

    /**
     * 其他销售提成
     */
    private BigDecimal normalBdReward;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}