package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * crm企微用户统计
 *
 * <AUTHOR>
 * @date 2024/2/23 15:14
 */
@Data
public class CrmWecomUserSummary implements Serializable {
    /**
    * primary key
    */
    private Long id;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * 销售id
    */
    private Long bdId;

    /**
    * 销售名称
    */
    private String bdName;

    /**
    * 私海客户数
    */
    private Integer privateSeaCount;

    /**
    * 有效企微客户数
    */
    private Integer effectiveWecomUserCount;

    /**
    * 企微客户数
    */
    private Integer wecomUserCount;

    /**
    * 销售删除客户数
    */
    private Integer bdDeleteWecomCount;

    /**
    * 用户删除客户数
    */
    private Integer userDeleteWecomCount;

    /**
    * 互删客户数
    */
    private Integer deleteWecomCount;

    private static final long serialVersionUID = 1L;
}