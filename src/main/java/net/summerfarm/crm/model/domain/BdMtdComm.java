package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * MTD单销售绩效汇总表
 */
@Data
@TableName(value = "bd_mtd_comm")
public class BdMtdComm {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 最新归属BD
     */
    @TableField(value = "last_bd_name")
    private String lastBdName;

    /**
     * BD_ID
     */
    @TableField(value = "last_bd_id")
    private Long lastBdId;

    /**
     * 大区
     */
    @TableField(value = "dep_level3")
    private String depLevel3;

    /**
     * 区域
     */
    @TableField(value = "dep_name")
    private String depName;

    /**
     * 利润积分
     */
    @TableField(value = "total_score_num")
    private Double totalScoreNum;

    /**
     * 利润积分系数
     */
    @TableField(value = "bd_performance_rate")
    private Double bdPerformanceRate;

    /**
     * 佣金总额
     */
    @TableField(value = "total_comm_amt")
    private BigDecimal totalCommAmt;

    /**
     * 高价值客户总佣金
     */
    @TableField(value = "a_commisstion_amt")
    private BigDecimal aCommisstionAmt;

    /**
     * 高价值客户数
     */
    @TableField(value = "a_cust_cnt")
    private Integer aCustCnt;

    /**
     * 高价值客户数佣金
     */
    @TableField(value = "a_cust_comm_amt")
    private BigDecimal aCustCommAmt;

    /**
     * 高价值客户超额SPU数
     */
    @TableField(value = "more_than_spu_cnt")
    private Long moreThanSpuCnt;

    /**
     * 高价值超额spu佣金
     */
    @TableField(value = "a_spu_comm_amt")
    private BigDecimal aSpuCommAmt;

    /**
     * 品类推广总佣金
     */
    @TableField(value = "category_comm_amt")
    private BigDecimal categoryCommAmt;

    /**
     * 存量客户品类佣金
     */
    @TableField(value = "old_cust_comm")
    private BigDecimal oldCustComm;

    /**
     * 新增客户品类佣金
     */
    @TableField(value = "new_cust_comm")
    private BigDecimal newCustComm;

    /**
     * 品类推广件数_大规格
     */
    @TableField(value = "big_sku_cnt")
    private Object bigSkuCnt;

    /**
     * 存量客户推广件数_大规格
     */
    @TableField(value = "old_big_sku_cnt")
    private Object oldBigSkuCnt;

    /**
     * 新增客户推广件数_大规格
     */
    @TableField(value = "new_big_sku_cnt")
    private Object newBigSkuCnt;

    /**
     * MTD履约实付GMV
     */
    @TableField(value = "dlv_real_amt")
    private BigDecimal dlvRealAmt;

    /**
     * MTD履约商品毛利润
     */
    @TableField(value = "item_profit_amt")
    private BigDecimal itemProfitAmt;

    /**
     * 数据日期
     */
    @TableField(value = "ds")
    private String ds;

    /**
     * MTD履约SPU数
     */
    @TableField(value = "dlv_spu_cnt")
    private Long dlvSpuCnt;

    /**
     * 高价值超额spu客户数
     */
    @TableField(value = "more_than_spu_cust_cnt")
    private Long moreThanSpuCustCnt;
}