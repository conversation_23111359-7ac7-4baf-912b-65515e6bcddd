package net.summerfarm.crm.model.domain.BDTarget;

import java.util.List;

/**
 * BdVisitPlan查询对象
 */
public class BdVisitPlanQuery extends BdVisitPlan {

    /**
     * 门店ID列表（用于IN查询）
     */
    private List<Long> mIdList;

    /**
     * 无参构造方法
     */
    public BdVisitPlanQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdVisitPlanQuery(Long id) {
        super();
        this.setId(id);
    }

    /**
     * 获取门店ID列表
     * @return 门店ID列表
     */
    public List<Long> getMIdList() {
        return mIdList;
    }

    /**
     * 设置门店ID列表
     * @param mIdList 门店ID列表
     */
    public void setMIdList(List<Long> mIdList) {
        this.mIdList = mIdList;
    }
}