package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售主管负责城市
 *
 * <AUTHOR>
 * @date 2023/8/22 11:51
 */
@Data
public class CrmSalesCity {
    /**
     * id
     */
    private Integer id;

    /**
     * 销售id
     */
    private Integer salesAreaId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
