package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * orders
 * <AUTHOR>
@Data
public class Orders implements Serializable {
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商户编号
     */
    private Long mId;

    /**
     * 订单生成时间
     */
    private LocalDateTime orderTime;

    /**
     * 订单类型：0普通，1省心送，2运费,3代下单,10虚拟商品（黄金卡、充值...）,11直发采购
     */
    private Integer type;

    /**
     * 订单状态
     */
    private Short status;

    /**
     * 配送费用
     */
    private BigDecimal deliveryFee;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 确认收货时间
     */
    private LocalDateTime confirmTime;

    private String areaName;

    private Integer outTimes;

    /**
     * 0没用1优惠券2满减3满返
     */
    private Integer discountType;

    /**
     * 超时加单费用
     */
    private BigDecimal outTimesFee;

    private Integer areaNo;

    private String mSize;

    private Integer direct;

    private Integer skuShow;

    /**
     * 红包金额
     */
    private BigDecimal redPackAmount;

    /**
     * 待发放优惠卡id
     */
    private Integer cardRuleId;

    /**
     * 子账号id
     */
    private Long accountId;

    /**
     * 应付价格
     */
    private BigDecimal originPrice;

    private Integer outStock;

    /**
     * 虚拟商品id：奶油卡id、充值送券id
     */
    private Integer discountCardId;

    /**
     * 订单扩展类型
普通订单：0、普通 1、预售 	虚拟商品（type=10）：0、黄金卡 1、充值
     */
    private Integer orderSaleType;

    /**
     * 0（默认）：该订单下无应收款，1：该订单下有应收款且未付，2：该订单下有应收款且部分付款，3：该订单下有应收且已付清
     */
    private Short receivableStatus;

    /**
     * 大客户的门店下单时，该门店下单时所属大客户
     */
    private Integer adminId;

    /**
     * 0:未开票,1:部分开票,2:已开票
     */
    private Byte invoiceStatus;

    /**
     * 财务发票表id(202209弃用)
     */
    private Long financialInvoiceId;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 代下单操作人
     */
    private Integer operateId;

    private static final long serialVersionUID = 1L;
}