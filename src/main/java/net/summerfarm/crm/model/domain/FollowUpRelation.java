package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import net.summerfarm.crm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("follow_up_relation")
public class FollowUpRelation implements Serializable {
    private Integer id;

    private Long mId;

    private Integer adminId;

    private String adminName;

    private LocalDateTime addTime;

    private Boolean reassign;

    private LocalDateTime lastFollowUpTime;

    private LocalDateTime reassignTime;

    private String reason;

    /**
    * 新购买标签 0 无 1是新购买 2 由定时任务处理取消新购买标签,无
    */
    private Integer followType;

    private Integer dangerDay;

    /**
     * 释放时间
     */
    private LocalDateTime releaseTime;

    /**
     * 释放保护原因
     */
    private String protectReason;

    /**
    * 省心送标签 0 无 1 是省心送
    */
    private Integer timingFollowType;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    @TableField(exist = false)
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    @TableField(exist = false)
    private LocalDateTime endTime;

    @TableField(exist = false)
    private String size;

    private Integer careBdId;

    private String province;

    private String city;

    private String area;

    /**
     * 上次负责销售id. 0代表上次在公海
     */
    private Integer source;

    public FollowUpRelation() {
    }

    public FollowUpRelation(Long mId, Integer adminId, String adminName) {
        this.mId = mId;
        this.adminId = adminId;
        this.adminName = adminName;
        this.addTime = LocalDateTime.now();
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }


}