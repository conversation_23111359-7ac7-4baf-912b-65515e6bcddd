package net.summerfarm.crm.model.domain;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2019/8/1  2:21 PM
 *
 * 拜访计划实体
 */
@Data
public class VisitPlan {

    private Long id;

    /**
     * 期望拜访时间
     */
    @JSONField(format = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime expectedTime;

    /**
     * 状态 0 待拜访 1已拜访,2取消,4未完成
     */
    private Integer status;

    /**
     * 客户id
     */
    private Long mId;

    /**
     * 拜访销售ID
     */
    private Integer adminId;

    /**
     * 期望内容
     */
    private String expectedContent;

    /**
     * 联系人地址id
     */
    private Integer contactId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 拜访类型,0拜访 1拉新 2陪访
     */
    @NotNull(groups = {Update.class,Add.class},message = "前端为啥不传拜访类型参数?")
    private Integer type;

    /**
     * 取消原因
     */
    @NotBlank(groups = Update.class,message = "取消原因不可为空")
    private String cancelContent;

    private Integer source;
    /**
     * 拉新所在区域
     */
    private Integer areaNo;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}
