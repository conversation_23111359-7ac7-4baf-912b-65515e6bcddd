package net.summerfarm.crm.model.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 核心商品底价
 *
 * <AUTHOR>
 * @Date 2023/2/28 17:36
 */
@Data
@EqualsAndHashCode
public class CoreProductBasePrice {
    @NotNull(groups = {Update.class}, message = "id不能为空")
    private Integer id;

    /**
     * 运营大区名称
     */
    @NotBlank(groups = {Add.class}, message = "运营大区不能为空")
    @ExcelProperty("运营大区")
    private String largeAreaName;

    /**
     * 运营大区编号
     */
    @NotNull(groups = {Add.class}, message = "运营大区编号不能为空")
    @ExcelProperty("运营大区编号")
    private Integer largeAreaNo;

    /**
     * sku
     */
    @NotBlank(groups = {Add.class, Update.class}, message = "sku不能为空")
    @ExcelProperty("sku")
    private String sku;


    /**
     * 产品id
     */
    @NotNull(groups = {Add.class,Update.class}, message = "产品id不能为空")
    private Long pdId;

    /**
     * 产品名称
     */
    @NotBlank(groups = {Add.class,Update.class}, message = "产品名称不能为空")
    private String pdName;

    /**
     * 重量
     */
    @NotBlank(groups = {Add.class,Update.class}, message = "规格不能为空")
    private String weight;

    /**
     * 底价
     */
    @NotNull(groups = {Add.class, Update.class}, message = "非拓品底价")
    @DecimalMin(groups = {Add.class, Update.class}, value = "0", message = "非拓品底价金额不正确")
    @ExcelProperty("非拓品底价")
    private BigDecimal basePrice;

    /**
     * 品类拓宽底价
     */
    @DecimalMin(groups = {Add.class, Update.class}, value = "0", message = "拓品保底价金额不正确")
    @ExcelProperty("拓品保底价")
    private BigDecimal merchantSituationCategoryBasePrice;

    /**
     * 品类拓宽红线底价
     */
    @NotNull(groups = {Add.class, Update.class}, message = "拓品红线底价不能为空")
    @DecimalMin(groups = {Add.class, Update.class}, value = "0", message = "拓品红线底价金额不正确")
    @ExcelProperty("拓品红线底价")
    private BigDecimal merchantSituationCategoryRedLinePrice;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}
