package net.summerfarm.crm.model.domain;

import lombok.Data;
import net.summerfarm.common.util.validation.annotation.InRange;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2019/7/31  7:13 PM
 *
 * 客情申请表
 */
@Data
public class MerchantSituation {

    /**
    * 申请ID 自增
    */
    private Integer id;

    /**
    * 创建时间
    */
    private Date gmtCreate;

    /**
    * 修改时间
    */
    private Date gmtModified;
    /**
    * 创建位置 0 后台 1 CRM
    */
    private Integer createLocation;

    /**
    * 状态  0 待审核 1已审核(待审批) 2 审批通过 3 申请未通过(关闭)
    */
    @InRange(rangeNums = {0,1,2,3,4},message = "无效的审核状态参数")
    private Integer status;

    /**
    * 用户id
    */
    @NotNull
    private Long merchantId;

    /**
     * 商户优惠券id
     */
    private Integer merchantCouponId;

    /**
    * 创建人id
    */
    private Integer creatorId;

    /**
    * 审核人ID
    */
    private Integer examineId;

    /**
    * 审核时间
    */
    private LocalDateTime examineTime;

    /**
    *审批人ID
    */
    private Integer approvalId;

    /**
    * 审批时间
    */
    private LocalDateTime approvalTime;

    /**
    * 审批备注
    */
    private String approvalRemark;

    /**
    * 审核备注
    */
    private String examineRemark;

    /**
    * 审核人名称
    */
    private  String  examineName;

    /**
    * 审批人名称
    */
    private String approvalName;

    /**
     * 券id
     */
    private Integer couponId;

    /**
     * bd名称
     */
    private String creatorName;

    /**
    * 所属bd
    */
    private Integer adminId;

    /**
     * 所属bd名称
     */
    private String adminName;

    /**
     * 申请单备注
     */
    private String situationRemake;

    /**
     * 下单数量
     */
    private Integer orderQuantity;

    /**
     * 申请类型:0客情;1月活;2品类券-价格补贴;3品类券-品类拓宽
     */
    private Integer situationType;

    /**
     * 审批图片
     */
    private String attachedImage;

    /**
     * poolId
     */
    private Long poolId;

    private BigDecimal basePrice;

    private BigDecimal amount;

}
