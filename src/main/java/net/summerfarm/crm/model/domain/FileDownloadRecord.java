package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:06
 */
@Data
public class FileDownloadRecord {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 0 未上传 1 已上传 2 上传数据失败
     */
    private Integer status;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 导出人id
     */
    private Integer adminId;

    /**
     * 查询参数
     */
    private String params;

    /**
     * 模块类型 1 大客户对账单 2 账单
     */
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 标识
     */
    private String uId;

    public String getuId() {
        return uId;
    }

    public void setuId(String uId) {
        this.uId = uId;
    }
}
