package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户MTD维度绩效表现
 */
@Data
@TableName(value = "cust_mtd_category_comm")
public class CustMtdCategoryComm {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 是否测试BD
     */
    @TableField(value = "is_test_bd")
    private String isTestBd;

    /**
     * MTD是否拜访
     */
    @TableField(value = "is_visit")
    private String isVisit;

    /**
     * 履约归属BD
     */
    @TableField(value = "bd_name")
    private String bdName;

    /**
     * BD_ID
     */
    @TableField(value = "bd_id")
    private Long bdId;

    /**
     * 大区
     */
    @TableField(value = "bd_region")
    private String bdRegion;

    /**
     * 区域
     */
    @TableField(value = "bd_work_zone")
    private String bdWorkZone;

    /**
     * 客户ID
     */
    @TableField(value = "cust_id")
    private Long custId;

    /**
     * 客户名称
     */
    @TableField(value = "cust_name")
    private String custName;

    /**
     * 客户类型
     */
    @TableField(value = "cust_type")
    private String custType;

    /**
     * 客户类型细分
     */
    @TableField(value = "cust_type_detail")
    private String custTypeDetail;

    /**
     * 推广品类
     */
    @TableField(value = "spu_group")
    private String spuGroup;

    /**
     * 履约(交易)应付GMV
     */
    @TableField(value = "dlv_ori_amt")
    private BigDecimal dlvOriAmt;

    /**
     * 履约(交易)实付GMV
     */
    @TableField(value = "dlv_real_amt")
    private BigDecimal dlvRealAmt;

    /**
     * 自营商品毛利润
     */
    @TableField(value = "item_profit_amt")
    private BigDecimal itemProfitAmt;

    /**
     * 履约(交易)件数
     */
    @TableField(value = "dlv_sku_cnt")
    private Long dlvSkuCnt;

    /**
     * 小规格履约(交易)件数
     */
    @TableField(value = "small_sku_cnt")
    private Long smallSkuCnt;

    /**
     * 大规格履约(交易)件数
     */
    @TableField(value = "big_sku_cnt")
    private Double bigSkuCnt;

    /**
     * 品类推广佣金
     */
    @TableField(value = "category_comm_amt")
    private BigDecimal categoryCommAmt;

    /**
     * 存量推广佣金
     */
    @TableField(value = "old_cust_comm")
    private BigDecimal oldCustComm;

    /**
     * 新客推广佣金
     */
    @TableField(value = "new_cust_comm")
    private BigDecimal newCustComm;

    /**
     * 一级类目
     */
    @TableField(value = "category1")
    private String category1;

    /**
     * 是否履约结算:1 履约，0 交易
     */
    @TableField(value = "is_dlv_payment")
    private Long isDlvPayment;

    /**
     * 数据日期
     */
    @TableField(value = "ds")
    private String ds;
}