package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.crm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 陪访计划表
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CrmEscortVisitPlan implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 陪访人id
     */
    private Long adminId;

    /**
     * 拜访计划id
     */
    private Long visitPlanId;

    /**
     * 期望拜访时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime expectedTime;

    /**
     * 状态:0待拜访,1已拜访,2取消,4未完成
     */
    private Integer status;

    /**
     * 陪访原因
     */
    private String expectedContent;

    /**
     * 取消原因
     */
    private String cancelContent;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public CrmEscortVisitPlan(Long adminId, Long visitPlanId, String creator) {
        this.adminId = adminId;
        this.visitPlanId = visitPlanId;
        this.creator = creator;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", adminId=").append(adminId);
        sb.append(", visitPlanId=").append(visitPlanId);
        sb.append(", expectedTime=").append(expectedTime);
        sb.append(", status=").append(status);
        sb.append(", expectedContent=").append(expectedContent);
        sb.append(", cancelContent=").append(cancelContent);
        sb.append(", creator=").append(creator);
        sb.append(", updater=").append(updater);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}