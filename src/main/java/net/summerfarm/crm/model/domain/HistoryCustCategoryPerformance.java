package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 近6个月推广品类历史履约客户本月表现
 */
@Data
@TableName(value = "history_cust_category_performance")
public class HistoryCustCategoryPerformance {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 是否测试BD
     */
    @TableField(value = "is_test_bd")
    private String isTestBd;

    /**
     * 履约归属BD
     */
    @TableField(value = "bd_name")
    private String bdName;

    /**
     * BD_ID
     */
    @TableField(value = "bd_id")
    private Long bdId;

    /**
     * 大区
     */
    @TableField(value = "bd_region")
    private String bdRegion;

    /**
     * 区域
     */
    @TableField(value = "bd_work_zone")
    private String bdWorkZone;

    /**
     * 客户ID
     */
    @TableField(value = "cust_id")
    private Long custId;

    /**
     * 客户名称
     */
    @TableField(value = "cust_name")
    private String custName;

    /**
     * 来源：鲜沐,SAAS
     */
    @TableField(value = "order_source")
    private String orderSource;

    /**
     * 一级类目
     */
    @TableField(value = "category1")
    private String category1;

    /**
     * 推广品类
     */
    @TableField(value = "spu_group")
    private String spuGroup;

    /**
     * 是否履约结算:1 履约，0 交易
     */
    @TableField(value = "is_dlv_payment")
    private Long isDlvPayment;

    /**
     * 最近履约(交易)月份
     */
    @TableField(value = "max_months")
    private String maxMonths;

    /**
     * 非本月的最近履约(交易)月份
     */
    @TableField(value = "last_months")
    private String lastMonths;

    /**
     * 本月履约(交易)应付GMV
     */
    @TableField(value = "mtd_dlv_ori_amt")
    private BigDecimal mtdDlvOriAmt;

    /**
     * 本月履约(交易)实付GMV
     */
    @TableField(value = "mtd_dlv_real_amt")
    private BigDecimal mtdDlvRealAmt;

    /**
     * 本月履约(交易)件数
     */
    @TableField(value = "mtd_dlv_sku_cnt")
    private Long mtdDlvSkuCnt;

    /**
     * 本月大规格履约(交易)件数
     */
    @TableField(value = "mtd_big_sku_cnt")
    private Double mtdBigSkuCnt;

    /**
     * 是否完成该品类任务：1 是，0 否
     */
    @TableField(value = "is_completed")
    private Long isCompleted;

    /**
     * 客户类型:存量，新增
     */
    @TableField(value = "cust_type")
    private String custType;

    /**
     * 客户类型详情:新客，老客
     */
    @TableField(value = "cust_type_detail")
    private String custTypeDetail;

    /**
     * 数据日期
     */
    @TableField(value = "ds")
    private String ds;
}