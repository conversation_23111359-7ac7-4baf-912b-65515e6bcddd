package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 企微鲜沐用户关系
 *
 * <AUTHOR>
 * @date 2024/2/23 15:03
 */

@Data
public class WecomUserInfo implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 企微用户id
     */
    private String userId;

    /**
     * 部门
     */
    private String department;

    /**
     * 鲜沐账号id
     */
    private Long adminId;

    /**
     * 账号名称
     */
    private String adminName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 激活状态:1=已激活，2=已禁用，4=未激活，5=退出企业
     */
    private Integer status;

    /**
     * 邮箱
     */
    private String email;

    private static final long serialVersionUID = 1L;
}