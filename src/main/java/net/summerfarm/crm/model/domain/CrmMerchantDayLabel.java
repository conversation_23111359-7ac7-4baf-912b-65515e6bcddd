package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * crm_merchant_day_label
 * <AUTHOR>
@Data
public class CrmMerchantDayLabel implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 标签枚举
     */
    private String merchantLabel;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}