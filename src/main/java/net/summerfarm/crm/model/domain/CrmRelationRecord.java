package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * crm_relation_record
 * <AUTHOR>
@Data
public class CrmRelationRecord implements Serializable {
    private Integer id;

    /**
     * 商户ID
     */
    private Long mId;

    /**
     * 跟进人
     */
    private Integer adminId;

    /**
     * 管理员名称
     */
    private String adminName;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 重新指派标志，0没有，1已重新指派跟进人
     */
    private Boolean reassign;

    /**
     * 最近跟进时间
     */
    private LocalDateTime lastFollowUpTime;

    /**
     * 重新指派时间
     */
    private LocalDateTime reassignTime;

    /**
     * 释放或跟进原因
     */
    private String reason;

    /**
     * 新购买标签,无(0)新购买(1)由定时任务处理取消新购买标签(2)
     */
    private Integer followType;

    /**
     * 自动释放倒计时
     */
    private Integer dangerDay;

    /**
     * 省心送标签:无(0)省心送(1)
     */
    private Integer timingFollowType;

    private static final long serialVersionUID = 1L;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}