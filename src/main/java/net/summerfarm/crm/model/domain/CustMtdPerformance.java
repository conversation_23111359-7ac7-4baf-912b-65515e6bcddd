package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户MTD维度绩效表现
 */
@Data
@TableName(value = "cust_mtd_performance")
public class CustMtdPerformance {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 最新归属BD
     */
    @TableField(value = "last_bd_name")
    private String lastBdName;

    /**
     * BD_ID
     */
    @TableField(value = "last_bd_id")
    private Long lastBdId;

    /**
     * 大区
     */
    @TableField(value = "bd_region")
    private String bdRegion;

    /**
     * 区域
     */
    @TableField(value = "bd_work_zone")
    private String bdWorkZone;

    /**
     * 客户ID
     */
    @TableField(value = "cust_id")
    private Long custId;

    /**
     * 客户名称
     */
    @TableField(value = "last_cust_name")
    private String lastCustName;

    /**
     * 客户类型
     */
    @TableField(value = "cust_dlv_type")
    private String custDlvType;

    /**
     * BD利润积分累计
     */
    @TableField(value = "total_score_num")
    private Object totalScoreNum;

    /**
     * 利润积分系数
     */
    @TableField(value = "bd_performance_rate")
    private Object bdPerformanceRate;

    /**
     * 履约客户数
     */
    @TableField(value = "dlv_cust_cnt")
    private Long dlvCustCnt;

    /**
     * 客户数佣金
     */
    @TableField(value = "cust_comm_amt")
    private BigDecimal custCommAmt;

    /**
     * 履约应付GMV
     */
    @TableField(value = "dlv_ori_amt")
    private BigDecimal dlvOriAmt;

    /**
     * 履约实付GMV
     */
    @TableField(value = "dlv_real_amt")
    private BigDecimal dlvRealAmt;

    /**
     * 自营商品毛利润
     */
    @TableField(value = "item_profit_amt")
    private BigDecimal itemProfitAmt;

    /**
     * AT_履约实付金额
     */
    @TableField(value = "dlv_real_amt_at")
    private BigDecimal dlvRealAmtAt;

    /**
     * 流量品_履约实付金额
     */
    @TableField(value = "dlv_real_amt_expo")
    private BigDecimal dlvRealAmtExpo;

    /**
     * 利润品_履约实付金额
     */
    @TableField(value = "dlv_real_amt_profit")
    private BigDecimal dlvRealAmtProfit;

    /**
     * 常规品_履约实付金额
     */
    @TableField(value = "dlv_real_amt_normal")
    private BigDecimal dlvRealAmtNormal;

    /**
     * 鲜果_履约实付金额
     */
    @TableField(value = "dlv_real_amt_fruit")
    private BigDecimal dlvRealAmtFruit;

    /**
     * 利润得分
     */
    @TableField(value = "cate_group_score_num")
    private Object cateGroupScoreNum;

    /**
     * 履约SPU数
     */
    @TableField(value = "dlv_spu_cnt")
    private Long dlvSpuCnt;

    /**
     * 超额SPU数
     */
    @TableField(value = "more_than_spu_cnt")
    private Long moreThanSpuCnt;

    /**
     * 超额SPU数佣金
     */
    @TableField(value = "more_than_spu_comm")
    private BigDecimal moreThanSpuComm;

    /**
     * 超额SPU客户数
     */
    @TableField(value = "more_than_spu_cust")
    private Long moreThanSpuCust;

    /**
     * 高价值客户佣金汇总
     */
    @TableField(value = "total_comm_amt")
    private BigDecimal totalCommAmt;

    /**
     * 是否测试BD
     */
    @TableField(value = "is_test_bd")
    private String isTestBd;

    /**
     * 数据日期
     */
    @TableField(value = "ds")
    private String ds;

    /**
     * 高价值客户标签 枚举:高价值，准高，潜高，普通
     */
    @TableField(value = "cust_value_lable")
    private String custValueLable;
}