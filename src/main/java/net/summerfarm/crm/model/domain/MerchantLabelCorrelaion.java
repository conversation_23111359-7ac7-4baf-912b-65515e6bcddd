package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class MerchantLabelCorrelaion implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户标签ID
     */
    private Long labelId;

    /**
     * 商户ID
     */
    private Long mId;

    /**
     * 标签类型 MerchantLabelTypeEnum
     */
    private Integer type;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}