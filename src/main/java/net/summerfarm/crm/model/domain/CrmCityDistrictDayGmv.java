package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 市区本月gmv表
 *
 * <AUTHOR>
 * @TableName crm_city_district_day_gmv
 * @date 2023/05/16
 */
@Data
public class CrmCityDistrictDayGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 行政城市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 总gmv
     */
    private BigDecimal totalGmv;

    /**
     * 拉新数
     */
    private Object pullNewAmount;

    /**
     * 月活数
     */
    private Object monthLiveAmount;

    /**
     * 公海月活
     */
    private Object openMerchantMonthLive;

    /**
     * 私海月活
     */
    private Object privateMerchantMonthLive;

    /**
     * 公海客户数
     */
    private Object openMerchantAmount;

    /**
     * 私海客户数
     */
    private Object privateMerchantAmount;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Object dayTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 倒闭客户数
     */
    private Object operateMerchantNum;

    /**
     * 拜访数
     */
    private Object visitNum;

    /**
     * 陪访数
     */
    private Object escortNum;

    /**
     * 自营品牌GMV
     */
    private BigDecimal brandGmv;

    /**
     * spu均值
     */
    private BigDecimal spuAverage;

    /**
     * 普通拉新数
     */
    private Object ordinaryPullNewAmount;

    /**
     * 私海有效月活
     */
    private Object privateMerchantEffectiveMonthLive;

    /**
     * 公海有效月活
     */
    private Object openMerchantEffectiveMonthLive;

    /**
     * 自有品牌客户数
     */
    private Object brandMerchantCount;

    /**
     * 鲜果gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 鲜果商户数量
     */
    private Object fruitMerchantCount;

    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 乳制品商户数量
     */
    private Object dairyMerchantCount;

    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 非乳制品商户数量
     */
    private Object nonDairyMerchantCount;

    /**
     * 代售品gmv
     */
    private BigDecimal agentGoodsGmv;

    private static final long serialVersionUID = 1L;
}