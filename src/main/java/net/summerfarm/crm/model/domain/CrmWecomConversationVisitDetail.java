package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 企微会话消息
 */
@Data
public class CrmWecomConversationVisitDetail {
    /**
     * primary key
     */
    private Long id;

    /**
     * 时间标签
     */
    private Integer dateTag;

    /**
     * 销售id
     */
    private Long bdId;

    /**
     * 销售企微user_id
     */
    private String bdUserId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 门店企微external_user_id
     */
    private String custExternalUserId;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String custName;

    /**
     * 会话列表
     */
    private String conversationList;

    /**
     * 会话里的图片列表
     */
    private String imageList;

    /**
     * 会话日期
     */
    private LocalDate date;

    /**
     * 会话开始时间
     */
    private LocalDateTime conversationStartTime;

    /**
     * 会话结束时间
     */
    private LocalDateTime conversationEndTime;

    /**
     * 是否有效拜访
     */
    private Boolean isValidVisit;

    /**
     * 数据生成时间
     */
    private LocalDateTime createTime;
}