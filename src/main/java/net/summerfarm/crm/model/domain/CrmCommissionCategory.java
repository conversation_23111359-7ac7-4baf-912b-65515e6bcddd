package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * crm_commission_category
 * <AUTHOR>
@Data
public class CrmCommissionCategory implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 提点比例
     */
    private BigDecimal proportion;

    /**
     * 品类:0自有品牌,1全部,2乳制品,3非乳制品,4水果
     */
    private Integer categoryType;

    /**
     * 删除标识:0否1是
     */
    private Byte deleteFlag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 更新人
     */
    private String updateName;

    private static final long serialVersionUID = 1L;
}