package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商户属性表
 * <AUTHOR>
 * @TableName crm_merchant_day_attribute
 */
@Data
public class CrmMerchantDayAttribute implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 未拜访天数
     */
    private Integer notVisited;

    /**
     * 累计未下单数目
     */
    private Integer daysWithoutOrder;

    /**
     * 未下单天数（和跟进相关）
     */
    private Integer  daysWithoutOrderFollow;

    /**
     * 生命周期:0新注册,1首单,2非稳,3稳定
     */
    private Integer merchantLifecycle;

    /**
     * 下单频率
     */
    private Integer orderFrequency;

    /**
     * 未完结的省心送标识:无(0)省心送(1)
     */
    private Integer timingFollowType;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * 总gmv
     */
    private BigDecimal totalGmv;

    /**
     * 核心客户标识:否(0),是(1)
     */
    private Integer coreMerchantTag;

    /**
     * 近三个月平均下单周期
     */
    private Integer orderCycle;

    /**
     * 新生命周期定义
     */
    private String lifecycle;

    /**
     * r值
     */
    private String rValue;

    /**
     * f值
     */
    private String fValue;

    /**
     * m值
     */
    private String mValue;

    /**
     * 未登录天数
     */
    private Integer daysNotLoggedIn;

    /**
     * 当月被拜访次数
     */
    private Integer visitCount;




    private static final long serialVersionUID = 1L;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}