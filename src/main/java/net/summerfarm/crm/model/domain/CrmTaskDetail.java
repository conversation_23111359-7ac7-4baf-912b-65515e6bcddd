package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/28 14:27
 */
@Data
public class CrmTaskDetail implements Serializable {
    /**
     * id
     */
    private Integer id;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 门店 id
     */
    private Integer mId;
    /**
     * 销售id
     */
    private Integer bdId;
    /**
     * 销售名字
     */
    private String bdName;
    /**
     * 卡券 id
     */
    private String sourceId;
    /**
     * 任务id
     */
    private Integer taskId;
    /**
     * 状态 0:未完成;1:已完成
     */
    private Integer status;
}
