package net.summerfarm.crm.model.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "账号信息")
@Data
public class MerchantSubAccount {
    @ApiModelProperty(name = "账号id")
    private Long accountId;

    @ApiModelProperty(name = "店铺id")
    @NotNull(message = "子账号店铺id")
    private Long mId;

    @ApiModelProperty(name = "联系人")
    private String contact;

    @ApiModelProperty(name = "手机号")
    private String phone;

    @ApiModelProperty(name = "unionId")
    private String unionid;

    @ApiModelProperty(name = "公众号openid")
    private String openid;

    @ApiModelProperty(name = "小程序openid")
    private String mpOpenid;

    private Integer popView;

    private Integer firstPopView;

    private BigDecimal cashAmount;

    private Date cashUpdateTime;

    private Date loginTime;

    private Date lastOrderTime;

    @ApiModelProperty(name = "账号状态：0、待审核 1、审核通过")
    private Integer status;

    private Integer deleteFlag;

    private String mInfo;

    @ApiModelProperty(name = "注册时间")
    private Date registerTime;

    @ApiModelProperty(name = "审核时间")
    private Date auditTime;

    private Integer auditUser;

    @ApiModelProperty(name = "账号类型", value = "0、母账号 1、子账号")
    private Integer type;
}