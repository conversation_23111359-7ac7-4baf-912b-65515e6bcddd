package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * crm_clue_delete
 * <AUTHOR>
@Data
public class CrmClueDelete implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 店铺id
     */
    private String shopId;

    private static final long serialVersionUID = 1L;
}