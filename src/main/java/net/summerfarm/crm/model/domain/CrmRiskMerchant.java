package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 风控门店
 *
 * <AUTHOR>
 * @date 2023/11/23 10:24
 */
@Data
public class CrmRiskMerchant implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 风控门店 id
     */
    private Long mId;

    /**
     * 触发场景
     */
    private Integer triggerOccasions;

    /**
     * 命中条件，多个条件用;分割
     */
    private String triggerCondition;

    /**
     * 命中分类:0:疑似重复;1:疑似虚假;2:疑似换壳;
     */
    private Integer triggerClassification;

    /**
     * 相似店铺id
     */
    private Long similarMId;

    /**
     * 日期标志
     */
    private Integer dayTag;

    /**
     * 风控门店类型 -大客户名称
     */
    private String merchantSize;

    /**
     * 相似门店类型
     */
    private String similarSize;

    /**
     * 相似门店名称/相似员工名称
     */
    private String similarName;

    /**
     * 相似门店/员工/紧急联系人电话
     */
    private String similarPhone;

    /**
     * 0:xm;1:saas
     */
    private Integer sourceType;
}