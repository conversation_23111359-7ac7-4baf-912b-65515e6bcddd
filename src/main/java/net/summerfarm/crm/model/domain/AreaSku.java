package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AreaSku {
    private Integer id;

    private String sku;

    /**
    * 城市编号
    */
    private Integer areaNo;

    /**
    * 可售库存
    */
    private Integer quantity;

    /**
    * 1使用虚拟库存0不
    */
    private Object share;

    /**
    * 原价
    */
    private BigDecimal originalPrice;

    /**
    * 销售价
    */
    private BigDecimal price;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 0下架 1上架
    */
    private Object onSale;

    /**
    * 添加时间
    */
    private LocalDateTime addTime;

    /**
    * 越小排序越靠前
    */
    private Integer priority;

    private Integer pdPriority;

    private String ladderPrice;

    private Integer limitedQuantity;

    private Integer salesMode;

    private Integer show;

    private String info;

    /**
    * 是否是大客户0 不是 1是
    */
    private Integer mType;

    /**
    * 是否展示预告:0否 1是
    */
    private Object showAdvance;

    /**
    * 预告信息
    */
    private String advance;

    /**
    * 角标状态 0、关闭 1、开启
    */
    private Integer cornerStatus;

    private LocalDateTime cornerOpenTime;

    /**
    * 上架操作: 0上架、1有库存时上架、2定时上架 3有库存时上架(永久生效)
    */
    private Integer openSale;

    /**
    * 定时上架时间
    */
    private LocalDateTime openSaleTime;

    /**
    * 下架操作: 0下架、1售罄下架、2定时下架
    */
    private Integer closeSale;

    /**
    * 定时下架时间
    */
    private LocalDateTime closeSaleTime;

    /**
    * 固定排序标识 0、关闭 1、开启
    */
    private Integer fixFlag;

    /**
    * 固定排序值
    */
    private Integer fixNum;

    /**
    * 上下架更新人
    */
    private String updater;
}