package net.summerfarm.crm.model.domain;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * merchant_ext
 * <AUTHOR>
@Data
public class MerchantExt implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 客户id
     */
    private Long mId;

    /**
     * 免邮日
     */
    private String freeDay;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否点击
     */
    private Integer clickFlag;

    private static final long serialVersionUID = 1L;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }
}