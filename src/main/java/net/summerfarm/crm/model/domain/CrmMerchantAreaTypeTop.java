package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商户同区域同行业品类,spu,top10
 * @TableName crm_merchant_area_type_top
 */
@Data
public class CrmMerchantAreaTypeTop implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 区域no
     */
    private Integer areaNo;

    /**
     * 商户经营类型
     */
    private String type;

    /**
     * 品类TOP10,以,分割
     */
    private String categoryIdList;

    /**
     * 商品TOP10,以,分割
     */
    private String pdIdList;

    /**
     * 所选时间类型:  0:本月,1:近3月,2:近半年
     */
    private Integer monthType;

    /**
     * 信号表标记:yyyyMM
     */
    private Integer monthTag;

    private static final long serialVersionUID = 1L;
}