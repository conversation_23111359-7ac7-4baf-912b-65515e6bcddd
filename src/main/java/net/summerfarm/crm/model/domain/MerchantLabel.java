package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MerchantLabel implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 售后状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 标签类型  MerchantLabelTypeEnum
     */
    private Integer type;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Integer auditor;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}