package net.summerfarm.crm.model.domain;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * M1绩效
 * 
 * <AUTHOR>
 */
@Data
public class M1PerformanceComm {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * M1的id
     */
    private Long m1Id;

    /**
     * M1名称
     */
    private String m1Name;

    /**
     * PB标品得分
     */
    private Double pbCommRate;

    /**
     * PB标品增长率
     */
    private Double pbIncreaseRate;

    /**
     * PBGMV基数
     */
    private BigDecimal pbGmvBase;

    /**
     * PB月累计履约客户数
     */
    private Integer pbTotalDlvCustCnt;

    /**
     * 上月PB履约GMV
     */
    private BigDecimal pbLastMDlvGmv;

    /**
     * PB本月截止昨晚履约GMV
     */
    private BigDecimal pbMtdDlvGmv;

    /**
     * PB今日待履约GMV
     */
    private BigDecimal pbTodayDlvGmv;

    /**
     * PB今日交易本月待履约GMV
     */
    private BigDecimal pbTodayTrdGmv;

    /**
     * PB其余待履约GMV
     */
    private BigDecimal pbOtherDlvGmv;

    /**
     * PB本月预计总履约GMV
     */
    private BigDecimal pbTotalDlvGmv;

    /**
     * 利润积分得分
     */
    private Double scoreCommRate;

    /**
     * 利润积分增长率
     */
    private Double scoreIncreaseRate;

    /**
     * 利润积分基数
     */
    private Long scoreBase;

    /**
     * 利润积分月累计履约客户数
     */
    private Integer scoreTotalDlvCustCnt;

    /**
     * 上月利润积分
     */
    private Double lastMScores;

    /**
     * 本月截止昨晚履约利润积分
     */
    private Double mtdScores;

    /**
     * 今日待履约利润积分
     */
    private Double todayDlvScores;

    /**
     * 今日交易本月待履约利润积分
     */
    private Double todayTrdScores;

    /**
     * 其余待履约利润积分
     */
    private Double otherDlvScores;

    /**
     * 本月预计总履约利润积分
     */
    private Double totalScores;

    /**
     * 数据日期
     */
    private String ds;

    /**
     * 客户类型：大客户，平台客户，全部
     */
    private String custType;

    /**
     * 销售区域
     */
    private String workZone;

}
