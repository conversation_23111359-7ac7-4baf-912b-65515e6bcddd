package net.summerfarm.crm.model.domain;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 企微客户沟通统计
 *
 * <AUTHOR>
 * @date 2024/2/23 15:03
 */
@Data
public class WecomCommunicationSummary implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 销售id
     */
    private Long bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 聊天消息数
     */
    private Integer conversationCount;

    /**
     * 消息总数
     */
    private Integer messageCount;

    /**
     * 回复消息占比
     */
    private BigDecimal answerProportion;

    /**
     * 回复时长，单位分钟
     */
    private Integer answerDuration;

    /**
     * 统计日期
     */
    private LocalDate date;

    private static final long serialVersionUID = 1L;
}