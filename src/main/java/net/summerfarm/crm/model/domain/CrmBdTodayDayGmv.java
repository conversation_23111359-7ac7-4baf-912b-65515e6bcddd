package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * bd本日gmv表
 * @TableName crm_bd_today_gmv
 */
@Data
public class CrmBdTodayDayGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 销售id
     */
    private Integer adminId;

    /**
     * 销售姓名
     */
    private String adminName;

    /**
     * 下单gmv
     */
    private BigDecimal totalGmv;

    /**
     * 配送gmv
     */
    private BigDecimal deliveryGmv;

    /**
     * 配送spu均值
     */
    private BigDecimal deliverySpuAvg;

    /**
     * 单店gmv
     */
    private BigDecimal singleGmv;

    /**
     * 大客户gmv
     */
    private BigDecimal vipGmv;

    /**
     * 鲜果gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 自营品牌gmv
     */
    private BigDecimal brandGmv;

    /**
     * 固定奖励sku的gmv
     */
    private BigDecimal rewardGmv;

    /**
     * 固定奖励sku销量
     */
    private Integer rewardAmout;

    /**
     * 自营品牌下单客户数
     */
    private Integer brandOrderMerchant;

    /**
     * 拉新数
     */
    private Integer pullNewAmount;

    /**
     * 拜访数
     */
    private Integer visitNum;

    /**
     * 数据更新标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 预估总收益
     */
    private BigDecimal estimatedIncome;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 代售gmv
     */
    private BigDecimal agentGmv;

    /**
     * 下单客户数
     */
    private Integer orderMerchant;

    private static final long serialVersionUID = 1L;
}