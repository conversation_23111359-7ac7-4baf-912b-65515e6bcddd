package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 企微标签
 * @TableName wechat_tag
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WechatTag implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 标签
     */
    private String tagName;

    /**
     * 企微tag id
     */
    private String tagId;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 标签组
     */
    private String groupId;

    /**
     * 标签名
     */
    private String groupName;

    private static final long serialVersionUID = 1L;
}