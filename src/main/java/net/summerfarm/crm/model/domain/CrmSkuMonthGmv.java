package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * sku每月下单gmv表
 * @TableName crm_sku_month_gmv
 */
@Data
public class CrmSkuMonthGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * sku
     */
    private String sku;

    /**
     * 下单gmv
     */
    private BigDecimal gmv;

    /**
     * 销量
     */
    private Integer salesVolume;

    /**
     * 下单客户数
     */
    private Integer merchantNum;

    /**
     * 自营品标记:0普通,1自营,2核心
     */
    private Integer selfSupport;

    /**
     * 运营区域
     */
    private Integer areaNo;

    /**
     * 信号表标记:yyyyMM
     */
    private Integer monthTag;

    private static final long serialVersionUID = 1L;
}