package net.summerfarm.crm.model.domain;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 城市今日数据（小时级）
 */
@Data
public class CrmCityTodayHourGmv {
    /**
     * primary key
     */
    private Long id;

    /**
     * 时间标签
     */
    private Integer dateTag;

    /**
     * 行政城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 交易应付GMV（无全品类）
     */
    private BigDecimal originTotalGmv;

    /**
     * 交易实付GMV（无全品类）
     */
    private BigDecimal realTotalGmv;

    /**
     * 交易客户数（无全品类）
     */
    private Long custCnt;

    /**
     * 全品类交易应付GMV
     */
    private BigDecimal categoriesOriginTotalGmv;

    /**
     * 全品类交易实付GMV
     */
    private BigDecimal categoriesRealTotalGmv;

    /**
     * 全品类交易客户数
     */
    private Long categoriesCustCnt;

    /**
     * 鲜果交易应付GMV（无全品类）
     */
    private BigDecimal fruitOriginTotalGmv;

    /**
     * 鲜果交易实付GMV（无全品类）
     */
    private BigDecimal fruitRealTotalGmv;

    /**
     * 鲜果交易客户数（无全品类）
     */
    private Long fruitCustCnt;

    /**
     * 安佳铁塔交易应付GMV
     */
    private BigDecimal anchorOriginTotalGmv;

    /**
     * 安佳铁塔交易实付GMV
     */
    private BigDecimal anchorRealTotalGmv;

    /**
     * 安佳铁塔交易客户数
     */
    private Long anchorCustCnt;

    /**
     * 乳制品交易应付GMV（无全品类）
     */
    private BigDecimal dairyOriginTotalGmv;

    /**
     * 乳制品交易实付GMV（无全品类）
     */
    private BigDecimal dairyRealTotalGmv;

    /**
     * 乳制品交易客户数（无全品类）
     */
    private Long dairyCustCnt;

    /**
     * 其他交易应付GMV（无全品类）
     */
    private BigDecimal otherOriginTotalGmv;

    /**
     * 其他交易实付GMV（无全品类）
     */
    private BigDecimal otherRealTotalGmv;

    /**
     * 其他交易客户数（无全品类）
     */
    private Long otherCustCnt;

    /**
     * 非AT交易应付GMV（无全品类）
     */
    private BigDecimal noAnchorOriginTotalGmv;

    /**
     * 非AT交易实付GMV（无全品类）
     */
    private BigDecimal noAnchorRealTotalGmv;

    /**
     * 非AT交易客户数（无全品类）
     */
    private Long noAnchorCustCnt;

    /**
     * 履约自营实付GMV（无全品类）
     */
    private BigDecimal dlvRealTotalGmv;

    /**
     * 履约自营客户数（无全品类）
     */
    private Long dlvCustCnt;

    /**
     * 全品类履约应付GMV
     */
    private BigDecimal dlvCategoriesOriginTotalGmv;

    /**
     * 全品类履约实付GMV
     */
    private BigDecimal dlvCategoriesRealTotalGmv;

    /**
     * 全品类履约客户数
     */
    private Long dlvCategoriesCustCnt;

    /**
     * 鲜果自营履约实付GMV（无全品类）
     */
    private BigDecimal dlvFruitRealTotalGmv;

    /**
     * 鲜果自营履约客户数（无全品类）
     */
    private Long dlvFruitCustCnt;

    /**
     * 安佳铁塔履约实付GMV
     */
    private BigDecimal dlvAnchorRealTotalGmv;

    /**
     * 安佳铁塔履约客户数
     */
    private Long dlvAnchorCustCnt;

    /**
     * 乳制品自营履约实付GMV（无全品类）
     */
    private BigDecimal dlvDairyRealTotalGmv;

    /**
     * 乳制品自营履约客户数（无全品类）
     */
    private Long dlvDairyCustCnt;

    /**
     * 其他履约实付GMV（无全品类）
     */
    private BigDecimal dlvOtherRealTotalGmv;

    /**
     * 其他履约客户数（无全品类）
     */
    private Long dlvOtherCustCnt;

    /**
     * 非AT履约实付GMV（无全品类）
     */
    private BigDecimal dlvNoAnchorRealTotalGmv;

    /**
     * 非AT履约客户数（无全品类）
     */
    private Long dlvNoAnchorCustCnt;

    /**
     * 省心送明日实付GMV
     */
    private BigDecimal dlvTimingRealTotalGmv;

    /**
     * 安佳铁塔省心送明日实付GMV
     */
    private BigDecimal dlvTimingAnchorRealTotalGmv;

    /**
     * 乳制品自营省心送明日实付GMV（无全品类+AT）
     */
    private BigDecimal dlvTimingDairyRealTotalGmv;

    /**
     * 非AT省心送明日实付GMV（无全品类）
     */
    private BigDecimal dlvTimingNoAnchorRealTotalGmv;

    /**
     * 其他自营省心送明日实付GMV
     */
    private BigDecimal dlvTimingOtherRealTotalGmv;

    /**
     * 履约明日自营品应付GMV（无全品类）
     */
    private BigDecimal dlvTomorrowOriginTotalGmv;

    /**
     * 履约明日自营品实付GMV（无全品类）
     */
    private BigDecimal dlvTomorrowRealTotalGmv;

    /**
     * 履约明日自营品客户数（无全品类）
     */
    private Long dlvTomorrowCustCnt;

    /**
     * 非AT履约实付GMV（自营品）+全品类交易实付GMV
     */
    private BigDecimal noAnchorCategoriesKpiGmv;

    /**
     * M1陪访数
     */
    private Long accompanyingVisitsM1;

    /**
     * M2陪访数
     */
    private Long accompanyingVisitsM2;
}