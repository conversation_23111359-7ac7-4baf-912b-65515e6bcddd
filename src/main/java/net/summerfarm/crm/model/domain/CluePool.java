package net.summerfarm.crm.model.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import net.summerfarm.common.util.StringUtils;
import org.elasticsearch.common.geo.GeoPoint;

/**
 * <AUTHOR> ct
 * create at:  2020/10/14  10:25
 *线索池
 */
@Data
public class CluePool {


    private String id;

    /**
    * 线索池 店铺名称
    */
    @JSONField(name = "shop_name")
    private String shopName;

    /**
    * 地址
    */
    private String address;

    /**
    * 手机号 N 否 Y 是
    */
    @JSONField(name = "phone_number")
    private String phone;

    /**
    * 是否是连锁店
    */
    @JSONField(name = "ischain")
    private String isChain;

    /**
    * 省
    */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
    * 城市编号
    */
    private Integer areaNo;
    /**
     * 商圈
     */
    @JSONField(name = "shop_region")
    private String shopRegion;
    /**
     * 商场
     */
    @JSONField(name = "shopping_mall")
    private String shoppingMall;
    /**
    * 店铺类型
    */
    @JSONField(name = "cuisine_type")
    private String type;
    /**
     * shopid也是唯一 key
     */
    @JSONField(name = "shopId")
    private String shopid;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 季度热评数
     *
     */
    @JSONField(name = "comments_count_inc")
    private String commentsCountInc;

    private Poi poi;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 精度
     */
    private String lng;
    /**
     * 店铺年龄
     */
    @JSONField(name = "shop_age")
    private String shopAge;
    /**
     * 网评热度
     */
    @JSONField(name = "popularity_index")
    private String popularityIndex;


    private String manage;

    private String cuisineType;


    private Integer distance;

  public void initPoi(){
        if (StringUtils.isNotBlank(lat) && StringUtils.isNotBlank(lng)){
            poi = new Poi(Double.parseDouble(lat),Double.parseDouble(lng));
        }
    }
}
