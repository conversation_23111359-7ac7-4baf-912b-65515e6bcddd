package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 门店poi变更记录
 *
 * <AUTHOR>
 * @Date 2023/6/27 16:17
 */
@Data
@Accessors(chain = true)
public class MerchantPoiUpdateRecord {
    /**
     * id
     */
    private Long id;
    /**
     * 拜访记录id
     */
    private Long followUpRecordId;

    /**
     * 变更前地址
     */
    private String addressBeforeChange;
    /**
     * 变化前Poi
     */
    private String poiBeforeChange;

    /**
     * 变更后地址
     */
    private String addressAfterChange;
    /**
     * 变化后Poi
     */
    private String poiAfterChange;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 联系人id
     */
    private Integer contactId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 销售id
     */
    private Integer salerId;

    /**
     * 距离
     */
    private BigDecimal distance;

    /**
     * 变更结果 0:待审批;1:审批通过;2:审批拒绝
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 飞书门头照门code
     */
    private String feishuHeaderImageCode;

    /**
     * 飞书门店铺code
     */
    private List<String> feishuMerchantImageCode;
}
