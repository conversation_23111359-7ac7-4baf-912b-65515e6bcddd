package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商户本月gmv表
 * @TableName crm_merchant_today_gmv
 */
@Data
public class CrmMerchantTodayGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 商户名
     */
    private String merchantName;

    /**
     * 商户总gmv
     */
    private BigDecimal merchantTotalGmv;

    /**
     * 配送gmv
     */
    private BigDecimal distributionGmv;

    /**
     * 鲜果gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 自营品牌gmv
     */
    private BigDecimal brandGmv;

    /**
     * 固定奖励sku的gmv
     */
    private BigDecimal rewardGmv;

    /**
     * 配送spu数量
     */
    private Integer distributionSpu;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}