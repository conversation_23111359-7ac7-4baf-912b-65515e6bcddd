package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * crm任务门店任务详情
 */
@Data
@TableName(value = "crm_job_merchant_detail")
public class CrmJobMerchantDetail {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 任务Id FK crm_job
     */
    @TableField(value = "job_id")
    private Long jobId;

    /**
     * 门店id
     */
    @TableField(value = "m_id")
    private Long mId;

    /**
     * 完成状态 0.未完成 1.已完成-待发奖，2-奖励已发放
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 门店命中品列表
     */
    @TableField(value = "merchant_product_list")
    private String merchantProductList;

    /**
     * 拜访记录id
     */
    @TableField(value = "follow_up_record_id")
    private Integer followUpRecordId;

    /**
     * 门店命中商品数
     */
    @TableField(value = "merchant_product_cnt")
    private Integer merchantProductCnt;

    /**
     * 任务领取状态：0-已领取，1-未领取
     */
    @TableField(value = "claiming_status")
    private Integer claimingStatus;

    /**
     * 任务领取时间
     */
    @TableField(value = "claiming_time")
    private LocalDateTime claimingTime;

    /**
     * 任务完成时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;

    /**
     * 完成任务的订单号列表
     */
    @TableField(value = "order_no_list")
    private String orderNoList;

    /**
     * 类目id列表（应用于下单任务）
     */
    @TableField(value = "category_id_list")
    private String categoryIdList;

    /**
     * 任务期间累计下单实付
     */
    @TableField(value = "real_total_amt")
    private BigDecimal realTotalAmt;

    /**
     * 已达成数量
     */
    @TableField(value = "fulfilled_qty")
    private Integer fulfilledQty;

    /**
     * 数量达标状态 0 未达标 1 已达标
     */
    @TableField(value = "fulfilled_qty_status")
    private Integer fulfilledQtyStatus;

    /**
     * 金额达标状态 0 未达标 1 已达标
     */
    @TableField(value = "total_amt_status")
    private Integer totalAmtStatus;

    /**
     * 高价值客户标签
     */
    @TableField(value = "high_value_customer_label")
    private String highValueCustomerLabel;

    /**
     * 任务门店标签
     */
    @TableField(value = "job_merchant_label")
    private String jobMerchantLabel;
}