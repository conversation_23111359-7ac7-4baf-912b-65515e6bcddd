package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * crm_commission_core_merchant
 * <AUTHOR>
@Data
public class CrmCommissionCoreMerchant implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 等级:0王者,1钻石,2金牌,3银牌,4铜牌
     */
    private Byte level;

    /**
     * 该等级最低值
     */
    private Integer minimum;

    /**
     * 该等级最高值
     */
    private Integer maximum;

    /**
     * 该等级奖励系数
     */
    private BigDecimal proportion;

    /**
     * 删除标识:0否1是
     */
    private Byte deleteFlag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 更新人
     */
    private String updateName;

    private static final long serialVersionUID = 1L;
}