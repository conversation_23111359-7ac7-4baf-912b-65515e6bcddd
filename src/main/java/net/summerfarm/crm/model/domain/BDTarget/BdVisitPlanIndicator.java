package net.summerfarm.crm.model.domain.BDTarget;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * crm_bd_visit_plan_indicator
 * 销售拜访计划指标
 * <AUTHOR>
@Data
public class BdVisitPlanIndicator implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 销售拜访计划id
     */
    private Long bdVisitPlanId;

    /**
     * 销售每日目标id
     */
    private Long bdDailyTargetId;

    /**
     * 销售每日目标明细id
     */
    private Long bdDailyTargetDetailId;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 门店id
     */
    private Long mId;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 指标当前值
     */
    private BigDecimal indicatorCurrentValue;

    /**
     * 指标潜力值
     */
    private BigDecimal indicatorPotentialValue;

    /**
     * 目标类型，1：拉新客户数，2：月活客户数，3：高价值客户数，4：平台总目标，5：品类目标，6：sku目标，7：spu目标
     */
    private Integer targetType;

    /**
     * 逻辑删除，0-正常，1-被删除
     */
    private Integer isDeleted;
}