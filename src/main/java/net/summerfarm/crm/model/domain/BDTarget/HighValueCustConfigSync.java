package net.summerfarm.crm.model.domain.BDTarget;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 高价值客户配置同步
 */
@Data
@TableName(value = "crm_high_value_cust_config_sync")
public class HighValueCustConfigSync {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 销售id
     */
    @TableField(value = "bd_id")
    private Long bdId;

    /**
     * 销售大区
     */
    @TableField(value = "bd_region")
    private String bdRegion;

    /**
     * 销售区域
     */
    @TableField(value = "bd_work_zone")
    private String bdWorkZone;

    /**
     * 高价值客户GMV阈值
     */
    @TableField(value = "gmv_threshold")
    private BigDecimal gmvThreshold;

    /**
     * 高价值客户SPU数阈值
     */
    @TableField(value = "spu_cnt_threshold")
    private Integer spuCntThreshold;
}