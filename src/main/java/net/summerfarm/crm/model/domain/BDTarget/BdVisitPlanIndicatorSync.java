package net.summerfarm.crm.model.domain.BDTarget;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售拜访计划指标同步表
 */
@Data
@TableName(value = "crm_bd_visit_plan_indicator_sync")
public class BdVisitPlanIndicatorSync {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 销售拜访计划id
     */
    @TableField(value = "bd_visit_plan_id")
    private Long bdVisitPlanId;

    /**
     * 销售每日目标id
     */
    @TableField(value = "bd_daily_target_id")
    private Long bdDailyTargetId;

    /**
     * 销售每日目标明细id
     */
    @TableField(value = "bd_daily_target_detail_id")
    private Long bdDailyTargetDetailId;

    /**
     * 销售id
     */
    @TableField(value = "bd_id")
    private Integer bdId;

    /**
     * 门店id
     */
    @TableField(value = "m_id")
    private Long mId;

    /**
     * 指标当前值
     */
    @TableField(value = "indicator_current_value")
    private BigDecimal indicatorCurrentValue;

    /**
     * 指标潜力值
     */
    @TableField(value = "indicator_potential_value")
    private BigDecimal indicatorPotentialValue;
}