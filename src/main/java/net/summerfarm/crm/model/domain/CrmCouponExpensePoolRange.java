package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * crm_coupon_expense_pool_range
 * <AUTHOR>
@Data
@EqualsAndHashCode
public class CrmCouponExpensePoolRange implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * primary key
     */
    private Long id;
    /**
     * create time
     */
    private Date createTime;
    /**
     * update time
     */
    private Date updateTime;
    /**
     * 池子id
     */
    private Long poolId;
    /**
     * sku/类目枚举
     */
    @ExcelProperty("sku")
    private String objKey;
}