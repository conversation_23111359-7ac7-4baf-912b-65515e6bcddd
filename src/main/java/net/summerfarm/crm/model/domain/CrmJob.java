package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * crm 任务中心新版，替代crm_task表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "crm_job")
public class CrmJob {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 任务名称
     */
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 任务描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 任务类型 0.发券 1.其它 2. 指定商品下单（拓品任务） 3. 指定品类下单 4. 普通下单（下单任务）
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 任务开始时间
     */
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 任务命中的品列表
     */
    @TableField(value = "product_list")
    private String productList;

    /**
     * 创建人admin_id
     */
    @TableField(value = "creator")
    private Long creator;

    /**
     * 修改人admin_id
     */
    @TableField(value = "updater")
    private Long updater;

    /**
     * 状态 0.未开始 1.进行中 2. 已结束 3. 已取消  0,1,2已废弃，视为同一个状态，即‘未取消’. 具体状态通过start_time和end_time实时获取。
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 关联人群方式 0. 门店列表 1. 上传门店excel 2. 上传门店商品关联excel 3-人群包
     */
    @TableField(value = "merchant_selection_type")
    private Integer merchantSelectionType;

    /**
     * 发券任务必须关联的优惠券id
     */
    @TableField(value = "coupon_id")
    private Integer couponId;

    /**
     * 任务命中的品类列表。是个二维数组
     */
    @TableField(value = "category_list")
    private String categoryList;

    /**
     * 任务领取方式：0-手动领取，1-自动领取
     */
    @TableField(value = "claiming_method")
    private Integer claimingMethod;

    /**
     * 任务备注（用于后台展示）
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 业务类型：0-销售任务，1-门店任务
     */
    @TableField(value = "business_type")
    private Integer businessType;

    /**
     * 上传的excel地址
     */
    @TableField(value = "excel_url")
    private String excelUrl;

    /**
     * 关联人群包id列表
     */
    @TableField(value = "merchant_selection_list")
    private String merchantSelectionList;

    /**
     * 奖励类型：0-红包，1-优惠券
     */
    @TableField(value = "reward_type")
    private Integer rewardType;

    /**
     * 任务具体的奖励：卡券id、积分等
     */
    @TableField(value = "reward_value")
    private String rewardValue;

    /**
     * 子任务类型
     */
    @TableField(value = "sub_type")
    private Integer subType;
}