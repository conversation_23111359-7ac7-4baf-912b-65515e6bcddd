package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * merchant_key_person
 * <AUTHOR>
@Data
public class <PERSON><PERSON><PERSON><PERSON><PERSON> implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户id
     */
    @NotNull
    private Long mId;

    /**
     * 角色:0店长,1老板,2合伙人,3采购
     */
    @NotNull
    private Integer role;

    /**
     * kp姓名
     */
    @NotNull
    private String personName;

    /**
     * 联系电话
     */
    @NotNull
    private String phone;

    /**
     * 创建人id
     */
    private Integer createId;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建人如果为空,则默认为"系统"
     * @return
     */
    public String getCreateName() {
        if(Objects.isNull(createName)){
            return "系统";
        }
        return createName;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    private static final long serialVersionUID = 1L;
}