package net.summerfarm.crm.model.domain;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户维度绩效表现
 * 
 * <AUTHOR>
 */
@Data
public class CustPerformanceComm {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * BD_ID
     */
    private Long bdId;

    /**
     * 履约当天BD
     */
    private String bdName;

    /**
     * 当前高价值客户标签的客户数
     */
    private Integer custCnt;

    /**
     * 来源：SaaS，鲜沐
     */
    private String orderSource;

    /**
     * 客户ID
     */
    private Long custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 客户佣金:高价值25，其余0
     */
    private BigDecimal custCommAmt;

    /**
     * 本月截止昨晚履约GMV
     */
    private BigDecimal dlvRealAmt;

    /**
     * 今日待履约GMV
     */
    private BigDecimal dlvRealAmtToday;

    /**
     * 今日交易本月履约GMV
     */
    private BigDecimal dlvOrderAmtToday;

    /**
     * 其余待履约GMV
     */
    private BigDecimal dlvOtherAmtToday;

    /**
     * 本月预计总履约GMV
     */
    private BigDecimal dlvMonthTotalAmt;

    /**
     * 本月截止今天累计履约GMV(离线+今日)
     */
    private BigDecimal dlvMonthTodayTotalAmt;

    /**
     * 本月截止昨晚履约SPU
     */
    private Integer dlvSpuCnt;

    /**
     * 今日待履约SPU
     */
    private Integer dlvRealSpuCntToday;

    /**
     * 今日交易本月履约SPU
     */
    private Integer dlvOrderSpuCntToday;

    /**
     * 其余待履约SPU
     */
    private Integer dlvOtherSpuCntToday;

    /**
     * 本月预计总履约SPU
     */
    private Integer dlvMonthTotalSpuCnt;

    /**
     * 本月截止今天累计履约spu数(离线+今日 去重)
     */
    private Integer dlvMonthTodayTotalSpuCnt;

    /**
     * 超额SPU数
     */
    private Integer moreThanSpuCnt;

    /**
     * 超额SPU数佣金
     */
    private BigDecimal moreThanSpuComm;

    /**
     * 高价值客户佣金汇总
     */
    private BigDecimal totalCommAmt;

    /**
     * 高价值客户标签：高价值，潜力高价值，预测高价值，准高价值
     */
    private String custValueLable;

    /**
     * GMV是否满足高价值客户门槛：是，否
     */
    private String isCompleteAmt;

    /**
     * SPU是否满足高价值客户门槛：是，否
     */
    private String isCompleteSpu;

    /**
     * 数据日期
     */
    private String ds;
}
