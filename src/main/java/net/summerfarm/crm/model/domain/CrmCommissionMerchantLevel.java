package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * crm_commission_merchant_level
 * <AUTHOR>
@Data
public class CrmCommissionMerchantLevel implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 城市等级:S.A.B.C.D
     */
    private String grade;

    /**
     * 该等级gmv最低值
     */
    private Integer gmvMinimum;

    /**
     * 该等级gmv最高值
     */
    private Integer gmvMaximum;

    /**
     * 该等级客单价最低值
     */
    private Integer priceMinimum;

    /**
     * 该等级客单价最高值
     */
    private Integer priceMaximum;

    /**
     * 该等级gmv奖励系数
     */
    private BigDecimal gmvProportion;

    /**
     * 该等级客单价奖励系数
     */
    private BigDecimal priceProportion;

    /**
     * 客户等级标识:0普通1核心
     */
    private Integer merchantLevelType;

    /**
     * 删除标识:0否1是
     */
    private Byte deleteFlag;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * create time
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createName;
    /**
     * 更新人
     */
    private String updateName;

    private static final long serialVersionUID = 1L;
}