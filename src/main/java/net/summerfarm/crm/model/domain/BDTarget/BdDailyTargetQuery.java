package net.summerfarm.crm.model.domain.BDTarget;

import lombok.Data;

/**
 * BdDailyTarget查询对象
 */
@Data
public class BdDailyTargetQuery extends BdDailyTarget {

    /**
     * 无参构造方法
     */
    public BdDailyTargetQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdDailyTargetQuery(Long id) {
        super();
        this.setId(id);
    }

    /**
     * 线上拜访数量是否不为空
     */
    private Integer visitOnlineCountIsNotNull;
}