package net.summerfarm.crm.model.domain.BDTarget;

import java.util.List;

import lombok.Data;

/**
 * BdDailyTargetDetail查询对象
 */
@Data
public class BdDailyTargetDetailQuery extends BdDailyTargetDetail {

    /**
     * 主键ID列表，用于批量查询
     */
    private List<Long> ids;

    /**
     * 无参构造方法
     */
    public BdDailyTargetDetailQuery() {
        super();
    }

    /**
     * 带id参数的构造方法
     * @param id 主键ID
     */
    public BdDailyTargetDetailQuery(Long id) {
        super();
        this.setId(id);
    }

    /**
     * 带ids参数的构造方法
     * @param ids 主键ID列表
     */
    public BdDailyTargetDetailQuery(List<Long> ids) {
        super();
        this.ids = ids;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}