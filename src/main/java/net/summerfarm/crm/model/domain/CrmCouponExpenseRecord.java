package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * crm_coupon_expense_record
 * <AUTHOR>
@Data
public class CrmCouponExpenseRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * primary key
     */
    private Long id;
    /**
     * create time
     */
    private Date createTime;
    /**
     * update time
     */
    private Date updateTime;
    /**
     * 费用池id
     */
    private Long poolId;
    /**
     * 划分人id
     */
    private Integer adminId;
    /**
     * 余额
     */
    private BigDecimal totalAmount;
    /**
     * 余额
     */
    private BigDecimal remainingAmount;
    /**
     * 创建人id
     */
    private Long createUserId;
}