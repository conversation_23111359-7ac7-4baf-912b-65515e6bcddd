package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 每个sku每月下单商户id表
 * @TableName crm_sku_area_month_merchant
 */
@Data
public class CrmSkuAreaMonthMerchant implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * sku
     */
    private String sku;

    /**
     * 区域no
     */
    private Integer areaNo;

    /**
     * 商户ids, 使用,分割
     */
    private String merchantIdText;

    /**
     * 信号表标记:yyyyMM
     */
    private Integer monthTag;

    private static final long serialVersionUID = 1L;
}