package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * crm任务门店命中任务品(品,品类等)详情
 */
@Data
@TableName(value = "crm_job_merchant_item")
public class CrmJobMerchantItem {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 任务Id FK crm_job
     */
    @TableField(value = "job_id")
    private Long jobId;

    /**
     * 门店id
     */
    @TableField(value = "m_id")
    private Long mId;

    /**
     * 任务item,比如品或品类
     */
    @TableField(value = "item")
    private String item;

    /**
     * item类型 0 - sku; 1 - 品类; 2 - 其它(自定义)
     */
    @TableField(value = "item_type")
    private Integer itemType;

    /**
     * 达成状态 0.未完成 1.已完成
     */
    @TableField(value = "`status`")
    private Integer status;
}