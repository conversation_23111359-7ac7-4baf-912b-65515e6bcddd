package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * crm_manage_area
 * <AUTHOR>
@Data
public class CrmManageArea implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * 负责人id
     */
    private Integer manageAdminId;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 创建人id
     */
    private Integer creator;

    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}