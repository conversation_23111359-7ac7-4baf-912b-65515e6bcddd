package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * crm_bd_area
 * <AUTHOR>
@Data
public class CrmBdArea implements Serializable {
    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * bd id
     */
    private Integer adminId;

    /**
     * 城市编号
     */
    private Integer areaNo;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}