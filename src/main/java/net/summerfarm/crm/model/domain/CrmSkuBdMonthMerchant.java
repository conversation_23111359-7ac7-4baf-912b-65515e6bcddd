package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 每个sku每月下单商户id表
 * @TableName crm_sku_bd_month_merchant
 */
@Data
public class CrmSkuBdMonthMerchant implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * sku
     */
    private String sku;

    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 商户ids, 使用,分割
     */
    private String merchantIdText;

    /**
     * 月份标记:yyyyMM
     */
    private Integer monthTag;

    private static final long serialVersionUID = 1L;
}