package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商户当日及未来gmv表
 * @TableName crm_merchant_future_day_gmv
 */
@Data
public class CrmMerchantFutureDayGmv implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 商户所在运营区域
     */
    private Integer areaNo;

    /**
     * 配送gmv
     */
    private BigDecimal distributionGmv;

    /**
     * 鲜果gmv
     */
    private BigDecimal fruitGmv;

    /**
     * 乳制品gmv
     */
    private BigDecimal dairyGmv;

    /**
     * 非乳制品gmv
     */
    private BigDecimal nonDairyGmv;

    /**
     * 自营品牌gmv
     */
    private BigDecimal brandGmv;

    /**
     * 固定奖励sku的gmv
     */
    private BigDecimal rewardGmv;

    /**
     * 下单spu数
     */
    private Integer spuNum;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * 联系地址id
     */
    private Integer contactId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 预估收益
     */
    private BigDecimal estimatedIncome;

    /**
     * 达标后预估最低收益
     */
    private BigDecimal minimumIncomeAfterReachingStandard;

    /**
     * 达标后预估最高收益
     */
    private BigDecimal maximumIncomeAfterReachingStandard;

    /**
     * 是否达标:0未达标 1已达标
     */
    private Integer deliveryUpToStandard;

    /**
     * 归属bdid,公海为0
     */
    private Integer bdId;

    private static final long serialVersionUID = 1L;
}