package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * bd销售每日数据小时级更新
 */
@Data
@TableName("crm_bd_today_hour_gmv")
public class CrmBdTodayHourGmv {
    /**
    * primary key
    */
    private Long id;

    /**
    * 时间标签
    */
    private Integer dateTag;

    /**
    * bdID
    */
    private Long bdId;

    /**
    * bd名称
    */
    private String bdName;

    /**
    * 是否同城市（BD归属的M1和客户注册行政市区所属的M1是否一致）
    */
    private String isSameCity;

    /**
    * 交易应付GMV
    */
    private BigDecimal originTotalGmv;

    /**
    * 交易实付GMV
    */
    private BigDecimal realTotalGmv;

    /**
    * 交易客户数
    */
    private Long custCnt;

    /**
    * 全品类交易应付GMV
    */
    private BigDecimal categoriesOriginTotalGmv;

    /**
    * 全品类交易实付GMV
    */
    private BigDecimal categoriesRealTotalGmv;

    /**
    * 全品类交易客户数
    */
    private Long categoriesCustCnt;

    /**
    * 鲜果交易应付GMV
    */
    private BigDecimal fruitOriginTotalGmv;

    /**
    * 鲜果交易实付GMV
    */
    private BigDecimal fruitRealTotalGmv;

    /**
    * 鲜果交易客户数
    */
    private Long fruitCustCnt;

    /**
    * 安佳铁塔交易应GMV
    */
    private BigDecimal anchoOriginTotalGmv;

    /**
    * 安佳铁塔交易实付GMV
    */
    private BigDecimal anchoRealTotalGmv;

    /**
    * 安佳铁塔交易客户数
    */
    private Long anchoCustCnt;

    /**
    * 乳制品（不含AT）交易应付GMV
    */
    private BigDecimal dairyOriginTotalGmv;

    /**
    * 乳制品（不含AT）交易实付GMV
    */
    private BigDecimal dairyRealTotalGmv;

    /**
    * 乳制品（不含AT）交易客户数
    */
    private Long dairyCustCnt;

    /**
    * 其他交易应付GMV
    */
    private BigDecimal otherOriginTotalGmv;

    /**
    * 其他交易实付GMV
    */
    private BigDecimal otherRealTotalGmv;

    /**
    * 其他交易客户数
    */
    private Long otherCustCnt;

    /**
    * 非AT交易应付GMV
    */
    private BigDecimal noAnchorOriginTotalGmv;

    /**
    * 非AT交易实付GMV
    */
    private BigDecimal noAnchorRealTotalGmv;

    /**
    * 非AT交易客户数
    */
    private Long noAnchorCustCnt;

    /**
    * 拜访数
    */
    private Long ordinaryNum;

    /**
    * 履约实付GMV
    */
    private BigDecimal dlvRealTotalGmv;

    /**
    * 省心送明日履约实付GMV
    */
    private BigDecimal dlvTimingRealTotalGmv;

    /**
    * 履约客户数
    */
    private Long dlvCustCnt;

    /**
    * 全品类履约实付GMV
    */
    private BigDecimal dlvCategoriesRealTotalGmv;

    /**
    * 全品类履约客户数
    */
    private Long dlvCategoriesCustCnt;

    /**
    * 鲜果履约实付GMV
    */
    private BigDecimal dlvFruitRealTotalGmv;

    /**
    * 鲜果履约客户数
    */
    private Long dlvFruitCustCnt;

    /**
    * 安佳铁塔履约实付GMV
    */
    private BigDecimal dlvAnchoRealTotalGmv;

    /**
    * 安佳铁塔省心送履约实付GMV
    */
    private BigDecimal dlvTimingAnchoRealTotalGmv;

    /**
    * 安佳铁塔履约客户数
    */
    private Long dlvAnchoCustCnt;

    /**
    * 乳制品（不含AT）履约实付GMV
    */
    private BigDecimal dlvDairyRealTotalGmv;

    /**
    * 乳制品（不含AT）省心送履约实付GMV
    */
    private BigDecimal dlvTimingDairyRealTotalGmv;

    /**
    * 乳制品（不含AT）履约客户数
    */
    private Long dlvDairyCustCnt;

    /**
    * 其他履约实付GMV
    */
    private BigDecimal dlvOtherRealTotalGmv;

    /**
    * 其他省心送履约实付GMV
    */
    private BigDecimal dlvTimingOtherRealTotalGmv;

    /**
    * 其他履约客户数
    */
    private Long dlvOtherCustCnt;

    /**
    * 非AT履约实付GMV
    */
    private BigDecimal dlvNoAnchorRealTotalGmv;

    /**
    * 非AT履约客户数
    */
    private Long dlvNoAnchorCustCnt;

    /**
    * 非AT省心送实付GMV
    */
    private BigDecimal dlvTimingNoAnchorRealTotalGmv;

    /**
    * 非AT履约实付GMV+全品类交易实付GMV
    */
    private BigDecimal noAnchorCategoriesKpiGmv;
}