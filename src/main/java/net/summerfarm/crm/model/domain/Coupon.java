package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.crm.common.util.DateUtils;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class Coupon implements Serializable {

    /**
     * 优惠券id
     */
    @Null
    private Integer id;

    /**
     * 红包名称
     */
    @NotNull
    private String name;

    /**
     * 优惠券代码
     */
    private String code;

    /**
     * 额度
     */
    @Min(1)
    @NotNull
    private BigDecimal money;

    /**
     * 使用阈值
     */
    @Min(0)
    @NotNull
    private BigDecimal threshold;

    /**
     * 有效期类型，0指固定时间间隔到期，1固定时间点到期
     */
    @Range(min = 0, max = 1, message = "无效的有效期类型")
    private Byte type;

    /**
     * 优惠券分组 0-活动 1-售后 2-新人 3-权益  4-客情券
     *        5-用户召回 6-新品 7-消费返券 8-员工福利
     */
    @InRange(rangeNums = {0,1,2,3,4,5,6,7,8})
    private Integer grouping;

    /**
     * 有效活动范围
     */
    private Integer activityScope;

    /**
     * 有效日期
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private Date vaildDate;

    /**
     * 有效时间，单位：天
     */
    private Integer vaildTime;

    /**
     * 备注
     */
    private String reamrk;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 可用的品类
     */
    private String categoryId;

    /**
     * sku编号
     */
    private String sku;

    /**
     * 优惠券类型 1普通商品优惠券2普通运费优惠券3精准送优惠券4红包
     */
    @InRange(rangeNums = {1,2,3,4})
    private Integer agioType;

    /**
     * 优惠券状态：1有效、2无效
     */
    private Integer status;

    /**
     * 是否系统创建
     */
    private Integer autoCreated;
    /**
     * 开始生效时间
     */
    @DateTimeFormat(pattern = DateUtils.LONG_DATE_FORMAT)
    private LocalDateTime startDate;

    /**
     * 开始生效间隔时间
     */
    private Integer startTime;

    private Integer number;

    /**
     * 是否置灰 0置灰
     */
    private Integer showFlag;

    private Integer couponId;

    private Integer couponSenderId;

    /**
     * 是否限制发放一个 0(否）1（是）
     */
    private Integer limitFlag;

    /**
     * 任务标识:0(否),1(是)
     */
    private Integer taskTag;
    /**
     * 任务作废标识:0(否),1(是)
     */
    private Integer deleteTag;
    /**
     * 关联操作,例如优惠券
     */
    private Integer operation;
    /**
     * 任务状态:未开始(0)\进行中(1)\已结束(2)\取消(3)
     */
    private Integer taskStatus;
    /**
     * 卡券结束时间毫秒值
     */
    private Date taskEndTime;
}