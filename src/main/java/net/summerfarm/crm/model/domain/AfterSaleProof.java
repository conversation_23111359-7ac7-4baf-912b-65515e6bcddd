package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName(value = "after_sale_proof")
public class AfterSaleProof {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 售后单号
     */
    @TableField(value = "after_sale_order_no")
    private String afterSaleOrderNo;

    /**
     * 售后数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 售后金额
     */
    @TableField(value = "handle_num")
    private BigDecimal handleNum;

    /**
     * 售后凭证
     */
    @TableField(value = "proof_pic")
    private String proofPic;

    /**
     * 售后类型
     */
    @TableField(value = "after_sale_type")
    private String afterSaleType;

    /**
     * 退款类型
     */
    @TableField(value = "refund_type")
    private String refundType;

    /**
     * 售后处理方式,0返券，1未知，2退款，3录入账单，4退货退款，5退货录入账单，6换货，7补发，8人工退款，9拒收退款，10拒收账单，11拦截退款，12拦截录入账单 13退运费 14 退运费录入账单
     */
    @TableField(value = "handle_type")
    private Integer handleType;

    /**
     * 处理人
     */
    @TableField(value = "`handler`")
    private String handler;

    /**
     * 原因
     */
    @TableField(value = "handle_remark")
    private String handleRemark;

    /**
     * 审批人
     */
    @TableField(value = "auditer")
    private String auditer;

    /**
     * 售后状态：0、审核中 1、处理中 2、成功 3、失败 4、补充凭证 11、取消 12、退款中
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 审核备注
     */
    @TableField(value = "apply_remark")
    private String applyRemark;

    @TableField(value = "updatetime")
    private LocalDateTime updatetime;

    /**
     * 审核人
     */
    @TableField(value = "applyer")
    private String applyer;

    /**
     * 审批备注
     */
    @TableField(value = "audite_remark")
    private String auditeRemark;

    /**
     * 审核备注
     */
    @TableField(value = "extra_remark")
    private String extraRemark;

    /**
     * 审批时间
     */
    @TableField(value = "auditetime")
    private LocalDateTime auditetime;

    /**
     * 审核时间
     */
    @TableField(value = "handletime")
    private LocalDateTime handletime;

    /**
     * 回收废金额
     */
    @TableField(value = "recovery_num")
    private BigDecimal recoveryNum;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 二级审核原因
     */
    @TableField(value = "handle_secondary_remark")
    private String handleSecondaryRemark;

    /**
     * 二级售后分类
     */
    @TableField(value = "apply_secondary_remark")
    private String applySecondaryRemark;

    /**
     * 售后视频
     */
    @TableField(value = "proof_video")
    private String proofVideo;
}