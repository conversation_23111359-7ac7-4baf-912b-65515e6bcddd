package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 门店增量标签更新
 * @TableName crm_merchant_increment_label
 */
@Data
public class CrmMerchantIncrementLabel implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 商户标签
     */
    private String merchantLabel;

    /**
     * 数据所在日标记:yyyyMMdd
     */
    private Integer dayTag;

    /**
     * 微信unionid
     */
    private String unionid;

    /**
     * 变动类型: 0:新增标签;1:删除标签
     */
    private Integer type;
    /**
     * 组名称
     */
    private String groupName;

    private static final long serialVersionUID = 1L;
}