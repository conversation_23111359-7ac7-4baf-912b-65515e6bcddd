package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 风控门店
 *
 * <AUTHOR>
 * @date 2023/11/17 16:58
 */
@Data
public class RiskMerchant {
    /**
     * primary key
     */
    private Integer id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 门店 id
     */
    private Integer mId;

    /**
     * 门店类型:单店/大客户
     */
    private String size;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 触发场景:0:新注册;1:存量动销;
     */
    private Integer triggerOccasions;

    /**
     * 处理状态:0:未处理;1:审核通过;2:审核未通过;
     */
    private Integer status;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 运营服务区
     */
    private Integer areaNo;

    /**
     * 运营服务区
     */
    private String areaName;

    /**
     * 销售 id
     */
    private Integer bdId;

    /**
     * 销售 名称
     */
    private String bdName;

    /**
     * 归属大客户
     */
    private Integer adminId;

    /**
     * 归属大客户
     */
    private String nameRemakes;

    /**
     * 门头照
     */
    private String doorPic;

    /**
     * 门头照 OCR结果
     */
    private String doorPicOcr;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 审核人
     */
    private Integer auditorId;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 命中分类:0:疑似重复;1:疑似虚假;2:疑似换壳;
     */
    private Integer triggerClassification;

    /**
     * poi
     */
    private String poi;
}