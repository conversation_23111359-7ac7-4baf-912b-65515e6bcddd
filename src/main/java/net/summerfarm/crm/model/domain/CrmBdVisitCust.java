package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户拜访汇总
 */
@Data
public class CrmBdVisitCust {
    /**
     * PRIMARY
     */
    private Long id;

    /**
     * 时间标签
     */
    private Integer dateTag;

    /**
     * 客户ID
     */
    private Long custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 客户业态：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他
     */
    private String custType;

    /**
     * 客户分组：大客户、平台客户、批发客户
     */
    private String custGroup;

    /**
     * 注册时省
     */
    private String registerProvince;

    /**
     * 注册时市
     */
    private String registerCity;

    /**
     * 注册分类：1、当月新注册 2、本月之前注册且上月未下单 3、本月之前注册且上月已下单
     */
    private Integer registerType;

    /**
     * 拜访销售ID
     */
    private Long visitBdId;

    /**
     * 拜访销售名称
     */
    private String visitBdName;

    /**
     * 拜访来源：七鱼电话、企微、个微
     */
    private String visitType;

    /**
     * 拜访时间
     */
    private LocalDateTime visitTime;

    /**
     * 拜访后首次登录时间
     */
    private LocalDateTime visitAfterLoadTime;

    /**
     * 拜访后首次点击时间
     */
    private LocalDateTime visitAfterClickTime;

    /**
     * 拜访后首次加购时间
     */
    private LocalDateTime visitAfterAddTime;

    /**
     * 拜访后七天是否下单
     */
    private String visitAfterIsBuy;

    /**
     * 拜访后七天首次下单时间
     */
    private String visitAfterFirstorderTime;

    /**
     * 拜访后七天总实付gmv
     */
    private BigDecimal visitAfterOriginAmt;

    /**
     * 拜访后七天sku名称&规格
     */
    private String visitAfterSkuName;

    /**
     * 跟进情况描述
     */
    private String visitRemarks;

    /**
     * 是否有效拜访
     */
    private String isEffectiveVisit;
}