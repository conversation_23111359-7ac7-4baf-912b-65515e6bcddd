package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 企微客户联系「联系我」配置
 * @TableName wecom_user_contact_way
 */
@Data
public class WeComUserContactWay implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 鲜沐id
     */
    private Long adminId;

    /**
     * 新增联系方式的配置id
     */
    private String configId;

    /**
     * 联系方式的备注信息，用于助记，不超过30个字符
     */
    private String remark;

    /**
     * 企业自定义的state参数，用于区分不同的添加渠道，不超过30个字符
     */
    private String state;

    /**
     * 使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
     */
    private String userId;

    /**
     * 联系我二维码链接
     */
    private String qrCode;

    private static final long serialVersionUID = 1L;
}