package net.summerfarm.crm.model.domain;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户维度PB标品和利润积分
 * 
 * <AUTHOR>
 */
@Data
public class CustPbScoreComm {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * BD_ID
     */
    private Long bdId;

    /**
     * 归属BD名称
     */
    private String bdName;

    /**
    * 客户数量
    */
    private Integer custCnt;

    /**
     * 客户ID
     */
    private Long custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 上月PB履约GMV
     */
    private BigDecimal lastDlvRealAmtPb;

    /**
     * PB本月截止昨晚履约GMV
     */
    private BigDecimal dlvRealAmtPb;

    /**
     * PB今日待履约GMV
     */
    private BigDecimal dlvRealAmtTodayPb;

    /**
     * PB今日交易本月待履约GMV
     */
    private BigDecimal dlvOrderAmtTodayPb;

    /**
     * PB其余待履约GMV
     */
    private BigDecimal dlvOtherAmtTodayPb;

    /**
     * PB本月预计总履约GMV
     */
    private BigDecimal totalCateGroupAmtPb;

    /**
     * 上月利润积分
     */
    private Double lastCateGroupScore;

    /**
     * 本月截止昨晚履约利润积分
     */
    private Double cateGroupScore;

    /**
     * 今日待履约利润积分
     */
    private Double cateGroupScoreToday;

    /**
     * 今日交易本月待履约利润积分
     */
    private Double orderGroupScoreToday;

    /**
     * 其余待履约利润积分
     */
    private Double otherGroupScoreToday;

    /**
     * 本月预计总履约利润积分
     */
    private Double totalCateGroupScore;

    /**
     * 客户类型：平台客户（单店）、大客户
     */
    private String custType;

    /**
     * 数据所属日期
     */
    private String ds;

    /**
     * 来源：SaaS，鲜沐
     */
    private String orderSource;

    /**
     * 销售区域
     */
    private String bdWorkZone;

}
