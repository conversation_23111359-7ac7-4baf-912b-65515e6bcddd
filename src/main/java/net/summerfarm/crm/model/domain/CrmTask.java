package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * crm 任务中心
 *
 * <AUTHOR>
 * @date 2023/10/8 11:52
 */
@Data
@Accessors(chain = true)
public class CrmTask {
    /**
     * primary key
     */
    private Integer id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 0: 发券;1:拜访
     */
    private Integer type;

    /**
     * 发券：卡券 id/ 拜访:省心送订单号
     */
    private String sourceId;

    /**
     * 任务结束时间
     */
    private LocalDateTime startTime;

    /**
     * 任务开始时间
     */
    private LocalDateTime endTime;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 修改人
     */
    private Integer updator;

    /**
     * 作废标记:0:正常;1:作废
     */
    private Integer deleteFlag;
}