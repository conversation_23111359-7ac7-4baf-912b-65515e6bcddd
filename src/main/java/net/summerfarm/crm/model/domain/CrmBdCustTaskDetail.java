package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户任务达成情况表
 */
@Data
@TableName(value = "crm_bd_cust_task_detail")
public class CrmBdCustTaskDetail {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * job_id
     */
    @TableField(value = "job_id")
    private Long jobId;

    /**
     * cust_id
     */
    @TableField(value = "cust_id")
    private Long custId;

    /**
     * 完成状态 0.未完成 1.已完成
     */
    @TableField(value = "completion_status")
    private Integer completionStatus;

    /**
     * 任务期间累计下单实付
     */
    @TableField(value = "job_real_total_amt")
    private BigDecimal jobRealTotalAmt;
}