package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商户商城搜索记录表
 * @TableName crm_merchant_mall_search_top
 */
@Data
public class CrmMerchantMallSearchTop implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 搜索次数
     */
    private Integer searchNum;

    /**
     * 信号表标记:yyyyMMdd
     */
    private Integer dayTag;

    private static final long serialVersionUID = 1L;
}