package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * SAAS（鲜沐订单）
 */
@Data
public class SaasOrderCust {
    /**
    * primary key
    */
    private Long id;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * 下单时间
    */
    private LocalDateTime orderTime;

    /**
    * 支付时间
    */
    private LocalDateTime payTime;

    /**
    * 配送时间
    */
    private LocalDateTime deliveryTime;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 应付价格
    */
    private BigDecimal payablePrice;

    /**
    * 配送费
    */
    private BigDecimal deliveryFee;

    /**
    * 订单收货人
    */
    private String orderAddressContactName;

    /**
    * 收货人联系电话
    */
    private String orderAddressContactPhone;

    /**
    * 订单收货地址-省
    */
    private String orderAddressProvince;

    /**
    * 订单收货地址-市
    */
    private String orderAddressCity;

    /**
    * 订单收货地址-区
    */
    private String orderAddressArea;

    /**
    * 收获地址
    */
    private String orderAddress;

    /**
    * 订单状态 2-待配送，3-待收货，6-已收货
    */
    private Long status;

    /**
    * 状态文本
    */
    private String statusText;

    /**
    * 客户类型
    */
    private String mSize;

    /**
    * 省份
    */
    private String province;

    /**
    * 城市
    */
    private String city;

    /**
    * 区县
    */
    private String district;

    /**
    * 商户名称
    */
    private String mname;

    /**
    * 商户联系人
    */
    private String mcontact;

    /**
    * 商户手机号
    */
    private String mphone;

    /**
    * 子账号联系人
    */
    private String subAccountContact;

    /**
    * 子账号手机号
    */
    private String subAccountPhone;

    /**
    * BD ID
    */
    private Long bdId;

    /**
    * BD名称
    */
    private String bdName;
}