package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售BD维度MTD绩效表现 (按客户价值标签聚合)
 */
@Data
@TableName(value = "bd_cval_mtd_performance")
public class BdCvalMtdPerformance {
    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * BD_ID (销售代表ID)
     */
    @TableField(value = "bd_id")
    private Long bdId;

    /**
     * 高价值客户标签
     */
    @TableField(value = "cust_value_lable")
    private String custValueLable;

    /**
     * 最新归属BD名称
     */
    @TableField(value = "bd_name")
    private String bdName;

    /**
     * 大区
     */
    @TableField(value = "bd_region")
    private String bdRegion;

    /**
     * 区域
     */
    @TableField(value = "bd_work_zone")
    private String bdWorkZone;

    /**
     * 是否测试BD
     */
    @TableField(value = "is_test_bd")
    private String isTestBd;

    /**
     * 履约客户数总和
     */
    @TableField(value = "sum_dlv_cust_cnt")
    private Long sumDlvCustCnt;

    /**
     * 客户数佣金总和
     */
    @TableField(value = "sum_cust_comm_amt")
    private BigDecimal sumCustCommAmt;

    /**
     * 履约应付GMV总和
     */
    @TableField(value = "sum_dlv_ori_amt")
    private BigDecimal sumDlvOriAmt;

    /**
     * 履约实付GMV总和
     */
    @TableField(value = "sum_dlv_real_amt")
    private BigDecimal sumDlvRealAmt;

    /**
     * 自营商品毛利润总和
     */
    @TableField(value = "sum_item_profit_amt")
    private BigDecimal sumItemProfitAmt;

    /**
     * AT_履约实付金额总和
     */
    @TableField(value = "sum_dlv_real_amt_at")
    private BigDecimal sumDlvRealAmtAt;

    /**
     * 流量品_履约实付金额总和
     */
    @TableField(value = "sum_dlv_real_amt_expo")
    private BigDecimal sumDlvRealAmtExpo;

    /**
     * 利润品_履约实付金额总和
     */
    @TableField(value = "sum_dlv_real_amt_profit")
    private BigDecimal sumDlvRealAmtProfit;

    /**
     * 常规品_履约实付金额总和
     */
    @TableField(value = "sum_dlv_real_amt_normal")
    private BigDecimal sumDlvRealAmtNormal;

    /**
     * 鲜果_履约实付金额总和
     */
    @TableField(value = "sum_dlv_real_amt_fruit")
    private BigDecimal sumDlvRealAmtFruit;

    /**
     * 品类组利润得分总和
     */
    @TableField(value = "sum_cate_group_score_num")
    private Object sumCateGroupScoreNum;

    /**
     * 履约SPU数总和
     */
    @TableField(value = "sum_dlv_spu_cnt")
    private Long sumDlvSpuCnt;

    /**
     * 超额SPU数总和
     */
    @TableField(value = "sum_more_than_spu_cnt")
    private Long sumMoreThanSpuCnt;

    /**
     * 超额SPU数佣金总和
     */
    @TableField(value = "sum_more_than_spu_comm")
    private BigDecimal sumMoreThanSpuComm;

    /**
     * 超额SPU客户数总和
     */
    @TableField(value = "sum_more_than_spu_cust")
    private Long sumMoreThanSpuCust;

    /**
     * 高价值客户佣金总和 (按BD和标签汇总)
     */
    @TableField(value = "sum_total_comm_amt")
    private BigDecimal sumTotalCommAmt;

    /**
     * 数据日期 (例如 YYYY-MM-DD 或 YYYYMMDD)
     */
    @TableField(value = "ds")
    private String ds;
}