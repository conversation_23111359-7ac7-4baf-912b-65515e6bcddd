package net.summerfarm.crm.model.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * crm_wechat_tag_group
 * <AUTHOR>
@Data
public class CrmWechatTagGroup implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 标签组名称
     */
    private String groupName;

    /**
     * 商户标签
     */
    private String merchantLabel;

    /**
     * 数据标记
     */
    private Integer dayTag;

    /**
     * 变动类型: 0:新增标签;1:删除标签,
     */
    private Byte type;

    /**
     * 排序 order值大的排序靠前
     */
    private Integer rank;

    private static final long serialVersionUID = 1L;
}