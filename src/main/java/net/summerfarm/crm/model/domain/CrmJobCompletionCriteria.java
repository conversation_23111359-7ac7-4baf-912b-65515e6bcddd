package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * crm任务完成判定条件
 */
@Data
@TableName(value = "crm_job_completion_criteria")
public class CrmJobCompletionCriteria {
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 任务id FK crm_job
     */
    @TableField(value = "job_id")
    private Long jobId;

    /**
     * 判定类型 0.卡券是否核销 1.拜访类型 2. 任意商品单笔下单实付金额>= xx元 3. 任意商品累计下单实付金额>= xx元 4.任意商品下单>xx件 5.单笔下单实付金额>= xx元
     */
    @TableField(value = "completion_type")
    private Integer completionType;

    /**
     * 完成判定值（卡券id或拜访类型或金额或件数）
     */
    @TableField(value = "completion_value")
    private String completionValue;

    /**
     * 任务支持的订单类型列表：0-普通订单，1-省心送订单
     */
    @TableField(value = "order_type_list")
    private String orderTypeList;
}