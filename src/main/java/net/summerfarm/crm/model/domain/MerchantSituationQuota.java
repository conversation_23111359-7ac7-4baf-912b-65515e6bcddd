package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 *create at:  2019/8/2  4:35 PM
 * 已使用客情额度
 */
@Data
public class MerchantSituationQuota {

    /**
     * id 自增
     */
    private Integer id;

    /**
    * bdId
    */
    private Integer adminId;

    /**
    * 创建时间
    */
    private LocalDateTime gmtCreate;

    /**
    * 更新时间
    */
    private LocalDateTime gmtModified;

    /**
    * 已用额度
    */
    private BigDecimal amount;

    /**
    *是否可用 0 不可用 1 可用
    */
    private Integer status;

    /**
    * 所属城市
    */
    private Integer areaNo;

}
