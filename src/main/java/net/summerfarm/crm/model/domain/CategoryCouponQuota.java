package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 品类券额度
 *
 * <AUTHOR>
 * @Date 2023/2/28 17:45
 */
@Data
@EqualsAndHashCode
public class CategoryCouponQuota {
    private Integer id;

    /**
     * bd 名称
     */
    private String adminName;

    /**
     * bd id
     */
    private Integer adminId;

    /**
     * 额度
     */
    private BigDecimal quota;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 额度类型: 0:品类券;1:月活券
     */
    private Integer type;

    /**
     * 新客费比
     */
    private BigDecimal newCustomerRate;

    /**
     * 老客户费比
     */
    private BigDecimal oldCustomerRate;
}
