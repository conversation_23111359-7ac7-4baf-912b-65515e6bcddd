package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/4/24  10:42
 * 样品申请sku信息
 */
@Data
public class SampleSku {

    private Integer id;

    /**
    * '样品申请ID'
    */
    private Integer sampleId;

    /**
    * 'sku'
    */
    private String sku;

    /**
    * '商品名称'
    */
    private String pdName;

    /**
    *  '申请数量'
    */
    private Integer amount;

    /**
    * '规格'
    */
    private String weight;


    /**
     *拦截状态 0 正常 1被拦截
     */
    private Integer interceptFlag;


    /**
     * 拦截时间
     */
    private LocalDateTime interceptTime;

    /**
     * 完成排线-展示标识 0 展示 1不展示
     */
    private Integer showFlag;
}
