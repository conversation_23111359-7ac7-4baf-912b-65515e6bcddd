package net.summerfarm.crm.model.domain;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.crm.model.vo.PoiVO;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/14 2:00
 */
@Data
public class Contact implements Serializable {
    /**
     *联系人id
     */
    private Long contactId;
    /**
     *商户id
     */
    @NotNull(message = "mId.null", groups = {Add.class})
    private Long mId;
    /**
     *联系人
     */
    @NotNull(message = "contact.null", groups = {Add.class})
    private String contact;
    /**
     *职位
     */
    private String position;
    /**
     *性别
     */
    private Boolean gender;
    /**
     *手机号
     */
    @NotNull(message = "phone.null", groups = {Add.class})
    private String phone;
    /**
     *邮箱
     */
    private String email;
    /**
     *省份
     */
    @NotNull(message = "province.null", groups = {Add.class})
    private String province;
    /**
     * 城市
     */
    @NotNull(message = "city.null", groups = {Add.class})
    private String city;
    /**
     */
    @NotNull(message = "area.null", groups = {Add.class})
    private String area;
    /**
     * 地址
     */
    @NotNull(message = "address.null", groups = {Add.class})
    private String address;
    /**
     * 状态(1正常或审核通过、2删除、3待审核、4审核不通过)
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 配送车辆
     */
    private String deliveryCar;
    /**
     * 1默认地址
     */
    private Integer isDefault;
    /**
     * 经纬度
     */
    private String poiNote;
    /**
     * 到仓库距离
     */
    private BigDecimal distance;
    /**
     * 预排路线
     */
    private String path;
    /**
     * 门牌号
     */
    private String houseNumber;
    /**
     * 配送仓编号
     */
    private Integer storeNo;
    /**
     * poi
     */
    private PoiVO poi;
    /**
     * 配送周期
     */
    private String deliveryFrequent;

    /**
     * 运营区域id
     */
    private Integer areaNo;
    /**
     * 运营区域名
     */
    private String areaName;

    /**
     * 是否修改
     */
    private Boolean modify;
    /**
     * 是否使用新地址
     */
    private Boolean useNew;

    /**
     * 首配日
     */
    private LocalDate nextDeliveryDate;
    /**
     * 地址备注
     */

    private String addressRemark;

    /**
     * 自定义地址备注
     */
    @Setter
    @Getter
    private ContactAddressRemark contactAddressRemark;



    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public void initAddrRemark(){
        if (StringUtils.isEmpty(addressRemark)){
            return;
        }
        ContactAddressRemark contactAddressRemark = JSONUtil.toBean(addressRemark, ContactAddressRemark.class);
        setContactAddressRemark(contactAddressRemark);
    }
}