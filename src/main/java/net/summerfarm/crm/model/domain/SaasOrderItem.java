package net.summerfarm.crm.model.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * SAAS（鲜沐订单）订单详情
 */
@Data
public class SaasOrderItem {
    /**
    * primary key
    */
    private Long id;

    /**
    * update time
    */
    private LocalDateTime updateTime;

    /**
    * create time
    */
    private LocalDateTime createTime;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 订单项-SKU
    */
    private String skuId;

    /**
    * 订单项-商品pd_id
    */
    private Long spuId;

    /**
    * 订单项-商品名称
    */
    private String spuName;

    /**
    * 订单项-规格
    */
    private String weight;

    /**
    * 订单项-数量
    */
    private Long skuCnt;

    /**
    * 订单项-单价
    */
    private BigDecimal payablePrice;

    /**
    * 订单项-状态 2-待配送，3-待收货，6-已收货
    */
    private Long orderItemStatus;

    /**
    * 订单项-图片路径
    */
    private String picturePath;
}