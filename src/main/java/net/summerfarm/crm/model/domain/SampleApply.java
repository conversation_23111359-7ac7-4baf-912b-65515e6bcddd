package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;
import net.summerfarm.crm.common.util.DateUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR> ct
 * create at:  2020/4/24  10:34
 * 样品申请
 */
@Data
@ToString
@TableName("sample_apply")
public class SampleApply {

    private Integer sampleId;

    /**
    * 添加时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * '创建人id'
    */
    private Integer createId;
    /**
    * '创建人名称'
    */
    private String createName;

    /**
    * '试样用户id'
    */
    private Long mId;

    /**
    * '试样用户名称'
    */
    private String mName;

    /**
    * '试样用户收货地址id'
    */
    private Integer contactId;

    private Integer areaNo;

    /**
    * '客户所属用户bdid'
    */
    private Integer bdId;

    /**
    * 用户会员等级
    */
    private Integer grade;

    /**
    * 用户类型
    */
    private String mSize;

    /**
    * 用户联系方式
    */
    private String mPhone;

    /**
    * 用户联系人
    */
    private String mContact;

    /**
    * '客户归属bd名称'
    */
    private String bdName;

    /**
    * '状态 0 待反馈 1 已反馈 2-取消 3-待审核、4-已关闭'
    */
    private Integer status;

    /**
    * '客户满意度 0 未评价,  1 满意 ,2 一般, 3 不满意'
    */
    private Integer satisfaction;

    /**
    * '客户购买意向 0 未评价 1 乐意购买 ,2 考虑购买, 3 不购买'
    */
    private Integer purchaseIntention;

    /**
    * '备注'
    */
    private String remark;

    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;

    /** 仓 */
    private Integer storeNo;
    /**
     * 风险标签
     */
    private Integer riskLevel;

    /**
     * 销售主体名称
     */
    private String sellingEntityName;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getmName() {
        return mName;
    }

    public void setmName(String mName) {
        this.mName = mName;
    }

    public String getmSize() {
        return mSize;
    }

    public void setmSize(String mSize) {
        this.mSize = mSize;
    }

    public String getmPhone() {
        return mPhone;
    }

    public void setmPhone(String mPhone) {
        this.mPhone = mPhone;
    }

    public String getmContact() {
        return mContact;
    }

    public void setmContact(String mContact) {
        this.mContact = mContact;
    }
}
