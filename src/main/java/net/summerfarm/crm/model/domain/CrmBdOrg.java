package net.summerfarm.crm.model.domain;

import com.alibaba.excel.support.cglib.core.Local;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * crm销售组织
 *
 * <AUTHOR>
 * @date 2023/08/22
 */
@Data
@Accessors(chain = true)
public class CrmBdOrg {
    /**
     * id
     */
    private Integer id;
    /**
     * 销售id
     */
    private Integer bdId;

    /**
     * 销售名字
     */
    private String bdName;

    /**
     * 上级 id
     */
    private Integer parentId;

    /**
     * 上级姓名
     */
    private String parentName;

    /**
     * 级别:1:m3;2:m2;3:m1;4:bd
     */
    private Integer rank;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
