package net.summerfarm.crm.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 配送计划表
 */
@Data
@TableName(value = "delivery_plan")
public class DeliveryPlan {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 配送状态: 1待支付，2待配送，3待收货，6已收货，8已退款，10支付中断超时关闭订单，11已撤销，14手动关闭订单
     */
    @TableField(value = "`status`")
    private Object status;

    /**
     * 配送时间
     */
    @TableField(value = "delivery_time")
    private LocalDate deliveryTime;

    /**
     * 配送数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 随单配送时对应的普通订单订单号
     */
    @TableField(value = "master_order_no")
    private String masterOrderNo;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 联系人
     */
    @TableField(value = "contact_id")
    private Integer contactId;

    /**
     * 配送方式：0 配送，1自提
     */
    @TableField(value = "deliverytype")
    private Boolean deliverytype;

    /**
     * 配送时间区间
     */
    @TableField(value = "time_frame")
    private String timeFrame;

    /**
     * 子帐号id
     */
    @TableField(value = "account_id")
    private Long accountId;

    /**
     * 管理员id
     */
    @TableField(value = "admin_id")
    private Integer adminId;

    /**
     * 下单仓编号
     */
    @TableField(value = "order_store_no")
    private Integer orderStoreNo;

    /**
     * 省心送推迟订单时间
     */
    @TableField(value = "put_off_time")
    private LocalDate putOffTime;

    @TableField(value = "add_time")
    private LocalDateTime addTime;

    /**
     * 旧配送时间
     */
    @TableField(value = "old_delivery_time")
    private LocalDate oldDeliveryTime;

    /**
     * 拦截状态 0 正常 1被拦截
     */
    @TableField(value = "intercept_flag")
    private Integer interceptFlag;

    /**
     * 拦截时间
     */
    @TableField(value = "intercept_time")
    private LocalDateTime interceptTime;

    /**
     * 完成排线-展示标识 0 展示 1不展示
     */
    @TableField(value = "show_flag")
    private Boolean showFlag;

    /**
     * 0:未评价，1:已评价
     */
    @TableField(value = "delivery_evaluation_status")
    private Boolean deliveryEvaluationStatus;
}