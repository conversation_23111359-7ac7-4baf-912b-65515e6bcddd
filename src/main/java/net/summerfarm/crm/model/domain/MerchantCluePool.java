package net.summerfarm.crm.model.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.crm.model.vo.MerchantLeadsVO;
import net.summerfarm.crm.model.vo.MerchantVO;
import org.joda.time.LocalDateTime;

import java.io.Serializable;

/**
 * <AUTHOR> ct
 * create at:  2020/10/13  15:25
 */
@Data
@NoArgsConstructor
public class MerchantCluePool implements Serializable {

    /**
     * 失效
     */
    public static final  Integer CLUE_POOL_NOT_EFFICACY = 1;

    /**
     * 生效
     */
    public static final  Integer CLUE_POOL_EFFICACY = 0;

    /**
     * 线索池绑定
     */
    public static final  Integer BANDING = 1;

    private Integer id;

    /**
    * mid
    */
    private Long mId;

    /**
    * es线索池Id
    */
    private String esId;

    /**
    * merchantLeads id 例子池id
    */
    private Long mlId;

    /**
    * 创建时间
    */
    private LocalDateTime gmtCreate;

    /**
    * 更新时间
    */
    private LocalDateTime gmtModified;

    /**
    * 地址
    */
    private String address;

    /**
    * 门店名称
    */
    private String mName;

    /**
    * 手机号
    */
    private String phone;

    /**
    * 状态 0 生效 1 失效
    */
    private Integer status;

    public  MerchantCluePool(MerchantLeadsVO merchantLeadsVO) {
        this.setPhone(merchantLeadsVO.getCluePhone());
        this.setAddress(merchantLeadsVO.getClueAddress());
        this.setmName(merchantLeadsVO.getClueMName());
        this.setMlId(merchantLeadsVO.getId());
        this.setEsId(merchantLeadsVO.getEsId());
    }
    public  MerchantCluePool(MerchantVO merchantVO) {
        this.setPhone(merchantVO.getCluePhone());
        this.setAddress(merchantVO.getClueAddress());
        this.setmName(merchantVO.getClueMName());
        this.setmId(merchantVO.getmId());
        this.setEsId(merchantVO.getEsId());
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getmName() {
        return mName;
    }

    public void setmName(String mName) {
        this.mName = mName;
    }
}
