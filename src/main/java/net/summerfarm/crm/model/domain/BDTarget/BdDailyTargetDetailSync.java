package net.summerfarm.crm.model.domain.BDTarget;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售每日目标明细同步
 */
@Data
@TableName(value = "crm_bd_daily_target_detail_sync")
public class BdDailyTargetDetailSync {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 销售每日目标id
     */
    @TableField(value = "bd_daily_target_id")
    private Long bdDailyTargetId;

    /**
     * 销售每日目标明细id
     */
    @TableField(value = "bd_daily_target_detail_id")
    private Long bdDailyTargetDetailId;

    /**
     * 销售id
     */
    @TableField(value = "bd_id")
    private Integer bdId;

    /**
     * 目标日期
     */
    @TableField(value = "target_date")
    private LocalDate targetDate;

    /**
     * 指标当前值
     */
    @TableField(value = "indicator_current_value")
    private BigDecimal indicatorCurrentValue;
}