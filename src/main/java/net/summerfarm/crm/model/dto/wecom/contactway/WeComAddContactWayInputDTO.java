package net.summerfarm.crm.model.dto.wecom.contactway;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;
import net.summerfarm.crm.enums.WeComContactWayEnum;
import net.summerfarm.crm.model.query.wecom.contactway.WeComAddContactWayInput;
import net.xianmu.common.exception.ParamsException;

@Data
@Builder
public class WeComAddContactWayInputDTO {

    /**
     * @see net.summerfarm.crm.enums.WeComContactWayEnum.Type
     */
    private Integer type;

    /**
     * @see net.summerfarm.crm.enums.WeComContactWayEnum.Scene
     */
    private Integer scene;

    /**
     * 联系方式的备注信息，用于助记，不超过30个字符
     */
    private String remark;

    /**
     * 企业自定义的state参数，用于区分不同的添加渠道
     * @see net.summerfarm.crm.enums.WeComContactWayEnum.State
     */
    private String state;

    @JSONField(name = "user")
    private String userId;

    public static WeComAddContactWayInputDTO buildFrom(WeComAddContactWayInput input) {
        return WeComAddContactWayInputDTO.builder()
                .type(WeComContactWayEnum.Type.SINGLE.getCode())
                .scene(WeComContactWayEnum.Scene.QR_CODE.getCode())
                .remark(input.getRemark())
                .state(input.getState())
                .userId(input.getUserId())
                .build();
    }
}
