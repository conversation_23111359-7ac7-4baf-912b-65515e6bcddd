package net.summerfarm.crm.model.dto;

import lombok.*;

import java.util.List;

/**
 * 企业微信消息发送
 */
@Data
@Builder
public class QwChatMessageDTO {

    private List<String> external_userid;
    private Text text;
    private List<Attachment> attachments;


    /**
     * 消息文本内容，最多4000个字节
     */
    @Data
    @AllArgsConstructor
    public static class Text {
        private String content;
    }

    /**
     * 附件，最多支持添加9个附件
     */
    @Data
    @Builder
    public static class Attachment {

        private String msgtype;
        private MiniProgram miniprogram;

        @Getter
        @AllArgsConstructor
        public enum MsgType {

            MINI_PROGRAM("miniprogram"),

            ;

            private final String type;

        }

        /**
         * 小程序附件
         */
        @Data
        @Builder
        public static class MiniProgram {
            private String title;
            private String pic_media_id;
            private String appid;
            private String page;
        }
    }
}
