package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.crm.common.util.DataMappingUtil;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAndExtendResp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class MerchantStoreAndExtendDTO extends MerchantStoreAndExtendResp implements Serializable {

    /**
     * 门头照片
     */
    private String doorPic;

    /**
     * 0,正常经营 1,倒闭
     */
    private Integer operateStatus;

    /**
     * 会员当月积分
     */
    private BigDecimal memberIntegral;

    /**
     * 1账期 2现结,
     */
    private Integer direct;

    /**
     * 会员等级
     */
    private Integer grade;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 上次下单时间
     */
    private Date lastOrderTime;

    /**
     * 用户分享码
     */
    private String inviterChannelCode;

    /**
     * openid
     */
    private String openid;

    /**
     * 等级
     */
    private Byte rankId;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 拉黑备注
     */
    private String pullBlackRemark;

    /**
     * 拉黑操作人
     */
    private String pullBlackOperator;

    /**
     * 6位邀请码
     */
    private String inviteCode;

    /**
     * 审核人
     */
    private Integer auditUser;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 商圈
     */
    private String tradeArea;

    /**
     * 商圈组
     */
    private String tradeGroup;

    /**
     * 1服务区内 2服务区外
     */
    private Integer server;

    /**
     * 配送单是否展示价格信息
     */
    private Boolean showPrice;

    /**
     * 是否选择了线索池 0 不是 1 是
     */
    private Integer cluePool;

    /**
     * 公司品牌
     */
    private String companyBrand;

    /**
     * 企业规模
     */
    private String enterpriseScale;

    /**
     * 大客户类型 ka， 批发大客户
     */
    private String merchantType;

    /**
     * 审核类型
     */
    private Integer examineType;

    private Integer displayButton;




    /**
     * 基于鲜沐的merchant补充门店数据
     * @param sourceList
     * @param targetList
     * @param <T>
     * @param <U>
     */
    public static <T, U> void wrapMerchant(List<T> sourceList, List<U> targetList) {
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("mId", "mId");
        fieldMapping.put("doorPic", "doorPic");
        fieldMapping.put("operateStatus", "operateStatus");
        fieldMapping.put("memberIntegral", "memberIntegral");
        fieldMapping.put("direct", "direct");
        fieldMapping.put("grade", "grade");
        fieldMapping.put("houseNumber", "houseNumber");
        fieldMapping.put("lastOrderTime", "lastOrderTime");
        DataMappingUtil.mapAndFillFields(sourceList, targetList, fieldMapping, "mId");
    }
}
