package net.summerfarm.crm.model.dto.crmCouponExpense;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CrmCouponExpenseAdminDto implements Serializable {

    /**
     * 创建人
     */
    private String adminName;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * adminId
     */
    private Integer adminId;

    /**
     * 余额
     */
    private BigDecimal remainingAmount;
    /**
     * 池子id
     */
    private Long poolId;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 费用名称
     */
    private String name;

    /**
     * 有效期 开始
     */
    private LocalDateTime startDate;

    /**
     * 有效期 结束
     */
    private LocalDateTime endDate;

    /**
     * 自动审批状态 0关闭 1开启
     */
    private Byte autoApprove;
    /**
     * 0 不限 1按照品类 2按照sku
     */
    private Byte productRange;
    /**
     * 费比限制
     */
    private Integer costLimit;

    private List<String> limits;
}
