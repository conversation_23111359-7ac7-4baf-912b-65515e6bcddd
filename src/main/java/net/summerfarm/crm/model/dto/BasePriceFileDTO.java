package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Del;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品底价导入导出
 *
 * <AUTHOR>
 * @Date 2023/4/27 18:14
 */
@Data
public class BasePriceFileDTO {
    /**
     * 底价id
     */
    @NotEmpty(message = "删除底价记录不能为空",groups = {Del.class})
    private List<Integer> ids;
    /**
     * 导出导入文件地址
     */
    @NotBlank(message = "文件地址不能为空",groups = {Add.class})
    @NotNull(message = "文件地址不能为空",groups = {Add.class})
    private String filePath;
}
