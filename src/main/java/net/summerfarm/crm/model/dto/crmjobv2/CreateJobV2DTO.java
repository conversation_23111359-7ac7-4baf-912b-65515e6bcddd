package net.summerfarm.crm.model.dto.crmjobv2;

import lombok.Data;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.model.dto.crmjob.JobCompletionCriteriaDTO;
import org.hibernate.validator.constraints.Range;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;

/**
 * 创建任务V2DTO - 支持新的item表结构
 */
@Data
public class CreateJobV2DTO {

    @NotNull(message = "任务名称不能为空")
    private String jobName;

    private String description;

    /**
     * @see net.summerfarm.crm.enums.CrmJobEnum.Type#getCode()
     */
    @NotNull(message = "任务类型不能为空")
    private Integer type;

    /**
     * 子任务类型
     */
    private Integer subType;

    @NotNull(message = "任务开始时间不能为空")
    private LocalDateTime startTime;

    @NotNull(message = "任务结束时间不能为空")
    private LocalDateTime endTime;

    @NotNull(message = "任务完成条件判定不能为空")
    private List<JobCompletionCriteriaDTO> completionCriteriaList;

    /**
     * @see net.summerfarm.crm.enums.CrmJobEnum.MerchantSelectionType#getCode()
     */
    @NotNull(message = "人群选择方式不能为空")
    private Integer merchantSelectionType;

    /**
     * 门店ID列表
     */
    private List<Long> mIdList;

    /**
     * 导入excel文件的oss key
     */
    private String ossKey;

    /**
     * item类型 0 - sku; 1 - 品类; 2 - 其它(自定义)
     * 同一个job只能有一个类型的item type,也就是说,一个任务不可能混合多种item类型.
     */
    @NotNull(message = "item类型不能为空")
    @Range(min = 0, max = 2, message = "item类型不正确")
    private Integer itemType;

    /**
     * 任务item列表
     * 如果不为空,说明为全局item模式，即,所有门店使用相同的item列表，在数据库中merchantId存储为-1
     */
    private List<String> itemList;

    /**
     * 给每个门店创建的item列表
     */
    private Map<Long, List<String>> merchantItemMap;

    /**
     * 门店ID, 任务门店标签
     */
    private Map<Long, CrmJobEnum.JobMerchantLabel> jobMerchantLabelMap;
}
