package net.summerfarm.crm.model.dto.crmjob;

import lombok.Data;
import net.summerfarm.crm.enums.CrmJobEnum;

import javax.validation.constraints.NotNull;

@Data
public class JobCompletionCriteriaDTO {

    /**
     * @see CrmJobEnum.CompletionCriteriaType#getCode()
     */
    @NotNull(message = "任务完成条件判定类型不能为空")
    private Integer completionType;

    @NotNull(message = "任务完成条件判定值不能为空")
    private String completionValue;
}
