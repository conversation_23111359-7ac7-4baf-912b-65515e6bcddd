package net.summerfarm.crm.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description 活动黑名单
 * @date 2023/6/19 18:49:29
 */
@Data
public class CouponBlackAndWhiteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String weight;

    /**
     * 商品类型
     */
    private Integer extType;

    /**
     * 类型 1-黑名单  2-白名单
     */
    private Integer type;

    /**
     * 卡劵ID
     */
    private Long couponId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * create time
     */
    private LocalDateTime createTime;
}
