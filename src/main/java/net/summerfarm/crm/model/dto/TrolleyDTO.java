package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.mall.client.resp.ShoppingCartResp;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/2 10:59
 */
@Data
public class TrolleyDTO {

    /**
     * 规格
     */
    private String weight;

    /**
     * 加购数量
     */
    private Integer quantity;

    /**
     * 商品名称
     */
    private String pdName;
    /**
     * sku
     */
    private String sku;

    public static TrolleyDTO toTrolley(ShoppingCartResp shoppingCartResp){
        if (shoppingCartResp == null) {
            return null;
        }
        TrolleyDTO trolleyDTO = new TrolleyDTO();
        trolleyDTO.setWeight(shoppingCartResp.getWeight());
        trolleyDTO.setQuantity(shoppingCartResp.getQuantity());
        trolleyDTO.setPdName(shoppingCartResp.getPdName());
        trolleyDTO.setSku(shoppingCartResp.getSku());
        return trolleyDTO;
    }

}
