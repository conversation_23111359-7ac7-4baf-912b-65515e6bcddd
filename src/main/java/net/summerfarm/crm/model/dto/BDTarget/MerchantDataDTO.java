package net.summerfarm.crm.model.dto.BDTarget;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 门店数据DTO
 * 用于封装门店的各种业务数据，支持AI推荐逻辑
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class MerchantDataDTO {

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 门店名称
     */
    private String merchantName;

    /**
     * 注册时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date registerTime;

    /**
     * 最近登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 是否为新客户（历史未下过订单）
     */
    private Boolean isNewCustomer;

    /**
     * 历史拜访记录列表（最近10次）
     */
    private List<VisitRecordDTO> visitRecords;

    /**
     * 历史订单记录列表（最近10笔）
     */
    private List<OrderRecordDTO> orderRecords;

    /**
     * 客诉记录列表（最近10笔订单的客诉）
     */
    private List<ComplaintRecordDTO> complaintRecords;

    /**
     * 每日目标详情列表
     */
    private List<BdDailyTargetDetailDTO> dailyTargetDetails;

    @Data
    public static class  BdDailyTargetDetailDTO {

        /**
         * 主键id
         */
        private Long id;

        /**
         * 销售每日目标id
         */
        private Long bdDailyTargetId;

        /**
         * 优先级, 1,2,3,4,5
         */
        private Integer priority;

        /**
         * 目标类型，1：拉新客户数，2：月活客户数，3：高价值客户数，4：平台总目标，5：品类目标，6：sku目标，7：spu目标
         */
        private Integer targetType;

        /**
         * 目标名称
         */
        private String targetName;

        /**
         * 目标日期
         */
        private LocalDate targetDate;

        /**
         * 业务类型
         */
        private Integer businessType;

        /**
         * 指标类型，1：GMV，2：客户数，3：件数
         */
        private Integer indicatorType;

        /**
         * 品类名称
         */
        private String categoryName;

        /**
         * SKU编码
         */
        private String sku;

        /**
         * SKU名称
         */
        private String skuName;

        /**
         * SPU编码
         */
        private String spu;

        /**
         * SPU名称
         */
        private String spuName;

        /**
         * 指标期望值
         */
        private BigDecimal indicatorExpectedValue;

        /**
         * 指标状态，0：未完成，1：已完成
         */
        private Integer indicatorStatus;

        /**
         * 指标当前值
         */
        private BigDecimal indicatorCurrentValue;
    }

    /**
     * 拜访记录DTO
     */
    @Data
    public static class VisitRecordDTO {
        /**
         * 拜访人
         */
        private String adminName;

        /**
         * 跟进方式
         */
        private String followUpWay;

        /**
         * 跟进情况描述
         */
        private String condition;

        /**
         * 拜访目的
         */
        private String visitObjective;

        /**
         * 拜访时间
         */
        private LocalDateTime visitTime;
    }

    /**
     * 订单记录DTO
     */
    @Data
    public static class OrderRecordDTO {
        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 下单时间
         */
        private LocalDateTime orderTime;

        /**
         * 订单类型描述
         */
        private String orderTypeDesc;

        /**
         * 订单状态描述
         */
        private String orderStatusDesc;

        /**
         * 订单总价
         */
        private String totalPrice;

        /**
         * 商品SKU
         */
        private String sku;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 购买数量
         */
        private Integer amount;

        /**
         * 商品价格
         */
        private String price;

        /**
         * 商品原价
         */
        private String originalPrice;

        /**
         * 订单项状态描述
         */
        private String orderItemStatusDesc;
    }

    /**
     * 客诉记录DTO
     */
    @Data
    public static class ComplaintRecordDTO {
        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 售后单号
         */
        private String afterSaleOrderNo;

        /**
         * 商品SKU
         */
        private String sku;

        /**
         * 售后状态描述
         */
        private String statusDesc;

        /**
         * 处理方式
         */
        private String handleType;

        /**
         * 处理方式描述
         */
        private String handleTypeDesc;

        /**
         * 退款类型
         */
        private String refundType;

        /**
         * 售后备注
         */
        private String afterSaleRemark;

        /**
         * 售后类型
         */
        private String afterSaleType;

        /**
         * 处理二级备注
         */
        private String handleSecondaryRemark;
    }
}