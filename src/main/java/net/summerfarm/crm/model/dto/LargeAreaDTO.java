package net.summerfarm.crm.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import net.summerfarm.pojo.DO.Area;

import java.util.List;

/**
 * <AUTHOR>
 * @describe
 * @create 2022-08-28 22:16
 */
@Data
@NoArgsConstructor
public class LargeAreaDTO {

    /**
     * 运营大区标号
     */
    private Integer largeAreaNo;

    /**
     * 运营大区名称
     */
    private String largeAreaName;

    /**
     * 开放状态
     */
    private Integer status;

    /**
     * 负责人
     */
    private Integer manageAdminId;

    /**
     * 下属行政区域
     */
    private List<Area> areaList;

    public LargeAreaDTO(Integer largeAreaNo, String largeAreaName, List<Area> areaList) {
        this.largeAreaNo = largeAreaNo;
        this.largeAreaName = largeAreaName;
        this.areaList = areaList;
    }
}
