package net.summerfarm.crm.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderDeliveryListDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 客户名称
     */
    private String mName;

    /**
     * 客户ID
     */
    private Long mId;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 配送状态
     */
    private Integer deliveryStatus;

    /**
     * 缺货数量
     */
    private Integer outStockQuantity;

    /**
     * 地址ID
     */
    private Long contactId;

    /**
     * 取消类型，0普通，1拦截
     */
    private Integer cancelType;

}
