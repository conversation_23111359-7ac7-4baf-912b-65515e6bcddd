package net.summerfarm.crm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.contexts.RegConstant;
import net.summerfarm.crm.model.domain.FollowUpRelation;

import javax.validation.constraints.Pattern;

/**
 * <AUTHOR> ct
 * create at:  2019/8/9  12:19 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FollowUpRelationDTO extends FollowUpRelation {

    /**
     * BD名称
     */
    private String realname;

    /**
     * bd手机号
     */
    private String phone;
}
