package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Del;
import net.summerfarm.crm.model.domain.CoreProductBasePrice;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/7 18:04
 */
@Data
public class BasePriceDTO {
    @NotNull(groups = {Del.class}, message = "id不能为空")
    private Integer id;

    @Valid
    private List<CoreProductBasePrice> basePrice;
}
