package net.summerfarm.crm.model.dto.crmjobv2;

import lombok.Data;
import net.summerfarm.crm.enums.CrmJobEnum;
import org.hibernate.validator.constraints.Range;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;

/**
 * 更新任务V2DTO - 支持新的item表结构
 * 支持部分更新，某些字段可以为null表示不更新
 */
@Data
public class UpdateJobV2DTO {

    @NotNull(message = "任务ID不能为空")
    private Long jobId;

    /**
     * 任务名称 - 可选更新
     */
    private String jobName;

    /**
     * 任务描述 - 可选更新
     */
    private String description;

    /**
     * 任务结束时间 - 可选更新
     */
    private LocalDateTime endTime;

    /**
     * 门店ID列表 - 可选更新, 只能新增不能删除
     */
    private List<Long> mIdList;

    /**
     * 导入excel文件的oss key
     */
    private String ossKey;

    /**
     * item类型 0 - sku; 1 - 品类; 2 - 其它(自定义)
     * 同一个job只能有一个类型的item type,也就是说,一个任务不可能混合多种item类型.
     */
    @NotNull(message = "item类型不能为空")
    @Range(min = 0, max = 2, message = "item类型不正确")
    private Integer itemType;

    /**
     * 任务item列表
     * 如果不为空,说明为全局item模式，即,所有门店使用相同的item列表，在数据库中merchantId存储为-1
     */
    private List<String> itemList;

    /**
     * 门店ID, item列表
     */
    private Map<Long, List<String>> merchantItemMap;

    /**
     * 门店ID, 任务门店标签
     */
    private Map<Long, CrmJobEnum.JobMerchantLabel> jobMerchantLabelMap;
}
