package net.summerfarm.crm.model.dto.areaConfig;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.model.domain.CrmSalesCity;
import net.summerfarm.crm.model.vo.BdSalesCityVo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/22 14:19
 */
@Data
public class BdSaleAreaUpsertDTO {
    /**
     * 销售区域名称
     */
    @NotBlank(message = "销售区域名称不能为空", groups = {Update.class, Add.class})
    private String salesAreaName;
    /**
     * 销售org id
     */
    @NotNull(message = "销售org id不能为空", groups = {Update.class, Add.class})
    private Integer bdOrgId;
    /**
     * 销售区域id
     */
    @NotNull(message = "销售区域id不能为空", groups = {Update.class})
    private Integer salesAreaId;
    /**
     * 销售负责城市列表
     */
    @NotEmpty(message = "销售负责城市列表不能为空", groups = {Update.class, Add.class})
    private List<CrmSalesCity> bdSalesCityList;
}
