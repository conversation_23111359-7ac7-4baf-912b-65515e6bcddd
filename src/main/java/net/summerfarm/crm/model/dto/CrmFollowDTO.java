package net.summerfarm.crm.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description crm_clue_follow
 * @date 2023-05-17
 */
@Data
public class CrmFollowDTO  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id 自增
     */
    private Long id;

    /**
     * 主题id 也可以看作是线索id
     */
    private Long subjectId;

    /**
     * bd id
     */
    private Integer bdId;

    /**
     * 跟进时间
     */
    private LocalDateTime followTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 跟进目的
     */
    private String followGoal;

    /**
     * 客户反馈
     */
    private String customerFeedback;

    /**
     * 下次跟进
     */
    private String nextFollow;

    /**
     * 图片
     */
    private String images;

    /**
     * 图片地址集合
     */
    private List<String> imageList;

    private String bdName;
    /**
     * 跟进方式
     */
    private String followModel;

    public CrmFollowDTO() {}
}