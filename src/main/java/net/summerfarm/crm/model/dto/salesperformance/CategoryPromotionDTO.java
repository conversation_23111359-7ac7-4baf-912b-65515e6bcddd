package net.summerfarm.crm.model.dto.salesperformance;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 品类推广DTO - 用于数据库查询结果
 */
@Data
public class CategoryPromotionDTO {

    /**
     * 销售ID
     */
    private Long bdId;

    /**
     * 销售名称
     */
    private String bdName;

    /**
     * 客户ID
     */
    private Long custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 商品分组
     */
    private String spuGroup;

    /**
     * 奖励金额
     */
    private BigDecimal rewardAmount;

    /**
     * 履约件数 (is_dlv_payment = 1)
     */
    private Double fulfillmentCount;

    /**
     * 交易件数 (is_dlv_payment = 0)
     */
    private Double transactionCount;

    /**
     * 客户数
     */
    private Integer customerCount;

    /**
     * 单件奖励
     */
    private BigDecimal rewardPerItem;
}
