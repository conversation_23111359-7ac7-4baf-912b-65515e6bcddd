package net.summerfarm.crm.model.dto.crmjobv2;

import lombok.AllArgsConstructor;
import lombok.Data;
import net.summerfarm.crm.enums.CrmJobEnum;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
public class CreateJobMerchantDetailV2DTO {

    private List<Long> mIdList;

    private Map<Long, List<String>> merchantItemMap;

    private String ossKey;

    /**
     * item类型 0 - sku; 1 - 品类; 2 - 其它(自定义)
     * 同一个job只能有一个类型的item type,也就是说,一个任务不可能混合多种item类型.
     */
    private Integer itemType;

    /**
     * 门店ID, 任务门店标签
     */
    private Map<Long, CrmJobEnum.JobMerchantLabel> jobMerchantLabelMap;
}
