package net.summerfarm.crm.model.dto.crmCouponExpense;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CrmCouponExpensePoolExtDto implements Serializable {
    /**
     * sku
     */
    private String sku;
    /**
     * 商品名称
     */
    private String pdName;
    /**
     * 种类名称
     */
    private String categoryName;
    /**
     * 规格
     */
    private String pdWeight;

    private String objKey;

    private String productName;

    private String weight;

    private Integer extType;
}
