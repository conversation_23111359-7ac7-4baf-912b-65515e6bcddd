package net.summerfarm.crm.model.dto.crmCouponExpense;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CrmCouponExpensePoolLogDto implements Serializable {
    /**
     * 划分的人名称
     * */
    private String adminName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 变动的钱
     */
    private BigDecimal changeExpense;
    /**
     * 类型
     */
    private String type;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 自动审批状态 0关闭 1开启
     */
    private Integer autoApprove;
    /**
     * 0 不限 1按照品类 2按照sku
     */
    private Integer productRange;
    /**
     * 费比限制
     */
    private Integer costLimit;
    /**
     * 池子名称
     */
    private String poolName;

    /**
     * 池子id
     */
    private Long poolId;

    /**
     * 有效期 开始
     */
    private LocalDateTime startDate;

    /**
     * 有效期 结束
     */
    private LocalDateTime endDate;

    private String createName;

    private Integer changeCreateId;
}
