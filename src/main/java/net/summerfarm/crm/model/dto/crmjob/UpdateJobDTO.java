package net.summerfarm.crm.model.dto.crmjob;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class UpdateJobDTO {

    @NotNull(message = "任务id不能为空")
    private Long jobId;

    private String jobName;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    private List<Long> mIdList;

    /**
     * excel的oss key
     */
    private String ossKey;
}
