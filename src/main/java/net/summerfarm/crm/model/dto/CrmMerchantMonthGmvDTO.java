package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.crm.model.vo.BrowsingHistoryVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @describe
 * @date 2022/11/3 10:40
 */
@Data
public class CrmMerchantMonthGmvDTO {

    /**
     * 上月gmv
     */
    private BigDecimal lastMonthGmv;
    /**
     * 本月gmv
     */
    private BigDecimal thisMonthGmv;
    /**
     * 上月客单价
     */
    private BigDecimal lastMonthDeliveryUnitPrice;
    /**
     * 本月客单价
     */
    private BigDecimal thisMonthDeliveryUnitPrice;
    /**
     * 上月配送gmv
     */
    private BigDecimal lastDistributionGmv;
    /**
     * 本月配送gmv
     */
    private BigDecimal thisDistributionGmv;
    /**
     * 本月配送次数
     */
    private Integer distributionAmount;
    /**
     *  是否存在标签
     */
    private Integer coreMerchantTag;
    /**
     * 核心客户定义之本月gmv阈值
     */
    private Integer gmvThreshold;
    /**
     * 核心客户定义之本月配送客单价阈值
     */
    private Integer priceThreshold;

    /**
     * 30天订购spu
     */
    private Integer thirtyDaysOrderSpu;

    /**
     * 30-60天订购spu
     */
    private Integer thirtySixtyDaysOrderSpu;

    /**
     * 七天水果GMV
     */
    private BigDecimal sevenDaysFruitGmv;

    /**
     * 7天乳制品GMV
     */
    private BigDecimal sevenDaysDairyGmv;

    /**
     * 七天自营品牌GMV
     */
    private BigDecimal sevenDaysBrandGmv;

    /**
     * 30天水果GMV
     */
    private BigDecimal thirtyDaysFruitGmv;

    /**
     * 30天乳制品GMV
     */
    private BigDecimal thirtyDaysDairyGmv;

    /**
     * 30天自营品牌GMV
     */
    private BigDecimal thirtyDaysBrandGmv;

    /**
     * 浏览历史记录
     */
    private List<BrowsingHistoryVo> browsingHistory;
}
