package net.summerfarm.crm.model.dto.crmjob;

import lombok.Data;
import net.summerfarm.crm.enums.CrmJobEnum;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建任务DTO
 */
@Data
public class CreateJobDTO {

    @NotNull(message = "任务名称不能为空")
    private String jobName;

    private String description;

    /**
     * @see CrmJobEnum.Type#getCode()
     */
    @NotNull(message = "任务类型不能为空")
    private Integer type;

    @NotNull(message = "任务开始时间不能为空")
    private LocalDateTime startTime;

    @NotNull(message = "任务结束时间不能为空")
    private LocalDateTime endTime;

    @NotNull(message = "任务完成条件判定不能为空")
    private List<JobCompletionCriteriaDTO> completionCriteriaList;

    /**
     * @see CrmJobEnum.MerchantSelectionType#getCode()
     */
    @NotNull(message = "人群选择方式不能为空")
    private Integer merchantSelectionType;

    /**
     * 任务品类列表
     * 是个二维数组
     */
    private List<List<Long>> categoryList;

    private List<Long> mIdList;

    /**
     * 导入excel文件的oss key
     */
    private String ossKey;
}
