package net.summerfarm.crm.model.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class OrderDeliveryDetailDTO {

    /**
     * 城配仓名称
     */
    private String storeName;

    /**
     * 库存仓名称
     */
    private Set<String> warehouseNames;

    /**
     * 司机名称
     */
    private String driver;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 配送状态，10：未签收，20：已签收，30：签收异常
     */
    private Integer status;

    /**
     * 签收照片
     */
    private String signInPic;

    /**
     * 签收备注
     */
    private String signInRemark;

    /**
     * 完成配送时间
     */
    private LocalDateTime finishTime;

    /**
     * 缺货商品信息
     */
    List<OutStockSkuInfo> outStockSkuInfoList;

}
