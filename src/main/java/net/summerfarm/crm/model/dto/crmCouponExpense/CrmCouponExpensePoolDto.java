package net.summerfarm.crm.model.dto.crmCouponExpense;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CrmCouponExpensePoolDto implements Serializable {
    /**
     * 费用名称
     * */
    private String name;
    /**
     * 创建人
     */
    private String adminName;
    /**
     * 状态 0正常 1失效
     */
    private Byte status;

    /**
     * 有效期 开始
     */
    private LocalDateTime startDate;

    /**
     * 有效期 结束
     */
    private LocalDateTime endDate;
    /**
     * 自动审批状态 0关闭 1开启
     */
    private Byte autoApprove;
    /**
     * 0 不限 1按照品类 2按照sku
     */
    private Byte productRange;
    /**
     * 费比限制
     */
    private Integer costLimit;
    /**
     * 池子id
     */
    private Long poolId;
    /**
     * 总金额
     */
    private BigDecimal totalAmount;
    /**
     * 余额
     */
    private BigDecimal remainingAmount;
    /**
     * 限制
     */
    private List<String> limits;
}
