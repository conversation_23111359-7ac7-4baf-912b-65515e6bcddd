package net.summerfarm.crm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 */
@Data
public class SubAccountDTO {
    /**
     * zi账号 id
     */
    private Long accountId;
    /**
     * 店铺id
     */
    private Long mid;
    /**
     * 联系人
     */
    private String contact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型 0、母账号 1、子账号
     */
    private Integer type;
    /**
     * 上次登陆时间
     */
    private LocalDateTime lastLoginTime;
}
