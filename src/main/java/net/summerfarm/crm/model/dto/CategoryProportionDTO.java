package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Update;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/3/8 10:36
 */
@Data
public class CategoryProportionDTO {
    /**
     * 额度比例
     */
    @NotNull(groups = {Add.class},message = "比例不能为空")
    @DecimalMin(groups = {Add.class},value = "0",message = "底价范围不正确")
    @DecimalMax(groups = {Add.class},value = "100",message = "底价范围不正确")
    private BigDecimal basePriceProportion;

    /**
     * 额度比例
     */
    @NotNull(groups = {Update.class},message = "品类拓宽额度比例不能为空")
    @DecimalMin(groups = {Update.class},value = "0",message = "品类拓宽额度比例范围不正确")
    @DecimalMax(groups = {Update.class},value = "500",message = "品类拓宽额度比例范围不正确")
    private BigDecimal rewardProportion;
}
