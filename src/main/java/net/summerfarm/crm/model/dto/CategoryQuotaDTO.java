package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.common.util.validation.groups.Del;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.enums.CategoryQuotaEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/3/7 15:04
 */
@Data
public class CategoryQuotaDTO implements Serializable {
    /**
     * 配额
     */
    @NotNull(groups = {Add.class, Update.class}, message = "额度不能为空")
    private BigDecimal quota;
    /**
     * 备注
     */
    private String remark;
    /**
     * bd id
     */
    @NotNull(groups = {Add.class, Update.class, Del.class}, message = "adminId不能为空")
    private Integer adminId;

    @NotBlank(groups = {Add.class, Update.class}, message = "adminName不能为空")
    private String adminName;

    /**
     * 操作类型: 0设置;1:划分;2:发券;3:返还;4:品类拓宽;5:新客费比;6:老客费比
     */
    private Integer type;

    /**
     * 额度类型: 0:品类券;1:月活券;2:品类券-客情
     */
    private Integer quotaType = CategoryQuotaEnum.QuotaType.CATEGORY.getCode();
}
