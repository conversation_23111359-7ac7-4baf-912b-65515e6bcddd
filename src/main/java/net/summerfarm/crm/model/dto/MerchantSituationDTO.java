package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.validation.annotation.InRange;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.crm.enums.ProductCategoryTypeEnum;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/18 14:46
 */
@Data
public class MerchantSituationDTO {

    /**
     * 申请ID
     */
    private Long id;

    /**
     * 状态  0 待审核 1已审核(待审批) 2 审批通过 3 申请未通过(关闭)
     */
    @InRange(groups = {Add.class}, rangeNums = {0, 1, 2, 3, 4}, message = "无效的审核状态参数")
    private Integer status;

    /**
     * 券金额
     */
    @NotNull(groups = {Add.class}, message = "请填写券金额")
    private BigDecimal couponAmount;

    /**
     * 券门槛
     */
    @NotNull(groups = {Add.class}, message = "请填写券门槛")
    private BigDecimal threshold;

    /**
     * 用户id
     */
    @NotNull(groups = {Add.class}, message = "请选择用户")
    private Long merchantId;

    /**
     * 提交类型:0客情,1月活,2品类券-价格补贴，3品类券-品类拓宽
     */
    @InRange(groups = {Add.class}, rangeNums = {0, 1, 2, 3}, message = "无效的申请状态参数")
    private Integer monthLivingCoupon;

    /**
     * 申请单备注
     */
    private String situationRemake;

    /**
     * 所属bd 品类券-价格补贴为m1 id
     */
    private Integer adminId;

    /**
     * 所属bd名称 品类券-价格补贴为m1名称
     */
    private String adminName;

    /**
     * 2:普通订单,4:仅省心送
     */
    private Integer activityScope;

    /**
     * sku
     */
    private String sku;

    /**
     * 卡券id
     */
    private Integer couponId;
    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 审核人ID
     */
    private Integer examineId;

    /**
     * 审核时间
     */
    private LocalDateTime examineTime;

    /**
     * 审核人名称
     */
    private String examineName;

    /**
     * 商品阶梯价字符串
     */
    private String ladderPrice;

    /**
     * 商品售价
     */
    private BigDecimal salePrice;

    /**
     * 用券后单价
     */
    private String afterCouponPriceStr;

    /**
     * 下单数量
     */
    private Integer orderQuantity;

    /**
     * 客情申请类型:0客情,1月活,2品类
     */
    private Integer situationType;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = BaseDateUtils.LONG_DATE_FORMAT)
    private LocalDateTime updateTime;
    /**
     * 钉钉审批附件
     */
    private String attachedImage;

    /**
     * 飞书图片 code
     */
    private String feishuImgCode;

    /**
     * 大区编号
     */
    private Integer areaNo;

    /**
     * 卡劵黑白名单
     */
    private List<CouponBlackAndWhiteDTO> couponBlackAndWhiteDTOS;
    /**
     * 余额池id
     */
    private Long poolId;

    private String poolName;

    private Boolean autoApprove;

    private BigDecimal basePrice;

    private BigDecimal amount;

    /**
     * 品类类型
     * @see ProductCategoryTypeEnum#getCode()
     */
    private Integer categoryType;
}
