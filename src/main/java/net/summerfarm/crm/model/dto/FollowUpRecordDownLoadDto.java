package net.summerfarm.crm.model.dto;

import lombok.Data;

@Data
public class FollowUpRecordDownLoadDto {
    private String areaName;

    private String mname;

    private String adminName;

    private String followUpWay;

    private String addTimeStr;

    private String visitObjectiveStr;

    private String escortAdminName;

    private String kpName;

    private String businessMsg;

    private String useProductMsg;

    private String ordersMsg;

    private String CIMsg;

    private String afterSaleMsg;

    private String sendSaleMsg;

    private String lostCustomerMsg;

    private String others;

    private String location;

    private String m1;

    private String m2;

    private Integer escortAdminId;

    private Long kpId;

    private Integer adminId;

    private Long merchantId;

    private String visitAll;

    private Long escortVisitPlanId;

    private Long visitPlanId;

}
