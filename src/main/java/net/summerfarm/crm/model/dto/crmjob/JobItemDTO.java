package net.summerfarm.crm.model.dto.crmjob;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 任务item DTO
 * 用于创建和更新任务时指定item信息
 */
@Data
public class JobItemDTO {

    /**
     * item内容，比如SKU ID、品类ID等
     */
    @NotNull(message = "item不能为空")
    private String item;

    /**
     * item类型 0 - sku; 1 - 品类; 2 - 其它(自定义)
     */
    @NotNull(message = "item类型不能为空")
    private Integer itemType;
}
