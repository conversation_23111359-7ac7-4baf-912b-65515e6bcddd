package net.summerfarm.crm.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.crm.model.domain.VisitPlan;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/15 14:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class VisitPlanDTO extends VisitPlan {

    /**
     * 陪访人id
     */
    private List<Long> escortAdminIdList;

    /**
     * 更新人
     */
    private String updater;



}
