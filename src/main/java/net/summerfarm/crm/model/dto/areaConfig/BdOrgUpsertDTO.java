package net.summerfarm.crm.model.dto.areaConfig;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/22 14:08
 */
@Data
public class BdOrgUpsertDTO {
    /**
     * m3 id
     */
    @NotNull(message = "部门主管id不能为空")
    private Integer departmentAdminId;
    @NotNull(message = "当前部门主管id不能为空")
    private Integer currentDepartmentAdminId;
    /**
     * m2 id
     */
    @NotNull(message = "区域主管id不能为空")
    private Integer manageAdminId;
    @NotNull(message = "当前区域主管id不能为空")
    private Integer currentManageAdminId;
    /**
     * m1 id
     */
    @NotNull(message = "城市主管id不能为空")
    private Integer cityAdminId;
    @NotNull(message = "当前城市主管id不能为空")
    private Integer currentCityAdminId;

    /**
     * 销售区域id
     */
    private Integer salesAreaId;

    /**
     * 销售区域id
     */
    private List<Integer> salesAreaIdList;

    /**
     * 销售列表
     */
    private List<BdOrgDTO> bdOrgList;
}
