package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import net.summerfarm.crm.model.dto.BDTarget.MerchantDataDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 推荐上下文DTO
 * 用于封装多目标推荐所需的所有上下文信息
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class RecommendContextDTO {

    /**
     * 门店ID
     */
    private Long mId;

    /**
     * 门店数据
     */
    private MerchantDataDTO merchantData;

    /**
     * 新客户推荐上下文
     */
    private NewCustomerRecommendContext newCustomerContext;

    /**
     * 老客户推荐上下文
     */
    private OldCustomerRecommendContext oldCustomerContext;

    /**
     * 商品限定推荐上下文
     */
    private ProductLimitedRecommendContext productLimitedContext;

    /**
     * 新客户推荐上下文
     */
    @Data
    public static class NewCustomerRecommendContext {
        /**
         * 门店类型
         */
        private String merchantType;

        /**
         * 门店类型排行榜商品列表
         */
        private List<ProductRankingDTO> typeRankingProducts;
    }

    /**
     * 老客户推荐上下文
     */
    @Data
    public static class OldCustomerRecommendContext {
        /**
         * 门店常购商品列表
         */
        private List<ProductPurchaseDTO> frequentProducts;
    }

    /**
     * 商品限定推荐上下文
     */
    @Data
    public static class ProductLimitedRecommendContext {
        /**
         * 限定范围类型（品类/SKU/SPU）
         */
        private String limitType;

        /**
         * 限定范围值
         */
        private String limitValue;

        /**
         * 限定范围值描述
         */
        private String limitValueDesc;

        /**
         * 限定范围内的商品列表
         */
        private List<ProductInRangeDTO> productsInRange;
    }

    /**
     * 商品排行DTO
     */
    @Data
    public static class ProductRankingDTO {
        /**
         * 商品SKU
         */
        private String sku;

        /**
         * 商品名称
         */
        private String productName;

        /** 规格 **/
        private String specification;

        /**
         * 排行榜排名
         */
        private Integer ranking;

        /**
         * 销量
         */
        private Integer salesVolume;

        /**
         * 销售额
         */
        private String salesAmount;
    }

    /**
     * 商品购买DTO
     */
    @Data
    public static class ProductPurchaseDTO {
        /**
         * 商品SKU
         */
        private String sku;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 品类名称
         */
        private String categoryName;

        /**
         * 购买次数
         */
        private Integer purchaseCount;

        /**
         * 最近购买时间
         */
        private String lastPurchaseTime;

        /**
         * 累计购买金额
         */
        private String totalAmount;
    }

    /**
     * 购买偏好DTO
     */
    @Data
    public static class PurchasePreferenceDTO {
        /**
         * 偏好品类列表
         */
        private List<String> preferredCategories;

        /**
         * 平均订单金额
         */
        private String avgOrderAmount;

        /**
         * 购买频率（天）
         */
        private Integer purchaseFrequency;

        /**
         * 价格敏感度（高/中/低）
         */
        private String priceSensitivity;
    }

    /**
     * 限定范围内商品DTO
     */
    @Data
    public static class ProductInRangeDTO {
        /**
         * 商品SKU
         */
        private String sku;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品规格
         */
        private String skuSpecification;
        /**
         * 购买次数
         */
        private Integer purchaseCount;

        /**
         * 最后购买时间
         */
        private LocalDate lastPurchaseTime;

        /**
         * 购买金额
         */
        private BigDecimal totalAmount;
    }
}