package net.summerfarm.crm.model.dto;

import lombok.Data;
import net.summerfarm.crm.model.domain.SampleSku;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/9 14:15
 */
@Data
public class SampleApplyReviewDTO {

    /**
     * '试样用户id'
     */
    private Long mId;
    /**
     * 样品申请id
     */
    private Integer sampleId;

    /**
     * '状态 0 待反馈 1 已反馈 2-取消 3-待审核、4-已关闭'
     */
    private Integer status;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 仓库编号
     */
    private Integer storeNo;

    /**
     * '试样用户收货地址id'
     */
    private Integer contactId;

    /**
     * 样品申请sku详情
     */
    List<SampleSku> sampleSkuList;

}
