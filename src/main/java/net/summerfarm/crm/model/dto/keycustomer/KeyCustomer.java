package net.summerfarm.crm.model.dto.keycustomer;

import lombok.Data;
import net.summerfarm.crm.enums.FollowUpRelationEnum;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class KeyCustomer {

    /**
     *  商户id
     */
    private Long mId;

    /**
     * 商户名称
     */
    private String mname;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 最后下单时间
     */
    private LocalDateTime lastOrderTime;

    /**
     * 跟进人id
     */
    private Long bdId;

    /**
     * 跟进者
     */
    private String bdName;

    /**
     * 客户类型(主营类型)
     */
    private String merchantType;

    /**
     * 掉落倒计时
     */
    private Integer dangerDay;

    /**
     * 掉落时间
     */
    private LocalDateTime releaseTime;

    /**
     * 掉落保护原因
     */
    private String protectReason;

    /**
     * 掉落规则(原因)
     * @see FollowUpRelationEnum.DangerDayRule
     */
    private String dangerDayReason;

    /**
     * 当前私海销售的最后拜访时间
     */
    private Date lastVisitTimeByCurrentBd;

    /**
     * 备注(便签)
     */
    private String remark;
}
