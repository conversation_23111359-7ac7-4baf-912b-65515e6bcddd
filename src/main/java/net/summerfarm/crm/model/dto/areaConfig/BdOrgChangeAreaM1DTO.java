package net.summerfarm.crm.model.dto.areaConfig;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BdOrgChangeAreaM1DTO {

    /**
     * 销售区域id
     */
    @NotNull(message = "销售区域id不能为空")
    private Integer salesAreaId;

    @NotNull(message = "新城市主管id不能为空")
    private Integer newCityAdminId;

    /**
     * 需要迁移至新m1的下属bd的adminId列表
     * 只有在原m1负责多个城市的情况下才需要
     * 如果原m1只负责一个城市,原下属都会转移到新m1
     */
    private List<Long> newAdminTeamMemberAdminIdList;
}
