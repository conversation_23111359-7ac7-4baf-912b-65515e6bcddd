package net.summerfarm.crm.model.dto;

import lombok.Data;

/**
 * SKU销售排行榜数据传输对象
 * 对应offline数据源中的sku_sales_ranking_list表
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class SkuSalesRankingListDTO {

    /**
     * 日期标签，格式：yyyyMMdd
     */
    private String dayTag;

    /**
     * 门店类型
     */
    private String merchantType;

    /**
     * SKU列表，逗号分隔的SKU编码字符串
     * 例如：N001S01R005,N001S01R002,607164503701,607330063305
     */
    private String skuList;
}