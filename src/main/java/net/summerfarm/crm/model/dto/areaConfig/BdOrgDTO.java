package net.summerfarm.crm.model.dto.areaConfig;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/8/31 11:47
 */
@Data
public class BdOrgDTO {
    /**
     * m3 id
     */
    @NotNull(message = "销售adminid不能为空")
    private Integer bdAdminId;
    private Integer bdOrgId;
    @NotNull(message = "当前bd org id不能为空")
    private Integer currentBdOrgId;
    /**
     * m3 名字
     */
    @NotBlank(message = "销售名称不能为空")
    private String bdName;
    private Integer rank;
    /**
     * 销售区域id
     */
    private Integer saleAreaId;
}
