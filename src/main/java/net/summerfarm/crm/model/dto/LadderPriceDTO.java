package net.summerfarm.crm.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/26 15:42
 */
@Data
public class LadderPriceDTO {

    /**
     * 阶梯数
     */
    private Integer unit;

    /**
     * 阶梯价格
     */
    private BigDecimal price;

    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    private BigDecimal amount;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整
     */
    private Integer roundingMode;

}
