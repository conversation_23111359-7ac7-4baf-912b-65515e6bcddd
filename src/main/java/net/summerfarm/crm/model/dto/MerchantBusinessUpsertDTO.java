package net.summerfarm.crm.model.dto;

import lombok.Data;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 客户行业属性更新DTO
 *
 * <AUTHOR>
 */
@Data
public class MerchantBusinessUpsertDTO {

    /**
     * 客户id
     */
    @NotNull(message = "客户id不能为空")
    private Long mId;

    /**
     * 客户主业类型（仅有一个）
     */
    @NotBlank(message = "客户主业类型不能为空")
    private String mainBusinessType;

    /**
     * 客户副业类型：多个
     */
    @NotEmpty(message = "客户副业类型不能为空")
    private List<String> sideBusinessTypeList;

    /**
     * 客户连锁范围:0（NKA-全国连锁）1(LKA-区域连锁) 2(其他连锁) 3- 跨区域连锁 4- 工厂 99-单店
     */
    @NotNull(message = "客户连锁范围不能为空")
    private Integer merchantChainType;
}
