package net.summerfarm.crm.model.input.BDTarget;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 新增或更新销售每日目标的输入参数
 *
 * <AUTHOR>
 */
@Data
public class UpsertBdDailyTargetInput {

    /**
     * 销售id
     */
    @NotEmpty(message = "销售id不能为空")
    private Integer bdId;

    /**
     * 目标日期
     */
    @NotNull(message = "目标日期不能为空")
    private LocalDate targetDate;

    /**
     * 目标制定人id
     */
    private Integer targetCreator;

    /**
     * 目标制定人名称
     */
    private String targetCreatorName;

    /**
     * 新增或更新的销售每日目标明细列表
     */
    @Valid
    @NotEmpty(message = "新增或更新的销售每日目标明细列表不能为空")
    private List<UpsertBdDailyTargetDetailInput> upsertBdDailyTargetDetails;
}
