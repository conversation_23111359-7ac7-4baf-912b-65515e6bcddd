package net.summerfarm.crm.model.input.BDTarget;

import lombok.Data;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;

/**
 * 新增销售每日目标明细的输入参数
 *
 * <AUTHOR>
 */
@Data
public class AddBdDailyTargetDetailInput {

    /**
     * 目标类型，1：拉新客户数，2：月活客户数，3：高价值客户数，4：平台总目标，5：品类目标，6：sku目标，7：spu目标
     */
    @NotNull(message = "目标类型不能为空")
    private Integer targetType;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 指标优先级，1,2,3,4,5，越小越高
     */
    @NotNull(message = "指标优先级不能为空")
    private Integer priority;

    /**
     * 业务类型，0：交易，1：履约
     */
    private Integer businessType;

    /**
     * 指标类型，1：GMV，2：客户数，3：件数
     */
    private Integer indicatorType;

    /**
     * 品类名称，指标类型为品类目标时需要传入
     */
    private String categoryName;

    /**
     * sku编码，指标类型为sku目标时需要传入
     */
    private String sku;

    /**
     * spu编码，指标类型为spu目标时需要传入
     */
    private String spu;

    /**
     * 指标期望值
     */
    @NotNull(message = "指标期望值不能为空")
    private BigDecimal indicatorExpectedValue;

}
