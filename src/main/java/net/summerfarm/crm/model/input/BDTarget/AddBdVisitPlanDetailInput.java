package net.summerfarm.crm.model.input.BDTarget;

import lombok.Data;
import net.summerfarm.crm.model.vo.PoiVO;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 添加销售拜访计划详情
 *
 * <AUTHOR>
 */
@Data
public class AddBdVisitPlanDetailInput {

    /**
     * 门店id
     */
    @NotNull(message = "门店id不能为空")
    private Long mId;

    /**
     * 门店名称
     */
    private String mname;

    /**
     * 拜访类型，0：线下拜访，1：线上拜访
     */
    @NotNull(message = "拜访类型不能为空")
    private Integer visitType;

    /**
     * 门店poi
     */
    @NotNull(message = "poi位置信息不能为空")
    private PoiVO poi;

    /**
     * 联系人地址id
     */
    @NotNull(message = "联系人地址id不能为空")
    private Long contactId;

}
