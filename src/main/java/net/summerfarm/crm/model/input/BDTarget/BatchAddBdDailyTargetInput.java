package net.summerfarm.crm.model.input.BDTarget;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 批量新增销售每日目标的输入参数
 *
 * <AUTHOR>
 */
@Data
public class BatchAddBdDailyTargetInput {

    /**
     * 销售id列表
     */
    @NotEmpty(message = "销售id列表不能为空")
    private List<Integer> bdIds;

    /**
     * 目标起始日期
     */
    @NotNull(message = "目标起始日期不能为空")
    private LocalDate targetBeginDate;

    /**
     * 目标截止日期
     */
    @NotNull(message = "目标截止日期不能为空")
    private LocalDate targetEndDate;

    /**
     * 新增销售每日目标明细列表
     */
    @Valid
    @NotEmpty(message = "新增销售每日目标明细列表不能为空")
    private List<AddBdDailyTargetDetailInput> addBdDailyTargetDetails;
}
