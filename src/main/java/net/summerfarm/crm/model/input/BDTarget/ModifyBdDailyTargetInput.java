package net.summerfarm.crm.model.input.BDTarget;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 修改销售每日目标的输入参数
 *
 * <AUTHOR>
 */
@Data
public class ModifyBdDailyTargetInput {

    /**
     * 销售每日目标id
     */
    private Long bdDailyTargetId;

    /**
     * 替换的销售每日目标明细列表，该列表会替换掉原来所有的销售每日目标明细
     */
    @Valid
    @NotEmpty(message = "替换的销售每日目标明细列表不能为空")
    private List<ReplaceBdDailyTargetDetailInput> replaceBdDailyTargetDetails;
}
