package net.summerfarm.crm.model.input.BDTarget;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 添加销售拜访计划的输入参数
 *
 * <AUTHOR>
 */
@Data
public class AddBdVisitPlanInput {

    /**
     * 销售每日目标id
     */
    @NotNull(message = "销售每日目标id不能为空")
    private Long bdDailyTargetId;

    /**
     * 添加销售拜访计划详情列表
     */
    @Valid
    @NotEmpty(message = "添加销售拜访计划详情列表不能为空")
    private List<AddBdVisitPlanDetailInput> addBdVisitPlanDetails;


    /**
     * 销售ID
     */
    private Integer bdId;
}
