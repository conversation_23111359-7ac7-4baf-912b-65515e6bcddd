package net.summerfarm.crm.model.input.samples;

import lombok.Data;
import net.summerfarm.crm.model.domain.SampleSku;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/4/26  11:08
 */
@Data
public class SampleApplyBatchInsertInput {

    /**
     * '试样用户id'
     */
    private Long mId;

    /**
     * 运营区域
     */
    private Integer areaNo;

    /**
     * 'sku'
     */
    @NotNull(message = "sku不能为空")
    private String sku;

    /**
     * '商品名称'
     */
    private String pdName;

    /**
     *  '申请数量'
     */
    @NotNull(message = "申请数量不能为空")
    private Integer amount;

    /**
     * '规格'
     */
    private String weight;

    /**
     * 文件链接--Excel导入的文件链接（Excel方式为必填）
     */
    private String key;

    /**
     * 操作人ID
     */
    private Integer adminId;

    /**
     * 备注
     */
    private String remark;
}
