package net.summerfarm.crm.model.input.label;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class NewYearLabelInput {

    @NotNull(message = "门店id不能为空")
    private Long mId;

    @NotNull(message = "是否在新年期间营业不能为空")
    private Boolean openDuringNewYear;

    /**
     * 开业日期
     * 只取月日
     */
    @NotNull(message = "开业日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate openDate;
}
