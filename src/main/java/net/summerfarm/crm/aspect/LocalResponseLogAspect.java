package net.summerfarm.crm.aspect;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.log.helper.LogConfigHolder;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 打印所有 @RestController 或者 @Controller 公开方法的返回结果，如果返回结果的类型是 CommonResult或者AjaxResult
 */
@Configuration
@ConditionalOnProperty(name = "xm.log.resp", havingValue = "true")
@Aspect
@Slf4j
public class LocalResponseLogAspect implements InitializingBean {


    private static final String PHONE_FORMAT_REGEX = "(\\d{3})\\d{4}(\\d{4})";

    private static final String PHONE_FORMAT_REPLACEMENT = "$1****$2";

    @Pointcut(value = "@within(org.springframework.web.bind.annotation.RestController) || @within(org.springframework.stereotype.Controller)")
    public void pointcut() {
    }

    @AfterReturning(value = "pointcut()", returning = "result")
    public void afterReturning(Object result) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
            String requestURI = request.getRequestURI();
            if (null == result) {
                log.info("接口:{} 返回空对象", requestURI);
                return;
            }
            if (!LogConfigHolder.isBlackList(requestURI)) {
                if (!(result instanceof CommonResult) && !(result instanceof AjaxResult)) {
                    log.info("接口:{} 返回的数据类型既不是CommonResult，AjaxResult，返回类型：{}", requestURI, result.getClass().getName());
                } else {
                    //打印日志
                    UserBase user = UserInfoHolder.getUser();
                    log.info("接口:{} 登录信息:{} 返回数据: {}", requestURI, printLoginContext(user), JSON.toJSONString(result));
                }
            }
        } catch (Exception e) {
            log.error("接口响应日志打印异常,异常信息：{}", e.getMessage(), e);
        }
    }


    private static String printLoginContext(UserBase user) {
        if (Objects.isNull(user)) {
            return "<empty-login-context>";
        }
        return String.format("(userName:%s, phone:%s)",  user.getNickname(), formatPhone(user.getPhone()));
    }


    private static String formatPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return phone;
        }
        return phone.replaceAll(PHONE_FORMAT_REGEX, PHONE_FORMAT_REPLACEMENT);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("\n\n\nthis is running\n\n\n");
    }
}
