package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.repository.CrmBdCustTaskDetailRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantDetailRepository;
import net.summerfarm.crm.model.domain.CrmBdCustTaskDetail;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 更新crm任务完成状态
 */
@Component
@Slf4j
public class CrmJobCompletionStatusHandler extends XianMuJavaProcessorV2 {

    @Resource
    private CrmBdCustTaskDetailRepository crmBdCustTaskDetailRepository;
    @Resource
    private CrmJobMerchantDetailRepository crmJobMerchantDetailRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("[crm任务完成状态处理] 开始更新任务完成状态");

        long lastId = 0L;
        List<CrmBdCustTaskDetail> records;
        do {
            LambdaQueryWrapper<CrmBdCustTaskDetail> last = Wrappers.lambdaQuery(CrmBdCustTaskDetail.class)
                    .gt(CrmBdCustTaskDetail::getId, lastId) // 基于id分页
                    .orderByAsc(CrmBdCustTaskDetail::getId)
                    .last("limit 3000");
            records = crmBdCustTaskDetailRepository.list(last);
            if (CollectionUtil.isEmpty(records)) {
                break;
            }

            log.info("[crm任务完成状态处理] 更新任务完成状态, 当前处理id: {}", lastId);
            lastId = records.get(records.size() - 1).getId();
            List<LambdaUpdateWrapper<CrmJobMerchantDetail>> updateWrappers = records.stream().map(record ->
                    Wrappers.lambdaUpdate(CrmJobMerchantDetail.class)
                            .set(CrmJobMerchantDetail::getStatus, record.getCompletionStatus())
                            .set(CrmJobMerchantDetail::getRealTotalAmt, record.getJobRealTotalAmt())
                            .eq(CrmJobMerchantDetail::getJobId, record.getJobId())
                            .eq(CrmJobMerchantDetail::getMId, record.getCustId())).collect(Collectors.toList());
            crmJobMerchantDetailRepository.updateBatch(updateWrappers);
        } while (CollectionUtil.isNotEmpty(records));

        log.info("[crm任务完成状态处理] 更新任务完成状态完成");
        return new ProcessResult(true);
    }
}
