package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmJobConstant;
import net.summerfarm.crm.common.util.SplitUtils;
import net.summerfarm.crm.mapper.repository.HistoryCustCategoryPerformanceRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantDetailRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantItemRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.domain.CrmJobMerchantItem;
import net.summerfarm.crm.model.domain.HistoryCustCategoryPerformance;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 品类履约任务完成状态更新
 */
@Component
@Slf4j
public class CategoryFulfillmentJobStatusUpdateHandler extends XianMuJavaProcessorV2 {

    @Resource
    private CrmJobRepository crmJobRepository;
    @Resource
    private CrmJobMerchantDetailRepository crmJobMerchantDetailRepository;
    @Resource
    private CrmJobMerchantItemRepository crmJobMerchantItemRepository;
    @Resource
    private HistoryCustCategoryPerformanceRepository historyCustCategoryPerformanceRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 处理存量客户任务
        updateJobStatusByCustomerType("存量", CrmJobConstant.EXISTING_CUSTOMER_CATEGORY_JOB_POSTFIX);

        // 处理增量客户任务
        updateJobStatusByCustomerType("新增", CrmJobConstant.NEW_CUSTOMER_CATEGORY_JOB_POSTFIX);

        return new ProcessResult(true);
    }

    /**
     * 根据客户类型更新任务状态
     *
     * @param customerType 客户类型 ("存量" 或 "新增")
     * @param jobPostfix 任务名后缀
     */
    private void updateJobStatusByCustomerType(String customerType, String jobPostfix) {

        // 根据任务名查找需要被更新的任务id.
        // 如果今天是1号, 那么更新的是上个月任务; 否则更新本月任务
        LocalDateTime now = LocalDateTime.now();
        int monthValue = now.getDayOfMonth() == 1 ?
                now.minusMonths(1).getMonthValue() : now.getMonthValue();
        String jobName = monthValue + jobPostfix;
        CrmJob job = crmJobRepository.lambdaQuery()
                .select(CrmJob::getId)
                .eq(CrmJob::getJobName, jobName)
                .orderByDesc(CrmJob::getId).one();

        if (job == null) {
            log.info("没有找到{}客户任务, jobName: {}", customerType, jobName);
            return;
        }

        Long jobId = job.getId();
        log.info("开始处理{}客户任务, jobId: {}, jobName: {}", customerType, jobId, jobName);

        // 更新单个任务品完成状态
        Long lastId = 0L;
        List<HistoryCustCategoryPerformance> records;
        do {
            records = historyCustCategoryPerformanceRepository.lambdaQuery()
                    .eq(HistoryCustCategoryPerformance::getCustType, customerType)
                    .eq(HistoryCustCategoryPerformance::getIsCompleted, 1) // 只处理已完成的
                    .gt(HistoryCustCategoryPerformance::getId, lastId) // 基于id进行分页
                    .orderByAsc(HistoryCustCategoryPerformance::getId)
                    .last("limit 1000")
                    .list();

            if (CollectionUtil.isEmpty(records)) {
                break;
            }

            log.info("更新{}客户任务商品项, 当前处理id: {}", customerType, lastId);
            lastId = records.get(records.size() - 1).getId();

            List<LambdaUpdateWrapper<CrmJobMerchantItem>> itemUpdateWrappers = records.stream()
                    .map(record ->
                            Wrappers.lambdaUpdate(CrmJobMerchantItem.class)
                                    .set(CrmJobMerchantItem::getStatus, 1)
                                    .eq(CrmJobMerchantItem::getJobId, jobId)
                                    .eq(CrmJobMerchantItem::getMId, record.getCustId())
                                    .eq(CrmJobMerchantItem::getItem, record.getSpuGroup()))
                    .collect(Collectors.toList());

            crmJobMerchantItemRepository.updateBatch(itemUpdateWrappers);
        } while (CollectionUtil.isNotEmpty(records));

        // 更新整个门店任务的完成状态
        List<Long> allFinishedCust = historyCustCategoryPerformanceRepository.lambdaQuery()
                .select(HistoryCustCategoryPerformance::getCustId)
                .eq(HistoryCustCategoryPerformance::getCustType, customerType)
                .groupBy(HistoryCustCategoryPerformance::getCustId)
                .having("min(is_completed) = 1")
                .list().stream().map(HistoryCustCategoryPerformance::getCustId).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(allFinishedCust)) {
            return;
        }

        // 分批处理，每次最多100个，避免IN条件过大
        List<List<Long>> batches = SplitUtils.fixedSplit(allFinishedCust, 100);
        for (List<Long> batch : batches) {
            LambdaUpdateWrapper<CrmJobMerchantDetail> updateWrapper = Wrappers.lambdaUpdate(CrmJobMerchantDetail.class)
                    .set(CrmJobMerchantDetail::getStatus, 1)
                    .eq(CrmJobMerchantDetail::getJobId, jobId)
                    .in(CrmJobMerchantDetail::getMId, batch);
            crmJobMerchantDetailRepository.update(updateWrapper);
        }

        log.info("完成处理{}客户任务, jobId: {}", customerType, jobId);
    }
}
