package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.service.CategoryCouponService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 品类券定时任务
 *
 * <AUTHOR>
 * @Date 2023/3/10 16:27
 */
@Component
@Slf4j
public class MerchantSituationTaskHandler extends XianMuJavaProcessorV2 {
    @Resource
    private CategoryCouponService categoryCouponService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("分布式调度任务开始------调度任务:{}", context.getJobParameters());
        categoryCouponService.quotaReward();
        return new ProcessResult(true);
    }
}
