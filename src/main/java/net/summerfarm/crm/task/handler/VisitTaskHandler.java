package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.service.SampleApplyService;
import net.summerfarm.crm.service.VisitPlanService;
import net.summerfarm.crm.task.JobNameConstant;
import net.summerfarm.crm.task.JobParameters;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/28 0:11
 */
@Component
@Slf4j
public class VisitTaskHandler extends XianMuJavaProcessorV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(VisitTaskHandler.class);

    @Resource
    private VisitPlanService visitPlanService;

    @Resource
    private SampleApplyService sampleApplyService;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        LOGGER.info("分布式调度任务开始------调度任务:{}", context.getJobParameters());
        String jobParametersStr = context.getJobParameters();
        if (StringUtils.isBlank(jobParametersStr)) {
            LOGGER.error("未找到分布式调度任务!请确认");
        }
        JobParameters jobParameters = JSONObject.parseObject(jobParametersStr, JobParameters.class);
        if (Objects.isNull(jobParameters) || StringUtils.isBlank(jobParameters.getJobName())) {
            LOGGER.error("未找到分布式调度任务!请确认");
            return new ProcessResult(false);
        }

        // 当天未拜访计划更新为未处理
        if (JobNameConstant.CLOSE_CALL_PLAN.equals(jobParameters.getJobName())) {
            visitPlanService.updateUnHandlePlan(LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0)));
        }

        // 样品反馈
        if (JobNameConstant.SAMPLE_FEEDBACK.equals(jobParameters.getJobName())) {
            sampleApplyService.sendDingTalk();
        }

        // 期望时间为当天的所有拜访计划消息
        if (JobNameConstant.CALL_PLAN_MESSAGES.equals(jobParameters.getJobName())) {
            visitPlanService.sendDingTalkMsg();
        }

        return new ProcessResult(true);
    }
}
