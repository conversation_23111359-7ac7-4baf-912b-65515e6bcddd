package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.enums.WeChatTagGroupEnum;
import net.summerfarm.crm.enums.WechatEnum;
import net.summerfarm.crm.mapper.offline.CrmWechatTagGroupMapper;
import net.summerfarm.crm.mapper.manage.WechatTagMapper;
import net.summerfarm.crm.mapper.offline.CrmMerchantIncrementLabelMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.CrmWechatTagGroup;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.summerfarm.crm.model.domain.WechatTag;
import net.summerfarm.crm.model.vo.wechat.*;
import net.summerfarm.crm.service.WechatService;
import net.summerfarm.crm.service.impl.WechatServiceImpl;
import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.EnterpriseWechatProvider;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.*;
import static net.summerfarm.crm.facade.FeiShuPersonalMsgFacade.XIANMU_TENANT_ID;

/**
 * 企微标签同步
 *
 * <AUTHOR>
 * @Date 2023/8/4 15:46
 */
@Slf4j
@Component
public class WechatTagSyncHandle extends XianMuJavaProcessorV2 {
    @DubboReference
    private EnterpriseWechatProvider enterpriseWechatProvider;
    @Resource
    private WechatTagMapper tagMapper;
    @Resource
    private CrmConfig crmConfig;
    @Resource
    private WechatService wechatService;
    @Resource
    private CrmMerchantIncrementLabelMapper incrementLabelMapper;
    @Resource
    CrmWechatTagGroupMapper crmWechatTagGroupMapper;
    @Resource
    DataSynchronizationInformationMapper dataSynchronizationInformationMapper;
    @Override
    //@Transactional(rollbackFor = Exception.class)
    public ProcessResult processResult(XmJobInput context) throws Exception {

        DubboResponse<String> tokenRes = enterpriseWechatProvider.queryEnterpriseWeChatToken(SystemOriginEnum.ADMIN, XIANMU_TENANT_ID, EnterpriseWeChatTokenTypeEnum.CUSTOMER_ACCESS_TOKEN, true);

        // 获取企微分组下所有标签
        TagGroup getTag = new TagGroup();
        WechatServiceImpl.token = tokenRes.getData();

        String post = wechatService.post(GET_CORP_TAG_LIST, JSON.toJSONString(getTag));

        WechatTagResp wechatTagResp = WechatTagResp.fromJson(post);
        if (!wechatTagResp.success()) {
            log.error("获取企微标签失败:{}", post);
            return new ProcessResult(false);
        }
        if (wechatTagResp.getTagGroup().isEmpty()) {
            return new ProcessResult(true);
        }

        //名字map
        Map<String, String> nameIdMap = WechatTagResp.getNameIdMap(wechatTagResp);
        DataSynchronizationInformation information = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_WECHAT_TAG_GROUP.getTableName());
        Integer dateTag = Objects.isNull(information) ? NumberUtils.INTEGER_ZERO : information.getDateFlag();

        for (String groupName : nameIdMap.keySet()) {
            //判断是否时候需要同步的标签
            if (!WeChatTagGroupEnum.exitByName(groupName)){
                continue;
            }
            //根据标签组名称获取id
            String groupId = nameIdMap.get(groupName);
            List<TagGroup> collect = wechatTagResp.getTagGroup().stream().filter(it -> it.getGroupId().equals(groupId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)){
                continue;
            }
            List<Tag> tagList = collect.get(0).getTag();
            List<String> tagNameList = tagList.stream().map(Tag::getName).distinct().collect(Collectors.toList());

            //根据名称去获取组
            List<CrmWechatTagGroup>  needAdds = crmWechatTagGroupMapper.selectByGroupNameStatus(dateTag, groupName, WechatEnum.WechatTagTypeEnum.ADD.getType());
            Map<String, Integer> neddAddGroupMap = needAdds.stream().collect(Collectors.toMap(CrmWechatTagGroup::getMerchantLabel, CrmWechatTagGroup::getRank));

            // 过滤需要新增的标签
            List<String> allLabel = new ArrayList<>(neddAddGroupMap.keySet());
            List<Tag> tagJsonList = allLabel.stream().filter(label -> !tagNameList.contains(label)).map(label -> {
                Tag tag = new Tag();
                tag.setName(label);
                tag.setOrder(neddAddGroupMap.get(label));
                return tag;
            }).collect(Collectors.toList());

            if (!tagJsonList.isEmpty()) {
                // 同步标签
                TagGroup addTag = new TagGroup();
                addTag.setGroupId(groupId);
                addTag.setTag(tagJsonList);
                String addTagPost = wechatService.post(ADD_MARK_TAG, JSON.toJSONString(addTag));
                TagGroupResp addTagResp = TagGroupResp.fromJson(addTagPost);

                if (!addTagResp.success()) {
                    log.error("新增企微标签失败:{} ", post);
                    continue;
                }
                List<WechatTag> wechatTagList = addTagResp.getTagGroup().getTag().stream().map(tag -> {
                    WechatTag wechatTag = new WechatTag();
                    wechatTag.setTagName(tag.getName()).setTagId(tag.getId()).setGroupId(addTagResp.getTagGroup().getGroupId()).setGroupName(addTagResp.getTagGroup().getGroupName());
                    return wechatTag;
                }).collect(Collectors.toList());
                tagMapper.insertBatch(wechatTagList);
            }

            List<CrmWechatTagGroup>  needDelete = crmWechatTagGroupMapper.selectByGroupNameStatus(dateTag, groupName, WechatEnum.WechatTagTypeEnum.REMOVE.getType());
            if (!CollectionUtils.isEmpty(needDelete)){
                //查询是否存在
                List<String> needDeleteTag = needDelete.stream().map(CrmWechatTagGroup::getMerchantLabel).collect(Collectors.toList());
                List<WechatTag> wechatTags = tagMapper.selectByGroupNameTags(groupName, needDeleteTag);
                if (!CollectionUtils.isEmpty(wechatTags)){
                    DeleteTag deleteTag = new DeleteTag();
                   // deleteTag.setGroup_id(Collections.singletonList(groupId));
                    List<String> tagIds = wechatTags.stream().map(WechatTag::getTagId).collect(Collectors.toList());
                    deleteTag.setTag_id(tagIds);
                    log.info("要删除的标签集合{}", tagIds);
                    String addTagPost = wechatService.post(DELETE_CORP_TAG, JSON.toJSONString(deleteTag));
                    WeChatBaseResp weChatBaseResp = JSON.parseObject(addTagPost, WeChatBaseResp.class);
                    if (!weChatBaseResp.success()) {
                        log.error("删除微标签失败:{} ", post);
                        continue;
                    }
                    List<Long> deleteTagIds = wechatTags.stream().map(WechatTag::getId).collect(Collectors.toList());
                    tagMapper.deleteIds(deleteTagIds);
                }
            }
        }
        return new ProcessResult(true);
    }
}
