package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.enums.MerchantSizeEnum;
import net.summerfarm.crm.mapper.manage.ConfigMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.mapper.offline.CrmMerchantDayAttributeMapper;
import net.summerfarm.crm.mapper.offline.CrmMerchantDayLabelMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.CrmMerchantDayAttribute;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.vo.FollowUpRelationVO;
import net.summerfarm.crm.service.RiskMerchantService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/28 0:04
 */
@Component
@Slf4j
public class RiskMerchantTaskHandler extends XianMuJavaProcessorV2 {
    @Resource
    private RiskMerchantService riskMerchantService;

    @Override
    public ProcessResult processResult(XmJobInput context) {
        log.info("分布式调度任务开始------调度任务:{}", context.getJobParameters());
        riskMerchantService.riskMerchantSync();
        return new ProcessResult(true);
    }
}
