package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.service.BatchApplicationSamplesService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/8/29 15:34
 * @PackageName:net.summerfarm.crm.task.handler
 * @ClassName: BatchApplicationSampleHandler
 * @Description: 定时创建样品申请
 * @Version 1.0
 */
@Slf4j
@Component
public class BatchApplicationSampleHandler extends XianMuJavaProcessorV2 {

    @Resource
    private BatchApplicationSamplesService batchApplicationSamplesService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("BatchApplicationSampleHandler[]processResult[]start:{}", LocalDateTime.now());
        batchApplicationSamplesService.autoCreateSampleTask();
        log.info("BatchApplicationSampleHandler[]processResult[]end:{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
