package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.service.FollowUpRecordService;
import net.summerfarm.crm.service.SalesDataService;
import net.summerfarm.crm.task.JobNameConstant;
import net.summerfarm.crm.task.JobParameters;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/27 23:53
 */
@Component
@Slf4j
public class MailTaskHandler extends XianMuJavaProcessorV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(MailTaskHandler.class);

    @Resource
    private FollowUpRecordService followUpRecordService;

    @Resource
    private SalesDataService salesDataService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        LOGGER.info("分布式调度任务开始------调度任务:{}", jobContext.getJobParameters());

        String jobParametersStr = jobContext.getJobParameters();
        if (StringUtils.isBlank(jobParametersStr)) {
            LOGGER.error("未找到分布式调度任务!请确认");
        }
        JobParameters jobParameters = JSONObject.parseObject(jobParametersStr, JobParameters.class);
        if (Objects.isNull(jobParameters) || StringUtils.isBlank(jobParameters.getJobName())) {
            LOGGER.error("未找到分布式调度任务!请确认");
            return new ProcessResult(false);
        }

        // 拜访记录邮件
        if (JobNameConstant.CALL_LOG_EMAIL.equals(jobParameters.getJobName())) {
            followUpRecordService.autoSendMsg();
        }

        // 发生销售数据邮件
        if (JobNameConstant.SALES_DATA_MAIL.equals(jobParameters.getJobName())) {
            salesDataService.sendMail();
        }

        return new ProcessResult(true);
    }
}
