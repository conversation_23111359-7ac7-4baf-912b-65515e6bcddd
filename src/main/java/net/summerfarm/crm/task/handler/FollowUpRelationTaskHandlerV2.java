package net.summerfarm.crm.task.handler;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.enums.FollowUpRelationEnum;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.mapper.repository.FollowUpRelationReleaseDetailRepository;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.domain.FollowUpRelationReleaseDetail;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;

/**
 * 更新倒计时,释放公海,以及掉落倒计时飞书消息提醒
 */
@Slf4j
@Component
public class FollowUpRelationTaskHandlerV2 extends XianMuJavaProcessorV2 {

    private static final int MAX_TIMES_500 = 500;
    
    /**
     * 客户释放详情数据总量告警阈值
     */
    @NacosValue(value = "${follow.up.relation.alarm.threshold.count:90000}", autoRefreshed = true)
    private Integer alarmThresholdCount;

    @Resource
    private FollowUpRelationReleaseDetailRepository followUpRelationReleaseDetailRepository;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Autowired
    private CrmConfig crmConfig;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始更新商户倒计时并释放公海, context:{}", JSON.toJSONString(context));
        // 离线数据所属日期
        String dateFlag = LocalDate.now().minusDays(1L).format(DateTimeFormatter.BASIC_ISO_DATE);
        // 是否将商户释放到公海
        boolean releaseMerchant = false;
        // 是否更新倒计时
        boolean updateDangerDay = true;
        // 释放指定销售的商户到公海
        List<Integer> releaseBdIds = null;
        String parameter = StringUtils.isNotEmpty(context.getInstanceParameters()) ? context.getInstanceParameters() : context.getJobParameters();
        if (StringUtils.isNotEmpty(parameter)) {
            JSONObject paramJSON = JSON.parseObject(parameter);
            if (paramJSON.get("dateFlag") != null) {
                dateFlag = paramJSON.getString("dateFlag");
            }
            if (paramJSON.getBoolean("releaseMerchant") != null) {
                 releaseMerchant = paramJSON.getBooleanValue("releaseMerchant");
            }
            if (paramJSON.get("releaseBdIds") != null) {
                releaseBdIds = paramJSON.getJSONArray("releaseBdIds").toJavaList(Integer.class);
            }
             if (paramJSON.getBoolean("updateDangerDay") != null) {
                 updateDangerDay = paramJSON.getBooleanValue("updateDangerDay");
            }
        }

        // 校验离线数据总量，小于等于阈值时告警
        long count = followUpRelationReleaseDetailRepository.count(new LambdaQueryWrapper<FollowUpRelationReleaseDetail>().eq(FollowUpRelationReleaseDetail::getDateFlag, dateFlag));
        if (count <= alarmThresholdCount) {
            log.error("\n商户释放数据总量小于等于阈值，请检查，count:{}，dateFlag:{}\n", count, dateFlag);
        }

        int pageIndex = 1;
        int pageSize = 1000;
        PageInfo<FollowUpRelationReleaseDetail> pageInfo;

        // 分批处理
        do {
            if (pageIndex > MAX_TIMES_500) {
                log.warn("更新商户倒计时并释放公海的循环次数超过{}", MAX_TIMES_500);
                break;
            }
            PageHelper.startPage(pageIndex, pageSize);
            List<FollowUpRelationReleaseDetail> releaseDetails = followUpRelationReleaseDetailRepository.list(new LambdaQueryWrapper<FollowUpRelationReleaseDetail>()
                    .eq(FollowUpRelationReleaseDetail::getDateFlag, dateFlag).orderByAsc(FollowUpRelationReleaseDetail::getId));
            pageInfo = PageInfoHelper.createPageInfo(releaseDetails);
            log.info("当前处理第{}页,共{}条数据", pageIndex, releaseDetails.size());

            // 更新商户倒计时
            if (updateDangerDay) {
                this.updateDangerDay(releaseDetails);
            }
            // 将释放时间小于等于今天的商户释放至公海
            if (releaseMerchant) {
                this.releaseMerchant(releaseDetails, releaseBdIds);
            }

            pageIndex++;
        } while (pageInfo.isHasNextPage());
        log.info("更新商户倒计时并释放公海结束");

        return new ProcessResult(true);
    }

    private void releaseMerchant(List<FollowUpRelationReleaseDetail> releaseDetails, List<Integer> bdIds) {
        List<Long> releaseMIds = releaseDetails.stream().map(FollowUpRelationReleaseDetail::getMId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        try {
            // 有指定bdId时，只释放指定bd的私海客户
            if (CollectionUtils.isNotEmpty(bdIds)) {
                List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectByReassignAndMIdIn(Boolean.FALSE, releaseMIds);
                Map<Long, Integer> mId2AdminIdMap = followUpRelations.stream().collect(Collectors.toMap(FollowUpRelation::getmId, FollowUpRelation::getAdminId, (v1, v2) -> v1));
                releaseDetails = releaseDetails.stream().filter(x -> bdIds.contains(mId2AdminIdMap.get(x.getMId()))).collect(Collectors.toList());
            }

            Map<String, List<Long>> releasing = releaseDetails.stream()
                    .filter(releaseDetail -> releaseDetail.getReleaseDate() != null)
                    // 释放时间小于等于今天，则释放
                    .filter(releaseDetail -> releaseDetail.getReleaseDate().toLocalDate().compareTo(LocalDate.now()) <= 0)
                    // 根据释放规则分组,以便于批量释放
                    .collect(Collectors.groupingBy(
                            FollowUpRelationReleaseDetail::getReleaseRule,
                            Collectors.mapping(FollowUpRelationReleaseDetail::getMId, Collectors.toList())
                    ));

            releasing.forEach((reason, mIds) -> {
                log.info("释放以下私海客户，mIds：{}，reason:{}", mIds, reason);
                followUpRelationMapper.autoRelease(mIds, reason);
            });
        } catch (Exception ex) {
            log.error("释放私海客户失败, mIds:{}, bdIds:{}", releaseMIds, bdIds, ex);
        }
    }

    private void updateDangerDay(List<FollowUpRelationReleaseDetail> releaseDetails) {
        releaseDetails.forEach(releaseDetail -> {
            try {
                LocalDate releaseDate = releaseDetail.getReleaseDate().toLocalDate();
                // 现在BI给出的dangerDay已经不准，为了暂时兼容老逻辑重新计算下dangerDay
                int dangerDay = (int) ChronoUnit.DAYS.between(LocalDate.now(), releaseDate);
                String protectReason = releaseDetail.getHasUnfinishedDelivery() ? FollowUpRelationEnum.ProtectReason.UNFINISHED_DELIVERY.getValue() : "";
                // 更新掉落时间
                LocalDateTime releaseTime = releaseDate.atTime(crmConfig.getPrivateSeaReassignTime());
                followUpRelationMapper.updateDangerDayAndReasonByMId(
                        dangerDay,
                        releaseDetail.getReleaseRule(),
                        releaseTime,
                        protectReason,
                        releaseDetail.getMId());
            } catch (Exception ex) {
                log.error("更新商户掉落时间失败, releaseDetail:{}", JSON.toJSONString(releaseDetail), ex);
            }
        });
    }
}
