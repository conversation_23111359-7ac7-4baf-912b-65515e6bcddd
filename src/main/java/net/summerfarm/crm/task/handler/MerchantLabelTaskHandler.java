package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.es.dto.EsMerchantIndexDTO;
import net.summerfarm.crm.common.util.EsUtil;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.mapper.offline.CrmMerchantDayLabelMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static net.summerfarm.crm.common.constant.CrmGlobalConstant.REGISTER_FIRST_ORDER_30_DAY;
import static net.summerfarm.crm.common.constant.CrmGlobalConstant.REGISTER_NO_ORDER_30_DAY;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/28 0:04
 */
@Component
@Slf4j
public class MerchantLabelTaskHandler extends XianMuJavaProcessorV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantLabelTaskHandler.class);

    @Resource
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        initLabel();
        return new ProcessResult(true);
    }


    /**
     * 释放私海客户,更新倒计时
     * 释放规则: 包含配置表中key = RELEASE_RULES中描述的标签
     */
    public void initLabel() {
        // 释放用户至公海
        DataSynchronizationInformation labelData = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        Integer data = Objects.isNull(labelData) ? NumberUtils.INTEGER_ZERO : labelData.getDateFlag();
        List<String> ruleLabels = Arrays.asList(REGISTER_NO_ORDER_30_DAY, REGISTER_FIRST_ORDER_30_DAY);
        int size = 200;
        for (String ruleLabel : ruleLabels) {
            Long count = crmMerchantDayLabelMapper.selectMidCountByLabelLimit(ruleLabel, data);
            if (count == 0) {
                continue;
            }
            int l = (int) (count / size);
            int offset = 0;
            for (int i = 0; i <= l; i++) {
                Set<Long> midList = crmMerchantDayLabelMapper.selectMidListByLabelLimit(ruleLabel, data, offset, size);
                offset = offset + size;
                if (CollectionUtil.isEmpty(midList)) {
                    LOGGER.info("{} 无 {}的客户", LocalDateTime.now(), ruleLabel);
                    continue;
                }
                addLabel(midList, ruleLabel);
            }


        }
        // 更新用户倒计时
    }

    private void addLabel(Set<Long> midList, String label) {
        if (CollectionUtils.isEmpty(midList)) {
            return;
        }
        ArrayList<Long> longs = new ArrayList<>(midList);
        List<EsMerchantIndexDTO> list = EsUtil.queryByMids(longs);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(
                it -> {
                    List<String> merchantLabel = it.getMerchantLabel();
                    if (CollectionUtils.isEmpty(merchantLabel)) {
                        merchantLabel = new ArrayList<>();
                        merchantLabel.add(label);
                    }
                    if (!merchantLabel.contains(label)) {
                        merchantLabel.add(label);
                    }
                    it.setMerchantLabel(merchantLabel);
                    EsUtil.insertOrUpdateByMid(it);
                }
        );
    }

}
