package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.mapper.manage.ContactMapper;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.service.MerchantService;
import net.summerfarm.crm.service.MerchantSituationService;
import net.summerfarm.crm.service.SampleApplyReviewService;
import net.summerfarm.crm.task.JobNameConstant;
import net.summerfarm.crm.task.JobParameters;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/27 23:50
 */
@Component
@Slf4j
public class MerchantTaskHandler extends XianMuJavaProcessorV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantTaskHandler.class);

    @Resource
    private SampleApplyReviewService sampleApplyReviewService;

    @Resource
    private MerchantSituationService merchantSituationService;

    @Resource
    private MerchantService merchantService;

    @Resource
    private MerchantMapper merchantMapper;

    @Resource
    private ContactMapper contactMapper;


    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        LOGGER.info("分布式调度任务开始--调度任务:{}", jobContext.getJobParameters());
        String jobParametersStr = jobContext.getJobParameters();
        if (StringUtils.isBlank(jobParametersStr)) {
            LOGGER.error("未找到分布式调度任务!请确认");
        }
        JobParameters jobParameters = JSONObject.parseObject(jobParametersStr, JobParameters.class);
        if (Objects.isNull(jobParameters) || StringUtils.isBlank(jobParameters.getJobName())) {
            LOGGER.error("未找到分布式调度任务!请确认");
            return new ProcessResult(false);
        }

        // 关闭样品申请审核,每个月1号或者三天前
        if (JobNameConstant.CLOSE_SAMPLE_REQUEST.equals(jobParameters.getJobName())) {
            sampleApplyReviewService.closeMonthSampleApplyReview();
        }

        // 关闭客情申请 每个月1号或者三天前
        if (JobNameConstant.CLOSE_CUSTOMER_REQUEST.equals(jobParameters.getJobName())) {
            if (NumberUtils.INTEGER_ONE.equals(LocalDate.now().getDayOfMonth())) {
                merchantSituationService.autoCloseSituation();
                merchantSituationService.autoQuota();
            } else {
                merchantSituationService.autoTreeDayCloseSituation();
            }
        }

        // 审批客户提醒
        if (JobNameConstant.APPROVE_CUSTOMER_REMINDER.equals(jobParameters.getJobName())) {
            merchantService.sendMerchantReviewMessage();
        }
        return new ProcessResult(true);
    }


}
