package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.service.FollowUpRecordService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 拜访记录定时通知
 *
 * <AUTHOR>
 * @date 2023/1/29 10:52
 */
@Slf4j
@Component
public class FollowUpRecordDingTalkTaskHandler extends XianMuJavaProcessorV2 {
    @Resource
    FollowUpRecordService follwUpRecordService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("分布式调度任务开始------调度任务:{}", context.getJobParameters());
        follwUpRecordService.sendDingMessage();
        return new ProcessResult(true);
    }
}
