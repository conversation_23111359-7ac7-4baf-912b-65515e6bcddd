package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.mapper.manage.CrmJobMapper;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

@Component
@Slf4j
public class CrmJobUpdateStatusHandler extends XianMuJavaProcessorV2 {

    @Resource
    private CrmJobMapper crmJobMapper;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("定时任务 - 开始更新任务状态");

        LocalDateTime now = LocalDateTime.now();
        // 更新未开始的任务状态为进行中
        crmJobMapper.updateStatusByStartTimeLessThanOrEqualToAndStatusIn(
                CrmJobEnum.Status.IN_PROGRESS.getCode(),
                now,
                Collections.singletonList(CrmJobEnum.Status.NOT_STARTED.getCode()));

        // 更新未开始或进行中的任务状态为已结束
        crmJobMapper.updateStatusByEndTimeLessThanOrEqualToAndStatusIn(
                CrmJobEnum.Status.COMPLETED.getCode(),
                now,
                Arrays.asList(CrmJobEnum.Status.NOT_STARTED.getCode(), CrmJobEnum.Status.IN_PROGRESS.getCode()));

        log.info("定时任务 - 更新任务状态结束");
        return new ProcessResult(true);
    }
}
