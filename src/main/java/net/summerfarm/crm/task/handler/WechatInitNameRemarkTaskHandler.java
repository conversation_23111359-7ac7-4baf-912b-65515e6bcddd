package net.summerfarm.crm.task.handler;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.StringUtils;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.enums.WeChatTagGroupEnum;
import net.summerfarm.crm.enums.WechatEnum;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.CrmMerchantIncrementLabelMapper;
import net.summerfarm.crm.mapper.offline.CrmWechatTagGroupMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.vo.wechat.*;
import net.summerfarm.crm.service.WechatService;
import net.summerfarm.crm.service.impl.WechatServiceImpl;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.provider.EnterpriseWechatProvider;
import net.xianmu.authentication.client.resp.UserBaseThirdPartyResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.*;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.OFFICIAL_WECHAT;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.SALES_WECHAT;
import static net.summerfarm.crm.enums.WechatEnum.WechatFollowStatusEnum.FOLLOW;
import static net.summerfarm.crm.enums.WechatEnum.WechatTagTypeEnum.ADD;
import static net.summerfarm.crm.facade.FeiShuPersonalMsgFacade.XIANMU_TENANT_ID;

/**
 * 企业微信名字备注初始化
 *
 * <AUTHOR>
 * @Date 2023/8/4 15:46
 */
@Slf4j
@Component
public class WechatInitNameRemarkTaskHandler extends XianMuJavaProcessorV2 {

    @DubboReference
    private AuthUserAuthProvider authUserAuthProvider;
    @Resource
    private WechatService wechatService;
    @Resource
    MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    WechatUserInfoMapper wechatUserInfoMapper;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        WechatServiceImpl.token = wechatService.getAccessToken(true);


        // 获取bd列表
        AuthUserAuthQueryInput input = new AuthUserAuthQueryInput();
        input.setPageNum(1);
        input.setPageSize(500);
        input.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        input.setAuthType(AuthTypeEnum.ENTERPRISE_WE_CHAT);
        input.setTenantId(1L);
        DubboResponse<PageInfo<UserBaseThirdPartyResp>> pageInfoDubboResponse = authUserAuthProvider.queryPageUserBase(input);
        if (!pageInfoDubboResponse.isSuccess()) {
            return new ProcessResult(false, "获取用户信息失败");
        }
        List<UserBaseThirdPartyResp> userBaseList = pageInfoDubboResponse.getData().getList();

        // 获取bd 企微客户列表
        for (UserBaseThirdPartyResp userBase : userBaseList) {

            List<WechatUserInfo> wechatUserInfos = wechatUserInfoMapper.selectByUserId(userBase.getThirdPartyId());
            if (CollectionUtils.isEmpty(wechatUserInfos)){
                continue;
            }
            for (WechatUserInfo wechatUserInfo : wechatUserInfos) {
                String unionid = wechatUserInfo.getUnionid();
                MerchantSubAccount merchantSubAccount = merchantSubAccountMapper.selectByUnionid(unionid);
                wechatService.updateRemark(merchantSubAccount, wechatUserInfo.getUserId(), wechatUserInfo.getExternalUserid());

            }
        }
        return new ProcessResult(true);
    }

}
