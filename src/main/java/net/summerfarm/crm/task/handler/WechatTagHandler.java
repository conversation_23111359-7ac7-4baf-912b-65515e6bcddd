package net.summerfarm.crm.task.handler;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.schedulerx.common.util.StringUtils;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.enums.WeChatTagGroupEnum;
import net.summerfarm.crm.mapper.manage.*;
import net.summerfarm.crm.mapper.offline.CrmMerchantIncrementLabelMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.*;
import net.summerfarm.crm.model.vo.wechat.CustomerDetailResp;
import net.summerfarm.crm.model.vo.wechat.WeChatBaseResp;
import net.summerfarm.crm.model.vo.wechat.WechatCustomerInfo;
import net.summerfarm.crm.service.WechatService;
import net.summerfarm.crm.service.impl.WechatServiceImpl;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.resp.UserBaseThirdPartyResp;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.*;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.OFFICIAL_WECHAT;
import static net.summerfarm.crm.enums.WechatEnum.WechatCrmTagEnum.SALES_WECHAT;
import static net.summerfarm.crm.enums.WechatEnum.WechatFollowStatusEnum.FOLLOW;
import static net.summerfarm.crm.enums.WechatEnum.WechatTagTypeEnum.ADD;

/**
 * <AUTHOR>
 * @Date 2023/8/1 11:34
 */
@Slf4j
@Component
public class WechatTagHandler extends XianMuJavaProcessorV2 {
    @Resource
    private MerchantLabelMapper merchantLabelMapper;
    @Resource
    private MerchantLabelCorrelaionMapper correlaionMapper;
    @Resource
    private CrmMerchantIncrementLabelMapper crmMerchantIncrementLabelMapper;
    @Resource
    private DataSynchronizationInformationMapper dataMapper;
    @Resource
    private WechatTagMapper tagMapper;
    @Resource
    private ConfigMapper configMapper;
    @DubboReference
    private AuthUserAuthProvider authUserAuthProvider;
    @Resource
    private WechatService wechatService;
    @Resource
    WechatUserInfoMapper wechatUserInfoMapper;
    @Resource
    RedisTemplate redisTemplate;

    private static final String REDIS_KEY= "crm:qw:update:remark:";
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        DataSynchronizationInformation data = dataMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_INCREMENT_LABEL.getTableName());

        if (data == null || !data.getDateFlag().toString().equals(DateUtils.localDateTimeToStringTwo(LocalDate.now().minusDays(1)))) {
            log.error("data_synchronization_information 为空或者日期不正确:{}", JSONUtil.toJsonPrettyStr(data));
            return new ProcessResult(false, "data_synchronization_information 为空或者日期不正确");
        }

        WechatServiceImpl.token = wechatService.getAccessToken(true);

        String param = context.getInstanceParameters();

        // 获取bd列表
        AuthUserAuthQueryInput input = new AuthUserAuthQueryInput();
        input.setPageNum(1);
        input.setPageSize(500);
        input.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        input.setAuthType(AuthTypeEnum.ENTERPRISE_WE_CHAT);
        input.setTenantId(1L);
        DubboResponse<PageInfo<UserBaseThirdPartyResp>> pageInfoDubboResponse = authUserAuthProvider.queryPageUserBase(input);
        if (!pageInfoDubboResponse.isSuccess()) {
            return new ProcessResult(false, "获取用户信息失败");
        }
        List<UserBaseThirdPartyResp> userBaseList = pageInfoDubboResponse.getData().getList();

        // 官微用户列表
        Config config = configMapper.selectOne(ConfigValueEnum.OFFICIAL_WECHAT_USER_ID.getKey());
        List<String> officialUserIdList = StrUtil.split(config.getValue(), StrUtil.COMMA);
        // 过滤已存在的官微
        userBaseList.removeIf(userBase -> officialUserIdList.contains(userBase.getThirdPartyId()));
        for (String userId : officialUserIdList) {
            UserBaseThirdPartyResp resp = new UserBaseThirdPartyResp();
            resp.setThirdPartyId(userId);
            userBaseList.add(resp);
        }

        // 获取bd 企微客户列表
        for (UserBaseThirdPartyResp userBase : userBaseList) {
            List<WechatCustomerInfo> customerInfoList = new ArrayList<>();
            // 获取企微客户列表
            getCustomerList(userBase.getThirdPartyId(), null, customerInfoList, userBase.getBizUserId());
            // 给企微/门店打标签
            tag(customerInfoList, data.getDateFlag());
            // 企微客户入库
            insertWechatUser(customerInfoList, userBase.getThirdPartyId(),param);
        }

        return new ProcessResult(true);
    }

    /**
     * 新增企微数据
     *
     * @param customerInfoList 客户信息列表
     * @param userId           用户id
     */
    public void insertWechatUser(List<WechatCustomerInfo> customerInfoList, String userId,String param) {
        if (customerInfoList.isEmpty()){
            return;
        }
        List<WechatUserInfo> wechatUserInfos = wechatUserInfoMapper.selectByUserIdAndUnionIds(userId, customerInfoList.stream().map(WechatCustomerInfo::getUnionId).collect(Collectors.toList()));

        // 初始化企微来源
        if (StrUtil.isNotEmpty(param) && Boolean.parseBoolean(param)) {
            Map<String, List<WechatCustomerInfo>> emptyStateMap = customerInfoList.stream().filter(w -> StrUtil.isNotEmpty(w.getState())).collect(Collectors.groupingBy(WechatCustomerInfo::getState));
            if (!emptyStateMap.isEmpty()){
                emptyStateMap.forEach((key, value) -> wechatUserInfoMapper.updateStateById(key, value));
            }
        }

        List<WechatUserInfo> wechatUserInfoList = customerInfoList.stream().filter(c->StrUtil.isNotEmpty(c.getUnionId())).filter(c -> wechatUserInfos.stream().noneMatch(w -> ObjectUtil.equal(w.getUnionid(), c.getUnionId()))).map(c -> {
            return new WechatUserInfo().setStatus(FOLLOW.getStatus()).setState(c.getState()).setUnionid(c.getUnionId()).setUserId(c.getUserId()).setExternalUserid(c.getExternalUserid()).setAddTime(c.getCreatetime()).setAdminId(c.getBizId());
        }).collect(Collectors.toList());
        if (wechatUserInfoList.isEmpty()){
            return;
        }
        wechatUserInfoMapper.insertBatch(wechatUserInfoList);
    }

    /**
     * 给门店打标签
     *
     * @param customerInfoList 客户信息列表
     * @param dateFlag         日期标记
     */
    public void tag(List<WechatCustomerInfo> customerInfoList, Integer dateFlag) {
        if (customerInfoList.isEmpty()) {
            return;
        }
        // 获取未加销微的客户
        List<MerchantSubAccount> salesWechat = merchantLabelMapper.selectListMerchantLabel(customerInfoList.stream().map(WechatCustomerInfo::getUnionId).collect(Collectors.toList()), SALES_WECHAT);
        // 获取未加官微的客户
        List<MerchantSubAccount> officialWechat = merchantLabelMapper.selectListMerchantLabel(customerInfoList.stream().map(WechatCustomerInfo::getUnionId).collect(Collectors.toList()),
                OFFICIAL_WECHAT);
        Map<String, MerchantSubAccount> salesWechatMap = salesWechat.stream().collect(Collectors.toMap(MerchantSubAccount::getUnionid, Function.identity()));
        Map<String, MerchantSubAccount> officialWechatMap = officialWechat.stream().collect(Collectors.toMap(MerchantSubAccount::getUnionid, Function.identity()));

        MerchantLabel salesLabel = merchantLabelMapper.selectByName(SALES_WECHAT);
        MerchantLabel officialLabel = merchantLabelMapper.selectByName(OFFICIAL_WECHAT);
        Map<String, List<WechatTag>> tagMap = tagMapper.listAll().stream().filter(it ->
                !StringUtils.isEmpty(it.getGroupName()) && WeChatTagGroupEnum.exitByName(it.getGroupName()))
                .collect(Collectors.groupingBy(WechatTag::getTagName));
        for (WechatCustomerInfo customerInfo : customerInfoList) {
            boolean isNewCustomer = customerInfo.getCreatetime().toLocalDate().equals(LocalDate.now().minusDays(1));
            // 标记客户标签
            List<CrmMerchantIncrementLabel> labels = crmMerchantIncrementLabelMapper.selectByUnionId(customerInfo.getUnionId(), dateFlag, isNewCustomer);
            tagCustomer(customerInfo.getUserId(), customerInfo.getExternalUserid(), labels, tagMap);

            // 判断门店是否已经设置标签
            boolean isOfficial = customerInfo.getBizId() == null;
            MerchantSubAccount subAccount = isOfficial ? officialWechatMap.get(customerInfo.getUnionId()) : salesWechatMap.get(customerInfo.getUnionId());
            if (subAccount != null) {
                // 通过biz id是否为空区分是否为官微
                Long id = isOfficial ? officialLabel.getId() : salesLabel.getId();
                correlaionMapper.insertByMidAndLabelId(subAccount.getMId(), id);
                String redisKey = REDIS_KEY + subAccount.getMId();
                if (!redisTemplate.hasKey(redisKey)) {
                    wechatService.updateRemark(subAccount, customerInfo.getUserId(), customerInfo.getExternalUserid());
                    redisTemplate.opsForValue().set(redisKey, subAccount.getMId().toString(), 1, TimeUnit.DAYS);
                }
            }

        }


    }

    /**
     * 获取bd 企微客户列表
     *
     * @param userId          用户id
     * @param cursor          光标
     * @param customerInfoLis 客户信息列表
     */
    public void getCustomerList(String userId, String cursor, List<WechatCustomerInfo> customerInfoLis, Long bizId) {
        JSONObject customerJson = new JSONObject();
        customerJson.set("userid_list", Collections.singletonList(userId));
        // 由于企业接口限制，单次最大值为100
        customerJson.set("limit", 100);
        if (cursor != null) {
            customerJson.set("cursor", cursor);
        }

        String post = wechatService.post(BATCH_GET_BY_USER, customerJson.toString());
        CustomerDetailResp customerDetailResp = CustomerDetailResp.fromJson(post);
        if (!customerDetailResp.success()) {
            log.error("批量获取客户详情失败 : {}", post);
            return;
        }
        List<WechatCustomerInfo> info = customerDetailResp.getExternalContactList().stream().map(c -> {
            WechatCustomerInfo wechatCustomerInfo = new WechatCustomerInfo();
            wechatCustomerInfo.setUserId(userId);
            wechatCustomerInfo.setExternalUserid(c.getExternalContact().getExternalUserid());
            wechatCustomerInfo.setUnionId(c.getExternalContact().getUnionid());
            wechatCustomerInfo.setBizId(bizId);
            wechatCustomerInfo.setState(c.getFollowInfo().getState());
            if (c.getFollowInfo() != null) {
                wechatCustomerInfo.setTagId(c.getFollowInfo().getTagId());
                if (c.getFollowInfo().getCreatetime() != null) {
                    wechatCustomerInfo.setCreatetime(LocalDateTimeUtil.of(c.getFollowInfo().getCreatetime()*1000));
                }
            }
            return wechatCustomerInfo;
        }).collect(Collectors.toList());
        customerInfoLis.addAll(info);
        // 递归查询剩余客户
        if (StrUtil.isNotEmpty(customerDetailResp.getNextCursor())) {
            getCustomerList(userId, customerDetailResp.getNextCursor(), customerInfoLis, bizId);
        }
    }

    /**
     * 给企微客户打标签
     *
     * @param userId                     用户id
     * @param externalUserid             外部用户标识
     * @param crmMerchantIncrementLabels CRM商户增量标签
     * @param tagMap                     标记图
     */
    public void tagCustomer(String userId, String externalUserid, List<CrmMerchantIncrementLabel> crmMerchantIncrementLabels,
                            Map<String, List<WechatTag>> tagMap) {
        if (crmMerchantIncrementLabels.isEmpty()) {
            return;
        }
        JSONObject customerJson = new JSONObject();
        customerJson.set("userid", userId);
        customerJson.set("external_userid", externalUserid);

        List<String> addTagList = new ArrayList<>();
        List<String> removeTagList = new ArrayList<>();
        // 获取新增/移除标签
        for (CrmMerchantIncrementLabel label : crmMerchantIncrementLabels) {
            List<WechatTag> wechatTags = tagMap.get(label.getMerchantLabel());
            if (CollectionUtils.isEmpty(wechatTags)) {
                continue;
            }
            String tagid = null;
            if (wechatTags.size() == 1){
                tagid = wechatTags.get(0).getTagId();
            }else {
                Optional<WechatTag> first = wechatTags.stream().filter(it -> Objects.equals(it.getGroupName(), label.getGroupName())).findFirst();
                if (first.isPresent()){
                    tagid = first.get().getTagId();
                }
            }
            if (StringUtils.isEmpty(tagid)){
                continue;
            }

            if (ObjectUtil.equal(label.getType(), ADD.getType())) {
                addTagList.add(tagid);
            } else {
                removeTagList.add(tagid);
            }
        }
        if (!addTagList.isEmpty()) {
            customerJson.set("add_tag", addTagList);
            String post = wechatService.post(EDIT_MARK_TAG, customerJson.toString());
            WeChatBaseResp resp = WeChatBaseResp.fromJson(post);
            if (!resp.success()) {
                // token过期刷新token
                log.warn("编辑客户标签失败：{}", post);
            }
        }
        if (!removeTagList.isEmpty()) {
            customerJson.set("remove_tag", removeTagList);
            customerJson.remove("add_tag");
            String post = wechatService.post(EDIT_MARK_TAG, customerJson.toString());
            WeChatBaseResp resp = WeChatBaseResp.fromJson(post);
            if (!resp.success()) {
                // token过期刷新token
                log.warn("编辑删除失败：{}", post);
            }
        }
       /* WeChatBaseResp resp = WeChatBaseResp.fromJson(post);
        if (!resp.success()) {
            // token过期刷新token
            log.error("编辑客户标签失败：{}", post);
        }*/
    }

}
