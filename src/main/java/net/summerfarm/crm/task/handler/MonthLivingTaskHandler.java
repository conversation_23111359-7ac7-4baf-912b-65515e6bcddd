package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.service.CommissionService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/28 0:00
 */
@Component
@Slf4j
public class MonthLivingTaskHandler extends XianMuJavaProcessorV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(MonthLivingTaskHandler.class);

    @Resource
    private CommissionService commissionService;



    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        LOGGER.info("分布式调度任务开始------调度任务:{}", context.getJobParameters());
        // 月活池额度更新
        commissionService.updateMonthLivingCouponQuotaEveryMonth();
        return new ProcessResult(true);
    }
}
