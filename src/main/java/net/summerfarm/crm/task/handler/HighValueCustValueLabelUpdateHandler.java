package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.es.dto.EsMerchantIndexDTO;
import net.summerfarm.crm.common.util.EsUtil;
import net.summerfarm.crm.enums.PerformanceOrderSourceEnum;
import net.summerfarm.crm.mapper.repository.performance.CustPerformanceCommRepository;
import net.summerfarm.crm.model.domain.CustPerformanceComm;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 高价值客户标签更新处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HighValueCustValueLabelUpdateHandler extends XianMuJavaProcessorV2 {

    private static final int MAX_TIMES_2000 = 2000;

    @Autowired
    private CustPerformanceCommRepository custPerformanceCommRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("更新高价值客户标签开始, context:{}", JSON.toJSONString(context));
        String parameter = StringUtils.isNotEmpty(context.getInstanceParameters()) ? context.getInstanceParameters()
                : context.getJobParameters();
        if (StringUtils.isNotEmpty(parameter)) {
            List<Long> custIds = Arrays.stream(parameter.split(",")).map(Long::valueOf).collect(Collectors.toList());
            log.info("开始更新指定客户的高价值标签，custIds:{}", custIds);
            List<CustPerformanceComm> custPerformanceComms =
                    custPerformanceCommRepository.listByCustIds(custIds, PerformanceOrderSourceEnum.XIANMU.getValue());
            this.updateHighValueCustValueLabel(custPerformanceComms);
            return new ProcessResult(true);
        }

        int pageNum = 1;
        int pageSize = 100;
        PageInfo<CustPerformanceComm> pageInfo;
        do {
            if (pageNum > MAX_TIMES_2000) {
                log.warn("更新高价值客户标签的循环次数超过{}", MAX_TIMES_2000);
                break;
            }
            pageInfo = custPerformanceCommRepository.listByOrderSource(PerformanceOrderSourceEnum.XIANMU.getValue(),
                    pageNum, pageSize);
            log.info("当前处理第{}页,共{}条数据", pageNum, pageInfo.getSize());
            if (CollectionUtils.isEmpty(pageInfo.getList())) {
                break;
            }
            // 更新高价值客户标签
            this.updateHighValueCustValueLabel(pageInfo.getList());
            pageNum++;
        } while (pageInfo.isHasNextPage());
        log.info("更新高价值客户标签结束");

        return new ProcessResult(true);
    }

    private void updateHighValueCustValueLabel(List<CustPerformanceComm> custPerformanceComms) {
        try {
            Map<Long, String> custId2ValueLabelMap =
                    custPerformanceComms.stream().filter(x -> StringUtils.isNotEmpty(x.getCustValueLable())).collect(
                            Collectors.toMap(CustPerformanceComm::getCustId, CustPerformanceComm::getCustValueLable,
                                    (v1, v2) -> v1));
            if (MapUtils.isEmpty(custId2ValueLabelMap)) {
                return;
            }
            List<EsMerchantIndexDTO> merchantIndexList = EsUtil.queryByMids(Lists.newArrayList(custId2ValueLabelMap.keySet()));
            if (CollectionUtils.isEmpty(merchantIndexList)) {
                return;
            }
            merchantIndexList.forEach(merchantIndex -> {
                String custValueLabelInEs = merchantIndex.getHighValueLabelV2();
                String custValueLabel = custId2ValueLabelMap.get(merchantIndex.getmId());
                if (!Objects.equal(custValueLabelInEs, custValueLabel)) {
                    merchantIndex.setHighValueLabelV2(custValueLabel);
                    EsUtil.insertOrUpdateByMid(merchantIndex);
                }
            });
        } catch (Exception e) {
            log.error("更新高价值客户标签异常", e);
        }
    }
}
