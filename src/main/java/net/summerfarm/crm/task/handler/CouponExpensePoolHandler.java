package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.service.CrmCouponExpensePoolService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class CouponExpensePoolHandler extends XianMuJavaProcessorV2 {
    @Resource
    CrmCouponExpensePoolService crmCouponExpensePoolService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
         crmCouponExpensePoolService.updatePoolStatus();
        return new ProcessResult(true);
    }
}
