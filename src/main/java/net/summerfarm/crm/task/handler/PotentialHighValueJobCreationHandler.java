package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmJobConstant;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.enums.HighValueCustomerTypeEnum;
import net.summerfarm.crm.mapper.repository.CustMtdPerformanceRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CustMtdPerformance;
import net.summerfarm.crm.model.dto.crmjob.JobCompletionCriteriaDTO;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobV2DTO;
import net.summerfarm.crm.model.dto.crmjobv2.UpdateJobV2DTO;
import net.summerfarm.crm.service.crmjob.CrmJobServiceV2;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 潜力高价值客户任务创建
 */
@Component
@Slf4j
public class PotentialHighValueJobCreationHandler extends XianMuJavaProcessorV2 {

    @Resource
    private CrmJobServiceV2 crmJobServiceV2;
    @Resource
    private CrmJobRepository crmJobRepository;
    @Resource
    private CustMtdPerformanceRepository custMtdPerformanceRepository;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始创建高价值客户任务------");

        // 任务名为 "x月份潜在高价值客户任务"
        // 2号开始才创建当月任务
        LocalDateTime now = LocalDateTime.now();
        int monthValue = now.getDayOfMonth() == 1 ?
                now.minusMonths(1).getMonthValue() : now.getMonthValue();
        String jobName = monthValue + CrmJobConstant.POTENTIAL_HIGH_VALUE_CUSTOMER_JOB_POSTFIX;

        CrmJob job = this.getJobByName(jobName);
        if (job == null) {
            this.createJob(jobName);
            job = this.getJobByName(jobName);
        }

        Long lastId = 0L;
        List<CustMtdPerformance> records;
        do {
            records = custMtdPerformanceRepository.lambdaQuery()
                    .select(CustMtdPerformance::getCustId, CustMtdPerformance::getId)
                    .eq(CustMtdPerformance::getCustValueLable, HighValueCustomerTypeEnum.POTENTIAL_HIGH_VALUE.getDesc())
                    .gt(CustMtdPerformance::getId, lastId)
                    .orderByAsc(CustMtdPerformance::getId)
                    .last("limit 1000")
                    .list();

            if (CollectionUtil.isEmpty(records)) {
                break;
            }

            List<Long> mIds = records.stream().map(CustMtdPerformance::getCustId).collect(Collectors.toList());

            UpdateJobV2DTO dto = new UpdateJobV2DTO();
            dto.setJobId(job.getId());
            dto.setMIdList(mIds);
            crmJobServiceV2.updateJob(dto);

            log.info("已经创建了{}条数据, jobName: {}", mIds.size(), jobName);
            lastId = records.get(records.size() - 1).getId();
        } while (CollectionUtil.isNotEmpty(records));

        return new ProcessResult(true);
    }

    private CrmJob getJobByName(String jobName) {
        return crmJobRepository.lambdaQuery()
                .eq(CrmJob::getJobName, jobName)
                .gt(CrmJob::getCreateTime, LocalDateTime.now().minusMonths(2))
                .last("limit 1").one();
    }

    private void createJob(String jobName) {
        CreateJobV2DTO dto = new CreateJobV2DTO();
        JobCompletionCriteriaDTO completionCriteria = new JobCompletionCriteriaDTO();
        completionCriteria.setCompletionType(CrmJobEnum.CompletionCriteriaType.POTENTIAL_HIGH_VALUE.getCode());

        DateTime now = DateUtil.date();
        String description = String.format("%s月的潜力高价值客户清单，请给予重点关注", now.monthBaseOne());

        // 任务开始时间为每月2号
        LocalDateTime startTime = DateUtil.beginOfMonth(now).toLocalDateTime().plusDays(1);
        // 任务结束时间为下月2号 00:00
        LocalDateTime endTime = DateUtil.beginOfMonth(now).toLocalDateTime().plusMonths(1).plusDays(1);

        dto.setJobName(jobName);
        dto.setType(CrmJobEnum.Type.POTENTIAL_HIGH_VALUE_CUSTOMER.getCode());
        dto.setDescription(description);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCompletionCriteriaList(Collections.singletonList(completionCriteria));
        dto.setMerchantSelectionType(CrmJobEnum.MerchantSelectionType.MERCHANT_LIST.getCode());

        crmJobServiceV2.createJob(dto);
    }
}
