package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.mapper.manage.MerchantSubAccountMapper;
import net.summerfarm.crm.mapper.manage.WechatUserInfoMapper;
import net.summerfarm.crm.mapper.manage.WecomUserInfoMapper;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.domain.MerchantSubAccount;
import net.summerfarm.crm.model.domain.WechatUserInfo;
import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.service.WechatService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BD离职或手动分配导致的客户被转移至其他BD后, 同步客户的企微至新BD的企微
 * 调用企微的离职继承接口. 传入的企微用户id必须是离职状态
 * 注意：从2025.02.25起该功能已被{@see WeComBdSyncHandler}包括. 但是可以作为手动补偿链路
 */
@Slf4j
@Component
public class WeComResignedBdCustomerTransferHandler extends XianMuJavaProcessorV2 {

    @Resource
    private WechatUserInfoMapper wechatUserInfoMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private WecomUserInfoMapper wecomUserInfoMapper;
    @Resource
    private WechatService wechatService;

    @Data
    private static class JobParams {
        List<String> userIds = Collections.emptyList();
    }


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 获取参数
        JobParams jobParams = JSONObject.parseObject(context.getInstanceParameters(), JobParams.class);

        List<String> userIds = jobParams.userIds;
        log.info("[离职分配]开始离职分配, userIds: {}", userIds);
        userIds.forEach(this::resignedTransferCustomer);
        log.info("[离职分配]离职分配完成, userIds: {}", userIds);

        return new ProcessResult(true);
    }

    private void resignedTransferCustomer(String fromUserId) {
        log.info("[离职分配]开始离职分配, userId: {}", fromUserId);

        // 1. 获取添加了该销售企微的客户列表
        List<WechatUserInfo> wechatUserInfos = wechatUserInfoMapper.selectByUserId(fromUserId);
        if (CollectionUtils.isEmpty(wechatUserInfos)) {
            return;
        }
        // uninoId和externalUserId的对应关系
        Map<String, String> unionIdExternalUserIdMap = wechatUserInfos.stream()
                .filter(wechatUserInfo -> StringUtils.isNotBlank(wechatUserInfo.getUnionid()))
                .filter(wechatUserInfo -> StringUtils.isNotBlank(wechatUserInfo.getExternalUserid()))
                .collect(Collectors.toMap(WechatUserInfo::getUnionid, WechatUserInfo::getExternalUserid));
        List<String> unionIds = new ArrayList<>(unionIdExternalUserIdMap.keySet());

        // 2. 通过wechatUserInfo的unionId,查找这些unionId对应的merchant
        List<MerchantSubAccount> merchantSubAccounts = merchantSubAccountMapper.selectByUnionidIn(unionIds);
        Map<Long, List<String>> mIdUnionIdMap = merchantSubAccounts.stream()
                .collect(Collectors.groupingBy(MerchantSubAccount::getMId, Collectors.mapping(MerchantSubAccount::getUnionid, Collectors.toList())));
        if (mIdUnionIdMap.isEmpty()) {
            return;
        }
        log.info("[离职分配]销售{}的企微客户列表: {}", fromUserId, mIdUnionIdMap.keySet());

        // 3. 查找现在负责这些客户的销售
        List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectByReassignAndMIdIn(false, mIdUnionIdMap.keySet());
        Map<Integer, List<Long>> bdIdMIdMap = followUpRelations.stream()
                .collect(Collectors.groupingBy(FollowUpRelation::getAdminId, Collectors.mapping(FollowUpRelation::getmId, Collectors.toList())));

        // 4. 查找这些销售的企微信息,并调用接口转移客户
        bdIdMIdMap.forEach((bdId, mIdList) -> {
            WecomUserInfo toWeComUserInfo = wecomUserInfoMapper.selectByAdminId(Long.valueOf(bdId));
            if (toWeComUserInfo == null) {
                log.info("[离职分配]销售{}没有企微信息", bdId);
                return;
            }

            if (Objects.equals(toWeComUserInfo.getUserId(), fromUserId)) {
                log.info("[离职分配]门店{}没有分配新销售, 销售id: {}", mIdList, toWeComUserInfo.getAdminId());
                return;
            }

            List<String> custExternalUserIds = mIdList.stream()
                    .map(mIdUnionIdMap::get)
                    .flatMap(Collection::stream)
                    .map(unionIdExternalUserIdMap::get)
                    .collect(Collectors.toList());

            // 调用企微接口
            wechatService.transferCustomerListForResignedBd(fromUserId, toWeComUserInfo.getUserId(), custExternalUserIds);
            log.info("[离职分配]销售{}的客户{}已转移至销售{}", fromUserId, mIdList, toWeComUserInfo.getUserId());
        });
    }
}
