package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmJobConstant;
import net.summerfarm.crm.enums.PerformanceOrderSourceEnum;
import net.summerfarm.crm.mapper.repository.CustMtdPerformanceRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantDetailRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.mapper.repository.performance.CustPerformanceCommRepository;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.domain.CustMtdPerformance;
import net.summerfarm.crm.model.domain.CustPerformanceComm;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 潜力高价值客户任务状态更新
 */
@Component
@Slf4j
public class PotentialHighValueJobStatueUpdateHandler extends XianMuJavaProcessorV2 {

    @Resource
    private CrmJobRepository crmJobRepository;
    @Resource
    private CrmJobMerchantDetailRepository crmJobMerchantDetailRepository;
    @Resource
    private CustMtdPerformanceRepository custMtdPerformanceRepository;
    @Autowired
    private CustPerformanceCommRepository custPerformanceCommRepository;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        // 根据任务名查找需要被更新的任务id.
        // 如果今天是1号, 那么更新的是上个月任务; 否则更新本月任务
        LocalDateTime now = LocalDateTime.now();
        int monthValue = now.getDayOfMonth() == 1 ? now.minusMonths(1).getMonthValue() : now.getMonthValue();
        String jobName = monthValue + CrmJobConstant.POTENTIAL_HIGH_VALUE_CUSTOMER_JOB_POSTFIX;
        CrmJob job = crmJobRepository.lambdaQuery()
                .select(CrmJob::getId)
                .eq(CrmJob::getJobName, jobName)
                .orderByDesc(CrmJob::getId).one();
        if (job == null) {
            log.info("没有找到本月潜在高价值客户任务, jobName: {}", jobName);
            return new ProcessResult(true);
        }

        Long lastId = 0L;
        List<CustMtdPerformance> records;
        do {
            records = custMtdPerformanceRepository.lambdaQuery()
                    .gt(CustMtdPerformance::getId, lastId)
                    .orderByAsc(CustMtdPerformance::getId)
                    .last("limit 1000")
                    .list();

            if (CollectionUtil.isEmpty(records)) {
                break;
            }

            log.info("更新潜在高价值客户任务, 当前处理id: {}", lastId);
            lastId = records.get(records.size() - 1).getId();

            List<Long> custIds = records.stream().map(CustMtdPerformance::getCustId).filter(
                    Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, CustPerformanceComm> custPerformanceCommMap = getCustPerformanceCommMap(custIds);

            List<LambdaUpdateWrapper<CrmJobMerchantDetail>> updateWrappers = records.stream().map(record -> {
                boolean hasAmtFinished = record.getDlvRealAmt().compareTo(new BigDecimal(2000)) >= 0;
                boolean hasSpuFinished = record.getDlvSpuCnt() >= 4;
                String custValueLabel = record.getCustValueLable();
                // 如果绩效二期数据存在, 则以绩效二期数据为准
                CustPerformanceComm custPerformanceComm = custPerformanceCommMap.get(record.getCustId());
                if (custPerformanceComm != null) {
                    hasAmtFinished = "是".equals(custPerformanceComm.getIsCompleteAmt());
                    hasSpuFinished = "是".equals(custPerformanceComm.getIsCompleteSpu());
                    if (StringUtils.isNotEmpty(custPerformanceComm.getCustValueLable())) {
                        custValueLabel = custPerformanceComm.getCustValueLable();
                    }
                }

                return Wrappers.lambdaUpdate(CrmJobMerchantDetail.class)
                        .set(CrmJobMerchantDetail::getStatus, hasAmtFinished && hasSpuFinished ? 1 : 0)
                        .set(CrmJobMerchantDetail::getTotalAmtStatus, hasAmtFinished ? 1 : 0)
                        .set(CrmJobMerchantDetail::getFulfilledQtyStatus, hasSpuFinished ? 1 : 0)
                        .set(CrmJobMerchantDetail::getRealTotalAmt, record.getDlvRealAmt())
                        .set(CrmJobMerchantDetail::getFulfilledQty, record.getDlvSpuCnt())
                        .set(CrmJobMerchantDetail::getHighValueCustomerLabel, custValueLabel)
                        .eq(CrmJobMerchantDetail::getJobId, job.getId())
                        .eq(CrmJobMerchantDetail::getMId, record.getCustId());
            }).collect(Collectors.toList());

            crmJobMerchantDetailRepository.updateBatch(updateWrappers);
        } while (CollectionUtil.isNotEmpty(records));


        return new ProcessResult(true);
    }

    private Map<Long, CustPerformanceComm> getCustPerformanceCommMap(List<Long> custIds) {
        if (CollectionUtil.isEmpty(custIds)) {
            return Collections.emptyMap();
        }
        List<CustPerformanceComm> custPerformanceComms =
                custPerformanceCommRepository.listByCustIds(custIds, PerformanceOrderSourceEnum.XIANMU.getValue());
        return custPerformanceComms.stream()
                .collect(Collectors.toMap(CustPerformanceComm::getCustId, Function.identity(), (v1, v2) -> v1));
    }

}
