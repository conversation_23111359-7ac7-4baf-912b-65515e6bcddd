package net.summerfarm.crm.task.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.MerchantOperatingStateEnum;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import java.util.List;
import javax.annotation.Resource;

/**
 * 倒闭门店下单后，重新设置为正常门店
 */
@Slf4j
@Component
public class ClosedMerchantReactivateHandler extends XianMuJavaProcessorV2 {

    // 多少天内下单的门店。如未来有需要，可以修改或做成配置项
    private static final Integer DAYS = 1;

    @Resource
    private MerchantMapper merchantMapper;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始处理倒闭门店重新激活任务");

        List<Long> reactivateMIds = merchantMapper.selectClosedAndOrdered(DAYS);
        log.info("共有 {} 家门店需要重新激活", reactivateMIds.size());
        if (CollectionUtils.isNotEmpty(reactivateMIds)) {
            merchantMapper.updateOperatingStateByMids(reactivateMIds, MerchantOperatingStateEnum.OPERATE_STATE.getId());
        }

        log.info("门店重新激活任务完成");
        return new ProcessResult(true);
    }
}
