package net.summerfarm.crm.task.handler;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.enums.FollowRecordEnum;
import net.summerfarm.crm.mapper.manage.ContactMapper;
import net.summerfarm.crm.mapper.offline.CrmBdVisitCustMapper;
import net.summerfarm.crm.mapper.offline.CrmWecomConversationVisitDetailMapper;
import net.summerfarm.crm.model.domain.Contact;
import net.summerfarm.crm.model.domain.CrmBdVisitCust;
import net.summerfarm.crm.model.domain.CrmWecomConversationVisitDetail;
import net.summerfarm.crm.model.vo.FollowUpRecordVO;
import net.summerfarm.crm.service.FollowUpRecordService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;

/**
 * 从企微和销售拜访（EC、七鱼）导入拜访记录
 */
@Slf4j
@Component
public class FollowUpRecordImportHandler extends XianMuJavaProcessorV2 {

    @Data
    private static class JobParam {
        private String dateTag = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        private List<Long> bdIds;
        private Boolean qiyu = true; // 是否从七鱼导入
        private Boolean ec = false; // 是否从EC导入
        private Boolean wecom = true; // 是否从企微导入
    }

    @Resource
    private CrmBdVisitCustMapper crmBdVisitCustMapper;
    @Resource
    private CrmWecomConversationVisitDetailMapper crmWecomConversationVisitDetailMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private FollowUpRecordService followUpRecordService;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        JobParam jobParam = Optional.ofNullable(context.getInstanceParameters())
                .map(params -> JSON.parseObject(params, JobParam.class))
                .orElse(new JobParam());
        log.info("导入拜访记录，参数：{}", jobParam);

        String dateTag = jobParam.getDateTag();
        int successCnt = 0;
        if (jobParam.qiyu) {
            successCnt += importFromCrmBdVisitCust(Integer.valueOf(dateTag), jobParam.getBdIds(), "七鱼电话");
        }
        if (Boolean.TRUE.equals(jobParam.ec)) {
            successCnt += importFromCrmBdVisitCust(Integer.valueOf(dateTag), jobParam.getBdIds(), "EC电话");
        }
        if (jobParam.wecom) {
            successCnt += importFromWeCom(Integer.valueOf(dateTag), jobParam.getBdIds());
        }

        log.info("导入拜访记录完成, 成功导入 {} 条", successCnt);
        return new ProcessResult(true);
    }

    private int importFromCrmBdVisitCust(Integer dateTag, List<Long> bdIds, String visitType) {
        AtomicInteger successCount = new AtomicInteger();
        // 从销售拜访导入
        List<CrmBdVisitCust> bdVisits = crmBdVisitCustMapper.selectEffectiveByDateTagAndTypeAndBdIdIn(dateTag, visitType, bdIds);

        // 批量处理
        CollectionUtil.split(bdVisits, 100).forEach(batch -> {
            List<Long> mIds = batch.stream().map(CrmBdVisitCust::getCustId).collect(Collectors.toList());
            log.info("[销售拜访记录]商户ID: {}，拜访类型：{}", mIds, visitType);
            Map<Long, Contact> mIdContactMap = this.getContactForMerchant(mIds);

            batch.forEach(visit -> {
                Contact contact = mIdContactMap.get(visit.getCustId());
                if (contact == null) {
                    log.info("商户 {} 未找到默认联系方式", visit.getCustId());
                    return;
                }

                FollowUpRecordVO record = new FollowUpRecordVO();
                record.setmId(visit.getCustId());
                record.setContactId(Math.toIntExact(contact.getContactId()));
                record.setAdminId(Math.toIntExact(visit.getVisitBdId()));
                record.setAdminName(visit.getVisitBdName());
                record.setAddTime(Date.from(visit.getVisitTime().atZone(ZoneId.systemDefault()).toInstant()));
                record.setStatus(FollowRecordEnum.Status.WAIT_DELIVERY.ordinal());

                record.setFollowUpWay("普通拜访-电话");
                record.setCondition(StringUtils.isEmpty(visit.getVisitRemarks()) ? "[暂无记录]" : visit.getVisitRemarks());

                if (this.saveRecord(record)) {
                    successCount.getAndIncrement();
                }
            });
        });
        return successCount.get();
    }

    private int importFromWeCom(Integer dateTag, List<Long> bdIds) {
        AtomicInteger successCount = new AtomicInteger();

        List<CrmWecomConversationVisitDetail> wecomVisits = crmWecomConversationVisitDetailMapper.selectValidByDateTagAndBdIdIn(dateTag, bdIds);

        CollectionUtil.split(wecomVisits, 100).forEach(batch -> {
            List<Long> mIds = batch.stream().map(CrmWecomConversationVisitDetail::getMId).collect(Collectors.toList());
            log.info("[企微拜访记录]商户ID: {}", mIds);
            Map<Long, Contact> mIdContactMap = this.getContactForMerchant(mIds);

            batch.forEach(visit -> {
                Contact contact = mIdContactMap.get(visit.getMId());
                if (contact == null) {
                    log.info("商户 {} 未找到联系方式", visit.getMId());
                    return;
                }

                FollowUpRecordVO record = new FollowUpRecordVO();
                record.setmId(visit.getMId());
                record.setContactId(Math.toIntExact(contact.getContactId()));
                record.setAdminId(Math.toIntExact(visit.getBdId()));
                record.setAdminName(visit.getBdName());
                record.setAddTime(Date.from(visit.getConversationStartTime().atZone(ZoneId.systemDefault()).toInstant()));
                record.setStatus(FollowRecordEnum.Status.WAIT_DELIVERY.ordinal());

                record.setFollowUpWay("普通拜访-企微");
                List<String> conversationList = JSON.parseArray(visit.getConversationList(), String.class);
                String conversation = CollectionUtil.isEmpty(conversationList) ? "[暂无记录]" : String.join("\n", conversationList);
                record.setCondition(conversation);
                List<String> imageList = JSON.parseArray(visit.getImageList(), String.class);
                record.setFollowUpPic(String.join(";", CollUtil.sub(imageList, 0, 9))); // 最多9张图片

                if (this.saveRecord(record)) {
                    successCount.getAndIncrement();
                }
            });
        });
        return successCount.get();
    }

    private boolean saveRecord(FollowUpRecordVO record) {
        try {
            AjaxResult ajaxResult = followUpRecordService.saveRecord(record, true);
            if (!ajaxResult.isSuccess()) {
                log.info("保存拜访记录失败，m_id: {}, message: {}", record.getmId(), ajaxResult.getMsg());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.info("保存拜访记录失败，m_id: {}", record.getmId(), e);
            return false;
        }
    }

    private Map<Long, Contact> getContactForMerchant(List<Long> mIds) {
        if (CollectionUtils.isEmpty(mIds)) {
            return new HashMap<>();
        }

        return contactMapper.selectByMidIn(mIds)
                .stream()
                .collect(Collectors.toMap(
                        Contact::getmId,
                        Function.identity(),
                        BinaryOperator.maxBy(
                                Comparator.comparingInt(Contact::getIsDefault).reversed()  // 先选默认地址
                                        .thenComparingLong(Contact::getContactId))
                ));
    }
}
