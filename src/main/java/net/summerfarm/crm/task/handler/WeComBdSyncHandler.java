package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.WeComEnum;
import net.summerfarm.crm.facade.MerchantAccountQueryFacade;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.mapper.manage.WechatUserInfoMapper;
import net.summerfarm.crm.mapper.manage.WecomUserInfoMapper;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.domain.WechatUserInfo;
import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.service.WechatService;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * BD离职或手动分配导致的客户被转移至其他BD后, 同步客户的企微至新BD的企微
 */
@Slf4j
@Component
public class WeComBdSyncHandler extends XianMuJavaProcessorV2 {

    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private MerchantAccountQueryFacade merchantAccountQueryFacade;
    @Resource
    private WechatUserInfoMapper wechatUserInfoMapper;
    @Resource
    private WecomUserInfoMapper wecomUserInfoMapper;
    @Resource
    private WechatService wechatService;

    @Data
    private static class JobParams {
        String taskTime = LocalDateTime.now().minusHours(72).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 限定需要同步的BD, 为空则同步所有
        List<Integer> bdIds = Collections.emptyList();
    }

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 获取参数
        JobParams jobParams = Optional.ofNullable(context.getInstanceParameters())
                .map(params -> JSONObject.parseObject(params, JobParams.class))
                .orElseGet(JobParams::new);

        // 同步客户企微信息
        log.info("[企微同步]开始同步客户企微信息, taskTime: {}", jobParams.taskTime);
        syncWeComBd(jobParams.taskTime, jobParams.bdIds);
        log.info("[企微同步]同步客户企微信息完成, taskTime: {}", jobParams.taskTime);

        return new ProcessResult(true);
    }

    private void syncWeComBd(String taskTime, List<Integer> bdIds) {
        // 查询followUpRelation表中客户被变更负责人的记录
        Map<Integer, List<FollowUpRelation>> adminGroups = followUpRelationMapper
                .selectPrivateSeaByReassignTimeAndBdIds(taskTime, bdIds)
                .stream().collect(groupingBy(FollowUpRelation::getAdminId));

        log.info("[企微同步]涉及adminIds: {}", adminGroups.keySet().stream().sorted().collect(Collectors.toList()));

        adminGroups.forEach((adminId, relations) -> {
            processBatch(relations);
            log.info("已成功处理adminId: {}，共{}条", adminId, relations.size());
        });
    }

    private void processBatch(List<FollowUpRelation> relations) {
        List<Long> mIds = relations.stream().map(FollowUpRelation::getmId).distinct().collect(Collectors.toList());

        // 1. 根据mId,查询门店所有员工的企微unionId
        // Map<mId, List<UnionId>>, 一个店铺下有多名员工
        Map<Long, List<String>> mIdUnionIdMap = merchantAccountQueryFacade
                .getAllAccountList(mIds).stream()
                .filter(acc -> StringUtils.hasText(acc.getUnionId()))
                .collect(groupingBy(MerchantStoreAccountResultResp::getMId,
                        Collectors.mapping(MerchantStoreAccountResultResp::getUnionId, Collectors.toList())));
        if (mIdUnionIdMap.isEmpty()) {
            log.info("[企微同步]没有需要同步的客户企微信息");
            return;
        }

        // 2. 查询WechatUserInfo表中,bd和客户企微信息的对应关系
        // Map<客户unionId, List<WechatUserInfo>>, 一个客户可能添加多个bd的企微
        Map<String, List<WechatUserInfo>> wechatMap = wechatUserInfoMapper.selectActiveByUnionIds(mIdUnionIdMap.values()
                        .stream().flatMap(Collection::stream).collect(Collectors.toList()))
                .stream().collect(groupingBy(WechatUserInfo::getUnionid));

        // 3. 对比followUpRelations和wechatUserInfos中bd(adminId)对应的客户信息一不一致,不一致则需要同步
        this.createSyncMap(relations, wechatMap, mIdUnionIdMap).forEach(this::transferCustomerToNewBd);
    }

    private Map<Map.Entry<WechatUserInfo, String>, List<String>> createSyncMap(List<FollowUpRelation> followUpRelations,
                                                                               Map<String, List<WechatUserInfo>> custUnionIdWechatUserInfoMap,
                                                                               Map<Long, List<String>> mIdUnionIdMap) {
        Map<Map.Entry<WechatUserInfo, String>, List<String>> userIdFromToAndExternalUserIdMap = new HashMap<>(followUpRelations.size());

        followUpRelations.forEach(followUpRelation -> {

            Long mId = followUpRelation.getmId();

            // 根据mId查询门店下的员工信息
            List<String> unionIds = mIdUnionIdMap.getOrDefault(mId, Collections.emptyList());
            if (unionIds.isEmpty()) {
                log.info("[企微同步]没有找到客户的企微信息, mId: {}", mId);
                return;
            }

            // 一个店铺下有多名员工, 为每个员工同步客户的企微信息
            unionIds.forEach(unionId -> {
                // -- followUpRelation表中客户对应的bd
                Long followingBdId = followUpRelation.getAdminId().longValue();
                WecomUserInfo followingBdWeComInfo = this.getWeComUserInfoByAdminId(followingBdId);
                if (followingBdWeComInfo == null) {
                    log.info("[企微同步]获取新bd的企微用户id失败, 新bd adminId: {}", followingBdId);
                    return;
                }
                String followingBdWecomUserId = followingBdWeComInfo.getUserId();

                // -- wechatUserInfo表中客户已经加了的bd的微信id
                List<WechatUserInfo> addedBdWechatList = custUnionIdWechatUserInfoMap.getOrDefault(unionId, Collections.emptyList());
                List<String> addedBdWechatUserIdList = addedBdWechatList.stream().map(WechatUserInfo::getUserId).collect(Collectors.toList());
                if (addedBdWechatList.isEmpty()) {
                    log.info("[企微同步]没有找到客户与旧BD的企微绑定关系, mId: {}, unionId: {}", mId, unionId);
                    return;
                }

                // 如果客户没有加过新bd的企微, 则需要同步
                if (!addedBdWechatUserIdList.contains(followingBdWecomUserId)) {
                    // 任选一个旧bd的企微, 迁移到新bd的企微
                    // union_id和external_user_id是一一对应的,所以取第一个即可
                    userIdFromToAndExternalUserIdMap
                            .computeIfAbsent(new AbstractMap.SimpleEntry<>(addedBdWechatList.get(0), followingBdWecomUserId), k -> new ArrayList<>())
                            .add(addedBdWechatList.get(0).getExternalUserid());
                }
            });
        });

        return userIdFromToAndExternalUserIdMap;
    }

    /**
     * 调用接口,更新客户企微的BD信息.
     */
    private void transferCustomerToNewBd(Map.Entry<WechatUserInfo, String> userFromTo, List<String> externalUserIds) {
        WechatUserInfo fromUser = userFromTo.getKey();
        String fromUserId = fromUser.getUserId();
        WecomUserInfo fromUserWeCom = this.getWeComUserInfoByAdminId(fromUser.getAdminId());
        String toUserId = userFromTo.getValue();

        if (fromUserWeCom == null) {
            log.info("[企微同步]获取旧bd的企微用户id失败, 旧bd adminId: {}", fromUser.getAdminId());
            return;
        }

        if (WeComEnum.Status.QUIT.getCode().equals(fromUserWeCom.getStatus())) {
            log.info("[企微同步]旧bd已离职,开始离职继承, fromUserId: {}, toUserId: {}, externalUserId: {}", fromUserWeCom.getUserId(), toUserId, externalUserIds);
            wechatService.transferCustomerListForResignedBd(fromUserId, toUserId, externalUserIds);
        } else {
            log.info("[企微同步]开始转移客户企微信息, fromUserId: {}, toUserId: {}, externalUserId: {}", fromUserId, toUserId, externalUserIds);
            wechatService.transferCustomerList(fromUserId, toUserId, externalUserIds);
        }
    }

    @InMemoryCache
    private WecomUserInfo getWeComUserInfoByAdminId(Long adminId) {
        return wecomUserInfoMapper.selectByAdminId(adminId);
    }
}
