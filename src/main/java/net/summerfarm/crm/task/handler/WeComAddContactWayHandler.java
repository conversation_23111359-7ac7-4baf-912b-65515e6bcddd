package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.WeComContactWayEnum;
import net.summerfarm.crm.mapper.manage.WeComUserContactWayMapper;
import net.summerfarm.crm.mapper.manage.WecomUserInfoMapper;
import net.summerfarm.crm.model.domain.WecomUserInfo;
import net.summerfarm.crm.model.query.wecom.contactway.WeComAddContactWayInput;
import net.summerfarm.crm.service.wecom.WeComContactWayService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WeComAddContactWayHandler extends XianMuJavaProcessorV2 {

    private static final String PARAMETER_START_TIME = "startTime";
    private static final String PARAMETER_END_TIME = "endTime";
    public static final String PARAMETER_CONTACT_WAY_TYPE = "contactWayTypes";

    private static final String REMARK = "批量生成销售“联系我”二维码";

    @Resource
    private WeComContactWayService weComContactWayService;
    @Resource
    private WecomUserInfoMapper wecomUserInfoMapper;
    @Resource
    private WeComUserContactWayMapper weComUserContactWayMapper;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        // 获取参数
        JSONObject jsonObject = JSONObject.parseObject(context.getInstanceParameters());
        String startTime = Optional.ofNullable(jsonObject).map(o -> o.getString(PARAMETER_START_TIME)).orElse(null);
        String endTime = Optional.ofNullable(jsonObject).map(o -> o.getString(PARAMETER_END_TIME)).orElse(null);
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            startTime = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
            endTime = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd 23:59:59"));
        }
        List<String> contactWayTypes = Optional.ofNullable(jsonObject)
                .map(o -> o.getJSONArray(PARAMETER_CONTACT_WAY_TYPE))
                .map(array -> array.toJavaList(String.class))
                .orElse(Collections.emptyList());


        log.info("批量生成销售“联系我”二维码，startTime: {}, endTime: {}", startTime, endTime);
        List<WecomUserInfo> bdList = wecomUserInfoMapper.selectActiveByUpdateTime(startTime, endTime);

        // 如果参数不为空,获取参数里的state.否则获取所有state
        List<WeComContactWayEnum.State> stateList;
        if (CollectionUtil.isNotEmpty(contactWayTypes)) {
            stateList = contactWayTypes.stream().map(WeComContactWayEnum.State::valueOf).collect(Collectors.toList());
        } else {
            stateList = Arrays.asList(WeComContactWayEnum.State.values());
        }

        bdList.forEach(bd -> {
            try {
                setupQrCode(bd, stateList);
            } catch (Exception e) {
                log.error("创建企微联系我二维码失败, bd: {}", bd.getAdminId());
            }
        });
        log.info("批量生成销售“联系我”二维码完成, 共处理{}个销售的二维码", bdList.size());
        return new ProcessResult(true);
    }

    private void setupQrCode(WecomUserInfo bd, List<WeComContactWayEnum.State> stateList) {
        // 为每个销售在每种渠道下生成二维码
        stateList.forEach(state -> {
            WeComAddContactWayInput input = WeComAddContactWayInput
                    .builder()
                    .remark(REMARK)
                    .state(state.getState())
                    .userId(bd.getUserId())
                    .build();
            weComContactWayService.addContactWay(input);
        });
    }
}
