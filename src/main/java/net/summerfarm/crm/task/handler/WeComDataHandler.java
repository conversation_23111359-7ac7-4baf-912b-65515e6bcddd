package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.service.WeComDataBoardService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 企微用户状态&客户沟通&营销任务数据同步
 *
 * <AUTHOR>
 * @date 2024/2/23 10:28
 */
@Slf4j
@Component
public class WeComDataHandler extends XianMuJavaProcessorV2 {
    @Resource
    private WeComDataBoardService dataBoardService;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("企微用户状态&客户沟通&营销任务数据同步");
        // 同步用户状态
        dataBoardService.weComStateSync();

        // 同步客户沟通数据
        dataBoardService.userBehaviorData();

        // 同步营销任务数据
        dataBoardService.groupMsgData();
        return new ProcessResult(true);
    }
}
