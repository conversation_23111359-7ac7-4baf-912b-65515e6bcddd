package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.service.CrmTaskService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CrmTaskUpdateStatusHandler extends XianMuJavaProcessorV2 {

    @Resource
    CrmTaskService crmTaskService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String instanceParameters = context.getInstanceParameters();
        if (StringUtils.isEmpty(instanceParameters)) {
            return new ProcessResult(true);
        }
        String[] split = instanceParameters.split(";");
        if (split.length == 0) {
            return new ProcessResult(true);
        }
        List<Long> taskIds = Arrays.stream(split).map(Long::valueOf).collect(Collectors.toList());
        crmTaskService.updateTaskUpStatus(taskIds);
        return new ProcessResult(true);
    }
}
