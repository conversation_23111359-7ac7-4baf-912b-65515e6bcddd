package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.constant.CrmJobConstant;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.mapper.repository.HistoryCustCategoryPerformanceRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantItemRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.HistoryCustCategoryPerformance;
import net.summerfarm.crm.model.dto.crmjob.JobCompletionCriteriaDTO;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobV2DTO;
import net.summerfarm.crm.model.dto.crmjobv2.UpdateJobV2DTO;
import net.summerfarm.crm.service.crmjob.CrmJobServiceV2;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 品类履约任务创建
 */
@Component
@Slf4j
public class CategoryFulfillmentJobCreationHandler extends XianMuJavaProcessorV2 {

    @Resource
    private CrmJobServiceV2 crmJobServiceV2;
    @Resource
    private CrmJobRepository crmJobRepository;
    @Resource
    private CrmJobMerchantItemRepository crmJobMerchantItemRepository;
    @Resource
    private HistoryCustCategoryPerformanceRepository historyCustCategoryPerformanceRepository;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 处理存量客户任务
        processJobByCustomerType("存量", CrmJobConstant.EXISTING_CUSTOMER_CATEGORY_JOB_POSTFIX);

        // 处理增量客户任务
        processJobByCustomerType("新增", CrmJobConstant.NEW_CUSTOMER_CATEGORY_JOB_POSTFIX);

        return new ProcessResult(true);
    }

    /**
     * 根据客户类型处理任务创建和更新
     *
     * @param customerType 客户类型 ("存量" 或 "增量")
     * @param jobPostfix 任务名后缀
     */
    private void processJobByCustomerType(String customerType, String jobPostfix) {
        // 2号开始才创建当月任务
        LocalDateTime now = LocalDateTime.now();
        int monthValue = now.getDayOfMonth() == 1 ? now.minusMonths(1).getMonthValue() : now.getMonthValue();
        String jobName = monthValue + jobPostfix;

        CrmJob job = this.getJobByName(jobName);
        if (job == null) {
            this.createJob(jobName, customerType);
            job = this.getJobByName(jobName);
        }

        Long jobId = job.getId();
        log.info("开始处理{}客户任务创建, jobId: {}, jobName: {}", customerType, jobId, jobName);

        Long lastId = 0L;
        List<HistoryCustCategoryPerformance> records;

        do {
            // 1. 先看下任务品有没有更新. 这里的假设是,每个月同一个品的存量门店列表不会更新,对于存量门店来说只会增加品
            List<String> spu = historyCustCategoryPerformanceRepository.lambdaQuery()
                    .select(HistoryCustCategoryPerformance::getSpuGroup)
                    .eq(HistoryCustCategoryPerformance::getCustType, customerType)
                    .groupBy(HistoryCustCategoryPerformance::getSpuGroup)
                    .list().stream().map(HistoryCustCategoryPerformance::getSpuGroup).collect(Collectors.toList());

            List<String> jobItems = crmJobMerchantItemRepository.queryJobItemByJobIdWithCache(jobId);

            Collection<String> newItems = CollUtil.subtract(spu, jobItems);
            if ("存量".equals(customerType) && CollUtil.isEmpty(newItems)) {
                log.info("存量门店没有新增推广品，不需要更新品类推广任务");
                break;
            }

            // 2. 更新任务品
            records = historyCustCategoryPerformanceRepository.lambdaQuery()
                    .eq(HistoryCustCategoryPerformance::getCustType, customerType)
                    .in("存量".equals(customerType), HistoryCustCategoryPerformance::getSpuGroup, newItems)
                    .gt(HistoryCustCategoryPerformance::getId, lastId)
                    .orderByAsc(HistoryCustCategoryPerformance::getId)
                    .last("limit 1000")
                    .list();
            if (CollUtil.isEmpty(records)) {
                break;
            }
            lastId = records.get(records.size() - 1).getId();

            // 获取拉新的门店列表
            List<Long> newAccountMIds =
                    records.stream().filter(x -> "新增".equals(x.getCustType()) && "拉新".equals(x.getCustTypeDetail()))
                            .map(HistoryCustCategoryPerformance::getCustId).distinct().collect(Collectors.toList());
            // 如果没有拉新门店且没有新增品, 那么就不需要更新品类推广任务
            if (CollUtil.isEmpty(newAccountMIds) && CollUtil.isEmpty(newItems)) {
                continue;
            }

            Map<Long, List<String>> mIdItemMap = records.stream().filter(x -> newAccountMIds.contains(x.getCustId()) || newItems.contains(x.getSpuGroup()))
                    .collect(Collectors.groupingBy(HistoryCustCategoryPerformance::getCustId, Collectors.mapping(HistoryCustCategoryPerformance::getSpuGroup, Collectors.toList())));

            UpdateJobV2DTO dto = new UpdateJobV2DTO();
            dto.setJobId(jobId);
            dto.setMerchantItemMap(mIdItemMap);
            // 构造任务门店标签
            Map<Long, CrmJobEnum.JobMerchantLabel> jobMerchantLabelMap = new HashMap<>();
            records.stream().forEach(x -> {
                if ("新增".equals(x.getCustType())) {
                    if ("拉新".equals(x.getCustTypeDetail())) {
                        jobMerchantLabelMap.put(x.getCustId(), CrmJobEnum.JobMerchantLabel.CATEGORY_FULFILLMENT_INC_NEW_ACCOUNT);
                    } else {
                        jobMerchantLabelMap.put(x.getCustId(), CrmJobEnum.JobMerchantLabel.CATEGORY_FULFILLMENT_INC_OLD_ACCOUNT);
                    }
                }
            });
            dto.setJobMerchantLabelMap(jobMerchantLabelMap);
            crmJobServiceV2.updateJob(dto);

            log.info("为{}个{}客户创建了品类履约任务", mIdItemMap.size(), customerType);
        } while (CollUtil.isNotEmpty(records));

        log.info("完成处理{}客户任务创建, jobId: {}", customerType, jobId);
    }

    private CrmJob getJobByName(String jobName) {
        return crmJobRepository.lambdaQuery()
                .eq(CrmJob::getJobName, jobName)
                .gt(CrmJob::getCreateTime, LocalDateTime.now().minusMonths(2))
                .last("limit 1").one();
    }

    private void createJob(String jobName, String customerType) {
        CreateJobV2DTO dto = new CreateJobV2DTO();
        JobCompletionCriteriaDTO completionCriteria = new JobCompletionCriteriaDTO();
        completionCriteria.setCompletionType(CrmJobEnum.CompletionCriteriaType.CATEGORY_FULFILLMENT.getCode());

        DateTime now = DateUtil.date();
        String description = this.generateJobDescription(customerType, now);

        // 任务开始时间为每月2号
        LocalDateTime startTime = DateUtil.beginOfMonth(now).toLocalDateTime().plusDays(1);
        // 任务结束时间为下月2号 00:00
        LocalDateTime endTime = DateUtil.beginOfMonth(now).toLocalDateTime().plusMonths(1).plusDays(1);

        dto.setJobName(jobName);
        dto.setType(CrmJobEnum.Type.CATEGORY_FULFILLMENT.getCode());
        dto.setDescription(description);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCompletionCriteriaList(Collections.singletonList(completionCriteria));
        dto.setMerchantSelectionType(CrmJobEnum.MerchantSelectionType.MERCHANT_ITEM_MAP.getCode());

        crmJobServiceV2.createJob(dto);
    }

    /**
     * 根据客户类型生成任务描述
     *
     * @param customerType 客户类型
     * @param now 当前时间
     * @return 任务描述
     */
    private String generateJobDescription(String customerType, DateTime now) {
        if ("新增".equals(customerType)) {
            // 增量客户：本月-6、本月-5、本月-4
            return String.format("以下为增量客户品类推广商品%d年%d月、%d月、%d月有过履约的商户，请认真跟进",
                    now.year(),
                    DateUtil.offsetMonth(now, -6).monthBaseOne(),
                    DateUtil.offsetMonth(now, -5).monthBaseOne(),
                    DateUtil.offsetMonth(now, -4).monthBaseOne());
        } else {
            // 存量客户：本月-3、本月-2、本月-1
            return String.format("以下为存量客户品类推广商品%d月、%d月、%d月有过履约的商户，请认真跟进",
                    DateUtil.offsetMonth(now, -3).monthBaseOne(),
                    DateUtil.offsetMonth(now, -2).monthBaseOne(),
                    DateUtil.offsetMonth(now, -1).monthBaseOne());
        }
    }
}
