package net.summerfarm.crm.task.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.crm.mapper.manage.CrmBdOrgMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.vo.FollowUpRelationVO;
import net.summerfarm.crm.service.FollowUpReleaseTimeCompareService;
import net.summerfarm.crm.task.CrmDelayReleaseRule;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户释放时间比对
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FollowUpReleaseTimeCompareProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private CrmBdOrgMapper crmBdOrgMapper;
    @Autowired
    private FollowUpRelationMapper followUpRelationMapper;
    @Autowired
    private FollowUpReleaseTimeCompareService followUpReleaseTimeCompareService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始客户释放时间比对, context:{}", JSON.toJSONString(context));
        // 比对的bdId列表
        List<Integer> bdIds = null;
        // 比对的mId列表
        List<Long> mIds = null;
        // 开始计算首单客户和公转私客户的时间
        LocalDateTime calculateStartTime = LocalDate.of(2016, 6, 19).atStartOfDay();
        List<CrmDelayReleaseRule> delayReleaseRules = new ArrayList<>();
        // 计算其他掉落客户的释放时间时，是否使用最大释放时间，默认为true（取最大释放时间），false为取最小释放时间
        boolean otherCustomUseMaxReleaseDate = true;
        // 计算首单客户和公转私客户的释放时间时，是否使用最大释放时间，默认为true（取最大释放时间），false为取最小释放时间
        boolean firstBuyerAndOpenToPrivateUseMaxReleaseDate = true;
        String parameter = StringUtils.isNotEmpty(context.getInstanceParameters()) ? context.getInstanceParameters() : context.getJobParameters();
        if (StringUtils.isNotEmpty(parameter)) {
            JSONObject paramJSON = JSON.parseObject(parameter);
            if (paramJSON.get("bdIds") != null) {
                bdIds = paramJSON.getJSONArray("bdIds").toJavaList(Integer.class);
            }
            if (paramJSON.get("mIds") != null) {
                mIds = paramJSON.getJSONArray("mIds").toJavaList(Long.class);
            }
            if (paramJSON.get("startTime") != null) {
                calculateStartTime = BaseDateUtils.stringToLocalDateTime(paramJSON.getString("startTime"));
            }
            if (paramJSON.get("delayReleaseRules") != null) {
                delayReleaseRules = paramJSON.getJSONArray("delayReleaseRules").toJavaList(CrmDelayReleaseRule.class);
            }
            if (paramJSON.get("otherCustomUseMaxReleaseDate") != null) {
                otherCustomUseMaxReleaseDate = paramJSON.getBoolean("otherCustomUseMaxReleaseDate");
            }
            if (paramJSON.get("firstBuyerAndOpenToPrivateUseMaxReleaseDate") != null) {
                firstBuyerAndOpenToPrivateUseMaxReleaseDate = paramJSON.getBoolean("firstBuyerAndOpenToPrivateUseMaxReleaseDate");
            }
        }

        // 如果指定了mIds则忽略bdIds参数
        if (CollectionUtils.isNotEmpty(mIds)) {
            List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectByReassignAndMIdIn(Boolean.FALSE, mIds);
            for (FollowUpRelation followUpRelation : followUpRelations) {
                followUpReleaseTimeCompareService.compareReleaseTimeByBdAndMids(followUpRelation.getAdminId(), Lists.newArrayList(followUpRelation), calculateStartTime, delayReleaseRules, otherCustomUseMaxReleaseDate, firstBuyerAndOpenToPrivateUseMaxReleaseDate);
            }
            log.info("客户释放时间比对完成");
            return new ProcessResult(true);
        }

        if (CollectionUtils.isEmpty(bdIds)) {
            // 未指定bdId列表时比对全部bd
            bdIds = crmBdOrgMapper.listAllBdId();
        }
        for (Integer bdId : bdIds) {
            this.compareReleaseTimeByBd(bdId, calculateStartTime, delayReleaseRules, otherCustomUseMaxReleaseDate, firstBuyerAndOpenToPrivateUseMaxReleaseDate);
        }

        log.info("客户释放时间比对完成");
        return new ProcessResult(true);
    }

    private void compareReleaseTimeByBd(Integer bdId, LocalDateTime calculateStartTime, List<CrmDelayReleaseRule> delayReleaseRules, boolean otherCustomUseMaxReleaseDate, boolean firstBuyerAndOpenToPrivateUseMaxReleaseDate) {
        try {
            // 分页查询销售的私海客户
            FollowUpRelationVO query = new FollowUpRelationVO();
            query.setAdminId(bdId);
            query.setReassign(Boolean.FALSE);
            int count = followUpRelationMapper.countByAreaNo(query);
            if (count == 0) {
                log.info("该销售没有私海客户, bdId:{}", bdId);
                return;
            }
            int pageSize = 100;
            int pages = (int) Math.ceil(count / (double) pageSize);
            for (int i = 1; i <= pages; i++) {
                query.setOffset((i - 1) * pageSize);
                query.setOffSize(pageSize);
                List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectByAreaNo(query);
                if (CollectionUtils.isEmpty(followUpRelations)) {
                    break;
                }
                // 根据bdId和客户id列表比对释放时间
                followUpReleaseTimeCompareService.compareReleaseTimeByBdAndMids(bdId, followUpRelations, calculateStartTime, delayReleaseRules, otherCustomUseMaxReleaseDate, firstBuyerAndOpenToPrivateUseMaxReleaseDate);
            }
            log.info("该销售的所有客户释放时间比对完成，bdId:{}", bdId);
        } catch (Exception ex) {
            log.error("比对客户释放时间失败，bdId:{}", bdId, ex);
        }
    }

}
