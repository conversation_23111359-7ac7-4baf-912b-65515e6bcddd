package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.xianmu.common.exception.BizException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.util.DateUtils.NUMBER_DATE_FORMAT_TWO;
import static net.summerfarm.crm.common.util.DateUtils.YEAR_MONTH;

@Slf4j
@Component
public class CheckOfflineHandler extends XianMuJavaProcessorV2 {
    private static final int MONTH_DATE_LENGTH = 6;

    @Resource
    DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        List<String> tables = Arrays.stream(DataSynchronizationInformationEnum.values()).map(DataSynchronizationInformationEnum::getTableName).collect(Collectors.toList());
        List<DataSynchronizationInformation> information = dataSynchronizationInformationMapper.selectByTableNames(tables, null);
        List<DataSynchronizationInformation> collect = information.stream().filter(it -> !suspected(it)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            List<String> names = collect.stream().map(DataSynchronizationInformation::getTableName).collect(Collectors.toList());
            throw new BizException("离线任务异常：" + String.join(",", names));
        }
        return new ProcessResult(true);
    }

    private Boolean suspected(DataSynchronizationInformation dataSynchronizationInformation) {
        if (dataSynchronizationInformation == null) {
            return true;
        }
        Integer dateFlag = dataSynchronizationInformation.getDateFlag();
        if (isMonthUpdate(dateFlag)) {
            return checkMonthUpdate(dateFlag);
        } else {
            return checkDayUpdate(dateFlag);
        }
    }

    private boolean isMonthUpdate(Integer dateFlag) {
        return String.valueOf(dateFlag).length() <= MONTH_DATE_LENGTH;
    }

    private boolean checkMonthUpdate(Integer dateFlag) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime lastMonth = currentDateTime.minusMonths(1);
        String formattedDateTime = lastMonth.format(DateTimeFormatter.ofPattern(YEAR_MONTH));
        return Objects.equals(formattedDateTime, dateFlag.toString());
    }

    private boolean checkDayUpdate(Integer dateFlag) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        currentDateTime = currentDateTime.minusDays(1L);
        String formattedDateTime = currentDateTime.format(DateTimeFormatter.ofPattern(NUMBER_DATE_FORMAT_TWO));
        try {
            return dateFlag >= Integer.parseInt(formattedDateTime);
        } catch (NumberFormatException e) {
            // 处理异常情况，例如记录日志或返回一个默认值
            return false;
        }
    }

}
