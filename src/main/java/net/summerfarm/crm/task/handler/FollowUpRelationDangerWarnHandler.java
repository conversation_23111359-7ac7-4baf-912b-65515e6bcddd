package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.enums.DingTalkMsgEnum;
import net.summerfarm.crm.enums.FollowUpRelationEnum;
import net.summerfarm.crm.mapper.manage.CrmBdOrgMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.model.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.service.DingTalkMsgSender;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static net.summerfarm.crm.enums.BdAreaConfigEnum.SaleRank.BD;

@Slf4j
@Component
public class FollowUpRelationDangerWarnHandler extends XianMuJavaProcessorV2 {

    @Resource
    private FollowUpRelationMapper followUpRelationMapper;
    @Resource
    private CrmBdOrgMapper crmBdOrgMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Autowired
    private CrmConfig crmConfig;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        // 即将掉落1.3.7天的客户飞书消息提醒
        log.info("即将掉落的商户飞书消息提醒");
        this.sendWarningToBd(Arrays.asList(1, 3, 7));
        log.info("即将掉落的商户飞书消息提醒结束");

        return new ProcessResult(true);
    }

    private void sendWarningToBd(List<Integer> dangerDaysToWarn) {
        final String firstTimeBuyerTitle = "【重点客户-首单客户】";
        final String openToPrivateTitle = "【重点客户-公转私客户】";
        final String otherTitle = "【重点客户-其他掉落客户】";
        final List<String> titleOrder = Arrays.asList(firstTimeBuyerTitle, openToPrivateTitle, otherTitle);

        List<Integer> bdIds = crmBdOrgMapper.selectByMinRank(BD); // 只给普通销售发
        bdIds.forEach(bdId -> {
            // 发送飞书消息
            String msgTitle = "您有即将掉落的商户";
            StringBuffer msg = new StringBuffer(msgTitle).append("\n");

            // 查找即将掉落的商户
            List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectByAdminIdAndDangerDayIn(bdId, dangerDaysToWarn);
            // 过滤掉落保护的客户（这部分客户在掉落保护期后会直接掉落，不需要发送飞书消息通知）
            followUpRelations = followUpRelations.stream().filter(x -> !FollowUpRelationEnum.ProtectReason.getProtectReasonValues().contains(x.getProtectReason())).collect(Collectors.toList());
            log.info("bdId={},即将掉落的商户数量={}", bdId, followUpRelations.size());
            if (CollectionUtil.isEmpty(followUpRelations)) {
                return;
            }

            Map<String, List<FollowUpRelation>> reasonGroup = followUpRelations.stream()
                    .collect(Collectors.groupingBy(relation -> {
                        String reason = relation.getReason();
                        if (FollowUpRelationEnum.DangerDayRule.FIRST_TIME_BUYER.getValue().equals(reason)) {
                            return firstTimeBuyerTitle;
                        } else if (FollowUpRelationEnum.DangerDayRule.OPEN_TO_PRIVATE.getValue().equals(reason)) {
                            return openToPrivateTitle;
                        } else {
                            return otherTitle;
                        }
                    }));

            titleOrder.forEach(title -> {
                List<FollowUpRelation> relations = reasonGroup.get(title);
                if (CollectionUtil.isEmpty(relations)) {
                    return;
                }

                msg.append(title).append("\n");
                // key: 即将掉落天数, value: 客户数量
                Map<Integer, Long> dangerDayCountMap = relations.stream()
                        .collect(Collectors.groupingBy(FollowUpRelation::getDangerDay, Collectors.counting()));
                dangerDayCountMap.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .forEach(entry -> {
                            LocalDateTime releaseDate = LocalDate.now().plusDays(entry.getKey()).atTime(crmConfig.getPrivateSeaReassignTime());
                            msg.append(entry.getValue())
                                    .append("个客户将于")
                                    .append(DateUtil.format(releaseDate, "M月d号H:mm"))
                                    .append("从你的私海掉落\n");
                        });
                msg.append("请及时填写拜访记录/催下单/催履约\n");
            });

            DingTalkMsgReceiverIdBO feiShiMsgBo = new DingTalkMsgReceiverIdBO();
            feiShiMsgBo.setType(DingTalkMsgEnum.MARKDOWN.getType());
            feiShiMsgBo.setTitle(msgTitle);
            feiShiMsgBo.setText(msg.toString());
            feiShiMsgBo.setReceiverIdList(Collections.singletonList(bdId.longValue()));
            dingTalkMsgSender.sendMessageWithFeiShu(feiShiMsgBo);
        });
    }
}
