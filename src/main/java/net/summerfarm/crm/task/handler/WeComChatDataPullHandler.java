package net.summerfarm.crm.task.handler;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.tencent.wework.FinanceClient;
import com.tencent.wework.FinanceFileClient;
import com.tencent.wework.enums.MediaExtEnum;
import com.tencent.wework.model.MediaFileDTO;
import com.tencent.wework.model.WeComChatMessageDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.mapper.manage.ConfigMapper;
import net.xianmu.common.exception.BizException;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.helper.XianMuOssHelper;
import net.xianmu.oss.result.OssUploadResult;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 企业微信会话数据拉取
 */
@Slf4j
@Component
public class WeComChatDataPullHandler extends XianMuJavaProcessorV2 {

    private static final String PARAMETER_SEQ = "seq";
    // 一次拉取的消息条数
    private static final String PARAMETER_LIMIT = "limit";
    // 是否更新数据库里的seq值
    private static final String PARAMETER_UPDATE_SEQ = "updateSeq";
    // 请求次数
    private static final String PARAMETER_REQUEST_LIMIT = "requestLimit";
    // 是否推送到SLS
    private static final String PARAMETER_PUSH_TO_SLS = "pushToSls";

    private static final int LIMIT = 1000;
    private static final int REQUEST_LIMIT = 20;

    @Resource
    private ConfigMapper configMapper;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        // 从context中获取参数
        JSONObject jsonObject = JSONObject.parseObject(context.getInstanceParameters());
        long seq = Optional.ofNullable(jsonObject).map(o -> o.getLong(PARAMETER_SEQ))
                .orElseGet(() -> {
                    // 从config中获取
                    String value = configMapper.selectOne(ConfigValueEnum.WECOM_CHAT_DATA_SEQ.getKey()).getValue();
                    return StringUtils.isBlank(value) ? 0L : Long.parseLong(value);
                });
        int limit = Optional.ofNullable(jsonObject).map(o -> o.getIntValue(PARAMETER_LIMIT))
                .orElse(LIMIT);
        boolean updateSeq = Optional.ofNullable(jsonObject).map(o -> o.getBooleanValue(PARAMETER_UPDATE_SEQ))
                .orElse(true);
        int requestLimit = Optional.ofNullable(jsonObject).map(o -> o.getIntValue(PARAMETER_REQUEST_LIMIT))
                .orElse(REQUEST_LIMIT);
        boolean pushToSls = Optional.ofNullable(jsonObject).map(o -> o.getBooleanValue(PARAMETER_PUSH_TO_SLS))
                .orElse(true);

        // 拉取企微会话数据
        log.info("[企微会话数据拉取] 开始拉取企微会话数据, seq: {}", seq);
        int messageCount = getChatMessage(seq, limit, updateSeq, requestLimit, pushToSls);
        log.info("[企微会话数据拉取] 企微会话数据拉取完成,拉取到的消息数量: {}", messageCount);

        return new ProcessResult(true);
    }

    private int getChatMessage(long seq, int limit, boolean updateSeq, int requestLimit, boolean pushToSls) {
        String corpId = configMapper.selectOne(ConfigValueEnum.WECOM_CORP_ID.getKey()).getValue();
        String secret = configMapper.selectOne(ConfigValueEnum.WECOM_CHAT_DATA_SECRET.getKey()).getValue();
        String privateKey = configMapper.selectOne(ConfigValueEnum.WECOM_CHAT_DATA_PRIVATE_KEY.getKey()).getValue();
        // base64 decode private key
        privateKey = new String(Base64.getDecoder().decode(privateKey));

        // 初始化企微sdk client
        FinanceClient financeClient = FinanceClient.newInstance(corpId, secret, privateKey);
        FinanceFileClient fileClient = FinanceFileClient.newInstance(corpId, secret);


        // 拉取数据
        int messageCount = 0;
        for (int count = 0; count < requestLimit; count++) {
            List<WeComChatMessageDTO> chatMessageList = financeClient.getChatMessageList(seq, limit);
            if (CollectionUtils.isEmpty(chatMessageList)) {
                break;
            }

            chatMessageList.forEach(chatMessage -> {
                // 下载文件并上传到oss
                log.info("解密后的消息:【{}】", chatMessage);
                List<String> urls = handleMedia(chatMessage, fileClient);
                chatMessage.setMediaUrlsOnOss(urls);
            });

            // 发送到SLS
            if (pushToSls) {
                postToSlsLogStore(chatMessageList.stream().map(chatMessage -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("messageContent", JSON.toJSONString(chatMessage));
                    return jsonObject;
                }).collect(Collectors.toList()));
            }

            // 更新seq, seq为之前接口返回的最大seq值
            seq = chatMessageList.stream().map(WeComChatMessageDTO::getSeq).max(Long::compareTo).orElse(seq);
            if (updateSeq) {
                // 更新seq
                log.info("更新seq: {}", seq);
                configMapper.updateValue(ConfigValueEnum.WECOM_CHAT_DATA_SEQ.getKey(), String.valueOf(seq));
            }

            messageCount += chatMessageList.size();
            // 如果返回的数据小于limit, 说明已经拉取完了
            if (chatMessageList.size() < limit) {
                break;
            }
        }

        return messageCount;
    }

    private static final String SLS_LOG_STORE_URL = "https://xianmu-front-end-log.cn-hangzhou.log.aliyuncs.com/logstores/wecom-messages/track?APIVersion=0.6.0";

    private static void postToSlsLogStore(List<JSONObject> logs) {
        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            HttpPost httpPost = new HttpPost(SLS_LOG_STORE_URL);

            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("__logs__", logs);

            httpPost.setEntity(new StringEntity(JSONObject.toJSONString(jsonMap), ContentType.APPLICATION_JSON));
            HttpResponse response = httpClient.execute(httpPost);
            if (200 != response.getStatusLine().getStatusCode()) {
                log.error("发送企微消息到SLS失败了:{}, {}", response.getStatusLine(), logs);
            }
            String slsResponse = EntityUtils.toString(response.getEntity());
            log.info("SLS日志返回结果:{}", slsResponse);

        } catch (Exception e) {
            log.error("发送企微消息到SLS失败了{}", logs, e);
        }
    }


    /**
     * 下载消息中的媒体文件并上传到oss, 返回oss的url
     */
    private List<String> handleMedia(WeComChatMessageDTO chatMessage, FinanceFileClient fileClient) {
        // 混合消息, 可能有多个媒体文件
        if (chatMessage.getMixedBody() != null) {
            return chatMessage.getMixedBody().getItems().stream().map(item -> {
                MediaFileDTO mediaFileDTO = JSONObject.parseObject(item.getContent().toJSONString(), MediaFileDTO.class);
                return downloadMediaAndUploadToOss(fileClient, item.getType(), mediaFileDTO);
            }).filter(StringUtils::hasText).collect(Collectors.toList());
        } else if (chatMessage.getBody() != null) {
            // 普通消息
            MediaFileDTO mediaFileDTO = JSONObject.parseObject(chatMessage.getBody().toJSONString(), MediaFileDTO.class);
            return Collections.singletonList(downloadMediaAndUploadToOss(fileClient, chatMessage.getMsgType(), mediaFileDTO));
        } else {
            log.info("未知消息类型, msg: {}", chatMessage);
            return Collections.emptyList();
        }
    }


    /**
     * 下载媒体文件并上传到oss, 返回oss的url
     *
     * @param fileClient   企微文件下载client
     * @param msgType      消息类型
     * @param mediaFileDTO 媒体文件信息
     * @return oss的url. 如果消息中没有媒体文件, 则返回空字符串
     */
    private String downloadMediaAndUploadToOss(FinanceFileClient fileClient, String msgType, MediaFileDTO mediaFileDTO) {
        String sdkField = mediaFileDTO.getSdkFileId();
        if (StringUtils.isBlank(mediaFileDTO.getSdkFileId())) {
            return "";
        }

        String fileExt = mediaFileDTO.getFileExt();
        fileExt = StringUtils.isBlank(fileExt) ? MediaExtEnum.getFileExt(msgType, mediaFileDTO.getType()) : fileExt;

        String prefix = mediaFileDTO.getMd5sum() + '-' + mediaFileDTO.getFileSize();
        String fileName = prefix + "." + fileExt;


        // 根据md5sum查找oss上是否已经存在, 如果已存在,直接返回oss的url
        List<String> objectOssKeys = OssGetUtil.listObjectsByPrefix(prefix, OSSExpiredLabelEnum.NO_EXPIRATION);
        if (CollectionUtils.isNotEmpty(objectOssKeys)) {
            log.info("文件已存在, md5sum: {}, objectOssKey: {}", mediaFileDTO.getMd5sum(), objectOssKeys.get(0));
            return XianMuOssHelper.generateUrl(objectOssKeys.get(0));
        }

        String file = "wecom_message" + File.separator + fileName;
        File fs = new File(file);
        try {
            // 下载
            boolean downSuccess = fileClient.downloadMediaFile(sdkField, mediaFileDTO.getFileSize(), file);
            if (!downSuccess) {
                return "";
            }
            OssUploadResult uploadResult = OssUploadUtil.upload(
                    fileName,
                    FileUtils.openInputStream(fs),
                    OSSExpiredLabelEnum.NO_EXPIRATION);

            return uploadResult.getUrl();
        } catch (IOException e) {
            throw new BizException("文件上传失败", e);
        } finally {
            try {
                FileUtils.forceDelete(fs);
            } catch (IOException e) {
                log.error("删除文件失败", e);
            }
        }
    }
}
