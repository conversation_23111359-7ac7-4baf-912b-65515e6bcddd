package net.summerfarm.crm.task.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.es.dto.EsMerchantIndexDTO;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.common.util.EsUtil;
import net.summerfarm.crm.common.util.NumberUtils;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.crm.enums.MerchantSizeEnum;
import net.summerfarm.crm.mapper.manage.ConfigMapper;
import net.summerfarm.crm.mapper.manage.FollowUpRelationMapper;
import net.summerfarm.crm.mapper.offline.CrmMerchantDayAttributeMapper;
import net.summerfarm.crm.mapper.offline.CrmMerchantDayLabelMapper;
import net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.crm.model.domain.Config;
import net.summerfarm.crm.model.domain.CrmMerchantDayAttribute;
import net.summerfarm.crm.model.domain.DataSynchronizationInformation;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import net.summerfarm.crm.model.vo.FollowUpRelationVO;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/28 0:04
 */
@Component
@Slf4j
public class FollowUpRelationTaskHandler extends XianMuJavaProcessorV2 {

    private static final Logger LOGGER = LoggerFactory.getLogger(FollowUpRelationTaskHandler.class);

    @Resource
    private FollowUpRelationMapper followUpRelationMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private CrmMerchantDayLabelMapper crmMerchantDayLabelMapper;

    @Resource
    private CrmMerchantDayAttributeMapper crmMerchantDayAttributeMapper;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        LOGGER.info("分布式调度任务开始------调度任务:{}", context.getJobParameters());
        // 自动释放客户至公海
        autoRelease();
        // 更新用户倒计时
        this.updateDangerDay();
        return new ProcessResult(true);
    }


    /**
     * 释放私海客户,更新倒计时
     * 释放规则: 包含配置表中key = RELEASE_RULES中描述的标签
     */
    public void autoRelease() {
        Config config = configMapper.selectOne(ConfigValueEnum.RELEASE_RULES.getKey());
        if (Objects.isNull(config)) {
            LOGGER.error("配置表未更新释放规则标签,请及时处理");
            return;
        }

        // 获取释放规则标签
        String[] ruleLabels = config.getValue().split(CrmGlobalConstant.SEPARATING_SYMBOL);

        // 释放用户至公海
        DataSynchronizationInformation labelData = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_LABEL.getTableName());
        Integer data = Objects.isNull(labelData) ? NumberUtils.INTEGER_ZERO : labelData.getDateFlag();

        for (String ruleLabel : ruleLabels) {
            int size = 1000;
            Long count = crmMerchantDayLabelMapper.selectMidCountByLabelLimit(ruleLabel, data);
            if (count == 0) {
                continue;
            }
            int l = (int) (count / size);
            int offset = 0;
            for (int i = 0; i <= l; i++) {
                Set<Long> midList = crmMerchantDayLabelMapper.selectMidListByLabelLimit(ruleLabel, data, offset, size);
                offset = offset + size;
                if (CollectionUtil.isEmpty(midList)) {
                    LOGGER.info("{} 无 {}的客户", LocalDateTime.now(), ruleLabel);
                    continue;
                }
                followUpRelationMapper.autoRelease(midList, ruleLabel);
            }
        }
    }

    private void updateDangerDay() {
        FollowUpRelationVO followUpRelationQuery = new FollowUpRelationVO();
        followUpRelationQuery.setReassign(Boolean.FALSE);
        DataSynchronizationInformation attributeData = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_MERCHANT_DAY_ATTRIBUTE.getTableName());
        if (Objects.isNull(attributeData)) {
            return;
        }
        int count = followUpRelationMapper.countByAreaNo(followUpRelationQuery);
        if (count == 0) {
            return;
        }
        int size = 1000;
        int l = count / size;
        int offset = 0;
        for (int i = 0; i <= l; i++) {
            followUpRelationQuery.setOffSize(size);
            followUpRelationQuery.setOffset(offset);
            List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectByAreaNo(followUpRelationQuery);
            offset = offset + size;
            if (CollectionUtil.isEmpty(followUpRelations)) {
                continue;
            }
            List<EsMerchantIndexDTO> merchantIndexList = EsUtil.queryByMidsAndSize(followUpRelations.stream().map(FollowUpRelation::getmId).collect(Collectors.toList()), MerchantSizeEnum.SINGGLE_STORE.getValue());
            if (CollectionUtil.isEmpty(merchantIndexList)) {
                continue;
            }
            List<Long> existIdList = merchantIndexList.stream().map(EsMerchantIndexDTO::getmId).collect(Collectors.toList());
            followUpRelations = followUpRelations.stream().filter(fur -> existIdList.contains(fur.getmId())).collect(Collectors.toList());

            updateDangerDay(followUpRelations, attributeData.getDateFlag());
        }
    }


    private void updateDangerDay(List<FollowUpRelation> followUpRelations, Integer dayTag) {
        long notOrder;
        long notFollow;
        long dangerDay = 0;
        String reason = null;
        for (FollowUpRelation followUpRelation : followUpRelations) {
            // 获取客户未下单时间及未拜访时间
            CrmMerchantDayAttribute crmMerchantDayAttribute = crmMerchantDayAttributeMapper.selectByPrimaryKey(followUpRelation.getmId(), dayTag);
            if (Objects.isNull(crmMerchantDayAttribute)) {
                continue;
            }
            notFollow = crmMerchantDayAttribute.getNotVisited();
            notOrder = crmMerchantDayAttribute.getDaysWithoutOrderFollow() == null ? 0 : crmMerchantDayAttribute.getDaysWithoutOrderFollow();
            //15天未跟进且30天未下单
            if (notFollow <= 15 && notOrder <= 30) {
                dangerDay = Math.max(15 - notFollow, 30 - notOrder);
                reason = "15天未跟进且30天未下单";
                //15天未跟进
            } else if (notFollow <= 15) {
                dangerDay = Math.min(15 - notFollow, Math.abs(60 - notOrder));
                reason = "15天未跟进";
                //30天未下单
            } else if (notOrder <= 30) {
                dangerDay = 30 - notOrder;
                reason = "30天未下单";
            }
            FollowUpRelation update = new FollowUpRelation();
            update.setDangerDay((int) dangerDay);
            update.setId(followUpRelation.getId());
            if (reason != null) {
                update.setReason(reason);
            }
            LOGGER.info("开始更新mid:{}的dangerDay:{},notFollow:{},notOrder:{},reason:{}", followUpRelation.getmId(), dangerDay, notFollow, notOrder, reason);
            followUpRelationMapper.updateReassign(update);
        }
    }

}
