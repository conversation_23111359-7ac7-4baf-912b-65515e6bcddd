package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.mapper.manage.OrdersMapper;
import net.summerfarm.crm.service.CrmTaskService;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 省心送触发 crm 拜访任务
 *
 * <AUTHOR>
 * @date 2023/10/11 11:36
 */
@Component
@Slf4j
public class TimingCrmTaskHandle extends XianMuJavaProcessorV2 {
    @Resource
    private CrmTaskService crmTaskService;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        // 省心送到期提醒
        crmTaskService.createTimingTask();
        // 省心送未配送提醒
        crmTaskService.sendTimingRemindMsg();
        return new ProcessResult(true);
    }
}
