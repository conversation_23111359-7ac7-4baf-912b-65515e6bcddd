package net.summerfarm.crm.task.handler.bdTarget;

import net.summerfarm.crm.service.BDTarget.BdVisitPlanIndicatorSyncService;
import net.summerfarm.crm.service.DataSynchronization.DataSynchronizationInformationService;
import org.springframework.stereotype.Component;

import com.alibaba.schedulerx.worker.processor.ProcessResult;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 拜访计划指标同步处理
 *
 * <AUTHOR>
 * @date 2025-09-08
 */
@Slf4j
@Component
public class BdVisitPlanIndicatorSyncHandler extends XianMuJavaProcessorV2 {
    
    @Resource
    private DataSynchronizationInformationService dataSynchronizationInformationService;
    
    @Resource
    private BdVisitPlanIndicatorSyncService bdVisitPlanIndicatorSyncService;
    
    private static final String SYNC_TABLE_NAME = "crm_bd_visit_plan_indicator_sync";
    private static final int PAGE_SIZE = 1000;
    @Override
    public ProcessResult processResult(XmJobInput xmJobInput) throws Exception {
        log.info("开始执行销售拜访计划指标同步任务");
        
        try {
            // 步骤1: 读取同步表标识位，检查是否已同步
            if (!checkSyncStatus()) {
                log.info("同步表数据未准备好，跳过本次同步");
                return new ProcessResult(true);
            }
            
            // 步骤2: 一次性分页处理所有数据（性能优化：避免多次全量查询）
            processAllDataInOnePass();
            
            log.info("销售拜访计划指标同步任务执行完成");
            return new ProcessResult(true);
            
        } catch (Exception e) {
            log.error("销售拜访计划指标同步任务执行失败", e);
            return new ProcessResult(false, "同步任务执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查同步状态
     * @return true-可以同步，false-不能同步
     */
    private boolean checkSyncStatus() {
        try {
            // 生成当前小时级别的时间标识（yyyyMMddhh格式）
            String currentHourFlag = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + 
                    String.format("%02d", LocalTime.now().getHour());
            
            // 使用Service层检查同步状态，传入时间标识
            boolean needSync = dataSynchronizationInformationService.needSync(SYNC_TABLE_NAME, currentHourFlag);
            if (!needSync) {
                log.info("同步表{}在时间标识{}已同步，跳过同步", SYNC_TABLE_NAME, currentHourFlag);
                return false;
            }
            
            log.info("同步表{}状态检查通过，需要同步，时间标识: {}", SYNC_TABLE_NAME, currentHourFlag);
            return true;
            
        } catch (Exception e) {
            log.error("检查同步状态失败", e);
            return false;
        }
    }
    
    /**
     * 一次性分页处理所有数据（性能优化版本）
     * Handler层只负责流程编排，具体业务逻辑委托给Service层
     */
    private void processAllDataInOnePass() {
        try {
            // 委托给Service层处理具体的同步业务逻辑
            bdVisitPlanIndicatorSyncService.processSyncDataInBatch(PAGE_SIZE);
            
        } catch (Exception e) {
            log.error("分页处理同步数据失败", e);
            throw e;
        }
    }
    


}
