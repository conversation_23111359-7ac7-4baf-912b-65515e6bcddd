package net.summerfarm.crm.task.handler;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.resp.merchant.MerchantQueryResp;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.enums.CrmJobEnum;
import net.summerfarm.crm.facade.MerchantQueryFacade;
import net.summerfarm.crm.mapper.manage.OrdersMapper;
import net.summerfarm.crm.mapper.repository.job.CrmJobMerchantDetailRepository;
import net.summerfarm.crm.mapper.repository.job.CrmJobRepository;
import net.summerfarm.crm.model.domain.CrmJob;
import net.summerfarm.crm.model.domain.CrmJobMerchantDetail;
import net.summerfarm.crm.model.domain.Orders;
import net.summerfarm.crm.model.dto.crmjob.JobCompletionCriteriaDTO;
import net.summerfarm.crm.model.dto.crmjobv2.CreateJobV2DTO;
import net.summerfarm.crm.model.dto.crmjobv2.UpdateJobV2DTO;
import net.summerfarm.crm.model.dto.followUpRelation.LastOrderDTO;
import net.summerfarm.crm.service.crmjob.CrmJobServiceV2;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户行业属性打标任务创建
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MerchantBusinessJobCreationHandler extends XianMuJavaProcessorV2 {

    private static final int MAX_TIMES_500 = 500;
    private static final List<Integer> ORDER_STATUS_LIST = Lists.newArrayList(2, 3, 6);
    private static final String MERCHANT_BUSINESS_JOB_NAME = "近90天下单客户打标";
    private static final String MERCHANT_BUSINESS_JOB_DESCRIPTION = "近90天有下单的客户，需要完成门店业态和经营规模的信息上传";
    private static final String MERCHANT_BUSINESS_JOB_COMPLETION_VALUE = "完成客户打标";
    private static final LocalDateTime MERCHANT_BUSINESS_JOB_START_TIME = LocalDateTime.of(2025, 8, 21, 0, 0, 0);
    private static final LocalDateTime MERCHANT_BUSINESS_JOB_END_TIME = LocalDateTime.of(2099, 12, 31, 23, 59, 59);

    @Autowired
    private OrdersMapper ordersMapper;
    @Autowired
    private MerchantQueryFacade merchantQueryFacade;
    @Autowired
    private CrmJobRepository crmJobRepository;
    @Autowired
    private CrmJobMerchantDetailRepository crmJobMerchantDetailRepository;
    @Autowired
    private CrmJobServiceV2 crmJobServiceV2;
    @Autowired
    private CrmConfig crmConfig;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始客户行业属性打标任务创建");
        int pageIndex = 1;
        int pageSize = 200;
        LocalDateTime orderTimeBegin = LocalDate.now().minusDays(1L).atStartOfDay();
        LocalDateTime orderTimeEnd = LocalDate.now().atStartOfDay();
        PageInfo<Orders> pageInfo;

        // 分批处理
        do {
            if (pageIndex > MAX_TIMES_500) {
                log.error("\n查询昨日下单客户的循环次数超过{}\n", MAX_TIMES_500);
                break;
            }
            PageHelper.startPage(pageIndex, pageSize);
            List<Orders> orders = ordersMapper.getMidAndAdminIdBetweenOrderTime(orderTimeBegin, orderTimeEnd, ORDER_STATUS_LIST);
            pageInfo = PageInfoHelper.createPageInfo(orders);
            log.info("当前处理第{}页,共{}条数据", pageIndex, orders.size());
            // 给需要打标的昨日下单客户创建打标任务
            this.createMerchantBusinessJob(orders);

            pageIndex++;
        } while (pageInfo.isHasNextPage());

        log.info("结束客户行业属性打标任务创建");
        return new ProcessResult(true);
    }

    private void createMerchantBusinessJob(List<Orders> orders) {
        try {
            if (CollectionUtils.isEmpty(orders)) {
                return;
            }
            // 先过滤掉品牌白名单下的客户
            List<Long> mIds = orders.stream().filter(x -> !crmConfig.getMerchantBusinessAdminIdWhiteList().contains(x.getAdminId()))
                    .map(Orders::getMId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mIds)) {
                return;
            }
            // 再过滤掉已经完成打标的客户
            List<Long> hasMerchantBusinessMIds = new ArrayList<>();
            List<MerchantQueryResp> merchantQueryRespList = merchantQueryFacade.queryMerchantInfoList(mIds);
            if (CollectionUtils.isNotEmpty(merchantQueryRespList)) {
                merchantQueryRespList.stream().filter(x -> StringUtils.isNotEmpty(x.getMainBusinessType()) &&
                                CollectionUtils.isNotEmpty(x.getSideBusinessTypeList()) && x.getMerchantChainType() != null)
                        .map(MerchantQueryResp::getMId).forEach(hasMerchantBusinessMIds::add);
            }
            mIds = mIds.stream().filter(mId -> !hasMerchantBusinessMIds.contains(mId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mIds)) {
                return;
            }
            // 再过滤掉存在打标任务的客户
            List<Long> hasJobMIds = new ArrayList<>();
            List<CrmJob> crmJobs = crmJobRepository.lambdaQuery()
                    .eq(CrmJob::getType, CrmJobEnum.Type.OTHER.getCode())
                    .eq(CrmJob::getSubType, CrmJobEnum.SubType.MERCHANT_BUSINESS.getCode())
                    .list();
            if (CollectionUtils.isNotEmpty(crmJobs)) {
                List<Long> crmJobIds = crmJobs.stream().map(CrmJob::getId).collect(Collectors.toList());
                List<CrmJobMerchantDetail> jobMerchantDetails = crmJobMerchantDetailRepository.lambdaQuery()
                        .select(CrmJobMerchantDetail::getMId)
                        .in(CrmJobMerchantDetail::getJobId, crmJobIds)
                        .in(CrmJobMerchantDetail::getMId, mIds)
                        .list();
                jobMerchantDetails.stream().map(CrmJobMerchantDetail::getMId).forEach(hasJobMIds::add);
            }
            mIds = mIds.stream().filter(mId -> !hasJobMIds.contains(mId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mIds)) {
                return;
            }
            // 给剩下的客户创建打标任务
            doCreateMerchantBusinessIncrementalJob(mIds);
        } catch (Exception ex) {
            log.error("创建客户行业属性打标任务失败", ex);
        }
    }

    private void doCreateMerchantBusinessIncrementalJob(List<Long> mIds) {
        CrmJob crmJob = this.getMerchantBusinessIncrementalJob();
        if (crmJob == null) {
            createMerchantBusinessIncrementalJob();
            crmJob = this.getMerchantBusinessIncrementalJob();
        }

        // 获取首单客户
        List<LastOrderDTO> lastOrderDTOS = ordersMapper.listLastOrderInfo(mIds, ORDER_STATUS_LIST);
        List<Long> firstOrderMIds = lastOrderDTOS.stream().filter(x -> Integer.valueOf(1).equals(x.getOrderCount()))
                .map(LastOrderDTO::getMId).collect(Collectors.toList());
        Map<Long, CrmJobEnum.JobMerchantLabel> jobMerchantLabelMap = new HashMap<>();
        for (Long mId : mIds) {
            if (firstOrderMIds.contains(mId)) {
                jobMerchantLabelMap.put(mId, CrmJobEnum.JobMerchantLabel.MERCHANT_BUSINESS_NEW_ACCOUNT);
            } else {
                jobMerchantLabelMap.put(mId, CrmJobEnum.JobMerchantLabel.MERCHANT_BUSINESS_OLD_ACCOUNT);
            }
        }
        UpdateJobV2DTO dto = new UpdateJobV2DTO();
        dto.setJobId(crmJob.getId());
        dto.setMIdList(mIds);
        dto.setJobMerchantLabelMap(jobMerchantLabelMap);
        crmJobServiceV2.updateJob(dto);
    }

    private CrmJob getMerchantBusinessIncrementalJob() {
        return crmJobRepository.lambdaQuery()
                .eq(CrmJob::getJobName, MERCHANT_BUSINESS_JOB_NAME)
                .eq(CrmJob::getType, CrmJobEnum.Type.OTHER.getCode())
                .eq(CrmJob::getSubType, CrmJobEnum.SubType.MERCHANT_BUSINESS.getCode())
                .last("limit 1").one();
    }

    private void createMerchantBusinessIncrementalJob() {
        CreateJobV2DTO dto = new CreateJobV2DTO();
        JobCompletionCriteriaDTO completionCriteria = new JobCompletionCriteriaDTO();
        completionCriteria.setCompletionType(CrmJobEnum.CompletionCriteriaType.CUSTOMIZE.getCode());
        completionCriteria.setCompletionValue(MERCHANT_BUSINESS_JOB_COMPLETION_VALUE);
        dto.setJobName(MERCHANT_BUSINESS_JOB_NAME);
        dto.setType(CrmJobEnum.Type.OTHER.getCode());
        dto.setSubType(CrmJobEnum.SubType.MERCHANT_BUSINESS.getCode());
        dto.setDescription(MERCHANT_BUSINESS_JOB_DESCRIPTION);
        dto.setStartTime(MERCHANT_BUSINESS_JOB_START_TIME);
        dto.setEndTime(MERCHANT_BUSINESS_JOB_END_TIME);
        dto.setCompletionCriteriaList(Collections.singletonList(completionCriteria));
        dto.setMerchantSelectionType(CrmJobEnum.MerchantSelectionType.MERCHANT_LIST.getCode());
        crmJobServiceV2.createJob(dto);
    }

}
