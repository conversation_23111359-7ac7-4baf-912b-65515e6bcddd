package net.summerfarm.crm.task.handler;

import cn.hutool.core.io.FileUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.ConfigValueEnum;
import net.summerfarm.crm.mapper.manage.ConfigMapper;
import net.summerfarm.crm.model.vo.weCom.media.WeComMediaUploadResp;
import net.summerfarm.crm.service.wecom.WeComFileManagementService;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.security.ProviderException;

/**
 * 上传素材至企微
 * 由于素材media_id仅3天有效,如果某素材需要长期使用,需要定时上传
 */
@Slf4j
@Component
public class WeComMediaUploadHandler extends XianMuJavaProcessorV2 {
    
    @Resource
    private WeComFileManagementService weComFileManagementService;
    @Resource
    private ConfigMapper configMapper;

    @Value("${xm.oss.persistent-storage.bucketName}")
    public String bucketName;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        log.info("开始上传素材");
        uploadMallMiniProgramCover();
        log.info("上传结束");

        return new ProcessResult(true);
    }

    /**
     * 上传商城小程序封面图片
     */
    private void uploadMallMiniProgramCover() {
        String ossObjKey = bucketName + ":res/mall_miniprogram_cover.png";
        log.info("ossObjKey: {}", ossObjKey);
        try (InputStream inputStream = OssGetUtil.getInputStream(ossObjKey)) {
            File file = FileUtil.file("mini_program_cover.png");
            FileUtil.writeFromStream(inputStream, file);

            try {
                WeComMediaUploadResp resp = weComFileManagementService.uploadMedia("image", file);
                if (resp.success()) {
                    String mediaId = resp.getMedia_id();
                    configMapper.updateValue(ConfigValueEnum.MALL_MINI_PROGRAM_COVER_MEDIA_ID.getKey(), mediaId);
                } else {
                    log.error("上传商城小程序封面图片失败, msg: {}", resp.getErrmsg());
                    throw new ProviderException("上传商城小程序封面图片失败: " + resp.getErrmsg());
                }
            } finally {
                FileUtil.del(file);
            }
        } catch (IOException e) {
            log.error("处理商城小程序封面图片时发生错误", e);
            throw new RuntimeException("处理商城小程序封面图片时发生错误", e);
        }
    }
}
