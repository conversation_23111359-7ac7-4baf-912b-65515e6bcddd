package net.summerfarm.crm.task;

import net.summerfarm.common.util.Prompt;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description spring事件监听器
 * @date 2022/6/14 2:00
 */
@Component
public class EventAfterInit implements ApplicationListener<ContextRefreshedEvent> {

    protected final Logger logger = LoggerFactory.getLogger(EventAfterInit.class);

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if(event.getApplicationContext().getParent() != null){
            logger.info("防止重复执行");
            return;
        }
        // api地址初始化
        CrmGlobalConstant.apiInit();
        logger.info("--------------使用的商城域名为：{}", CrmGlobalConstant.DOMAIN_NAME);
        logger.info("--------------使用的顶级域名为：{}", CrmGlobalConstant.TOP_DOMAIN_NAME);

        // 加载通用返回信息
        Prompt.processProperties();
        logger.info("容器启动事件执行完成。。。。。");
    }
}
