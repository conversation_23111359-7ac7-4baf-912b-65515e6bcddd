package net.summerfarm.crm.task;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class CrmReleaseTimeCompareResult {

    /**
     * 是否销售主管（M1、M2、M3）
     */
    private boolean isManager;

    /**
     * 是否大客户
     */
    private boolean isMajor;

    /**
     * 是否白名单客户
     */
    private boolean isWhiteList;

    /**
     * 是否POP客户
     */
    private boolean isPop;

    /**
     * 是否绑定销售
     */
    private boolean isBindBd;

    /**
     * 首单客户释放日期
     */
    private LocalDate firstBuyerReleaseDate;

    /**
     * 公转私客户释放日期
     */
    private LocalDate openToPrivateReleaseDate;

    /**
     * 最后一次下单日期
     */
    private LocalDate lastOrderDate;

    /**
     * 最后一次履约日期
     */
    private LocalDate lastFulfillmentFinishDate;

    /**
     * 最后一次拜访日期
     */
    private LocalDate lastFollowUpRecordDate;

    /**
     * 规则1释放日期：30天未下单且未履约，且15天未拜访
     */
    private LocalDate rule1ReleaseDate;

    /**
     * 规则2释放日期：60天未下单且未履约
     */
    private LocalDate rule2ReleaseDate;

    /**
     * 客户标签列表
     */
    private List<Long> labelIds;

    /**
     * 原释放日期
     */
    private LocalDate originalReleaseDate;

    /**
     * 延期掉落的释放日期
     */
    private LocalDate delayReleaseDate;

    /**
     * 保护规则判定后的释放日期
     */
    private LocalDate protectReleaseDate;

    /**
     * BI释放日期
     */
    private LocalDate releaseDateFromBI;

    /**
     * mId
     */
    private Long mId;

    /**
     * bdId
     */
    private Integer bdId;

    public CrmReleaseTimeCompareResult(Long mId, Integer bdId) {
        this.mId = mId;
        this.bdId = bdId;
    }
}
