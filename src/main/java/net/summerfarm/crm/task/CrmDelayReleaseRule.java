package net.summerfarm.crm.task;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 延期掉落规则
 *
 * <AUTHOR>
 */
@Data
public class CrmDelayReleaseRule {

    /**
     * 规则生效的商户标签id列表，为空时对所有商户生效
     */
    private List<Long> labelIds;

    /**
     * 规则起始时间
     */
    private LocalDate ruleStartTime;

    /**
     * 规则结束时间
     */
    private LocalDate ruleEndTime;

    /**
     * 客户释放时间在规则起止时间内时，延期释放的时间
     */
    private LocalDate delayReleaseTime;

}
