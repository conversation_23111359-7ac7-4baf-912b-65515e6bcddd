package net.summerfarm.crm.factory;

import net.summerfarm.crm.service.crmjob.strategy.MerchantJobStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.security.ProviderException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CrmJobMerchantDetailStrategyFactory {

    @Resource
    private List<MerchantJobStrategy> merchantJobStrategies;

    private Map<Integer, MerchantJobStrategy> merchantJobStrategyMap;

    @PostConstruct
    private void init() {
        merchantJobStrategyMap = merchantJobStrategies.stream().collect(
                Collectors.toMap(
                        merchantJobStrategy -> merchantJobStrategy.getMerchantSelectionType().getCode(),
                        merchantJobStrategy -> merchantJobStrategy
                ));
    }

    public MerchantJobStrategy getMerchantJobStrategy(Integer type) {
        return merchantJobStrategyMap.computeIfAbsent(type, key -> {
            throw new ProviderException(String.format("未找到人群关联方式为%s的策略", type));
        });
    }

}
