package net.summerfarm.crm.factory;


import net.summerfarm.crm.mq.CrmBusiness;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 10:03
 */
public class BusinessFactory {
    /**
     * 注册器:存储业务类型与实现类关系
     */
    private Map<String, CrmBusiness> businessMap;

    public void setBusinessMap(Map<String, CrmBusiness> businessMap) {
        this.businessMap = businessMap;
    }

    /**
     * 通过注册好的业务类型,获取实现类对象
     * @param business 业务类型
     * @return 实现类对象
     */
    public CrmBusiness creator(String business){
        return businessMap.get(business);
    }
}
