package net.summerfarm.crm.factory;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.crm.common.constant.CrmGlobalConstant;
import net.summerfarm.crm.mq.annotation.MqMethodHandle;
import org.reflections.Reflections;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/18 17:12
 */
@Slf4j
@Component
public class InitMethodFactory implements InitializingBean {

    @Resource
    private MethodFactory methodFactory;


    /**
     * 仅项目启动时初始化一次
     */
    private void initMethodMap(){
        Map<String, Method> methodMap = new HashMap<>();
        // 反射扫包
        Reflections reflections = new Reflections("net.summerfarm.crm.mq.handle");
        //获取包内带Service注解的类
        Set<Class<?>> typesAnnotatedWith = reflections.getTypesAnnotatedWith(Service.class);
        for (Class clazz : typesAnnotatedWith) {
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                //拿到带自定义注解的方法名,注解参数
                if (method.isAnnotationPresent(MqMethodHandle.class)) {
                    MqMethodHandle mqMethodHandle = method.getAnnotation(MqMethodHandle.class);
                    if(mqMethodHandle != null && StringUtils.isNotBlank(mqMethodHandle.param())){
                        log.info("注解参数为:{}", mqMethodHandle.param());
                        // 将注解参数 与 方法对象初始化
                        methodMap.put(mqMethodHandle.param(),method);
                    }
                }
            }
        }
        methodFactory.setMethodMap(methodMap);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            this.initMethodMap();
        } catch (Exception e) {
            log.error("消费者反射方法初始化失败:{}", e.getMessage(),e);
        }
    }
}
