package net.summerfarm.crm.factory;

import net.summerfarm.crm.service.crmjob.strategy.v2.MerchantJobStrategyV2;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.security.ProviderException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CRM任务门店详情策略工厂V2
 * 与V1的区别：不在CrmJobMerchantDetail中存储item信息
 */
@Component
public class CrmJobMerchantDetailStrategyFactoryV2 {

    @Resource
    private List<MerchantJobStrategyV2> merchantJobStrategiesV2;

    private Map<Integer, MerchantJobStrategyV2> merchantJobStrategyV2Map;

    @PostConstruct
    private void init() {
        merchantJobStrategyV2Map = merchantJobStrategiesV2.stream().collect(
                Collectors.toMap(
                        strategy -> strategy.getMerchantSelectionType().getCode(),
                        strategy -> strategy
                ));
    }

    public MerchantJobStrategyV2 getMerchantJobStrategyV2(Integer type) {
        return merchantJobStrategyV2Map.computeIfAbsent(type, key -> {
            throw new ProviderException(String.format("未找到人群关联方式为%s的V2策略", type));
        });
    }
}
