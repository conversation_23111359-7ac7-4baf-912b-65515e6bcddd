package net.summerfarm.crm.factory;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/18 16:06
 */
@Data
@Slf4j
@Component
public class MethodFactory {

    private Map<String, Method> methodMap;

    public Method getMethod(String mType){
        Method method = methodMap.get(mType);
        if(Objects.isNull(method)){
            log.info("未识别的业务类型：type:{}",mType);
        }
        return method;
    }
}
