package net.summerfarm.crm.factory;

import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-08-26
 * @description
 */
@Component(value = "esClientPoolFactory")
public class EsClientPoolFactory implements PooledObjectFactory<RestHighLevelClient> {
    @Value("${es.url}")
    private String url;
    @Value("${es.port}")
    private Integer port;
    @Value("${es.user-name}")
    private String name;
    @Value("${es.user-pwd}")
    private String pwd;

    @Override
    public PooledObject<RestHighLevelClient> makeObject() throws Exception {
        RestClientBuilder restClientBuilder = RestClient.builder(new HttpHost(url, port, "http"));

        //统一设置请求头
        Header[] defaultHeaders = new Header[]{
                new BasicHeader("header", "value")
        };
        restClientBuilder.setDefaultHeaders(defaultHeaders);

        restClientBuilder.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder.setSocketTimeout(10000));

        //身份认证
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(name, pwd));

        restClientBuilder.setHttpClientConfigCallback(httpClientBuilder -> {
            httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);

            //线程设置
            httpClientBuilder.setDefaultIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(10).build());
            return httpClientBuilder;
        });

        //超时时间设置
        restClientBuilder.setRequestConfigCallback(requestConfigBuilder ->
                requestConfigBuilder
                        .setConnectTimeout(3 * 1000)
                        .setConnectionRequestTimeout(3 * 1000)
                        .setSocketTimeout(60 * 1000));

        RestHighLevelClient client = new RestHighLevelClient(restClientBuilder);
        return new DefaultPooledObject<>(client);
    }

    @Override
    public void destroyObject(PooledObject<RestHighLevelClient> pooledObject) throws Exception {
        RestHighLevelClient client = pooledObject.getObject();
        client.close();
    }

    @Override
    public boolean validateObject(PooledObject<RestHighLevelClient> pooledObject) {
        return true;
    }

    @Override
    public void activateObject(PooledObject<RestHighLevelClient> pooledObject) throws Exception {
    }

    @Override
    public void passivateObject(PooledObject<RestHighLevelClient> pooledObject) throws Exception {

    }
}
