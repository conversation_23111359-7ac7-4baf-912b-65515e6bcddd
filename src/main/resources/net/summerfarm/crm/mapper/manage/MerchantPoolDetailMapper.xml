<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantPoolDetailMapper">


  <sql id="Base_Column_List">
    `id`
    , `pool_info_id`, `m_id`, `size`, `area_no`, `version`, `create_time`, `update_time`
  </sql>




  <select id="countByMidPoolInfoID" resultType="int">
    select count(1)
    from merchant_pool_detail
    where m_id = #{mId}
    and `pool_info_id` = #{poolInfoId}
  </select>
</mapper>