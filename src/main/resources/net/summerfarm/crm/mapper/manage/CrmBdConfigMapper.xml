<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmBdConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="gmv_base" jdbcType="INTEGER" property="gmvBase" />
    <result column="gmv_target" jdbcType="INTEGER" property="gmvTarget" />
    <result column="gmv_target_exclude_at" jdbcType="INTEGER" property="gmvTargetExcludeAT" />
    <result column="month_order_target" jdbcType="INTEGER" property="monthOrderTarget" />
    <result column="private_sea_limit" jdbcType="INTEGER" property="privateSeaLimit" />
    <result column="quota_limit" jdbcType="INTEGER" property="quotaLimit" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="core_merchant_amount" jdbcType="TIMESTAMP" property="coreMerchantAmount" />
    <result column="administrative_city"  property="administrativeCity" />
    <result column="brand_gmv_target"  property="brandGmvTarget" />
  </resultMap>
  <resultMap id="AreaCombinedMap" type="net.summerfarm.crm.model.vo.TableArea">
    <result column="label" jdbcType="VARCHAR" property="label"/>
    <result column="value" jdbcType="VARCHAR" property="value"/>
    <result column="adminId" jdbcType="INTEGER" property="adminId"/>
    <collection property="children" ofType="net.summerfarm.crm.model.vo.TableArea" select="selectBdAreaByAdminIdAndLargeArea" column="{value=value,adminId=adminId}">
      <result column="label" jdbcType="VARCHAR" property="label"/>
      <result column="adminId" jdbcType="VARCHAR" property="adminId"/>
      <result column="value" jdbcType="VARCHAR" property="value"/>
    </collection>
  </resultMap>
  
  <sql id="Base_Column_List">
    id, admin_id, gmv_base, gmv_target, gmv_target_exclude_at, month_order_target, private_sea_limit, quota_limit, type,
    updater, update_time, creator, create_time,core_merchant_amount,administrative_city,brand_gmv_target
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_config
    where id = #{id,jdbcType=INTEGER}
  </select>

    <select id="selectIncentiveIndex" resultType="net.summerfarm.crm.model.vo.CrmBdConfigVo">
      select DISTINCT cbc.id,cbc.admin_id as adminId,a.realname as adminName,cbc.gmv_target as gmvTarget, cbc.gmv_target_exclude_at as gmvTargetExcludeAT, cbc.administrative_city administrativeCity,
        cbc.month_order_target as monthOrderTarget,cbc.private_sea_limit as privateSeaLimit,cbc.quota_limit as quotaLimit,
        cbc.type as type,cbc.core_merchant_amount coreMerchantAmount,cbc.brand_gmv_target brandGmvTarget,cbo.parent_id parentId,cbo.parent_name parentName
        from crm_bd_config cbc
        left JOIN crm_bd_area cba ON cba.admin_id = cbc.admin_id
        left join admin a on cbc.admin_id  = a.admin_id
        left join crm_bd_org cbo on cbc.admin_id= cbo.bd_id and cbo.rank=4
      <where>
        <if test="area != null">
          and cba.area_no in
          <foreach collection="area" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="adminName != null and adminName != ''">
          and a.realname like concat('%', #{adminName}, '%')
        </if>
        <if test="bdCities != null and bdCities.size() > 0">
          and cbc.admin_id in(SELECT bd_id
                              FROM crm_bd_city WHERE (province, city, area) IN (
          <foreach collection="bdCities" item="item" separator=",">
            (#{item.province}, #{item.city}, #{item.area})
          </foreach>)
        )
        </if>
        <if test="cityAdminId != null">
          and cbo.parent_id = #{cityAdminId}
        </if>
      </where>
      order by cbc.id desc
    </select>

  <select id="queryBdName" resultType="net.summerfarm.crm.model.vo.BdVO">
    SELECT distinct a.admin_id as adminId, a.realname as adminName
    FROM admin a
    left join (select distinct admin_id from crm_bd_config) b on a.admin_id = b.admin_id
    WHERE is_disabled = 0
    <if test="baseUserIds != null and baseUserIds.size!=0">
      AND a.base_user_id in
      <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="bdName!=null and bdName!=null">
      and a.realname like concat('%',#{bdName},'%')
    </if>
    <if test="isExist!=null">
      <if test="isExist">
        and b.admin_id is not null
      </if>
      <if test="!isExist">
        and b.admin_id is null
      </if>
    </if>

  </select>
  <select id="selectZoneNameChildrenList" resultType="net.summerfarm.crm.model.vo.ZoneNameListVo">
    select b.area_no as id ,(select area_name  from area a where a.area_no = b.area_no) as value
    from crm_manage_area b where mb_id = #{id}

  </select>
  <select id="selectByAdminId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_bd_config
    where admin_id = #{adminId,jdbcType=INTEGER}
  </select>

  <select id="selectBdArea" resultType="net.summerfarm.crm.model.dto.CrmBdAreaDTO">
    select
      cba.id as id,cba.area_no as areaNo,a.area_name as areaName
    from crm_bd_area cba
    left join area a on cba.area_no = a.area_no
    where cba.admin_id = #{adminId}
  </select>

  <select id="selectBdAreaCombined" resultMap="AreaCombinedMap">
    select distinct la.large_area_no `value`, la.large_area_name label, cba.admin_id adminId
    from crm_bd_area cba
           inner join area a on cba.area_no = a.area_no
           inner join large_area la on la.large_area_no = a.parent_no
    where cba.admin_id = #{adminId}
  </select>

  <select id="selectAdminByAdministrativeCity" resultType="net.summerfarm.crm.model.vo.AdminInfoVo">
    SELECT a.admin_id adminId, a.realname adminName
    FROM crm_bd_config c
    LEFT JOIN admin a ON c.admin_id = a.admin_id
    where (a.is_disabled = 0 OR (a.is_disabled = 1 AND a.update_time > #{updateTime}))
    <if test="administrativeCityList != null and administrativeCityList.size() >0">
      and administrative_city in
      <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
      <if test="bdName!=null and bdName !=''">
        AND a.realname LIKE CONCAT(#{bdName},'%')
      </if>
    </if>
  </select>
  <select id="selectBdAreaByAdminIdAndLargeArea" resultMap="AreaCombinedMap">
    select distinct a.area_no `value`, a.area_name label,cba.admin_id adminId
    from crm_bd_area cba
           left join area a on cba.area_no = a.area_no
    where cba.admin_id = #{adminId} and parent_no=#{value}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_bd_config
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmBdConfig" useGeneratedKeys="true">
    insert into crm_bd_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="gmvTarget != null">
        gmv_target,
      </if>
      <if test="gmvTargetExcludeAT != null">
        gmv_target_exclude_at,
      </if>
      <if test="monthOrderTarget != null">
        month_order_target,
      </if>
      <if test="privateSeaLimit != null">
        private_sea_limit,
      </if>
      <if test="quotaLimit != null">
        quota_limit,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="coreMerchantAmount != null">
        core_merchant_amount,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="administrativeCity != null">
        administrative_city,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="gmvTarget != null">
        #{gmvTarget,jdbcType=INTEGER},
      </if>
      <if test="monthOrderTarget != null">
        #{monthOrderTarget,jdbcType=INTEGER},
      </if>
      <if test="privateSeaLimit != null">
        #{privateSeaLimit,jdbcType=INTEGER},
      </if>
      <if test="quotaLimit != null">
        #{quotaLimit,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coreMerchantAmount != null">
        #{coreMerchantAmount},
      </if>
      <if test="type != null">
        #{type},
      </if>
      <if test="administrativeCity != null">
        #{administrativeCity},
      </if>
    </trim>
  </insert>

  <insert id="copyIncentiveIndex">
    insert into crm_bd_config ( admin_id, gmv_base, gmv_target, gmv_target_exclude_at,
                                month_order_target, private_sea_limit, quota_limit,type,
                                updater, update_time, creator, create_time,administrative_city,brand_gmv_target)
    select #{intInfo}, gmv_base, gmv_target, gmv_target_exclude_at, month_order_target,
           private_sea_limit, quota_limit,type, #{adminId}, sysdate(),
           #{adminId}, sysdate(),administrative_city,brand_gmv_target
    from crm_bd_config where admin_id = #{copyIntInfo}
  </insert>
  <insert id="copyIncentiveIndexLastMonthGmv">
    insert into crm_bd_config ( admin_id, core_merchant_amount, gmv_target, gmv_target_exclude_at,
                                month_order_target, private_sea_limit, quota_limit,type,
                                updater, update_time, creator, create_time,administrative_city,brand_gmv_target)
    select #{intInfo}, #{lastMonthNum}, gmv_target, gmv_target_exclude_at, month_order_target,
           private_sea_limit, quota_limit,type, #{adminId}, sysdate(),
           #{adminId}, sysdate(),administrative_city,brand_gmv_target
    from crm_bd_config where admin_id = #{copyIntInfo}

  </insert>

  <update id="updateByPrimaryKeySelective">
    update crm_bd_config
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="gmvBase != null">
        gmv_base = #{gmvBase,jdbcType=INTEGER},
      </if>
      <if test="gmvTarget != null">
        gmv_target = #{gmvTarget,jdbcType=INTEGER},
      </if>
      <if test="gmvTargetExcludeAT != null">
        gmv_target_exclude_at = #{gmvTargetExcludeAT,jdbcType=INTEGER},
      </if>
      <if test="monthOrderTarget != null">
        month_order_target = #{monthOrderTarget,jdbcType=INTEGER},
      </if>
      <if test="privateSeaLimit != null">
        private_sea_limit = #{privateSeaLimit,jdbcType=INTEGER},
      </if>
      <if test="quotaLimit != null">
        quota_limit = #{quotaLimit,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coreMerchantAmount != null">
        core_merchant_amount = #{coreMerchantAmount},
      </if>
    </set>
    where admin_id in
          <foreach collection="id" open="(" close=")" item="item" separator=",">
            #{item}
          </foreach>
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmBdConfig">
    update crm_bd_config
    set admin_id = #{adminId,jdbcType=INTEGER},
    core_merchant_amount = #{coreMerchantAmount,jdbcType=INTEGER},
      gmv_target = #{gmvTarget,jdbcType=INTEGER},
      gmv_target_exclude_at = #{gmvTargetExcludeAT,jdbcType=INTEGER},
      month_order_target = #{monthOrderTarget,jdbcType=INTEGER},
      private_sea_limit = #{privateSeaLimit,jdbcType=INTEGER},
      quota_limit = #{quotaLimit,jdbcType=INTEGER},
      type = #{type},
      updater = #{updater,jdbcType=INTEGER},
      update_time = sysdate(),
      administrative_city = #{administrativeCity},
      brand_gmv_target = #{brandGmvTarget}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByAdminId" >
    update crm_bd_config
    <set>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="coreMerchantAmount != null">
        core_merchant_amount = #{coreMerchantAmount},
      </if>
      <if test="gmvTarget != null">
        gmv_target = #{gmvTarget,jdbcType=INTEGER},
      </if>
      <if test="gmvTargetExcludeAT != null">
        gmv_target_exclude_at = #{gmvTargetExcludeAT,jdbcType=INTEGER},
      </if>
      <if test="monthOrderTarget != null">
        month_order_target = #{monthOrderTarget,jdbcType=INTEGER},
      </if>
      <if test="privateSeaLimit != null">
        private_sea_limit = #{privateSeaLimit,jdbcType=INTEGER},
      </if>
      <if test="quotaLimit != null">
        quota_limit = #{quotaLimit,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where admin_id = #{adminId}
  </update>
</mapper>