<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmTaskDetailMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmTaskDetail">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="m_id" jdbcType="INTEGER" property="mId"/>
        <result column="bd_id" jdbcType="INTEGER" property="bdId"/>
        <result column="bd_name" jdbcType="VARCHAR" property="bdName"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="task_id" jdbcType="INTEGER" property="taskId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        m_id,
        bd_id,
        bd_name,
        source_id,
        task_id,
        status
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_task_detail
        where id = #{id,jdbcType=INTEGER
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from crm_task_detail
        where id = #{id,jdbcType=INTEGER
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmTaskDetail"
            useGeneratedKeys="true">
        insert into crm_task_detail (create_time, update_time, m_id,
                                     bd_id, bd_name, source_id,
                                     task_id)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{mId,jdbcType=INTEGER },
                #{bdId,jdbcType=INTEGER}, #{bdName,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR},
                #{taskId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.CrmTaskDetail" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into crm_task_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="mId != null">
                m_id,
            </if>
            <if test="bdId != null">
                bd_id,
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name,
            </if>
            <if test="sourceId != null">
                source_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mId != null">
                #{mId,jdbcType=INTEGER},
            </if>
            <if test="bdId != null">
                #{bdId,jdbcType=INTEGER},
            </if>
            <if test="bdName != null and bdName != ''">
                #{bdName,jdbcType=VARCHAR},
            </if>
            <if test="sourceId != null">
                #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmTaskDetail">
        <!--@mbg.generated-->
        update crm_task_detail
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mId != null">
                m_id = #{mId,jdbcType=INTEGER},
            </if>
            <if test="bdId != null">
                bd_id = #{bdId,jdbcType=INTEGER},
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name = #{bdName,jdbcType=VARCHAR},
            </if>
            <if test="sourceId != null">
                source_id = #{sourceId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmTaskDetail">
        update crm_task_detail
        set create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            m_id        = #{mId,jdbcType=INTEGER},
            bd_id       = #{bdId,jdbcType=INTEGER},
            bd_name     = #{bdName,jdbcType=VARCHAR},
            source_id   = #{sourceId,jdbcType=VARCHAR},
            task_id     = #{taskId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="listMidByMIdsAndTaskId" resultType="java.lang.Integer">
        select m_id mId
        from crm_task_detail ctd  where task_id = #{taskId}
                                    and m_id in
        <foreach collection="mIds" open="(" close=")" separator="," item="mId">
            #{mId}
        </foreach>
    </select>

    <insert id="insertBatch">
        insert into crm_task_detail (m_id, bd_id, bd_name,source_id, task_id)
        select m.m_id, fur.admin_id, fur.admin_name,#{sourceId}, #{taskId}
        from merchant m
                 join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
        where m.m_id in
        <foreach collection="insertList" item="mId" open="(" close=")" separator=",">
            #{mId}
        </foreach>
        ON DUPLICATE key update update_time = now()
    </insert>
    <select id="taskExport" resultType="net.summerfarm.crm.model.vo.task.TaskDetailExportVo">
        select ctd.m_id mId, m.mname, m.city, if(ctd.status = 1, '已完成', '未完成') result
        from crm_task_detail ctd
                 join crm_task ct on ctd.task_id = ct.id
                 join merchant m on ctd.m_id = m.m_id
                 left join follow_up_record fur
                           on fur.m_id = ctd.m_id and fur.whether_remark=ct.id
        where ct.id = #{taskId}
    </select>

    <select id="listDetail" resultType="net.summerfarm.crm.model.vo.task.TaskDetailVo">
        SELECT ctd.m_id                     mId,
               ctd.id                       taskDetailId,
               m.mname,
               m.phone,
               ctd.bd_id                    bdId,
               ctd.bd_name                  bdName,
               if(fur.id is not null, 0, 1) followUpFlag,
               ctd.source_id                sourceId
        FROM crm_task_detail ctd
            JOIN merchant m on ctd.m_id=m.m_id
            LEFT JOIN follow_up_record fur on fur.whether_remark = ctd.task_id and ctd.m_id = fur.m_id
        <if test="type == 0">
            JOIN merchant_coupon mc on ctd.source_id = mc.coupon_id and ctd.m_id=mc.m_id
        </if>
        <if test="type == 1">
            join crm_task ct on ctd.task_id = ct.id
        </if>
        where ctd.task_id = #{taskId}
        <if test="bdId != null and bdId.size() != 0">
            AND ctd.bd_id in
            <foreach collection="bdId" item="bdId" open="(" close=")" separator=",">
                #{bdId}
            </foreach>
        </if>
        <if test="type == 0">
            <if test="status == 0">
                AND mc.used = 0
            </if>
            <if test="status == 1">
                AND mc.used = 1
            </if>
        </if>
        <if test="type == 1">
            <if test="status == 0">
                AND fur.add_time is null
            </if>
            <if test="status == 1">
                AND fur.add_time between ct.start_time and ct.end_time
            </if>
        </if>
    </select>

    <select id="listByMIdAndType" resultMap="BaseResultMap">
        select ctd.id,
               ctd.create_time,
               ctd.update_time,
               ctd.m_id,
               ctd.bd_id,
               ctd.bd_name,
               ctd.source_id,
               ctd.task_id
        from crm_task_detail ctd
                 join crm_task ct on ctd.task_id = ct.id
        where ct.type = #{type} and ct.end_time > now()  and ctd.m_id in
        <foreach collection="mIds" open="(" close=")" separator="," item="mId">
            #{mId}
        </foreach>
    </select>

    <select id="listNonNullSourceTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_task_detail ctd
                 join crm_task ct on ctd.task_id = ct.id
        where ct.type = #{type} and ctd.m_id = #{mId} and ct.source_id is not null
    </select>

    <insert id="insertByMid">
        insert into crm_task_detail (m_id, bd_id, bd_name, task_id,source_id)
        select m.m_id, fur.admin_id, fur.admin_name, #{taskId},#{sourceId}
        from merchant m
        join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
        where m.m_id =#{mId}
        ON DUPLICATE key update update_time = now()
    </insert>

    <update id="cancelTask">
        update crm_task set delete_flag=1 where source_id = #{sourceId}
    </update>

    <update id="upsertBatch">
           update crm_task_detail set status = #{status}
           where id in
           <foreach collection="upsertList" item="id" open="(" close=")" separator=",">
               #{id}
           </foreach>
    </update>

    <select id="timingTaskCount" resultType="net.summerfarm.crm.model.vo.task.TimingTaskCountVo">
        select count(1) count
        from crm_task_detail ctd
                 join crm_task ct on ctd.task_id = ct.id
        where type = 1 and ctd.source_id is not null and now() between ct.start_time and ct.end_time
          and ctd.bd_id in
        <foreach collection="bdIds" open="(" close=")" separator="," item="bdId">
            #{bdId}
        </foreach>
    </select>

    <select id="timingTaskDownload" resultType="net.summerfarm.crm.model.vo.task.TimingTaskVo">
        select * from crm_task ct join crm_task_detail ctd
    </select>

    <select id="hasNotFinishTask" resultType="java.lang.Integer">
        select count(1) count
        from crm_task_detail where m_id = #{mId} and task_id = #{taskId} and status = #{status}
    </select>
    <select id="listDetailForVisitTask" resultType="net.summerfarm.crm.model.vo.task.TaskDetailVo">
        SELECT ctd.m_id                     mId,
        ctd.id                       taskDetailId,
        m.mname,
        m.phone,
        ctd.bd_id                    bdId,
        ctd.bd_name                  bdName,
        if(fur.id is not null, 0, 1) followUpFlag,
        ctd.source_id                sourceId,
        ctd.status
        FROM crm_task_detail ctd
        JOIN merchant m on ctd.m_id=m.m_id
        LEFT JOIN follow_up_record fur on fur.whether_remark = ctd.task_id and ctd.m_id = fur.m_id
        join crm_task ct on ctd.task_id = ct.id
        where ctd.task_id = #{taskId} and ctd.status = #{status}
            <if test="bdId != null and bdId.size() != 0">
                AND ctd.bd_id in
                <foreach collection="bdId" item="bdId" open="(" close=")" separator=",">
                    #{bdId}
                </foreach>
            </if>
    </select>

    <select id="listDetailForMId" resultType="net.summerfarm.crm.model.vo.task.TaskDetailVo">
        select
            ctd.id taskDetailId,
            ctd.create_time createTime,
            ctd.update_time updateTime,
            ctd.m_id mId,
            ctd.bd_id bdId,
            ctd.bd_name bdName,
            ctd.source_id sourceId,
            ctd.task_id taskId,
            ctd.status
        from crm_task_detail ctd
        join crm_task ct on ctd.task_id = ct.id
        where ct.type = #{type} and ctd.m_id = #{mId} and ctd.status = #{status}
    </select>


    <select id="selectOverTask" resultType="java.lang.Integer">
        select ctd.id
        FROM crm_task_detail ctd
            JOIN merchant m on ctd.m_id=m.m_id
            JOIN   follow_up_record fur on fur.whether_remark = ctd.task_id and ctd.m_id = fur.m_id
            join crm_task ct on ctd.task_id = ct.id
        where ctd.task_id = #{taskId}
    </select>
</mapper>