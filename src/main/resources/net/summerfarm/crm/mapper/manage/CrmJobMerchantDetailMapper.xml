<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmJobMerchantDetailMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmJobMerchantDetail">
        <!--@mbg.generated-->
        <!--@Table crm_job_merchant_detail-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="job_id" jdbcType="BIGINT" property="jobId"/>
        <result column="m_id" jdbcType="BIGINT" property="mId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="merchant_product_list" jdbcType="LONGVARCHAR" property="merchantProductList"/>
        <result column="follow_up_record_id" jdbcType="INTEGER" property="followUpRecordId"/>
        <result column="merchant_product_cnt" jdbcType="INTEGER" property="merchantProductCnt"/>
        <result column="claiming_status" jdbcType="INTEGER" property="claimingStatus"/>
        <result column="claiming_time" jdbcType="TIMESTAMP" property="claimingTime"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="order_no_list" jdbcType="VARCHAR" property="orderNoList"/>
        <result column="category_id_list" jdbcType="VARCHAR" property="categoryIdList"/>
        <result column="real_total_amt" jdbcType="DECIMAL" property="realTotalAmt"/>
        <result column="fulfilled_qty" jdbcType="INTEGER" property="fulfilledQty"/>
        <result column="fulfilled_qty_status" jdbcType="INTEGER" property="fulfilledQtyStatus"/>
        <result column="total_amt_status" jdbcType="INTEGER" property="totalAmtStatus"/>
        <result column="high_value_customer_label" jdbcType="VARCHAR" property="highValueCustomerLabel"/>
        <result column="job_merchant_label" jdbcType="VARCHAR" property="jobMerchantLabel"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, job_id, m_id, `status`, merchant_product_list, follow_up_record_id,
        merchant_product_cnt, claiming_status, claiming_time, complete_time, order_no_list,
        category_id_list, real_total_amt, fulfilled_qty, fulfilled_qty_status, total_amt_status,
        high_value_customer_label, job_merchant_label
    </sql>
    <sql id="Join_Column_List">
        <!--@mbg.generated-->
        crm_job_merchant_detail.id as crm_job_merchant_detail_id, crm_job_merchant_detail.create_time as
        crm_job_merchant_detail_create_time,
        crm_job_merchant_detail.update_time as crm_job_merchant_detail_update_time, crm_job_merchant_detail.job_id as
        crm_job_merchant_detail_job_id,
        crm_job_merchant_detail.m_id as crm_job_merchant_detail_m_id, crm_job_merchant_detail.`status` as
        crm_job_merchant_detail_status,
        crm_job_merchant_detail.merchant_product_list as crm_job_merchant_detail_merchant_product_list,
        crm_job_merchant_detail.follow_up_record_id as crm_job_merchant_detail_follow_up_record_id,
        crm_job_merchant_detail.merchant_product_cnt as crm_job_merchant_detail_merchant_product_cnt,
        crm_job_merchant_detail.claiming_status as crm_job_merchant_detail_claiming_status,
        crm_job_merchant_detail.claiming_time as crm_job_merchant_detail_claiming_time,
        crm_job_merchant_detail.complete_time as crm_job_merchant_detail_complete_time,
        crm_job_merchant_detail.order_no_list as crm_job_merchant_detail_order_no_list,
        crm_job_merchant_detail.category_id_list as crm_job_merchant_detail_category_id_list,
        crm_job_merchant_detail.real_total_amt as crm_job_merchant_detail_real_total_amt,
        crm_job_merchant_detail.fulfilled_qty as crm_job_merchant_detail_fulfilled_qty,
        crm_job_merchant_detail.fulfilled_qty_status as crm_job_merchant_detail_fulfilled_qty_status,
        crm_job_merchant_detail.total_amt_status as crm_job_merchant_detail_total_amt_status,
        crm_job_merchant_detail.high_value_customer_label as crm_job_merchant_detail_high_value_customer_label,
        crm_job_merchant_detail.job_merchant_label as crm_job_merchant_detail_job_merchant_label
    </sql>
    <resultMap id="JoinResultMap" type="net.summerfarm.crm.model.domain.CrmJobMerchantDetail">
        <!--@mbg.generated-->
        <id column="crm_job_merchant_detail_id" jdbcType="BIGINT" property="id"/>
        <result column="crm_job_merchant_detail_create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="crm_job_merchant_detail_update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="crm_job_merchant_detail_job_id" jdbcType="BIGINT" property="jobId"/>
        <result column="crm_job_merchant_detail_m_id" jdbcType="BIGINT" property="mId"/>
        <result column="crm_job_merchant_detail_status" jdbcType="INTEGER" property="status"/>
        <result column="crm_job_merchant_detail_merchant_product_list" jdbcType="LONGVARCHAR"
                property="merchantProductList"/>
        <result column="crm_job_merchant_detail_follow_up_record_id" jdbcType="INTEGER" property="followUpRecordId"/>
        <result column="crm_job_merchant_detail_merchant_product_cnt" jdbcType="INTEGER" property="merchantProductCnt"/>
        <result column="crm_job_merchant_detail_claiming_status" jdbcType="INTEGER" property="claimingStatus"/>
        <result column="crm_job_merchant_detail_claiming_time" jdbcType="TIMESTAMP" property="claimingTime"/>
        <result column="crm_job_merchant_detail_complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="crm_job_merchant_detail_order_no_list" jdbcType="VARCHAR" property="orderNoList"/>
        <result column="crm_job_merchant_detail_category_id_list" jdbcType="VARCHAR" property="categoryIdList"/>
        <result column="crm_job_merchant_detail_real_total_amt" jdbcType="DECIMAL" property="realTotalAmt"/>
        <result column="crm_job_merchant_detail_fulfilled_qty" jdbcType="INTEGER" property="fulfilledQty"/>
        <result column="crm_job_merchant_detail_fulfilled_qty_status" jdbcType="INTEGER" property="fulfilledQtyStatus"/>
        <result column="crm_job_merchant_detail_total_amt_status" jdbcType="INTEGER" property="totalAmtStatus"/>
        <result column="crm_job_merchant_detail_high_value_customer_label" jdbcType="VARCHAR"
                property="highValueCustomerLabel"/>
        <result column="crm_job_merchant_detail_job_merchant_label" jdbcType="VARCHAR"
                property="jobMerchantLabel"/>
    </resultMap>

    <!--auto generated by MybatisCodeHelper on 2024-09-20-->
    <insert id="insertList" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO crm_job_merchant_detail(
        job_id,
        m_id,
        merchant_product_list,
        merchant_product_cnt
        )VALUES
        <foreach collection="list" index="index" item="element" separator=",">
            (
            #{element.jobId,jdbcType=BIGINT},
            #{element.mId,jdbcType=BIGINT},
            #{element.merchantProductList,jdbcType=LONGVARCHAR},
            #{element.merchantProductCnt,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2024-09-26-->
    <update id="updateFollowUpRecordIdByMIdAndFollowUpRecordIdIsNull">
        update crm_job_merchant_detail
        left join crm_job on crm_job_merchant_detail.job_id = crm_job.id
        set follow_up_record_id=#{updatedFollowUpRecordId,jdbcType=INTEGER}
        where m_id=#{mId,jdbcType=BIGINT} and follow_up_record_id is null and crm_job.business_type=0
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-09-20-->
    <select id="selectMIdByJobId" resultType="java.lang.Long">
        select m_id
        from crm_job_merchant_detail
        where job_id=#{jobId,jdbcType=BIGINT}
    </select>

    <select id="selectByQuery" resultMap="JoinResultMap">
        select
        <include refid="Join_Column_List"/>
        from crm_job_merchant_detail
        join follow_up_relation fur on crm_job_merchant_detail.m_id = fur.m_id
        join merchant m on crm_job_merchant_detail.m_id = m.m_id
        <where>
            <if test="queryDTO.jobId != null">
                and crm_job_merchant_detail.job_id = #{queryDTO.jobId,jdbcType=BIGINT}
            </if>
            <if test="(bdIds != null and bdIds.size() != 0) or (cities != null and cities.size() != 0)">
                and
                (
                <!-- 查询私海门店 -->
                <if test="bdIds != null and bdIds.size() != 0">
                    (fur.reassign = 0 and fur.admin_id in
                    <foreach close=")" collection="bdIds" item="bdId" open="(" separator=",">
                        #{bdId,jdbcType=INTEGER}
                    </foreach>)
                </if>

                <!-- 如果 bdIds 和 cities 都不为空，需要加上 OR -->
                <if test="bdIds != null and bdIds.size() != 0 and cities != null and cities.size() != 0">
                    or
                </if>

                <!-- 查询公海门店 -->
                <if test="cities != null and cities.size() != 0">
                    (fur.reassign = 1 and m.city in
                    <foreach close=")" collection="cities" item="city" open="(" separator=",">
                        #{city,jdbcType=VARCHAR}
                    </foreach>)
                </if>
                )
            </if>
            <if test="queryDTO.followedUp != null">
                <if test="queryDTO.followedUp == true ">
                    and crm_job_merchant_detail.follow_up_record_id is not null
                </if>
                <if test="queryDTO.followedUp == false ">
                    and crm_job_merchant_detail.follow_up_record_id is null
                </if>
            </if>
            <if test="queryDTO.mname != null">
                and m.mname like concat('%', #{queryDTO.mname, jdbcType=VARCHAR}, '%')
            </if>
            <if test="queryDTO.status != null">
                and crm_job_merchant_detail.status = #{queryDTO.status,jdbcType=INTEGER}
            </if>
            <if test="queryDTO.jobMerchantLabel != null">
                and crm_job_merchant_detail.job_merchant_label = #{queryDTO.jobMerchantLabel.value}
            </if>
        </where>
    </select>

    <!-- V2版本：融合XML和MyBatis Plus的查询方法 -->
    <select id="selectMerchantDetailWithItemsByQueryV2" resultMap="JoinResultMap">
        select
        <include refid="Join_Column_List"/>
        from crm_job_merchant_detail
        join follow_up_relation fur on crm_job_merchant_detail.m_id = fur.m_id
        join merchant m on crm_job_merchant_detail.m_id = m.m_id
        left join crm_job_merchant_item cjmi on crm_job_merchant_detail.job_id = cjmi.job_id and
        crm_job_merchant_detail.m_id = cjmi.m_id
        <!-- 坑点: 品类履约任务：根据item筛选条件动态计算status, 不能直接拿merchant_detail.status -->
        <if test="jobType == 6">
            left join (select job_id, m_id, min(status) as status
            from crm_job_merchant_item
            where job_id = #{query.jobId,jdbcType=BIGINT}
            <if test="query.categoryFulfillmentQuery != null
            and query.categoryFulfillmentQuery.itemFilter != null
            and query.categoryFulfillmentQuery.itemFilter.size() > 0">
                and item in
                <foreach close=")" collection="query.categoryFulfillmentQuery.itemFilter" item="item" open="("
                         separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            group by job_id, m_id) AS cf
            on cf.job_id = crm_job_merchant_detail.job_id and cf.m_id = crm_job_merchant_detail.m_id
        </if>
        <where>
            <!-- 基础查询条件 -->
            <if test="query.jobId != null">
                and crm_job_merchant_detail.job_id = #{query.jobId,jdbcType=BIGINT}
            </if>

            <!-- 权限控制：私海和公海的复杂逻辑 -->
            <if test="(bdIds != null and bdIds.size() != 0) or (cities != null and cities.size() != 0)">
                and
                (
                <!-- 查询私海门店 -->
                <if test="bdIds != null and bdIds.size() != 0">
                    (fur.reassign = 0 and fur.admin_id in
                    <foreach close=")" collection="bdIds" item="bdId" open="(" separator=",">
                        #{bdId,jdbcType=INTEGER}
                    </foreach>)
                </if>

                <!-- 如果 bdIds 和 cities 都不为空，需要加上 OR -->
                <if test="bdIds != null and bdIds.size() != 0 and cities != null and cities.size() != 0">
                    or
                </if>

                <!-- 查询公海门店 -->
                <if test="cities != null and cities.size() != 0">
                    (fur.reassign = 1 and m.city in
                    <foreach close=")" collection="cities" item="city" open="(" separator=",">
                        #{city,jdbcType=VARCHAR}
                    </foreach>)
                </if>
                )
            </if>

            <!-- 通用查询条件 -->
            <if test="query.followedUp != null">
                <if test="query.followedUp == true ">
                    and crm_job_merchant_detail.follow_up_record_id is not null
                </if>
                <if test="query.followedUp == false ">
                    and crm_job_merchant_detail.follow_up_record_id is null
                </if>
            </if>
            <if test="query.mname != null">
                and m.mname like concat('%', #{query.mname, jdbcType=VARCHAR}, '%')
            </if>

            <!-- 坑点: 品类履约任务有自己单独一套的状态逻辑 -->
            <if test="query.status != null">
                <choose>
                    <when test="jobType == 6">
                        and cf.status = #{query.status,jdbcType=INTEGER}
                    </when>
                    <otherwise>
                        and crm_job_merchant_detail.status = #{query.status,jdbcType=INTEGER}
                    </otherwise>
                </choose>
            </if>

            <!-- 潜力高价值任务只查询类型为单店的客户 -->
            <if test="jobType != null and jobType == 5">
                and m.size = '单店'
            </if>

            <!-- V2特有的查询条件 -->
            <!-- 潜力高价值客户任务特有条件 -->
            <if test="query.potentialHighValueCustomerQuery != null">
                <if test="query.potentialHighValueCustomerQuery.achievementFilter != null">
                    <if test="query.potentialHighValueCustomerQuery.achievementFilter == 0">
                        and crm_job_merchant_detail.fulfilled_qty_status = 1
                    </if>
                    <if test="query.potentialHighValueCustomerQuery.achievementFilter == 1">
                        and crm_job_merchant_detail.total_amt_status = 1
                    </if>
                </if>
                <if test="query.potentialHighValueCustomerQuery.highValueCustomerFilter != null">
                    <if test="query.potentialHighValueCustomerQuery.highValueCustomerFilter == 0">
                        and crm_job_merchant_detail.high_value_customer_label != '准高价值客户'
                    </if>
                    <if test="query.potentialHighValueCustomerQuery.highValueCustomerFilter == 1">
                        and crm_job_merchant_detail.high_value_customer_label = '准高价值客户'
                    </if>
                </if>
                <if test="query.potentialHighValueCustomerQuery.predictedHighValueCustomerFilter != null">
                    <if test="query.potentialHighValueCustomerQuery.predictedHighValueCustomerFilter == 0">
                        and crm_job_merchant_detail.high_value_customer_label != '预测高价值客户'
                    </if>
                    <if test="query.potentialHighValueCustomerQuery.predictedHighValueCustomerFilter == 1">
                        and crm_job_merchant_detail.high_value_customer_label = '预测高价值客户'
                    </if>
                </if>
            </if>

            <!-- 品类履约任务特有条件 -->
            <if test="query.categoryFulfillmentQuery != null">
                <if test="query.categoryFulfillmentQuery.itemFilter != null and query.categoryFulfillmentQuery.itemFilter.size() > 0">
                    and cjmi.item in
                    <foreach close=")" collection="query.categoryFulfillmentQuery.itemFilter" item="item" open="("
                             separator=",">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="query.categoryFulfillmentQuery.jobMerchantLabel != null">
                    and job_merchant_label = #{query.categoryFulfillmentQuery.jobMerchantLabel.value}
                </if>
            </if>
        </where>
        GROUP BY crm_job_merchant_detail.id,
        crm_job_merchant_detail.job_id,
        crm_job_merchant_detail.m_id,
        crm_job_merchant_detail.real_total_amt,
        crm_job_merchant_detail.fulfilled_qty,
        crm_job_merchant_detail.fulfilled_qty_status,
        crm_job_merchant_detail.total_amt_status,
        crm_job_merchant_detail.high_value_customer_label
        <!-- 排序 -->
        <choose>
            <!-- 潜力高价值客户任务特有排序 -->
            <when test="jobType==5">
                <if test="query.potentialHighValueCustomerQuery != null and query.potentialHighValueCustomerQuery.sortField != null">
                    order by
                    <choose>
                        <when test="query.potentialHighValueCustomerQuery.sortField == 'fulfillmentGmv'">
                            crm_job_merchant_detail.real_total_amt
                        </when>
                        <when test="query.potentialHighValueCustomerQuery.sortField == 'spuCount'">
                            crm_job_merchant_detail.fulfilled_qty
                        </when>
                        <otherwise>
                            crm_job_merchant_detail.id
                        </otherwise>
                    </choose>
                    ${query.potentialHighValueCustomerQuery.sortDirection.name()}
                </if>
            </when>

            <!-- 品类履约任务特有排序 -->
            <when test="jobType==6">
                <if test="query.categoryFulfillmentQuery != null and query.categoryFulfillmentQuery.sortField != null">
                    order by
                    <choose>
                        <when test="query.categoryFulfillmentQuery.sortField == 'hitItemCount'">
                            (select count(1)
                            from crm_job_merchant_item cjmi
                            where cjmi.job_id = #{query.jobId,jdbcType=INTEGER}
                            and cjmi.m_id = crm_job_merchant_detail.m_id)
                        </when>
                        <when test="query.categoryFulfillmentQuery.sortField == 'unfulfilledItemCount'">
                            (select count(1)
                            from crm_job_merchant_item cjmi
                            where cjmi.job_id = #{query.jobId,jdbcType=INTEGER}
                            and cjmi.m_id = crm_job_merchant_detail.m_id
                            and cjmi.status = 0)
                        </when>
                        <when test="query.categoryFulfillmentQuery.sortField == 'fulfilledItemCount'">
                            (select count(1)
                            from crm_job_merchant_item cjmi
                            where cjmi.job_id = #{query.jobId,jdbcType=INTEGER}
                            and cjmi.m_id = crm_job_merchant_detail.m_id
                            and cjmi.status = 1)
                        </when>
                        <otherwise>
                            crm_job_merchant_detail.id
                        </otherwise>
                    </choose>
                    ${query.categoryFulfillmentQuery.sortDirection.name()}
                </if>
            </when>
        </choose>
    </select>
</mapper>