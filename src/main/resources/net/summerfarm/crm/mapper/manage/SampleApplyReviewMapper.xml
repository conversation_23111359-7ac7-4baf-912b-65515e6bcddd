<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.SampleApplyReviewMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.SampleApplyReview">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="sampleId" column="sample_id" jdbcType="INTEGER"/>
            <result property="reviewId" column="review_id" jdbcType="INTEGER"/>
            <result property="reviewName" column="review_name" jdbcType="VARCHAR"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="reviewRemark" column="review_remark" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="SampleApplyMap" type="net.summerfarm.crm.model.domain.SampleApply">
        <id column="sample_id" property="sampleId"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        <result column="m_id" property="mId"/>
        <result column="m_name" property="mName"/>
        <result column="grade" property="grade"/>
        <result column="m_size" property="mSize"/>
        <result column="m_phone" property ="mPhone"/>
        <result column="m_contact" property="mContact"/>
        <result column="area_no" property="areaNo"/>
        <result column="contact_id" property="contactId"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="status" property="status"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="purchase_intention" property="purchaseIntention"/>
        <result column="remark" property="remark"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="store_no" property="storeNo"/>
    </resultMap>

    <resultMap id="VOMap" type="net.summerfarm.crm.model.vo.SampleApplyReviewVO">
        <id column="sample_id" property="sampleId"/>
        <result column="add_time" property="addTime"/>
        <result column="bd_name" property="bdName"/>
        <result column="bd_id" property="bdId" />
        <result column="m_name" property="mName"/>
        <result column="m_id" property="mId"/>
        <result column="m_size" property="mSize"/>
        <result column="m_phone" property ="mPhone"/>
        <result column="grade" property="grade"/>
        <result column="create_id" property="createId"/>
        <result column="m_contact" property="mContact"/>
        <result column="contact_id" property="contactId" />
        <result column="satisfaction" property="satisfaction" />
        <result column="purchase_intention" property="purchaseIntention"/>
        <result column="remark" property="remark"/>
        <result column="area_name" property="areaName"/><!-- 城市名称-->
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="address" property="address"/>
        <result column="house_number" property="houseNumber"/>
        <result column="area_no" property="areaNo"/>
        <result column="status" property="status"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="store_no" property="storeNo"/>
        <result column="create_name" property="createName"/>
        <result column="update_time" property="updateTime"/>
        <result column="review_name" property="reviewName"/>
        <result column="review_remark" property="reviewRemark"/>
        <result column="audit_time" property="auditTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sample_id,review_id,
        review_name,audit_time,status,
        review_remark
    </sql>

    <select id="selectByBdIds" resultMap="VOMap">
        select sa.sample_id,sa.update_time,sa.create_name,sa.m_name,sa.grade,sa.m_size,sa.m_phone,sa.m_contact,sa.bd_name,sa.status,
        sa.delivery_time,sar.review_name,sar.audit_time,sar.review_remark,a.area_name,sa.area_no,
        c.province, c.city, c.area, c.address,c.house_number
        from sample_apply sa
        left join area a on a.area_no = sa.area_no
        left join contact c on c.contact_id = sa.contact_id
        left join sample_apply_review sar on sa.sample_id = sar.sample_id
        <where>
            <if test="mName != null">
                and sa.m_name like  CONCAT('%',#{mName} ,'%')
            </if>
            <if test="status != null">
                and sa.status = #{status}
            </if>
            <if test="areaNo != null">
                and sa.area_no = #{areaNo}
            </if>
            <if test="bdId != null">
                and sa.bd_id = #{bdId}
            </if>
        </where>
        order by sa.sample_id DESC
    </select>

    <select id="isReview" resultType="net.summerfarm.crm.model.domain.SampleApplyReview">
        /*FORCE_MASTER*/
        select
        <include refid="Base_Column_List" />
        from sample_apply_review
        where  sample_id = #{sampleApplyId,jdbcType=INTEGER}
        <if test="status != null">
            AND status = #{status}
        </if>
        LIMIT 1
    </select>

    <select id="selectSampleApplyReviewVOById" resultMap="VOMap" >
        /*FORCE_MASTER*/
        select sa.sample_id,sa.add_time,sa.update_time,sa.create_name,sa.m_name,sa.grade,sa.m_size,sa.m_phone,sa.m_contact,sa.bd_name,sa.status,
        sa.delivery_time,sa.m_id,sa.contact_id,sa.bd_id,sa.satisfaction,sa.purchase_intention,sa.store_no,sa.remark,
        sar.review_name, sar.audit_time,sar.review_remark,a.area_name,sa.area_no, c.province, c.city, c.area, c.address,c.house_number
        from sample_apply sa
        left join area a on a.area_no = sa.area_no
        left join contact c on c.contact_id = sa.contact_id
        left join sample_apply_review sar on sa.sample_id = sar.sample_id
        where sa.sample_id = #{sampleApplyId} order by sa.sample_id DESC limit 1
    </select>

    <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.SampleApplyReview">
        insert into sample_apply_review
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="sampleId != null">sample_id,</if>
                <if test="reviewId != null">review_id,</if>
                <if test="reviewName != null">review_name,</if>
                <if test="auditTime != null">audit_time,</if>
                <if test="status != null">status,</if>
                <if test="reviewRemark != null">review_remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="sampleId != null">#{sampleId,jdbcType=INTEGER},</if>
                <if test="reviewId != null">#{reviewId,jdbcType=INTEGER},</if>
                <if test="reviewName != null">#{reviewName,jdbcType=VARCHAR},</if>
                <if test="auditTime != null">#{auditTime,jdbcType=TIMESTAMP},</if>
                <if test="status != null">#{status,jdbcType=INTEGER},</if>
                <if test="reviewRemark != null">#{reviewRemark,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.SampleApplyReview">
        update sample_apply_review
        <set>
                <if test="sampleId != null">
                    sample_id = #{sampleId,jdbcType=INTEGER},
                </if>
                <if test="reviewId != null">
                    review_id = #{reviewId,jdbcType=INTEGER},
                </if>
                <if test="reviewName != null">
                    review_name = #{reviewName,jdbcType=VARCHAR},
                </if>
                <if test="auditTime != null">
                    audit_time = #{auditTime,jdbcType=TIMESTAMP},
                </if>
                <if test="auditTime == null">
                    audit_time = now(),
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=INTEGER},
                </if>
                <if test="reviewRemark != null">
                    review_remark = #{reviewRemark,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateBySampleId">
        update sample_apply_review
        <set>
            <if test="reviewId != null">
                review_id = #{reviewId,jdbcType=INTEGER},
            </if>
            <if test="reviewName != null">
                review_name = #{reviewName,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditTime == null">
                audit_time = now(),
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="reviewRemark != null">
                review_remark = #{reviewRemark,jdbcType=VARCHAR},
            </if>
        </set>
        where  sample_id = #{sampleId}
    </update>
</mapper>
