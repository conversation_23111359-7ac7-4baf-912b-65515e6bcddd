<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.ProductsMapper">

    <sql id="Base_Column_List">
     pd_no, category_id, brand_id, pd_name, create_time, detail_picture, after_sale_time, after_sale_type,
     after_sale_unit, storage_location,origin,storage_method,slogan,picture_path, outdated,refund_type,product_introduction,
     other_slogan,quality_time,quality_time_unit,warn_time,creator,create_type,real_name,audit_status,audit_time,create_remark,auditor
    </sql>
    <sql id="Blob_Column_List">
        pddetail
    </sql>

    <select id="selectByQuery" resultType="net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO">
        select p.pd_id pdId,p.pd_name pdName,i.sku,i.weight,p.pd_no spu,p.picture_path picturePath
        from products p
        LEFT JOIN inventory i on i.pd_id = p.pd_id
        <where>
            <if test="pdName != null">
                and p.pd_name = #{pdName}
            </if>
            <if test="sku != null">
                and i.sku = #{sku}
            </if>
            <if test="pdId != null">
                and p.pd_id = #{pdId}
            </if>
        </where>
    </select>


    <select id="selectProductByQuery" resultType="net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO">
        SELECT DISTINCT oi.`pd_name` pdName,oi.picture_path picturePath,oi.sku  sku
        FROM orders o
            LEFT JOIN order_item oi ON o.order_no = oi.order_no
        WHERE o.order_time BETWEEN #{startTime} and #{endTime}
          and o.`m_id` = #{merchantId} and o.type in (0,1,3,12)
    </select>
    <select id="selectByCategoryQuery" resultType="java.lang.String">
        select category from category where id = #{categoryId}
    </select>
<!--    <select id="getCategoryTypeBySku" resultType="net.summerfarm.crm.model.dto.CategoryProductDTO">-->
<!--        SELECT p.pd_id pdId,c.id categoryId,c.type categoryType,p.pd_name pdName,i.weight weight-->
<!--        from `inventory` i-->
<!--                 LEFT JOIN `products` p on i.`pd_id` = p.`pd_id`-->
<!--                 LEFT JOIN `category` c on p.`category_id` = c.id-->
<!--        WHERE i.`sku` = #{sku}-->
<!--    </select>-->

</mapper>
