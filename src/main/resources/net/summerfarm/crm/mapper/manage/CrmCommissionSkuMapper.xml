<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmCommissionSkuMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCommissionSku">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="zone_name" jdbcType="VARCHAR" property="zoneName" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="reward" jdbcType="DECIMAL" property="reward" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, zone_name, sku, reward, updater, update_time, creator, create_time
  </sql>
    <select id="selectRewardSku" resultType="net.summerfarm.crm.model.vo.CrmCommissionSkuVo">
      select
        ccs.id,
        ccs.zone_name as zoneName,
        p.pd_name as pdName,
        i.pd_id productId,
        i.inv_id skuId,
        ccs.sku sku,
        i.weight weight,
        i.ext_type extType,
        ccs.reward
      from crm_commission_sku ccs
      left join inventory i on ccs.sku = i.sku
      left join products p on p.pd_id =i.pd_id
      left join (select t1.zone_name, @sort := @sort + 1 sort
      from (select zone_name, max(id) from crm_commission_sku ccs group by zone_name order by max(id) desc) t1,
      (select @sort := 0 sort) t2) t on t.zone_name = ccs.zone_name
      <where>
        <if test="zoneName!=null and zoneName!=''">
          and ccs.zone_name like concat('%',#{zoneName},'%')
        </if>
        <if test="sku!=null and sku !=''">
          and ccs.sku like concat('%',#{sku},'%')
        </if>
        <if test="pdName!=null and pdName!=''">
          and pd_name like concat('%',#{pdName},'%')
        </if>
      </where>
      order by t.sort, ccs.id desc
    </select>
  <select id="exist" resultType="java.lang.Integer">
    select count(*) from crm_commission_sku
    where zone_name = #{zoneName} and sku = #{sku}
    <if test="id!=null">
       and id &lt;&gt; #{id}
    </if>
  </select>
  <select id="isExistAreaSkuInfo" resultType="java.lang.String">
    select a.sku from
        (select sku from crm_commission_sku where zone_name = #{copyInfo}) as a
          cross join
        (select sku from crm_commission_sku
          <where>
            <foreach collection="info" item="item" >
              or zone_name = #{item}
            </foreach>
         </where>) as b on a.sku = b.sku
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_commission_sku
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="deleteByZoneName">
      delete from crm_commission_sku
      where zone_name = #{zoneName}

    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCommissionSku" useGeneratedKeys="true">
    insert into crm_commission_sku (zone_name, sku, reward, 
      updater, update_time, creator, 
      create_time)
    values (#{zoneName,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{reward,jdbcType=DECIMAL},
      #{updater,jdbcType=INTEGER}, sysdate(), #{creator,jdbcType=INTEGER},
      sysdate()
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCommissionSku" useGeneratedKeys="true">
    insert into crm_commission_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="zoneName != null">
        zone_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="reward != null">
        reward,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="zoneName != null">
        #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="reward != null">
        #{reward,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="copyAreaSku">
    insert into crm_commission_sku ( zone_name, sku, reward, updater, update_time, creator, create_time)
    select #{skuInfo}, sku, reward, #{adminId}, sysdate(), #{adminId}, sysdate()
    from crm_commission_sku where zone_name = #{copyAreaSkuInfo}
  </insert>

  <insert id="insertOrUpdateById">
    insert into crm_commission_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="zoneName != null">
        zone_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="reward != null">
        reward,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="zoneName != null">
        #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="reward != null">
        #{reward,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    ON DUPLICATE KEY
    UPDATE
    <trim suffixOverrides=",">
      <if test="zoneName != null">
        zone_name = #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="reward != null">
        reward = #{reward,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmCommissionSku">
    update crm_commission_sku
    <set>
      <if test="zoneName != null">
        zone_name = #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="reward != null">
        reward = #{reward,jdbcType=DECIMAL},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>