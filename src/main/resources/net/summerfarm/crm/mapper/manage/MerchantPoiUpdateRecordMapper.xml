<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantPoiUpdateRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.MerchantPoiUpdateRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="followUpRecordId" column="follow_up_record_id" jdbcType="INTEGER"/>
            <result property="addressBeforeChange" column="address_before_change" jdbcType="VARCHAR"/>
            <result property="poiBeforeChange" column="poi_before_change" jdbcType="VARCHAR"/>
            <result property="addressAfterChange" column="address_after_change" jdbcType="VARCHAR"/>
            <result property="poiAfterChange" column="poi_after_change" jdbcType="VARCHAR"/>
            <result property="mId" column="m_id" jdbcType="BIGINT"/>
            <result property="mname" column="mname" jdbcType="VARCHAR"/>
            <result property="salerName" column="saler_name" jdbcType="VARCHAR"/>
            <result property="salerId" column="saler_id" jdbcType="BIGINT"/>
            <result property="distance" column="distance" jdbcType="DECIMAL"/>
            <result property="contactId" column="contact_id" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,follow_up_record_id,address_before_change,
        poi_before_change,address_after_change,poi_after_change,
        m_id,mname,saler_name,contact_id,
        saler_id,distance,create_time,status,
        update_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from merchant_poi_update_record
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from merchant_poi_update_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.MerchantPoiUpdateRecord" useGeneratedKeys="true">
        insert into merchant_poi_update_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="followUpRecordId != null">follow_up_record_id,</if>
            <if test="addressBeforeChange != null">address_before_change,</if>
            <if test="poiBeforeChange != null">poi_before_change,</if>
            <if test="addressAfterChange != null">address_after_change,</if>
            <if test="poiAfterChange != null">poi_after_change,</if>
            <if test="mId != null">m_id,</if>
            <if test="mname != null">mname,</if>
            <if test="contactId != null">contact_id,</if>
            <if test="salerName != null">saler_name,</if>
            <if test="salerId != null">saler_id,</if>
            <if test="distance != null">distance,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="followUpRecordId != null">#{followUpRecordId,jdbcType=INTEGER},</if>
            <if test="addressBeforeChange != null">#{addressBeforeChange,jdbcType=VARCHAR},</if>
            <if test="poiBeforeChange != null">#{poiBeforeChange,jdbcType=VARCHAR},</if>
            <if test="addressAfterChange != null">#{addressAfterChange,jdbcType=VARCHAR},</if>
            <if test="poiAfterChange != null">#{poiAfterChange,jdbcType=VARCHAR},</if>
            <if test="mId != null">#{mId,jdbcType=BIGINT},</if>
            <if test="mname != null">#{mname,jdbcType=VARCHAR},</if>
            <if test="contactId != null">#{contactId},</if>
            <if test="salerName != null">#{salerName,jdbcType=VARCHAR},</if>
            <if test="salerId != null">#{salerId,jdbcType=BIGINT},</if>
            <if test="distance != null">#{distance,jdbcType=DECIMAL},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.MerchantPoiUpdateRecord">
        update merchant_poi_update_record
        <set>
            <if test="followUpRecordId != null">
                follow_up_record_id = #{followUpRecordId,jdbcType=INTEGER},
            </if>
            <if test="addressBeforeChange != null">
                address_before_change = #{addressBeforeChange,jdbcType=VARCHAR},
            </if>
            <if test="poiBeforeChange != null">
                poi_before_change = #{poiBeforeChange,jdbcType=VARCHAR},
            </if>
            <if test="addressAfterChange != null">
                address_after_change = #{addressAfterChange,jdbcType=VARCHAR},
            </if>
            <if test="poiAfterChange != null">
                poi_after_change = #{poiAfterChange,jdbcType=VARCHAR},
            </if>
            <if test="mId != null">
                m_id = #{mId,jdbcType=BIGINT},
            </if>
            <if test="mname != null">
                mname = #{mname,jdbcType=VARCHAR},
            </if>
            <if test="salerName != null">
                saler_name = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="salerId != null">
                saler_id = #{salerId,jdbcType=BIGINT},
            </if>
            <if test="distance != null">
                distance = #{distance,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status!=null">
                status=#{status}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
