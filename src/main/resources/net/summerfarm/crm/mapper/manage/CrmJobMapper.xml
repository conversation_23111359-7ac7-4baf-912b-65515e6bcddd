<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmJobMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmJob">
    <!--@mbg.generated-->
    <!--@Table crm_job-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="product_list" jdbcType="LONGVARCHAR" property="productList" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="merchant_selection_type" jdbcType="INTEGER" property="merchantSelectionType" />
    <result column="coupon_id" jdbcType="INTEGER" property="couponId" />
    <result column="category_list" jdbcType="LONGVARCHAR" property="categoryList" />
    <result column="claiming_method" jdbcType="INTEGER" property="claimingMethod" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="excel_url" jdbcType="VARCHAR" property="excelUrl" />
    <result column="merchant_selection_list" jdbcType="VARCHAR" property="merchantSelectionList" />
    <result column="reward_type" jdbcType="INTEGER" property="rewardType" />
    <result column="reward_value" jdbcType="VARCHAR" property="rewardValue" />
    <result column="sub_type" jdbcType="INTEGER" property="subType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, job_name, description, `type`, start_time, end_time, 
    product_list, creator, updater, `status`, merchant_selection_type, coupon_id, category_list, 
    claiming_method, remark, business_type, excel_url, merchant_selection_list, reward_type, 
    reward_value, sub_type
  </sql>
  <sql id="Join_Column_List">
    <!--@mbg.generated-->
    crm_job.id as crm_job_id, crm_job.create_time as crm_job_create_time, crm_job.update_time as crm_job_update_time, 
    crm_job.job_name as crm_job_job_name, crm_job.description as crm_job_description, 
    crm_job.`type` as crm_job_type, crm_job.start_time as crm_job_start_time, crm_job.end_time as crm_job_end_time, 
    crm_job.product_list as crm_job_product_list, crm_job.creator as crm_job_creator, 
    crm_job.updater as crm_job_updater, crm_job.`status` as crm_job_status, crm_job.merchant_selection_type as crm_job_merchant_selection_type, 
    crm_job.coupon_id as crm_job_coupon_id, crm_job.category_list as crm_job_category_list, 
    crm_job.claiming_method as crm_job_claiming_method, crm_job.remark as crm_job_remark, 
    crm_job.business_type as crm_job_business_type, crm_job.excel_url as crm_job_excel_url, 
    crm_job.merchant_selection_list as crm_job_merchant_selection_list, crm_job.reward_type as crm_job_reward_type, 
    crm_job.reward_value as crm_job_reward_value, crm_job.sub_type as crm_job_sub_type
  </sql>
  <resultMap id="JoinResultMap" type="net.summerfarm.crm.model.domain.CrmJob">
    <!--@mbg.generated-->
    <id column="crm_job_id" jdbcType="BIGINT" property="id" />
    <result column="crm_job_create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="crm_job_update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="crm_job_job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="crm_job_description" jdbcType="VARCHAR" property="description" />
    <result column="crm_job_type" jdbcType="INTEGER" property="type" />
    <result column="crm_job_start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="crm_job_end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="crm_job_product_list" jdbcType="LONGVARCHAR" property="productList" />
    <result column="crm_job_creator" jdbcType="BIGINT" property="creator" />
    <result column="crm_job_updater" jdbcType="BIGINT" property="updater" />
    <result column="crm_job_status" jdbcType="INTEGER" property="status" />
    <result column="crm_job_merchant_selection_type" jdbcType="INTEGER" property="merchantSelectionType" />
    <result column="crm_job_coupon_id" jdbcType="INTEGER" property="couponId" />
    <result column="crm_job_category_list" jdbcType="LONGVARCHAR" property="categoryList" />
    <result column="crm_job_claiming_method" jdbcType="INTEGER" property="claimingMethod" />
    <result column="crm_job_remark" jdbcType="VARCHAR" property="remark" />
    <result column="crm_job_business_type" jdbcType="INTEGER" property="businessType" />
    <result column="crm_job_excel_url" jdbcType="VARCHAR" property="excelUrl" />
    <result column="crm_job_merchant_selection_list" jdbcType="VARCHAR" property="merchantSelectionList" />
    <result column="crm_job_reward_type" jdbcType="INTEGER" property="rewardType" />
    <result column="crm_job_reward_value" jdbcType="VARCHAR" property="rewardValue" />
    <result column="crm_job_sub_type" jdbcType="INTEGER" property="subType" />
  </resultMap>
    <resultMap extends="JoinResultMap" id="selectCompletionCriteriaResultMap" type="net.summerfarm.crm.model.domain.CrmJobWithCrmJobCompletionCriteriaList">
        <collection property="crmJobCompletionCriteriaList" resultMap="net.summerfarm.crm.mapper.manage.CrmJobCompletionCriteriaMapper.JoinResultMap" />
    </resultMap>

    <!--auto generated by MybatisCodeHelper on 2024-09-29-->
    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO crm_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime!=null">create_time,</if>
            <if test="updateTime!=null">update_time,</if>
            <if test="jobName!=null">job_name,</if>
            <if test="description!=null">description,</if>
            <if test="type!=null">`type`,</if>
            <if test="startTime!=null">start_time,</if>
            <if test="endTime!=null">end_time,</if>
            <if test="productList!=null">product_list,</if>
            <if test="creator!=null">creator,</if>
            <if test="updater!=null">updater,</if>
            <if test="status!=null">`status`,</if>
            <if test="merchantSelectionType!=null">merchant_selection_type,</if>
            <if test="couponId!=null">coupon_id,</if>
            <if test="categoryList!=null">category_list,</if>
            <if test="subType!=null">sub_type,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime!=null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime!=null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="jobName!=null">#{jobName,jdbcType=VARCHAR},</if>
            <if test="description!=null">#{description,jdbcType=VARCHAR},</if>
            <if test="type!=null">#{type,jdbcType=INTEGER},</if>
            <if test="startTime!=null">#{startTime,jdbcType=TIMESTAMP},</if>
            <if test="endTime!=null">#{endTime,jdbcType=TIMESTAMP},</if>
            <if test="productList!=null">#{productList,jdbcType=LONGVARCHAR},</if>
            <if test="creator!=null">#{creator,jdbcType=BIGINT},</if>
            <if test="updater!=null">#{updater,jdbcType=BIGINT},</if>
            <if test="status!=null">#{status,jdbcType=INTEGER},</if>
            <if test="merchantSelectionType!=null">#{merchantSelectionType,jdbcType=INTEGER},</if>
            <if test="couponId!=null">#{couponId,jdbcType=INTEGER},</if>
            <if test="categoryList!=null">#{categoryList,jdbcType=LONGVARCHAR},</if>
            <if test="subType!=null">#{subType,jdbcType=INTEGER},</if>
        </trim>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2024-09-29-->
    <update id="updateById">
        update crm_job
        <set>
            <if test="updated.id != null">
                id = #{updated.id,jdbcType=BIGINT},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.jobName != null">
                job_name = #{updated.jobName,jdbcType=VARCHAR},
            </if>
            <if test="updated.description != null">
                description = #{updated.description,jdbcType=VARCHAR},
            </if>
            <if test="updated.type != null">
                type = #{updated.type,jdbcType=INTEGER},
            </if>
            <if test="updated.startTime != null">
                start_time = #{updated.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.endTime != null">
                end_time = #{updated.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.productList != null">
                product_list = #{updated.productList,jdbcType=LONGVARCHAR},
            </if>
            <if test="updated.creator != null">
                creator = #{updated.creator,jdbcType=BIGINT},
            </if>
            <if test="updated.updater != null">
                updater = #{updated.updater,jdbcType=BIGINT},
            </if>
            <if test="updated.status != null">
                status = #{updated.status,jdbcType=INTEGER},
            </if>
            <if test="updated.merchantSelectionType != null">
                merchant_selection_type = #{updated.merchantSelectionType,jdbcType=INTEGER},
            </if>
            <if test="updated.couponId != null">
                coupon_id = #{updated.couponId,jdbcType=INTEGER},
            </if>
            <if test="updated.categoryList != null">
                category_list = #{updated.categoryList,jdbcType=LONGVARCHAR},
            </if>
            <if test="updated.subType != null">
                sub_type = #{updated.subType,jdbcType=INTEGER},
            </if>
        </set>
        where id=#{id,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-09-24-->
    <update id="updateStatusAndUpdaterByIdInAndStatusNotIn">
        update crm_job
        set `status`=#{updatedStatus,jdbcType=INTEGER}, updater=#{updatedUpdater,jdbcType=BIGINT}
        where id in
        <foreach close=")" collection="idCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        and `status` not in
        <foreach close=")" collection="statusCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-09-24-->
    <update id="updateStatusByStartTimeLessThanOrEqualToAndStatusIn">
        update crm_job
        set `status`=#{updatedStatus,jdbcType=INTEGER}
        where start_time <![CDATA[<=]]> #{minStartTime,jdbcType=TIMESTAMP} and `status` in
        <foreach close=")" collection="statusCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-09-24-->
    <update id="updateStatusByEndTimeLessThanOrEqualToAndStatusIn">
        update crm_job
        set `status`=#{updatedStatus,jdbcType=INTEGER}
        where end_time <![CDATA[<=]]> #{maxEndTime,jdbcType=TIMESTAMP} and `status` in
        <foreach close=")" collection="statusCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </update>


    <!--auto generated by MybatisCodeHelper on 2024-09-24-->
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_job
        where id=#{id,jdbcType=BIGINT}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-09-24-->
    <select id="selectOneByCouponId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_job
        where coupon_id=#{couponId,jdbcType=INTEGER}
        and business_type=0
    </select>

    <select id="selectDistinctIdByQuery" resultType="java.lang.Long">
        select distinct cj.id
        from crm_job cj
        left join crm_job_merchant_detail cjmd on cj.id = cjmd.job_id
        left join follow_up_relation fur on cjmd.m_id = fur.m_id
        left join merchant m on cjmd.m_id = m.m_id
        <where>
            <if test="(bdIds != null and bdIds.size() != 0) or (cities != null and cities.size() != 0)">
                (
                <!-- 查询私海门店 -->
                <if test="bdIds != null and bdIds.size() != 0">
                    (fur.reassign = 0 and fur.admin_id in
                    <foreach close=")" collection="bdIds" item="bdId" open="(" separator=",">
                        #{bdId,jdbcType=INTEGER}
                    </foreach>)
                </if>

                <!-- 如果 bdIds 和 cities 都不为空，需要加上 OR -->
                <if test="bdIds != null and bdIds.size() != 0 and cities != null and cities.size() != 0">
                    or
                </if>

                <!-- 查询公海门店 -->
                <if test="cities != null and cities.size() != 0">
                    (fur.reassign = 1 and m.city in
                    <foreach close=")" collection="cities" item="city" open="(" separator=",">
                        #{city,jdbcType=VARCHAR}
                    </foreach>)
                </if>
                )
            </if>
            <if test="crmJobQueryDTO.id != null">
                and cj.id = #{crmJobQueryDTO.id,jdbcType=BIGINT}
            </if>
            <if test="crmJobQueryDTO.mId != null">
                and cjmd.m_id = #{crmJobQueryDTO.mId,jdbcType=BIGINT}
            </if>
            <if test="crmJobQueryDTO.jobName != null">
                and cj.job_name like concat('%',#{crmJobQueryDTO.jobName,jdbcType=VARCHAR},'%')
            </if>
            <if test="crmJobQueryDTO.type != null">
                and cj.type = #{crmJobQueryDTO.type,jdbcType=INTEGER}
            </if>
            <if test="crmJobQueryDTO.startTime != null">
                and cj.end_time <![CDATA[>=]]> #{crmJobQueryDTO.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="crmJobQueryDTO.endTime != null">
                and cj.start_time <![CDATA[<=]]> #{crmJobQueryDTO.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="crmJobQueryDTO.status != null">
                <choose>
                    <when test="crmJobQueryDTO.status == 3">
                        <!-- 已取消-->
                        and cj.status = 3
                    </when>
                    <otherwise>
                        <!-- 未取消-->
                        and cj.status != 3
                        <choose>
                            <!-- 未开始 -->
                            <when test="crmJobQueryDTO.status == 0">
                                and start_time <![CDATA[>]]> now()
                            </when>
                            <!-- 进行中 -->
                            <when test="crmJobQueryDTO.status == 1">
                                and start_time <![CDATA[<=]]> now() and end_time <![CDATA[>=]]> now()
                            </when>
                            <!-- 已结束 -->
                            <otherwise>
                                and end_time <![CDATA[<]]> now()
                            </otherwise>
                        </choose>
                    </otherwise>
                </choose>
            </if>
            <if test="crmJobQueryDTO.creator != null">
                and cj.creator = #{crmJobQueryDTO.creator,jdbcType=BIGINT}
            </if>
        </where>
        and cj.business_type = 0
        order by cj.id desc
    </select>

    <select id="selectWithCriteriaByIdsIn" resultMap="selectCompletionCriteriaResultMap">
        select<include refid="Join_Column_List" />,
        <include refid="net.summerfarm.crm.mapper.manage.CrmJobCompletionCriteriaMapper.Join_Column_List" />
        from crm_job join crm_job_completion_criteria on crm_job.id = crm_job_completion_criteria.job_id
        where crm_job.id in
        <foreach close=")" collection="idCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        and crm_job.business_type = 0
        order by crm_job.id desc
    </select>
    
    <select id="selectInProgressJobNameByMId" resultType="java.lang.String">
        select job_name
        from crm_job cj
        left join crm_job_merchant_detail cjmd on cj.id = cjmd.job_id
        where cjmd.m_id = #{mId,jdbcType=BIGINT}
        and cj.business_type = 0
        and cj.start_time <![CDATA[<=]]> now() and cj.end_time <![CDATA[>=]]> now() -- 进行中
    </select>
</mapper>