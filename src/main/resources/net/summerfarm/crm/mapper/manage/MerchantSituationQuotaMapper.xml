<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantSituationQuotaMapper">




    <select id="queryOne" resultType="net.summerfarm.crm.model.domain.MerchantSituationQuota">
    select
    id,admin_id adminId,
    gmt_create gmtCreate,
    gmt_modified gmtModified,
    area_no areaNo
    ,amount,status
    from merchant_situation_quota
    where admin_id = #{adminId} and status = 1 and area_no = #{areaNo}
    </select>



    <insert id="insertSituationQuota" parameterType="net.summerfarm.crm.model.domain.MerchantSituationQuota" useGeneratedKeys="true" keyProperty="id">
        insert into merchant_situation_quota (gmt_create,gmt_modified,admin_id,amount,status,area_no)
        values (now(),now(),#{adminId},#{amount},#{status},#{areaNo})
    </insert>

    <update id="updateQuota">

        update merchant_situation_quota
          set
            gmt_modified =  now()
            <if test="amount != null">
              , amount = #{amount}
            </if>
            <if test="status != null">
              , status = #{status}
            </if>
        where id =#{id}
    </update>


    <update id="updateQuotaAll">
        update merchant_situation_quota
        set
        gmt_modified =  now(), amount = 0.00
        where status = 1
    </update>

    <select id="selectAmount" parameterType="integer" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(amount),0)
        FROM merchant_situation_quota
        WHERE admin_id = #{adminId} and status = 1
    </select>
</mapper>