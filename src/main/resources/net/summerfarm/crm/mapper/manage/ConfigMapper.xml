<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.ConfigMapper" >

  <select id="selectOne" resultType="net.summerfarm.crm.model.domain.Config">
    SELECT co.id,co.key,co.value,co.remark
    FROM config co
    WHERE co.key = #{key}
  </select>

  <update id="update" parameterType="net.summerfarm.crm.model.domain.Config">
    UPDATE config co
    <set>
      <if test="value != null">
        co.value = #{value},
      </if>
      <if test="remark != null and remark!=''">
        co.remark = #{remark},
      </if>
    </set>
    WHERE co.key = #{key}
  </update>

  <update id="updateValue">
    UPDATE config co
    <set>
      <if test="value != null">
        co.value = #{value},
      </if>
    </set>
    WHERE co.key = #{key}
  </update>
</mapper>