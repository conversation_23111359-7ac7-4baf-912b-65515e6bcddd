<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantSubAccountMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.MerchantSubAccount">
    <result column="account_id" property="accountId" jdbcType="BIGINT"/>
    <result column="m_id" property="mId" jdbcType="BIGINT"/>
    <result column="contact" property="contact" jdbcType="VARCHAR"/>
    <result column="phone" property="phone" jdbcType="VARCHAR"/>
    <result column="unionid" property="unionid" jdbcType="VARCHAR"/>
    <result column="openid" property="openid" jdbcType="VARCHAR"/>
    <result column="mp_openid" property="mpOpenid" jdbcType="VARCHAR"/>
    <result column="pop_view" property="popView" jdbcType="INTEGER"/>
    <result column="first_pop_view" property="firstPopView" jdbcType="INTEGER"/>
    <result column="cash_amount" property="cashAmount" jdbcType="DECIMAL"/>
    <result column="cash_update_time" property="cashUpdateTime" jdbcType="TIMESTAMP"/>
    <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
    <result column="last_order_time" property="lastOrderTime" jdbcType="TIMESTAMP"/>
    <result column="status" property="status" jdbcType="INTEGER"/>
    <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER"/>
    <result column="m_info" property="mInfo" jdbcType="VARCHAR"/>
    <result column="register_time" property="registerTime" jdbcType="TIMESTAMP"/>
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
    <result column="audit_user" property="auditUser" jdbcType="INTEGER"/>
    <result column="type" property="type" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List">
    account_id, m_id, contact,
      phone, unionid, openid,
      mp_openid, pop_view, first_pop_view,
      cash_amount, cash_update_time, login_time,
      last_order_time, status, delete_flag,
      m_info, register_time, audit_time,
      audit_user,`type`
  </sql>

  <select id="selectByMId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where m_id = #{mId} and delete_flag = 1 and status = 1
    order by `type` asc, status desc, account_id asc
  </select>

  <select id="selectByMIdlAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where m_id = #{mId}
    order by `type` asc, status desc, account_id asc
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_sub_account
    where account_id = #{id}
  </select>
    <select id="selectByUnionid" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"/>
      from `merchant_sub_account` where `unionid` =#{unionId} and `delete_flag` =1 and `type` =0
    </select>
  <select id="selectByMIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM `merchant_sub_account`
    WHERE `m_id` IN
    <foreach collection="mIds" item="mId" open="(" close=")" separator=",">
      #{mId}
    </foreach>
  </select>

  <select id="selectByUnionidIn" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM `merchant_sub_account`
    WHERE `unionid` IN
    <foreach collection="unionIds" item="unionid" open="(" close=")" separator=",">
      #{unionid}
    </foreach>
    and delete_flag = 1
  </select>
</mapper>