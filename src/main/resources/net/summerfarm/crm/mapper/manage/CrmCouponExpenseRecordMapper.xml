<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmCouponExpenseRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCouponExpenseRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pool_id" jdbcType="BIGINT" property="poolId" />
    <result column="admin_id" jdbcType="BIGINT" property="adminId" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, pool_id, admin_id, total_amount, create_user_id,remaining_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_coupon_expense_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_coupon_expense_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpenseRecord" useGeneratedKeys="true">
    insert into crm_coupon_expense_record (create_time, update_time, pool_id, 
      admin_id, total_amount, create_user_id,remaining_amount
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{poolId,jdbcType=BIGINT}, 
      #{adminId,jdbcType=BIGINT}, #{totalAmount,jdbcType=DECIMAL}, #{createUserId,jdbcType=BIGINT},#{remainingAmount,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpenseRecord" useGeneratedKeys="true">
    insert into crm_coupon_expense_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="poolId != null">
        pool_id,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="remainingAmount != null">
        remaining_amount,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="poolId != null">
        #{poolId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=BIGINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="remainingAmount != null">
        #{remainingAmount,jdbcType=DECIMAL},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpenseRecord">
    update crm_coupon_expense_record
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="poolId != null">
        pool_id = #{poolId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=BIGINT},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="remainingAmount != null">
        remaining_amount =  #{remainingAmount,jdbcType=DECIMAL},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpenseRecord">
    update crm_coupon_expense_record
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pool_id = #{poolId,jdbcType=BIGINT},
      admin_id = #{adminId,jdbcType=BIGINT},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      remaining_amount =  #{remainingAmount,jdbcType=DECIMAL},
      create_user_id = #{createUserId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByPoolId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_coupon_expense_record
    where pool_id = #{poolId}
  </select>

  <delete id="deleteByPoolId" parameterType="java.lang.Long">
    delete from crm_coupon_expense_record
    where pool_id = #{poolId}
  </delete>

  <select id="groupByAdminIdsAmount"  resultType="net.summerfarm.crm.model.domain.CrmCouponExpenseRecord">
    select
    admin_id as adminId ,
    IFNULL(SUM(remaining_amount),0) as totalAmount
    from crm_coupon_expense_record
    where admin_id in
    <foreach collection="adminIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    group by admin_id
  </select>


  <select id="crmCouponExpensePoolQuery"  resultType="net.summerfarm.crm.model.domain.CrmCouponExpensePool">
    select
    cp.id, cr.create_time createTime, cr.update_time updateTime, cp.`name`, cr.total_amount totalAmount, cp.auto_approve autoApprove, cp.target_type targetType, cp.`status`,
    cp.start_date startDate,cp.end_date  endDate,cp.cost_limit costLimit, cp.create_user_id createUserId,cp.create_user_name createUserName,cr.remaining_amount remainingAmount
    from crm_coupon_expense_pool cp
    join  crm_coupon_expense_record cr on cp.id = cr.pool_id
    <where>
      <if test="name!=null and name!=''">
        and cp.name like concat('%',#{name},'%')
      </if>
      <if test="adminName!=null and adminName!=''">
        and cp.create_user_name like concat('%',#{adminName},'%')
      </if>
      <if test="status!=null ">
        and cp.status  = #{status}
      </if>
      <if test="autoApprove != null">
        and cp.auto_approve = #{autoApprove}
      </if>
      <if test="adminId != null">
        and cr.admin_id = #{adminId}
      </if>
      <if test="poolIds != null and poolIds.size>0">
        and cp.id in
        <foreach open="(" close=")" separator="," collection="poolIds" item="item" index="index">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectPoolIdAdminId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_coupon_expense_record
    where pool_id = #{poolId} and admin_id =#{adminId}
  </select>
</mapper>