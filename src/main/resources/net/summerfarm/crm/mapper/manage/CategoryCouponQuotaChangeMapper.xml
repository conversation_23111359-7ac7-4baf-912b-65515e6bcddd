<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CategoryCouponQuotaChangeMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CategoryCouponQuotaChange">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="adminId" column="admin_id" jdbcType="BIGINT"/>
        <result property="adminName" column="admin_name" jdbcType="VARCHAR"/>
        <result property="quota" column="quota" jdbcType="DECIMAL"/>
        <result property="basePrice" column="base_price" jdbcType="DECIMAL"/>
        <result property="rewardRule" column="reward_rule" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="merchantCouponId" column="merchant_coupon_id" jdbcType="INTEGER"/>
        <result property="dingtalkBizId" column="dingtalk_biz_id" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="quotaType" column="quota_type" jdbcType="INTEGER"/>
        <result property="poolId" column="pool_id" jdbcType="BIGINT"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        ,admin_id,admin_name,
        quota,base_price,reward_rule,
        type,merchant_coupon_id,dingtalk_biz_id,
        creator,creator_name,remark,
        create_time,quota_type,pool_id
    </sql>
    <update id="updateCouponIdByBizId">
        update category_coupon_quota_change
        set merchant_coupon_id=#{merchantCouponId}
        where dingtalk_biz_id = #{bizId}
    </update>

    <select id="listQuotaChange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from category_coupon_quota_change
        where admin_id = #{adminId} and type != 4 and quota_type = #{quotaType}
        order by create_time desc
    </select>
    <select id="selectByBizId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from category_coupon_quota_change where dingtalk_biz_id=#{bizId}
    </select>

    <select id="selectByAdminIdTypeQuotaType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from category_coupon_quota_change where admin_id = #{adminId}  and quota_type = #{quotaType}
        order by create_time desc

    </select>

    <delete id="delByAdmin">
        delete
        from category_coupon_quota_change
        where admin_id = #{adminId}
    </delete>
    <delete id="delByBizId">
        delete
        from category_coupon_quota_change
        where dingtalk_biz_id = #{bizId}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.CategoryCouponQuotaChange" useGeneratedKeys="true">
        insert into category_coupon_quota_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="adminId != null">admin_id,</if>
            <if test="adminName != null">admin_name,</if>
            <if test="quota != null">quota,</if>
            <if test="basePrice != null">base_price,</if>
            <if test="rewardRule != null">reward_rule,</if>
            <if test="type != null">type,</if>
            <if test="merchantCouponId != null">merchant_coupon_id,</if>
            <if test="dingtalkBizId != null">dingtalk_biz_id,</if>
            <if test="creator != null">creator,</if>
            <if test="creatorName != null">creator_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="quotaType != null">quota_type,</if>
            <if test="poolId != null">pool_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="adminId != null">#{adminId,jdbcType=BIGINT},</if>
            <if test="adminName != null">#{adminName,jdbcType=VARCHAR},</if>
            <if test="quota != null">#{quota,jdbcType=DECIMAL},</if>
            <if test="basePrice != null">#{basePrice,jdbcType=DECIMAL},</if>
            <if test="rewardRule != null">#{rewardRule,jdbcType=INTEGER},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="merchantCouponId != null">#{merchantCouponId,jdbcType=INTEGER},</if>
            <if test="dingtalkBizId != null">#{dingtalkBizId,jdbcType=INTEGER},</if>
            <if test="creator != null">#{creator,jdbcType=BIGINT},</if>
            <if test="creatorName != null">#{creatorName,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="quotaType != null">#{quotaType,jdbcType=INTEGER},</if>
            <if test="poolId != null">#{poolId,jdbcType=BIGINT},</if>

        </trim>
    </insert>
    <insert id="insertRewardBatch">
        insert into category_coupon_quota_change (admin_id,admin_name,quota,type,creator,creator_name,quota_type)
        VALUES
        <foreach collection ="reward" item="item" separator =",">
            (#{item.adminId},#{item.adminName},#{item.amount},3,#{item.bdId},#{item.bdName},#{type})
        </foreach>
    </insert>
</mapper>
