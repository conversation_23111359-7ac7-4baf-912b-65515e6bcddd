<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmJobCompletionCriteriaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmJobCompletionCriteria">
    <!--@mbg.generated-->
    <!--@Table crm_job_completion_criteria-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="job_id" jdbcType="BIGINT" property="jobId" />
    <result column="completion_type" jdbcType="INTEGER" property="completionType" />
    <result column="completion_value" jdbcType="VARCHAR" property="completionValue" />
    <result column="order_type_list" jdbcType="VARCHAR" property="orderTypeList" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, job_id, completion_type, completion_value, order_type_list
  </sql>
  <sql id="Join_Column_List">
    <!--@mbg.generated-->
    crm_job_completion_criteria.id as crm_job_completion_criteria_id, crm_job_completion_criteria.create_time as crm_job_completion_criteria_create_time, 
    crm_job_completion_criteria.update_time as crm_job_completion_criteria_update_time, 
    crm_job_completion_criteria.job_id as crm_job_completion_criteria_job_id, crm_job_completion_criteria.completion_type as crm_job_completion_criteria_completion_type, 
    crm_job_completion_criteria.completion_value as crm_job_completion_criteria_completion_value, 
    crm_job_completion_criteria.order_type_list as crm_job_completion_criteria_order_type_list
  </sql>
  <resultMap id="JoinResultMap" type="net.summerfarm.crm.model.domain.CrmJobCompletionCriteria">
    <!--@mbg.generated-->
    <id column="crm_job_completion_criteria_id" jdbcType="BIGINT" property="id" />
    <result column="crm_job_completion_criteria_create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="crm_job_completion_criteria_update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="crm_job_completion_criteria_job_id" jdbcType="BIGINT" property="jobId" />
    <result column="crm_job_completion_criteria_completion_type" jdbcType="INTEGER" property="completionType" />
    <result column="crm_job_completion_criteria_completion_value" jdbcType="VARCHAR" property="completionValue" />
    <result column="crm_job_completion_criteria_order_type_list" jdbcType="VARCHAR" property="orderTypeList" />
  </resultMap>

    <!--auto generated by MybatisCodeHelper on 2024-09-20-->
    <insert id="insertList" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO crm_job_completion_criteria(
        job_id,
        completion_type,
        completion_value
        )VALUES
        <foreach collection="list" index="index" item="element" separator=",">
            (
            #{element.jobId,jdbcType=BIGINT},
            #{element.completionType,jdbcType=INTEGER},
            #{element.completionValue,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>