<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.WecomGroupTaskMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.WecomGroupTask">
        <!--@mbg.generated-->
        <!--@Table wecom_group_task-->
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="message_id" property="messageId"/>
        <result column="message_status" property="messageStatus"/>
        <result column="send_count" property="sendCount"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        create_time,
        update_time,
        bd_id,
        bd_name,
        message_id,
        message_status,
        send_count
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from wecom_group_task
        where id = #{id}
    </select>
    <insert id="insertBatch" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.WecomGroupTask" useGeneratedKeys="true">
        insert into wecom_group_task (bd_id, bd_name, message_id, message_status, send_count)
        values
        <foreach collection="record" item="record" separator=",">
            (#{record.bdId}, #{record.bdName}, #{record.messageId}, #{record.messageStatus}, #{record.sendCount})
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.WecomGroupTask" useGeneratedKeys="true">
        insert into wecom_group_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="bdId != null">
                bd_id,
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name,
            </if>
            <if test="messageId != null and messageId != ''">
                message_id,
            </if>
            <if test="messageStatus != null">
                message_status,
            </if>
            <if test="sendCount != null">
                send_count,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="bdId != null">
                #{bdId},
            </if>
            <if test="bdName != null and bdName != ''">
                #{bdName},
            </if>
            <if test="messageId != null and messageId != ''">
                #{messageId},
            </if>
            <if test="messageStatus != null">
                #{messageStatus},
            </if>
            <if test="sendCount != null">
                #{sendCount},
            </if>
        </trim>
    </insert>

    <select id="selectByBdId" resultType="net.summerfarm.crm.model.vo.weCom.WeComTaskSummaryVo">
        select ifnull(count(distinct (message_id)), 0) taskCount,
               count(1)                                taskBdCount,
               sum(if(message_status = 2, 1, 0))       taskSendBdCount,
               sum(send_count)                         messageReceivedCount
        from wecom_group_task
        where create_time > DATE_FORMAT(NOW(), '%Y-%m-01 00:00:00')
          and bd_id in
        <foreach collection="bdId" item="bdId" separator="," open="(" close=")">
            #{bdId}
        </foreach>
    </select>

    <delete id="deleteByDate">
        delete
        from wecom_group_task
        where create_time between #{startTime} and #{endTime}
    </delete>
</mapper>