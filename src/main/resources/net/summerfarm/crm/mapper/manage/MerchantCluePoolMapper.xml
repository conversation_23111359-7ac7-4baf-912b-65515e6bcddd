<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantCluePoolMapper">


    <insert id="insertMerchantCluePool" parameterType="net.summerfarm.crm.model.domain.MerchantCluePool">
        insert into merchant_clue_pool (m_id,es_id,ml_id,address,m_name,phone,status)
        values (#{mId},#{esId},#{mlId},#{address},#{mName},#{phone},#{status})
    </insert>

    <select id="queryMerchantClue" resultType = "net.summerfarm.crm.model.domain.MerchantCluePool">
        select
        es_id esId,
        m_name mName,
        address address,
        phone phone,
        m_id mId,
        ml_id mlId
        from merchant_clue_pool
        where status = 0
        <if test="mId != null">
            ANd m_id = #{mId}
        </if>
        <if test="mlId != null">
            ANd ml_id = #{mlId}
        </if>
    </select>

    <select id="count" resultType="int" >
        select
        count (1)
        from merchant_clue_pool
        where status = 0
    </select>

    <select id="getExitEsids" resultType="String" >
        select
        es_id from merchant_clue_pool
        where status = 0 and es_id in
            <foreach collection="esIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>




    <update id="updateMerchantCluePool" parameterType="net.summerfarm.crm.model.domain.MerchantCluePool">
        update  merchant_clue_pool set status = #{status}
        where es_id = #{esId}
    </update>

    <update id="updateCluePool" parameterType="net.summerfarm.crm.model.domain.MerchantCluePool">
        update merchant_clue_pool
        <set>
            <if test="mName != null">
                m_name = #{mName},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="address != null">
                address =#{address},
            </if>
            <if test="esId != null">
                es_id =#{esid}
            </if>
        </set>
        where id = #{id}
    </update>
    <select id="queryEsIdNumber" resultType="int">
        select ifnull(count(id), 0)
        from merchant_clue_pool
        where BINARY es_id = #{esId} and status = 0
    </select>
    <select id="selectEffectClue" resultType="net.summerfarm.crm.model.domain.MerchantCluePool">
        select id,
               m_id mId,
               ml_id mlId,
               address,
               m_name mName,
               phone,
               status,
               es_id esId
        from merchant_clue_pool where status = 0 and es_id = #{esId} limit 1
    </select>

</mapper>