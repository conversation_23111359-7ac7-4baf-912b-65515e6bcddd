<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmCouponExpensePoolMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCouponExpensePool">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="remaining_amount" jdbcType="DECIMAL" property="remainingAmount" />
    <result column="auto_approve" jdbcType="TINYINT" property="autoApprove" />
    <result column="target_type" jdbcType="TINYINT" property="targetType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="cost_limit" jdbcType="INTEGER" property="costLimit" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />

  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, `name`, total_amount, auto_approve, target_type, `status`, 
    start_date, end_date, cost_limit, create_user_id,create_user_name,remaining_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_coupon_expense_pool
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_coupon_expense_pool
    where name = #{name,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_coupon_expense_pool
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpensePool" useGeneratedKeys="true">
    insert into crm_coupon_expense_pool (create_time, update_time, `name`, 
      total_amount,remaining_amount, auto_approve, target_type,
      `status`, start_date, end_date, 
      cost_limit, create_user_id,create_user_name)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR}, 
      #{totalAmount,jdbcType=DECIMAL}, #{remainingAmount,jdbcType=DECIMAL}, #{autoApprove,jdbcType=TINYINT}, #{targetType,jdbcType=TINYINT},
      #{status,jdbcType=TINYINT}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, 
      #{costLimit,jdbcType=INTEGER}, #{createUserId,jdbcType=BIGINT},#{createUserName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpensePool" useGeneratedKeys="true">
    insert into crm_coupon_expense_pool
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="remainingAmount != null">
        remaining_amount,
      </if>
      <if test="autoApprove != null">
        auto_approve,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="costLimit != null">
        cost_limit,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createUserName != null">
        create_user_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="remainingAmount != null">
        #{remainingAmount,jdbcType=DECIMAL},
      </if>
      <if test="autoApprove != null">
        #{autoApprove,jdbcType=TINYINT},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="costLimit != null">
        #{costLimit,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=BIGINT},
      </if>
      <if test="createUserName != null">
        #{createUserName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpensePool">
    update crm_coupon_expense_pool
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="remainingAmount != null">
        remaining_amount = #{remainingAmount,jdbcType=DECIMAL},
      </if>
      <if test="autoApprove != null">
        auto_approve = #{autoApprove,jdbcType=TINYINT},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="costLimit != null">
        cost_limit = #{costLimit,jdbcType=INTEGER},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByQuery"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_coupon_expense_pool
    <where>
      <if test="name!=null and name!=''">
        and `name` like concat('%',#{name},'%')
      </if>
      <if test="adminName!=null and adminName!=''">
        and create_user_name like concat('%',#{adminName},'%')
      </if>
      <if test="status!=null ">
        and status  = #{status}
      </if>
      <if test="productRange!=null ">
        and target_type  = #{productRange}
      </if>
      <if test="autoApprove != null">
       and auto_approve = #{autoApprove}
      </if>
      <if test="poolId != null">
        and id = #{poolId}
      </if>
      <if test="poolIds != null and poolIds.size>0">
        and id in
        <foreach open="(" close=")" separator="," collection="poolIds" item="item" index="index">
          #{item}
        </foreach>
      </if>
    </where>
    order by id  desc
  </select>

  <select id="selectExpire"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_coupon_expense_pool
    where
    start_date <![CDATA[<=]]> sysdate() and end_date <![CDATA[<=]]> sysdate() and status = 0
  </select>


  <update id="updateCostLimitNull">
    update crm_coupon_expense_pool set cost_limit  = null where id = #{id}
  </update>

  <update id="updateStartTimeEndTimeNull">
    update crm_coupon_expense_pool set start_date  = null,end_date = null where id = #{id}
  </update>
</mapper>