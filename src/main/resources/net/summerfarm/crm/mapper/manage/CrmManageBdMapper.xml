<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmManageBdMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmManageBd">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_admin_id" jdbcType="INTEGER" property="parentAdminId" />
    <result column="manage_admin_id" jdbcType="INTEGER" property="manageAdminId" />
    <result column="zone_name" jdbcType="VARCHAR" property="zoneName" />
    <result column="updater" jdbcType="INTEGER" property="updater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_admin_id, manage_admin_id, zone_name, updater, update_time, creator, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_manage_bd
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="existZoneName" resultType="java.lang.Integer">
    select count(1) from crm_manage_bd a
    <where>
      <if test="id !=null">
        and a.id &lt;&gt; #{id}
      </if>
      <if test="zoneName!=null and zoneName!=''">
        and a.zone_name = #{zoneName}
      </if>
    </where>

  </select>

  <select id="queryZoneName" resultType="java.lang.String">
    select zone_name from crm_manage_bd
    <where>
      <if test="zoneName!=null and zoneName!=''">
        and zone_name like concat('%',#{zoneName},'%')
      </if>
    </where>

  </select>

  <select id="selectZoneNameByAdminId" resultType="net.summerfarm.crm.model.vo.BdExtVO">
    SELECT DISTINCT cmb.id areaNo,zone_name areaName
      FROM crm_manage_bd cmb
      INNER JOIN crm_manage_area cma ON cmb.id = cma.mb_id
    <where>
      <if test="adminIds != null and adminIds.size() > 0">
        cmb.parent_admin_id IN
        <foreach collection="adminIds" open="(" close=") " separator="," item="item">
          #{item}
        </foreach>
        OR cmb.manage_admin_id IN
        <foreach collection="adminIds" open="(" close=") " separator="," item="item">
          #{item}
        </foreach>
        OR cmb.department_admin_id IN
        <foreach collection="adminIds" open="(" close=") " separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getAreaNoByAdmin" resultType="java.lang.Integer">
    select distinct cma.area_no from crm_manage_bd cmb right join crm_manage_area cma on cmb.id = cma.mb_id
    <where>
      <if test="adminId!=null">
        cmb.manage_admin_id = #{adminId} OR cmb.parent_admin_id = #{adminId} OR cmb.department_admin_id = #{adminId}
      </if>
    </where>
  </select>

    <select id="queryExistArea" resultType="net.summerfarm.crm.model.dto.CrmBdAreaDTO">
      select distinct cma.area_no as areaNo,a.area_name as areaName
      from crm_manage_area cma
        left join  area a on cma.area_no = a.area_no
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_manage_bd
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmManageBd" useGeneratedKeys="true">
    insert into crm_manage_bd (parent_admin_id, manage_admin_id,department_admin_id, zone_name, updater, update_time, creator, create_time)
    values (#{parentAdminId,jdbcType=INTEGER}, #{manageAdminId,jdbcType=INTEGER},#{departmentAdminId,jdbcType=INTEGER},#{zoneName,jdbcType=VARCHAR},
    #{updater,jdbcType=INTEGER}, sysdate(), #{creator,jdbcType=INTEGER}, sysdate())
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmManageBd" useGeneratedKeys="true">
    insert into crm_manage_bd
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentAdminId != null">
        parent_admin_id,
      </if>
      <if test="manageAdminId != null">
        manage_admin_id,
      </if>
      <if test="zoneName != null">
        zone_name,
      </if>
      <if test="updater != null">
        updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentAdminId != null">
        #{parentAdminId,jdbcType=INTEGER},
      </if>
      <if test="manageAdminId != null">
        #{manageAdminId,jdbcType=INTEGER},
      </if>
      <if test="zoneName != null">
        #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmManageBd">
    update crm_manage_bd
    <set>
      <if test="parentAdminId != null">
        parent_admin_id = #{parentAdminId,jdbcType=INTEGER},
      </if>
      <if test="manageAdminId != null">
        manage_admin_id = #{manageAdminId,jdbcType=INTEGER},
      </if>
      <if test="zoneName != null">
        zone_name = #{zoneName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmManageBd">
    update crm_manage_bd
    set parent_admin_id = #{parentAdminId,jdbcType=INTEGER},
      manage_admin_id = #{manageAdminId,jdbcType=INTEGER},
      department_admin_id = #{departmentAdminId,jdbcType=INTEGER},
      zone_name = #{zoneName,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=INTEGER},
      update_time = sysdate()
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectRealName" resultType="net.summerfarm.crm.model.domain.BdExt">
    SELECT a.realname adminName,cmb.manage_admin_id adminId FROM crm_manage_area cma
    INNER JOIN crm_manage_bd cmb ON cma.mb_id = cmb.id
    INNER JOIN admin a on cmb.manage_admin_id = a.admin_id
    WHERE a.is_disabled = 0 AND cma.area_no = #{areaNo}
  </select>
  <select id="selectZoneInfoById" resultType="net.summerfarm.pojo.DO.Area">
    select b.area_no areaNo ,area_name areaName,a.large_area_no largeAreaNo
    from crm_manage_area b
    INNER JOIN area a ON a.area_no = b.area_no
    <where>
      <if test="mbId != null">
        AND  b.mb_id = #{mbId}
      </if>
      <if test="areaNo != null">
        AND a.area_no = #{areaNo}
      </if>
    </where>
  </select>
  <select id="selectOperateLargeArea" resultType="net.summerfarm.crm.model.vo.BdExtVO">
    select
      large_area_no areaNo,
      large_area_name areaName,
      status largeStatus
    from large_area
    <where>
      <if test="largeStatus != null" >
        AND status = #{largeStatus}
      </if>
      <if test="largeAreaNo != null" >
        AND large_area_no = #{largeAreaNo}
      </if>
    </where>
  </select>
  <select id="selectOperateAreaByLargeNo" resultType="net.summerfarm.pojo.DO.Area">
    SELECT DISTINCT a.area_no areaNo,a.area_name areaName
      FROM area a
      LEFT JOIN crm_bd_area cba ON cba.area_no = a.area_no
    <where>
      <if test="adminId != null">
        AND cba.admin_id = #{adminId}
      </if>
      <if test="areaNo != null">
        AND a.large_area_no = #{areaNo}
      </if>
    </where>
  </select>
  <select id="selectAdministrativeCityByAdminId" resultType="java.lang.String">
    SELECT DISTINCT cma.administrative_city
        FROM crm_manage_bd cmb
        INNER JOIN crm_manage_administrative_city cma ON cmb.id = cma.mb_id
    <where>
    <if test="adminIds != null and adminIds.size() > 0">
      cmb.parent_admin_id IN
      <foreach collection="adminIds" open="(" close=") " separator="," item="item">
        #{item}
      </foreach>
      OR cmb.manage_admin_id IN
      <foreach collection="adminIds" open="(" close=") " separator="," item="item">
        #{item}
      </foreach>
      OR cmb.department_admin_id IN
      <foreach collection="adminIds" open="(" close=") " separator="," item="item">
        #{item}
      </foreach>
    </if>
    </where>
  </select>
  <select id="selectManegeSubordinate" resultType="net.summerfarm.crm.model.domain.BdExt">
    SELECT DISTINCT cbc.admin_id adminId,a.realname adminName
    FROM crm_manage_bd cmb
    INNER JOIN crm_manage_administrative_city cmc ON cmc.mb_id = cmb.id AND cmc.delete_flag = 0
    INNER JOIN crm_bd_config cbc ON cmc.administrative_city = cbc.administrative_city
    INNER JOIN admin a ON a.admin_id = cbc.admin_id
    <where>
      <if test="adminIds != null and adminIds.size() > 0">
        cmb.parent_admin_id IN
        <foreach collection="adminIds" open="(" close=") " separator="," item="item">
          #{item}
        </foreach>
        OR cmb.manage_admin_id IN
        <foreach collection="adminIds" open="(" close=") " separator="," item="item">
          #{item}
        </foreach>
        OR cmb.department_admin_id IN
        <foreach collection="adminIds" open="(" close=") " separator="," item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

</mapper>