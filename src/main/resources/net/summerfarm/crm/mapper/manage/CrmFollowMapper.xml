<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmFollowMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmFollow">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="subject_id" jdbcType="BIGINT" property="subjectId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="follow_time" jdbcType="TIMESTAMP" property="followTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="follow_goal" jdbcType="VARCHAR" property="followGoal" />
    <result column="customer_feedback" jdbcType="VARCHAR" property="customerFeedback" />
    <result column="next_follow" jdbcType="VARCHAR" property="nextFollow" />
    <result column="images" jdbcType="VARCHAR" property="images" />
    <result column="follow_model" jdbcType="VARCHAR" property="followModel" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, subject_id, bd_id, follow_time, create_time, update_time, follow_goal, 
    customer_feedback, next_follow, images, follow_model
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_follow
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_follow
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmFollow" useGeneratedKeys="true">
    insert into crm_follow (`type`, subject_id, bd_id, 
      follow_time, create_time, update_time, 
      follow_goal, customer_feedback, next_follow, 
      images, follow_model)
    values (#{type,jdbcType=VARCHAR}, #{subjectId,jdbcType=BIGINT}, #{bdId,jdbcType=INTEGER}, 
      #{followTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{followGoal,jdbcType=VARCHAR}, #{customerFeedback,jdbcType=VARCHAR}, #{nextFollow,jdbcType=VARCHAR}, 
      #{images,jdbcType=VARCHAR}, #{followModel,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmFollow" useGeneratedKeys="true">
    insert into crm_follow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="subjectId != null">
        subject_id,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="followTime != null">
        follow_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="followGoal != null">
        follow_goal,
      </if>
      <if test="customerFeedback != null">
        customer_feedback,
      </if>
      <if test="nextFollow != null">
        next_follow,
      </if>
      <if test="images != null">
        images,
      </if>
      <if test="followModel != null">
        follow_model,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null">
        #{subjectId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="followTime != null">
        #{followTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followGoal != null">
        #{followGoal,jdbcType=VARCHAR},
      </if>
      <if test="customerFeedback != null">
        #{customerFeedback,jdbcType=VARCHAR},
      </if>
      <if test="nextFollow != null">
        #{nextFollow,jdbcType=VARCHAR},
      </if>
      <if test="images != null">
        #{images,jdbcType=VARCHAR},
      </if>
      <if test="followModel != null">
        #{followModel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmFollow">
    update crm_follow
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null">
        subject_id = #{subjectId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="followTime != null">
        follow_time = #{followTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followGoal != null">
        follow_goal = #{followGoal,jdbcType=VARCHAR},
      </if>
      <if test="customerFeedback != null">
        customer_feedback = #{customerFeedback,jdbcType=VARCHAR},
      </if>
      <if test="nextFollow != null">
        next_follow = #{nextFollow,jdbcType=VARCHAR},
      </if>
      <if test="images != null">
        images = #{images,jdbcType=VARCHAR},
      </if>
      <if test="followModel != null">
        follow_model = #{followModel,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmFollow">
    update crm_follow
    set `type` = #{type,jdbcType=VARCHAR},
      subject_id = #{subjectId,jdbcType=BIGINT},
      bd_id = #{bdId,jdbcType=INTEGER},
      follow_time = #{followTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      follow_goal = #{followGoal,jdbcType=VARCHAR},
      customer_feedback = #{customerFeedback,jdbcType=VARCHAR},
      next_follow = #{nextFollow,jdbcType=VARCHAR},
      images = #{images,jdbcType=VARCHAR},
      follow_model = #{followModel,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="query" parameterType="net.summerfarm.crm.model.query.ClueDetailQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_follow
    where `type` = 'clue'
    <if test="clueId != null">
      and subject_id = #{clueId}
    </if>

    order by id desc

  </select>
</mapper>