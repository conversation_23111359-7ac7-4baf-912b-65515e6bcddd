<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmTaskMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmTask">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="updator" jdbcType="INTEGER" property="updator" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_name, `type`, source_id, start_time, end_time, create_time, update_time,
    creator, updator,delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmTask" useGeneratedKeys="true">
    insert into crm_task (task_name, `type`, source_id,
      start_time, end_time, create_time, 
      update_time, creator, updator
      )
    values (#{taskName,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, #{sourceId,jdbcType=VARCHAR},
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmTask" useGeneratedKeys="true">
    insert into crm_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskName != null and taskName != ''">
        task_name,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updator != null">
        updator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskName != null and taskName != ''">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmTask">
    update crm_task
    <set>
      <if test="taskName != null and taskName != ''">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR },
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmTask">
    update crm_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=TINYINT},
      source_id = #{sourceId,jdbcType=VARCHAR },
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=INTEGER},
      updator = #{updator,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="taskList" resultType="net.summerfarm.crm.model.vo.task.TaskListVo">
    SELECT cs.id,
           cs.task_name   taskName,
           cs.type,
           cs.start_time  startTime,
           cs.end_time    endTime,
           cs.create_time createTime,
           cs.source_id   sourceId,
           cs.delete_flag deleteFlag
    FROM crm_task cs
    <where>
      <if test="taskName != null and taskName != ''">
        AND cs.task_name like concat(#{taskName}, '%')
      </if>
      <if test="taskDay != null">
        AND #{taskDay} between DATE_FORMAT(cs.start_time ,'%Y-%m-%d') and DATE_FORMAT(cs.end_time ,'%Y-%m-%d')  and delete_flag=0
      </if>
      <if test="startTime != null">
        AND cs.start_time > #{startTime}
      </if>
      <if test="endTime != null">
        AND #{endTime} > cs.end_time
      </if>
      <if test="status != null">
        <if test="status == 0">
          AND cs.start_time > now()
          AND cs.delete_flag = 0
        </if>
        <if test="status == 1">
          AND now() between cs.start_time and cs.end_time
          AND cs.delete_flag = 0
        </if>
        <if test="status == 2">
          AND now() > cs.end_time
          AND cs.delete_flag = 0
        </if>
        <if test="status == 3">
          AND cs.delete_flag = 1
        </if>
        <if test="startTime != null">
          AND cs.start_time > #{startTime}
        </if>
        <if test="endTime != null">
          AND #{endTime} > cs.end_time
        </if>
      </if>
    </where>
    order by cs.id desc
  </select>

  <select id="selectBySourceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_task
    where source_id = #{sourceId}
  </select>

  <select id="selectCouponTaskCount" resultType="net.summerfarm.crm.model.vo.task.TaskCountVo">
    select task_id                    taskId,
           sum(if(mc.used = 1, 1, 0)) completedCount,
           sum(if(mc.used = 0, 1, 0)) uncompletedCount
    from crm_task ct
           join crm_task_detail ctd on ct.id = ctd.task_id
           join merchant_coupon mc on ct.source_id = mc.coupon_id and ctd.m_id=mc.m_id
    where ct.id = #{taskId}
    <if test="bdIdList != null and bdIdList.size() != 0">
      AND ctd.bd_id in
      <foreach collection="bdIdList" item="bdId" open="(" close=")" separator=",">
        #{bdId}
      </foreach>
    </if>
  </select>

  <select id="selectFollowTaskCount" resultType="net.summerfarm.crm.model.vo.task.TaskCountVo">
      select task_id                           taskId,
             sum(ctd.status = 1)       completedCount,
             sum(ctd.status = 0)     uncompletedCount
      from crm_task ct
               join crm_task_detail ctd on ct.id = ctd.task_id
               LEFT JOIN follow_up_record fur on fur.whether_remark = ctd.task_id and ctd.m_id = fur.m_id
      where ct.id = #{taskId}
      <if test="bdIdList != null and bdIdList.size() != 0">
          AND ctd.bd_id in
          <foreach collection="bdIdList" item="bdId" open="(" close=")" separator=",">
              #{bdId}
          </foreach>
      </if>
  </select>

  <select id="listOrderByRefundTime" resultType="net.summerfarm.crm.model.dto.task.TimingTaskDTO">
    select m_id mId, order_no orderNo
    from timing_order_refund_time
    where refund_time in
    <foreach collection="refundTime" open="(" close=")" separator="," item="refundTime">
      #{refundTime}
    </foreach>
    <if test="mId != null">
      AND m_id = #{mId}
    </if>
    <if test="excludeOrderNo != null and excludeOrderNo != ''">
      AND order_no != #{excludeOrderNo}
    </if>
  </select>

  <select id="listTimingOrderByNo" resultType="net.summerfarm.crm.model.vo.task.TimingTaskVo">
    select m.mname,
           m.province,
           m.city,
           ctd.source_id                              orderNo,
           ctd.bd_name                                bdName,
           ct.end_time                                refundTime,
           ct.start_time                              startTime,
           ct.end_time                                endTime,
           ct.delete_flag                             deleteFlag,
           if(fur.id is not null, '已完成', '未完成') status
    from crm_task_detail ctd
           join merchant m on ctd.m_id = m.m_id
           join crm_task ct on ctd.task_id = ct.id
           left join follow_up_record fur on ct.id = fur.whether_remark and ctd.m_id = fur.m_id
    where ctd.source_id in
    <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
      #{orderNo}
    </foreach>
  </select>
</mapper>