<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmBdOrgMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdOrg">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bd_id" jdbcType="BIGINT" property="bdId"/>
        <result column="bd_name" jdbcType="VARCHAR" property="bdName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="parent_name" jdbcType="VARCHAR" property="parentName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="rank" jdbcType="INTEGER" property="rank"/>
    </resultMap>

    <resultMap id="AreaConfigResultMap" type="net.summerfarm.crm.model.vo.areaConfig.BdAreaConfigVo">
        <result column="departmentAdminId" jdbcType="BIGINT" property="departmentAdminId"/>
        <result column="departmentOrgId" jdbcType="BIGINT" property="departmentOrgId"/>
        <result column="departmentAdminName" jdbcType="VARCHAR" property="departmentAdminName"/>
        <result column="managerAdminId" jdbcType="BIGINT" property="managerAdminId"/>
        <result column="managerOrgId" jdbcType="BIGINT" property="managerOrgId"/>
        <result column="managerAdminName" jdbcType="VARCHAR" property="managerAdminName"/>
        <result column="cityAdminId" jdbcType="BIGINT" property="cityAdminId"/>
        <result column="cityOrgId" jdbcType="BIGINT" property="cityOrgId"/>
        <result column="cityAdminName" jdbcType="VARCHAR" property="cityAdminName"/>
        <result column="salesAreaId" jdbcType="BIGINT" property="salesAreaId"/>
        <result column="salesAreaName" jdbcType="VARCHAR" property="salesAreaName"/>
        <collection property="salesCityList" ofType="net.summerfarm.crm.model.vo.BdSalesCityVo"
                    select="net.summerfarm.crm.mapper.manage.CrmSalesCityMapper.listBySalesAreaId"
                    column="salesAreaId"/>
    </resultMap>
    <resultMap id="AreaConfigDetailResultMap" type="net.summerfarm.crm.model.vo.areaConfig.BdAreaConfigDetail">
        <result column="bdOrgId" jdbcType="BIGINT" property="bdOrgId"/>
        <result column="cityAdminId" jdbcType="BIGINT" property="cityAdminId"/>
        <result column="cityAdminName" jdbcType="VARCHAR" property="cityAdminName"/>
        <result column="salesAreaId" jdbcType="BIGINT" property="salesAreaId"/>
        <result column="salesAreaName" jdbcType="VARCHAR" property="salesAreaName"/>
        <collection property="bdList" ofType="net.summerfarm.crm.model.domain.CrmBdOrg" select="selectByParentId"
                    column="bdOrgId"/>
        <collection property="salesCityList" ofType="net.summerfarm.crm.model.vo.BdSalesCityVo"
                    select="net.summerfarm.crm.mapper.manage.CrmSalesCityMapper.listBySalesAreaId"
                    column="salesAreaId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        bd_id,
        bd_name,
        parent_id,
        parent_name,
        create_time,
        update_time,
        `rank`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_org
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from crm_bd_org
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.CrmBdOrg" useGeneratedKeys="true">
        insert into crm_bd_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bdId != null">
                bd_id,
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="parentName != null and parentName != ''">
                parent_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="rank != null">
                `rank`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bdId != null">
                #{bdId,jdbcType=BIGINT},
            </if>
            <if test="bdName != null and bdName != ''">
                #{bdName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="parentName != null and parentName != ''">
                #{parentName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="rank != null">
                #{rank,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmBdOrg">
        update crm_bd_org
        <set>
            <if test="bdId != null">
                bd_id = #{bdId,jdbcType=BIGINT},
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name = #{bdName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="parentName != null and parentName != ''">
                parent_name = #{parentName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="rank != null">
                `rank` = #{rank,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listBdAreaConfig" resultMap="AreaConfigResultMap">
        SELECT c.bd_id departmentAdminId,
        c.id departmentOrgId,
        c.bd_name departmentAdminName,
        b.bd_id managerAdminId,
        b.id managerOrgId,
        b.bd_name managerAdminName,
        o.bd_id cityAdminId,
        o.id cityOrgId,
        o.bd_name cityAdminName,
        csa.sales_area_name salesAreaName,
        csa.id salesAreaId
        FROM crm_bd_org o
        LEFT JOIN crm_bd_org b ON b.id = o.parent_id
        LEFT JOIN crm_bd_org c ON c.id = b.parent_id
        LEFT JOIN crm_sales_area csa ON o.id = csa.bd_org_id
        WHERE o.`rank` = 3
        <if test="departmentAdminId != null">
            AND c.bd_id = #{departmentAdminId}
        </if>
        <if test="managerAdminId != null">
            AND b.bd_id = #{managerAdminId}
        </if>
        <if test="salesAreaName != null and salesAreaName != ''">
            AND csa.sales_area_name = #{salesAreaName}
        </if>
        <if test="province != null or city != null or area != null">
            AND csa.id IN (select ca.id
            from crm_sales_area ca
            join crm_sales_city cc on ca.id = cc.sales_area_id
            <where>
                <if test="province != null and province != ''">
                    cc.province = #{province}
                </if>
                <if test="city != null and city != ''">
                    AND cc.city = #{city}
                </if>
                <if test="area != null and area != ''">
                    AND cc.area = #{area}
                </if>
            </where>
            )
        </if>
        order by o.create_time desc
    </select>

    <select id="selectAreaConfigDetail" resultMap="AreaConfigDetailResultMap">
        select cbo.id              bdOrgId,
               cbo.bd_id           cityAdminId,
               cbo.bd_name         cityAdminName,
               csa.sales_area_name salesAreaName,
               csa.id              salesAreaId
        from crm_bd_org cbo
                 left join crm_sales_area csa on cbo.id = csa.bd_org_id
        where cbo.`rank` = 3
          and cbo.bd_id = #{cityAdminId}
          and csa.id = #{salesAreaId}
    </select>

    <select id="selectByParentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_org
        where parent_id = #{parentId,jdbcType=BIGINT}
    </select>

    <select id="selectByBdIdAndRank" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_org where bd_id=#{bdId} and `rank`=#{rank}
    </select>

    <select id="listByRank" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_org
        where `rank` in
        <foreach collection="rankList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and (bd_id, `rank`) IN (SELECT bd_id,
        min(rank)
        FROM crm_bd_org
        where
        `rank` in
        <foreach collection="rankList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY bd_id)
    </select>

    <select id="listChildByParentId" resultMap="BaseResultMap">
        SELECT id, bd_id, bd_name, parent_id, `rank`, @pid := concat(@pid, ',', id)
        FROM (SELECT id, bd_id, bd_name, parent_id, `rank` FROM crm_bd_org WHERE parent_id IS NOT NULL) au,
             (SELECT @pid := #{parentId}) pd
        WHERE FIND_IN_SET(parent_id, @pid)
    </select>

    <select id="listChildByParentIdAndRank" resultType="net.summerfarm.crm.model.vo.AdminInfoVo">
        select bd_id adminId, bd_name adminName
        from (SELECT id, bd_id, bd_name, parent_id, `rank`, @pid := concat(@pid, ',', id)
              FROM (SELECT id, bd_id, bd_name, parent_id, `rank`
                    FROM crm_bd_org
                    WHERE parent_id IS NOT NULL
                    ORDER BY rank) au,
                   (SELECT @pid := #{parentId}) pd
              WHERE FIND_IN_SET(parent_id, @pid)) au
        where au.`rank` = #{rank}
    </select>

    <select id="listParentByChildrenId" resultMap="BaseResultMap">
        SELECT id, bd_id, bd_name, parent_id, `rank`
        FROM (SELECT @r as cId,
        (SELECT @r := parent_id FROM crm_bd_org WHERE id = cId) AS pid,
        @l := @l + 1 AS lvl
        FROM (SELECT @r := #{childrenId}, @l := 0) vars,
        crm_bd_org AS h
        WHERE @r <![CDATA[<>]]> 0) t1
        JOIN crm_bd_org t2
        ON t1.cId = t2.id
        <if test="notContainChildren != null and notContainChildren">
            and t2.id <![CDATA[<>]]> #{childrenId}
        </if>
        ORDER BY t1.lvl DESC;
    </select>

    <select id="listByBdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_org
        where bd_id = #{adminId}
        order by `rank` desc
    </select>

    <delete id="deleteByKeyList">
        delete
        from crm_bd_org
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByBdIdAndRank">
        delete
        from crm_bd_org
        where bd_id = #{bdId}
          and `rank` = #{rank}
    </delete>

    <select id="listAdminIdRank" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_org
        where `rank` = #{rank} and bd_id in
        <foreach collection="bdIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_org
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <select id="selectM1ByCityAndArea" resultMap="BaseResultMap">
        select org.*
        from crm_bd_org org
                 join crm_sales_area area on org.id = area.bd_org_id
                 join crm_sales_city city on area.id = city.sales_area_id
        where city.city = #{city}
          and city.area = #{area}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-11-29-->
    <select id="selectByMinRank" resultType="java.lang.Integer">
        select bd_id
        from crm_bd_org
        group by bd_id
        having min(`rank`) = #{rank}
    </select>

    <select id="listAllBdId" resultType="java.lang.Integer">
        select distinct bd_id from crm_bd_org
    </select>
</mapper>