<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CoreProductBasePriceMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CoreProductBasePrice">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="largeAreaNo" column="large_area_no" jdbcType="INTEGER"/>
        <result property="largeAreaName" column="large_area_name" jdbcType="VARCHAR"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="pdId" column="pd_id" jdbcType="INTEGER"/>
        <result property="pdName" column="pd_name" jdbcType="VARCHAR"/>
        <result property="weight" column="weight" jdbcType="VARCHAR"/>
        <result property="basePrice" column="base_price" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="merchantSituationCategoryBasePrice" column="merchant_situation_category_base_price" jdbcType="DECIMAL"/>
        <result property="merchantSituationCategoryRedLinePrice" column="merchant_situation_category_red_line_price" jdbcType="DECIMAL"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,large_area_no,large_area_name,sku,pd_id,pd_name,weight,base_price,create_time,update_time,
        merchant_situation_category_base_price, merchant_situation_category_red_line_price
    </sql>
    <insert id="insertBasePrice"  keyColumn="id" keyProperty="id"  useGeneratedKeys="true">
        insert into core_product_base_price
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="largeAreaNo!=null">
                large_area_no,
            </if>
            <if test="largeAreaName!=null and largeAreaName!=''">
                large_area_name,
            </if>
            <if test="sku!=null and sku!=''">
                sku,
            </if>
            <if test="pdId!=null">
                pd_id,
            </if>
            <if test="pdName!=null and pdName!=''">
                pd_name,
            </if>
            <if test="weight!=null">
                weight,
            </if>
            <if test="basePrice!=null">
                base_price,
            </if>
            <if test="merchantSituationCategoryBasePrice!=null">
                merchant_situation_category_base_price,
            </if>
            <if test="merchantSituationCategoryRedLinePrice!=null">
                merchant_situation_category_red_line_price
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="largeAreaNo!=null">
                #{largeAreaNo},
            </if>
            <if test="largeAreaName!=null and largeAreaName!=''">
                #{largeAreaName},
            </if>
            <if test="sku!=null and sku!=''">
                #{sku},
            </if>
            <if test="pdId!=null">
                #{pdId},
            </if>
            <if test="pdName!=null and pdName!=''">
                #{pdName},
            </if>
            <if test="weight!=null">
                #{weight},
            </if>
            <if test="basePrice!=null">
                #{basePrice},
            </if>
            <if test="merchantSituationCategoryBasePrice!=null">
                #{merchantSituationCategoryBasePrice},
            </if>
            <if test="merchantSituationCategoryRedLinePrice!=null">
                #{merchantSituationCategoryRedLinePrice}
            </if>
        </trim>
    </insert>
    <update id="updateBasePrice">
        update core_product_base_price
        set base_price=#{basePrice},sku=#{sku},weight=#{weight},pd_id=#{pdId},pd_name=#{pdName},
        merchant_situation_category_base_price=#{merchantSituationCategoryBasePrice},
        merchant_situation_category_red_line_price=#{merchantSituationCategoryRedLinePrice}
        where id=#{id}
    </update>
    <delete id="delBasePrice">
        delete from core_product_base_price where id=#{id}
    </delete>
    <delete id="batchDelBasePrice">
        delete from core_product_base_price where id in
        <foreach collection="ids" item="item" open="(" close=")"  separator=",">
            #{item}
        </foreach>
    </delete>
    <select id="selectBySkuAreaNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from core_product_base_price
        where large_area_no=#{areaNo} and sku=#{sku}
        <if test="id!=null">
            and id!=#{id}
        </if>
    </select>
    <select id="listBasePrice" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from core_product_base_price
        <where>
            <if test="areaNo!='' and areaNo!=null">
                large_area_no=#{areaNo}
            </if>
            <if test="sku!='' and sku!=null">
                and sku=#{sku}
            </if>
            <if test="pdName!='' and pdName!=null">
                and pd_name like concat('%',#{pdName},'%')
            </if>
            <if test="pdId!=null">
                and pd_id = #{pdId}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="listBasePriceArea" resultType="net.summerfarm.crm.model.dto.LargeAreaDTO">
        SELECT large_area_no largeAreaNo, large_area_name largeAreaName
        from core_product_base_price b
        GROUP BY large_area_no
        order by create_time desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-11-12-->
    <update id="updateBasePriceAndMscRedLinePriceById">
        update core_product_base_price
        set base_price=#{updatedBasePrice,jdbcType=DECIMAL},
        merchant_situation_category_red_line_price=#{updatedMerchantSituationCategoryRedLinePrice,jdbcType=DECIMAL}
        where id=#{id,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-11-12-->
    <update id="updateMerchantSituationCategoryBasePriceById">
        update core_product_base_price
        set merchant_situation_category_base_price=#{updatedMerchantSituationCategoryBasePrice,jdbcType=DECIMAL}
        where id=#{id,jdbcType=BIGINT}
    </update>
</mapper>