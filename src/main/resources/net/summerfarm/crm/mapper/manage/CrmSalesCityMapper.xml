<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmSalesCityMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmSalesCity">
    <!--@mbg.generated-->
    <!--@Table crm_sales_city-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sales_area_id" jdbcType="BIGINT" property="salesAreaId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sales_area_id, province, city, area, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from crm_sales_city
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from crm_sales_city
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmSalesCity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into crm_sales_city (sales_area_id, province, city, 
      area, create_time, update_time
      )
    values (#{salesAreaId,jdbcType=BIGINT}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmSalesCity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into crm_sales_city
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="salesAreaId != null">
        sales_area_id,
      </if>
      <if test="province != null and province != ''">
        province,
      </if>
      <if test="city != null and city != ''">
        city,
      </if>
      <if test="area != null and area != ''">
        area,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="salesAreaId != null">
        #{salesAreaId,jdbcType=BIGINT},
      </if>
      <if test="province != null and province != ''">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null and city != ''">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null and area != ''">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmSalesCity">
    <!--@mbg.generated-->
    update crm_sales_city
    <set>
      <if test="salesAreaId != null">
        sales_area_id = #{salesAreaId,jdbcType=BIGINT},
      </if>
      <if test="province != null and province != ''">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null and city != ''">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null and area != ''">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmSalesCity">
    <!--@mbg.generated-->
    update crm_sales_city
    set sales_area_id = #{salesAreaId,jdbcType=BIGINT},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listBySalesAreaId" resultType="net.summerfarm.crm.model.vo.BdSalesCityVo">
    select
    id,province,city,area
    from crm_sales_city
    where sales_area_id = #{salesAreaId,jdbcType=BIGINT}
  </select>

  <select id="listByCityAndSalesArea" resultMap="BaseResultMap">
    select id,
           province,
           city,
           area
    from crm_sales_city
    where (province, city, area) in
    <foreach collection="salesCityVoList" open="(" close=")" separator="," item="item">
      (#{item.province}, #{item.city}, #{item.area})
    </foreach>
    <if test="salesAreaId != null">
      and sales_area_id != #{salesAreaId,jdbcType=BIGINT}
    </if>
  </select>

  <insert id="insertBatch">
    insert into crm_sales_city (sales_area_id, province, city, area)
    values
    <foreach collection="list" item="item" separator=",">
      (#{salesAreaId}, #{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, #{item.area,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <delete id="deleteBySalesAreaId">
    delete from crm_sales_city where sales_area_id= #{salesAreaId,jdbcType=BIGINT}
  </delete>

  <select id="listByBdOrgId" resultMap="BaseResultMap">
    select
    csc.id, csc.sales_area_id, csc.province, csc.city,csc.area
    from crm_sales_city csc
           left join crm_sales_area csa on csc.sales_area_id = csa.id
    where csa.bd_org_id in
    <foreach collection="bdOrgIdList" open="(" close=")" separator="," item="bdOrg">
      #{bdOrg}
    </foreach>
  </select>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_sales_city
  </select>

<!--auto generated by MybatisCodeHelper on 2024-09-10-->
  <select id="selectBySalesAreaId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_sales_city
        where sales_area_id=#{salesAreaId,jdbcType=BIGINT}
    </select>
</mapper>