<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CategoryCouponQuotaMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CategoryCouponQuota">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="adminId" column="admin_id" jdbcType="BIGINT"/>
        <result property="adminName" column="admin_name" jdbcType="VARCHAR"/>
        <result property="quota" column="quota" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="newCustomerRate" column="new_customer_rate" jdbcType="DECIMAL"/>
        <result property="oldCustomerRate" column="old_customer_rate" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,admin_id,admin_name,quota,create_time,update_time,type,new_customer_rate,old_customer_rate
    </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CategoryCouponQuota"
            useGeneratedKeys="true">
        insert into category_coupon_quota
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="adminId != null">admin_id,</if>
            <if test="adminName != null">admin_name,</if>
            <if test="quota != null">quota,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="adminId != null">#{adminId,jdbcType=BIGINT},</if>
            <if test="adminName != null">#{adminName,jdbcType=VARCHAR},</if>
            <if test="quota != null">#{quota,jdbcType=DECIMAL},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateQuota">
        update category_coupon_quota
        set quota=quota + #{quota}
        where admin_id = #{adminId} and type = #{type}
    </update>
    <delete id="deleteByAdminId">
        delete
        from category_coupon_quota
        where admin_id = #{adminId} and type = #{quotaType}
    </delete>
    <select id="selectByAdminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM category_coupon_quota
        WHERE admin_id = #{adminId} and type = #{type}
    </select>


    <select id="listQuota" resultType="net.summerfarm.crm.model.vo.CategoryCouponQuotaVO">
        SELECT cq.id                                                 id,
               cq.admin_id                                           adminId,
               cq.admin_name                                         adminName,
               cq.quota,
               cq.update_time                                        updateTime,
               cq.new_customer_rate                                  newCustomerRate,
               cq.old_customer_rate                                  oldCustomerRate,
               cq.type,
               ABS(SUM(IF(qc.type = 2 and cq.type = qc.quota_type, IFNULL(qc.quota, 0), 0))) appliedQuota,
               ABS(SUM(IF((qc.type = 2 and cq.type=qc.quota_type and mc.id is not null), IFNULL(qc.quota, 0), 0))) usedQuota,
               SUM(IF(qc.type = 3, IFNULL(qc.quota, 0), 0))          awardedQuota
        from category_coupon_quota cq
                 LEFT JOIN category_coupon_quota_change qc on cq.admin_id = qc.admin_id
            and qc.create_time BETWEEN TIMESTAMP(date_add(curdate(), interval -day(curdate()) + 1 day)) and NOW()
                 LEFT JOIN merchant_coupon mc on qc.merchant_coupon_id = mc.id and mc.used = 1
        <where>
            <if test="quotaType != null">
                cq.type = #{quotaType}
            </if>
            <if test="adminId != null">
                and cq.admin_id = #{adminId}
            </if>
            <if test="adminName != null and adminName!=''">
                and  cq.admin_name like concat('%',#{adminName},'%')
            </if>
            <if test="adminIds != null and adminIds.size > 0">
                and  cq.admin_id in
                <foreach collection="adminIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY cq.admin_id,cq.type
        order by cq.id desc
    </select>

    <select id="listQuotaAll" resultType="net.summerfarm.crm.model.vo.CategoryCouponQuotaVO">
        SELECT cq.id                                                 id,
        cq.admin_id                                           adminId,
        cq.admin_name                                         adminName,
        cq.quota,
        cq.update_time                                        updateTime,
        cq.new_customer_rate                                  newCustomerRate,
        cq.old_customer_rate                                  oldCustomerRate,
        cq.type,
        ABS(SUM(IF(qc.type = 2 and cq.type = qc.quota_type, IFNULL(qc.quota, 0), 0))) appliedQuota,
        ABS(SUM(IF((qc.type = 2 and cq.type=qc.quota_type and mc.id is not null), IFNULL(qc.quota, 0), 0))) usedQuota
        from category_coupon_quota cq
        LEFT JOIN category_coupon_quota_change qc on cq.admin_id = qc.admin_id
        and qc.create_time BETWEEN TIMESTAMP(date_add(curdate(), interval -day(curdate()) + 1 day)) and NOW()
        LEFT JOIN merchant_coupon mc on qc.merchant_coupon_id = mc.id and mc.used = 1
        <where>
            <if test="quotaType != null">
                cq.type = #{quotaType}
            </if>
            <if test="adminId != null">
                and cq.admin_id = #{adminId}
            </if>
            <if test="adminName != null and adminName!=''">
                and  cq.admin_name like concat('%',#{adminName},'%')
            </if>
            <if test="adminIds != null and adminIds.size > 0">
                and  cq.admin_id in
                <foreach collection="adminIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY cq.admin_id,cq.type
        order by cq.id desc
    </select>

    <update id="updateRate">
        update category_coupon_quota
        set new_customer_rate=#{newCustomerRate}, old_customer_rate=#{oldCustomerRate}
        where id = #{id}
    </update>

    <select id="listAdmin" resultType="net.summerfarm.crm.model.vo.CategoryCouponQuotaVO">
        SELECT cq.id id, cq.admin_id adminId, cq.admin_name adminName
        from category_coupon_quota cq
        <where>
            <if test="quotaType != null">
                type = #{quotaType}
            </if>
        </where>
        order by cq.id desc
    </select>
</mapper>
