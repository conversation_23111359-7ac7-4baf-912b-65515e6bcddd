<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.InvoiceConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.InvoiceConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_id" jdbcType="BIGINT" property="merchantId" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="tax_number" jdbcType="VARCHAR" property="taxNumber" />
    <result column="open_account" jdbcType="VARCHAR" property="openAccount" />
    <result column="open_bank" jdbcType="VARCHAR" property="openBank" />
    <result column="company_address" jdbcType="VARCHAR" property="companyAddress" />
    <result column="company_phone" jdbcType="VARCHAR" property="companyPhone" />
    <result column="mail_address" jdbcType="VARCHAR" property="mailAddress" />
    <result column="company_receiver" jdbcType="VARCHAR" property="companyReceiver" />
    <result column="company_email" jdbcType="VARCHAR" property="companyEmail" />
    <result column="valid_status" jdbcType="TINYINT" property="validStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="link_method" property="linkMethod"/>
    <result column="business_license_address" jdbcType="VARCHAR" property="businessLicenseAddress"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, merchant_id, admin_id, `type`, invoice_title, tax_number, open_account, open_bank, 
    company_address, company_phone, mail_address, company_receiver, company_email, valid_status, 
    update_time, create_time, link_method , updater,creator, business_license_address,default_flag
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from invoice_config
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.InvoiceConfig" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into invoice_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        `merchant_id`,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="invoiceTitle != null">
        invoice_title,
      </if>
      <if test="taxNumber != null">
        tax_number,
      </if>
      <if test="openAccount != null">
        open_account,
      </if>
      <if test="openBank != null">
        open_bank,
      </if>
      <if test="companyAddress != null">
        company_address,
      </if>
      <if test="companyPhone != null">
        company_phone,
      </if>
      <if test="mailAddress != null">
        mail_address,
      </if>
      <if test="companyReceiver != null">
        company_receiver,
      </if>
      <if test="companyEmail != null">
        company_email,
      </if>
        valid_status,
      <if test="updateTime != null">
        update_time,
      </if>
        create_time,
      <if test="linkMethod != null">
        link_method,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="businessLicenseAddress != null">
        business_license_address,
      </if>
        <if test="defaultFlag != null">
          default_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="merchantId != null">
        #{merchantId},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
        #{type},
      <if test="invoiceTitle != null">
        #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="openAccount != null">
        #{openAccount,jdbcType=VARCHAR},
      </if>
      <if test="openBank != null">
        #{openBank,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null">
        #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyPhone != null">
        #{companyPhone,jdbcType=VARCHAR},
      </if>
      <if test="mailAddress != null">
        #{mailAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyReceiver != null">
        #{companyReceiver,jdbcType=VARCHAR},
      </if>
      <if test="companyEmail != null">
        #{companyEmail,jdbcType=VARCHAR},
      </if>
        0,
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
        now(),
      <if test="linkMethod != null">
        #{linkMethod,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator},
      </if>
      <if test="businessLicenseAddress != null">
        #{businessLicenseAddress},
      </if>
      <if test="defaultFlag != null">
        #{defaultFlag},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.InvoiceConfig">
    update invoice_config
    <set>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="invoiceTitle != null">
        invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="taxNumber != null">
        tax_number = #{taxNumber,jdbcType=VARCHAR},
      </if>
      <if test="openAccount != null">
        open_account = #{openAccount,jdbcType=VARCHAR},
      </if>
      <if test="openBank != null">
        open_bank = #{openBank,jdbcType=VARCHAR},
      </if>
      <if test="companyAddress != null">
        company_address = #{companyAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyPhone != null">
        company_phone = #{companyPhone,jdbcType=VARCHAR},
      </if>
      <if test="mailAddress != null">
        mail_address = #{mailAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyReceiver != null">
        company_receiver = #{companyReceiver,jdbcType=VARCHAR},
      </if>
      <if test="companyEmail != null">
        company_email = #{companyEmail,jdbcType=VARCHAR},
      </if>
      <if test="validStatus != null">
        valid_status = #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="linkMethod != null">
        link_method = #{linkMethod, jdbcType=VARCHAR},
      </if>
      <if test="defaultFlag != null">
        default_flag = #{defaultFlag},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByType" resultType="net.summerfarm.crm.model.vo.InvoiceConfigVo">
    select c.id, c.merchant_id merchantId, c.admin_id adminId, c.`type`, c.invoice_title invoiceTitle, c.tax_number taxNumber,
    c.open_account openAccount,c.open_bank openBank,c.company_address companyAddress, c.company_phone companyPhone,c.mail_address mailAddress,
    c.company_receiver companyReceiver,coalesce(ieo.email, c.company_email) companyEmail, c.valid_status vaildStatus,c.update_time updateTime,
    c.create_time createTime, c.link_method linkMethod,c.business_license_address businessLicenseAddress,c.creator creator ,c.updater updater,
    if(r.id is not null,0,if(#{adminId} is not null,1,default_flag)) defaultFlag
    FROM invoice_config c
    left join invoice_merchant_relation r on c.id= r.invoice_id and r.`merchant_id` = #{mId} AND status=0
    left join invoice_email_override ieo on c.id = ieo.invoice_config_id and ieo.m_id = #{mId}
    where valid_status = 0
    <if test="mId != null and adminId == null">
      AND c.merchant_id = #{mId} and type =0
    </if>
    <if test="adminId != null">
      AND c.admin_id = #{adminId} and type = 1
    </if>
    <if test="invoiceTitle!=null">
      AND c.invoice_title=#{invoiceTitle}
    </if>
    <if test="taxNumber !=null ">
      AND c.tax_number=#{taxNumber}
    </if>
    order by c.create_time desc
  </select>

  <update id="resetDefaultFlag">
    update invoice_config
    set default_flag=1
    where merchant_id = #{mId} and type = 0
  </update>
  <update id="updateDefaultFlag">
    update invoice_config set default_flag=#{defaultFlag} where id=#{id}
  </update>
</mapper>