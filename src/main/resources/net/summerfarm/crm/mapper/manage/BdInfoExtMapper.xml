<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.BdInfoExtMapper">

    <resultMap id="BdInfoResultMap" type="net.summerfarm.pojo.DO.Admin">
        <id column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="login_fail_times" property="loginFailTimes" jdbcType="INTEGER"/>
        <result column="is_disabled" property="isDisabled" jdbcType="BIT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="realname" property="realname" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="BIT"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="kp" property="kp" jdbcType="VARCHAR"/>
        <result column="saler_id" property="salerId" jdbcType="INTEGER"/>
        <result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
        <result column="contract" property="contract" jdbcType="VARCHAR"/>
        <result column="contract_method" property="contractMethod" jdbcType="VARCHAR"/>
        <result column="name_remakes" property="nameRemakes" jdbcType="VARCHAR"/>
        <result column="operate_id" property="operateId" jdbcType="INTEGER"/>
        <result column="close_order_type" property="closeOrderType"/>
        <result column="cooperation_stage" property="cooperationStage"/>
        <result column="close_order_time" property="closeOrderTime"/>
        <result column="sku_sorting" property="skuSorting"/>
        <result column="update_close_order_time" property="updateCloseOrderTime"/>
        <result column="low_price_remainder" property="lowPriceRemainder"/>
        <result column="not_included_area" property="notIncludedArea"/>
    </resultMap>


    <select id="select" resultType="net.summerfarm.crm.model.vo.BdExtVO"
            parameterType="net.summerfarm.crm.model.domain.BdExt">
        select cbc.id,
               cbc.admin_id as adminId,
               a.realname as adminName,
               cba.area_no as areaNo,
               (select area_name from area where area.area_no = cba.area_no) as areaName,
               cbc.gmv_target as gmvTarget,
               cbc.private_sea_limit as privateNum,
               cbc.quota_limit as privateQuota
        from crm_bd_config cbc
        left join  crm_bd_area cba on cbc.admin_id = cba.admin_id
        left join admin a on cbc.admin_id = a.admin_id
        <where>
            <if test="areaNo != null">
                AND cba.area_no = #{areaNo}
            </if>
            <if test="adminId != null">
                AND cbc.admin_id = #{adminId}
            </if>
        </where>
    </select>

    <select id="selectOne" resultType="net.summerfarm.crm.model.domain.BdExt"
            parameterType="net.summerfarm.crm.model.domain.BdExt">
        select cbc.id,
            cbc.admin_id as adminId,
            a.realname as adminName,
            cba.area_no as areaNo,
            (select area_name from area where area.area_no = cba.area_no) as areaName,
            cbc.gmv_target as gmvTarget,
            cbc.private_sea_limit as privateNum,
            cbc.quota_limit as privateQuota
        from crm_bd_config cbc
        left join  crm_bd_area cba on cbc.admin_id = cba.admin_id
        left join admin a on cbc.admin_id = a.admin_id
        <where>
            <if test="areaNo != null">
                AND cba.area_no = #{areaNo}
            </if>
            <if test="adminId != null">
                AND cbc.admin_id = #{adminId}
            </if>
        </where>
    </select>

    <select id="selectBdInfo" resultType="net.summerfarm.crm.model.vo.BdExtVO">
        SELECT DISTINCT a.admin_id adminId,a.realname adminName
            FROM admin a
            INNER JOIN crm_bd_area cba ON cba.admin_id = a.admin_id
        WHERE a.is_disabled = 0
        <!--<if test="infoType != null and infoType == 0">
            AND ar.role_id IN (1,5,13,20,74,89,92)
        </if>
        <if test="infoType != null and infoType == 1">
            AND ar.role_id = 5
        </if>
        <if test="infoType != null and infoType == 5">
            AND ar.role_id IN (13,20,74,89,92)
        </if>-->
        <if test="baseUserIds != null and baseUserIds.size!=0">
            AND a.base_user_id in
            <foreach collection="baseUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="areaNo != null">
            AND cba.area_no = #{areaNo}
        </if>
        <if test="areaNo != null">
            AND cba.area_no = #{areaNo}
        </if>
        <if test="adminName != null">
            AND a.realname like concat('%',#{adminName},'%')
        </if>
    </select>

    <select id="selectSkuNumByMid" resultType="java.lang.Integer">
        select count(distinct oi.sku) skuCount
        from  orders o
        INNER JOIN order_item oi on oi.order_no = o.order_no
        where m_id = #{mId} AND o.status in (2, 3, 6)
        and  o.order_time BETWEEN #{startTime} AND #{endTime}
        and sku != 'DF001TD0001'
    </select>
    <select id="selectByPrimaryKey" resultMap="BdInfoResultMap" parameterType="java.lang.Integer">
        select
            admin_id, create_time, login_fail_times, is_disabled, username, password,
            login_time, realname, gender, department, phone,contract_method, name_remakes,operate_id,close_order_type,
            kp, saler_id, saler_name, contract, cooperation_stage,close_order_time,update_close_order_time,sku_sorting,low_price_remainder,not_included_area
        from admin
        where admin_id = #{adminId,jdbcType=INTEGER}
    </select>
</mapper>