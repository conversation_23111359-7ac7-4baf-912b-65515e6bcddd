<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.vo.MerchantVO">
        <id column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="size" property="size" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="area_name" property="areaName" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="last_order_time" property="lastOrderTime"/>
        <result column="operate_status" property="operateStatus"/>
        <result column="direct" property="direct"/>
        <result column="login_time" property="loginTime"/>
        <result column="type" property="type"/>
        <result column="address" property="address"/>
        <result column="poi_note" property="poiNote"/>
        <result column="admin_id" property="adminId"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="door_pic" property="doorPic"/>
        <result column="member_integral" property="memberIntegral"/>
        <result column="house_number" property="houseNumber"/>
        <result column="clue_pool" property="cluePool"/>

    </resultMap>

    <resultMap id="merchant" type="net.summerfarm.crm.model.vo.MerchantVO">
        <id column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="areaNo" property="areaNo" jdbcType="INTEGER"/>
        <result column="adminName" property="adminName" jdbcType="VARCHAR"/>
        <result column="adminId" property="adminId" jdbcType="INTEGER"/>
        <result column="size" property="size" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <collection property="contacts" ofType="net.summerfarm.crm.model.domain.Contact">
            <id column="contact_id" property="contactId" />
            <result column="address" property="address" />
            <result column="phone" property="phone"/>
            <result column="contact" property="contact"/>
            <result column="status" property="status"/>
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
    m_id, mname, mcontact, phone, islock, rank_id, register_time, login_time,last_order_time,
    invitecode, audit_time, audit_user, province, city, area, address,remark,area_no,size,type,operate_status,
    trade_area,trade_group,admin_id,direct,server,member_integral,grade,house_number,enterprise_scale,company_brand,merchant_type
    </sql>

        <select id="selectOne" parameterType="hashmap" resultType="net.summerfarm.crm.model.domain.Merchant">
        select m.m_id mId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo, m.area,m.province,m.city,m.address,
        m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode, m.remark,m.member_integral memberIntegral,m.size,m.direct,
        m.house_number houseNumber, m.admin_id adminId
        from merchant m
        <where>
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
            <if test="mname != null">
                AND mname = #{mname}
            </if>
            <if test="openid != null">
                AND m.openid = #{openid}
            </if>
            <if test="phone != null">
                AND m.phone = #{phone}
            </if>
            <if test="channelCode != null">
                AND m.channel_code = #{channelCode}
            </if>
            <if test="adminId != null">
                AND m.admin_id = #{adminId}
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
            m.m_id, m.mname, m.phone, m.last_order_time,m.area_no,m.type,m.size,m.operate_status,m.grade,a.area_name,direct,
            m.login_time,l.large_area_no largeAreaNo,m.address,m.poi_note,m.admin_id,m.province,m.city,m.area,m.door_pic,
            m.member_integral,house_number, m.clue_pool
        from merchant m
        INNER JOIN area a ON a.area_no = m.area_no
        INNER JOIN large_area l ON l.large_area_no= a.large_area_no
        where m_id = #{mId}
    </select>

    <select id="selectByPrimaryKeys" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
            m.m_id, m.mname, m.phone, m.last_order_time,m.area_no,m.type,m.size,m.operate_status,m.grade,a.area_name,direct,
            m.login_time,l.large_area_no largeAreaNo,province,city,area,door_pic,m.poi_note
        from merchant m
        INNER JOIN area a ON a.area_no = m.area_no
        INNER JOIN large_area l ON l.large_area_no= a.large_area_no
        where m_id in
        <foreach open="(" close=")" separator="," collection="mIds" item="item" index="index">
            #{item}
        </foreach>
    </select>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.Merchant">
        update merchant
        <set>
            <if test="mname != null">
                mname = #{mname,jdbcType=VARCHAR},
            </if>
            <if test="mcontact != null">
                mcontact = #{mcontact,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                openid = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                unionid = #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="islock != null">
                islock = #{islock,jdbcType=BIT},
            </if>
            <if test="rankId != null">
                rank_id = #{rankId,jdbcType=TINYINT},
            </if>
            <if test="registerTime != null">
                register_time = #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invitecode != null">
                invitecode = #{invitecode,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditUser != null">
                audit_user = #{auditUser,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="lastOrderTime != null">
                last_order_time = #{lastOrderTime},
            </if>
            <if test="areaNo != null">
                area_no = #{areaNo},
            </if>
            <if test="size != null">
                size = #{size},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="tradeArea != null">
                trade_area = #{tradeArea},
            </if>
            <if test="tradeGroup != null">
                trade_group = #{tradeGroup},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="adminId != null">
                admin_id = #{adminId},
            </if>
            <if test="direct != null">
                direct = #{direct},
            </if>
            <if test="skuShow != null">
                sku_show = #{skuShow},
            </if>
            <if test="grade != null">
                grade = #{grade},
            </if>
            <if test="poiNote != null">
                poi_note = #{poiNote},
            </if>
            <if test="mpOpenid != null">
                mp_openid = #{mpOpenid},
            </if>
            <if test="cashAmount != null">
                cash_amount = #{cashAmount},
            </if>
            <if test="cashUpdateTime">
                cash_update_time = #{cashUpdateTime},
            </if>
            <if test="mergeAdmin != null">
                merge_admin = #{mergeAdmin},
            </if>
            <if test="mergeTime != null">
                merge_time = #{mergeTime},
            </if>
            <if test="rechargeAmount != null">
                recharge_amount = #{rechargeAmount},
            </if>
            <if test="memberIntegral != null">
                member_integral = #{memberIntegral},
            </if>
            <if test="showPrice != null">
                show_price = #{showPrice},
            </if>
            <if test="changePop != null">
                change_pop = #{changePop},
            </if>
            <if test="houseNumber != null">
                house_number = #{houseNumber},
            </if>
            <if test="enterpriseScale != null">
                enterprise_scale =#{enterpriseScale} ,
            </if>
            <if test="companyBrand != null">
                company_brand =#{companyBrand},
            </if>
            <if test ="cluePool != null">
                clue_pool =#{cluePool},
            </if>
            <if test ="merchantType != null">
                merchant_type = #{merchantType},
            </if>
            <if test="examineType != null">
                examine_type = #{examineType},
            </if>
            <if test="operateStatus != null">
                operate_status = #{operateStatus},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            <if test="doorPic != null">
                door_pic = #{doorPic},
            </if>
        </set>
        where m_id = #{mId,jdbcType=BIGINT}
    </update>

    <select id="selectMerchantByMid"  resultType="net.summerfarm.crm.model.vo.MerchantVO">
        select m.m_id mId, m.mname, m.mcontact, m.phone, m.area_no areaNo, m.area,m.province,m.city,m.address, m.remark,m.member_integral memberIntegral,m.size,m.direct,f.admin_name adminName,m.grade
        ,f.admin_id adminId,a.realname realName, m.poi_note poiNote,m.house_number houseNumber,m.company_brand companyBrand,m.enterprise_scale enterpriseScale,m.last_order_time latestOrderTime,
        a.close_order_time closeOrderTime,mw.status whiteListType,m.register_time registerTime ,
        f.follow_type followType,f.timing_follow_type timingFollowType,f.admin_id followId,f.danger_day dangerDay,
        a.name_remakes nameRemakes,m.operate_status operateStatus,a.phone as adminPhone,m.door_pic doorPic, m.clue_pool as cluePool,
        f.reassign reassign
        from merchant m
        left join follow_up_relation f on m.m_id=f.m_id AND reassign = 0
        left JOIN admin a on a.admin_id = m.admin_id
        left join merchant_follow_white_list mw on mw.m_id = m.m_id
        <where>
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
        </where>
    </select>

    <select id="queryPrivateSea" resultType="net.summerfarm.crm.model.vo.MerchantVO">
        select f.m_id                               mId,
               f.admin_id                           adminId,
               if(f.reassign = 0, f.admin_name, '') adminName
        from follow_up_relation f
        <where>
            <if test="mId != null">
                AND f.m_id = #{mId}
            </if>
            <if test="mIdList != null and mIdList.size() != 0">
                AND f.m_id in
                <foreach collection="mIdList" open="(" close=")" separator="," item="merchantId">
                    #{merchantId}
                </foreach>
            </if>
            <if test="reassign != null">
                AND f.reassign = #{reassign}
            </if>
            <if test="adminId != null">
                AND f.admin_id = #{adminId}
            </if>
        </where>
        limit 2000
    </select>

       <select id="selectRegisterPrivateSea" resultType="net.summerfarm.crm.model.vo.MerchantVO">
        select m.m_id mId,
               mname,
               mcontact,
               phone,
               province,
               city,
               area,
               address,
               remark,
               area_no areaNo,
               size,
               type,
               unionid,
               direct,
               server,
               grade,
              m.house_number houseNumber
        from merchant m
        left join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
        where m.islock = 0
            and fur.admin_id = #{bdAdminId}
            and date(register_time) = #{registerDate}
            and date(m.audit_time) = #{registerDate}
            <if test="mname != null and mname != ''">
                and m.mname like concat('%', #{mname}, '%')
            </if>
    </select>

    <select id="selectSkuGmvByMid" resultType="net.summerfarm.crm.model.vo.CrmSkuMerchantGmvVO">
        SELECT IFNULL(SUM(oi.amount * oi.price),0) gmv,
               IFNULL(SUM(oi.amount),0) salesVolume,
               IFNULL(COUNT( DISTINCT(o.`order_no`)),0) merchantNum
        FROM orders o
        LEFT JOIN order_item oi ON o.order_no = oi.order_no
        WHERE o.order_time BETWEEN #{startTime} and #{endTime}
          AND  oi.sku = #{sku} and o.`m_id` = #{mId}
    </select>
    <select id="selectCategoryTop" resultType="net.summerfarm.crm.model.dto.CustomerAnalysisDTO">
        SELECT oi.category_id categoryId,count(*) quantity
        FROM orders o
                 INNER JOIN order_item oi ON o.order_no = oi.order_no
        WHERE o.m_id = #{merchantId} AND o.order_time BETWEEN #{startTime} and #{endTime}
        GROUP BY oi.category_id
        ORDER BY quantity DESC
        LIMIT 10
    </select>

    <select id="selectTypeByMids" resultType="net.summerfarm.crm.model.domain.Merchant" >
        SELECT m_id mId, type
        from merchant where type is not null and  m_id in
        <foreach open="(" close=")" separator="," collection="ids" item="item" index="index">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    <select id="selectDiscountCardByMid" resultType="net.summerfarm.crm.model.vo.MerchantVO">
        select `status`    discountCardStatus,
               used_times  discountUsedTimes,
               total_times discountTotalTimes,
               deadline    discountDeadline
        from discount_card_to_merchant d
        where d.deadline > now() and d.discount_card_id = 1 and d.status = 1
        <if test="mId != null">
            AND d.m_id = #{mId}
        </if>
        ORDER BY d.`create_time` desc
        LIMIT 1
    </select>
    <select id="selectByMname" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from merchant where mname=#{mname}
    </select>
    <select id="selectByPhone" resultType="net.summerfarm.crm.model.vo.MerchantVO">
        select m.m_id,
               m.mname,
               m.phone,
               m.last_order_time,
               m.area_no,
               m.type,
               m.size,
               m.operate_status,
               m.grade,
               m.direct,
               m.login_time,
               m.address,
               m.poi_note
        from merchant m
        where m.phone = #{phone}
        <if test="mId!=null">
            and m.m_id!=#{mId}
        </if>
    </select>
    <select id="selectByUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from merchant where unionid =#{unionId}
    </select>

    <select id="selectByNames" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"></include>
        from merchant
        where mname in
        <foreach open="(" close=")" separator="," collection="names" item="item" index="index">
            #{item}
        </foreach>
    </select>
    <update id="updateOperatingStateByMids" parameterType="long">
        update merchant set operate_status = #{operateStatus} where m_id in
        <foreach open="(" close=")" separator="," collection="mids" item="item" index="index">
            #{item}
        </foreach>
    </update>

    <select id="selectMIdByCity" resultType="java.lang.String">
        select m_id
        from merchant where  m_id in
        <foreach collection="mIds" item="mId" separator="," open="(" close=")">
            #{mId}
        </foreach>
        <if test="province != null and province != ''">
            and province=#{province}
        </if>
        <if test="city != null and city != ''">
            and city = #{city}
        </if>
        <if test="area != null and area != ''">
            and area = #{area}
        </if>
    </select>
    <select id="selectByMids" resultType="net.summerfarm.crm.model.vo.MerchantVO">
        /*FORCE_MASTER*/
        select
        m.m_id, m.mname, m.phone, m.last_order_time,m.area_no,m.type,m.size,m.operate_status,m.grade,direct,
        m.login_time, islock
        from merchant m
        where m.m_id in
        <foreach open="(" close=")" separator="," collection="mIds" item="item" index="index">
            #{item}
        </foreach>
    </select>


    <select id="selectByMidsNew" resultType="net.summerfarm.crm.model.vo.MerchantVO">
        /*FORCE_MASTER*/
        select
        m.m_id, m.mname, m.phone, m.last_order_time lastOrderTime,m.area_no areaNo,m.type,m.size,m.operate_status operateStatus,m.grade,m.direct,
        m.login_time loginTime, m.admin_id adminId, m.business_line businessLine
        from merchant m
        where m.m_id in
        <foreach open="(" close=")" separator="," collection="mIds" item="item" index="index">
            #{item}
        </foreach>
    </select>


    <select id="selectMidsByAdminId" resultType="java.lang.Long">
        select
          m_id
        from merchant
        where admin_id = #{adminId} and islock =0
    </select>

    <select id="selectMIdsByMIds" resultType="java.lang.Integer">
        select m_id mId
        from merchant where m_id in
        <foreach collection="mIds" item="mId" open="(" close=")" separator=",">
            #{mId}
        </foreach>
    </select>

    <select id="selectByMIdList" resultType="net.summerfarm.crm.model.domain.Merchant">
        select
        <include refid="Base_Column_List"/>
        from merchant where m_id in
        <foreach collection="mIds" item="mId" open="(" close=")" separator=",">
            #{mId}
        </foreach>
    </select>

    <select id="selectMIdByMId" resultType="java.lang.Long">
        select m_id mId
        from merchant where m_id in
        <foreach collection="mIds" item="mId" open="(" close=")" separator=",">
            #{mId}
        </foreach>
    </select>

    <select id="selectClosedAndOrdered" resultType="java.lang.Long">
        SELECT m.m_id
        FROM merchant m
        Inner JOIN orders o
        ON m.m_id = o.m_id
            AND o.order_time >= DATE_SUB(CURRENT_DATE(), INTERVAL #{days} DAY)
        WHERE m.operate_status = 1
          AND o.status IN (2, 3, 6)
    </select>
</mapper>
