<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.FollowUpRelationCompareMapper">

    <select id="selectLastDeliveryFinishTime" resultType="java.time.LocalDateTime">
        SELECT MAX(`real_arrival_time`) FROM `tms_dist_order`
        WHERE `source` IN (200,203) AND `outer_client_id` = #{mId} AND `state` = 40
    </select>

    <select id="selectLastSelfPickupTime" resultType="java.time.LocalDate">
        SELECT MAX(a.`delivery_time`) FROM `delivery_plan` a JOIN `orders` b ON a.`order_no` = b.`order_no`
        WHERE b.`m_id` = #{mId} AND a.`deliverytype` = 1 AND a.`status` = 6;
    </select>

</mapper>
