<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.AdminDataPermissionMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.pojo.DO.AdminDataPermission">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="permission_value" property="permissionValue" jdbcType="VARCHAR"/>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
        <result column="addtime" property="addtime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type"/>
    </resultMap>

    <select id="selectByAdminId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select id, admin_id, permission_name, permission_value, addtime
        FROM admin_data_permission
        WHERE admin_id = #{adminId}
    </select>

    <select id="selectDataByAdminId" resultType="net.summerfarm.crm.model.bo.AdminDataPermissionBO">
        select adp.id, adp.admin_id adminId, adp.permission_name permissionName, adp.permission_value permissionValue,
               adp.addtime addtime, wsc.warehouse_no warehouseNo
        FROM admin_data_permission adp
        LEFT JOIN warehouse_storage_center wsc ON adp.permission_name = wsc.warehouse_name
        WHERE admin_id = #{adminId}
    </select>

</mapper>