<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.VisitPlanMapper">
    <select id="queryVisitPlanOne" resultType="net.summerfarm.crm.model.vo.VisitPlanVO">
        select
         vp.id ,
         vp.expected_time expectedTime,
         vp.status,
         vp.m_id mId,
         vp.admin_id adminId,
         vp.expected_content expectedContent,
         vp.type,
         vp.cancel_content cancelContent,
         vp.creator creator,
        vp.area_no areaNo,
         a.realname adminName,
        vp.contact_id contactId
        from visit_plan vp
        left join admin a on a.admin_id = vp.admin_id
        where id = #{id}
    </select>

    <select id="queryVisitPlanList" resultType="net.summerfarm.crm.model.vo.VisitPlanVO">
        select vp.id , vp.expected_time expectedTime, vp.status, vp.m_id mId, vp.admin_id adminId, vp.expected_content expectedContent,
            vp.type, vp.cancel_content cancelContent,vp.creator,vp.contact_id contactId,
            vp.area_no areaNo
        from visit_plan vp
        <where>
            <if test="id != null">
                AND  vp.id = #{id}
            </if>
            <if test="status != null">
                AND  vp.status = #{status}
            </if>
            <if test="expectedTime != null">
                AND  date(vp.expected_time) = date(#{expectedTime})
            </if>
            <if test="mId != null">
                AND  vp.m_id = #{mId}
            </if>
            <if test="adminId != null">
                AND  vp.admin_id = #{adminId}
            </if>
            <if test="areaNo != null and type != 1">
                AND  vp.area_no = #{areaNo}
            </if>
            <if test="mId != null">
                AND  vp.m_id =#{mId}
            </if>
            <if test="startTime != null">
                AND vp.expected_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND vp.expected_time <![CDATA[<=]]>#{endTime}
            </if>
            <if test="type != null">
                AND vp.type = #{type}
            </if>
            <if test="source != null">
                AND vp.source = #{source}
            </if>
            <if test="areaNos != null and areaNos.size()>0">
                AND vp.area_no IN
                <foreach collection="areaNos" item="no" separator="," open="(" close=")">
                    #{no}
                </foreach>
            </if>
        </where>
        order by vp.id DESC
    </select>

    <insert id="insertVisitPlan" parameterType="net.summerfarm.crm.model.domain.VisitPlan" keyColumn="id" keyProperty="id"  useGeneratedKeys="true">
        insert into  visit_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="expectedTime != null">
                expected_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="mId != null">
                m_id,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="expectedContent != null">
                expected_content,
            </if>
            <if test="contactId != null">
                contact_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="cancelContent != null">
                cancel_content,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="expectedTime != null">
                #{expectedTime},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="mId != null">
                #{mId},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="expectedContent != null">
                #{expectedContent},
            </if>
            <if test="contactId != null">
                #{contactId},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="cancelContent != null">
                #{cancelContent},
            </if>
            <if test="areaNo != null">
                #{areaNo},
            </if>
            <if test="source != null">
                #{source},
            </if>
            <if test="province != null">
                #{province},
            </if>
            <if test="city != null">
                #{city},
            </if>
            <if test="area != null">
                #{area},
            </if>
        </trim>
    </insert>

    <update id="updateVisitPlan" parameterType="net.summerfarm.crm.model.domain.VisitPlan">
        update visit_plan
        <set>
            <if test="status != null">
                status = #{status} ,
            </if>
            <if test="expectedTime != null">
                expected_time = #{expectedTime} ,
            </if>
            <if test="mId != null">
                m_id = #{mId} ,
            </if>
            <if test="adminId != null">
                admin_id = #{adminId} ,
            </if>
            <if test="expectedContent != null">
                expected_content = #{expectedContent} ,
            </if>
            <if test="contactId != null">
                contact_id = #{contactId} ,
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="cancelContent != null">
                cancel_content = #{cancelContent},
            </if>
            <if test="areaNo != null">
                area_no = #{areaNo},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectList" resultType="net.summerfarm.crm.model.vo.VisitPlanVO">
        select vp.id,
               vp.expected_time    expectedTime,
               vp.status,
               vp.m_id             mId,
               vp.admin_id         adminId,
               vp.expected_content expectedContent,
               vp.type,
               vp.cancel_content   cancelContent,
               fur.admin_name      adminName,
               vp.area_no,
               vp.creator,
               vp.contact_id       contactId
        from visit_plan vp
                 left join follow_up_relation fur on fur.m_id = vp.m_id AND fur.reassign = 0
        <where>
            <if test="adminId != null">
                and vp.admin_id = #{adminId}
            </if>
            <if test="date != null">
                and date(vp.expected_time) = date(#{date})
            </if>
            <if test="mId != null">
                and vp.m_id = #{mId}
            </if>
            <if test="status != null">
                and vp.status = #{status}
            </if>
            <if test="type != null">
                and vp.type = #{type}
            </if>
            <if test="province != null and province != ''">
                and vp.province = #{province,jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != ''">
                and vp.city = #{city,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
  <update id="updateUnHandlePlan">
    update visit_plan set status = 4 where expected_time &lt; #{time} and status = 0
  </update>
  <select id="selectById" resultType="net.summerfarm.crm.model.domain.VisitPlan">
    select id,
       area_no areaNo,
       expected_time expectedTime,
       status,
       m_id mId,
       admin_id adminId,
       expected_content expectedContent,
       contact_id contactId,
       creator,
       type,
       cancel_content cancelContent
    from visit_plan where id = #{id}
  </select>

    <select id="queryVisitPlanListNew" resultType="net.summerfarm.crm.model.vo.VisitPlanVO">
        select vp.id , vp.expected_time expectedTime, vp.status, vp.m_id mId, vp.admin_id adminId, vp.expected_content expectedContent,
        vp.type, vp.cancel_content cancelContent,vp.creator,vp.contact_id contactId,vp.area_no
        from visit_plan vp
        <where>
            <if test="id != null">
                AND  vp.id = #{id}
            </if>
            <if test="status != null">
                AND  vp.status = #{status}
            </if>
            <if test="adminId != null">
                AND  vp.admin_id = #{adminId}
            </if>
            <if test="areaNo != null and type != 1">
                AND  m.area_no = #{areaNo}
            </if>
            <if test="type != null">
                AND vp.type = #{type}
            </if>
            <if test="source != null">
                AND vp.source = #{source}
            </if>
        </where>
        order by vp.id DESC
    </select>
</mapper>