<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.WecomCommunicationSummaryMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.WecomCommunicationSummary">
        <!--@mbg.generated-->
        <!--@Table wecom_communication_summary-->
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="conversation_count" property="conversationCount"/>
        <result column="message_count" property="messageCount"/>
        <result column="answer_proportion" property="answerProportion"/>
        <result column="answer_duration" property="answerDuration"/>
        <result column="date" property="date"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        create_time,
        update_time,
        bd_id,
        bd_name,
        conversation_count,
        message_count,
        answer_proportion,
        answer_duration,
        `date`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from wecom_communication_summary
        where id = #{id}
    </select>
    <insert id="insertBatch" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.WecomCommunicationSummary" useGeneratedKeys="true">
        insert into wecom_communication_summary (bd_id, bd_name, conversation_count, message_count, answer_proportion,
                                                 answer_duration, `date`)
        values
        <foreach collection="record" item="record" separator=",">
            (#{record.bdId}, #{record.bdName}, #{record.conversationCount}, #{record.messageCount},
             #{record.answerProportion}, #{record.answerDuration}, #{record.date})
        </foreach>
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.WecomCommunicationSummary" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into wecom_communication_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="bdId != null">
                bd_id,
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name,
            </if>
            <if test="conversationCount != null">
                conversation_count,
            </if>
            <if test="messageCount != null">
                message_count,
            </if>
            <if test="answerProportion != null">
                answer_proportion,
            </if>
            <if test="answerDuration != null">
                answer_duration,
            </if>
            <if test="date != null">
                `date`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="bdId != null">
                #{bdId},
            </if>
            <if test="bdName != null and bdName != ''">
                #{bdName},
            </if>
            <if test="conversationCount != null">
                #{conversationCount},
            </if>
            <if test="messageCount != null">
                #{messageCount},
            </if>
            <if test="answerProportion != null">
                #{answerProportion},
            </if>
            <if test="answerDuration != null">
                #{answerDuration},
            </if>
            <if test="date != null">
                #{date},
            </if>
        </trim>
    </insert>

    <select id="selectByBdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wecom_communication_summary
        where date = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 DAY), '%Y-%m-%d') and bd_id in
        <foreach collection="bdIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByDate">
        delete
        from wecom_communication_summary
        where date = #{date}
    </delete>
</mapper>