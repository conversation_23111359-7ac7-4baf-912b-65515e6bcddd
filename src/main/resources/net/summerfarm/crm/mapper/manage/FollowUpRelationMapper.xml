<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.FollowUpRelationMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.FollowUpRelation">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="reassign_time" property="reassignTime" jdbcType="TIMESTAMP"/>
        <result column="last_follow_up_time" property="lastFollowUpTime" jdbcType="TIMESTAMP"/>
        <result column="admin_name" property="adminName" jdbcType="VARCHAR"/>
        <result column="reassign" property="reassign" jdbcType="BIT"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="care_bd_id" property="careBdId" jdbcType="INTEGER"/>
        <result column="follow_type" property="followType" jdbcType="INTEGER"/>
        <result column="danger_day" property="dangerDay" jdbcType="INTEGER"/>
        <result column="timing_follow_type" property="timingFollowType" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP"/>
        <result column="protect_reason" property="protectReason" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, m_id, admin_id, admin_name, add_time, last_follow_up_time, reassign, reassign_time,reason,care_bd_id,danger_day,timing_follow_type, source, release_time, protect_reason
  </sql>

    <update id="updateReassign" parameterType="net.summerfarm.crm.model.domain.FollowUpRelation">
    update follow_up_relation
    <set>
        <if test="adminId != null" >
            admin_id = #{adminId},
        </if>
        <if test="adminName != null" >
            admin_name = #{adminName},
        </if>
        <if test="lastFollowUpTime != null" >
            last_follow_up_time = #{lastFollowUpTime},
        </if>
        <if test="reassign != null" >
            reassign = #{reassign},
        </if>
        <if test="reassignTime != null" >
            reassign_time = #{reassignTime},
        </if>
        <if test="addTime != null and reassign == false" >
            add_time = #{addTime},
        </if>
        <if test="reason != null" >
            reason = #{reason},
        </if>
        <if test="dangerDay != null" >
            danger_day = #{dangerDay},
        </if>
        <if test="followType != null" >
            follow_type = #{followType},
        </if>
        <if test="careBdId != null" >
            care_bd_id = #{careBdId},
        </if>
        <if test="timingFollowType != null">
            timing_follow_type = #{timingFollowType},
        </if>
        <if test="province != null and province != ''">
            province=#{province},
        </if>
        <if test="city != null and city != ''">
            city=#{city},
        </if>
        <if test="area != null and area != ''">
            area=#{area},
        </if>
        <if test="source != null">
            source=#{source},
        </if>
        <if test="releaseTime != null">
            release_time=#{releaseTime},
        </if>
        <if test="protectReason != null">
            protect_reason=#{protectReason},
        </if>
    </set>
     WHERE id = #{id}
  </update>
    <update id="updateDangerDay" parameterType="net.summerfarm.crm.model.domain.FollowUpRelation">
        update follow_up_relation
        <set>
            <if test="dangerDay != null" >
                danger_day = #{dangerDay},
            </if>
        </set>
        WHERE m_id = #{mId}
    </update>
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        <where>
            <if test="mId != null">
                AND m_id = #{mId}
            </if>
            <if test="reassign != null">
                AND reassign =#{reassign}
            </if>
            <if test="adminName !=null">
                AND admin_name = #{adminName}
            </if>
        </where>
        order by id desc
        limit 1
    </select>

    <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.FollowUpRelation">
        insert into follow_up_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="reassign != null">
                reassign,
            </if>
            <if test="adminName != null">
                admin_name,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="reassignTime != null">
                reassign_time,
            </if>
            <if test="careBdId != null">
                care_bd_id,
            </if>
            <if test="timingFollowType != null">
                timing_follow_type,
            </if>
            <if test="dangerDay != null">
                danger_day,
            </if>
            <if test="province != null and province != ''">
                province,
            </if>
            <if test="city != null and city != ''">
                city,
            </if>
            <if test="area != null and area != ''">
                area,
            </if>
            <if test="releaseTime != null">
                release_time,
            </if>
            <if test="protectReason != null">
                protect_reason,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="adminId != null">
                #{adminId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reassign != null">
                #{reassign,jdbcType=BIT},
            </if>
            <if test="adminName != null">
                #{adminName},
            </if>
            <if test="reason != null">
                #{reason},
            </if>
            <if test="reassignTime != null">
                #{reassignTime},
            </if>
            <if test="careBdId != null">
                #{careBdId},
            </if>
            <if test="timingFollowType != null">
                #{timingFollowType},
            </if>
            <if test="dangerDay != null">
                #{dangerDay},
            </if>
            <if test="province != null and province != ''">
                #{province},
            </if>
            <if test="city != null and city != ''">
                #{city},
            </if>
            <if test="area != null and area != ''">
                #{area},
            </if>
            <if test="releaseTime != null">
                #{releaseTime},
            </if>
            <if test="protectReason != null">
                #{protectReason},
            </if>
        </trim>
    </insert>

    <update id="updateReassignByAdminId">
        update follow_up_relation
        <set>
            source = admin_id,
            <if test="reassign != null" >
                reassign = #{reassign},
            </if>

            <if test="reassignTime != null" >
                reassign_time = #{reassignTime},
            </if>

            <if test="reason != null" >
                reason = #{reason},
            </if>

            <if test="dangerDay != null" >
                danger_day = #{dangerDay},
            </if>
            <if test="followType != null" >
                follow_type = #{followType},
            </if>
            <if test="timingFollowType != null">
                timing_follow_type = #{timingFollowType},
            </if>

            <if test="releaseTime != null">
                release_time = #{releaseTime},
            </if>
            <if test="protectReason != null">
                protect_reason = #{protectReason},
            </if>
        </set>
        WHERE admin_id = #{adminId} and reassign = 0
    </update>
    <update id="updateReassignByAdminIdArea">
        update follow_up_relation fur left join merchant m on m.m_id = fur.m_id
        <set>
            fur.source = fur.admin_id,
            <if test="followUpRelation.reassign != null" >
                reassign = #{followUpRelation.reassign},
            </if>

            <if test="followUpRelation.reassignTime != null" >
                reassign_time = #{followUpRelation.reassignTime},
            </if>

            <if test="followUpRelation.reason != null" >
                reason = #{followUpRelation.reason},
            </if>

            <if test="followUpRelation.dangerDay != null" >
                danger_day = #{followUpRelation.dangerDay},
            </if>
            <if test="followUpRelation.followType != null" >
                follow_type = #{followUpRelation.followType},
            </if>
            <if test="followUpRelation.timingFollowType != null">
                timing_follow_type = #{followUpRelation.timingFollowType},
            </if>
            <if test="followUpRelation.releaseTime != null">
                release_time = #{followUpRelation.releaseTime},
            </if>
            <if test="followUpRelation.protectReason != null">
                protect_reason = #{followUpRelation.protectReason},
            </if>
        </set>
        <where>
               fur.reassign = 0
            <if test="adminId!=null">
                and fur.admin_id = #{adminId}
            </if>
            <if test="infoArea.size()>0">
                and m.area_no in
                <foreach collection="infoArea" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>
    <update id="autoRelease">
        UPDATE follow_up_relation
        SET
        source = if(reassign = 1, source, admin_id),
        reassign = 1,reassign_time = NOW(),reason = #{reason}
        WHERE m_id IN
        <foreach collection="mIds" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
    </update>


    <delete id="deleteByMids">
        delete from follow_up_relation
        WHERE m_id in
        <foreach collection="mIds" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
    </delete>


    <select id="selectByMid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        where m_id = #{mId}
    </select>
    <select id="countByAreaNo" resultType="integer">
        select
        count(1)
        from follow_up_relation fur
        <where>
            <if test="mId != null">
                AND fur.m_id = #{mId}
            </if>
            <if test="reassign != null">
                AND fur.reassign =#{reassign}
            </if>
            <if test="adminName !=null">
                AND fur.admin_name = #{adminName}
            </if>
            <if test="adminId != null">
                AND fur.admin_id = #{adminId}
            </if>
            ORDER BY fur.`id` desc
        </where>
    </select>

    <select id="selectByAreaNo" resultMap="BaseResultMap">
        select
        fur.id, fur.m_id, fur.admin_id, fur.admin_name, fur.add_time, fur.last_follow_up_time, fur.reassign,
        fur.reassign_time, fur.reason, fur.source, fur.release_time, fur.protect_reason
        from follow_up_relation fur
        <where>
            <if test="mId != null">
                AND fur.m_id = #{mId}
            </if>
            <if test="reassign != null">
                AND fur.reassign =#{reassign}
            </if>
            <if test="adminName !=null">
                AND fur.admin_name = #{adminName}
            </if>
            <if test="adminId != null">
                AND fur.admin_id = #{adminId}
            </if>
        </where>
        ORDER BY   fur.`id` desc
        limit #{offset},#{offSize}

    </select>
  <select id="countByMId" resultType="int">
    select ifnull(count(*), 0) from crm_relation_record where m_id = #{mId}
  </select>

    <select id="queryFollow" resultType="net.summerfarm.crm.model.vo.MerchantVO">
        SELECT admin_name adminName, reassign reassign, f.admin_id as adminId
        from follow_up_relation f
        WHERE f.m_id = #{mId} AND f.reassign = 0
    </select>
    <select id="selectBdType" resultType="integer">
        SELECT f.m_id FROM `crm_bd_team` cbt
        INNER JOIN follow_up_relation f ON cbt.admin_id = f.admin_id
        WHERE cbt.type = 2 and cbt.is_locked = 1 AND f.reassign = 0
    </select>
    <select id="selectMerchantNum" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
        SELECT
            IFNULL(SUM(coalesce(f.reassign,1) = 0),0) privateMerchant,
            IFNULL(SUM(coalesce(f.reassign,1) = 1),0) openMerchant
        FROM follow_up_relation f
        INNER JOIN merchant m ON f.m_id = m.m_id
        WHERE m.islock = 0
        AND m.area_no IN
        <foreach collection="areaNo" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        <if test="type != null and type == 1">
            AND f.admin_id NOT IN (SELECT admin_id FROM crm_bd_team WHERE is_locked = 1 AND type = 2)
        </if>
        <if test="type != null and type == 2">
            AND f.reassign = 0 AND f.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
    </select>
    <select id="selectOperateMerchantInOpenSea" resultType="integer">
        SELECT count(*) FROM follow_up_relation f
        INNER JOIN merchant m ON f.m_id = m.m_id
        WHERE f.reassign = 1 AND m.operate_status = 1
        AND m.area_no IN
        <foreach collection="areaNo" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectLastFollowOne" resultMap="BaseResultMap">
        select
        fur.id, fur.m_id, fur.admin_id, fur.admin_name, fur.add_time, fur.last_follow_up_time, fur.reassign,
        fur.reassign_time, fur.reason, fur.care_bd_id, fur.release_time, fur.protect_reason
        from follow_up_relation fur
        where fur.m_id = #{mId} and fur.reassign=0 and  fur.admin_id =#{adminId}
        limit 1
    </select>

    <select id="selectMidByBdId" resultType="java.lang.Long">
        select m_id from follow_up_relation where admin_id=#{bdId} and reassign=0
    </select>


    <update id="updateCareAdminId">
        UPDATE follow_up_relation SET care_bd_id =#{careBdId}
        where id = #{id}
    </update>

    <select id="selectByCity" resultMap="BaseResultMap">
        SELECT f.id,f.m_id, f.admin_id, f.admin_name, f.add_time, f.last_follow_up_time, f.reassign, f.reassign_time, f.reason, f.release_time, f.protect_reason
        FROM follow_up_relation f
        WHERE f.reassign = 0 AND f.admin_id = #{adminId}
        <if test="province != null and province != ''">
            and province=#{province,jdbcType=VARCHAR}
        </if>
        <if test="city != null and city != ''">
            and city=#{city,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area != ''">
            and area=#{area,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectUnReassign" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        where m_id = #{mId} and reassign = 0
    </select>

    <select id="batchSelect" resultMap="BaseResultMap">
        select
        fur.id, fur.m_id, fur.admin_id, fur.admin_name, fur.add_time, fur.last_follow_up_time, fur.reassign,
        fur.reassign_time, fur.reason, fur.release_time, fur.protect_reason
        from follow_up_relation fur
        where fur.reassign = 0 and fur.admin_id = #{bdAdminId}
        <if test="mIds!= null and mIds.size()>0">
            and fur.m_id in
            <foreach collection="mIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectMIdByMId" resultType="java.lang.Integer">
        select m_id
        from follow_up_relation where reassign = 0 and m_id in
        <foreach collection="mIds" item="mId" open="(" close=")" separator=",">
            #{mId}
        </foreach>
    </select>

    <select id="selectPrivateSeaByReassignTimeAndBdIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        where ((reassign_time IS NOT NULL AND reassign_time >= #{reassignTime})
        OR (reassign_time IS NULL AND add_time >= #{reassignTime}))
        and admin_id IS NOT NULL
        and admin_id > 0
        and m_id IS NOT NULL
        and reassign = 0
        <if test="bdIds != null and bdIds.size() != 0">
            and admin_id in
            <foreach collection="bdIds" item="bdId" open="(" close=")" separator=",">
                #{bdId}
            </foreach>
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-10-17-->
    <select id="selectByReassignAndMIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        <where>
            <if test="reassign != null">
                and reassign=#{reassign,jdbcType=BIT}
            </if>
            <if test="mIdCollection != null and mIdCollection.size() > 0">
                and m_id in
                <foreach item="item" index="index" collection="mIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectRecentlyReleasedByBdIdAndCity" resultType="java.lang.Long">
        select
        fur.m_id
        from follow_up_relation fur
        left join merchant m on fur.m_id = m.m_id
        where m.province=#{province,jdbcType=VARCHAR}
        and m.city=#{city,jdbcType=VARCHAR}
        and fur.reassign=1
        and fur.admin_id is not null
        and fur.admin_id > 1 -- 排除从来没有被分配过的
        and current_date  <![CDATA[<=]]> date_add(fur.reassign_time, interval #{numOfDay} day)
        <if test="bdId != null">
            and fur.admin_id = #{bdId}
        </if>
        order by fur.reassign_time
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-11-26-->
    <update id="updateDangerDayAndReasonByMId">
        update follow_up_relation
        set danger_day=#{updatedDangerDay,jdbcType=INTEGER}, reason=#{updatedReason,jdbcType=VARCHAR}, release_time = #{releaseTime}, protect_reason = #{protectReason}
        where m_id=#{mId,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-11-29-->
    <select id="selectByAdminIdAndDangerDayIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from follow_up_relation
        where admin_id=#{adminId,jdbcType=INTEGER}
        and danger_day in
        <foreach item="item" index="index" collection="dangerDayCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and DATEDIFF(`release_time`, NOW()) IN
        <foreach item="dangerDay" collection="dangerDayCollection"
                 open="(" separator="," close=")">
            #{dangerDay,jdbcType=INTEGER}
        </foreach>
        and reassign = 0
    </select>

    <select id="selectFollowUpRelationsWithAdmins" resultType="net.summerfarm.crm.model.dto.FollowUpRelationDTO">
        select
        fur.id, fur.m_id mId, fur.admin_id adminId, a.realname realname, a.phone phone
        from follow_up_relation fur left join admin a on a.admin_id = fur.admin_id
        where fur.reassign = 0 and a.is_disabled = 0
        <if test="mIds!= null and mIds.size()>0">
            and fur.m_id in
            <foreach collection="mIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>