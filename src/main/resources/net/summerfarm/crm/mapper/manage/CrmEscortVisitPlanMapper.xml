<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmEscortVisitPlanMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmEscortVisitPlan">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="visitPlanId" column="visit_plan_id" jdbcType="BIGINT"/>
            <result property="expectedTime" column="expected_time" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="expectedContent" column="expected_content" jdbcType="VARCHAR"/>
            <result property="cancelContent" column="cancel_content" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,admin_id,visit_plan_id,
        expected_time,status,expected_content,
        cancel_content,creator,updater,
        update_time,create_time
    </sql>
    <insert id="insertEscortVisitPlan" parameterType="net.summerfarm.crm.model.domain.CrmEscortVisitPlan">
            insert into  crm_escort_visit_plan
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    id,
                </if>
                <if test="expectedTime != null">
                    expected_time,
                </if>
                <if test="status != null">
                    status,
                </if>
                <if test="adminId != null">
                    admin_id,
                </if>
                <if test="expectedContent != null">
                    expected_content,
                </if>
                <if test="creator != null">
                    creator,
                </if>
                <if test="cancelContent != null">
                    cancel_content,
                </if>
                <if test="updater != null">
                    updater,
                </if>
                <if test="visitPlanId != null">
                    visit_plan_id,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">
                    #{id},
                </if>
                <if test="expectedTime != null">
                    #{expectedTime},
                </if>
                <if test="status != null">
                    #{status},
                </if>
                <if test="adminId != null">
                    #{adminId},
                </if>
                <if test="expectedContent != null">
                    #{expectedContent},
                </if>
                <if test="creator != null">
                    #{creator},
                </if>
                <if test="updater != null">
                    #{updater},
                </if>
                <if test="cancelContent != null">
                    #{cancelContent},
                </if>
                <if test="visitPlanId != null">
                    #{visitPlanId},
                </if>
            </trim>
    </insert>
    <update id="updateVisitPlan">
        update crm_escort_visit_plan
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="status != null">
                updater = #{updater},
            </if>
            <if test="status != null">
                cancel_content = #{cancelContent},
            </if>
        </set>
        where id = #{id}
    </update>
    <select id="queryEscortPlan" resultType="net.summerfarm.crm.model.vo.VisitPlanVO">
        select cvp.id , cvp.expected_time expectedTime, cvp.status, cvp.admin_id escortAdminId, cvp.expected_content expectedContent,
            cvp.cancel_content cancelContent,cvp.creator,
            vp.m_id mId,vp.contact_id contactId,vp.admin_id adminId
        from crm_escort_visit_plan cvp
        inner join visit_plan vp on cvp.visit_plan_id = vp.id
        <where>
            <if test="id != null">
                AND  cvp.id = #{id}
            </if>
            <if test="status != null">
                AND  cvp.status = #{status}
            </if>
            <if test="date != null">
                AND  date(cvp.expected_time) = date(#{date})
            </if>
            <if test="mId != null">
                AND  vp.m_id = #{mId}
            </if>
            <if test="adminId != null">
                AND  cvp.admin_id = #{adminId}
            </if>
            <if test="mId != null">
                AND  vp.m_id = #{mId}
            </if>
            <if test="startTime != null">
                AND cvp.expected_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND cvp.expected_time <![CDATA[<=]]>#{endTime}
            </if>
            <if test="areaNo != null">
                and vp.area_no = #{areaNo}
            </if>
            <if test="areaNos != null and areaNos.size()>0">
                AND vp.area_no IN
                <foreach collection="areaNos" item="no" separator="," open="(" close=")">
                    #{no}
                </foreach>
            </if>
            <if test="province != null and province != ''">
                and vp.province=#{province,jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != ''">
                and vp.city=#{city,jdbcType=VARCHAR}
            </if>
            <if test="area != null and area != ''">
                and vp.area=#{area,jdbcType=VARCHAR}
            </if>
        </where>
        order by cvp.id DESC
    </select>
    <select id="queryEscortPlanByVisitPlanId" resultType="net.summerfarm.crm.model.vo.VisitPlanVO">
        select cvp.id,  cvp.admin_id adminId, a.realname adminName
        from crm_escort_visit_plan cvp
        INNER JOIN admin a ON cvp.admin_id = a.admin_id
        WHERE cvp.visit_plan_id = #{visitPlanId}
    </select>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_escort_visit_plan
        where id = #{id}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_escort_visit_plan
        where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByVisitPlanId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_escort_visit_plan
        where  visit_plan_id=#{visitPlanId}
    </select>

    <select id="selectByVisitPlanIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_escort_visit_plan
        where  visit_plan_id  in
        <foreach collection="visitPlanIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
