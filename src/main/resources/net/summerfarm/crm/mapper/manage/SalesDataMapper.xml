<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.SalesDataMapper" >

    <select id="selectVisitNum" resultType="java.lang.Integer">
        select count(*) from follow_up_record fur
        where fur.admin_id = #{adminId} and fur.status = 1
        <if test="startTime!=null and endTime!=null">
            and  fur.add_time between #{startTime} and #{endTime}
        </if>
    </select>

    <select id="selectNewAdminOrderNum" resultType="java.lang.Integer">
        select IFNULL(count(DISTINCT m.m_id),0) from merchant m
        left join orders o on m.m_id = o.m_id
        left join follow_up_relation fur on m.m_id = fur.m_id
        where  o.status in (2, 3, 6) and fur.reassign = 0
        <if test="adminId != null">
            and fur.admin_id = #{adminId}
        </if>
        <if test="startTime!=null and endTime!=null ">
            and m.register_time between #{startTime} and #{endTime}
            and o.order_time between #{startTime} and #{endTime}
        </if>
        <if test="areaNo!=null and areaNo.size()>0">
            and m.area_no in
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type ==1">
            and fur.admin_id NOT IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
        <if test="type != null and type ==2">
            and fur.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
    </select>

    <select id="selectNewAdmin" resultType="java.lang.Integer">
        select IFNULL(count(DISTINCT m.m_id),0)
        from merchant_leads ml
        INNER JOIN merchant m ON m.m_id = ml.m_id
        INNER JOIN orders o on m.m_id = o.m_id
        where  o.status in (2, 3, 6) AND ml.`status` = 2
        <if test="adminId != null">
            and ml.admin_id = #{adminId}
        </if>
        <if test="startTime!=null and endTime!=null ">
            and m.register_time between #{startTime} and #{endTime}
            and o.order_time between #{startTime} and #{endTime}
        </if>
        <if test="administrativeCityList != null and administrativeCityList.size() != 0">
            AND m.city in
            <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="areaNo!=null and areaNo.size()>0">
            and m.area_no in
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type ==1">
            and ml.admin_id NOT IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
        <if test="type != null and type ==2">
            and ml.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
    </select>

    <select id="selectMonthLiving" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
        SELECT
            IFNULL(count(m.m_id),0) monthLiving,
            IFNULL(SUM(coalesce(reassign,0) = 1 ),0) openMonthLiving,
            IFNULL(SUM(coalesce(reassign,1) = 0),0) privateMonthLiving
        FROM merchant m
        INNER JOIN follow_up_relation f ON f.m_id = m.m_id
        WHERE m.last_order_time <![CDATA[ >= ]]> #{startTime}
        <if test="areaNo!=null and areaNo.size()>0">
            and m.area_no in
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="administrativeCityList != null and administrativeCityList.size() != 0">
            AND m.city in
            <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type == 1">
            AND f.admin_id NOT IN (SELECT admin_id FROM crm_bd_team WHERE is_locked = 1 AND type = 2)
            <if test="bigMerchantAdminIds != null and bigMerchantAdminIds.size() >0">
                AND (m.admin_id NOT IN
                <foreach collection="bigMerchantAdminIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR m.admin_id IS NULL)
            </if>
        </if>
        <if test="type != null and type ==2">
            and f.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)  AND f.reassign = 0
        </if>
    </select>

    <select id="selectMonthLivingByOrders" resultType="integer">
        SELECT
            count(DISTINCT f.m_id)
            FROM follow_up_relation f
            LEFT JOIN orders o ON f.m_id = o.m_id
            JOIN merchant m on f.m_id = m.m_id
        WHERE o.order_time BETWEEN #{startTime} AND #{endTime} AND o.status>0
        <if test="areaNo!=null and areaNo.size()>0">
            and o.area_no in
            <foreach collection="areaNo" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="administrativeCityList != null and administrativeCityList.size() > 0">
            and m.city in
            <foreach collection="administrativeCityList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type == 1">
            AND f.admin_id NOT IN (SELECT admin_id FROM crm_bd_team WHERE is_locked = 1 AND type = 2)
            <if test="bigMerchantAdminIds != null and bigMerchantAdminIds.size() >0">
                AND (o.admin_id NOT IN
                <foreach collection="bigMerchantAdminIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR o.admin_id IS NULL)
            </if>
        </if>
        <if test="type != null and type ==2">
            and f.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)  AND f.reassign = 0
        </if>
    </select>

    <select id="selectBDByArea" resultType="net.summerfarm.crm.model.vo.BdVO">
        SELECT DISTINCT a.admin_id bdId,a.realname bdName FROM crm_bd_area cba
        INNER JOIN admin a ON cba.admin_id = a.admin_id
        WHERE cba.area_no IN (SELECT area_no FROM crm_bd_area WHERE admin_id = #{adminId})
        <if test="bdName != null and bdName != ''">
            AND a.realname LIKE CONCAT('%',#{bdName},'%')
        </if>
    </select>

    <select id="salesTeamData" resultType="net.summerfarm.crm.model.vo.TeamDataVO" >
        SELECT m.m_id mId,m.mname ,m.area_no areaNo,m.size mSize,m.register_time registrationTime,m.last_order_time recentOrderTime,
        m.type mainType,if(f.reassign = 0,f.admin_name,'无归属BD') bdName
        FROM merchant m
        INNER JOIN follow_up_relation f ON m.m_id=f.m_id
        <where>
            <if test="startTime != null and endTime != null">
                m.m_id in
                (select DISTINCT (m_id) from orders o where m.m_id=o.m_id and order_time BETWEEN #{startTime} AND #{endTime})
            </if>
            <if test="bdName != null and bdName != '无归属BD'">
                AND f.admin_name = #{bdName}
                AND f.reassign = 0
            </if>
            <if test="bdName == '无归属BD'">
                AND f.reassign = 1
            </if>
            <if test="mName != null">
                AND m.mname LIKE CONCAT(#{mName},'%')
            </if>
            <if test="areaNos!=null and areaNos.size()>0">
                and m.area_no in
                <foreach collection="areaNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="mSize != null and mSize != '茶百道'">
                AND m.size = #{mSize}
                AND ( m.admin_id NOT IN
                <foreach collection="adminIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR m.admin_id IS NULL)
            </if>
            <if test="mSize != null and mSize == '茶百道'">
                AND m.admin_id IN
                <foreach collection="adminIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="phone != null">
                AND m.phone = #{phone}
            </if>
            <if test="grade != null and grade != -1">
                AND m.grade = #{grade}
            </if>
            <if test="grade != null and grade == -1">
                AND m.grade IS NULL
            </if>
            <if test="salesCityList != null and salesCityList.size() != 0">
                and (m.province, m.city, m.area) in
                <foreach collection="salesCityList" item="item" open="(" close=")" separator=",">
                    (#{item.province}, #{item.city}, #{item.area})
                </foreach>
            </if>
        </where>
        ORDER BY m.last_order_time DESC
    </select>
    <select id="selectLatestOrder" resultType="net.summerfarm.crm.model.vo.TeamDataVO">
        SELECT total_price recentGmv FROM orders WHERE status IN (2, 3, 6) AND m_id = #{mId} ORDER BY order_time DESC LIMIT 1
    </select>
    <select id="selectOrderDetails" resultType="net.summerfarm.crm.model.vo.TeamDataVO">
        SELECT m.m_id          mId,
               m.mname         mname,
               m.phone,
               m.province,
               m.city,
               m.area,
               m.address,
               m.size          mSize,
               m.register_time registrationTime,
               ar.area_name    areaName,
               f.admin_id      bdId,
               f.admin_name    bdName,
               cbt.type        salesTeam,
               m.admin_id      adminId,
               m.type          mainType,
               m.grade
        FROM merchant m
                 LEFT JOIN area ar ON ar.area_no = m.area_no
                 LEFT JOIN follow_up_relation f ON m.m_id = f.m_id AND f.reassign = 0
                 LEFT JOIN crm_bd_team cbt ON cbt.admin_id = f.admin_id AND cbt.is_locked = 1
        WHERE m.m_id = #{mId}
    </select>

    <select id="selectMerchantNum" resultType="net.summerfarm.crm.model.vo.AdminInfoVo">
        SELECT IFNULL(SUM(coalesce(m.size,0) = '单店'),0) singleShopNum,
        IFNULL(SUM(coalesce(m.size,0) = '大客户'),0) vipNum,
        count(DISTINCT m.admin_id) brandNum
        FROM follow_up_relation f
        INNER JOIN merchant m ON m.m_id = f.m_id
        WHERE f.admin_id = #{adminId} AND m.islock = 0 AND f.reassign = 0
    </select>
    <select id="selectOrderNum" resultType="net.summerfarm.crm.model.vo.AdminInfoVo">
        SELECT
        IFNULL(SUM(coalesce(m.size,0) = '单店'),0) singleShopOrderNum,
        IFNULL(SUM(coalesce(m.size,0) = '大客户'),0) vipOrderNum
        FROM follow_up_relation f
        INNER JOIN merchant m ON m.m_id = f.m_id
        WHERE f.admin_id = #{adminId} AND m.islock = 0 AND f.reassign = 0
        AND m.last_order_time  <![CDATA[ >= ]]> #{startTime}
    </select>

    <select id="selectSkuCategory" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
        SELECT p.pd_id pdId,p.pd_name pdName,b.`name` brandName,c.type categoryType
        FROM  products p
            left JOIN products_property_value ppv ON p.pd_id = ppv.pd_id AND ppv.products_property_id = 2
            INNER JOIN category c ON p.category_id = c.id
            left JOIN brand b ON b.`name` = ppv.products_property_value
        WHERE p.pd_name LIKE CONCAT(#{name} ,'%')
        GROUP BY p.pd_id
    </select>
    <select id="selectBdMerchantOrderNum" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
        SELECT
            IFNULL(SUM(coalesce(f.reassign,1) = 0),0) privateMerchantNoOrderNum,
            IFNULL(SUM(coalesce(f.reassign,0) = 1),0) openMerchantNoOrderNum
        FROM follow_up_relation f
        INNER JOIN merchant m ON m.m_id = f.m_id
        WHERE  m.islock = 0
        AND m.last_order_time <![CDATA[ >= ]]> #{startTime}
        <if test="areaNo!=null and areaNo.size()>0">
            AND m.area_no IN
            <foreach collection="areaNo" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type == 1 and adminIds != null and adminIds.size() > 0 ">
            AND f.admin_id IN
            <foreach collection="adminIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type == 2">
            AND f.reassign = 0 AND f.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
    </select>
    <select id="selectVisitByArea" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
        SELECT
            IFNULL(SUM(coalesce(f.visit_type,1) = 0),0) visitNum,
            IFNULL(SUM(coalesce(f.visit_type,0) = 1),0) escortNum,
            IFNULL(SUM(IF(f.follow_up_way = '普通上门拜访',1,0)),0) dropInVisitNum,
            IFNULL(SUM(IF(f.follow_up_way = '有效拜访',1,0)),0) efficientNum
        FROM follow_up_record f
        INNER JOIN merchant m ON m.m_id = f.m_id
        WHERE  f.add_time BETWEEN #{startTime} AND #{endTime}
        <if test="areaNo!=null and areaNo.size()>0">
            AND m.area_no IN
            <foreach collection="areaNo" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="administrativeCityList != null and administrativeCityList.size() != 0">
            AND m.city in
            <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type == 1 and adminIds != null and adminIds.size() > 0 ">
            AND f.admin_id IN
            <foreach collection="adminIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="type != null and type == 2">
            AND f.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE type =2 AND is_locked = 1)
        </if>
    </select>

    <select id="selectAdminIds" resultType="Integer">
        SELECT admin_id FROM crm_bd_team
    </select>

    <select id="selectAdminIdsByTypeLock" resultType="Integer">
        SELECT admin_id FROM crm_bd_team  where type =#{type} and is_locked=#{isLocked}
    </select>
</mapper>