<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmJobMerchantItemMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmJobMerchantItem">
    <!--@mbg.generated-->
    <!--@Table crm_job_merchant_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="job_id" jdbcType="BIGINT" property="jobId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="item" jdbcType="VARCHAR" property="item" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, job_id, m_id, item, item_type, `status`
  </sql>
</mapper>