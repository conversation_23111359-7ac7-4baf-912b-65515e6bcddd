<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.ActivityBasicInfoMapper">

    <select id="selectActivityPriceBySkuAndAreaNo" resultType="net.summerfarm.crm.model.dto.SkuActivityPriceDTO">
        select asp.sku,
        asp.activity_price AS activityPrice,
        abi.is_permanent AS isPermanent,
        abi.end_time AS endTime
        from activity_basic_info abi
        left join activity_sku_price asp on abi.id = asp.basic_info_id
        where abi.del_flag = 0
        and (abi.end_time &gt; NOW() or abi.is_permanent = 1)
        and abi.type = 0
        and asp.area_no = #{areaNo}
        and asp.sku in
        <foreach close=")" collection="skuCollection" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>