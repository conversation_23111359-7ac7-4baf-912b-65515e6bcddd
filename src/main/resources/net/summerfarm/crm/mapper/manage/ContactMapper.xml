<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.ContactMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.Contact">
            <id property="contactId" column="contact_id" jdbcType="BIGINT"/>
            <result property="mId" column="m_id" jdbcType="BIGINT"/>
            <result property="contact" column="contact" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="TINYINT"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="deliveryCar" column="delivery_car" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="isDefault" column="is_default" jdbcType="INTEGER"/>
            <result property="poiNote" column="poi_note" jdbcType="VARCHAR"/>
            <result property="distance" column="distance" jdbcType="DECIMAL"/>
            <result property="path" column="path" jdbcType="VARCHAR"/>
            <result property="houseNumber" column="house_number" jdbcType="VARCHAR"/>
            <result property="storeNo" column="store_no" jdbcType="INTEGER"/>
            <result property="deliveryFrequent" column="delivery_frequent" jdbcType="VARCHAR"/>
           <result property="addressRemark" column="address_remark" jdbcType="VARCHAR"/>


    </resultMap>

    <sql id="Base_Column_List">
        contact_id,m_id,contact,
        position,gender,phone,
        email,weixincode,province,
        city,area,address,
        delivery_car,status,remark,
        is_default,poi_note,distance,
        path,house_number,create_time,
        update_time,store_no,acm_id,
        back_store_no,delivery_frequent,delivery_rule,
        delivery_fee,address_remark
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from contact
        where  contact_id = #{contactId,jdbcType=BIGINT} 
    </select>

    <select id="selectByPrimaryKeys" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from contact
        where  contact_id in
        <foreach open="(" close=")" separator="," collection="ids" item="item" index="index">
            #{item}
        </foreach>
    </select>
    <select id="selectByMid" resultType="net.summerfarm.crm.model.domain.Contact">
        select contact_id        contactId,
               m_id              mId,
               contact,
               position,
               gender,
               phone,
               email,
               weixincode,
               province,
               city,
               area,
               address,
               delivery_car      deliveryCar,
               status,
               remark,
               is_default        isDefault,
               poi_note          poiNote,
               distance,
               path,
               house_number      houseNumber,
               create_time       createTime,
               update_time       updateTime,
               store_no          storeNo,
               acm_id            acmId,
               back_store_no     backStoreNo,
               delivery_frequent deliveryFrequent,
               delivery_rule     deliveryRule,
               delivery_fee      deliveryFee,
               address_remark    addressRemark
        from contact
        where m_id = #{mId} and status = 1 and is_default = 1
    </select>
    <select id="selectByPhone" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from contact where phone=#{phone}
        <if test="mId!=null">
            and m_id != #{mId}
        </if>
    </select>

    <insert id="insertSelective" keyColumn="contact_id" keyProperty="contactId" parameterType="net.summerfarm.crm.model.domain.Contact" useGeneratedKeys="true">
        insert into contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="contactId != null">contact_id,</if>
                <if test="mId != null">m_id,</if>
                <if test="contact != null">contact,</if>
                <if test="position != null">position,</if>
                <if test="gender != null">gender,</if>
                <if test="phone != null">phone,</if>
                <if test="email != null">email,</if>
                <if test="province != null">province,</if>
                <if test="city != null">city,</if>
                <if test="area != null">area,</if>
                <if test="address != null">address,</if>
                <if test="deliveryCar != null">delivery_car,</if>
                <if test="status != null">status,</if>
                <if test="remark != null">remark,</if>
                <if test="isDefault != null">is_default,</if>
                <if test="poiNote != null">poi_note,</if>
                <if test="distance != null">distance,</if>
                <if test="path != null">path,</if>
                <if test="houseNumber != null">house_number,</if>
                <if test="storeNo != null">store_no,</if>
                <if test="deliveryFrequent != null">delivery_frequent,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="contactId != null">#{contactId,jdbcType=BIGINT},</if>
                <if test="mId != null">#{mId,jdbcType=BIGINT},</if>
                <if test="contact != null">#{contact,jdbcType=VARCHAR},</if>
                <if test="position != null">#{position,jdbcType=VARCHAR},</if>
                <if test="gender != null">#{gender,jdbcType=TINYINT},</if>
                <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
                <if test="email != null">#{email,jdbcType=VARCHAR},</if>
                <if test="province != null">#{province,jdbcType=VARCHAR},</if>
                <if test="city != null">#{city,jdbcType=VARCHAR},</if>
                <if test="area != null">#{area,jdbcType=VARCHAR},</if>
                <if test="address != null">#{address,jdbcType=VARCHAR},</if>
                <if test="deliveryCar != null">#{deliveryCar,jdbcType=VARCHAR},</if>
                <if test="status != null">#{status,jdbcType=INTEGER},</if>
                <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
                <if test="isDefault != null">#{isDefault,jdbcType=INTEGER},</if>
                <if test="poiNote != null">#{poiNote,jdbcType=VARCHAR},</if>
                <if test="distance != null">#{distance,jdbcType=DECIMAL},</if>
                <if test="path != null">#{path,jdbcType=VARCHAR},</if>
                <if test="houseNumber != null">#{houseNumber,jdbcType=VARCHAR},</if>
                <if test="storeNo != null">#{storeNo,jdbcType=INTEGER},</if>
                <if test="deliveryFrequent != null">#{deliveryFrequent,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.Contact">
        update contact
        <set>
                <if test="mId != null">
                    m_id = #{mId,jdbcType=BIGINT},
                </if>
                <if test="contact != null">
                    contact = #{contact,jdbcType=VARCHAR},
                </if>
                <if test="position != null">
                    position = #{position,jdbcType=VARCHAR},
                </if>
                <if test="gender != null">
                    gender = #{gender,jdbcType=TINYINT},
                </if>
                <if test="phone != null">
                    phone = #{phone,jdbcType=VARCHAR},
                </if>
                <if test="email != null">
                    email = #{email,jdbcType=VARCHAR},
                </if>
                <if test="province != null">
                    province = #{province,jdbcType=VARCHAR},
                </if>
                <if test="city != null">
                    city = #{city,jdbcType=VARCHAR},
                </if>
                <if test="area != null">
                    area = #{area,jdbcType=VARCHAR},
                </if>
                <if test="address != null">
                    address = #{address,jdbcType=VARCHAR},
                </if>
                <if test="deliveryCar != null">
                    delivery_car = #{deliveryCar,jdbcType=VARCHAR},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=INTEGER},
                </if>
                <if test="remark != null">
                    remark = #{remark,jdbcType=VARCHAR},
                </if>
                <if test="isDefault != null">
                    is_default = #{isDefault,jdbcType=INTEGER},
                </if>
                <if test="poiNote != null">
                    poi_note = #{poiNote,jdbcType=VARCHAR},
                </if>
                <if test="distance != null">
                    distance = #{distance,jdbcType=DECIMAL},
                </if>
                <if test="path != null">
                    path = #{path,jdbcType=VARCHAR},
                </if>
                <if test="houseNumber != null">
                    house_number = #{houseNumber,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="storeNo != null">
                    store_no = #{storeNo,jdbcType=INTEGER},
                </if>
                <if test="acmId != null">
                    acm_id = #{acmId,jdbcType=INTEGER},
                </if>
                <if test="backStoreNo != null">
                    back_store_no = #{backStoreNo,jdbcType=INTEGER},
                </if>
                <if test="deliveryFrequent != null">
                    delivery_frequent = #{deliveryFrequent,jdbcType=VARCHAR},
                </if>
                <if test="deliveryRule != null">
                    delivery_rule = #{deliveryRule,jdbcType=VARCHAR},
                </if>
                <if test="deliveryFee != null">
                    delivery_fee = #{deliveryFee,jdbcType=DECIMAL},
                </if>
        </set>
        where   contact_id = #{contactId,jdbcType=BIGINT} 
    </update>
    <update id="updatePoiById">
        update contact
        set poi_note=#{poi}
        where contact_id = #{contactId,jdbcType=BIGINT}
    </update>

    <select id="selectByMidIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contact
        where m_id in
        <foreach open="(" close=")" separator="," collection="mIds" item="mId" index="index">
            #{mId}
        </foreach>
        and status = 1
    </select>
</mapper>
