<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantLeadsMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.MerchantLeads" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="mname" property="mname" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="area" property="area" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="poi_note" property="poiNote" jdbcType="VARCHAR" />
    <result column="area_no" property="areaNo"  />
    <result column="area_name" property="areaName" jdbcType="VARCHAR" />
    <result column="size" property="size" jdbcType="VARCHAR" />
    <result column="author" property="author" jdbcType="VARCHAR" />
    <result column="admin_id" property="adminId" jdbcType="INTEGER" />
    <result column="admin_name" property="adminName" jdbcType="VARCHAR" />
    <result column="source" property="source" jdbcType="VARCHAR" />
    <result column="mcontact" property="mcontact" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="house_number" property="houseNumber"/>
    <result column="type" property="type"/>
    <result column="m_id" property="mId"/>
    <result column="merchant_type" property="merchantType" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, mname, phone, province, city, area, address, poi_note, area_no, area_name, size, 
    author, admin_id, admin_name, source, mcontact, status, remark,house_number,`type`, m_id,merchant_type
  </sql>

    <select id="selectMerchantLeads" resultType="net.summerfarm.crm.model.vo.MerchantLeadsVO">
      select
      ml.id, ml.mname, ml.phone, ml.province, ml.city, ml.area, ml.address, ml.poi_note poiNote, a.area_no areaNo, a.area_name areaName, ml.size,
      ml.author, ml.admin_id adminId, ml.admin_name adminName, ml.source, ml.mcontact, ml.status, ml.remark,a.large_area_no storeNo,mcp.es_id esId,mcp.m_name clueMName,
      mcp.address clueAddress,mcp.phone cluePhone,ml.house_number houseNumber,ml.type,door_pic doorPic, ml.merchant_type merchantType
      from merchant_leads ml
      LEFT JOIN merchant_clue_pool mcp on mcp.ml_id = ml.id and mcp.status = 0
      left JOIN area a on a.area_no = ml.area_no
      <where>
        <if test="status  != null">
         and ml.status = #{status}
        </if>
        <if test="mname != null">
          and ml.mname like  CONCAT('%',#{mname},'%')
        </if>
        <if test="areaNo != null">
          and ml.area_no = #{areaNo}
        </if>
        <if test="address != null">
          and ml.address like concat('%',#{address},'%')
        </if>
        <if test="adminId != null">
          and ml.admin_id = #{adminId}
        </if>
        <if test="city != null">
          and ml.city = #{city}
        </if>
      </where>
      order by ml.addtime desc
    </select>
  <insert id="insertSelective" keyProperty="id"  useGeneratedKeys="true" parameterType="net.summerfarm.crm.model.domain.MerchantLeads" >
    insert into merchant_leads
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="mname != null" >
        mname,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="area != null" >
        area,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="poiNote != null" >
        poi_note,
      </if>
      <if test="areaNo != null" >
        area_no,
      </if>
      <if test="areaName != null" >
        area_name,
      </if>
      <if test="size != null" >
        size,
      </if>
      <if test="author != null" >
        author,
      </if>
      <if test="adminId != null" >
        admin_id,
      </if>
      <if test="adminName != null" >
        admin_name,
      </if>
      <if test="source != null" >
        source,
      </if>
      <if test="mcontact != null" >
        mcontact,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="houseNumber != null" >
        house_number,
      </if>
      <if test="enterpriseScale != null">
        enterprise_scale ,
      </if>
      <if test="companyBrand != null">
        company_brand ,
      </if>
      <if test="type != null">
        `type` ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mname != null" >
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null" >
        #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="areaName != null" >
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="author != null" >
        #{author,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null" >
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="adminName != null" >
        #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="mcontact != null" >
        #{mcontact,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null" >
        #{houseNumber},
      </if>
      <if test="enterpriseScale != null">
        #{enterpriseScale} ,
      </if>
      <if test="companyBrand != null">
       #{companyBrand},
      </if>
      <if test="type != null">
        #{type},
      </if>
    </trim>
  </insert>
  <insert id="insertOrUpdateById" parameterType="net.summerfarm.crm.model.domain.MerchantLeads" keyProperty="id"  useGeneratedKeys="true" >
    insert into merchant_leads
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="mname != null" >
        mname,
      </if>
      <if test="phone != null" >
        phone,
      </if>
      <if test="province != null" >
        province,
      </if>
      <if test="city != null" >
        city,
      </if>
      <if test="area != null" >
        area,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="poiNote != null" >
        poi_note,
      </if>
      <if test="areaNo != null" >
        area_no,
      </if>
      <if test="areaName != null" >
        area_name,
      </if>
      <if test="size != null" >
        size,
      </if>
      <if test="author != null" >
        author,
      </if>
      <if test="adminId != null" >
        admin_id,
      </if>
      <if test="adminName != null" >
        admin_name,
      </if>
      <if test="source != null" >
        source,
      </if>
      <if test="mcontact != null" >
        mcontact,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="houseNumber != null" >
        house_number,
      </if>
      <if test="enterpriseScale != null">
        enterprise_scale ,
      </if>
      <if test="companyBrand != null">
        company_brand ,
      </if>
      <if test="type != null">
        `type` ,
      </if>
      <if test="doorPic != null">
        `door_pic` ,
      </if>
      <if test="doorPicOcr != null">
        `door_pic_ocr` ,
      </if>
      <if test="merchantType != null">
        merchant_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mname != null" >
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null" >
        #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="areaName != null" >
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="author != null" >
        #{author,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null" >
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="adminName != null" >
        #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="mcontact != null" >
        #{mcontact,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null" >
        #{houseNumber},
      </if>
      <if test="enterpriseScale != null">
        #{enterpriseScale} ,
      </if>
      <if test="companyBrand != null">
        #{companyBrand},
      </if>
      <if test="type != null">
        #{type},
      </if>
      <if test="doorPic != null">
        #{doorPic},
      </if>
      <if test="doorPicOcr != null">
        #{doorPicOcr},
      </if>
      <if test="merchantType != null">
        #{merchantType,jdbcType=INTEGER},
      </if>
    </trim>
    ON DUPLICATE KEY
    UPDATE
    <trim suffixOverrides=",">
      updatetime = now(),
      <if test="mname != null" >
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null" >
        poi_note = #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="areaName != null" >
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        size = #{size,jdbcType=VARCHAR},
      </if>
      <if test="author != null" >
        author = #{author,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null" >
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="adminName != null" >
        admin_name = #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="mcontact != null" >
        mcontact = #{mcontact,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null" >
        house_number = #{houseNumber},
      </if>
      <if test="type != null" >
        `type` = #{type},
      </if>
      <if test="doorPic != null">
        `door_pic`=#{doorPic} ,
      </if>
      <if test="doorPicOcr != null">
        `door_pic_ocr`=#{doorPicOcr} ,
      </if>
    </trim>

  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.MerchantLeads" >
    update merchant_leads
    <set >
      updatetime = now(),
      <if test="mname != null" >
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null" >
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="poiNote != null" >
        poi_note = #{poiNote,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null" >
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="areaName != null" >
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="size != null" >
        size = #{size,jdbcType=VARCHAR},
      </if>
      <if test="author != null" >
        author = #{author,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null" >
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="adminName != null" >
        admin_name = #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="mcontact != null" >
        mcontact = #{mcontact,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="houseNumber != null" >
        house_number = #{houseNumber},
      </if>
      <if test="type != null" >
        `type` = #{type},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMname" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from merchant_leads where mname = #{mname}
  </select>
  <select id="selectById" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List"/>
    from merchant_leads where id = #{id}
  </select>
    <select id="selectByMId" resultMap="BaseResultMap">
      select
          <include refid="Base_Column_List"/>
      from merchant_leads
      where m_id = #{mId};
    </select>
</mapper>