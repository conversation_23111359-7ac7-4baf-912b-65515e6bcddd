<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CategoryCouponFeeRateMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CategoryCouponFeeRate">
    <!--@mbg.generated-->
    <!--@Table category_coupon_fee_rate-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="merchant_type" jdbcType="INTEGER" property="merchantType" />
    <result column="category_type" jdbcType="INTEGER" property="categoryType" />
    <result column="fee_rate" jdbcType="DECIMAL" property="feeRate" />
    <result column="category_coupon_quota_id" jdbcType="INTEGER" property="categoryCouponQuotaId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, merchant_type, category_type, fee_rate, category_coupon_quota_id
  </sql>
</mapper>