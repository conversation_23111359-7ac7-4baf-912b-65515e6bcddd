<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.SampleSkuMapper">

    <resultMap id="baseResultMap" type="net.summerfarm.crm.model.domain.SampleApply">
        <id column="sample_id" property="sampleId"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        <result column="m_id" property="mId"/>
        <result column="m_name" property="mName"/>
        <result column="contact_id" property="contactId"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="status" property="status"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="purchase_intention" property="purchaseIntention"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <insert id="insertSampleSku" keyColumn="id" keyProperty="id" useGeneratedKeys="true" parameterType="net.summerfarm.crm.model.domain.SampleSku">
        insert into sample_sku (sample_id,sku,pd_name,amount,weight)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.sampleId},#{item.sku},#{item.pdName},#{item.amount},#{item.weight})
        </foreach>
    </insert>

    <select id="selectBySampleId" parameterType="integer" resultType="net.summerfarm.crm.model.domain.SampleSku">
        /*FORCE_MASTER*/
        select id, sample_id sampleId, sku, pd_name pdName, amount, weight
        from sample_sku
        where sample_id = #{sampleId}
    </select>

<!--    <select id="selectSkuType" resultType="net.summerfarm.crm.model.domain.SampleSku">-->
<!--        SELECT p.pd_name pdName-->
<!--        FROM inventory i-->
<!--        LEFT JOIN products p ON i.pd_id=p.pd_id-->
<!--        WHERE i.outdated = 0 and i.sku = #{sku}-->
<!--    </select>-->

</mapper>