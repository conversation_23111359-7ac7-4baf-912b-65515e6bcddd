<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.RiskSimilarMerchantMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.RiskSimilarMerchant">
    <!--@mbg.generated-->
    <!--@Table risk_similar_merchant-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="door_pic" jdbcType="VARCHAR" property="doorPic" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="risk_merchant_id" jdbcType="BIGINT" property="riskMerchantId" />
    <result column="trigger_condition" jdbcType="VARCHAR" property="triggerCondition" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, m_id, mname, phone, area_no, area_name, bd_id, bd_name,
    door_pic, `size`, risk_merchant_id, trigger_condition
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from risk_similar_merchant
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.RiskSimilarMerchant" useGeneratedKeys="true">
    insert into risk_similar_merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="mname != null and mname != ''">
        mname,
      </if>
      <if test="phone != null and phone != ''">
        phone,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="areaName != null and areaName != ''">
        area_name,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="bdName != null and bdName != ''">
        bd_name,
      </if>
      <if test="doorPic != null and doorPic != ''">
        door_pic,
      </if>
      <if test="size != null and size != ''">
        `size`,
      </if>
      <if test="riskMerchantId != null">
        risk_merchant_id,
      </if>
      <if test="triggerCondition != null and triggerCondition != ''">
        trigger_condition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="mname != null and mname != ''">
        #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone != ''">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="areaName != null and areaName != ''">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null and bdName != ''">
        #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="doorPic != null and doorPic != ''">
        #{doorPic,jdbcType=VARCHAR},
      </if>
      <if test="size != null and size != ''">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="riskMerchantId != null">
        #{riskMerchantId,jdbcType=BIGINT},
      </if>
      <if test="triggerCondition != null and triggerCondition != ''">
        #{triggerCondition,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.RiskSimilarMerchant">
    update risk_similar_merchant
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="mname != null and mname != ''">
        mname = #{mname,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone != ''">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="areaName != null and areaName != ''">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null and bdName != ''">
        bd_name = #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="doorPic != null and doorPic != ''">
        door_pic = #{doorPic,jdbcType=VARCHAR},
      </if>
      <if test="size != null and size != ''">
        `size` = #{size,jdbcType=VARCHAR},
      </if>
      <if test="riskMerchantId != null">
        risk_merchant_id = #{riskMerchantId,jdbcType=BIGINT},
      </if>
      <if test="triggerCondition != null and triggerCondition != ''">
        trigger_condition = #{triggerCondition,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByRiskMerchantId" resultType="net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantVO">
    select m_id              mId,
           mname,
           phone,
           area_name         areaName,
           bd_name           bdName,
           size,
           trigger_condition triggerCondition,
           door_pic          doorPic
    from risk_similar_merchant
    where risk_merchant_id = #{riskMerchantId}
  </select>
</mapper>