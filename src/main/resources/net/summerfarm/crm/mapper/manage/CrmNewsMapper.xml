<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmNewsMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmNews">
        insert into crm_news (title, type, content, status, admin_id, creator,updater,warning_type)
        VALUES (#{title}, #{type}, #{content}, #{status}, #{adminId}, #{creator}, #{updater},#{warningType})
    </insert>

    <update id="update" parameterType="java.lang.Integer">
            update crm_news set status = 1 where admin_id = #{adminId} and status = 0
    </update>

    <select id="selectList" resultType="net.summerfarm.crm.model.vo.CrmNewsVO">
        select title, content, status,warning_type as warningType,creat_time as createTime,type
        from crm_news
        <where>
            admin_id = #{adminId}
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
        order by id desc
    </select>
</mapper>