<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantLifecycleMapper">
    <insert id="insertLifecycle">
    INSERT INTO merchant_lifecycle (m_id, lifecycle, tag, coupon_expire_date, purchasing_cycle_left, add_time, last_order_time)

    SELECT  t.m_id,2 as '生命周期',
    IF(t.orderNum=2,CONCAT('上升',t.`上次采购间隔`),CONCAT('下降',t.`上次采购间隔`-7)) '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    FORMAT(TIMESTAMPDIFF(DAY,DATE(t.`首次采购时间`),DATE(t.`上次采购时间`))/(t.orderNum-1),0)- TIMESTAMPDIFF(DAY,DATE(t.`上次采购时间`),CURDATE())  '预估采购周期剩余(单位:天)',NOW(), t.`上次采购时间`
    from (
    select m.m_id, m.register_time '注册时间',o.order_no ,
    MAX(o.order_time) '上次采购时间',
    MIN(o.order_time) '首次采购时间',
    TIMESTAMPDIFF(DAY,DATE(MAX(o.order_time)),CURDATE()) '上次采购间隔',
    COUNT(1) orderNum,
    SUM(o.total_price) '总支付金额',
    IF(COUNT(1)=2,TIMESTAMPDIFF(DAY,DATE(MIN(o.order_time)),DATE(MAX(o.order_time))),0) '初次复购时间间隔'
    FROM merchant m
    INNER JOIN orders o ON m.m_id = o.m_id AND o.`status` in (3,6)
    GROUP BY m.m_id
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id
    WHERE t.orderNum =2
    OR (t.orderNum >2 AND t.`上次采购间隔` >=7)
    GROUP BY t.m_id
    UNION ALL

    SELECT t.m_id,1 as '生命周期',
    NULL as '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    NULL as '预估采购周期剩余(单位:天)',NOW(), MAX(t.order_time)'上次采购时间'
    from
    (
    select m.m_id,m.mname '商户名称', m.register_time '注册时间', m.mcontact '联系人', m.phone '联系电话',CONCAT(m.city,m.area,m.address) '地址' ,o.order_no,
    COUNT(1) orderNum, o.order_time
    FROM merchant m
    INNER JOIN orders o ON m.m_id = o.m_id AND o.status in (3,6)
    GROUP BY m.m_id
    HAVING orderNum =1
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id
    GROUP BY t.m_id

    UNION ALL

    SELECT t.m_id,3 as '生命周期',
    NULL as '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    FORMAT(TIMESTAMPDIFF(DAY,DATE(t.`首次采购时间`),DATE(t.`上次采购时间`))/(t.orderNum-1),0)- TIMESTAMPDIFF(DAY,DATE(t.`上次采购时间`),CURDATE())  '预估采购周期剩余(单位:天)',
    NOW(), t.`上次采购时间`
    from (
    select m.m_id,m.mname '商户名称', m.register_time '注册时间', m.mcontact '联系人', m.phone '联系电话',CONCAT(m.city,m.area,m.address) '地址' ,o.order_no,
    MAX(o.order_time) '上次采购时间',
    MIN(o.order_time) '首次采购时间',
    COUNT(1) orderNum,
    SUM(o.total_price) '总支付金额'
    FROM merchant m
    INNER JOIN orders o ON m.m_id = o.m_id AND o.status in (3,6)
    GROUP BY m.m_id
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id
    WHERE t.orderNum >2 AND TIMESTAMPDIFF(DAY,DATE(t.`上次采购时间`),CURDATE()) <![CDATA[<]]> 7
    GROUP BY t.m_id

    UNION ALL

    SELECT t.m_id,0 as '生命周期',
    NULL as '标签1',
    TIMESTAMPDIFF(DAY,CURDATE()+1,DATE(t2.`优惠券到期时间`)) '优惠券到期时间',
    NULL as '预估采购周期剩余(单位:天)', NOW(), NULL as '上次采购时间'
    from (
    select m.m_id,m.mname '商户名称', m.register_time '注册时间', m.mcontact '联系人', m.phone '联系电话',CONCAT(m.city,m.area,m.address) '地址'
    FROM merchant m
    WHERE m.m_id NOT in (SELECT o.m_id from orders o where o.status in (3,6))
    AND m.islock = 0
    ) t
    LEFT JOIN (SELECT mc.id, mc.m_id, MIN(mc.vaild_date) '优惠券到期时间'  FROM merchant_coupon mc WHERE mc.used = 0 AND mc.vaild_date > NOW() GROUP BY mc.m_id) t2 on t2.m_id =t.m_id ;
  </insert>

</mapper>