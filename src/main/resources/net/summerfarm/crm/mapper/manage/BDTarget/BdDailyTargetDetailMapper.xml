<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDTarget.BdDailyTargetDetailMapper">

  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bd_daily_target_id" jdbcType="BIGINT" property="bdDailyTargetId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="target_type" jdbcType="INTEGER" property="targetType" />
    <result column="target_name" jdbcType="VARCHAR" property="targetName" />
    <result column="target_date" jdbcType="DATE" property="targetDate" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="indicator_type" jdbcType="INTEGER" property="indicatorType" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="sku" jdbcType="VARCHAR" property="sku" />
    <result column="spu" jdbcType="VARCHAR" property="spu" />
    <result column="indicator_expected_value" jdbcType="DECIMAL" property="indicatorExpectedValue" />
    <result column="indicator_status" jdbcType="INTEGER" property="indicatorStatus" />
    <result column="indicator_current_value" jdbcType="DECIMAL" property="indicatorCurrentValue" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, bd_daily_target_id, bd_id, bd_name, priority, target_type, target_name, target_date, business_type, indicator_type, category_name, sku, spu, indicator_expected_value, indicator_status, indicator_current_value, is_deleted
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_daily_target_detail
    where id = #{id,jdbcType=BIGINT}
  </select>

  <!-- 根据主键列表批量查询记录 -->
  <select id="selectByDailyTargetIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_daily_target_detail
    WHERE bd_daily_target_id IN
    <foreach collection="dailyTargetIds" item="dailyTargetId" open="(" close=")" separator=",">
      #{dailyTargetId,jdbcType=BIGINT}
    </foreach>
    AND is_deleted = 0
    ORDER BY id DESC
  </select>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_daily_target_detail (bd_daily_target_id, bd_id, bd_name, priority,
    target_type, target_name, target_date, business_type, indicator_type, category_name, sku, spu, indicator_expected_value, indicator_status, indicator_current_value)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bdDailyTargetId,jdbcType=BIGINT}, #{item.bdId,jdbcType=INTEGER}, #{item.bdName,jdbcType=VARCHAR}, #{item.priority,jdbcType=INTEGER},
      #{item.targetType,jdbcType=INTEGER}, #{item.targetName,jdbcType=VARCHAR}, #{item.targetDate,jdbcType=DATE}, #{item.businessType,jdbcType=INTEGER}, #{item.indicatorType,jdbcType=INTEGER}, #{item.categoryName,jdbcType=VARCHAR}, #{item.sku,jdbcType=VARCHAR}, #{item.spu,jdbcType=VARCHAR}, #{item.indicatorExpectedValue,jdbcType=DECIMAL}, #{item.indicatorStatus,jdbcType=INTEGER}, #{item.indicatorCurrentValue,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_daily_target_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bdDailyTargetId != null">
        bd_daily_target_id,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="bdName != null">
        bd_name,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetName != null">
        target_name,
      </if>
      <if test="targetDate != null">
        target_date,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="indicatorType != null">
        indicator_type,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="sku != null">
        sku,
      </if>
      <if test="spu != null">
        spu,
      </if>
      <if test="indicatorExpectedValue != null">
        indicator_expected_value,
      </if>
      <if test="indicatorStatus != null">
        indicator_status,
      </if>
      <if test="indicatorCurrentValue != null">
        indicator_current_value,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bdDailyTargetId != null">
        #{bdDailyTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetName != null">
        #{targetName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        #{targetDate,jdbcType=DATE},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="indicatorType != null">
        #{indicatorType,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="spu != null">
        #{spu,jdbcType=VARCHAR},
      </if>
      <if test="indicatorExpectedValue != null">
        #{indicatorExpectedValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        #{indicatorStatus,jdbcType=INTEGER},
      </if>
      <if test="indicatorCurrentValue != null">
        #{indicatorCurrentValue,jdbcType=DECIMAL},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailUpdate">
    update crm_bd_daily_target_detail
    <set>
      <if test="bdDailyTargetId != null">
        bd_daily_target_id = #{bdDailyTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bdName != null">
        bd_name = #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetName != null">
        target_name = #{targetName,jdbcType=VARCHAR},
      </if>
      <if test="targetDate != null">
        target_date = #{targetDate,jdbcType=DATE},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="indicatorType != null">
        indicator_type = #{indicatorType,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="spu != null">
        spu = #{spu,jdbcType=VARCHAR},
      </if>
      <if test="indicatorExpectedValue != null">
        indicator_expected_value = #{indicatorExpectedValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorStatus != null">
        indicator_status = #{indicatorStatus,jdbcType=INTEGER},
      </if>
      <if test="indicatorCurrentValue != null">
        indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_bd_daily_target_detail
    <where>
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="bdDailyTargetId != null">
        and bd_daily_target_id = #{bdDailyTargetId,jdbcType=BIGINT}
      </if>
      <if test="bdId != null">
        and bd_id = #{bdId,jdbcType=INTEGER}
      </if>
      <if test="bdName != null and bdName != ''">
        and bd_name = #{bdName,jdbcType=VARCHAR}
      </if>
      <if test="priority != null">
        and priority = #{priority,jdbcType=INTEGER}
      </if>
      <if test="targetType != null">
        and target_type = #{targetType,jdbcType=INTEGER}
      </if>
      <if test="targetName != null and targetName != ''">
        and target_name = #{targetName,jdbcType=VARCHAR}
      </if>
      <if test="targetDate != null">
        and target_date = #{targetDate,jdbcType=DATE}
      </if>
      <if test="businessType != null">
        and business_type = #{businessType,jdbcType=INTEGER}
      </if>
      <if test="indicatorType != null">
        and indicator_type = #{indicatorType,jdbcType=INTEGER}
      </if>
      <if test="categoryName != null and categoryName != ''">
        and category_name = #{categoryName,jdbcType=VARCHAR}
      </if>
      <if test="sku != null and sku != ''">
        and sku = #{sku,jdbcType=VARCHAR}
      </if>
      <if test="spu != null and spu != ''">
        and spu = #{spu,jdbcType=VARCHAR}
      </if>
      <if test="indicatorExpectedValue != null">
        and indicator_expected_value = #{indicatorExpectedValue,jdbcType=DECIMAL}
      </if>
      <if test="indicatorStatus != null">
        and indicator_status = #{indicatorStatus,jdbcType=INTEGER}
      </if>
      <if test="indicatorCurrentValue != null">
        and indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="isDeleted != null">
        and is_deleted = #{isDeleted,jdbcType=INTEGER}
      </if>
    </where>
    order by id desc
  </select>

  <!-- 更新单条指标当前值和状态 -->
  <update id="updateIndicatorValueAndStatus" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetailSync">
    UPDATE crm_bd_daily_target_detail
    SET indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL},
        indicator_status = 
            CASE 
                WHEN #{indicatorCurrentValue,jdbcType=DECIMAL} >= indicator_expected_value THEN 1
                ELSE 0
            END,
        update_time = NOW()
    WHERE id = #{bdDailyTargetDetailId,jdbcType=BIGINT}
  </update>

  <!-- 根据销售每日目标ID列表查询明细 -->
  <select id="listByBdDailyTargetIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_daily_target_detail
    WHERE bd_daily_target_id IN
    <foreach collection="bdDailyTargetIds" item="bdDailyTargetId" open="(" close=")" separator=",">
      #{bdDailyTargetId,jdbcType=BIGINT}
    </foreach>
    AND is_deleted = 0
  </select>

  <!-- 根据ID列表批量查询销售拜访目标指标 -->
  <select id="selectByIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_daily_target_detail
    WHERE id IN
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
    AND is_deleted = 0
  </select>


  <!-- 根据销售每日目标ID列表批量逻辑删除明细 -->
  <update id="logicDeleteByBdDailyTargetIds">
    UPDATE crm_bd_daily_target_detail
    SET is_deleted = 1,
    update_time = NOW()
    WHERE bd_daily_target_id IN
    <foreach collection="bdDailyTargetIds" item="bdDailyTargetId" open="(" separator="," close=")">
      #{bdDailyTargetId,jdbcType=BIGINT}
    </foreach>
  </update>

  <!-- 根据明细ID列表查询销售每日目标明细 -->
  <select id="listByDetailIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_daily_target_detail
    WHERE id IN
    <foreach collection="detailIds" item="detailId" open="(" close=")" separator=",">
      #{detailId,jdbcType=BIGINT}
    </foreach>
  </select>

</mapper>