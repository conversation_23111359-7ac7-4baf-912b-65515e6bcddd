<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmClueMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmClue">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_source" jdbcType="TINYINT" property="customerSource" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="kp" jdbcType="VARCHAR" property="kp" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="dept" jdbcType="VARCHAR" property="dept" />
    <result column="post" jdbcType="VARCHAR" property="post" />
    <result column="clue_status" jdbcType="VARCHAR" property="clueStatus" />
    <result column="clue_source" jdbcType="VARCHAR" property="clueSource" />
    <result column="want_business" jdbcType="VARCHAR" property="wantBusiness" />
    <result column="area_code" jdbcType="INTEGER" property="areaCode" />
    <result column="purpose_remark" jdbcType="VARCHAR" property="purposeRemark" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="shop_type" jdbcType="VARCHAR" property="shopType" />
    <result column="shop_size" jdbcType="VARCHAR" property="shopSize" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="shop_system" jdbcType="VARCHAR" property="shopSystem" />
    <result column="head_system" jdbcType="VARCHAR" property="headSystem" />
    <result column="send_remark" jdbcType="VARCHAR" property="sendRemark" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="b_id" jdbcType="BIGINT" property="bId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_bd_id" jdbcType="INTEGER" property="createBdId" />
    <result column="assist_bd_id" jdbcType="INTEGER" property="assistBdId" />
    <result column="last_follow_time" jdbcType="TIMESTAMP" property="lastFollowTime" />
    <result column="area_code_name" jdbcType="VARCHAR" property="areaCodeName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, customer_source, customer_name, kp, phone, dept, post, clue_status, clue_source, 
    want_business, area_code, purpose_remark, business_type, shop_type, shop_size, customer_type, 
    shop_system, head_system, send_remark, remark, bd_id, b_id, create_time, update_time, 
    create_bd_id, assist_bd_id, last_follow_time, area_code_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_clue
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_clue
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmClue" useGeneratedKeys="true">
    insert into crm_clue (customer_source, customer_name, kp, 
      phone, dept, post, 
      clue_status, clue_source, want_business, 
      area_code, purpose_remark, business_type, 
      shop_type, shop_size, customer_type, 
      shop_system, head_system, send_remark, 
      remark, bd_id, b_id, create_time, 
      update_time, create_bd_id, assist_bd_id, 
      last_follow_time, area_code_name)
    values (#{customerSource,jdbcType=TINYINT}, #{customerName,jdbcType=VARCHAR}, #{kp,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{dept,jdbcType=VARCHAR}, #{post,jdbcType=VARCHAR}, 
      #{clueStatus,jdbcType=VARCHAR}, #{clueSource,jdbcType=VARCHAR}, #{wantBusiness,jdbcType=VARCHAR}, 
      #{areaCode,jdbcType=INTEGER}, #{purposeRemark,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{shopType,jdbcType=VARCHAR}, #{shopSize,jdbcType=VARCHAR}, #{customerType,jdbcType=VARCHAR}, 
      #{shopSystem,jdbcType=VARCHAR}, #{headSystem,jdbcType=VARCHAR}, #{sendRemark,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{bdId,jdbcType=INTEGER}, #{bId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createBdId,jdbcType=INTEGER}, #{assistBdId,jdbcType=INTEGER}, 
      #{lastFollowTime,jdbcType=TIMESTAMP}, #{areaCodeName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmClue" useGeneratedKeys="true">
    insert into crm_clue
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerSource != null">
        customer_source,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="kp != null">
        kp,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="dept != null">
        dept,
      </if>
      <if test="post != null">
        post,
      </if>
      <if test="clueStatus != null">
        clue_status,
      </if>
      <if test="clueSource != null">
        clue_source,
      </if>
      <if test="wantBusiness != null">
        want_business,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="purposeRemark != null">
        purpose_remark,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="shopType != null">
        shop_type,
      </if>
      <if test="shopSize != null">
        shop_size,
      </if>
      <if test="customerType != null">
        customer_type,
      </if>
      <if test="shopSystem != null">
        shop_system,
      </if>
      <if test="headSystem != null">
        head_system,
      </if>
      <if test="sendRemark != null">
        send_remark,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="bId != null">
        b_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBdId != null">
        create_bd_id,
      </if>
      <if test="assistBdId != null">
        assist_bd_id,
      </if>
      <if test="lastFollowTime != null">
        last_follow_time,
      </if>
      <if test="areaCodeName != null">
        area_code_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerSource != null">
        #{customerSource,jdbcType=TINYINT},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="kp != null">
        #{kp,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="dept != null">
        #{dept,jdbcType=VARCHAR},
      </if>
      <if test="post != null">
        #{post,jdbcType=VARCHAR},
      </if>
      <if test="clueStatus != null">
        #{clueStatus,jdbcType=VARCHAR},
      </if>
      <if test="clueSource != null">
        #{clueSource,jdbcType=VARCHAR},
      </if>
      <if test="wantBusiness != null">
        #{wantBusiness,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=INTEGER},
      </if>
      <if test="purposeRemark != null">
        #{purposeRemark,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="shopType != null">
        #{shopType,jdbcType=VARCHAR},
      </if>
      <if test="shopSize != null">
        #{shopSize,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="shopSystem != null">
        #{shopSystem,jdbcType=VARCHAR},
      </if>
      <if test="headSystem != null">
        #{headSystem,jdbcType=VARCHAR},
      </if>
      <if test="sendRemark != null">
        #{sendRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bId != null">
        #{bId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBdId != null">
        #{createBdId,jdbcType=INTEGER},
      </if>
      <if test="assistBdId != null">
        #{assistBdId,jdbcType=INTEGER},
      </if>
      <if test="lastFollowTime != null">
        #{lastFollowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaCodeName != null">
        #{areaCodeName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmClue">
    update crm_clue
    <set>
      <if test="customerSource != null">
        customer_source = #{customerSource,jdbcType=TINYINT},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="kp != null">
        kp = #{kp,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="dept != null">
        dept = #{dept,jdbcType=VARCHAR},
      </if>
      <if test="post != null">
        post = #{post,jdbcType=VARCHAR},
      </if>
      <if test="clueStatus != null">
        clue_status = #{clueStatus,jdbcType=VARCHAR},
      </if>
      <if test="clueSource != null">
        clue_source = #{clueSource,jdbcType=VARCHAR},
      </if>
      <if test="wantBusiness != null">
        want_business = #{wantBusiness,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=INTEGER},
      </if>
      <if test="purposeRemark != null">
        purpose_remark = #{purposeRemark,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="shopType != null">
        shop_type = #{shopType,jdbcType=VARCHAR},
      </if>
      <if test="shopSize != null">
        shop_size = #{shopSize,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        customer_type = #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="shopSystem != null">
        shop_system = #{shopSystem,jdbcType=VARCHAR},
      </if>
      <if test="headSystem != null">
        head_system = #{headSystem,jdbcType=VARCHAR},
      </if>
      <if test="sendRemark != null">
        send_remark = #{sendRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="bId != null">
        b_id = #{bId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBdId != null">
        create_bd_id = #{createBdId,jdbcType=INTEGER},
      </if>
      <if test="assistBdId != null">
        assist_bd_id = #{assistBdId,jdbcType=INTEGER},
      </if>
      <if test="lastFollowTime != null">
        last_follow_time = #{lastFollowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaCodeName != null">
        area_code_name = #{areaCodeName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmClue">
    update crm_clue
    set customer_source = #{customerSource,jdbcType=TINYINT},
      customer_name = #{customerName,jdbcType=VARCHAR},
      kp = #{kp,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      dept = #{dept,jdbcType=VARCHAR},
      post = #{post,jdbcType=VARCHAR},
      clue_status = #{clueStatus,jdbcType=VARCHAR},
      clue_source = #{clueSource,jdbcType=VARCHAR},
      want_business = #{wantBusiness,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=INTEGER},
      purpose_remark = #{purposeRemark,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      shop_type = #{shopType,jdbcType=VARCHAR},
      shop_size = #{shopSize,jdbcType=VARCHAR},
      customer_type = #{customerType,jdbcType=VARCHAR},
      shop_system = #{shopSystem,jdbcType=VARCHAR},
      head_system = #{headSystem,jdbcType=VARCHAR},
      send_remark = #{sendRemark,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      bd_id = #{bdId,jdbcType=INTEGER},
      b_id = #{bId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_bd_id = #{createBdId,jdbcType=INTEGER},
      assist_bd_id = #{assistBdId,jdbcType=INTEGER},
      last_follow_time = #{lastFollowTime,jdbcType=TIMESTAMP},
      area_code_name = #{areaCodeName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByBId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_clue
    where b_id = #{bId,jdbcType=BIGINT}
  </select>

  <select id="detail" parameterType="net.summerfarm.crm.model.query.ClueDetailQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_clue
    <where>
      <if test="clueId != null">
        id = #{clueId}
      </if>
      <if test="bId !=null">
        and  b_id=#{bId}
      </if>
      <if test="customerName!='' and customerName!=null">
        and customer_name =#{customerName}
      </if>
    </where>

  </select>

  <select id="query" parameterType="net.summerfarm.crm.model.query.SaasClueQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_clue
    <where>
      <if test="bId !=null">
        and  b_id=#{bId}
      </if>
      <if test="customerName!='' and customerName!=null">
        and customer_name like concat('%',#{customerName},'%')
      </if>
      <if test="customerSource!=null">
        and customer_source = #{customerSource}
      </if>
      <if test="customerType!='' and customerType!=null">
        and customer_type =#{customerType}
      </if>
      <if test="clueSource!='' and clueSource!=null">
        and clue_source =#{clueSource}
      </if>
      <if test="businessType!='' and businessType!=null">
        and business_type =#{businessType}
      </if>
      <if test="areaCodes!=null and areaCodes.size()>0">
        and area_code in
        <foreach collection="areaCodes" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="clueStatusList!=null and clueStatusList.size()>0">
        and clue_status in
        <foreach collection="clueStatusList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="wantBusinessList!=null and wantBusinessList.size()>0">
        and
        <foreach collection="wantBusinessList" item="item" open="(" close=")" separator="or">
          want_business like concat('%',#{item},'%')
        </foreach>
      </if>
    </where>
    order by id desc

  </select>
</mapper>