<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmManageAreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmManageArea">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="manage_admin_id" jdbcType="INTEGER" property="manageAdminId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, manage_admin_id, area_no, creator, create_time
  </sql>

  <select id="selectManageArea" resultType="net.summerfarm.crm.model.vo.ManageAreaVo">
    SELECT cmb.id id,cmb.parent_admin_id parentAdminId,cmb.manage_admin_id manageAdminId,cmb.zone_name zoneName,cmb.department_admin_id departmentAdminId
    ,a.realname parentAdminName,ad.realname manageAdminName,adm.realname departmentAdminName
    FROM crm_manage_bd cmb
    LEFT JOIN crm_manage_area cma ON cmb.id = cma.mb_id
    LEFT JOIN admin a ON a.admin_id = cmb.parent_admin_id
    LEFT JOIN admin ad ON ad.admin_id = cmb.manage_admin_id
    LEFT JOIN admin adm ON adm.admin_id = cmb.department_admin_id
    <where>
      <if test="areaNo!=null">
        AND area_no IN
        <foreach collection="areaNo" item="item" close=")" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="parentAdminId !=null ">
        AND cmb.parent_admin_id = #{parentAdminId}
      </if>
      <if test="departmentAdminId !=null ">
        AND cmb.department_admin_id = #{departmentAdminId}
      </if>
      <if test="zoneName!=null and zoneName !=''">
        AND cmb.zone_name LIKE CONCAT('%',#{zoneName},'%')
      </if>
    </where>
    GROUP BY cmb.zone_name
    ORDER BY cmb.create_time DESC
  </select>
  <select id="selectArea" resultType="net.summerfarm.crm.model.dto.CrmBdAreaDTO">
    select cma.area_no as areaNo,a.area_name as areaName
    from crm_manage_area cma
     left join area a on cma.area_no = a.area_no
    <where>
      <if test="id!=null">
        and mb_id = #{id}
      </if>
      <if test="saveManageAreaInputId!=null">
        and mb_id != #{saveManageAreaInputId}
      </if>
    </where>
  </select>
  <select id="selectManageByBdId" resultType="net.summerfarm.crm.model.vo.ManageAreaVo">
    select a.`realname` manageAdminName, a.admin_id manageAdminId
    from `crm_bd_config` bc
           LEFT JOIN crm_manage_administrative_city ma on bc.`administrative_city` = ma.administrative_city
           LEFT JOIN `crm_manage_bd` mb on mb.`id` = ma.`mb_id`
           LEFT JOIN `admin` a on a.`admin_id` = mb.`manage_admin_id`
    where bc.admin_id=#{bdId}  and ma.delete_flag=0
  </select>

  <delete id="deleteArea">
    delete from  crm_manage_area where  mb_id = #{mbId}

  </delete>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmManageArea" useGeneratedKeys="true">
    insert into crm_manage_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="manageAdminId != null">
        manage_admin_id,
      </if>
      <if test="areaNo != null">
        area_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="manageAdminId != null">
        #{manageAdminId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>


  <insert id="insertArea">
    insert into crm_manage_area(mb_id,area_no,creator,create_time)
    values
    <foreach collection="city" item="item" separator=",">
        (#{mbId},#{item},#{adminId},sysdate())
    </foreach>

  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmManageArea">
    update crm_manage_area
    <set>
      <if test="manageAdminId != null">
        manage_admin_id = #{manageAdminId,jdbcType=INTEGER},
      </if>
      <if test="areaNo != null">
        area_no = #{areaNo,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>