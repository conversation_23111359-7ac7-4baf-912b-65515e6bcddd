<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.FileDownloadRecordMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.FileDownloadRecord">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
        <result property="params" column="params" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="base_column_filed">
        id,status,type,file_name,admin_id,params,create_time,update_time
    </sql>

    <select id="selectByAdminId" resultMap="BaseResultMap">
        select <include refid="base_column_filed"/>
        from file_download_record
        where admin_id = #{adminId}
        order by id desc
    </select>

    <insert id="insert" parameterType="net.summerfarm.crm.model.domain.FileDownloadRecord">
        insert into file_download_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                file_name,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="uId != null">
                u_id,
            </if>
            <if test="params != null">
                params,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">
                #{fileName},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="uId != null">
                #{uId},
            </if>
            <if test="params != null">
                #{params},
            </if>
        </trim>
    </insert>

    <update id="update">
        update file_download_record
        set status = #{status}
        where u_id = #{uId}
    </update>

    <update id="updateFileName" parameterType="net.summerfarm.crm.model.domain.FileDownloadRecord">
        update file_download_record
        set status = #{status},
            update_time = now()
        where file_name = #{fileName} and status = 0
    </update>
    <select id="selectByUid" resultMap="BaseResultMap">
        SELECT <include refid="base_column_filed"/> FROM file_download_record WHERE u_id = #{uId} LIMIT 1
    </select>

    <delete id="delete" parameterType="long">
        delete from file_download_record
        where id = #{id}
    </delete>

    <delete id="deleteAll" parameterType="long">
        delete from file_download_record
        where admin_id = #{adminId}
    </delete>

</mapper>
