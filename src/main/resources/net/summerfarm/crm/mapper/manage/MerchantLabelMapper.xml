<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantLabelMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.MerchantLabel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="auditor" jdbcType="INTEGER" property="auditor" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, name, status, type, creator, create_time, auditor, update_time
  </sql>

  <select id="selectByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_label where name=#{name}
  </select>
  <select id="selectListMerchantLabel" resultType="net.summerfarm.crm.model.domain.MerchantSubAccount">
      select msc.unionid, msc.m_id mId
      from merchant_sub_account msc
      where m_id NOT IN (select msc.m_id
                         from merchant_sub_account msc
                                  INNER JOIN merchant_label_correlation mlc
                                             on msc.m_id = mlc.m_id
                                  INNER JOIN merchant_label ml on mlc.label_id = ml.id
      where msc.type = 0 and msc.delete_flag = 1 and ml.name = #{name} and msc.unionid in
      <foreach collection="unionIds" item="unionId" open="(" separator="," close=")">
          #{unionId}
      </foreach>
      ) and msc.unionid in
      <foreach collection="unionIds" item="unionId" open="(" separator="," close=")">
          #{unionId}
      </foreach>
      and msc.type = 0 and msc.delete_flag = 1
      group by msc.unionid
  </select>
    <select id="selectByUnionIdAndName" resultMap="BaseResultMap">
        select ml.id, ml.name, ml.status, ml.type
        from merchant_label ml
                 left join merchant_label_correlation mlc on mlc.label_id = ml.id
                 left join merchant_sub_account msc on msc.m_id = mlc.m_id
        where msc.type = 0
          and msc.delete_flag = 1
          and msc.unionid = #{unionId}
          and ml.name = #{name}
        group by msc.unionid
    </select>
  <select id="selectByMidAndName" resultType="java.lang.String">
    select
    ml.name
    from merchant_label ml
    left join merchant_label_correlation mlc on mlc.label_id = ml.id
    where mlc.m_id=#{mId}
    <if test="name!=null">
      and ml.name =#{name}
    </if>
  </select>

    <select id="selectByMidAndId" resultType="java.lang.String">
        select ml.name
        from merchant_label ml
                 join merchant_label_correlation mlc on mlc.label_id = ml.id
        where mlc.m_id = #{mId}
        <if test="ids != null and ids.size() != 0">
            and ml.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>