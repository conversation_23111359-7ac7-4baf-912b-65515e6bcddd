<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmCommissionCoreMerchantMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCommissionCoreMerchant">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="minimum" jdbcType="INTEGER" property="minimum" />
    <result column="maximum" jdbcType="INTEGER" property="maximum" />
    <result column="proportion" jdbcType="DECIMAL" property="proportion" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `level`, minimum, maximum, proportion, delete_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_commission_core_merchant
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCommissionCoreMerchant" useGeneratedKeys="true">
    insert into crm_commission_core_merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="level != null">
        `level`,
      </if>
      <if test="minimum != null">
        minimum,
      </if>
      <if test="maximum != null">
        maximum,
      </if>
      <if test="proportion != null">
        proportion,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="minimum != null">
        #{minimum,jdbcType=INTEGER},
      </if>
      <if test="maximum != null">
        #{maximum,jdbcType=INTEGER},
      </if>
      <if test="proportion != null">
        #{proportion,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName},
      </if>
      <if test="updateName != null">
        #{updateName},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmCommissionCoreMerchant">
    update crm_commission_core_merchant
    <set>
      <if test="level != null">
        `level` = #{level,jdbcType=TINYINT},
      </if>
      <if test="minimum != null">
        minimum = #{minimum,jdbcType=INTEGER},
      </if>
      <if test="maximum != null">
        maximum = #{maximum,jdbcType=INTEGER},
      </if>
      <if test="proportion != null">
        proportion = #{proportion,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectCoreMerchantsNetGrowth" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_commission_core_merchant
    where delete_flag = 0
    order by proportion
  </select>
  <insert id="insertOrUpdateById" parameterType="net.summerfarm.crm.model.domain.CrmCommissionCoreMerchant">
    insert into crm_commission_core_merchant
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="minimum != null">
        minimum,
      </if>
      <if test="maximum != null">
        maximum,
      </if>
      <if test="proportion != null">
        proportion,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="minimum != null">
        #{minimum,jdbcType=INTEGER},
      </if>
      <if test="maximum != null">
        #{maximum,jdbcType=INTEGER},
      </if>
      <if test="proportion != null">
        #{proportion,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName},
      </if>
      <if test="updateName != null">
        #{updateName},
      </if>
    </trim>
    ON DUPLICATE KEY
    UPDATE
    <trim suffixOverrides=",">
      <if test="level != null">
        `level` = #{level,jdbcType=TINYINT},
      </if>
      <if test="minimum != null">
        minimum = #{minimum,jdbcType=INTEGER},
      </if>
      <if test="maximum != null">
        maximum = #{maximum,jdbcType=INTEGER},
      </if>
      <if test="proportion != null">
        proportion = #{proportion,jdbcType=DECIMAL},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName},
      </if>
    </trim>
  </insert>
</mapper>