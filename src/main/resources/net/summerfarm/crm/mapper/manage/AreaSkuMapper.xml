<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.AreaSkuMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.AreaSku">
        <!--@mbg.generated-->
        <!--@Table area_sku-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sku" jdbcType="VARCHAR" property="sku"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="share" jdbcType="BOOLEAN" property="share"/>
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="on_sale" jdbcType="BOOLEAN" property="onSale"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="pd_priority" jdbcType="INTEGER" property="pdPriority"/>
        <result column="ladder_price" jdbcType="VARCHAR" property="ladderPrice"/>
        <result column="limited_quantity" jdbcType="INTEGER" property="limitedQuantity"/>
        <result column="sales_mode" jdbcType="INTEGER" property="salesMode"/>
        <result column="show" jdbcType="INTEGER" property="show"/>
        <result column="info" jdbcType="VARCHAR" property="info"/>
        <result column="m_type" jdbcType="INTEGER" property="mType"/>
        <result column="show_advance" jdbcType="BOOLEAN" property="showAdvance"/>
        <result column="advance" jdbcType="VARCHAR" property="advance"/>
        <result column="corner_status" jdbcType="INTEGER" property="cornerStatus"/>
        <result column="corner_open_time" jdbcType="TIMESTAMP" property="cornerOpenTime"/>
        <result column="open_sale" jdbcType="INTEGER" property="openSale"/>
        <result column="open_sale_time" jdbcType="TIMESTAMP" property="openSaleTime"/>
        <result column="close_sale" jdbcType="INTEGER" property="closeSale"/>
        <result column="close_sale_time" jdbcType="TIMESTAMP" property="closeSaleTime"/>
        <result column="fix_flag" jdbcType="INTEGER" property="fixFlag"/>
        <result column="fix_num" jdbcType="INTEGER" property="fixNum"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, sku, area_no, quantity, `share`, original_price, price, update_time, on_sale,
        add_time, priority, pd_priority, ladder_price, limited_quantity, sales_mode, `show`,
        info, m_type, show_advance, advance, corner_status, corner_open_time, open_sale,
        open_sale_time, close_sale, close_sale_time, fix_flag, fix_num, updater
    </sql>
    <select id="selectPriceBySkuAndAreaNo" resultType="java.math.BigDecimal">
        select price from area_sku where area_no=#{areaNo} and sku=#{sku}
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-10-08-->
    <select id="selectSkuBySkuIn" resultType="java.lang.String">
        select distinct sku
        from area_sku
        where sku in
        <foreach item="item" index="index" collection="skuCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-10-08-->
    <select id="selectOneBySkuAndAreaNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area_sku
        where sku=#{sku,jdbcType=VARCHAR} and area_no=#{areaNo,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-10-14-->
    <select id="selectBySkuInAndAreaNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area_sku
        where sku in
        <foreach item="item" index="index" collection="skuCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and area_no=#{areaNo,jdbcType=INTEGER}
    </select>
</mapper>