<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.WechatUserInfoMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.WechatUserInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="unionid" jdbcType="VARCHAR" property="unionid"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="external_userid" jdbcType="VARCHAR" property="externalUserid"/>
        <result column="admin_id" jdbcType="BIGINT" property="adminId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        user_id,
        unionid,
        add_time,
        status,state,
        external_userid,
        admin_id,
        create_time,
        update_time
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.WechatUserInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into wechat_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="unionid != null">
                unionid,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="externalUserid != null">
                external_userid,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="externalUserid != null">
                #{externalUserid,jdbcType=VARCHAR},
            </if>
            <if test="adminId != null">
                #{adminId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.WechatUserInfo">
        <!--@mbg.generated-->
        update wechat_user_info
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                unionid = #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="externalUserid != null">
                external_userid = #{externalUserid,jdbcType=VARCHAR},
            </if>
            <if test="adminId != null">
                admin_id = #{adminId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByUserIdAndUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wechat_user_info
        where user_id = #{userId,jdbcType=VARCHAR}
          and unionid = #{unionId,jdbcType=VARCHAR}
    </select>


    <select id="selectByUserIdsAndUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wechat_user_info
        where user_id in
         <foreach collection="userIds" item="item" open="(" separator="," close=")">
        #{item}
         </foreach>
        and unionid = #{unionId,jdbcType=VARCHAR}
    </select>
    <update id="updateStatusByUserIdAndUnionId">
        update wechat_user_info
        set status = #{status,jdbcType=INTEGER}
        where user_id = #{userId,jdbcType=VARCHAR}
          and unionid = #{unionId,jdbcType=VARCHAR}
    </update>

    <select id="selectByUserIdAndUnionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wechat_user_info
        where user_id = #{userId,jdbcType=VARCHAR} and unionid in
        <foreach collection="unionIds" item="unionId" open="(" separator="," close=")">
            #{unionId}
        </foreach>
    </select>


    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wechat_user_info
        where user_id = #{userId,jdbcType=VARCHAR} and status =1
    </select>
    <select id="selectActiveByUnionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wechat_user_info
        where unionid in
        <foreach collection="unionIds" item="unionId" open="(" separator="," close=")">
            #{unionId}
        </foreach>
        and status = 1
    </select>

    <insert id="insertBatch">
        insert into wechat_user_info (user_id, unionid, add_time, status, external_userid, admin_id)
        values
        <foreach collection="list" item="wechatUserInfo" index="index" separator=",">
            (#{wechatUserInfo.userId},
             #{wechatUserInfo.unionid},
             #{wechatUserInfo.addTime},
             #{wechatUserInfo.status},
             #{wechatUserInfo.externalUserid},
             #{wechatUserInfo.adminId})
        </foreach>
    </insert>

    <update id="updateStateById">
        update wechat_user_info set state=#{state} where (user_id, unionid) in
        <foreach collection="userInfo" item="userInfo" open="(" close=")" separator=",">
            (#{userInfo.userId},#{userInfo.unionId})
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2024-06-21-->
    <select id="selectByUnionid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wechat_user_info
        where unionid=#{unionid,jdbcType=VARCHAR}
    </select>
</mapper>