<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmBdAreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdArea">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, area_no, creator, create_time
  </sql>
    <select id="selectAdminByAreas" resultType="net.summerfarm.crm.model.vo.AdminInfoVo">
      select cba.admin_id as adminId ,a.realname as adminName
      from crm_bd_area cba
      INNER join admin a on  cba.admin_id = a.admin_id
      WHERE (a.is_disabled = 0 OR (a.is_disabled = 1 AND a.update_time > #{updateTime}))
      <if test="areaNo!=null and areaNo.size()>0">
        and cba.area_no in
          <foreach collection="areaNo" open="(" close=")" item="item" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="bdName != null">
        AND a.realname LIKE CONCAT(#{bdName},'%')
      </if>
      <if test="type != null and type == 1">
        AND cba.admin_id NOT IN (SELECT admin_id FROM crm_bd_team WHERE is_locked = 1 AND type = 2)
      </if>
      <if test="type != null and type == 2">
        AND cba.admin_id IN (SELECT admin_id FROM crm_bd_team WHERE is_locked = 1 AND type = 2)
      </if>
      GROUP BY cba.admin_id
    </select>
    <select id="selectByAdminId" resultType="java.lang.Integer">
      select
       distinct area_no
      from crm_bd_area
      where admin_id = #{adminId,jdbcType=INTEGER}

    </select>
  <delete id="deleteByAdminId">
    delete from crm_bd_area
    where admin_id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertBatch">
     insert into crm_bd_area (admin_id, area_no, creator,
                              create_time)
     values
     <foreach collection="areaCityIds" separator="," item="item">
        (#{adminId},#{item},#{creator},sysdate())
     </foreach>


  </insert>
  <insert id="copyAreaByAdminId">
    insert into crm_bd_area (admin_id, area_no, creator,
                             create_time)
    select #{intInfo}, area_no, #{creator},
           sysdate()
    from crm_bd_area where admin_id = #{copyIntInfo}
  </insert>


  <select id="selectBdArea" resultType="net.summerfarm.pojo.DO.Area">
    select
      ar.id, ar.area_no areaNo, ar.area_name areaName, ar.admin_id adminId, ar.large_area_no largeAreaNo, ar.delivery_fee deliveryFee, ar.status, ar.address,
      ar.info,ar.delivery_rule deliveryRule,ar.express_fee expressFee,ar.member_rule memberRule,ar.company_account_id, ar.parent_no parentNo,grade
    from crm_bd_area cba
    inner join area ar on ar.area_no = cba.area_no
    where cba.admin_id = #{adminId}
  </select>
</mapper>