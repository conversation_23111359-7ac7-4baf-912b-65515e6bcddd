<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantDataMapper">

    <!-- 查询门店最近登录时间 -->
    <select id="selectLastLoginTime" resultType="java.time.LocalDateTime">
        SELECT MAX(login_time) AS last_login_time 
        FROM merchant_sub_account 
        WHERE m_id = #{mId}
    </select>

    <!-- 查询门店最近10次历史拜访记录 -->
    <select id="selectRecentVisitRecords" resultType="net.summerfarm.crm.model.dto.BDTarget.MerchantDataDTO$VisitRecordDTO">
        SELECT 
            fur.admin_name AS adminName,
            fur.follow_up_way AS followUpWay,
            fur.`condition` AS `condition`,
            fur.visit_objective AS visitObjective,
            fur.add_time AS visitTime
        FROM follow_up_record fur 
        WHERE fur.m_id = #{mId}
        ORDER BY fur.add_time DESC 
        LIMIT 10
    </select>

    <!-- 查询门店最近10笔订单记录 -->
    <select id="selectRecentOrderRecords" resultType="net.summerfarm.crm.model.dto.BDTarget.MerchantDataDTO$OrderRecordDTO">
        SELECT 
            o.order_no AS orderNo,
            o.order_time AS orderTime,
            CASE o.type 
                WHEN 0 THEN '普通订单'
                WHEN 1 THEN '省心送'
                WHEN 2 THEN '运费'
                WHEN 3 THEN '代下单'
                WHEN 10 THEN '虚拟商品（黄金卡、充值...）'
                WHEN 11 THEN '直发采购'
                WHEN 30 THEN 'pop商城订单'
                ELSE CONCAT('未知类型:', o.type)
            END AS orderTypeDesc,
            CASE o.status 
                WHEN 1 THEN '待支付'
                WHEN 2 THEN '待配送'
                WHEN 3 THEN '待收货'
                WHEN 6 THEN '已收货'
                WHEN 7 THEN '申请退款订单'
                WHEN 8 THEN '已退款订单'
                WHEN 9 THEN '支付失败订单'
                WHEN 10 THEN '支付中断超时关闭订单'
                WHEN 11 THEN '已撤销订单（超时未支付自动关单、未支付并取消订单）'
                WHEN 14 THEN '手动关闭订单'
                WHEN 15 THEN '人工退款中'
                ELSE CONCAT('未知状态:', o.status)
            END AS orderStatusDesc,
            o.total_price AS totalPrice,
            oi.sku AS sku,
            oi.pd_name AS productName,
            oi.amount AS amount,
            oi.price AS price,
            oi.original_price AS originalPrice,
            CASE oi.status 
                WHEN 1 THEN '待支付'
                WHEN 2 THEN '待配送'
                WHEN 3 THEN '待收货'
                WHEN 6 THEN '已收货'
                WHEN 7 THEN '申请退款订单'
                WHEN 8 THEN '已退款订单'
                WHEN 9 THEN '支付失败订单'
                WHEN 10 THEN '支付中断超时关闭订单'
                WHEN 11 THEN '已撤销订单（超时未支付自动关单、未支付并取消订单）'
                WHEN 14 THEN '手动关闭订单'
                WHEN 15 THEN '人工退款中'
                ELSE CONCAT('未知状态:', oi.status)
            END AS orderItemStatusDesc
        FROM (
            SELECT order_no, order_time, type, status, total_price, order_id, m_id 
            FROM orders 
            WHERE m_id = #{mId}
            ORDER BY order_id DESC 
            LIMIT 10
        ) o 
        INNER JOIN order_item oi ON oi.order_no = o.order_no 
        INNER JOIN inventory sku ON sku.sku = oi.sku 
        INNER JOIN products spu ON spu.pd_id = sku.pd_id 
        WHERE o.m_id = #{mId}
        ORDER BY o.order_id DESC 
        LIMIT 50
    </select>

    <!-- 查询门店最近10笔订单的客诉内容 -->
    <select id="selectRecentComplaintRecords" resultType="net.summerfarm.crm.model.dto.BDTarget.MerchantDataDTO$ComplaintRecordDTO">
        SELECT 
            aso.order_no AS orderNo,
            aso.after_sale_order_no AS afterSaleOrderNo,
            aso.sku AS sku,
            CASE aso.status 
                WHEN 0 THEN '售后审核中'
                WHEN 1 THEN '售后处理中'
                WHEN 2 THEN '售后成功'
                WHEN 3 THEN '售后失败'
                WHEN 4 THEN '售后补充凭证'
                WHEN 11 THEN '售后取消'
                WHEN 12 THEN '售后退款中'
                ELSE '未知状态'
            END AS statusDesc,
            asp.handle_type AS handleType,
            CASE asp.handle_type 
                WHEN 0 THEN '返券'
                WHEN 1 THEN '未知'
                WHEN 2 THEN '退款'
                WHEN 3 THEN '录入账单'
                WHEN 4 THEN '退货退款'
                WHEN 5 THEN '退货录入账单'
                WHEN 6 THEN '换货'
                WHEN 7 THEN '补发'
                WHEN 8 THEN '人工退款'
                WHEN 9 THEN '拒收退款'
                WHEN 10 THEN '拒收账单'
                WHEN 11 THEN '拦截退款'
                WHEN 12 THEN '拦截录入账单'
                WHEN 13 THEN '退运费'
                WHEN 14 THEN '退运费录入账单'
                ELSE '未知处理方式'
            END AS handleTypeDesc,
            asp.refund_type AS refundType,
            aso.after_sale_remark AS afterSaleRemark,
            asp.after_sale_type AS afterSaleType,
            asp.handle_secondary_remark AS handleSecondaryRemark
        FROM after_sale_order aso 
        INNER JOIN after_sale_proof asp ON asp.after_sale_order_no = aso.after_sale_order_no 
        INNER JOIN (
            SELECT order_no, order_time, type, status, total_price, order_id 
            FROM orders 
            WHERE m_id = #{mId}
            ORDER BY order_id DESC 
            LIMIT 10
        ) o ON o.order_no = aso.order_no
    </select>

    <!-- 判断门店是否为新客户（历史未下过订单） -->
    <select id="isNewCustomer" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(*) = 0 THEN 1 ELSE 0 END AS isNewCustomer
        FROM orders 
        WHERE m_id = #{mId} 
        AND status IN (2, 3, 6)
        LIMIT 1
    </select>

    <!-- 查询门店常购商品列表（针对老客户推荐） -->
    <select id="selectFrequentProducts" resultType="net.summerfarm.crm.model.dto.RecommendContextDTO$ProductPurchaseDTO">
        SELECT
            oi.sku AS sku,
            oi.pd_name AS productName,
            oi.weight AS weight,
            COUNT(DISTINCT o.order_no) AS purchaseCount,
            MAX(o.order_time) AS lastPurchaseTime,
            SUM(oi.amount * oi.price) AS totalAmount
        FROM order_item oi
        INNER JOIN orders o ON o.order_no = oi.order_no
        inner join `merchant` m on m.`m_id`  = o.`m_id`
        inner join `contact` c on c.`m_id`  = m.`m_id` and c.`status` = 1 and c.`is_default` = 1
        inner join `inventory` i on i.sku = oi.sku and i.`outdated` = 0
        inner join `warehouse_inventory_mapping` wim on wim.`sku` = oi.`sku` and wim.`store_no`  = c.`store_no`
        inner join `area_store` astore on astore.`area_no`  = wim.`warehouse_no` and `astore`.`sku`  = wim.`sku`
            and (`astore`.`quantity` > 0  or `astore`.`online_quantity`  > 0)
        WHERE o.m_id = #{mId}
        AND o.status IN (2, 3, 6)
        GROUP BY oi.sku, oi.pd_name,  oi.`weight`
        ORDER BY purchaseCount DESC, lastPurchaseTime DESC
        LIMIT #{limit}
    </select>

    <!-- 查询限定范围内的商品列表 -->
    <select id="selectProductsInRange" resultType="net.summerfarm.crm.model.dto.RecommendContextDTO$ProductInRangeDTO">
        SELECT 
            oi.sku AS sku,
            oi.pd_name AS productName,
            oi.weight AS skuSpecification,
            COUNT(DISTINCT o.order_no) AS purchaseCount,
            MAX(o.order_time) AS lastPurchaseTime,
            SUM(oi.amount * oi.price) AS totalAmount
        FROM order_item oi 
        INNER JOIN orders o ON o.order_no = oi.order_no 
        INNER JOIN merchant m ON m.m_id = o.m_id 
        INNER JOIN contact c ON c.m_id = m.m_id AND c.status = 1 AND c.is_default = 1 
        INNER JOIN inventory i ON i.sku = oi.sku AND i.outdated = 0 
        <choose>
            <!-- 1-全品类：sub_type in (1, 2) -->
            <when test="rangeTypeCode == 1">
                AND i.sub_type in (1, 2)
            </when>
            <!-- 2-PB商品：sub_type = 3 -->
            <when test="rangeTypeCode == 2">
                AND i.sub_type = 3
            </when>
            <!-- 3-指定SKU -->
            <when test="rangeTypeCode == 3">
                AND i.sku = #{limitValue}
            </when>
            <!-- 4-指定SPU -->
            <when test="rangeTypeCode == 4">
                INNER JOIN products p ON p.pd_id = i.pd_id AND p.pd_no = #{limitValue}
            </when>
            <!-- 5-乳制品：category.type = 2 -->
            <when test="rangeTypeCode == 5">
                INNER JOIN products p ON p.pd_id = i.pd_id
                INNER JOIN category category ON category.id = p.category_id AND category.type = 2
            </when>
            <!-- 6-非乳制品：category.type = 3 -->
            <when test="rangeTypeCode == 6">
                INNER JOIN products p ON p.pd_id = i.pd_id
                INNER JOIN category category ON category.id = p.category_id AND category.type = 3
            </when>
            <!-- 7-水果：category.type = 4 -->
            <when test="rangeTypeCode == 7">
                INNER JOIN products p ON p.pd_id = i.pd_id
                INNER JOIN category category ON category.id = p.category_id AND category.type = 4
            </when>
        </choose>
        <!-- 对于非指定SPU的情况，需要关联products表 -->
        <if test="rangeTypeCode != 4 and rangeTypeCode > 4">
            <!-- products表已在上面的条件中关联 -->
        </if>
        <if test="rangeTypeCode == 1 or rangeTypeCode == 2 or rangeTypeCode == 3">
            INNER JOIN products p ON p.pd_id = i.pd_id
            INNER JOIN category category ON category.id = p.category_id
        </if>
        INNER JOIN warehouse_inventory_mapping wim ON wim.sku = oi.sku AND wim.store_no = c.store_no 
        INNER JOIN area_store astore ON astore.area_no = wim.warehouse_no AND astore.sku = wim.sku 
            AND (astore.quantity > 0 OR astore.online_quantity > 0) 
        WHERE 1=1 
        AND o.area_no = #{areaNo}
        AND o.order_time >= #{startDate}
        AND o.status = 6 
        GROUP BY oi.sku, oi.pd_name, oi.weight 
        ORDER BY purchaseCount DESC, lastPurchaseTime DESC 
        LIMIT #{limit}
    </select>

</mapper>