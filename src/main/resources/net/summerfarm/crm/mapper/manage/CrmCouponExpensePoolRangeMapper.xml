<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmCouponExpensePoolRangeMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCouponExpensePoolRange">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pool_id" jdbcType="BIGINT" property="poolId" />
    <result column="obj_key" jdbcType="VARCHAR" property="objKey" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, pool_id, obj_key
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_coupon_expense_pool_range
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_coupon_expense_pool_range
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByPoolId" parameterType="java.lang.Long">
    delete from crm_coupon_expense_pool_range
    where pool_id = #{poolId,jdbcType=BIGINT}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpensePoolRange" useGeneratedKeys="true">
    insert into crm_coupon_expense_pool_range (create_time, update_time, pool_id, 
      obj_key)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{poolId,jdbcType=BIGINT}, 
      #{objKey,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpensePoolRange" useGeneratedKeys="true">
    insert into crm_coupon_expense_pool_range
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="poolId != null">
        pool_id,
      </if>
      <if test="objKey != null">
        obj_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="poolId != null">
        #{poolId,jdbcType=BIGINT},
      </if>
      <if test="objKey != null">
        #{objKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpensePoolRange">
    update crm_coupon_expense_pool_range
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="poolId != null">
        pool_id = #{poolId,jdbcType=BIGINT},
      </if>
      <if test="objKey != null">
        obj_key = #{objKey,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmCouponExpensePoolRange">
    update crm_coupon_expense_pool_range
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pool_id = #{poolId,jdbcType=BIGINT},
      obj_key = #{objKey,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into crm_coupon_expense_pool_range (create_time, update_time, pool_id,obj_key)
    values
    <foreach collection="list" separator="," item="item">
      (sysdate(),sysdate(), #{item.poolId}, #{item.objKey})
    </foreach>
  </insert>

  <select id="selectByPoolId" parameterType="java.lang.Long" resultType="net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolExtDto" >
    select obj_key as objKey
    from crm_coupon_expense_pool_range
    where pool_id = #{poolId}
  </select>


  <select id="selectByPoolIdObjKey"  resultType="net.summerfarm.crm.model.dto.crmCouponExpense.CrmCouponExpensePoolExtDto" >
    select obj_key as objKey
    from crm_coupon_expense_pool_range
    where pool_id = #{poolId} and obj_key = #{objKey}
  </select>
</mapper>