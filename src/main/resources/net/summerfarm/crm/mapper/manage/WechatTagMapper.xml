<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.WechatTagMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.WechatTag">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
            <result property="tagId" column="tag_id" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="groupId" column="group_id" jdbcType="VARCHAR"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tag_name,tag_id,
        update_time,create_time,group_id,
        group_name
    </sql>

    <select id="listAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from wechat_tag
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.WechatTag" useGeneratedKeys="true">
        insert into wechat_tag
        ( id,tag_name,tag_id
        ,update_time,create_time,group_id
        ,group_name)
        values (#{id,jdbcType=BIGINT},#{tagName,jdbcType=VARCHAR},#{tagId,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{createTime,jdbcType=TIMESTAMP},#{groupId,jdbcType=VARCHAR}
        ,#{groupName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.WechatTag" useGeneratedKeys="true">
        insert into wechat_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="tagName != null">tag_name,</if>
                <if test="tagId != null">tag_id,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="createTime != null">create_time,</if>
                <if test="groupId != null">group_id,</if>
                <if test="groupName != null">group_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="tagName != null">#{tagName,jdbcType=VARCHAR},</if>
                <if test="tagId != null">#{tagId,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
                <if test="groupName != null">#{groupName,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <insert id="insertBatch">
        insert into wechat_tag
        (tag_name,tag_id,group_id,group_name)
        values
        <foreach collection="tags"  separator="," item="item">
            (#{item.tagName},#{item.tagId},#{item.groupId},#{item.groupName})
        </foreach>
    </insert>

    <select id="selectByGroupNameTags" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from wechat_tag
        where group_name=#{groupName} and
        tag_name in
        <foreach collection="tags" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectByGroupName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from wechat_tag
        where group_name=#{groupName}
    </select>
    <delete id="deleteIds">
        delete from wechat_tag where id in
        <foreach collection="ids" open="(" close=") " separator="," item="item">
            #{item}
        </foreach>
    </delete>
</mapper>
