<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.LargeAreaMapper">
    <resultMap id="LargeAreaResultMap" type="net.summerfarm.crm.model.dto.LargeAreaDTO">
        <id column="large_area_no" jdbcType="INTEGER" property="largeAreaNo"/>
        <result column="large_area_name" jdbcType="VARCHAR" property="largeAreaName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="manage_admin_id" jdbcType="VARCHAR" property="manageAdminId"/>
        <collection property="areaList" ofType="net.summerfarm.pojo.DO.Area" select="net.summerfarm.crm.mapper.manage.AreaMapper.selectByLargeArea"
                    column="{largeAreaNo=large_area_no}"/>
    </resultMap>

    <resultMap id="AreaResultMap" type="net.summerfarm.crm.model.vo.AreaVO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="admin_id" jdbcType="INTEGER" property="adminId"/>
        <result column="parent_no" jdbcType="INTEGER" property="parentNo"/>
        <result column="large_area_no" jdbcType="INTEGER" property="largeAreaNo"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="status" jdbcType="BIT" property="status"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="info" jdbcType="VARCHAR" property="info"/>
        <result column="delivery_rule" jdbcType="VARCHAR" property="deliveryRule" />
        <result column="map_section" jdbcType="VARCHAR" property="mapSection"/>
        <result column="free_day" jdbcType="VARCHAR" property="freeDay"/>
        <result column="origin_area_no" jdbcType="INTEGER" property="originAreaNo"/>
        <result column="next_delivery_date" jdbcType="TIMESTAMP" property="nextDeliveryDate"/>
        <result column="change_flag" jdbcType="BOOLEAN" property="changeFlag"/>
        <result column="change_store_no" jdbcType="INTEGER" property="changeStoreNo"/>
        <result column="change_status" jdbcType="INTEGER" property="changeStatus"/>
        <result column="express_fee" property="expressFee"/>
        <result column="member_rule" property="memberRule"/>
        <result column="realname" property="realname"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="company_account_id" property="companyAccountId"/>
        <result column="mail_to_address" property="mailToAddress"/>
        <result column="administrative_area" property="administrativeArea"/>
        <result column="support_add_order" property="supportAddOrder"/>
        <result column="update_support_add_order" property="updateSupportAddOrder"/>
    </resultMap>

    <select id="selectByName" resultType="net.summerfarm.crm.model.dto.LargeAreaDTO">
        select large_area_name largeAreaName, large_area_no largeAreaNo
        from large_area
        where large_area_name = #{areaName}
          and status = 1
    </select>
    <select id="selectAll" resultMap="LargeAreaResultMap">
        select
        large_area_no ,large_area_name ,status,manage_admin_id
        from large_area
        <where>
            <if test="status != null">
                status = #{status}
            </if>
        </where>
    </select>
</mapper>
