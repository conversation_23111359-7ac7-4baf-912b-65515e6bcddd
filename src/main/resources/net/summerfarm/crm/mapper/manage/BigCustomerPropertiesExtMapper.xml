<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BigCustomerPropertiesExtMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BigCustomerPropertiesExt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="prop_key" jdbcType="VARCHAR" property="propKey" />
    <result column="prop_value" jdbcType="VARCHAR" property="propValue" />
    <result column="big_customer_id" jdbcType="BIGINT" property="bigCustomerId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, prop_key, prop_value, big_customer_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from big_cusutomer_properties_ext
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from big_cusutomer_properties_ext
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.BigCustomerPropertiesExt"  useGeneratedKeys="true" >
    insert into big_cusutomer_properties_ext (create_time, update_time, prop_key,
      prop_value, big_customer_id)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{propKey,jdbcType=VARCHAR}, 
      #{propValue,jdbcType=VARCHAR}, #{bigCustomerId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.BigCustomerPropertiesExt" useGeneratedKeys="true" >
    insert into big_cusutomer_properties_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="propKey != null">
        prop_key,
      </if>
      <if test="propValue != null">
        prop_value,
      </if>
      <if test="bigCustomerId != null">
        big_customer_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="propKey != null">
        #{propKey,jdbcType=VARCHAR},
      </if>
      <if test="propValue != null">
        #{propValue,jdbcType=VARCHAR},
      </if>
      <if test="bigCustomerId != null">
        #{bigCustomerId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.BigCustomerPropertiesExt">
    update big_cusutomer_properties_ext
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="propKey != null">
        prop_key = #{propKey,jdbcType=VARCHAR},
      </if>
      <if test="propValue != null">
        prop_value = #{propValue,jdbcType=VARCHAR},
      </if>
      <if test="bigCustomerId != null">
        big_customer_id = #{bigCustomerId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.BigCustomerPropertiesExt">
    update big_cusutomer_properties_ext
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      prop_key = #{propKey,jdbcType=VARCHAR},
      prop_value = #{propValue,jdbcType=VARCHAR},
      big_customer_id = #{bigCustomerId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByBigCustomerIdProKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from big_cusutomer_properties_ext
    where big_customer_id = #{bigCustomerId} and  prop_key = #{propKey}
  </select>

  <select id="listByBigCustomerIdsAndProKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from big_cusutomer_properties_ext
    where big_customer_id in
    <foreach open="(" close=")" separator="," collection="bigCustomerIds" item="item" index="index">
      #{item}
    </foreach>
    and prop_key = #{propKey}
  </select>

</mapper>