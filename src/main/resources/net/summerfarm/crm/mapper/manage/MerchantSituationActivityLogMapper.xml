<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantSituationActivityLogMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.MerchantSituationActivityLog">
        <!--@mbg.generated-->
        <!--@Table merchant_situation_activity_log-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="merchant_situation_id" jdbcType="BIGINT" property="merchantSituationId"/>
        <result column="pd_id" jdbcType="BIGINT" property="pdId"/>
        <result column="active_length" jdbcType="INTEGER" property="activeLength"/>
        <result column="active_end_date" jdbcType="DATE" property="activeEndDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, create_time, update_time, merchant_situation_id, pd_id, active_length, active_end_date
    </sql>

    <select id="selectByMIdAndActiveEndDateAfter" resultType="net.summerfarm.crm.model.dto.MscActiveSpuDTO">
        select
        msal.pd_id AS pdId,
        p.pd_name AS pdName,
        ms.examine_time AS examineTime,
        max(msal.active_end_date) AS activeEndDate -- 保证每个pdId查找到的是最新的
        from merchant_situation_activity_log msal
        left join merchant_situation ms on msal.merchant_situation_id = ms.id
        left join products p on p.pd_id = msal.pd_id
        where ms.merchant_id=#{mId,jdbcType=BIGINT}
        and ms.status = 2
        and ms.situation_type = 3
        and msal.active_end_date  <![CDATA[>=]]> #{minActiveEndTime,jdbcType=DATE}
        group by msal.pd_id -- 保证每个pdId查找到的是最新的
    </select>
</mapper>