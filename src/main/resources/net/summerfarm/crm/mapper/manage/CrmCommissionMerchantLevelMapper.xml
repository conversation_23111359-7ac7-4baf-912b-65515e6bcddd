<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmCommissionMerchantLevelMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCommissionMerchantLevel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="grade" jdbcType="CHAR" property="grade" />
    <result column="gmv_minimum" jdbcType="INTEGER" property="gmvMinimum" />
    <result column="gmv_maximum" jdbcType="INTEGER" property="gmvMaximum" />
    <result column="price_minimum" jdbcType="INTEGER" property="priceMinimum" />
    <result column="price_maximum" jdbcType="INTEGER" property="priceMaximum" />
    <result column="gmv_proportion" jdbcType="DECIMAL" property="gmvProportion" />
    <result column="price_proportion" jdbcType="DECIMAL" property="priceProportion" />
    <result column="merchant_level_type" jdbcType="TINYINT" property="merchantLevelType" />
    <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, grade, gmv_minimum, gmv_maximum, price_minimum, price_maximum, gmv_proportion, 
    price_proportion, merchant_level_type, delete_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_commission_merchant_level
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAllCoreMerchantLevel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_commission_merchant_level
    where merchant_level_type = #{merchantLevelType}  AND delete_flag = 0
    <if test="grade != null">
      and grade = #{grade}
    </if>
    order by gmv_minimum desc
  </select>
  <select id="selectCoreMerchantByAreaNo" resultMap="BaseResultMap">
    SELECT ccml.id, ccml.grade, ccml.gmv_minimum, ccml.gmv_maximum, ccml.price_minimum, ccml.price_maximum, ccml.gmv_proportion,
    ccml.price_proportion, ccml.merchant_level_type, ccml.delete_flag, ccml.update_time, ccml.create_time
    FROM area a
    LEFT JOIN crm_commission_merchant_level ccml ON a.grade = ccml.grade
    WHERE a.area_no = #{areaNo} and ccml.merchant_level_type = 1
  </select>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCommissionMerchantLevel" useGeneratedKeys="true">
    insert into crm_commission_merchant_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="grade != null">
        grade,
      </if>
      <if test="gmvMinimum != null">
        gmv_minimum,
      </if>
      <if test="gmvMaximum != null">
        gmv_maximum,
      </if>
      <if test="priceMinimum != null">
        price_minimum,
      </if>
      <if test="priceMaximum != null">
        price_maximum,
      </if>
      <if test="gmvProportion != null">
        gmv_proportion,
      </if>
      <if test="priceProportion != null">
        price_proportion,
      </if>
      <if test="merchantLevelType != null">
        merchant_level_type,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="grade != null">
        #{grade,jdbcType=CHAR},
      </if>
      <if test="gmvMinimum != null">
        #{gmvMinimum,jdbcType=INTEGER},
      </if>
      <if test="gmvMaximum != null">
        #{gmvMaximum,jdbcType=INTEGER},
      </if>
      <if test="priceMinimum != null">
        #{priceMinimum,jdbcType=INTEGER},
      </if>
      <if test="priceMaximum != null">
        #{priceMaximum,jdbcType=INTEGER},
      </if>
      <if test="gmvProportion != null">
        #{gmvProportion,jdbcType=DECIMAL},
      </if>
      <if test="priceProportion != null">
        #{priceProportion,jdbcType=DECIMAL},
      </if>
      <if test="merchantLevelType != null">
        #{merchantLevelType,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName},
      </if>
      <if test="updateName != null">
        #{updateName},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmCommissionMerchantLevel">
    update crm_commission_merchant_level
    <set>
      <if test="grade != null">
        grade = #{grade,jdbcType=CHAR},
      </if>
      <if test="gmvMinimum != null">
        gmv_minimum = #{gmvMinimum,jdbcType=INTEGER},
      </if>
      <if test="gmvMaximum != null">
        gmv_maximum = #{gmvMaximum,jdbcType=INTEGER},
      </if>
      <if test="priceMinimum != null">
        price_minimum = #{priceMinimum,jdbcType=INTEGER},
      </if>
      <if test="priceMaximum != null">
        price_maximum = #{priceMaximum,jdbcType=INTEGER},
      </if>
      <if test="gmvProportion != null">
        gmv_proportion = #{gmvProportion,jdbcType=DECIMAL},
      </if>
      <if test="priceProportion != null">
        price_proportion = #{priceProportion,jdbcType=DECIMAL},
      </if>
      <if test="merchantLevelType != null">
        merchant_level_type = #{merchantLevelType,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertOrUpdateById" parameterType="net.summerfarm.crm.model.domain.CrmCommissionMerchantLevel">
    insert into crm_commission_merchant_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="gmvMinimum != null">
        gmv_minimum,
      </if>
      <if test="gmvMaximum != null">
        gmv_maximum,
      </if>
      <if test="priceMinimum != null">
        price_minimum,
      </if>
      <if test="priceMaximum != null">
        price_maximum,
      </if>
      <if test="gmvProportion != null">
        gmv_proportion,
      </if>
      <if test="priceProportion != null">
        price_proportion,
      </if>
      <if test="merchantLevelType != null">
        merchant_level_type,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=CHAR},
      </if>
      <if test="gmvMinimum != null">
        #{gmvMinimum,jdbcType=INTEGER},
      </if>
      <if test="gmvMaximum != null">
        #{gmvMaximum,jdbcType=INTEGER},
      </if>
      <if test="priceMinimum != null">
        #{priceMinimum,jdbcType=INTEGER},
      </if>
      <if test="priceMaximum != null">
        #{priceMaximum,jdbcType=INTEGER},
      </if>
      <if test="gmvProportion != null">
        #{gmvProportion,jdbcType=DECIMAL},
      </if>
      <if test="priceProportion != null">
        #{priceProportion,jdbcType=DECIMAL},
      </if>
      <if test="merchantLevelType != null">
        #{merchantLevelType,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName},
      </if>
      <if test="updateName != null">
        #{updateName},
      </if>
    </trim>
    ON DUPLICATE KEY
    UPDATE
    <trim suffixOverrides=",">
      <if test="grade != null">
        grade = #{grade,jdbcType=CHAR},
      </if>
      <if test="gmvMinimum != null">
        gmv_minimum = #{gmvMinimum,jdbcType=INTEGER},
      </if>
      <if test="gmvMaximum != null">
        gmv_maximum = #{gmvMaximum,jdbcType=INTEGER},
      </if>
      <if test="priceMinimum != null">
        price_minimum = #{priceMinimum,jdbcType=INTEGER},
      </if>
      <if test="priceMaximum != null">
        price_maximum = #{priceMaximum,jdbcType=INTEGER},
      </if>
      <if test="gmvProportion != null">
        gmv_proportion = #{gmvProportion,jdbcType=DECIMAL},
      </if>
      <if test="priceProportion != null">
        price_proportion = #{priceProportion,jdbcType=DECIMAL},
      </if>
      <if test="merchantLevelType != null">
        merchant_level_type = #{merchantLevelType,jdbcType=TINYINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=TINYINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName},
      </if>
    </trim>
  </insert>
</mapper>