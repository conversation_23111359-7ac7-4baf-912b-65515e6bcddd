<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.WeComUserContactWayMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.WeComUserContactWay">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="adminId" column="admin_id" jdbcType="BIGINT"/>
        <result property="configId" column="config_id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="qrCode" column="qr_code" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        admin_id,config_id,remark,
        state,user_id,qr_code
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.WeComUserContactWay" useGeneratedKeys="true">
        INSERT INTO wecom_user_contact_way (admin_id, config_id, remark,
                                            state, user_id, qr_code)
        VALUES (#{adminId,jdbcType=BIGINT}, #{configId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{state,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{qrCode,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteByConfigId">
        DELETE
        FROM wecom_user_contact_way
        WHERE config_id = #{configId}
    </delete>

    <select id="selectByAdminIdAndState" resultMap="BaseResultMap">
        SELECT *
        FROM wecom_user_contact_way
        WHERE admin_id = #{adminId}
          AND state = #{state}
    </select>

</mapper>
