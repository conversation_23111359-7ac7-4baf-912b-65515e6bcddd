<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.SampleApplyMapper">

    <resultMap id="baseResultMap" type="net.summerfarm.crm.model.domain.SampleApply">
        <id column="sample_id" property="sampleId"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        <result column="m_id" property="mId"/>
        <result column="m_name" property="mName"/>
        <result column="grade" property="grade"/>
        <result column="m_size" property="mSize"/>
        <result column="m_phone" property ="mPhone"/>
        <result column="m_contact" property="mContact"/>
        <result column="area_no" property="areaNo"/>
        <result column="contact_id" property="contactId"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="status" property="status"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="purchase_intention" property="purchaseIntention"/>
        <result column="remark" property="remark"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="store_no" property="storeNo"/>
        <result column="risk_level" property="riskLevel"/>

    </resultMap>
    <resultMap id="VOMap" type="net.summerfarm.crm.model.vo.SampleApplyVO">
        <id column="sample_id" property="sampleId"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="create_id" property="createId"/>
        <result column="create_name" property="createName"/>
        <result column="m_id" property="mId"/>
        <result column="m_name" property="mName"/>
        <result column="grade" property="grade"/>
        <result column="m_size" property="mSize"/>
        <result column="m_phone" property ="mPhone"/>
        <result column="m_contact" property="mContact"/>
        <result column="area_no" property="areaNo"/>
        <result column="contact_id" property="contactId"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="status" property="status"/>
        <result column="satisfaction" property="satisfaction"/>
        <result column="purchase_intention" property="purchaseIntention"/>
        <result column="remark" property="remark"/>
        <result column="area_name" property="areaName"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="address" property="address"/>
        <result column="house_number" property="houseNumber"/>
        <result column="store_no" property="storeNo"/>
        <result column="risk_level" property="riskLevel"/>
    </resultMap>

    <insert id="insertSampleApply" keyColumn="sample_id" keyProperty="sampleId" useGeneratedKeys="true" parameterType="net.summerfarm.crm.model.domain.SampleApply">
        insert into sample_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createId != null">
                create_id,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="mId != null">
                m_id,
            </if>
            <if test="mName != null">
                m_name,
            </if>
            <if test="grade != null">
                grade,
            </if>
            <if test="mSize != null">
                m_size,
            </if>
            <if test="mPhone != null">
                m_phone,
            </if>
            <if test="mContact != null">
                m_contact,
            </if>
            <if test="contactId != null">
                contact_id,
            </if>
            <if test="areaNo!= null">
                area_no,
            </if>
            <if test="bdId != null">
                bd_id,
            </if>
            <if test="bdName != null">
                bd_name,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="satisfaction != null">
                satisfaction,
            </if>
            <if test="purchaseIntention != null">
                purchase_intention,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="deliveryTime != null">
                delivery_time,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="riskLevel != null">
                risk_level,
            </if>
            <if test="sellingEntityName != null">
                selling_entity_name,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createId != null">
                #{createId},
            </if>
            <if test="createName != null">
                #{createName},
            </if>
            <if test="mId != null">
                #{mId},
            </if>
            <if test="mName != null">
                #{mName},
            </if>
            <if test="grade != null">
                #{grade},
            </if>
            <if test="mSize != null">
                #{mSize},
            </if>
            <if test="mPhone != null">
                #{mPhone},
            </if>
            <if test="mContact != null">
                #{mContact},
            </if>

            <if test="contactId != null">
                #{contactId},
            </if>
            <if test="areaNo!= null">
                #{areaNo},
            </if>
            <if test="bdId != null">
                #{bdId},
            </if>
            <if test="bdName != null">
                #{bdName},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="satisfaction != null">
                #{satisfaction},
            </if>
            <if test="purchaseIntention != null">
                #{purchaseIntention},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="deliveryTime != null">
                #{deliveryTime},
            </if>
            <if test="storeNo != null">
                #{storeNo},
            </if>
            <if test="riskLevel != null">
                #{riskLevel},
            </if>
            <if test="sellingEntityName != null">
                #{sellingEntityName},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" keyColumn="sample_id" keyProperty="sampleId" useGeneratedKeys="true">
        INSERT INTO sample_apply (
        add_time,
        update_time,
        create_id,
        create_name,
        m_id,
        m_name,
        grade,
        m_size,
        m_phone,
        m_contact,
        contact_id,
        area_no,
        bd_id,
        bd_name,
        status,
        satisfaction,
        purchase_intention,
        remark,
        delivery_time,
        store_no,
        risk_level,
        selling_entity_name
        ) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (
            #{item.addTime},
            #{item.updateTime},
            #{item.createId},
            #{item.createName},
            #{item.mId},
            #{item.mName},
            #{item.grade},
            #{item.mSize},
            #{item.mPhone},
            #{item.mContact},
            #{item.contactId},
            #{item.areaNo},
            #{item.bdId},
            #{item.bdName},
            #{item.status},
            #{item.satisfaction},
            #{item.purchaseIntention},
            #{item.remark},
            #{item.deliveryTime},
            #{item.storeNo},
            #{item.riskLevel},
            #{item.sellingEntityName}
            )
        </foreach>
    </insert>

    <select id="selectSampleApplies"  resultMap="VOMap">
        select sa.area_no,sa.sample_id, sa.add_time, sa.update_time, sa.delivery_time, sa.create_id, sa.create_name, sa.m_id,
        sa.m_name, sa.grade, sa.m_size, sa.m_phone, sa.m_contact,
        sa.contact_id, sa.bd_id, sa.bd_name, sa.status, sa.area_no areaNo,  a.area_name
        ,c.province, c.city, c.area, c.address,c.house_number,sa.satisfaction,sa.purchase_intention,sa.remark,
        sa.risk_level
        from sample_apply sa
        left join area a on a.area_no = sa.area_no
        left join contact c on c.contact_id = sa.contact_id
        <where>
            <if test="sa.status !=null">
                and sa.status = #{sa.status}
            </if>
            <if test="sa.areaNo != null">
                and sa.area_no = #{sa.areaNo}
            </if>
            <if test="sa.mName != null">
                and sa.m_name like  CONCAT('%',#{sa.mName} ,'%')
            </if>
            <if test="keyword != null and keyword != ''">
                and (sa.m_name like  CONCAT('%',#{keyword} ,'%') OR sa.create_name like  CONCAT('%',#{keyword} ,'%') )
            </if>
            <if test="sa.addTime != null">
                and sa.add_time = #{sa.addTime}
            </if>
            <if test="sa.bdId != null">
                and sa.bd_id = #{sa.bdId}
            </if>
            <if test="sa.riskLevel != null">
                and sa.risk_level = #{sa.riskLevel}
            </if>
        </where>
        order by sa.add_time DESC ,sa.status
    </select>
    <update id="updateSampleApply" >
        update sample_apply
        <set>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="satisfaction != null">
                satisfaction = #{satisfaction},
            </if>
            <if test="purchaseIntention != null">
                purchase_intention = #{purchaseIntention},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deliveryTime != null">
                delivery_time = #{deliveryTime},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo},
            </if>
        </set>
        where sample_id =#{sampleId}
    </update>

    <select id="selectSampleById" resultMap="baseResultMap">
         /*FORCE_MASTER*/
         select * from sample_apply
         where sample_id =#{sampleId}
    </select>

    <update id="cancelSampleApply" parameterType="integer">
        UPDATE sample_apply
        SET status = 2,
            update_time = now()
        WHERE sample_id = #{sampleId}
    </update>

    <select id="querySituationListTime" resultType="Integer">
        select sample_id
        from sample_apply
        where update_time <![CDATA[<=]]> #{endTime} and status = 3
    </select>

    <update id="closeSampleApply">
        UPDATE sample_apply
        SET status = 4,
        update_time = now()
        WHERE sample_id = #{sampleId}
    </update>
    <update id="batchCancelSampleApply">
        UPDATE sample_apply
        SET status = 2, update_time = now()
        WHERE sample_id in
        <foreach item="item" index="index" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectByAddTime" resultMap="VOMap">
        select sa.area_no,sa.sample_id, sa.add_time, sa.update_time, sa.delivery_time, sa.create_id, sa.create_name, sa.m_id,
        sa.m_name, sa.grade, sa.m_size, sa.m_phone, sa.m_contact,
        sa.contact_id, sa.bd_id, sa.bd_name, sa.status, sa.area_no,  a.area_name,ss.id, ss.sample_id, ss.sku, ss.pd_name, ss.amount, ss.weight
        from sample_apply sa
        left join area a on a.area_no = sa.area_no
        left join sample_sku ss on ss.sample_id = sa.sample_id
        where sa.add_time <![CDATA[>=]]> #{addTime} and sa.add_time <![CDATA[<=]]> #{endTime} and sa.status = 0
    </select>

    <select id="selectOverCount" resultType="Integer">
        select count(1)
        from sample_apply
        where m_id = #{mId} and status in (0,1)
    </select>


    <select id="selectSampleApplySkuList" resultType="String">
       select distinct(sku)
        from sample_apply sa
        join sample_sku sk
        on sa.`sample_id`  = sk.sample_id
        where  sa.m_id = #{mId}
        and sa.status in (0,1)
    </select>

    <select id="getSampleInfoBySku" resultType="Long">
        select sa.m_id mId
        from sample_apply sa
        join sample_sku sk
        on sa.`sample_id` = sk.sample_id
        where sa.status in (0,1,3)
        and sk.sku = #{sku}
        and sa.m_id in
        <foreach collection="mIds" item="mId" open="(" separator="," close=")">
            #{mId}
        </foreach>
    </select>
    <select id="selectSampleInfoBySku" resultType="net.summerfarm.crm.model.domain.SampleApply">
        select sa.sample_id sampleId
        from sample_apply sa
        join sample_sku sk
        on sa.`sample_id` = sk.sample_id
        where sk.sku = #{sku}
        and sa.status = #{status}
        and sa.m_id in
        <foreach collection="mIds" item="mId" open="(" separator="," close=")">
            #{mId}
        </foreach>
    </select>

</mapper>