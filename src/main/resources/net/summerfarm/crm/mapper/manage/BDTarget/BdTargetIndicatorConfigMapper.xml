<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDTarget.BdTargetIndicatorConfigMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="target_type" jdbcType="INTEGER" property="targetType" />
    <result column="target_name" jdbcType="VARCHAR" property="targetName" />
    <result column="business_type_config" jdbcType="VARCHAR" property="businessTypeConfig" />
    <result column="indicator_type_config" jdbcType="VARCHAR" property="indicatorTypeConfig" />
    <result column="category_name_config" jdbcType="VARCHAR" property="categoryNameConfig" />
    <result column="sku_config" jdbcType="VARCHAR" property="skuConfig" />
    <result column="spu_config" jdbcType="VARCHAR" property="spuConfig" />
    <result column="unit_config" jdbcType="VARCHAR" property="unitConfig" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, target_type, target_name, business_type_config, indicator_type_config, category_name_config, sku_config, spu_config, unit_config
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_target_indicator_config
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_target_indicator_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_target_indicator_config (target_type, target_name, business_type_config, indicator_type_config, category_name_config, sku_config, spu_config, unit_config)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.targetType,jdbcType=INTEGER}, #{item.targetName,jdbcType=VARCHAR}, #{item.businessTypeConfig,jdbcType=VARCHAR},
      #{item.indicatorTypeConfig,jdbcType=VARCHAR}, #{item.categoryNameConfig,jdbcType=VARCHAR}, #{item.skuConfig,jdbcType=VARCHAR}, #{item.spuConfig,jdbcType=VARCHAR}, #{item.unitConfig,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfig" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_target_indicator_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetName != null">
        target_name,
      </if>
      <if test="businessTypeConfig != null">
        business_type_config,
      </if>
      <if test="indicatorTypeConfig != null">
        indicator_type_config,
      </if>
      <if test="categoryNameConfig != null">
        category_name_config,
      </if>
      <if test="skuConfig != null">
        sku_config,
      </if>
      <if test="spuConfig != null">
        spu_config,
      </if>
      <if test="unitConfig != null">
        unit_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="targetType != null">
        #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetName != null">
        #{targetName,jdbcType=VARCHAR},
      </if>
      <if test="businessTypeConfig != null">
        #{businessTypeConfig,jdbcType=VARCHAR},
      </if>
      <if test="indicatorTypeConfig != null">
        #{indicatorTypeConfig,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameConfig != null">
        #{categoryNameConfig,jdbcType=VARCHAR},
      </if>
      <if test="skuConfig != null">
        #{skuConfig,jdbcType=VARCHAR},
      </if>
      <if test="spuConfig != null">
        #{spuConfig,jdbcType=VARCHAR},
      </if>
      <if test="unitConfig != null">
        #{unitConfig,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfigUpdate">
    update crm_bd_target_indicator_config
    <set>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetName != null">
        target_name = #{targetName,jdbcType=VARCHAR},
      </if>
      <if test="businessTypeConfig != null">
        business_type_config = #{businessTypeConfig,jdbcType=VARCHAR},
      </if>
      <if test="indicatorTypeConfig != null">
        indicator_type_config = #{indicatorTypeConfig,jdbcType=VARCHAR},
      </if>
      <if test="categoryNameConfig != null">
        category_name_config = #{categoryNameConfig,jdbcType=VARCHAR},
      </if>
      <if test="skuConfig != null">
        sku_config = #{skuConfig,jdbcType=VARCHAR},
      </if>
      <if test="spuConfig != null">
        spu_config = #{spuConfig,jdbcType=VARCHAR},
      </if>
      <if test="unitConfig != null">
        unit_config = #{unitConfig,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="list" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdTargetIndicatorConfigQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_target_indicator_config
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="targetType != null">
        AND target_type = #{targetType,jdbcType=INTEGER}
      </if>
      <if test="targetName != null and targetName != ''">
        AND target_name = #{targetName,jdbcType=VARCHAR}
      </if>
      <if test="businessTypeConfig != null and businessTypeConfig != ''">
        AND business_type_config = #{businessTypeConfig,jdbcType=VARCHAR}
      </if>
      <if test="indicatorTypeConfig != null and indicatorTypeConfig != ''">
        AND indicator_type_config = #{indicatorTypeConfig,jdbcType=VARCHAR}
      </if>
      <if test="categoryNameConfig != null and categoryNameConfig != ''">
        AND category_name_config = #{categoryNameConfig,jdbcType=VARCHAR}
      </if>
      <if test="skuConfig != null and skuConfig != ''">
        AND sku_config = #{skuConfig,jdbcType=VARCHAR}
      </if>
      <if test="spuConfig != null and spuConfig != ''">
        AND spu_config = #{spuConfig,jdbcType=VARCHAR}
      </if>
      <if test="unitConfig != null and unitConfig != ''">
        AND unit_config = #{unitConfig,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>

  <select id="listAllConfigs" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_target_indicator_config
  </select>

</mapper>