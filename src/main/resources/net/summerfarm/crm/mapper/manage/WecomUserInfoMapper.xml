<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.WecomUserInfoMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.WecomUserInfo">
        <!--@mbg.generated-->
        <!--@Table wecom_user_info-->
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="admin_id" property="adminId"/>
        <result column="admin_name" property="adminName"/>
        <result column="phone" property="phone"/>
        <result column="status" property="status"/>
        <result column="email" property="email"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        create_time,
        update_time,
        user_id,
        admin_id,
        admin_name,
        phone,
        `status`,
        email
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from wecom_user_info
        where id = #{id}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.WecomUserInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into wecom_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="adminName != null">
                admin_name,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="department != null and email != ''">
                department,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="adminName != null and adminName != ''">
                #{adminName},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="email != null and email != ''">
                #{email},
            </if>
            <if test="department != null and department != ''">
                #{department},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.WecomUserInfo">
        <!--@mbg.generated-->
        update wecom_user_info
        <set>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="adminId != null">
                admin_id = #{adminId},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectList" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from wecom_user_info
    </select>

    <select id="summaryActivate" resultType="net.summerfarm.crm.model.vo.weCom.WeComActivateVo">
        select sum(if(a.is_disabled =0,1,0))
        from admin a
                 join wecom_user_info wui on a.admin_id = wui.admin_id
        where wui.status = 1
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wecom_user_info
        where user_id = #{userid}
    </select>
    <select id="selectByAdminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wecom_user_info
        where admin_id = #{adminId}
    </select>
    <select id="selectActiveByUpdateTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wecom_user_info
        where update_time between #{startTime} and #{endTime}
        and status = 1
    </select>
</mapper>