<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantSituationMapper">
    <resultMap id="baseResultMap" type="net.summerfarm.crm.model.vo.MerchantSituationVO">
        <id column="id" property="id"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="status" property="status"/>
        <result column="create_location" property="createLocation"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="creator_id" property="creatorId"/>
        <result column="examine_id" property="examineId"/>
        <result column="examine_time" property="examineTime"/>
        <result column="approval_id" property="approvalId"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="approval_remark" property="approvalRemark"/>
        <result column="examine_remark" property="examineRemark"/>
        <result column="examine_name" property="examineName"/>
        <result column="approval_name" property="approvalName"/>
        <result column="coupon_id" property="couponId"/>
        <result column="bd_name" property="creatorName"/>
        <result column="money" property="couponAmount"/>
        <result column="situation_type" property="situationType"/>
        <result column="pool_id" property="poolId"/>
        <result column="base_price" property="basePrice"/>
        <result column="amount" property="amount"/>
    </resultMap>

    <sql id="baseSql">
        id,
        gmt_create,
        gmt_modified  ,
        status,
        create_location ,
        merchant_id ,
        creator_id ,
        examine_id ,
        examine_time ,
        approval_id  ,
        approval_time ,
        approval_remark ,
        examine_remark ,
        examine_name ,
        approval_name,
        coupon_id,
        creator_name,
        admin_id,
        admin_name,
        situation_remake,
        situation_type,
        pool_id,
        base_price,
        amount

    </sql>

    <select id="querySituation" resultMap="baseResultMap">
        select
        <include refid="baseSql"/>
        from merchant_situation
        where id = #{msId}
    </select>

    <select id="querySituationList" resultType="net.summerfarm.crm.model.vo.MerchantSituationVO">
        select
            ms.id,ms.gmt_create gmtCreate,ms.status,ms.merchant_id merchantId,ms.coupon_id couponId,ms.admin_id adminId,ms.admin_name adminName,
            ms.creator_id creatorId,ms.situation_remake situationRemake,ms.creator_name creatorName,
            ms.examine_id examineId,ms.examine_time examineTime,ms.examine_remark examineRemark,ms.examine_name examineName,
            ms.situation_type situationType, ms.order_quantity orderQuantity,
            c.money couponAmount,c.threshold threshold,c.reamrk couponRemake,c.name couponName,c.sku,c.activity_scope activityScope,
            m.mname mname,m.grade,m.size,m.area_no areaNo,
            a.area_name areaName,ms.attached_image as attachedImage
        from merchant_situation ms
        left JOIN coupon c on c.id = ms.coupon_id
        LEFT JOIN merchant m on m.m_id = ms.merchant_id
        LEFT JOIN area a on a.area_no = m.area_no
        <where>
            <if test = "id != null">
                AND ms.id = #{id}
            </if>
            <if test="midList != null and midList.size() >0 ">
                and ms.merchant_id in
                <foreach collection="midList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="status != null">
                AND ms.status = #{status}
            </if>
            <if test="creatorName != null">
                AND ms.creator_name LIKE CONCAT(#{creatorName,jdbcType=VARCHAR},'%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (ms.creator_name LIKE CONCAT(#{keyword,jdbcType=VARCHAR},'%') OR m.mname LIKE CONCAT(#{keyword,jdbcType=VARCHAR},'%') )
            </if>
            <if test="creatorId != null">
                AND ms.creator_id = #{creatorId}
            </if>
        </where>
        order by ms.id DESC
    </select>

    <insert id="insertMerchantSituation" parameterType="net.summerfarm.crm.model.dto.MerchantSituationDTO" useGeneratedKeys="true" keyProperty="id">
        insert into merchant_situation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                gmt_create,
            </if>
            <if test="updateTime != null">
                gmt_modified,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="merchantId != null">
                merchant_id,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="examineId != null">
                examine_id,
            </if>
            <if test="examineTime != null">
                examine_time,
            </if>
            <if test="examineName != null">
                examine_name,
            </if>
            <if test="couponId != null">
                coupon_id,
            </if>
            <if test="creatorName != null">
                creator_name,
            </if>
            <if test="adminId != null">
                admin_id,
            </if>
            <if test="adminName != null">
                admin_name,
            </if>
            <if test="situationRemake != null">
                situation_remake,
            </if>
            <if test="situationType != null">
                situation_type,
            </if>
            <if test="orderQuantity != null">
                order_quantity,
            </if>
            <if test="attachedImage != null">
                attached_image,
            </if>
            <if test="poolId != null">
                pool_id,
            </if>
            <if test="basePrice != null">
                base_price,
            </if>
            <if test="amount != null">
                amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="merchantId != null">
                #{merchantId},
            </if>
            <if test="creatorId != null">
                #{creatorId},
            </if>
            <if test="examineId != null">
                #{examineId},
            </if>
            <if test="examineTime != null">
                #{examineTime},
            </if>
            <if test="examineName != null">
                #{examineName},
            </if>
            <if test="couponId != null">
                #{couponId},
            </if>
            <if test="creatorName != null">
                #{creatorName},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="adminName != null">
                #{adminName},
            </if>
            <if test="situationRemake != null">
                #{situationRemake},
            </if>
            <if test="situationType != null">
                #{situationType},
            </if>
            <if test="orderQuantity != null">
                #{orderQuantity},
            </if>
            <if test="attachedImage != null">
                #{attachedImage},
            </if>
            <if test="poolId != null">
                #{poolId},
            </if>
            <if test="basePrice != null">
                #{basePrice},
            </if>
            <if test="amount != null">
                #{amount},
            </if>
        </trim>
    </insert>

    <update id="updateSituation" parameterType="net.summerfarm.crm.model.domain.MerchantSituation">
        update merchant_situation
        <set>
            gmt_modified = now()
            <if test="status != null">
                ,status = #{status}
            </if>
            <if test="examineId != null">
               , examine_id = #{examineId}
            </if>
            <if test="examineTime != null">
               , examine_time = #{examineTime}
            </if>
            <if test="approvalId != null">
               , approval_id = #{approvalId}
            </if>
            <if test="approvalTime != null">
               , approval_time = #{approvalTime}
            </if>
            <if test="approvalRemark != null">
               , approval_remark =#{approvalRemark}
            </if>
            <if test="examineRemark != null">
               , examine_remark = #{examineRemark}
            </if>
            <if test="examineName != null">
               , examine_name = #{examineName}
            </if>
            <if test="approvalName != null">
               , approval_name = #{approvalName}
            </if>
            <if test="merchantCouponId != null">
                , merchant_coupon_id = #{merchantCouponId}
            </if>
          </set>
        where id = #{id}
    </update>


    <update id="updateSituationALl" parameterType="net.summerfarm.crm.model.domain.MerchantSituation">
        update merchant_situation
        <set>
            gmt_modified =now(),status = 3
            <if test="examineId != null">
                , examine_id = #{examineId}
            </if>
            <if test="examineTime != null">
                , examine_time = #{examineTime}
            </if>
            <if test="approvalId != null">
                , approval_id = #{approvalId}
            </if>
            <if test="approvalTime != null">
                , approval_time = #{approvalTime}
            </if>
            <if test="approvalRemark != null">
                , approval_remark =#{approvalRemark}
            </if>
            <if test="examineRemark != null">
                , examine_remark = #{examineRemark}
            </if>
            <if test="examineName != null">
                , examine_name = #{examineName}
            </if>
            <if test="approvalName != null">
                , approval_name = #{approvalName}
            </if>
        </set>
        where status = #{status} and situation_type = 0
    </update>

    <select id="querySituationListTime" resultMap="baseResultMap">
        select ms.id,ms.status,ms.create_location,ms.merchant_id,ms.creator_id,c.money
        from merchant_situation ms
        inner join coupon c on ms.coupon_id = c.id
        <where>
            <if test="endTime != null">
                and ms.gmt_create  <![CDATA[<=]]> #{endTime}
            </if>
            <if test="creatTime != null">
                and ms.gmt_create  <![CDATA[ > ]]> #{creatTime}
            </if>
            <if test="status != null">
                and ms.status  = #{status}
            </if>
            <if test="merchantId != null">
                and ms.merchant_id  = #{merchantId}
            </if>
            <if test="id != null">
                and ms.id  = #{id}
            </if>
            <if test="situationType != null">
                and ms.situation_type  = #{situationType}
            </if>
        </where>
    </select>

    <select id="selectCouponByMid" resultType="net.summerfarm.crm.model.vo.MerchantSituationVO">
        SELECT c.`name` couponName,ms.gmt_create creatTime,c.grouping
        FROM merchant_situation ms
        INNER JOIN coupon c ON ms.coupon_id = c.id
        WHERE ms.merchant_id = #{merchantId} AND ms.gmt_create <![CDATA[ > ]]> #{createTime}
    </select>


    <select id="selectByPrimaryKey" resultMap="baseResultMap">
        select
        *
        from merchant_situation
        where  id = #{id,jdbcType=BIGINT}
    </select>
</mapper>