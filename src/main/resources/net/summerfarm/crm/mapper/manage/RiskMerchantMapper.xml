<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.RiskMerchantMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.RiskMerchant">
        <!--@mbg.generated-->
        <!--@Table risk_merchant-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="m_id" jdbcType="BIGINT" property="mId"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="trigger_occasions" jdbcType="VARCHAR" property="triggerOccasions"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="mname" jdbcType="VARCHAR" property="mname"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="bd_id" jdbcType="BIGINT" property="bdId"/>
        <result column="bd_name" jdbcType="VARCHAR" property="bdName"/>
        <result column="door_pic" jdbcType="VARCHAR" property="doorPic"/>
        <result column="door_pic_ocr" jdbcType="VARCHAR" property="doorPicOcr"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="auditor_id" jdbcType="BIGINT" property="auditorId"/>
        <result column="auditor_name" jdbcType="VARCHAR" property="auditorName"/>
        <result column="trigger_classification" jdbcType="INTEGER" property="triggerClassification"/>
    </resultMap>
    <resultMap id="RiskMerchatDetailResultMap" type="net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantDetailVO">
        <id column="id" jdbcType="BIGINT" property="riskMerchantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="m_id" jdbcType="BIGINT" property="mId"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="trigger_occasions" jdbcType="VARCHAR" property="triggerOccasions"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="mname" jdbcType="VARCHAR" property="mname"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="bd_name" jdbcType="VARCHAR" property="bdName"/>
        <result column="door_pic" jdbcType="VARCHAR" property="doorPic"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="auditor_name" jdbcType="VARCHAR" property="auditorName"/>
        <result column="trigger_classification" jdbcType="INTEGER" property="triggerClassification"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="poi" jdbcType="VARCHAR" property="poi"/>
        <result column="door_pic_ocr" jdbcType="VARCHAR" property="doorPicOcr"/>
        <collection property="similarRiskMerchantList" ofType="net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantVO"
                    select="net.summerfarm.crm.mapper.manage.RiskSimilarMerchantMapper.selectByRiskMerchantId"
                    column="id"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        m_id,
        `size`,
        phone,
        trigger_occasions,
        `status`,
        mname,
        province,
        city,
        area,
        area_no,
        area_name,
        bd_id,
        bd_name,
        door_pic,
        door_pic_ocr,
        remark,
        auditor_id,
        auditor_name,
        trigger_classification
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from risk_merchant
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.RiskMerchant" useGeneratedKeys="true">
        insert into risk_merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="mId != null">
                m_id,
            </if>
            <if test="size != null and size != ''">
                `size`,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="triggerOccasions != null">
                trigger_occasions,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="mname != null and mname != ''">
                mname,
            </if>
            <if test="province != null and province != ''">
                province,
            </if>
            <if test="city != null and city != ''">
                city,
            </if>
            <if test="area != null and area != ''">
                area,
            </if>
            <if test="areaNo != null">
                area_no,
            </if>
            <if test="areaName != null and areaName != ''">
                area_name,
            </if>
            <if test="bdId != null">
                bd_id,
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name,
            </if>
            <if test="doorPic != null and doorPic != ''">
                door_pic,
            </if>
            <if test="doorPicOcr != null and doorPicOcr != ''">
                door_pic_ocr,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="auditorId != null">
                auditor_id,
            </if>
            <if test="auditorName != null and auditorName != ''">
                auditor_name,
            </if>
            <if test="triggerClassification != null">
                trigger_classification,
            </if>
            <if test="poi != null and poi != ''">
                poi,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="size != null and size != ''">
                #{size,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != ''">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="triggerOccasions != null">
                #{triggerOccasions,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="mname != null and mname != ''">
                #{mname,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != ''">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != ''">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null and area != ''">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null">
                #{areaNo,jdbcType=INTEGER},
            </if>
            <if test="areaName != null and areaName != ''">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="bdId != null">
                #{bdId,jdbcType=BIGINT},
            </if>
            <if test="bdName != null and bdName != ''">
                #{bdName,jdbcType=VARCHAR},
            </if>
            <if test="doorPic != null and doorPic != ''">
                #{doorPic,jdbcType=VARCHAR},
            </if>
            <if test="doorPicOcr != null and doorPicOcr != ''">
                #{doorPicOcr,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="auditorId != null">
                #{auditorId,jdbcType=BIGINT},
            </if>
            <if test="auditorName != null and auditorName != ''">
                #{auditorName,jdbcType=VARCHAR},
            </if>
            <if test="triggerClassification != null">
                #{triggerClassification,jdbcType=INTEGER },
            </if>
            <if test="poi != null and poi != ''">
                #{poi,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.RiskMerchant">
        <!--@mbg.generated-->
        update risk_merchant
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mId != null">
                m_id = #{mId,jdbcType=BIGINT},
            </if>
            <if test="size != null and size != ''">
                `size` = #{size,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="triggerOccasions != null and triggerOccasions != ''">
                trigger_occasions = #{triggerOccasions,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="mname != null and mname != ''">
                mname = #{mname,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != ''">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != ''">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null and area != ''">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null">
                area_no = #{areaNo,jdbcType=INTEGER},
            </if>
            <if test="areaName != null and areaName != ''">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="bdId != null">
                bd_id = #{bdId,jdbcType=BIGINT},
            </if>
            <if test="bdName != null and bdName != ''">
                bd_name = #{bdName,jdbcType=VARCHAR},
            </if>
            <if test="doorPic != null and doorPic != ''">
                door_pic = #{doorPic,jdbcType=VARCHAR},
            </if>
            <if test="doorPicOcr != null and doorPicOcr != ''">
                door_pic_ocr = #{doorPicOcr,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="auditorId != null">
                auditor_id = #{auditorId,jdbcType=BIGINT},
            </if>
            <if test="auditorName != null and auditorName != ''">
                auditor_name = #{auditorName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listRiskMerchant" resultType="net.summerfarm.crm.model.vo.riskMerchant.RiskMerchantListVO">
        select id                     riskMerchantId,
               update_time            updateTime,
               create_time            createTime,
               m_id                   mId,
               `size`,
               phone,
               trigger_occasions      triggerOccasions,
               `status`,
               mname,
               province,
               city,
               area,
               area_name              areaName,
               bd_name                bdName,
               trigger_classification triggerClassification
        from risk_merchant
        <where>
            <if test="mname != null and mname != ''">
                and mname like concat(#{mname}, '%')
            </if>
            <if test="mId != null">
                and m_id = #{mId}
            </if>
            <if test="triggerOccasions != null">
                and trigger_occasions = #{triggerOccasions}
            </if>
            <if test="triggerClassification != null and triggerClassification.size() > 0">
                and trigger_classification in
                <foreach collection="triggerClassification" open="(" close=")" separator=","
                         item="triggerClassification">
                    #{triggerClassification}
                </foreach>
            </if>
            <if test="size != null and size != ''">
                and size = #{size}
            </if>
            <if test="status != null">
                <if test="status == 0">
                    and status = 0
                </if>
                <if test="status == 1">
                    and status > 0
                </if>
            </if>
            <if test="updateTimeStart != null and updateTimeEnd != null">
                and update_time between DATE_FORMAT(#{updateTimeStart}, '%Y-%m-%d 00:00:00') and DATE_FORMAT(#{updateTimeEnd}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="createTimeStart != null and createTimeEnd != null">
                and create_time between DATE_FORMAT(#{createTimeStart}, '%Y-%m-%d 00:00:00') and DATE_FORMAT(#{createTimeEnd}, '%Y-%m-%d 23:59:59')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectRiskMerchantDetail" resultMap="RiskMerchatDetailResultMap">
        select id,
               update_time,
               m_id,
               `size`,
               phone,
               trigger_occasions,
               `status`,
               mname,
               area_name,
               bd_name,
               trigger_classification,
               create_time,
               door_pic,
               remark,
               auditor_name,
               door_pic_ocr,
               province,
               city,
               area,
               poi
        from risk_merchant
        where id = #{id}
        order by update_time desc
    </select>

    <delete id="auditRiskMerchantDetail">
        update risk_merchant
        set status=#{query.status},
            remark=#{query.remark},
            auditor_id=#{auditorId},
            auditor_name=#{auditorName}
        where id = #{query.riskMerchantId}
    </delete>

    <select id="selectByMid" resultType="java.lang.Long">
        select m_id mId
        from risk_merchant where create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                             and m_id in
        <foreach collection="mIdList" item="mId" separator="," open="(" close=")">
            #{mId}
        </foreach>
    </select>
</mapper>