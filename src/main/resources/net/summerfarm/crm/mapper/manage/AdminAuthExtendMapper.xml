<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.AdminAuthExtendMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.pojo.DO.AdminAuthExtend">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="openid" column="openid" jdbcType="VARCHAR"/>
            <result property="unionId" column="union_id" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type,admin_id,
        user_id,openid,union_id,
        status,update_time,create_time
    </sql>


    <!--<select id="selectByAdminId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from admin_auth_extend
        where status = 0 and type = #{type} and admin_id = #{adminId}
        limit 1
    </select>-->

</mapper>
