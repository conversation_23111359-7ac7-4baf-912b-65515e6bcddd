<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.AreaMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.pojo.DO.Area">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="admin_id" jdbcType="INTEGER" property="adminId"/>
        <result column="parent_no" jdbcType="INTEGER" property="parentNo"/>
        <result column="large_area_no" jdbcType="INTEGER" property="largeAreaNo"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="status" jdbcType="BIT" property="status"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="info" jdbcType="VARCHAR" property="info"/>
        <result column="map_section" jdbcType="VARCHAR" property="mapSection"/>
        <result column="free_day" jdbcType="VARCHAR" property="freeDay"/>
        <result column="origin_area_no" jdbcType="INTEGER" property="originAreaNo"/>
        <result column="next_delivery_date" jdbcType="TIMESTAMP" property="nextDeliveryDate"/>
        <result column="change_flag" jdbcType="BOOLEAN" property="changeFlag"/>
        <result column="change_store_no" jdbcType="INTEGER" property="changeStoreNo"/>
        <result column="change_status" jdbcType="INTEGER" property="changeStatus"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="company_account_id" jdbcType="INTEGER" property="companyAccountId"/>
    </resultMap>

    <resultMap id="areaResultMap" type="net.summerfarm.crm.model.vo.AreaVO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="area_no" jdbcType="INTEGER" property="areaNo"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="admin_id" jdbcType="INTEGER" property="adminId"/>
        <result column="parent_no" jdbcType="INTEGER" property="parentNo"/>
        <result column="large_area_no" jdbcType="INTEGER" property="largeAreaNo"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL" />
        <result column="status" jdbcType="BIT" property="status"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="info" jdbcType="VARCHAR" property="info"/>
        <result column="delivery_rule" jdbcType="VARCHAR" property="deliveryRule" />
        <result column="map_section" jdbcType="VARCHAR" property="mapSection"/>
        <result column="free_day" jdbcType="VARCHAR" property="freeDay"/>
        <result column="origin_area_no" jdbcType="INTEGER" property="originAreaNo"/>
        <result column="next_delivery_date" jdbcType="TIMESTAMP" property="nextDeliveryDate"/>
        <result column="change_flag" jdbcType="BOOLEAN" property="changeFlag"/>
        <result column="change_store_no" jdbcType="INTEGER" property="changeStoreNo"/>
        <result column="change_status" jdbcType="INTEGER" property="changeStatus"/>
        <result column="express_fee" property="expressFee"/>
        <result column="member_rule" property="memberRule"/>
        <result column="realname" property="realname"/>
        <result column="phone" property="phone"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="company_account_id" property="companyAccountId"/>
        <result column="mail_to_address" property="mailToAddress"/>
        <result column="administrative_area" property="administrativeArea"/>
        <result column="support_add_order" property="supportAddOrder"/>
        <result column="update_support_add_order" property="updateSupportAddOrder"/>
        <result column="m_Id" jdbcType="INTEGER" property="mid"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, area_no, area_name, admin_id, parent_no, status,delivery_fee,address,
    info,map_section,free_day,origin_area_no,next_delivery_date,change_flag,
    change_store_no,change_status,pay_channel,company_account_id,large_area_no
    </sql>
    <select id="selectByLargeArea" resultMap="areaResultMap">
        select
        ar.id, ar.area_no , ar.area_name , ar.admin_id , ar.parent_no, ar.delivery_fee,
        ar.status,ar.address,ar.info,ar.delivery_rule ,ar.express_fee
        ,a.realname,ar.member_rule,ar.pay_channel,ar.company_account_id , ar.large_area_no, ar.map_section ,ar.free_day
        ,ar.mail_to_address,ar.origin_area_no ,ar.next_delivery_date,ar.change_flag , ar.change_store_no ,
        ar.change_status, ar.administrative_area,support_add_order,ar.update_support_add_order
        from area ar
        left join admin a on ar.admin_id = a.admin_id
        <where>
            <if test="largeAreaNo != null">
                AND ar.large_area_no = #{largeAreaNo}
            </if>
        </where>
        ORDER BY ar.id
    </select>

    <select id="selectByAreaNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area
        where area_no = #{areaNo}
    </select>

    <select id="selectLargeAreaByAreaNo" resultType="net.summerfarm.crm.model.dto.AreaInfoDTO">
        select a.area_no areaNo,a.area_name areaName,la.large_area_no largeAreaNo,la.large_area_name largeAreaName
        from area a
        left join large_area la on a.large_area_no = la.large_area_no
        where a.area_no = #{areaNo}
    </select>

    <select id="selectByAreaNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from area
        where area_no in
        <foreach collection="areaNo" open="(" close=")" separator="," item="areaNo">
            #{areaNo}
        </foreach>
    </select>

    <select id="queryAreaWithAdmins" resultMap="areaResultMap">
        select m.m_id,
        ar.id, ar.area_no , ar.area_name , ar.admin_id , ar.parent_no, ar.delivery_fee,
        ar.status,ar.address,ar.info,ar.delivery_rule ,ar.express_fee
        ,a.realname, a.phone ,ar.member_rule,ar.pay_channel,ar.company_account_id , ar.large_area_no, ar.map_section ,ar.free_day
        ,ar.mail_to_address,ar.origin_area_no ,ar.next_delivery_date,ar.change_flag , ar.change_store_no ,
        ar.change_status, ar.administrative_area,support_add_order,ar.update_support_add_order
        from merchant m
        inner join area ar on m.area_no = ar.area_no
        left join admin a on ar.admin_id = a.admin_id
        where m.m_id in
        <foreach collection="mIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        and a.is_disabled = 0
    </select>
</mapper>
