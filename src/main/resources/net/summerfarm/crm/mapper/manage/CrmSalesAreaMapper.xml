<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmSalesAreaMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmSalesArea">
    <!--@mbg.generated-->
    <!--@Table crm_sales_area-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bd_org_id" jdbcType="BIGINT" property="bdOrgId" />
    <result column="sales_area_name" jdbcType="VARCHAR" property="salesAreaName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap id="SalesAreaResultMap" type="net.summerfarm.crm.model.vo.areaConfig.SalesAreaVo">
    <id column="id" jdbcType="BIGINT" property="salesAreaId"/>
    <result column="sales_area_name" jdbcType="VARCHAR" property="salesAreaName"/>
    <collection property="salesCityList" ofType="net.summerfarm.crm.model.vo.BdSalesCityVo"
                select="net.summerfarm.crm.mapper.manage.CrmSalesCityMapper.listBySalesAreaId"
                column="id"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id,  bd_org_id, sales_area_name, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_sales_area
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from crm_sales_area
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmSalesArea" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into crm_sales_area
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bdOrgId != null">
        bd_org_id,
      </if>
      <if test="salesAreaName != null and salesAreaName != ''">
        sales_area_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bdOrgId != null">
        #{bdOrgId,jdbcType=BIGINT},
      </if>
      <if test="salesAreaName != null and salesAreaName != ''">
        #{salesAreaName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmSalesArea">
    <!--@mbg.generated-->
    update crm_sales_area
    <set>
      <if test="bdOrgId != null">
        bd_org_id = #{bdOrgId,jdbcType=BIGINT},
      </if>
      <if test="salesAreaName != null and salesAreaName != ''">
        sales_area_name = #{salesAreaName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectBySalesAreaName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_sales_area
    where sales_area_name = #{salesAreaName,jdbcType=VARCHAR}
    <if test="salesAreaId != null">
      and id != #{salesAreaId,jdbcType=BIGINT}
    </if>
  </select>

  <select id="listSalesArea" resultMap="SalesAreaResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_sales_area
    <if test="bdOrgId != null">
      where bd_org_id = #{bdOrgId,jdbcType=INTEGER}
    </if>
  </select>

  <update id="updateBdOrgById">
    update crm_sales_area set bd_org_id = #{bdOrgId} where id = #{id}
  </update>

  <select id="selectByBdOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_sales_area
    where bd_org_id = #{bdOrgId,jdbcType=INTEGER}
  </select>

  <update id="updateBdOrgByIdList">
    update crm_sales_area
    set bd_org_id = #{bdOrgId} where id in
    <foreach collection="idList" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <select id="selectAreaByCity" resultMap="BaseResultMap">
    select csa.id,csa.bd_org_id,csa.sales_area_name
    from crm_sales_area csa
           join crm_sales_city csc on csa.id = csc.sales_area_id
    where csc.province = #{province,jdbcType=VARCHAR}
      and csc.city = #{city,jdbcType=VARCHAR}
      and csc.area = #{area,jdbcType=VARCHAR}
  </select>

  <select id="selectByBdOrgIdList" resultMap="SalesAreaResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_sales_area
    where bd_org_id in
    <foreach collection="bdOrgId" open="(" close=")" separator="," item="bdOrgId">
      #{bdOrgId}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2024-09-11-->
  <select id="selectAllByBdOrgIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_sales_area
    where bd_org_id in
    <foreach item="item" index="index" collection="bdOrgIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
</mapper>