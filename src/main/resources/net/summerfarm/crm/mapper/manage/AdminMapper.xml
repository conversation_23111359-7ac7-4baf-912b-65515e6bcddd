<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.crm.mapper.manage.AdminMapper" >

  <resultMap id="BaseResultMap" type="net.summerfarm.pojo.DO.Admin">
    <id column="admin_id" property="adminId" jdbcType="INTEGER"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="login_fail_times" property="loginFailTimes" jdbcType="INTEGER"/>
    <result column="is_disabled" property="isDisabled" jdbcType="BIT"/>
    <result column="username" property="username" jdbcType="VARCHAR"/>
    <result column="password" property="password" jdbcType="VARCHAR"/>
    <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
    <result column="realname" property="realname" jdbcType="VARCHAR"/>
    <result column="gender" property="gender" jdbcType="BIT"/>
    <result column="department" property="department" jdbcType="VARCHAR"/>
    <result column="phone" property="phone" jdbcType="VARCHAR"/>
    <result column="kp" property="kp" jdbcType="VARCHAR"/>
    <result column="saler_id" property="salerId" jdbcType="INTEGER"/>
    <result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
    <result column="contract" property="contract" jdbcType="VARCHAR"/>
    <result column="contract_method" property="contractMethod" jdbcType="VARCHAR"/>
    <result column="name_remakes" property="nameRemakes" jdbcType="VARCHAR"/>
    <result column="operate_id" property="operateId" jdbcType="INTEGER"/>
    <result column="close_order_type" property="closeOrderType"/>
    <result column="cooperation_stage" property="cooperationStage"/>
    <result column="close_order_time" property="closeOrderTime"/>
    <result column="sku_sorting" property="skuSorting"/>
    <result column="update_close_order_time" property="updateCloseOrderTime"/>
    <result column="low_price_remainder" property="lowPriceRemainder"/>
    <result column="not_included_area" property="notIncludedArea"/>
    <result column="admin_type" property="adminType"/>

  </resultMap>

  <sql id="Base_Column_List">
    admin_id, create_time, login_fail_times, is_disabled, username, password,
    login_time, realname, gender, department, phone,contract_method, name_remakes,operate_id,close_order_type,
    kp, saler_id, saler_name, contract, cooperation_stage,close_order_time,update_close_order_time,sku_sorting,low_price_remainder,not_included_area, admin_type
  </sql>

  <select id="select" resultMap="BaseResultMap" parameterType="java.lang.String">
    SELECT  admin_id, create_time, login_fail_times, is_disabled, username, password,
    login_time, realname, gender, department, phone, NULL add_time
    FROM admin where username = #{username,jdbcType=VARCHAR}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select
    <include refid="Base_Column_List"/>
    from admin
    where admin_id = #{adminId,jdbcType=INTEGER}
  </select>

  <select id="selectUserBaseIdById" resultType="java.lang.Long" parameterType="java.lang.Integer">
    select base_user_id
    from admin
    where admin_id = #{adminId,jdbcType=INTEGER}
  </select>


  <select id="selectByIds" resultMap="BaseResultMap" >
    SELECT  admin_id, create_time, login_fail_times, is_disabled, username, password,
    login_time, realname, gender, department, phone, NULL add_time, admin_type
    FROM admin where admin_id in
    <foreach open="(" close=")" separator="," collection="ids" item="item" index="index">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="selectByRealName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from admin
    where realname = #{realName} and is_disabled = 0
    <if test="userBaseId != null and userBaseId.size() != 0">
      and base_user_id in
      <foreach collection="userBaseId" open="(" close=")" item="item" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

    <select id="selectByInviterChannelCode" resultMap="BaseResultMap" >
      select
        a.admin_id, a.create_time, login_fail_times, is_disabled, username, password,
        login_time, realname, gender, department, phone
      from admin a left join invitecode ic on a.admin_id=ic.admin_id
      where ic.invitecode=#{inviterChannelCode}
    </select>

<!--auto generated by MybatisCodeHelper on 2025-04-17-->
  <select id="selectByRealnameIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin
        where realname in
        <foreach item="item" index="index" collection="realnameCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and is_disabled = 0
    </select>

</mapper>