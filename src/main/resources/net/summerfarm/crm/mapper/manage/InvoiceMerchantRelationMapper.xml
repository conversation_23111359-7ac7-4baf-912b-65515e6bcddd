<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.InvoiceMerchantRelationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.InvoiceConfig">
    <result column="invoice_id"  property="id" />
    <result column="merchant_id"  property="merchantId" />
    <result column="invoice_title"  property="invoiceTitle" />
  </resultMap>
  <sql id="Base_Column_List">
    id, invoice_id, merchant_id, update_time, create_time
  </sql>

  <insert id="insert">
    insert into invoice_merchant_relation (invoice_id, merchant_id, create_time, status)
    values (#{invoiceId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, now(), 0)
  </insert>
  <delete id="deleteByInvoiceIdAndMerchantId">
    delete
    from invoice_merchant_relation
    where merchant_id = #{merchantId} and status =0
  </delete>
  <select id="selectByMerchantId"
          resultType="net.summerfarm.crm.model.domain.InvoiceMerchantRelation">
    select invoice_id invoiceId, merchant_id merchantId, create_time, status
    from invoice_merchant_relation
    where merchant_id = #{merchantId}  and status = 0
  </select>
</mapper>