<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.CrmBdCityMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdCity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="bdId" column="bd_id" jdbcType="INTEGER"/>
            <result property="bdName" column="bd_name" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="area" column="area" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,bd_id,bd_name,
        province,city,area,
        update_time,updater,creator,
        create_time
    </sql>
    <insert id="insertBatch">
        insert into crm_bd_city
        (bd_id, bd_name, province, city, area , creator)
        values
        <foreach collection="list" separator="," item="item">
            (#{bdId},#{bdName},#{item.province},#{item.city},#{item.area},#{creator})
        </foreach>
    </insert>
    <insert id="copyCityByAdminId">
        insert into crm_bd_city
            (bd_id, bd_name, province, city, area, creator)
        select #{bdId}, #{bdName}, province, city, area, #{creator}
        from crm_bd_city
        where bd_id = #{copiedBdId}
    </insert>
    <delete id="deleteByBdId">
        delete
        from crm_bd_city
        where bd_id = #{bdId}
    </delete>
    <select id="selectByBdId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from crm_bd_city where bd_id=#{bdId}
    </select>

    <select id="listByBdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_city where bd_id in
        <foreach collection="bdIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByBdIdCityArea" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from crm_bd_city where bd_id=#{bdId} and city = #{city}
        <if test="area!=null and area!=''">
            and area = #{area}
        </if>
    </select>

</mapper>
