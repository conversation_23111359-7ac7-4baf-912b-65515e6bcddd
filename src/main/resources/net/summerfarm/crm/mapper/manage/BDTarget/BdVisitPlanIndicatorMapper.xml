<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.BDTarget.BdVisitPlanIndicatorMapper">
  
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bd_visit_plan_id" jdbcType="BIGINT" property="bdVisitPlanId" />
    <result column="bd_daily_target_id" jdbcType="BIGINT" property="bdDailyTargetId" />
    <result column="bd_daily_target_detail_id" jdbcType="BIGINT" property="bdDailyTargetDetailId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="target_name" jdbcType="VARCHAR" property="targetName" />
    <result column="indicator_current_value" jdbcType="DECIMAL" property="indicatorCurrentValue" />
    <result column="indicator_potential_value" jdbcType="DECIMAL" property="indicatorPotentialValue" />
    <result column="target_type" jdbcType="INTEGER" property="targetType" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>

  <sql id="Base_Column_List">
    id, create_time, update_time, bd_visit_plan_id, bd_daily_target_id, bd_daily_target_detail_id, bd_id, m_id, target_name, indicator_current_value, indicator_potential_value, target_type, is_deleted
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_bd_visit_plan_indicator
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan_indicator (bd_visit_plan_id, bd_daily_target_id, bd_daily_target_detail_id, bd_id, m_id, target_name, indicator_current_value, indicator_potential_value, target_type, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bdVisitPlanId,jdbcType=BIGINT}, #{item.bdDailyTargetId,jdbcType=BIGINT}, #{item.bdDailyTargetDetailId,jdbcType=BIGINT}, #{item.bdId,jdbcType=INTEGER}, #{item.mId,jdbcType=BIGINT}, #{item.targetName,jdbcType=VARCHAR}, #{item.indicatorCurrentValue,jdbcType=DECIMAL}, #{item.indicatorPotentialValue,jdbcType=DECIMAL}, #{item.targetType,jdbcType=INTEGER}, #{item.isDeleted,jdbcType=INTEGER})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicator" useGeneratedKeys="true" keyProperty="id">
    insert into crm_bd_visit_plan_indicator
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bdVisitPlanId != null">
        bd_visit_plan_id,
      </if>
      <if test="bdDailyTargetId != null">
        bd_daily_target_id,
      </if>
      <if test="bdDailyTargetDetailId != null">
        bd_daily_target_detail_id,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="targetName != null">
        target_name,
      </if>
      <if test="indicatorCurrentValue != null">
        indicator_current_value,
      </if>
      <if test="indicatorPotentialValue != null">
        indicator_potential_value,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bdVisitPlanId != null">
        #{bdVisitPlanId,jdbcType=BIGINT},
      </if>
      <if test="bdDailyTargetId != null">
        #{bdDailyTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdDailyTargetDetailId != null">
        #{bdDailyTargetDetailId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="targetName != null">
        #{targetName,jdbcType=VARCHAR},
      </if>
      <if test="indicatorCurrentValue != null">
        #{indicatorCurrentValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorPotentialValue != null">
        #{indicatorPotentialValue,jdbcType=DECIMAL},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorUpdate">
    update crm_bd_visit_plan_indicator
    <set>
      <if test="bdVisitPlanId != null">
        bd_visit_plan_id = #{bdVisitPlanId,jdbcType=BIGINT},
      </if>
      <if test="bdDailyTargetId != null">
        bd_daily_target_id = #{bdDailyTargetId,jdbcType=BIGINT},
      </if>
      <if test="bdDailyTargetDetailId != null">
        bd_daily_target_detail_id = #{bdDailyTargetDetailId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=INTEGER},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="targetName != null">
        target_name = #{targetName,jdbcType=VARCHAR},
      </if>
      <if test="indicatorCurrentValue != null">
        indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorPotentialValue != null">
        indicator_potential_value = #{indicatorPotentialValue,jdbcType=DECIMAL},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 根据查询条件查询记录列表 -->
  <select id="list" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM crm_bd_visit_plan_indicator
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=BIGINT}
      </if>
      <if test="bdVisitPlanId != null">
        AND bd_visit_plan_id = #{bdVisitPlanId,jdbcType=BIGINT}
      </if>
      <if test="bdDailyTargetId != null">
        AND bd_daily_target_id = #{bdDailyTargetId,jdbcType=BIGINT}
      </if>
      <if test="bdDailyTargetDetailId != null">
        AND bd_daily_target_detail_id = #{bdDailyTargetDetailId,jdbcType=BIGINT}
      </if>
      <if test="bdId != null">
        AND bd_id = #{bdId,jdbcType=INTEGER}
      </if>
      <if test="mId != null">
        AND m_id = #{mId,jdbcType=BIGINT}
      </if>
      <if test="targetName != null and targetName != ''">
        AND target_name = #{targetName,jdbcType=VARCHAR}
      </if>
      <if test="indicatorCurrentValue != null">
        AND indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL}
      </if>
      <if test="indicatorPotentialValue != null">
        AND indicator_potential_value = #{indicatorPotentialValue,jdbcType=DECIMAL}
      </if>
      <if test="targetType != null">
        AND target_type = #{targetType,jdbcType=INTEGER}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="isDeleted != null">
        AND is_deleted = #{isDeleted,jdbcType=INTEGER}
      </if>
      <if test="isDeleted == null">
        AND is_deleted = 0
      </if>
    </where>
    ORDER BY id DESC
  </select>

  <!-- 批量更新指标当前值和潜力值 -->
  <update id="batchUpdateIndicatorValueAndPotential">
    <foreach collection="list" item="item" separator=";">
      UPDATE crm_bd_visit_plan_indicator 
      SET 
        <if test="item.indicatorCurrentValue != null">
          indicator_current_value = #{item.indicatorCurrentValue,jdbcType=DECIMAL},
        </if>
        <if test="item.indicatorPotentialValue != null">
          indicator_potential_value = #{item.indicatorPotentialValue,jdbcType=DECIMAL},
        </if>
        update_time = NOW()
      WHERE bd_visit_plan_id = #{item.bdVisitPlanId,jdbcType=BIGINT}
        AND bd_daily_target_detail_id = #{item.bdDailyTargetDetailId,jdbcType=BIGINT}
        AND m_id = #{item.mId,jdbcType=BIGINT}
    </foreach>
  </update>

  <!-- 更新单条指标当前值和潜力值 -->
  <update id="updateIndicatorValueAndPotential" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync">
    UPDATE crm_bd_visit_plan_indicator 
    SET 
      <if test="indicatorCurrentValue != null">
        indicator_current_value = #{indicatorCurrentValue,jdbcType=DECIMAL},
      </if>
      <if test="indicatorPotentialValue != null">
        indicator_potential_value = #{indicatorPotentialValue,jdbcType=DECIMAL},
      </if>
      update_time = NOW()
    WHERE bd_daily_target_id = #{bdDailyTargetId,jdbcType=BIGINT}
      AND bd_daily_target_detail_id = #{bdDailyTargetDetailId,jdbcType=BIGINT}
      AND m_id = #{mId,jdbcType=BIGINT}
      and bd_id = #{bdId,jdbcType=INTEGER}
  </update>

  <!-- 根据门店ID和目标指标ID更新单条潜力值 -->
  <update id="updatePotentialValueByMerchantId" parameterType="net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorUpdate">
    UPDATE crm_bd_visit_plan_indicator 
    SET 
      indicator_potential_value = #{indicatorPotentialValue,jdbcType=DECIMAL},
      update_time = NOW()
    WHERE m_id = #{mId,jdbcType=BIGINT}
      AND bd_daily_target_detail_id = #{bdDailyTargetDetailId,jdbcType=BIGINT}
  </update>

  <!-- 根据门店ID查询拜访计划指标 -->
  <select id="selectByMerchantIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where m_id in
    <foreach collection="merchantIds" item="merchantId" open="(" close=")" separator=",">
      #{merchantId,jdbcType=BIGINT}
    </foreach>
    order by id desc
  </select>

  <!-- 根据销售ID查询拜访计划指标 -->
  <select id="selectBySalesId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where bd_id = #{salesId,jdbcType=INTEGER}
    order by id desc
  </select>

  <!-- 根据目标指标ID列表查询拜访计划指标 -->
  <select id="selectByTargetDetailIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where bd_daily_target_detail_id in
    <foreach collection="targetDetailIds" item="targetDetailId" open="(" close=")" separator=",">
      #{targetDetailId,jdbcType=BIGINT}
    </foreach>
    order by id desc
  </select>

  <!-- 根据门店ID列表和目标指标ID列表查询拜访计划指标 -->
  <select id="selectByMerchantIdsAndTargetDetailIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where m_id in
    <foreach collection="merchantIds" item="merchantId" open="(" close=")" separator=",">
      #{merchantId,jdbcType=BIGINT}
    </foreach>
    and bd_daily_target_detail_id in
    <foreach collection="targetDetailIds" item="targetDetailId" open="(" close=")" separator=",">
      #{targetDetailId,jdbcType=BIGINT}
    </foreach>
    order by id desc
  </select>

  <!-- 根据组合条件查询拜访计划指标 -->
  <select id="selectByCompositeConditions" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    <where>
      <if test="bdDailyTargetIds != null and bdDailyTargetIds.size() > 0">
        AND bd_daily_target_id in
        <foreach collection="bdDailyTargetIds" item="bdDailyTargetId" open="(" close=")" separator=",">
          #{bdDailyTargetId,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="bdDailyTargetDetailIds != null and bdDailyTargetDetailIds.size() > 0">
        AND bd_daily_target_detail_id in
        <foreach collection="bdDailyTargetDetailIds" item="bdDailyTargetDetailId" open="(" close=")" separator=",">
          #{bdDailyTargetDetailId,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="bdIds != null and bdIds.size() > 0">
        AND bd_id in
        <foreach collection="bdIds" item="bdId" open="(" close=")" separator=",">
          #{bdId,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="mIds != null and mIds.size() > 0">
        AND m_id in
        <foreach collection="mIds" item="mId" open="(" close=")" separator=",">
          #{mId,jdbcType=BIGINT}
        </foreach>
      </if>
      AND is_deleted = 0
    </where>
    order by id desc
  </select>

  <!-- 根据拜访计划ID列表批量逻辑删除指标记录 -->
  <update id="batchLogicalDeleteByVisitPlanIds">
    UPDATE crm_bd_visit_plan_indicator 
    SET is_deleted = 1, update_time = NOW()
    WHERE bd_visit_plan_id IN
    <foreach collection="visitPlanIds" item="visitPlanId" open="(" close=")" separator=",">
      #{visitPlanId,jdbcType=BIGINT}
    </foreach>
    AND is_deleted = 0
  </update>

  <!-- 根据销售拜访计划ID列表查询拜访计划指标 -->
  <select id="selectByVisitPlanIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator
    where bd_visit_plan_id in
    <foreach collection="visitPlanIds" item="visitPlanId" open="(" close=")" separator=",">
      #{visitPlanId,jdbcType=BIGINT}
    </foreach>
    and is_deleted = 0
  </select>

</mapper>