<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.manage.MerchantLabelCorrelaionMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.MerchantLabelCorrelaion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="label_id" jdbcType="BIGINT" property="labelId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, label_id, m_id
  </sql>

  <insert id="insertSelective" parameterType="net.summerfarm.crm.model.domain.MerchantLabelCorrelaion">
    insert into merchant_label_correlation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="labelId != null">
        label_id,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=BIGINT},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="insertByMidAndLabelId">
    insert into merchant_label_correlation(label_id, m_id)
    values (#{labelId,jdbcType=BIGINT}, #{mId,jdbcType=BIGINT})
    ON DUPLICATE key update update_time = now()
  </insert>
  <delete id="deleteByMidAndLabelId">
    delete
    from merchant_label_correlation
    where m_id = #{mId} and label_id = #{labelId}
  </delete>

  <select id="selectByMidAndLabelId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_label_correlation
    where m_id = #{mId} and label_id in
    <foreach collection="labelId" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectByMid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from merchant_label_correlation
    where m_id = #{mId}
  </select>

  <delete id="deleteByMidAndLabelIdList">
    delete
    from merchant_label_correlation
    where m_id = #{mId} and label_id in
    <foreach collection="labelId" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <!--auto generated by MybatisCodeHelper on 2025-01-17-->
  <update id="updateLabelIdById">
    update merchant_label_correlation
    set label_id=#{updatedLabelId,jdbcType=BIGINT}
    where id=#{id,jdbcType=BIGINT}
  </update>
</mapper>