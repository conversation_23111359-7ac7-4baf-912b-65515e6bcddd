<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CustPbScoreCommMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CustPbScoreComm">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="bdId" column="bd_id" jdbcType="BIGINT"/>
            <result property="bdName" column="bd_name" jdbcType="VARCHAR"/>
            <result property="custId" column="cust_id" jdbcType="BIGINT"/>
            <result property="custName" column="cust_name" jdbcType="VARCHAR"/>
            <result property="lastDlvRealAmtPb" column="last_dlv_real_amt_pb" jdbcType="DECIMAL"/>
            <result property="dlvRealAmtPb" column="dlv_real_amt_pb" jdbcType="DECIMAL"/>
            <result property="dlvRealAmtTodayPb" column="dlv_real_amt_today_pb" jdbcType="DECIMAL"/>
            <result property="dlvOrderAmtTodayPb" column="dlv_order_amt_today_pb" jdbcType="DECIMAL"/>
            <result property="dlvOtherAmtTodayPb" column="dlv_other_amt_today_pb" jdbcType="DECIMAL"/>
            <result property="totalCateGroupAmtPb" column="total_cate_group_amt_pb" jdbcType="DECIMAL"/>
            <result property="lastCateGroupScore" column="last_cate_group_score" jdbcType="DOUBLE"/>
            <result property="cateGroupScore" column="cate_group_score" jdbcType="DOUBLE"/>
            <result property="cateGroupScoreToday" column="cate_group_score_today" jdbcType="DOUBLE"/>
            <result property="orderGroupScoreToday" column="order_group_score_today" jdbcType="DOUBLE"/>
            <result property="otherGroupScoreToday" column="other_group_score_today" jdbcType="DOUBLE"/>
            <result property="totalCateGroupScore" column="total_cate_group_score" jdbcType="DOUBLE"/>
            <result property="custType" column="cust_type" jdbcType="VARCHAR"/>
            <result property="custCnt" column="cust_cnt" jdbcType="INTEGER"/>
            <result property="ds" column="ds" jdbcType="VARCHAR"/>
            <result property="orderSource" column="order_source" jdbcType="VARCHAR"/>
            <result property="bdWorkZone" column="bd_work_zone" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        bd_id,bd_name,cust_id,
        cust_name,last_dlv_real_amt_pb,dlv_real_amt_pb,
        dlv_real_amt_today_pb,dlv_order_amt_today_pb,dlv_other_amt_today_pb,
        total_cate_group_amt_pb,last_cate_group_score,cate_group_score,
        cate_group_score_today,order_group_score_today,other_group_score_today,
        total_cate_group_score,cust_type,ds,order_source,bd_work_zone
    </sql>

    <select id="summaryPbByBdIds" resultMap="BaseResultMap">
        SELECT
            `bd_id`,
            MAX(`bd_name`) AS bd_name,
            COUNT(DISTINCT cust_id, cust_name) AS cust_cnt,
            SUM(last_dlv_real_amt_pb) AS last_dlv_real_amt_pb,
            SUM(dlv_real_amt_pb) AS dlv_real_amt_pb,
            SUM(total_cate_group_amt_pb) AS total_cate_group_amt_pb,
            SUM(dlv_real_amt_today_pb) AS dlv_real_amt_today_pb,
            SUM(dlv_order_amt_today_pb) AS dlv_order_amt_today_pb,
            SUM(dlv_other_amt_today_pb) AS dlv_other_amt_today_pb
        FROM `cust_pb_score_comm`
        WHERE `bd_id` IN
        <foreach collection="bdIds" item="bdId" open="(" separator="," close=")">
            #{bdId}
        </foreach>
        AND total_cate_group_amt_pb > 0
        <if test="custType != null and custType != ''">
            AND `cust_type` = #{custType}
        </if>
        <if test="bdWorkZone != null and bdWorkZone != ''">
            AND `bd_work_zone` = #{bdWorkZone}
        </if>
        GROUP BY `bd_id`
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'customerCount'">
                    cust_cnt
                </when>
                <when test="sortField == 'fulfillmentGmv'">
                    dlv_real_amt_pb
                </when>
                <otherwise>
                    bd_id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

     <select id="listPbByBdId" resultMap="BaseResultMap">
        SELECT
            `cust_id`,
            cust_name,
            last_dlv_real_amt_pb,
            dlv_real_amt_pb,
            total_cate_group_amt_pb,
            dlv_real_amt_today_pb,
            dlv_order_amt_today_pb,
            dlv_other_amt_today_pb
        FROM `cust_pb_score_comm`
        WHERE `bd_id` = #{bdId}
        AND total_cate_group_amt_pb > 0
        <if test="mname != null and mname != ''">
            AND `cust_name` LIKE CONCAT('%', #{mname}, '%')
        </if>
        <if test="custType != null and custType != ''">
            AND `cust_type` = #{custType}
        </if>
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'fulfillmentGmv'">
                    dlv_real_amt_pb
                </when>
                <otherwise>
                    id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

    <select id="summaryScoreByBdIds" resultMap="BaseResultMap">
        SELECT
            `bd_id`,
            MAX(`bd_name`) AS bd_name,
            COUNT(DISTINCT cust_id, cust_name) AS cust_cnt,
            SUM(last_cate_group_score) AS last_cate_group_score,
            SUM(cate_group_score) AS cate_group_score,
            SUM(total_cate_group_score) AS total_cate_group_score,
            SUM(cate_group_score_today) AS cate_group_score_today,
            SUM(order_group_score_today) AS order_group_score_today,
            SUM(other_group_score_today) AS other_group_score_today
        FROM `cust_pb_score_comm`
        WHERE `bd_id` IN
        <foreach collection="bdIds" item="bdId" open="(" separator="," close=")">
            #{bdId}
        </foreach>
        AND total_cate_group_score > 0
        <if test="custType != null and custType != ''">
            AND `cust_type` = #{custType}
        </if>
        <if test="bdWorkZone != null and bdWorkZone != ''">
            AND `bd_work_zone` = #{bdWorkZone}
        </if>
        GROUP BY `bd_id`
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'customerCount'">
                    cust_cnt
                </when>
                <when test="sortField == 'score'">
                    cate_group_score
                </when>
                <otherwise>
                    bd_id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

     <select id="listScoreByBdId" resultMap="BaseResultMap">
        SELECT
            `cust_id`,
            cust_name,
            last_cate_group_score,
            cate_group_score,
            total_cate_group_score,
            cate_group_score_today,
            order_group_score_today,
            other_group_score_today
        FROM `cust_pb_score_comm`
        WHERE `bd_id` = #{bdId}
        AND total_cate_group_score > 0
        <if test="mname != null and mname != ''">
            AND `cust_name` LIKE CONCAT('%', #{mname}, '%')
        </if>
        <if test="custType != null and custType != ''">
            AND `cust_type` = #{custType}
        </if>
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'score'">
                    cate_group_score
                </when>
                <otherwise>
                    id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

</mapper>