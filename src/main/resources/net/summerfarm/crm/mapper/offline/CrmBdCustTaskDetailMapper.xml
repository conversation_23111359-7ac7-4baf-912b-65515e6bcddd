<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmBdCustTaskDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdCustTaskDetail">
    <!--@mbg.generated-->
    <!--@Table crm_bd_cust_task_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="job_id" jdbcType="BIGINT" property="jobId" />
    <result column="cust_id" jdbcType="BIGINT" property="custId" />
    <result column="completion_status" jdbcType="INTEGER" property="completionStatus" />
    <result column="job_real_total_amt" jdbcType="DECIMAL" property="jobRealTotalAmt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, job_id, cust_id, completion_status, job_real_total_amt
  </sql>
</mapper>