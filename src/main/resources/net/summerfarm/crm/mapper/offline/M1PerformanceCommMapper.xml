<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.M1PerformanceCommMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.M1PerformanceComm">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="m1Id" column="m1_id" jdbcType="BIGINT"/>
            <result property="m1Name" column="m1_name" jdbcType="VARCHAR"/>
            <result property="pbCommRate" column="pb_comm_rate" jdbcType="DOUBLE"/>
            <result property="pbIncreaseRate" column="pb_increase_rate" jdbcType="DOUBLE"/>
            <result property="pbGmvBase" column="pb_gmv_base" jdbcType="DECIMAL"/>
            <result property="pbTotalDlvCustCnt" column="pb_total_dlv_cust_cnt" jdbcType="INTEGER"/>
            <result property="pbLastMDlvGmv" column="pb_last_m_dlv_gmv" jdbcType="DECIMAL"/>
            <result property="pbMtdDlvGmv" column="pb_mtd_dlv_gmv" jdbcType="DECIMAL"/>
            <result property="pbTodayDlvGmv" column="pb_today_dlv_gmv" jdbcType="DECIMAL"/>
            <result property="pbTodayTrdGmv" column="pb_today_trd_gmv" jdbcType="DECIMAL"/>
            <result property="pbOtherDlvGmv" column="pb_other_dlv_gmv" jdbcType="DECIMAL"/>
            <result property="pbTotalDlvGmv" column="pb_total_dlv_gmv" jdbcType="DECIMAL"/>
            <result property="scoreCommRate" column="score_comm_rate" jdbcType="DOUBLE"/>
            <result property="scoreIncreaseRate" column="score_increase_rate" jdbcType="DOUBLE"/>
            <result property="scoreBase" column="score_base" jdbcType="BIGINT"/>
            <result property="scoreTotalDlvCustCnt" column="score_total_dlv_cust_cnt" jdbcType="INTEGER"/>
            <result property="lastMScores" column="last_m_scores" jdbcType="DOUBLE"/>
            <result property="mtdScores" column="mtd_scores" jdbcType="DOUBLE"/>
            <result property="todayDlvScores" column="today_dlv_scores" jdbcType="DOUBLE"/>
            <result property="todayTrdScores" column="today_trd_scores" jdbcType="DOUBLE"/>
            <result property="otherDlvScores" column="other_dlv_scores" jdbcType="DOUBLE"/>
            <result property="totalScores" column="total_scores" jdbcType="DOUBLE"/>
            <result property="ds" column="ds" jdbcType="VARCHAR"/>
            <result property="custType" column="cust_type" jdbcType="VARCHAR"/>
            <result property="workZone" column="work_zone" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        m1_id,m1_name,pb_comm_rate,
        pb_increase_rate,pb_gmv_base,pb_total_dlv_cust_cnt,
        pb_last_m_dlv_gmv,pb_mtd_dlv_gmv,pb_today_dlv_gmv,
        pb_today_trd_gmv,pb_other_dlv_gmv,pb_total_dlv_gmv,
        score_comm_rate,score_increase_rate,score_base,
        score_total_dlv_cust_cnt,last_m_scores,mtd_scores,
        today_dlv_scores,today_trd_scores,other_dlv_scores,
        total_scores,ds,cust_type,work_zone
    </sql>

    <select id="selectByM1Id" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from m1_performance_comm
        where m1_id = #{m1Id,jdbcType=BIGINT}
        <if test="custType != null and custType != ''">
            AND cust_type = #{custType,jdbcType=VARCHAR}
        </if>
        <if test="workZone != null and workZone != ''">
            AND work_zone = #{workZone,jdbcType=VARCHAR}
        </if>
    </select>

</mapper>