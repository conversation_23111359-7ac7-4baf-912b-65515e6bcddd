<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmCityDayGmvMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCityDayGmv">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="administrativeCity" column="administrative_city" jdbcType="VARCHAR"/>
            <result property="totalGmv" column="total_gmv" jdbcType="DECIMAL"/>
            <result property="pullNewAmount" column="pull_new_amount" jdbcType="INTEGER"/>
            <result property="coreMerchantAmount" column="core_merchant_amount" jdbcType="INTEGER"/>
            <result property="monthLiveAmount" column="month_live_amount" jdbcType="INTEGER"/>
            <result property="openMerchantMonthLive" column="open_merchant_month_live" jdbcType="INTEGER"/>
            <result property="privateMerchantMonthLive" column="private_merchant_month_live" jdbcType="INTEGER"/>
            <result property="openMerchantAmount" column="open_merchant_amount" jdbcType="INTEGER"/>
            <result property="privateMerchantAmount" column="private_merchant_amount" jdbcType="INTEGER"/>
            <result property="performance" column="performance" jdbcType="DECIMAL"/>
            <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
            <result property="teamTag" column="team_tag" jdbcType="TINYINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="operateMerchantNum" column="operate_merchant_num" jdbcType="INTEGER"/>
            <result property="visitNum" column="visit_num" jdbcType="INTEGER"/>
            <result property="escortNum" column="escort_num" jdbcType="INTEGER"/>
            <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
    </resultMap>

    <resultMap id="GmvResultMap" type="net.summerfarm.crm.model.vo.SalesDataVo">
        <result property="currentMonthGmv" column="total_gmv" jdbcType="DECIMAL"/>
        <result property="introducingNewReward" column="pull_new_amount" jdbcType="INTEGER"/>
        <result property="coreMerchantNum" column="core_merchant_amount" jdbcType="INTEGER"/>
        <result property="monthLiving" column="month_live_amount" jdbcType="INTEGER"/>
        <result property="openMonthLiving" column="open_merchant_month_live" jdbcType="INTEGER"/>
        <result property="privateMonthLiving" column="private_merchant_month_live" jdbcType="INTEGER"/>
        <result property="openMerchant" column="open_merchant_amount" jdbcType="INTEGER"/>
        <result property="privateMerchant" column="private_merchant_amount" jdbcType="INTEGER"/>
        <result property="operateMerchantNum" column="operate_merchant_num" jdbcType="INTEGER"/>
        <result property="visitNum" column="visit_num" jdbcType="INTEGER"/>
        <result property="escortNum" column="escort_num" jdbcType="INTEGER"/>
        <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
        <result property="spuAverage" column="spu_average" jdbcType="DECIMAL"/>
        <result property="ordinaryPullNewAmount" column="ordinary_pull_new_amount" jdbcType="INTEGER"/>
        <result property="privateEffectiveMonthLiving" column="private_merchant_effective_month_live" jdbcType="INTEGER"/>
        <result property="openEffectiveMonthLiving" column="open_merchant_effective_month_live" jdbcType="INTEGER"/>
        <result property="brandMerchantCount" column="brand_merchant_count" jdbcType="INTEGER"/>
        <result property="fruitGmv" column="fruit_gmv" jdbcType="DECIMAL"/>
        <result property="fruitMerchantCount" column="fruit_merchant_count" jdbcType="INTEGER"/>
        <result property="dairyGmv" column="dairy_gmv" jdbcType="DECIMAL"/>
        <result property="dairyMerchantCount" column="dairy_merchant_count" jdbcType="INTEGER"/>
        <result property="nonDairyGmv" column="non_dairy_gmv" jdbcType="DECIMAL"/>
        <result property="nonDairyCount" column="non_dairy_merchant_count" jdbcType="INTEGER"/>
        <result property="agentGoodsGmv" column="agentGoodsGmv" jdbcType="INTEGER"/>
        <result property="agentMerchantCount" column="agentMerchantCount" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,administrative_city,total_gmv,
        pull_new_amount,core_merchant_amount,month_live_amount,
        open_merchant_month_live,private_merchant_month_live,open_merchant_amount,
        private_merchant_amount,performance,day_tag,
        team_tag,update_time,create_time,
        operate_merchant_num,visit_num,escort_num,
        brand_gmv
    </sql>

    <select id="selectByCity" resultMap="GmvResultMap">
        select
            IFNULL(SUM(total_gmv),0.00) total_gmv,
            IFNULL(SUM(pull_new_amount),0) pull_new_amount,
            IFNULL(SUM(core_merchant_amount),0) core_merchant_amount,
            IFNULL(SUM(month_live_amount),0) month_live_amount,
            IFNULL(SUM(open_merchant_month_live),0) open_merchant_month_live,
            IFNULL(SUM(private_merchant_month_live),0) private_merchant_month_live,
            IFNULL(SUM(open_merchant_amount),0) open_merchant_amount,
            IFNULL(SUM(private_merchant_amount),0) private_merchant_amount,
            IFNULL(SUM(operate_merchant_num),0) operate_merchant_num,
            IFNULL(SUM(visit_num),0) visit_num,
            IFNULL(SUM(escort_num),0) escort_num,
            IFNULL(SUM(brand_gmv),0) brand_gmv,
            IFNULL(SUM(spu_average),0) spu_average,
            IFNULL(SUM(ordinary_pull_new_amount),0) ordinary_pull_new_amount,
            IFNULL(SUM(private_merchant_effective_month_live),0) private_merchant_effective_month_live,
            IFNULL(SUM(open_merchant_effective_month_live),0) open_merchant_effective_month_live,
            IFNULL(SUM(brand_merchant_count),0) brand_merchant_count,
            IFNULL(SUM(fruit_gmv),0) fruit_gmv,
            IFNULL(SUM(fruit_merchant_count),0) fruit_merchant_count,
            IFNULL(SUM(dairy_gmv),0) dairy_gmv,
            IFNULL(SUM(dairy_merchant_count),0) dairy_merchant_count,
            IFNULL(SUM(non_dairy_gmv),0) non_dairy_gmv,
            IFNULL(SUM(non_dairy_merchant_count),0) non_dairy_merchant_count,
            IFNULL(SUM(agent_goods_gmv),0) agentGoodsGmv,
            IFNULL(SUM(agent_merchant_count),0) agentMerchantCount
        from crm_city_day_gmv
        where day_tag = #{queryTime}
        <if test="type != null">
            and team_tag = #{type}
        </if>
        <if test="administrativeCityList != null and administrativeCityList.size() >0">
            and administrative_city in
            <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByCityList" resultType="net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo">
        select
        IFNULL(SUM(total_gmv),0.00) orderGmv,
        IFNULL(SUM(delivery_gmv),0.00) deliveryGmv,
        IFNULL(SUM(month_live_amount),0) monthLivingAmount,
        IFNULL(SUM(brand_gmv),0) brandGmv,
        IFNULL(SUM(fruit_gmv),0) fruitGmv,
        IFNULL(SUM(dairy_gmv),0) dairyGmv,
        IFNULL(SUM(non_dairy_gmv),0) nonDairyGmv,
        IFNULL(SUM(agent_goods_gmv),0) agentGmv,
        IFNULL(SUM(reward_gmv),0) rewardGmv,
        IFNULL(SUM(pull_new_amount),0) pullNewAmount,
        IFNULL(SUM(visit_num), 0) visitNum
        from crm_city_day_gmv
        where day_tag = #{dayTag} and (administrative_city, area) in
        <foreach collection="salesCity" item="list" open="(" close=")" separator=",">
            (#{list.city}, #{list.area})
        </foreach>
    </select>

    <select id="listByCities" resultType="net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo">
        select total_gmv           orderGmv,
               delivery_gmv        deliveryGmv,
               month_live_amount   monthLivingAmount,
               brand_gmv           brandGmv,
               fruit_gmv           fruitGmv,
               dairy_gmv           dairyGmv,
               non_dairy_gmv       nonDairyGmv,
               agent_goods_gmv     agentGmv,
               reward_gmv          rewardGmv,
               pull_new_amount     pullNewAmount,
               visit_num           visitNum,
               administrative_city city
        from crm_city_day_gmv
        where day_tag = #{dayTag}
          and (administrative_city, area) in
        <foreach collection="salesCity" item="list" open="(" close=")" separator=",">
            (#{list.city}, #{list.area})
        </foreach>
        group by administrative_city
    </select>
</mapper>
