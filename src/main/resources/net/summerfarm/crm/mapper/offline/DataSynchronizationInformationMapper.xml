<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.DataSynchronizationInformationMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.DataSynchronizationInformation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="date_flag" jdbcType="INTEGER" property="dateFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `table_name`, date_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from data_synchronization_information
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByTableName" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM data_synchronization_information
    WHERE table_name = #{tableName}
  </select>
  <select id="selectByTableNames" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM data_synchronization_information
    WHERE table_name in
    <foreach collection="tableNames" item="tableName" open="(" close=")" separator=",">
        #{tableName}
    </foreach>
    <if test="dateFlag != null">
      and date_flag = #{dateFlag}
    </if>
  </select>
</mapper>