<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantIncrementLabelMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantIncrementLabel">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="mId" column="m_id" jdbcType="BIGINT"/>
        <result property="merchantLabel" column="merchant_label" jdbcType="VARCHAR"/>
        <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
        <result property="unionid" column="unionid" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        ,create_time,update_time,
        m_id,merchant_label,day_tag,
        unionid,type,group_name
    </sql>

    <select id="selectByUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_merchant_increment_label where unionid = #{unionId} and day_tag = #{dayTag}
        <if test="isNewCustomer != null and !isNewCustomer">
            and type in (0, 1)
        </if>
    </select>
    <select id="listAllLabel" resultType="java.lang.String">
        SELECT DISTINCT(merchant_label) label from `crm_merchant_increment_label`
    </select>
</mapper>
