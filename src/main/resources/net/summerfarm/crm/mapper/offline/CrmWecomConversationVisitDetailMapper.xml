<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmWecomConversationVisitDetailMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmWecomConversationVisitDetail">
        <!--@mbg.generated-->
        <!--@Table crm_wecom_conversation_visit_detail-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="date_tag" jdbcType="INTEGER" property="dateTag"/>
        <result column="bd_id" jdbcType="BIGINT" property="bdId"/>
        <result column="bd_user_id" jdbcType="VARCHAR" property="bdUserId"/>
        <result column="bd_name" jdbcType="VARCHAR" property="bdName"/>
        <result column="cust_external_user_id" jdbcType="VARCHAR" property="custExternalUserId"/>
        <result column="m_id" jdbcType="BIGINT" property="mId"/>
        <result column="cust_name" jdbcType="VARCHAR" property="custName"/>
        <result column="conversation_list" jdbcType="LONGVARCHAR" property="conversationList"/>
        <result column="image_list" jdbcType="LONGVARCHAR" property="imageList"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="conversation_start_time" jdbcType="TIMESTAMP" property="conversationStartTime"/>
        <result column="conversation_end_time" jdbcType="TIMESTAMP" property="conversationEndTime"/>
        <result column="is_valid_visit" jdbcType="TINYINT" property="isValidVisit"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, date_tag, bd_id, bd_user_id, bd_name, cust_external_user_id, m_id, cust_name,
        conversation_list, image_list, `date`, conversation_start_time, conversation_end_time,
        is_valid_visit, create_time
    </sql>

    <!--auto generated by MybatisCodeHelper on 2024-11-11-->
    <select id="selectValidByDateTagAndBdIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_wecom_conversation_visit_detail
        <where>date_tag=#{dateTag,jdbcType=INTEGER}
            <if test="bdIdCollection != null and bdIdCollection.size() > 0">
                and bd_id in
                <foreach item="item" index="index" collection="bdIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            and is_valid_visit=1
            and bd_id IS NOT NULL
        </where>
    </select>
</mapper>