<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantDayAttributeMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantDayAttribute">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="mId" column="m_id" jdbcType="BIGINT"/>
        <result property="notVisited" column="not_visited" jdbcType="INTEGER"/>
        <result property="daysWithoutOrder" column="days_without_order" jdbcType="INTEGER"/>
        <result property="daysWithoutOrderFollow" column="days_without_order_follow" jdbcType="INTEGER"/>
        <result property="merchantLifecycle" column="merchant_lifecycle" jdbcType="TINYINT"/>
        <result property="orderFrequency" column="order_frequency" jdbcType="INTEGER"/>
        <result property="timingFollowType" column="timing_follow_type" jdbcType="TINYINT"/>
        <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
        <result property="totalGmv" column="merchant_total_gmv"/>
        <result property="coreMerchantTag" column="core_merchant_tag"/>
        <result property="orderCycle" column="order_cycle"/>
        <result property="lifecycle" column="lifecycle" jdbcType="VARCHAR"/>
        <result property="rValue" column="r_value" jdbcType="VARCHAR"/>
        <result property="fValue" column="f_value" jdbcType="VARCHAR"/>
        <result property="mValue" column="m_value" jdbcType="VARCHAR"/>
        <result property="daysNotLoggedIn" column="days_not_logged_in" jdbcType="INTEGER"/>
        <result property="visitCount" column="visit_count" jdbcType="INTEGER"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        m_id,not_visited,days_without_order,days_without_order_follow,
        merchant_lifecycle,order_frequency,timing_follow_type,
        day_tag,lifecycle,r_value,f_value,m_value,days_not_logged_in,visit_count
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select cmd.m_id,cmd.not_visited,cmd.days_without_order,cmd.merchant_lifecycle,cmd.order_frequency,cmd.timing_follow_type,cmd.days_without_order_follow,
        cmd.order_cycle ,mdg.core_merchant_tag,mdg.merchant_total_gmv,cmd.lifecycle lifecycle,cmd.r_value,cmd.f_value,cmd.m_value,days_not_logged_in
        from crm_merchant_day_attribute cmd
        LEFT JOIN crm_merchant_day_gmv mdg ON cmd.m_id = mdg.m_id AND cmd.day_tag = mdg.day_tag
        where  cmd.m_id = #{mId,jdbcType=BIGINT} and cmd.day_tag = #{dayTag}
    </select>

    <select id="selectByMids" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" /> from crm_merchant_day_attribute cmd
        where  cmd.day_tag = #{dayTag}
        and cmd.m_id in
        <foreach collection="mIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectBDMerchantTag" resultType="net.summerfarm.crm.model.vo.MerchantTagVO">
        select
        <if test="tagType==0">lifecycle</if>
        <if test="tagType==1">`r_value`</if>
        <if test="tagType==2">`f_value`</if>
        <if test="tagType==3">`m_value`</if>
        as name,count(1) as `value`
        from crm_merchant_day_attribute
        where m_id in
        <foreach collection="mIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and day_tag=#{dayTag}
        <if test="tagType==0">
            and lifecycle !="L" group by lifecycle
        </if>
        <if test="tagType==1">
            and r_value !="L" group by r_value
        </if>
        <if test="tagType==2">
            and f_value !="L" group by f_value
        </if>
        <if test="tagType==3">
            and m_value !="L" group by m_value
        </if>
    </select>
</mapper>
