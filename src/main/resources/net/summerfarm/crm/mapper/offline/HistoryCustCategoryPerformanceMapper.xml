<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.HistoryCustCategoryPerformanceMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.HistoryCustCategoryPerformance">
        <!--@mbg.generated-->
        <!--@Table history_cust_category_performance-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="is_test_bd" jdbcType="VARCHAR" property="isTestBd"/>
        <result column="bd_name" jdbcType="VARCHAR" property="bdName"/>
        <result column="bd_id" jdbcType="BIGINT" property="bdId"/>
        <result column="bd_region" jdbcType="VARCHAR" property="bdRegion"/>
        <result column="bd_work_zone" jdbcType="VARCHAR" property="bdWorkZone"/>
        <result column="cust_id" jdbcType="BIGINT" property="custId"/>
        <result column="cust_name" jdbcType="VARCHAR" property="custName"/>
        <result column="order_source" jdbcType="VARCHAR" property="orderSource"/>
        <result column="category1" jdbcType="VARCHAR" property="category1"/>
        <result column="spu_group" jdbcType="VARCHAR" property="spuGroup"/>
        <result column="is_dlv_payment" jdbcType="BIGINT" property="isDlvPayment"/>
        <result column="max_months" jdbcType="VARCHAR" property="maxMonths"/>
        <result column="last_months" jdbcType="VARCHAR" property="lastMonths"/>
        <result column="mtd_dlv_ori_amt" jdbcType="DECIMAL" property="mtdDlvOriAmt"/>
        <result column="mtd_dlv_real_amt" jdbcType="DECIMAL" property="mtdDlvRealAmt"/>
        <result column="mtd_dlv_sku_cnt" jdbcType="BIGINT" property="mtdDlvSkuCnt"/>
        <result column="mtd_big_sku_cnt" jdbcType="DOUBLE" property="mtdBigSkuCnt"/>
        <result column="is_completed" jdbcType="BIGINT" property="isCompleted"/>
        <result column="cust_type" jdbcType="VARCHAR" property="custType"/>
        <result column="ds" jdbcType="VARCHAR" property="ds"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, update_time, create_time, is_test_bd, bd_name, bd_id, bd_region, bd_work_zone,
        cust_id, cust_name, order_source, category1, spu_group, is_dlv_payment, max_months,
        last_months, mtd_dlv_ori_amt, mtd_dlv_real_amt, mtd_dlv_sku_cnt, mtd_big_sku_cnt,
        is_completed, cust_type, ds
    </sql>
</mapper>