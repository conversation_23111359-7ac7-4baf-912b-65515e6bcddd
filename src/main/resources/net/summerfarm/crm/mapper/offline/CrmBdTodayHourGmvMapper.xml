<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmBdTodayHourGmvMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdTodayHourGmv">
    <!--@mbg.generated-->
    <!--@Table crm_bd_today_hour_gmv-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date_tag" jdbcType="INTEGER" property="dateTag" />
    <result column="bd_id" jdbcType="BIGINT" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="is_same_city" jdbcType="VARCHAR" property="isSameCity" />
    <result column="origin_total_gmv" jdbcType="DECIMAL" property="originTotalGmv" />
    <result column="real_total_gmv" jdbcType="DECIMAL" property="realTotalGmv" />
    <result column="cust_cnt" jdbcType="BIGINT" property="custCnt" />
    <result column="categories_origin_total_gmv" jdbcType="DECIMAL" property="categoriesOriginTotalGmv" />
    <result column="categories_real_total_gmv" jdbcType="DECIMAL" property="categoriesRealTotalGmv" />
    <result column="categories_cust_cnt" jdbcType="BIGINT" property="categoriesCustCnt" />
    <result column="fruit_origin_total_gmv" jdbcType="DECIMAL" property="fruitOriginTotalGmv" />
    <result column="fruit_real_total_gmv" jdbcType="DECIMAL" property="fruitRealTotalGmv" />
    <result column="fruit_cust_cnt" jdbcType="BIGINT" property="fruitCustCnt" />
    <result column="ancho_origin_total_gmv" jdbcType="DECIMAL" property="anchoOriginTotalGmv" />
    <result column="ancho_real_total_gmv" jdbcType="DECIMAL" property="anchoRealTotalGmv" />
    <result column="ancho_cust_cnt" jdbcType="BIGINT" property="anchoCustCnt" />
    <result column="dairy_origin_total_gmv" jdbcType="DECIMAL" property="dairyOriginTotalGmv" />
    <result column="dairy_real_total_gmv" jdbcType="DECIMAL" property="dairyRealTotalGmv" />
    <result column="dairy_cust_cnt" jdbcType="BIGINT" property="dairyCustCnt" />
    <result column="other_origin_total_gmv" jdbcType="DECIMAL" property="otherOriginTotalGmv" />
    <result column="other_real_total_gmv" jdbcType="DECIMAL" property="otherRealTotalGmv" />
    <result column="other_cust_cnt" jdbcType="BIGINT" property="otherCustCnt" />
    <result column="no_anchor_origin_total_gmv" jdbcType="DECIMAL" property="noAnchorOriginTotalGmv" />
    <result column="no_anchor_real_total_gmv" jdbcType="DECIMAL" property="noAnchorRealTotalGmv" />
    <result column="no_anchor_cust_cnt" jdbcType="BIGINT" property="noAnchorCustCnt" />
    <result column="ordinary_num" jdbcType="BIGINT" property="ordinaryNum" />
    <result column="dlv_real_total_gmv" jdbcType="DECIMAL" property="dlvRealTotalGmv" />
    <result column="dlv_timing_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingRealTotalGmv" />
    <result column="dlv_cust_cnt" jdbcType="BIGINT" property="dlvCustCnt" />
    <result column="dlv_categories_real_total_gmv" jdbcType="DECIMAL" property="dlvCategoriesRealTotalGmv" />
    <result column="dlv_categories_cust_cnt" jdbcType="BIGINT" property="dlvCategoriesCustCnt" />
    <result column="dlv_fruit_real_total_gmv" jdbcType="DECIMAL" property="dlvFruitRealTotalGmv" />
    <result column="dlv_fruit_cust_cnt" jdbcType="BIGINT" property="dlvFruitCustCnt" />
    <result column="dlv_ancho_real_total_gmv" jdbcType="DECIMAL" property="dlvAnchoRealTotalGmv" />
    <result column="dlv_timing_ancho_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingAnchoRealTotalGmv" />
    <result column="dlv_ancho_cust_cnt" jdbcType="BIGINT" property="dlvAnchoCustCnt" />
    <result column="dlv_dairy_real_total_gmv" jdbcType="DECIMAL" property="dlvDairyRealTotalGmv" />
    <result column="dlv_timing_dairy_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingDairyRealTotalGmv" />
    <result column="dlv_dairy_cust_cnt" jdbcType="BIGINT" property="dlvDairyCustCnt" />
    <result column="dlv_other_real_total_gmv" jdbcType="DECIMAL" property="dlvOtherRealTotalGmv" />
    <result column="dlv_timing_other_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingOtherRealTotalGmv" />
    <result column="dlv_other_cust_cnt" jdbcType="BIGINT" property="dlvOtherCustCnt" />
    <result column="dlv_no_anchor_real_total_gmv" jdbcType="DECIMAL" property="dlvNoAnchorRealTotalGmv" />
    <result column="dlv_no_anchor_cust_cnt" jdbcType="BIGINT" property="dlvNoAnchorCustCnt" />
    <result column="dlv_timing_no_anchor_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingNoAnchorRealTotalGmv" />
    <result column="no_anchor_categories_kpi_gmv" jdbcType="DECIMAL" property="noAnchorCategoriesKpiGmv" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, date_tag, bd_id, bd_name, is_same_city, origin_total_gmv, real_total_gmv, cust_cnt, 
    categories_origin_total_gmv, categories_real_total_gmv, categories_cust_cnt, fruit_origin_total_gmv, 
    fruit_real_total_gmv, fruit_cust_cnt, ancho_origin_total_gmv, ancho_real_total_gmv, 
    ancho_cust_cnt, dairy_origin_total_gmv, dairy_real_total_gmv, dairy_cust_cnt, other_origin_total_gmv, 
    other_real_total_gmv, other_cust_cnt, no_anchor_origin_total_gmv, no_anchor_real_total_gmv, 
    no_anchor_cust_cnt, ordinary_num, dlv_real_total_gmv, dlv_timing_real_total_gmv, 
    dlv_cust_cnt, dlv_categories_real_total_gmv, dlv_categories_cust_cnt, dlv_fruit_real_total_gmv, 
    dlv_fruit_cust_cnt, dlv_ancho_real_total_gmv, dlv_timing_ancho_real_total_gmv, dlv_ancho_cust_cnt, 
    dlv_dairy_real_total_gmv, dlv_timing_dairy_real_total_gmv, dlv_dairy_cust_cnt, dlv_other_real_total_gmv, 
    dlv_timing_other_real_total_gmv, dlv_other_cust_cnt, dlv_no_anchor_real_total_gmv, 
    dlv_no_anchor_cust_cnt, dlv_timing_no_anchor_real_total_gmv, no_anchor_categories_kpi_gmv
  </sql>

<!--auto generated by MybatisCodeHelper on 2024-09-03-->
  <select id="selectOneByBdIdAndDateTagAndIsSameCity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_today_hour_gmv
        where bd_id=#{bdId,jdbcType=BIGINT} and date_tag=#{dateTag,jdbcType=INTEGER} and
        is_same_city=#{isSameCity,jdbcType=VARCHAR}
    </select>

</mapper>