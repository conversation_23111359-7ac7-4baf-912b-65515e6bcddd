<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.BDTarget.BdVisitPlanIndicatorSyncMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BDTarget.BdVisitPlanIndicatorSync">
    <!--@mbg.generated-->
    <!--@Table crm_bd_visit_plan_indicator_sync-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bd_visit_plan_id" jdbcType="BIGINT" property="bdVisitPlanId" />
    <result column="bd_daily_target_id" jdbcType="BIGINT" property="bdDailyTargetId" />
    <result column="bd_daily_target_detail_id" jdbcType="BIGINT" property="bdDailyTargetDetailId" />
    <result column="bd_id" jdbcType="INTEGER" property="bdId" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="indicator_current_value" jdbcType="DECIMAL" property="indicatorCurrentValue" />
    <result column="indicator_potential_value" jdbcType="DECIMAL" property="indicatorPotentialValue" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, bd_visit_plan_id, bd_daily_target_id, bd_daily_target_detail_id, bd_id,
    m_id, indicator_current_value, indicator_potential_value
  </sql>

  <!-- 分页查询 -->
  <select id="selectByPage" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator_sync
    order by id desc
    limit #{offset}, #{limit}
  </select>

  <!-- 根据门店ID列表查询记录 -->
  <select id="selectByMerchantIds" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator_sync
    where m_id in
    <foreach collection="merchantIds" item="merchantId" open="(" close=")" separator=",">
      #{merchantId,jdbcType=BIGINT}
    </foreach>
    order by id desc
  </select>

  <!-- 分页查询去重门店ID列表 -->
  <select id="selectDistinctMerchantIdsByPage" resultType="java.lang.Long">
    select distinct m_id
    from crm_bd_visit_plan_indicator_sync
    where m_id is not null
    order by m_id
    limit #{offset}, #{limit}
  </select>

  <!-- 批量插入记录 -->
  <insert id="insertBatch">
    insert into crm_bd_visit_plan_indicator_sync
    (create_time, update_time, bd_visit_plan_id, bd_daily_target_id, bd_daily_target_detail_id, bd_id, m_id,
     indicator_current_value, indicator_potential_value)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
       #{item.bdVisitPlanId,jdbcType=BIGINT}, #{item.bdDailyTargetId,jdbcType=BIGINT},
       #{item.bdDailyTargetDetailId,jdbcType=BIGINT}, #{item.bdId,jdbcType=INTEGER}, #{item.mId,jdbcType=BIGINT},
       #{item.indicatorCurrentValue,jdbcType=DECIMAL}, #{item.indicatorPotentialValue,jdbcType=DECIMAL})
    </foreach>
  </insert>

  <!-- 根据多个条件查询同步数据 -->
  <select id="selectByMultipleConditions" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_visit_plan_indicator_sync
    <where>
      <if test="bdIds != null and bdIds.size() > 0">
        AND bd_id IN
        <foreach collection="bdIds" item="bdId" open="(" close=")" separator=",">
          #{bdId,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="mIds != null and mIds.size() > 0">
        AND m_id IN
        <foreach collection="mIds" item="mId" open="(" close=")" separator=",">
          #{mId,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="bdDailyTargetIds != null and bdDailyTargetIds.size() > 0">
        AND bd_daily_target_id IN
        <foreach collection="bdDailyTargetIds" item="bdDailyTargetId" open="(" close=")" separator=",">
          #{bdDailyTargetId,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="bdDailyTargetDetailIds != null and bdDailyTargetDetailIds.size() > 0">
        AND bd_daily_target_detail_id IN
        <foreach collection="bdDailyTargetDetailIds" item="bdDailyTargetDetailId" open="(" close=")" separator=",">
          #{bdDailyTargetDetailId,jdbcType=BIGINT}
        </foreach>
      </if>
    </where>
    order by id desc
  </select>

</mapper>