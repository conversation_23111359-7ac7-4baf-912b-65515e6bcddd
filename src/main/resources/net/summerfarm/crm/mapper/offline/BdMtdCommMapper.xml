<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.BdMtdCommMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BdMtdComm">
    <!--@mbg.generated-->
    <!--@Table bd_mtd_comm-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="last_bd_name" jdbcType="VARCHAR" property="lastBdName" />
    <result column="last_bd_id" jdbcType="BIGINT" property="lastBdId" />
    <result column="dep_level3" jdbcType="VARCHAR" property="depLevel3" />
    <result column="dep_name" jdbcType="VARCHAR" property="depName" />
    <result column="total_score_num" jdbcType="DOUBLE" property="totalScoreNum" />
    <result column="bd_performance_rate" jdbcType="DOUBLE" property="bdPerformanceRate" />
    <result column="total_comm_amt" jdbcType="DECIMAL" property="totalCommAmt" />
    <result column="a_commisstion_amt" jdbcType="DECIMAL" property="aCommisstionAmt" />
    <result column="a_cust_cnt" jdbcType="INTEGER" property="aCustCnt" />
    <result column="a_cust_comm_amt" jdbcType="DECIMAL" property="aCustCommAmt" />
    <result column="more_than_spu_cnt" jdbcType="BIGINT" property="moreThanSpuCnt" />
    <result column="a_spu_comm_amt" jdbcType="DECIMAL" property="aSpuCommAmt" />
    <result column="category_comm_amt" jdbcType="DECIMAL" property="categoryCommAmt" />
    <result column="old_cust_comm" jdbcType="DECIMAL" property="oldCustComm" />
    <result column="new_cust_comm" jdbcType="DECIMAL" property="newCustComm" />
    <result column="big_sku_cnt" jdbcType="DOUBLE" property="bigSkuCnt" />
    <result column="old_big_sku_cnt" jdbcType="DOUBLE" property="oldBigSkuCnt" />
    <result column="new_big_sku_cnt" jdbcType="DOUBLE" property="newBigSkuCnt" />
    <result column="dlv_real_amt" jdbcType="DECIMAL" property="dlvRealAmt" />
    <result column="item_profit_amt" jdbcType="DECIMAL" property="itemProfitAmt" />
    <result column="ds" jdbcType="VARCHAR" property="ds" />
    <result column="dlv_spu_cnt" jdbcType="BIGINT" property="dlvSpuCnt" />
    <result column="more_than_spu_cust_cnt" jdbcType="BIGINT" property="moreThanSpuCustCnt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, update_time, create_time, last_bd_name, last_bd_id, dep_level3, dep_name, total_score_num, 
    bd_performance_rate, total_comm_amt, a_commisstion_amt, a_cust_cnt, a_cust_comm_amt, 
    more_than_spu_cnt, a_spu_comm_amt, category_comm_amt, old_cust_comm, new_cust_comm, 
    big_sku_cnt, old_big_sku_cnt, new_big_sku_cnt, dlv_real_amt, item_profit_amt, ds, 
    dlv_spu_cnt, more_than_spu_cust_cnt
  </sql>
</mapper>