<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.MerchantPurchaseDataMapper">
    <!-- 结果集映射 -->
    <resultMap id="merchantPurchaseDataResultMap" type="net.summerfarm.crm.model.domain.MerchantPurchaseData">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="day_tag" property="dayTag" jdbcType="VARCHAR"/>
		<result column="m_id" property="mId" jdbcType="NUMERIC"/>
		<result column="mname" property="mname" jdbcType="VARCHAR"/>
		<result column="area_name" property="areaName" jdbcType="VARCHAR"/>
		<result column="large_area_name" property="largeAreaName" jdbcType="VARCHAR"/>
		<result column="area_no" property="areaNo" jdbcType="INTEGER"/>
		<result column="type" property="type" jdbcType="VARCHAR"/>
		<result column="total_purchased_quantity" property="totalPurchasedQuantity" jdbcType="INTEGER"/>
		<result column="purchased_sku_list" property="purchasedSkuList" jdbcType="LONGVARCHAR"/>
		<result column="purchased_pd_name_list" property="purchasedPdNameList" jdbcType="LONGVARCHAR"/>
		<result column="order_time_range" property="orderTimeRange" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="merchantPurchaseDataColumns">
          t.id,
          t.day_tag,
          t.m_id,
          t.mname,
          t.area_name,
          t.large_area_name,
          t.area_no,
          t.type,
          t.total_purchased_quantity,
          t.purchased_sku_list,
          t.purchased_pd_name_list,
          t.order_time_range
    </sql>

    <select id="selectByDayTag" resultMap="merchantPurchaseDataResultMap">
        select <include refid="merchantPurchaseDataColumns"/>
        from merchant_purchase_data t
        where t.day_tag = #{dayTag}
    </select>


</mapper>