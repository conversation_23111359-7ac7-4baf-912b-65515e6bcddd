<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.BdCvalMtdPerformanceMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.BdCvalMtdPerformance">
    <!--@mbg.generated-->
    <!--@Table bd_cval_mtd_performance-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="bd_id" jdbcType="BIGINT" property="bdId" />
    <result column="cust_value_lable" jdbcType="VARCHAR" property="custValueLable" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="bd_region" jdbcType="VARCHAR" property="bdRegion" />
    <result column="bd_work_zone" jdbcType="VARCHAR" property="bdWorkZone" />
    <result column="is_test_bd" jdbcType="VARCHAR" property="isTestBd" />
    <result column="sum_dlv_cust_cnt" jdbcType="BIGINT" property="sumDlvCustCnt" />
    <result column="sum_cust_comm_amt" jdbcType="DECIMAL" property="sumCustCommAmt" />
    <result column="sum_dlv_ori_amt" jdbcType="DECIMAL" property="sumDlvOriAmt" />
    <result column="sum_dlv_real_amt" jdbcType="DECIMAL" property="sumDlvRealAmt" />
    <result column="sum_item_profit_amt" jdbcType="DECIMAL" property="sumItemProfitAmt" />
    <result column="sum_dlv_real_amt_at" jdbcType="DECIMAL" property="sumDlvRealAmtAt" />
    <result column="sum_dlv_real_amt_expo" jdbcType="DECIMAL" property="sumDlvRealAmtExpo" />
    <result column="sum_dlv_real_amt_profit" jdbcType="DECIMAL" property="sumDlvRealAmtProfit" />
    <result column="sum_dlv_real_amt_normal" jdbcType="DECIMAL" property="sumDlvRealAmtNormal" />
    <result column="sum_dlv_real_amt_fruit" jdbcType="DECIMAL" property="sumDlvRealAmtFruit" />
    <result column="sum_cate_group_score_num" jdbcType="DOUBLE" property="sumCateGroupScoreNum" />
    <result column="sum_dlv_spu_cnt" jdbcType="BIGINT" property="sumDlvSpuCnt" />
    <result column="sum_more_than_spu_cnt" jdbcType="BIGINT" property="sumMoreThanSpuCnt" />
    <result column="sum_more_than_spu_comm" jdbcType="DECIMAL" property="sumMoreThanSpuComm" />
    <result column="sum_more_than_spu_cust" jdbcType="BIGINT" property="sumMoreThanSpuCust" />
    <result column="sum_total_comm_amt" jdbcType="DECIMAL" property="sumTotalCommAmt" />
    <result column="ds" jdbcType="VARCHAR" property="ds" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, update_time, create_time, bd_id, cust_value_lable, bd_name, bd_region, bd_work_zone, 
    is_test_bd, sum_dlv_cust_cnt, sum_cust_comm_amt, sum_dlv_ori_amt, sum_dlv_real_amt, 
    sum_item_profit_amt, sum_dlv_real_amt_at, sum_dlv_real_amt_expo, sum_dlv_real_amt_profit, 
    sum_dlv_real_amt_normal, sum_dlv_real_amt_fruit, sum_cate_group_score_num, sum_dlv_spu_cnt, 
    sum_more_than_spu_cnt, sum_more_than_spu_comm, sum_more_than_spu_cust, sum_total_comm_amt, 
    ds
  </sql>
</mapper>