<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmBdBigCustMonthGmvMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdBigCustMonthGmv">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="big_cust" jdbcType="VARCHAR" property="bigCust" />
    <result column="merchant_total_gmv" jdbcType="DECIMAL" property="merchantTotalGmv" />
    <result column="credit_paid_gmv" jdbcType="DECIMAL" property="creditPaidGmv" />
    <result column="cash_settlement_gmv" jdbcType="DECIMAL" property="cashSettlementGmv" />
    <result column="merchant_total_gmv_ex" jdbcType="DECIMAL" property="merchantTotalGmvEx" />
    <result column="credit_paid_gmv_ex" jdbcType="DECIMAL" property="creditPaidGmvEx" />
    <result column="cash_settlement_gmv_ex" jdbcType="DECIMAL" property="cashSettlementGmvEx" />
    <result column="month_tag" jdbcType="INTEGER" property="monthTag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, bd_name, big_cust, merchant_total_gmv, credit_paid_gmv, cash_settlement_gmv, 
    merchant_total_gmv_ex, credit_paid_gmv_ex, cash_settlement_gmv_ex, month_tag, update_time, 
    create_time
  </sql>
  <select id="selectByAdminId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_bd_big_cust_month_gmv
    where admin_id = #{adminId} and month_tag = #{monthTah}
  </select>
</mapper>