<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmBdVisitCustMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdVisitCust">
        <!--@mbg.generated-->
        <!--@Table crm_bd_visit_cust-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="date_tag" jdbcType="INTEGER" property="dateTag"/>
        <result column="cust_id" jdbcType="BIGINT" property="custId"/>
        <result column="cust_name" jdbcType="VARCHAR" property="custName"/>
        <result column="cust_type" jdbcType="VARCHAR" property="custType"/>
        <result column="cust_group" jdbcType="VARCHAR" property="custGroup"/>
        <result column="register_province" jdbcType="VARCHAR" property="registerProvince"/>
        <result column="register_city" jdbcType="VARCHAR" property="registerCity"/>
        <result column="register_type" jdbcType="INTEGER" property="registerType"/>
        <result column="visit_bd_id" jdbcType="BIGINT" property="visitBdId"/>
        <result column="visit_bd_name" jdbcType="VARCHAR" property="visitBdName"/>
        <result column="visit_type" jdbcType="VARCHAR" property="visitType"/>
        <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime"/>
        <result column="visit_after_load_time" jdbcType="TIMESTAMP" property="visitAfterLoadTime"/>
        <result column="visit_after_click_time" jdbcType="TIMESTAMP" property="visitAfterClickTime"/>
        <result column="visit_after_add_time" jdbcType="TIMESTAMP" property="visitAfterAddTime"/>
        <result column="visit_after_is_buy" jdbcType="VARCHAR" property="visitAfterIsBuy"/>
        <result column="visit_after_firstorder_time" jdbcType="VARCHAR" property="visitAfterFirstorderTime"/>
        <result column="visit_after_origin_amt" jdbcType="DECIMAL" property="visitAfterOriginAmt"/>
        <result column="visit_after_sku_name" jdbcType="VARCHAR" property="visitAfterSkuName"/>
        <result column="visit_remarks" jdbcType="LONGVARCHAR" property="visitRemarks"/>
        <result column="is_effective_visit" jdbcType="VARCHAR" property="isEffectiveVisit"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, date_tag, cust_id, cust_name, cust_type, cust_group, register_province, register_city,
        register_type, visit_bd_id, visit_bd_name, visit_type, visit_time, visit_after_load_time,
        visit_after_click_time, visit_after_add_time, visit_after_is_buy, visit_after_firstorder_time,
        visit_after_origin_amt, visit_after_sku_name, visit_remarks, is_effective_visit
    </sql>

    <!--auto generated by MybatisCodeHelper on 2024-11-04-->
    <select id="selectEffectiveByDateTagAndTypeAndBdIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_visit_cust
        where date_tag=#{dateTag,jdbcType=INTEGER}
        and visit_type=#{visitType,jdbcType=VARCHAR}
        and is_effective_visit = '是'
        <if test="visitBdIdCollection != null and visitBdIdCollection.size() > 0">
            and visit_bd_id in
            <foreach item="item" index="index" collection="visitBdIdCollection"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        and visit_bd_id is not null
    </select>
</mapper>