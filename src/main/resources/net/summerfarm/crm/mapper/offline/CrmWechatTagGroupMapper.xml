<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmWechatTagGroupMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmWechatTagGroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="merchant_label" jdbcType="VARCHAR" property="merchantLabel" />
    <result column="day_tag" jdbcType="INTEGER" property="dayTag" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="rank" jdbcType="INTEGER" property="rank" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, group_name, merchant_label, day_tag, `type`, `rank`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_wechat_tag_group
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByGroupNameStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_wechat_tag_group
    where day_tag = #{dayTag} and group_name=#{groupName} and type=#{type}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_wechat_tag_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmWechatTagGroup" useGeneratedKeys="true">
    insert into crm_wechat_tag_group (create_time, update_time, group_name, 
      merchant_label, day_tag, `type`, 
      `rank`)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{groupName,jdbcType=VARCHAR}, 
      #{merchantLabel,jdbcType=VARCHAR}, #{dayTag,jdbcType=INTEGER}, #{type,jdbcType=TINYINT}, 
      #{rank,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmWechatTagGroup" useGeneratedKeys="true">
    insert into crm_wechat_tag_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="merchantLabel != null">
        merchant_label,
      </if>
      <if test="dayTag != null">
        day_tag,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="rank != null">
        `rank`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="merchantLabel != null">
        #{merchantLabel,jdbcType=VARCHAR},
      </if>
      <if test="dayTag != null">
        #{dayTag,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="rank != null">
        #{rank,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmWechatTagGroup">
    update crm_wechat_tag_group
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="merchantLabel != null">
        merchant_label = #{merchantLabel,jdbcType=VARCHAR},
      </if>
      <if test="dayTag != null">
        day_tag = #{dayTag,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="rank != null">
        `rank` = #{rank,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmWechatTagGroup">
    update crm_wechat_tag_group
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      group_name = #{groupName,jdbcType=VARCHAR},
      merchant_label = #{merchantLabel,jdbcType=VARCHAR},
      day_tag = #{dayTag,jdbcType=INTEGER},
      `type` = #{type,jdbcType=TINYINT},
      `rank` = #{rank,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>