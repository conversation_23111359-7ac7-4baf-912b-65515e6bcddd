<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmWecomUserSummaryMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmWecomUserSummary">
        <!--@mbg.generated-->
        <!--@Table crm_wecom_user_summary-->
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="private_sea_count" property="privateSeaCount"/>
        <result column="effective_wecom_user_count" property="effectiveWecomUserCount"/>
        <result column="wecom_user_count" property="wecomUserCount"/>
        <result column="bd_delete_wecom_count" property="bdDeleteWecomCount"/>
        <result column="user_delete_wecom_count" property="userDeleteWecomCount"/>
        <result column="delete_wecom_count" property="deleteWecomCount"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        create_time,
        update_time,
        bd_id,
        bd_name,
        private_sea_count,
        effective_wecom_user_count,
        wecom_user_count,
        bd_delete_wecom_count,
        user_delete_wecom_count,
        delete_wecom_count,
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_wecom_user_summary
        where id = #{id}
    </select>

    <select id="summaryByBdId" resultType="net.summerfarm.crm.model.vo.weCom.WeComUserSummaryVo">
        select ifnull(sum(private_sea_count), 0)                               as privateSeaCount,
               ifnull(sum(effective_wecom_user_count), 0)                      as effectiveWecomUserCount,
               ifnull(sum(wecom_user_count), 0)                                as wecomUserCount,
               ifnull(sum(bd_delete_wecom_count), 0)                           as bdDeleteWecomCount,
               ifnull(sum(user_delete_wecom_count), 0)                         as userDeleteWecomCount,
               ifnull(sum(delete_wecom_count), 0)                              as deleteWecomCount,
               ifnull(sum(effective_wecom_user_count), 0) /
               ifnull(sum(private_sea_count), 0) * 100                         as effectiveWecomUserProportion,
               ifnull(sum(wecom_user_count) / sum(private_sea_count), 0) * 100 as wecomUserProportion
        from crm_wecom_user_summary
        where bd_id in
        <foreach collection="bdId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>