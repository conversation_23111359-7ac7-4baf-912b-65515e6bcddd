<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.Hive2mysqlCrmShopListPoolMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="cuisine_type" jdbcType="VARCHAR" property="cuisineType" />
    <result column="common_price" jdbcType="DECIMAL" property="commonPrice" />
    <result column="popularity_index" jdbcType="DECIMAL" property="popularityIndex" />
    <result column="taste_index" jdbcType="DECIMAL" property="tasteIndex" />
    <result column="service_index" jdbcType="DECIMAL" property="serviceIndex" />
    <result column="environment_index" jdbcType="DECIMAL" property="environmentIndex" />
    <result column="shop_age" jdbcType="VARCHAR" property="shopAge" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="shop_region" jdbcType="VARCHAR" property="shopRegion" />
    <result column="shopping_mall" jdbcType="VARCHAR" property="shoppingMall" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="shopid" jdbcType="VARCHAR" property="shopid" />
    <result column="comments_count_inc" jdbcType="VARCHAR" property="commentsCountInc" />
    <result column="is_chain" jdbcType="VARCHAR" property="isChain" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />

  </resultMap>
  <sql id="Base_Column_List">
    id, shop_name, cuisine_type, common_price, popularity_index, taste_index, service_index, 
    environment_index, shop_age, province, city, district, shop_region, shopping_mall, 
    lat, lng, brand, phone_number, address, shopid, comments_count_inc, is_chain,store_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hive2mysql_crm_shop_list_pool
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from hive2mysql_crm_shop_list_pool
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool" useGeneratedKeys="true">
    insert into hive2mysql_crm_shop_list_pool (shop_name, cuisine_type, common_price, 
      popularity_index, taste_index, service_index, 
      environment_index, shop_age, province, 
      city, district, shop_region, 
      shopping_mall, lat, lng, 
      brand, phone_number, address, 
      shopid, comments_count_inc, is_chain
      )
    values (#{shopName,jdbcType=VARCHAR}, #{cuisineType,jdbcType=VARCHAR}, #{commonPrice,jdbcType=DECIMAL}, 
      #{popularityIndex,jdbcType=DECIMAL}, #{tasteIndex,jdbcType=DECIMAL}, #{serviceIndex,jdbcType=DECIMAL}, 
      #{environmentIndex,jdbcType=DECIMAL}, #{shopAge,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{district,jdbcType=VARCHAR}, #{shopRegion,jdbcType=VARCHAR}, 
      #{shoppingMall,jdbcType=VARCHAR}, #{lat,jdbcType=VARCHAR}, #{lng,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{shopid,jdbcType=VARCHAR}, #{commentsCountInc,jdbcType=VARCHAR}, #{isChain,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool" useGeneratedKeys="true">
    insert into hive2mysql_crm_shop_list_pool
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="cuisineType != null">
        cuisine_type,
      </if>
      <if test="commonPrice != null">
        common_price,
      </if>
      <if test="popularityIndex != null">
        popularity_index,
      </if>
      <if test="tasteIndex != null">
        taste_index,
      </if>
      <if test="serviceIndex != null">
        service_index,
      </if>
      <if test="environmentIndex != null">
        environment_index,
      </if>
      <if test="shopAge != null">
        shop_age,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="district != null">
        district,
      </if>
      <if test="shopRegion != null">
        shop_region,
      </if>
      <if test="shoppingMall != null">
        shopping_mall,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="shopid != null">
        shopid,
      </if>
      <if test="commentsCountInc != null">
        comments_count_inc,
      </if>
      <if test="isChain != null">
        is_chain,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="cuisineType != null">
        #{cuisineType,jdbcType=VARCHAR},
      </if>
      <if test="commonPrice != null">
        #{commonPrice,jdbcType=DECIMAL},
      </if>
      <if test="popularityIndex != null">
        #{popularityIndex,jdbcType=DECIMAL},
      </if>
      <if test="tasteIndex != null">
        #{tasteIndex,jdbcType=DECIMAL},
      </if>
      <if test="serviceIndex != null">
        #{serviceIndex,jdbcType=DECIMAL},
      </if>
      <if test="environmentIndex != null">
        #{environmentIndex,jdbcType=DECIMAL},
      </if>
      <if test="shopAge != null">
        #{shopAge,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        #{district,jdbcType=VARCHAR},
      </if>
      <if test="shopRegion != null">
        #{shopRegion,jdbcType=VARCHAR},
      </if>
      <if test="shoppingMall != null">
        #{shoppingMall,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="shopid != null">
        #{shopid,jdbcType=VARCHAR},
      </if>
      <if test="commentsCountInc != null">
        #{commentsCountInc,jdbcType=VARCHAR},
      </if>
      <if test="isChain != null">
        #{isChain,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool">
    update hive2mysql_crm_shop_list_pool
    <set>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="cuisineType != null">
        cuisine_type = #{cuisineType,jdbcType=VARCHAR},
      </if>
      <if test="commonPrice != null">
        common_price = #{commonPrice,jdbcType=DECIMAL},
      </if>
      <if test="popularityIndex != null">
        popularity_index = #{popularityIndex,jdbcType=DECIMAL},
      </if>
      <if test="tasteIndex != null">
        taste_index = #{tasteIndex,jdbcType=DECIMAL},
      </if>
      <if test="serviceIndex != null">
        service_index = #{serviceIndex,jdbcType=DECIMAL},
      </if>
      <if test="environmentIndex != null">
        environment_index = #{environmentIndex,jdbcType=DECIMAL},
      </if>
      <if test="shopAge != null">
        shop_age = #{shopAge,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        district = #{district,jdbcType=VARCHAR},
      </if>
      <if test="shopRegion != null">
        shop_region = #{shopRegion,jdbcType=VARCHAR},
      </if>
      <if test="shoppingMall != null">
        shopping_mall = #{shoppingMall,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="shopid != null">
        shopid = #{shopid,jdbcType=VARCHAR},
      </if>
      <if test="commentsCountInc != null">
        comments_count_inc = #{commentsCountInc,jdbcType=VARCHAR},
      </if>
      <if test="isChain != null">
        is_chain = #{isChain,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.Hive2mysqlCrmShopListPool">
    update hive2mysql_crm_shop_list_pool
    set shop_name = #{shopName,jdbcType=VARCHAR},
      cuisine_type = #{cuisineType,jdbcType=VARCHAR},
      common_price = #{commonPrice,jdbcType=DECIMAL},
      popularity_index = #{popularityIndex,jdbcType=DECIMAL},
      taste_index = #{tasteIndex,jdbcType=DECIMAL},
      service_index = #{serviceIndex,jdbcType=DECIMAL},
      environment_index = #{environmentIndex,jdbcType=DECIMAL},
      shop_age = #{shopAge,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      district = #{district,jdbcType=VARCHAR},
      shop_region = #{shopRegion,jdbcType=VARCHAR},
      shopping_mall = #{shoppingMall,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      shopid = #{shopid,jdbcType=VARCHAR},
      comments_count_inc = #{commentsCountInc,jdbcType=VARCHAR},
      is_chain = #{isChain,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="count" resultType="int">
    select count(1) from hive2mysql_crm_shop_list_pool
  </select>


  <select id="selectPage"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hive2mysql_crm_shop_list_pool
        order by id asc
    limit #{offset},#{offSize}
    </select>
</mapper>