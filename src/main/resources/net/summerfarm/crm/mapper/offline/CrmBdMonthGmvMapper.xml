<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmBdMonthGmvMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdMonthGmv">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="total_gmv" jdbcType="DECIMAL" property="totalGmv" />
    <result column="single_gmv" jdbcType="DECIMAL" property="singleGmv" />
    <result column="vip_gmv" jdbcType="DECIMAL" property="vipGmv" />
    <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
    <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
    <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv" />
    <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv" />
    <result column="reward_gmv" jdbcType="DECIMAL" property="rewardGmv" />
    <result column="reward_amout" jdbcType="INTEGER" property="rewardAmout" />
    <result column="core_merchant_amout" jdbcType="INTEGER" property="coreMerchantAmout" />
    <result column="month_live_amout" jdbcType="INTEGER" property="monthLiveAmout" />
    <result column="pull_new_amout" jdbcType="INTEGER" property="pullNewAmout" />
    <result column="ordinary_pull_new_amount" jdbcType="INTEGER" property="ordinaryPullNewAmount" />
    <result column="no_order_register" jdbcType="INTEGER" property="noOrderRegister" />
    <result column="performance" jdbcType="DECIMAL" property="performance" />
    <result column="month_tag" jdbcType="INTEGER" property="monthTag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap id="GmvResultMap" type="net.summerfarm.crm.model.vo.AdminInfoVo">
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="total_gmv" jdbcType="DECIMAL" property="totalGmv" />
    <result column="single_gmv" jdbcType="DECIMAL" property="singleShopGmv" />
    <result column="vip_gmv" jdbcType="DECIMAL" property="vipGmv" />
    <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
    <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
    <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv" />
    <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv" />
    <result column="reward_gmv" jdbcType="DECIMAL" property="desGoodsSalesGmv" />
    <result column="reward_amout" jdbcType="INTEGER" property="desGoodsSales" />
    <result column="core_merchant_amout" jdbcType="INTEGER" property="coreMerchantNum" />
    <result column="month_live_amout" jdbcType="INTEGER" property="monthLiving" />
    <result column="pull_new_amout" jdbcType="INTEGER" property="newAdminNum" />
    <result column="performance" jdbcType="DECIMAL" property="performance" />
    <result column="ordinary_num" jdbcType="DECIMAL" property="ordinaryNum" />
    <result column="drop_in_visit_num" jdbcType="DECIMAL" property="dropInVisitNum" />
    <result column="efficient_num" jdbcType="DECIMAL" property="efficientNum" />
    <result column="worth_num" jdbcType="DECIMAL" property="worthNum" />
    <result column="ordinary_num" jdbcType="INTEGER" property="ordinaryNum" />
    <result column="drop_in_visit_num" jdbcType="INTEGER" property="dropInVisitNum" />
    <result column="efficient_num" jdbcType="INTEGER" property="efficientNum" />
    <result column="worth_num" jdbcType="INTEGER" property="worthNum" />
    <result column="category_award" jdbcType="DECIMAL" property="categoryAward" />
    <result column="core_merchant_card_level" jdbcType="DECIMAL" property="coreMerchantCardLevel" />
    <result column="delivery_gmv" jdbcType="DECIMAL" property="deliveryGmv" />
    <result column="spu_average" jdbcType="DECIMAL" property="spuAverage" />
    <result column="category_multiply_gmv" jdbcType="DECIMAL" property="categoryMultiplyGmv" />
    <result column="single_month_live_num"  property="singleMonthLiveNum" />
    <result column="vip_month_live_num"  property="vipMonthLiveNum" />
    <result column="single_spu_average"  property="singleSpuAverage" />
    <result column="vip_spu_average"  property="vipSpuAverage" />
    <result column="ordinary_pull_new_amount" jdbcType="INTEGER" property="ordinaryPullNewAmount" />
    <result column="no_order_register" jdbcType="INTEGER" property="noOrderRegister" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, admin_name, total_gmv, brand_gmv,month_live_amout, pull_new_amout,spu_average,
    fruit_gmv, dairy_gmv, non_dairy_gmv,reward_gmv, reward_amout,
    single_gmv,single_month_live_num,single_spu_average,
    vip_gmv,vip_month_live_num,vip_spu_average,
    core_merchant_amout,performance,
    ordinary_num,drop_in_visit_num,efficient_num,worth_num,
    category_award,core_merchant_card_level,delivery_gmv,category_multiply_gmv,
    month_tag, update_time, create_time,ordinary_pull_new_amount,no_order_register
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_month_gmv
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByAdminId" resultMap="GmvResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_bd_month_gmv
    where admin_id = #{adminId} AND month_tag = #{monthTag}
  </select>
</mapper>