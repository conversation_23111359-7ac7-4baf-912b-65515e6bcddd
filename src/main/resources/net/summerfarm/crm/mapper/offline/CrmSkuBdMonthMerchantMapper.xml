<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmSkuBdMonthMerchantMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmSkuBdMonthMerchant">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="bdId" column="bd_id" jdbcType="OTHER"/>
            <result property="merchantIdText" column="merchant_id_text" jdbcType="VARCHAR"/>
            <result property="monthTag" column="month_tag" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        sku,bd_id,merchant_id_text,
        month_tag
    </sql>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from crm_sku_bd_month_merchant
        where  month_tag = #{monthTag} and bd_id = #{adminId} and sku = #{sku}
    </select>
</mapper>
