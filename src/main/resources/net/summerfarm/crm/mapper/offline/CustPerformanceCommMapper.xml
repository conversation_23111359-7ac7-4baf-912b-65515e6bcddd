<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CustPerformanceCommMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CustPerformanceComm">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="bdId" column="bd_id" jdbcType="BIGINT"/>
            <result property="bdName" column="bd_name" jdbcType="VARCHAR"/>
            <result property="orderSource" column="order_source" jdbcType="VARCHAR"/>
            <result property="custId" column="cust_id" jdbcType="BIGINT"/>
            <result property="custName" column="cust_name" jdbcType="VARCHAR"/>
            <result property="custCommAmt" column="cust_comm_amt" jdbcType="DECIMAL"/>
            <result property="dlvRealAmt" column="dlv_real_amt" jdbcType="DECIMAL"/>
            <result property="dlvRealAmtToday" column="dlv_real_amt_today" jdbcType="DECIMAL"/>
            <result property="dlvOrderAmtToday" column="dlv_order_amt_today" jdbcType="DECIMAL"/>
            <result property="dlvOtherAmtToday" column="dlv_other_amt_today" jdbcType="DECIMAL"/>
            <result property="dlvMonthTotalAmt" column="dlv_month_total_amt" jdbcType="DECIMAL"/>
            <result property="dlvMonthTodayTotalAmt" column="dlv_month_today_total_amt" jdbcType="DECIMAL"/>
            <result property="dlvSpuCnt" column="dlv_spu_cnt" jdbcType="INTEGER"/>
            <result property="dlvRealSpuCntToday" column="dlv_real_spu_cnt_today" jdbcType="INTEGER"/>
            <result property="dlvOrderSpuCntToday" column="dlv_order_spu_cnt_today" jdbcType="INTEGER"/>
            <result property="dlvOtherSpuCntToday" column="dlv_other_spu_cnt_today" jdbcType="INTEGER"/>
            <result property="dlvMonthTotalSpuCnt" column="dlv_month_total_spu_cnt" jdbcType="INTEGER"/>
            <result property="dlvMonthTodayTotalSpuCnt" column="dlv_month_today_total_spu_cnt" jdbcType="INTEGER"/>
            <result property="moreThanSpuCnt" column="more_than_spu_cnt" jdbcType="INTEGER"/>
            <result property="moreThanSpuComm" column="more_than_spu_comm" jdbcType="DECIMAL"/>
            <result property="totalCommAmt" column="total_comm_amt" jdbcType="DECIMAL"/>
            <result property="custValueLable" column="cust_value_lable" jdbcType="VARCHAR"/>
            <result property="isCompleteAmt" column="is_complete_amt" jdbcType="VARCHAR"/>
            <result property="isCompleteSpu" column="is_complete_spu" jdbcType="VARCHAR"/>
            <result property="ds" column="ds" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        bd_id,bd_name,order_source,
        cust_id,cust_name,cust_comm_amt,
        dlv_real_amt,dlv_real_amt_today,dlv_order_amt_today,
        <choose>
            <when test="firstDayOfMonth != null and firstDayOfMonth == true">
                dlv_real_amt_today as dlv_month_today_total_amt,
            </when>
            <otherwise>
                dlv_real_amt + dlv_real_amt_today as dlv_month_today_total_amt,
            </otherwise>
        </choose>
        dlv_other_amt_today,dlv_month_total_amt,dlv_spu_cnt,
        dlv_real_spu_cnt_today,dlv_order_spu_cnt_today,dlv_other_spu_cnt_today,
        dlv_month_total_spu_cnt,dlv_month_today_total_spu_cnt,more_than_spu_cnt,more_than_spu_comm,
        total_comm_amt,cust_value_lable,
        is_complete_amt,is_complete_spu,ds
    </sql>

    <select id="summaryByBdIds" resultType="net.summerfarm.crm.model.domain.CustPerformanceComm">
        SELECT
        bd_id as bdId,
        max(bd_name) as bdName,
        count(distinct cust_id, cust_name) as custCnt,
        sum(cust_comm_amt) as custCommAmt,
        sum(dlv_real_amt) as dlvRealAmt,
        sum(dlv_real_amt_today) as dlvRealAmtToday,
        sum(dlv_order_amt_today) as dlvOrderAmtToday,
        sum(dlv_other_amt_today) as dlvOtherAmtToday,
        sum(dlv_month_total_amt) as dlvMonthTotalAmt,
        <choose>
            <when test="firstDayOfMonth != null and firstDayOfMonth == true">
                sum(dlv_real_amt_today) as dlvMonthTodayTotalAmt,
            </when>
            <otherwise>
                sum(dlv_real_amt + dlv_real_amt_today) as dlvMonthTodayTotalAmt,
            </otherwise>
        </choose>
        sum(dlv_spu_cnt) as dlvSpuCnt,
        sum(dlv_real_spu_cnt_today) as dlvRealSpuCntToday,
        sum(dlv_order_spu_cnt_today) as dlvOrderSpuCntToday,
        sum(dlv_other_spu_cnt_today) as dlvOtherSpuCntToday,
        sum(dlv_month_total_spu_cnt) as dlvMonthTotalSpuCnt,
        sum(dlv_month_today_total_spu_cnt) as dlvMonthTodayTotalSpuCnt
        FROM cust_performance_comm
        WHERE bd_id IN
        <foreach collection="bdIds" item="bdId" open="(" separator="," close=")">
            #{bdId}
        </foreach>
        <if test="custValueLabel != null and custValueLabel != ''">
            AND cust_value_lable = #{custValueLabel,jdbcType=VARCHAR}
        </if>
        GROUP BY bd_id
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'customerCount'">
                    custCnt
                </when>
                <when test="sortField == 'fulfillmentGmv'">
                    dlvRealAmt
                </when>
                <when test="sortField == 'spuCount'">
                    dlvSpuCnt
                </when>
                <when test="sortField == 'byTonightFulfillmentGmv'">
                    dlvMonthTodayTotalAmt
                </when>
                <when test="sortField == 'byTonightSpuCount'">
                    dlvMonthTodayTotalSpuCnt
                </when>
                <when test="sortField == 'monthTotalFulfillmentGmv'">
                    dlvMonthTotalAmt
                </when>
                <when test="sortField == 'monthTotalSpuCount'">
                    dlvMonthTotalSpuCnt
                </when>
                <otherwise>
                    bd_id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

    <select id="listByBdId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM cust_performance_comm
        WHERE bd_id = #{bdId,jdbcType=BIGINT}
        <if test="custValueLabel != null and custValueLabel != ''">
            AND cust_value_lable = #{custValueLabel,jdbcType=VARCHAR}
        </if>
        <if test="mname != null and mname != ''">
            AND cust_name LIKE CONCAT('%', #{mname,jdbcType=VARCHAR}, '%')
        </if>
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'fulfillmentGmv'">
                    dlv_real_amt
                </when>
                <when test="sortField == 'spuCount'">
                    dlv_spu_cnt
                </when>
                <when test="sortField == 'byTonightFulfillmentGmv'">
                    dlv_month_today_total_amt
                </when>
                <when test="sortField == 'byTonightSpuCount'">
                    dlv_month_today_total_spu_cnt
                </when>
                <when test="sortField == 'monthTotalFulfillmentGmv'">
                    dlv_month_total_amt
                </when>
                <when test="sortField == 'monthTotalSpuCount'">
                    dlv_month_total_spu_cnt
                </when>
                <otherwise>
                    id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

    <select id="listByOrderSource" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cust_performance_comm
        WHERE order_source = #{orderSource,jdbcType=VARCHAR}
        ORDER BY id ASC
    </select>

    <select id="listByCustIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cust_performance_comm
        WHERE cust_id IN
        <foreach collection="custIds" item="custId" open="(" separator="," close=")">
            #{custId}
        </foreach>
        <if test="orderSource != null and orderSource != ''">
            AND order_source = #{orderSource,jdbcType=VARCHAR}
        </if>
    </select>

</mapper>