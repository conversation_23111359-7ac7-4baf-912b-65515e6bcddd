<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmClueDeleteMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmClueDelete">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, shop_id
  </sql>

  <select id="queryDeleteShopList"  resultType="string">
    select
    shop_id
    from crm_clue_delete
    order by shop_id asc
    limit #{offset},#{offSize}
  </select>

  <select id="count" resultType="int">
    select count(1) from crm_clue_delete
  </select>

</mapper>