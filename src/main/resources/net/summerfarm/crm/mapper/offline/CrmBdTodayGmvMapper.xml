<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmBdTodayGmvMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdTodayDayGmv">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="adminName" column="admin_name" jdbcType="VARCHAR"/>
            <result property="totalGmv" column="total_gmv" jdbcType="DECIMAL"/>
            <result property="deliveryGmv" column="delivery_gmv" jdbcType="DECIMAL"/>
            <result property="deliverySpuAvg" column="delivery_spu_avg" jdbcType="DECIMAL"/>
            <result property="singleGmv" column="single_gmv" jdbcType="DECIMAL"/>
            <result property="estimatedIncome" column="estimated_income" jdbcType="DECIMAL"/>
            <result property="vipGmv" column="vip_gmv" jdbcType="DECIMAL"/>
            <result property="fruitGmv" column="fruit_gmv" jdbcType="DECIMAL"/>
            <result property="dairyGmv" column="dairy_gmv" jdbcType="DECIMAL"/>
            <result property="nonDairyGmv" column="non_dairy_gmv" jdbcType="DECIMAL"/>
            <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
            <result property="rewardGmv" column="reward_gmv" jdbcType="DECIMAL"/>
            <result property="rewardAmout" column="reward_amout" jdbcType="OTHER"/>
            <result property="brandOrderMerchant" column="brand_order_merchant" jdbcType="OTHER"/>
            <result property="pullNewAmount" column="pull_new_amout" jdbcType="OTHER"/>
            <result property="visitNum" column="visit_num" jdbcType="OTHER"/>
            <result property="agentGmv" column="agent_gmv" jdbcType="DECIMAL"/>
            <result property="orderMerchant" column="order_merchant" jdbcType="INTEGER"/>
            <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,admin_id,admin_name,
        total_gmv,delivery_gmv,delivery_spu_avg,
        single_gmv,vip_gmv,fruit_gmv,
        dairy_gmv,non_dairy_gmv,brand_gmv,agent_gmv,order_merchant,
        reward_gmv,reward_amout,brand_order_merchant,
        pull_new_amout,visit_num,day_tag,estimated_income,
        update_time,create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_bd_today_gmv
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByAdminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_bd_today_gmv
        where  admin_id = #{adminId} and day_tag = #{dayTag}
    </select>

    <select id="selectIncomeRankingListVO" resultType="net.summerfarm.crm.model.vo.RankingListVO">
        select admin_id bdId, admin_name bdName, estimated_income rankNum
        from crm_bd_today_gmv
        where day_tag = #{dayTag}
        order by estimated_income desc
        limit 10
    </select>

    <select id="listByAdminId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_bd_today_gmv
        where day_tag = #{dayTag}
          and admin_id in
        <foreach collection="adminId" item="adminId" open="(" close=")" separator=",">
            #{adminId}
        </foreach>
    </select>
</mapper>
