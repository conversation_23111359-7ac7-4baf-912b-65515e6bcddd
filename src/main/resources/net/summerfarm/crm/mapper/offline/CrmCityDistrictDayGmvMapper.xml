<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmCityDistrictDayGmvMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCityDistrictDayGmv">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="district" column="district" jdbcType="VARCHAR"/>
        <result property="totalGmv" column="total_gmv" jdbcType="DECIMAL"/>
        <result property="pullNewAmount" column="pull_new_amount" jdbcType="OTHER"/>
        <result property="monthLiveAmount" column="month_live_amount" jdbcType="OTHER"/>
        <result property="openMerchantMonthLive" column="open_merchant_month_live" jdbcType="OTHER"/>
        <result property="privateMerchantMonthLive" column="private_merchant_month_live" jdbcType="OTHER"/>
        <result property="openMerchantAmount" column="open_merchant_amount" jdbcType="OTHER"/>
        <result property="privateMerchantAmount" column="private_merchant_amount" jdbcType="OTHER"/>
        <result property="dayTag" column="day_tag" jdbcType="OTHER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="operateMerchantNum" column="operate_merchant_num" jdbcType="OTHER"/>
        <result property="visitNum" column="visit_num" jdbcType="OTHER"/>
        <result property="escortNum" column="escort_num" jdbcType="OTHER"/>
        <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
        <result property="spuAverage" column="spu_average" jdbcType="DECIMAL"/>
        <result property="ordinaryPullNewAmount" column="ordinary_pull_new_amount" jdbcType="OTHER"/>
        <result property="privateMerchantEffectiveMonthLive" column="private_merchant_effective_month_live" jdbcType="OTHER"/>
        <result property="openMerchantEffectiveMonthLive" column="open_merchant_effective_month_live" jdbcType="OTHER"/>
        <result property="brandMerchantCount" column="brand_merchant_count" jdbcType="OTHER"/>
        <result property="fruitGmv" column="fruit_gmv" jdbcType="DECIMAL"/>
        <result property="fruitMerchantCount" column="fruit_merchant_count" jdbcType="OTHER"/>
        <result property="dairyGmv" column="dairy_gmv" jdbcType="DECIMAL"/>
        <result property="dairyMerchantCount" column="dairy_merchant_count" jdbcType="OTHER"/>
        <result property="nonDairyGmv" column="non_dairy_gmv" jdbcType="DECIMAL"/>
        <result property="nonDairyMerchantCount" column="non_dairy_merchant_count" jdbcType="OTHER"/>
        <result property="agentGoodsGmv" column="agent_goods_gmv" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,city,district,
        total_gmv,pull_new_amount,
        month_live_amount,open_merchant_month_live,private_merchant_month_live,
        open_merchant_amount,private_merchant_amount,
        day_tag,update_time,create_time,
        operate_merchant_num,visit_num,escort_num,
        brand_gmv,spu_average,
        ordinary_pull_new_amount,private_merchant_effective_month_live,open_merchant_effective_month_live,
        brand_merchant_count,fruit_gmv,fruit_merchant_count,
        dairy_gmv,dairy_merchant_count,non_dairy_gmv,
        non_dairy_merchant_count,agent_goods_gmv
    </sql>

    <select id="selectByCityAndDistrict" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_city_district_day_gmv
        where day_tag=#{dayTag} and city=#{city}
        <if test="district!=null and district.size()>0">
            and district in
            <foreach collection="district" item="district" open="(" close=")" separator=",">
                #{district}
            </foreach>
        </if>
    </select>
    <select id="selectByCityAndDistrictSum" resultMap="BaseResultMap">
        select sum(total_gmv)                             total_gmv,
               sum(pull_new_amount)                       pull_new_amount,
               sum(month_live_amount)                     month_live_amount,
               sum(open_merchant_month_live)              open_merchant_month_live,
               sum(private_merchant_month_live)           private_merchant_month_live,
               sum(open_merchant_amount)                  open_merchant_amount,
               sum(private_merchant_amount)               private_merchant_amount,
               sum(operate_merchant_num)                  operate_merchant_num,
               sum(visit_num)                             visit_num,
               sum(escort_num)                            escort_num,
               sum(brand_gmv)                             brand_gmv,
               sum(spu_average)                           spu_average,
               sum(ordinary_pull_new_amount)              ordinary_pull_new_amount,
               sum(private_merchant_effective_month_live) private_merchant_effective_month_live,
               sum(open_merchant_effective_month_live)    open_merchant_effective_month_live,
               sum(brand_merchant_count)                  brand_merchant_count,
               sum(fruit_gmv)                             fruit_gmv,
               sum(fruit_merchant_count)                  fruit_merchant_count,
               sum(dairy_gmv)                             dairy_gmv,
               sum(dairy_merchant_count)                  dairy_merchant_count,
               sum(non_dairy_gmv)                         non_dairy_gmv,
               sum(non_dairy_merchant_count)              non_dairy_merchant_count,
               sum(agent_goods_gmv)                       agent_goods_gmv
        from crm_city_district_day_gmv
        where day_tag = #{dayTag} and city = #{city}
        <if test="district!=null and district.size()>0">
            and district in
            <foreach collection="district" item="district" open="(" close=")" separator=",">
                #{district}
            </foreach>
        </if>
    </select>
</mapper>
