<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantDayGmvMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantDayGmv">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="merchant_total_gmv" jdbcType="DECIMAL" property="merchantTotalGmv" />
    <result column="distribution_gmv" jdbcType="DECIMAL" property="distributionGmv" />
    <result column="delivery_unit_price" jdbcType="DECIMAL" property="deliveryUnitPrice" />
    <result column="distribution_amout" jdbcType="INTEGER" property="distributionAmout" />
    <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
    <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
    <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv" />
    <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv" />
    <result column="core_merchant_tag" jdbcType="TINYINT" property="coreMerchantTag" />
    <result column="day_tag" jdbcType="INTEGER" property="dayTag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <result column="thirty_days_order_spu" jdbcType="INTEGER" property="thirtyDaysOrderSpu" />
    <result column="thirty_sixty_days_order_spu" jdbcType="INTEGER" property="thirtySixtyDaysOrderSpu" />
    <result column="seven_days_fruit_gmv" jdbcType="DECIMAL" property="sevenDaysFruitGmv" />
    <result column="seven_days_dairy_gmv" jdbcType="DECIMAL" property="sevenDaysDairyGmv" />
    <result column="seven_days_brand_gmv" jdbcType="DECIMAL" property="sevenDaysBrandGmv" />
    <result column="thirty_days_fruit_gmv" jdbcType="DECIMAL" property="thirtyDaysFruitGmv" />
    <result column="thirty_days_dairy_gmv" jdbcType="DECIMAL" property="thirtyDaysDairyGmv" />
    <result column="thirty_days_brand_gmv" jdbcType="DECIMAL" property="thirtyDaysBrandGmv" />
    <result column="browsing_history" jdbcType="VARCHAR" property="browsingHistory" />
  </resultMap>
  <resultMap id="GmvResultMap" type="net.summerfarm.crm.model.vo.PrivateSeaVO">
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="merchant_name" jdbcType="VARCHAR" property="mname" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="merchant_total_gmv" jdbcType="DECIMAL" property="thisMonthGmv" />
    <result column="distribution_gmv" jdbcType="DECIMAL" property="distributionGmv" />
    <result column="delivery_unit_price" jdbcType="DECIMAL" property="thisMonthDeliveryUnitPrice" />
    <result column="distribution_amout" jdbcType="INTEGER" property="distributionAmount" />
    <result column="core_merchant_tag" jdbcType="TINYINT" property="coreMerchantTag" />
    <result column="sku_num" property="thisMonthSkuCount" />

    <result column="thirty_days_order_spu" jdbcType="INTEGER" property="thirtyDaysOrderSpu" />
    <result column="thirty_sixty_days_order_spu" jdbcType="INTEGER" property="thirtySixtyDaysOrderSpu" />
    <result column="seven_days_fruit_gmv" jdbcType="DECIMAL" property="sevenDaysFruitGmv" />
    <result column="seven_days_dairy_gmv" jdbcType="DECIMAL" property="sevenDaysDairyGmv" />
    <result column="seven_days_brand_gmv" jdbcType="DECIMAL" property="sevenDaysBrandGmv" />
    <result column="thirty_days_fruit_gmv" jdbcType="DECIMAL" property="thirtyDaysFruitGmv" />
    <result column="thirty_days_dairy_gmv" jdbcType="DECIMAL" property="thirtyDaysDairyGmv" />
    <result column="thirty_days_brand_gmv" jdbcType="DECIMAL" property="thirtyDaysBrandGmv" />
    <result column="browsing_history" jdbcType="VARCHAR" property="browsingHistory" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, merchant_name, area_no, merchant_total_gmv, distribution_gmv, delivery_unit_price,
    distribution_amout, fruit_gmv, dairy_gmv, non_dairy_gmv, brand_gmv, core_merchant_tag, sku_num,
    day_tag, update_time, create_time,thirty_days_order_spu,thirty_sixty_days_order_spu,seven_days_fruit_gmv,seven_days_dairy_gmv,
    seven_days_brand_gmv,thirty_days_fruit_gmv,thirty_days_dairy_gmv,thirty_days_brand_gmv,browsing_history
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_merchant_day_gmv
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByInput" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
      SELECT
        IFNULL(SUM(merchant_total_gmv + reward_gmv),0) currentMonthGmv,
        IFNULL(SUM(distribution_gmv),0) distributionGmv,
        IFNULL(SUM(brand_gmv),0) brandGmv,
        IFNULL(SUM(coalesce(core_merchant_tag,0) = 1),0) coreMerchantNum,
        IFNULL(SUM(spu_average),0) spuAverage,
        IFNULL(SUM(fruit_gmv),0) fruitGmv,
        SUM(if(fruit_gmv !=0,1,0)) fruitMerchantCount,
        IFNULL(SUM(dairy_gmv),0) dairyGmv,
        SUM(if(dairy_gmv !=0,1,0)) dairyMerchantCount,
        IFNULL(SUM(non_dairy_gmv),0) nonDairyGmv,
        SUM(if(non_dairy_gmv != 0, 1, 0)) nonDairyCount,
        IFNULL(SUM(brand_gmv),0) brandGmv,
        SUM(if(brand_gmv != 0, 1, 0)) brandMerchantCount,
        IFNULL(SUM(agent_gmv),0) agentGoodsGmv,
        SUM(if(agent_gmv != 0, 1, 0)) agentMerchantCount
      FROM crm_merchant_day_gmv
      WHERE day_tag = #{queryTime}
      AND area_no IN
      <foreach collection="areaNo" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
      <if test="administrativeCityList != null and administrativeCityList.size() != 0">
          AND city in
          <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
              #{item}
          </foreach>
      </if>
  </select>
  <select id="selectByMid" resultMap="GmvResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_merchant_day_gmv
    where m_id = #{mId} AND day_tag = #{dayTag}
  </select>
  <select id="selectByAreaNo" resultMap="GmvResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_merchant_day_gmv
    where day_tag = #{dataTag}
    <if test="areaNo != null">
      and area_no = #{areaNo}
    </if>
    <if test="coreMerchantTag != null">
      AND core_merchant_tag = #{coreMerchantTag}
    </if>
    <if test="ids != null and ids.size() > 0">
      and m_id IN
      <foreach collection="ids" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    </if>
    order by merchant_total_gmv DESC
  </select>

  <select id="selectCoreMerchantById" resultType="net.summerfarm.crm.model.vo.ChangeMerchantVO">
    select m_id mId,merchant_name merchantName
    from crm_merchant_day_gmv
    where day_tag = #{dayTag} AND core_merchant_tag = 1 and bd_id = #{adminId}
  </select>

  <select id="selectAllMonthLiving" resultType="integer">
    SELECT COUNT(*) allMonthLiving
    FROM crm_merchant_day_gmv
    WHERE day_tag = #{queryTime} AND merchant_total_gmv > 0
    AND area_no IN
    <foreach collection="areaNo" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectByMids" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
    select SUM(merchant_total_gmv + reward_gmv) currentMonthGmv,
    SUM(brand_gmv) brandGmv,
    SUM(core_merchant_tag) coreMerchantNum,
    SUM(if(merchant_total_gmv > 0 ,1,0)) monthLiving
    from crm_merchant_day_gmv
    where day_tag = #{dayTag}
    <if test="ids != null and ids.size() > 0">
      and m_id IN
      <foreach collection="ids" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    </if>
    AND area_no IN
    <foreach collection="areaNo" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </select>
  <select id="selectLevelDistribution" resultType="net.summerfarm.crm.model.vo.MerchantLevelDistributionVO">
    SELECT m_id mId,merchant_name merchantName
    FROM crm_merchant_day_gmv
    WHERE day_tag = #{dayTag}
    AND bd_id = #{adminId}
    AND gmv_proportion + price_proportion = #{levelProportion}
  </select>
</mapper>