<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmBdDayGmvMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmBdDayGmv">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="total_gmv" jdbcType="DECIMAL" property="totalGmv" />
    <result column="single_gmv" jdbcType="DECIMAL" property="singleGmv" />
    <result column="vip_gmv" jdbcType="DECIMAL" property="vipGmv" />
    <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
    <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
    <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv" />
    <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv" />
    <result column="reward_gmv" jdbcType="DECIMAL" property="rewardGmv" />
    <result column="reward_amout" jdbcType="INTEGER" property="rewardAmout" />
    <result column="core_merchant_amout" jdbcType="INTEGER" property="coreMerchantAmout" />
    <result column="month_live_amout" jdbcType="INTEGER" property="monthLiveAmout" />
    <result column="pull_new_amout" jdbcType="INTEGER" property="pullNewAmout" />
    <result column="ordinary_pull_new_amount" jdbcType="INTEGER" property="ordinaryPullNewAmount" />
    <result column="no_order_register" jdbcType="INTEGER" property="noOrderRegister" />
    <result column="performance" jdbcType="DECIMAL" property="performance" />
    <result column="day_tag" jdbcType="INTEGER" property="dayTag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap id="GmvResultMap" type="net.summerfarm.crm.model.vo.AdminInfoVo">
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="total_gmv" jdbcType="DECIMAL" property="totalGmv" />
    <result column="single_gmv" jdbcType="DECIMAL" property="singleShopGmv" />
    <result column="vip_gmv" jdbcType="DECIMAL" property="vipGmv" />
    <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
    <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
    <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv" />
    <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv" />
    <result column="reward_gmv" jdbcType="DECIMAL" property="desGoodsSalesGmv" />
    <result column="reward_amout" jdbcType="INTEGER" property="desGoodsSales" />
    <result column="core_merchant_amout" jdbcType="INTEGER" property="coreMerchantNum" />
    <result column="month_live_amout" jdbcType="INTEGER" property="monthLiving" />
    <result column="pull_new_amout" jdbcType="INTEGER" property="newAdminNum" />
    <result column="performance" jdbcType="DECIMAL" property="performance" />
    <result column="ordinary_num" jdbcType="DECIMAL" property="ordinaryNum" />
    <result column="drop_in_visit_num" jdbcType="DECIMAL" property="dropInVisitNum" />
    <result column="efficient_num" jdbcType="DECIMAL" property="efficientNum" />
    <result column="worth_num" jdbcType="DECIMAL" property="worthNum" />
    <result column="ordinary_num" jdbcType="INTEGER" property="ordinaryNum" />
    <result column="drop_in_visit_num" jdbcType="INTEGER" property="dropInVisitNum" />
    <result column="efficient_num" jdbcType="INTEGER" property="efficientNum" />
    <result column="worth_num" jdbcType="INTEGER" property="worthNum" />
    <result column="category_award" jdbcType="DECIMAL" property="categoryAward" />
    <result column="core_merchant_card_level" jdbcType="DECIMAL" property="coreMerchantCardLevel" />
    <result column="delivery_gmv" jdbcType="DECIMAL" property="deliveryGmv" />
    <result column="order_shipped" jdbcType="INTEGER" property="orderShipped" />
    <result column="spu_average" jdbcType="DECIMAL" property="spuAverage" />
    <result column="category_multiply_gmv" jdbcType="DECIMAL" property="categoryMultiplyGmv" />
    <result column="single_month_live_num"  property="singleMonthLiveNum" />
    <result column="vip_month_live_num"  property="vipMonthLiveNum" />
    <result column="single_spu_average"  property="singleSpuAverage" />
    <result column="vip_spu_average"  property="vipSpuAverage" />
    <result column="ordinary_pull_new_amount" jdbcType="INTEGER" property="ordinaryPullNewAmount" />
    <result column="pull_new_amout" jdbcType="INTEGER" property="pullNewAmount" />
    <result column="no_order_register" jdbcType="INTEGER" property="noOrderRegister" />
    <result column="agent_goods_gmv" jdbcType="DECIMAL" property="agentGoodsGmv" />

    <result column="saas_total_gmv"  property="saasTotalGmv" />
    <result column="saas_depot_gmv"  property="saasDepotGmv" />
    <result column="saas_myself_gmv"  property="saasMyselfGmv" />
    <result column="saas_add_good_gvm"  property="saasAddGoodGvm" />
    <result column="saas_xianmu_gvm"  property="saasXianmuGvm" />

    <result column="saas_brand_count"  property="saasBrandCount" />
    <result column="saas_shop_register_count"  property="saasShopRegisterCount" />
    <result column="saas_order_shop_count"  property="saasOrderShopCount" />
    <result column="saas_brand_total_count"  property="saasBrandTotalCount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, admin_id, admin_name, total_gmv, brand_gmv,month_live_amout, pull_new_amout,spu_average,
    fruit_gmv, dairy_gmv, non_dairy_gmv,reward_gmv, reward_amout,
    single_gmv,single_month_live_num,single_spu_average,
    vip_gmv,vip_month_live_num,vip_spu_average,
    core_merchant_amout,performance,
    ordinary_num,drop_in_visit_num,efficient_num,worth_num,
    category_award,core_merchant_card_level,delivery_gmv,category_multiply_gmv,order_shipped,
    day_tag, update_time, create_time,ordinary_pull_new_amount,pull_new_amout,no_order_register,agent_goods_gmv,
    saas_total_gmv,saas_depot_gmv,saas_myself_gmv,saas_add_good_gvm,saas_xianmu_gvm,
    saas_brand_count,saas_shop_register_count,saas_order_shop_count,saas_brand_total_count
  </sql>
  <select id="selectByAdminId" resultMap="GmvResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bd_day_gmv
    where admin_id = #{adminId} AND day_tag = #{dayTag}
  </select>
  <select id="selectRankingListVO" resultType="net.summerfarm.crm.model.vo.RankingListVO">
    select admin_id bdId, admin_name bdName
    <if test="type==1">
      ,pull_new_amout rankNum
    </if>
    <if test="type==2">
      ,efficient_num rankNum
    </if>
    from crm_bd_day_gmv
    WHERE day_tag = #{dayTag}
    <if test="type==1">
      order by pull_new_amout desc
    </if>
    <if test="type==2">
      order by efficient_num desc
    </if>
    limit 10
  </select>

  <select id="listByAdminId" resultType="net.summerfarm.crm.model.domain.CrmBdTodayDayGmv">
    select admin_id adminId,
    admin_name adminName,
    total_gmv totalGmv,
    brand_gmv brandGmv,
    pull_new_amout pullNewAmount,
    fruit_gmv fruitGmv,
    dairy_gmv dairyGmv,
    non_dairy_gmv nonDairyGmv,
    delivery_gmv deliveryGmv,
    agent_goods_gmv agentGmv,
    order_merchant orderMerchant,
    reward_gmv rewardGmv,
    pull_new_amout pullNewAmount,
    efficient_num visitNum,
    reward_gmv rewardGmv
    from crm_bd_day_gmv
    where admin_id in
    <foreach collection="adminIds" item="adminId" open="(" close=")" separator=",">
      #{adminId}
    </foreach>
    and day_tag = #{dayTag}
  </select>
</mapper>