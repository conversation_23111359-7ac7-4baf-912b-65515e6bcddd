<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantMonthGmvMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantMonthGmv">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="merchant_total_gmv" jdbcType="DECIMAL" property="merchantTotalGmv" />
    <result column="distribution_gmv" jdbcType="DECIMAL" property="distributionGmv" />
    <result column="delivery_unit_price" jdbcType="DECIMAL" property="deliveryUnitPrice" />
    <result column="distribution_amout" jdbcType="INTEGER" property="distributionAmout" />
    <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv" />
    <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv" />
    <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv" />
    <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv" />
    <result column="core_merchant_tag" jdbcType="TINYINT" property="coreMerchantTag" />
    <result column="month_tag" jdbcType="TINYINT" property="monthTag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap id="GmvResultMap" type="net.summerfarm.crm.model.vo.PrivateSeaVO">
    <result column="merchant_name" jdbcType="VARCHAR" property="mname" />
    <result column="area_no" jdbcType="INTEGER" property="areaNo" />
    <result column="merchant_total_gmv" jdbcType="DECIMAL" property="thisMonthGmv" />
    <result column="distribution_gmv" jdbcType="DECIMAL" property="distributionGmv" />
    <result column="delivery_unit_price" jdbcType="DECIMAL" property="thisMonthDeliveryUnitPrice" />
    <result column="distribution_amout" jdbcType="INTEGER" property="distributionAmount" />
    <result column="core_merchant_tag" jdbcType="TINYINT" property="coreMerchantTag" />
    <result column="sku_num" property="thisMonthSkuCount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, m_id, merchant_name, area_no, merchant_total_gmv, distribution_gmv, delivery_unit_price, 
    distribution_amout, fruit_gmv, dairy_gmv, non_dairy_gmv, brand_gmv, core_merchant_tag, sku_num,
    month_tag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_merchant_month_gmv
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByInput" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
    SELECT SUM(merchant_total_gmv + reward_gmv) currentMonthGmv,
      SUM(brand_gmv) brandGmv,
      IFNULL(SUM(core_merchant_tag),0) coreMerchantNum
    FROM crm_merchant_month_gmv
    WHERE month_tag = #{queryTime}
    <if test="areaNo !=null and areaNo.size>0">
      AND area_no IN
      <foreach collection="areaNo" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    </if>
    <if test="administrativeCityList != null and administrativeCityList.size() != 0">
      AND city in
      <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="selectByMid" resultMap="GmvResultMap">
    select
    <include refid="Base_Column_List" />
    from crm_merchant_month_gmv
    where m_id = #{mId} AND month_tag = #{monthTag}
  </select>
  <select id="selectCoreMerchant" resultType="net.summerfarm.crm.model.vo.ChangeMerchantVO">
    select m_id mId,merchant_name merchantName
    from crm_merchant_month_gmv
    where core_merchant_tag = 1 and month_tag = #{monthTag}
    and bd_id = #{adminId}
  </select>
  <select id="selectAllMonthLiving" resultType="integer">
    SELECT COUNT(*) allMonthLiving
    FROM crm_merchant_month_gmv
    WHERE month_tag = #{queryTime} AND merchant_total_gmv > 0
    AND area_no IN
    <foreach collection="areaNo" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectByMids" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
    select SUM(merchant_total_gmv + reward_gmv) currentMonthGmv,
    SUM(brand_gmv) brandGmv,
    SUM(core_merchant_tag) coreMerchantNum,
    SUM(brand_gmv) brandGmv,
    SUM(if(merchant_total_gmv > 0 ,1,0)) monthLiving
    from crm_merchant_month_gmv
    where month_tag = #{monthTag}
    <if test="ids != null and ids.size() > 0">
      and m_id IN
      <foreach collection="ids" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
    </if>
    AND area_no IN
    <foreach collection="areaNo" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </select>
</mapper>