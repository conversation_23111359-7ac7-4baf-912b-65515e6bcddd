<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmCityMonthGmvMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCityMonthGmv">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="administrativeCity" column="administrative_city" jdbcType="VARCHAR"/>
            <result property="totalGmv" column="total_gmv" jdbcType="DECIMAL"/>
            <result property="pullNewAmount" column="pull_new_amount" jdbcType="INTEGER"/>
            <result property="coreMerchantAmount" column="core_merchant_amount" jdbcType="INTEGER"/>
            <result property="monthLiveAmount" column="month_live_amount" jdbcType="INTEGER"/>
            <result property="openMerchantMonthLive" column="open_merchant_month_live" jdbcType="INTEGER"/>
            <result property="privateMerchantMonthLive" column="private_merchant_month_live" jdbcType="INTEGER"/>
            <result property="openMerchantAmount" column="open_merchant_amount" jdbcType="INTEGER"/>
            <result property="privateMerchantAmount" column="private_merchant_amount" jdbcType="INTEGER"/>
            <result property="performance" column="performance" jdbcType="DECIMAL"/>
            <result property="monthTag" column="month_tag" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="teamTag" column="team_tag" jdbcType="TINYINT"/>
            <result property="operateMerchantNum" column="operate_merchant_num" jdbcType="INTEGER"/>
            <result property="visitNum" column="visit_num" jdbcType="INTEGER"/>
            <result property="escortNum" column="escort_num" jdbcType="INTEGER"/>
            <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
    </resultMap>

    <resultMap id="GmvResultMap" type="net.summerfarm.crm.model.vo.SalesDataVo">
        <result property="currentMonthGmv" column="total_gmv" jdbcType="DECIMAL"/>
        <result property="introducingNewReward" column="pull_new_amount" jdbcType="INTEGER"/>
        <result property="coreMerchantNum" column="core_merchant_amount" jdbcType="INTEGER"/>
        <result property="monthLiving" column="month_live_amount" jdbcType="INTEGER"/>
        <result property="openMonthLiving" column="open_merchant_month_live" jdbcType="INTEGER"/>
        <result property="privateMonthLiving" column="private_merchant_month_live" jdbcType="INTEGER"/>
        <result property="openMerchant" column="open_merchant_amount" jdbcType="INTEGER"/>
        <result property="privateMerchant" column="private_merchant_amount" jdbcType="INTEGER"/>
        <result property="operateMerchantNum" column="operate_merchant_num" jdbcType="INTEGER"/>
        <result property="visitNum" column="visit_num" jdbcType="INTEGER"/>
        <result property="escortNum" column="escort_num" jdbcType="INTEGER"/>
        <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
        <result property="ordinaryPullNewAmount" column="ordinary_pull_new_amount" jdbcType="INTEGER"/>
        <result property="privateEffectiveMonthLiving" column="private_merchant_effective_month_live" jdbcType="INTEGER"/>
        <result property="openEffectiveMonthLiving" column="open_merchant_effective_month_live" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,administrative_city,total_gmv,
        pull_new_amount,core_merchant_amount,month_live_amount,
        open_merchant_month_live,private_merchant_month_live,open_merchant_amount,
        private_merchant_amount,performance,month_tag,
        update_time,create_time,team_tag,
        operate_merchant_num,visit_num,escort_num,
        brand_gmv
    </sql>

    <select id="selectLastInfo" resultType="net.summerfarm.crm.model.vo.SalesDataVo">
        select
            IFNULL(SUM(total_gmv),0.00) lastMonthGmv,
            IFNULL(SUM(core_merchant_amount),0) lastMonthCoreMerchantNum
        from crm_city_month_gmv
        where  month_tag = #{queryTime}
        <if test="type != null">
            and team_tag = #{type}
        </if>
        <if test="administrativeCityList != null and administrativeCityList.size() >0">
            and administrative_city in
            <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByCity" resultMap="GmvResultMap">
        select
            IFNULL(SUM(total_gmv),0.00) total_gmv,
            IFNULL(SUM(pull_new_amount),0) pull_new_amount,
            IFNULL(SUM(core_merchant_amount),0) core_merchant_amount,
            IFNULL(SUM(month_live_amount),0) month_live_amount,
            IFNULL(SUM(open_merchant_month_live),0) open_merchant_month_live,
            IFNULL(SUM(private_merchant_month_live),0) private_merchant_month_live,
            IFNULL(SUM(open_merchant_amount),0) open_merchant_amount,
            IFNULL(SUM(private_merchant_amount),0) private_merchant_amount,
            IFNULL(SUM(operate_merchant_num),0) operate_merchant_num,
            IFNULL(SUM(visit_num),0) visit_num,
            IFNULL(SUM(escort_num),0) escort_num,
            IFNULL(SUM(brand_gmv),0) brand_gmv,
            IFNULL(SUM(ordinary_pull_new_amount),0) ordinaryPullNewAmount,
            IFNULL(SUM(private_merchant_effective_month_live),0) privateEffectiveMonthLiving,
            IFNULL(SUM(open_merchant_effective_month_live),0) openEffectiveMonthLiving
        from crm_city_month_gmv
        where month_tag = #{queryTime}
        <if test="type != null">
            and team_tag = #{type}
        </if>
        <if test="administrativeCityList != null and administrativeCityList.size() >0">
            and administrative_city in
            <foreach collection="administrativeCityList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
