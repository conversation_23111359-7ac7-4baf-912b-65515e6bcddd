<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantFutureDayGmvMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantFutureDayGmv">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="mId" column="m_id" jdbcType="BIGINT"/>
            <result property="areaNo" column="area_no" jdbcType="INTEGER"/>
            <result property="distributionGmv" column="distribution_gmv" jdbcType="DECIMAL"/>
            <result property="fruitGmv" column="fruit_gmv" jdbcType="DECIMAL"/>
            <result property="dairyGmv" column="dairy_gmv" jdbcType="DECIMAL"/>
            <result property="nonDairyGmv" column="non_dairy_gmv" jdbcType="DECIMAL"/>
            <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
            <result property="rewardGmv" column="reward_gmv" jdbcType="DECIMAL"/>
            <result property="spuNum" column="spu_num" jdbcType="INTEGER"/>
            <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" />
            <result property="createTime" column="create_time" />
            <result property="contactId" column="contact_id" jdbcType="INTEGER"/>
            <result property="deliveryTime" column="delivery_time" />
            <result property="bdId" column="bd_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,m_id,area_no,
        distribution_gmv,fruit_gmv,dairy_gmv,
        non_dairy_gmv,brand_gmv,reward_gmv,
        spu_num,day_tag,update_time,
        create_time,contact_id,delivery_time,
        bd_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_merchant_future_day_gmv
        where  id = #{id,jdbcType=BIGINT} 
    </select>
    <select id="selectByQuery" resultType="net.summerfarm.crm.model.vo.DeliveryMerchantVO">
        select id,m_id mId,distribution_gmv deliveryGmv,spu_num spuNum,contact_id contactId,estimated_income estimatedIncome,
               minimum_income_after_reaching_standard minimumIncomeAfterReachingStandard,maximum_income_after_reaching_standard maximumIncomeAfterReachingStandard,
               delivery_up_to_standard deliveryUpToStandard
        from crm_merchant_future_day_gmv
        where  day_tag = #{dayTag} and delivery_time = #{deliveryTime}
        <if test="deliveryMinimumGmv != null ">
            and distribution_gmv <![CDATA[ >= ]]> #{deliveryMinimumGmv}
        </if>
        <if test="deliveryHighestGmv != null">
            and distribution_gmv <![CDATA[ < ]]> #{deliveryHighestGmv}
        </if>
        <if test="spuMinimumNum != null">
            and spu_num <![CDATA[ >= ]]> #{spuMinimumNum}
        </if>
        <if test="spuHighestNum != null">
            and spu_num <![CDATA[ < ]]> #{spuHighestNum}
        </if>
        <if test="adminId != null">
            and bd_id = #{adminId}
        </if>
        <if test="deliveryUpToStandard != null">
            and delivery_up_to_standard = #{deliveryUpToStandard}
        </if>
        <if test="areaNo != null">
            and area_no = #{areaNo}
        </if>
        <if test="province != null and province != ''">
            and province =#{province,jdbcType=VARCHAR}
        </if>
        <if test="city != null and city != ''">
            and city=#{city,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area != ''">
            and area=#{area,jdbcType=VARCHAR}
        </if>
        <if test="orderBy !=null">
            <if test="orderBy==0">
                order by if(delivery_up_to_standard=0,estimated_income,100000) asc
            </if>
            <if test="orderBy==1">
                order by if(delivery_up_to_standard=0,estimated_income,-1) desc
            </if>
            <if test="orderBy==2">
                order by if(delivery_up_to_standard=0,minimum_income_after_reaching_standard,100000) asc
            </if>
            <if test="orderBy==3">
                order by if(delivery_up_to_standard=0,minimum_income_after_reaching_standard,-1)desc
            </if>
        </if>
    </select>


</mapper>
