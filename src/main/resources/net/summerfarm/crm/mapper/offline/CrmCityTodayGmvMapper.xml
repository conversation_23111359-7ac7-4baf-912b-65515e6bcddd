<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmCityTodayGmvMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCityTodayGmv">
        <!--@mbg.generated-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="order_gmv" jdbcType="DECIMAL" property="orderGmv"/>
        <result column="delivery_gmv" jdbcType="DECIMAL" property="deliveryGmv"/>
        <result column="order_merchant" jdbcType="INTEGER" property="orderMerchant"/>
        <result column="fruit_gmv" jdbcType="DECIMAL" property="fruitGmv"/>
        <result column="dairy_gmv" jdbcType="DECIMAL" property="dairyGmv"/>
        <result column="non_dairy_gmv" jdbcType="DECIMAL" property="nonDairyGmv"/>
        <result column="brand_gmv" jdbcType="DECIMAL" property="brandGmv"/>
        <result column="agent_gmv" jdbcType="DECIMAL" property="agentGmv"/>
        <result column="reward_gmv" jdbcType="DECIMAL" property="rewardGmv"/>
        <result column="pull_new_amount" jdbcType="INTEGER" property="pullNewAmount"/>
        <result column="visit_num" jdbcType="INTEGER" property="visitNum"/>
        <result column="day_tag" jdbcType="INTEGER" property="dayTag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        create_time,
        update_time,
        city,
        area,
        order_gmv,
        delivery_gmv,
        order_merchant,
        fruit_gmv,
        dairy_gmv,
        non_dairy_gmv,
        brand_gmv,
        agent_gmv,
        reward_gmv,
        pull_new_amount,
        visit_num,
        day_tag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from crm_city_today_gmv
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from crm_city_today_gmv
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmCityTodayGmv"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into crm_city_today_gmv (create_time, update_time,
                                        city, area, order_gmv,
                                        delivery_gmv, order_merchant, fruit_gmv,
                                        dairy_gmv, non_dairy_gmv, brand_gmv,
                                        agent_gmv, reward_gmv, pull_new_amount,
                                        visit_num, day_tag)
        values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{orderGmv,jdbcType=DECIMAL},
                #{deliveryGmv,jdbcType=DECIMAL}, #{orderMerchant,jdbcType=INTEGER}, #{fruitGmv,jdbcType=DECIMAL},
                #{dairyGmv,jdbcType=DECIMAL}, #{nonDairyGmv,jdbcType=DECIMAL}, #{brandGmv,jdbcType=DECIMAL},
                #{agentGmv,jdbcType=DECIMAL}, #{rewardGmv,jdbcType=DECIMAL}, #{pullNewAmount,jdbcType=INTEGER},
                #{visitNum,jdbcType=INTEGER}, #{dayTag,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.summerfarm.crm.model.domain.CrmCityTodayGmv" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into crm_city_today_gmv
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="city != null and city != ''">
                city,
            </if>
            <if test="area != null and area != ''">
                area,
            </if>
            <if test="orderGmv != null">
                order_gmv,
            </if>
            <if test="deliveryGmv != null">
                delivery_gmv,
            </if>
            <if test="orderMerchant != null">
                order_merchant,
            </if>
            <if test="fruitGmv != null">
                fruit_gmv,
            </if>
            <if test="dairyGmv != null">
                dairy_gmv,
            </if>
            <if test="nonDairyGmv != null">
                non_dairy_gmv,
            </if>
            <if test="brandGmv != null">
                brand_gmv,
            </if>
            <if test="agentGmv != null">
                agent_gmv,
            </if>
            <if test="rewardGmv != null">
                reward_gmv,
            </if>
            <if test="pullNewAmount != null">
                pull_new_amount,
            </if>
            <if test="visitNum != null">
                visit_num,
            </if>
            <if test="dayTag != null">
                day_tag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="city != null and city != ''">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null and area != ''">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="orderGmv != null">
                #{orderGmv,jdbcType=DECIMAL},
            </if>
            <if test="deliveryGmv != null">
                #{deliveryGmv,jdbcType=DECIMAL},
            </if>
            <if test="orderMerchant != null">
                #{orderMerchant,jdbcType=INTEGER},
            </if>
            <if test="fruitGmv != null">
                #{fruitGmv,jdbcType=DECIMAL},
            </if>
            <if test="dairyGmv != null">
                #{dairyGmv,jdbcType=DECIMAL},
            </if>
            <if test="nonDairyGmv != null">
                #{nonDairyGmv,jdbcType=DECIMAL},
            </if>
            <if test="brandGmv != null">
                #{brandGmv,jdbcType=DECIMAL},
            </if>
            <if test="agentGmv != null">
                #{agentGmv,jdbcType=DECIMAL},
            </if>
            <if test="rewardGmv != null">
                #{rewardGmv,jdbcType=DECIMAL},
            </if>
            <if test="pullNewAmount != null">
                #{pullNewAmount,jdbcType=INTEGER},
            </if>
            <if test="visitNum != null">
                #{visitNum,jdbcType=INTEGER},
            </if>
            <if test="dayTag != null">
                #{dayTag,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmCityTodayGmv">
        <!--@mbg.generated-->
        update crm_city_today_gmv
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="city != null and city != ''">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null and area != ''">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="orderGmv != null">
                order_gmv = #{orderGmv,jdbcType=DECIMAL},
            </if>
            <if test="deliveryGmv != null">
                delivery_gmv = #{deliveryGmv,jdbcType=DECIMAL},
            </if>
            <if test="orderMerchant != null">
                order_merchant = #{orderMerchant,jdbcType=INTEGER},
            </if>
            <if test="fruitGmv != null">
                fruit_gmv = #{fruitGmv,jdbcType=DECIMAL},
            </if>
            <if test="dairyGmv != null">
                dairy_gmv = #{dairyGmv,jdbcType=DECIMAL},
            </if>
            <if test="nonDairyGmv != null">
                non_dairy_gmv = #{nonDairyGmv,jdbcType=DECIMAL},
            </if>
            <if test="brandGmv != null">
                brand_gmv = #{brandGmv,jdbcType=DECIMAL},
            </if>
            <if test="agentGmv != null">
                agent_gmv = #{agentGmv,jdbcType=DECIMAL},
            </if>
            <if test="rewardGmv != null">
                reward_gmv = #{rewardGmv,jdbcType=DECIMAL},
            </if>
            <if test="pullNewAmount != null">
                pull_new_amount = #{pullNewAmount,jdbcType=INTEGER},
            </if>
            <if test="visitNum != null">
                visit_num = #{visitNum,jdbcType=INTEGER},
            </if>
            <if test="dayTag != null">
                day_tag = #{dayTag,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmCityTodayGmv">
        <!--@mbg.generated-->
        update crm_city_today_gmv
        set create_time     = #{createTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            city            = #{city,jdbcType=VARCHAR},
            area            = #{area,jdbcType=VARCHAR},
            order_gmv       = #{orderGmv,jdbcType=DECIMAL},
            delivery_gmv    = #{deliveryGmv,jdbcType=DECIMAL},
            order_merchant  = #{orderMerchant,jdbcType=INTEGER},
            fruit_gmv       = #{fruitGmv,jdbcType=DECIMAL},
            dairy_gmv       = #{dairyGmv,jdbcType=DECIMAL},
            non_dairy_gmv   = #{nonDairyGmv,jdbcType=DECIMAL},
            brand_gmv       = #{brandGmv,jdbcType=DECIMAL},
            agent_gmv       = #{agentGmv,jdbcType=DECIMAL},
            reward_gmv      = #{rewardGmv,jdbcType=DECIMAL},
            pull_new_amount = #{pullNewAmount,jdbcType=INTEGER},
            visit_num       = #{visitNum,jdbcType=INTEGER},
            day_tag         = #{dayTag,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listByCity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_city_today_gmv
        where (city, area) in
        <foreach collection="cityList" item="list" open="(" close=")" separator=",">
            (#{list.city}, #{list.area})
        </foreach>
        and day_tag = #{dayTag}
        group by city
    </select>

    <select id="selectByCities" resultType="net.summerfarm.crm.model.vo.saledata.SalesAreaDataVo">
        select ifnull(sum(order_gmv), 0)       orderGmv,
               ifnull(sum(delivery_gmv), 0)    deliveryGmv,
               ifnull(sum(order_merchant), 0)  orderMerchant,
               ifnull(sum(fruit_gmv), 0)       fruitGmv,
               ifnull(sum(dairy_gmv), 0)       dairyGmv,
               ifnull(sum(non_dairy_gmv), 0)   nonDairyGmv,
               ifnull(sum(brand_gmv), 0)       brandGmv,
               ifnull(sum(agent_gmv), 0)       agentGmv,
               ifnull(sum(reward_gmv), 0)      rewardGmv,
               ifnull(sum(pull_new_amount), 0) pullNewAmount,
               ifnull(sum(visit_num), 0)       visitNum
        from crm_city_today_gmv
        where (city, area) in
        <foreach collection="cityList" item="list" open="(" close=")" separator=",">
            (#{list.city}, #{list.area})
        </foreach>
        and day_tag = #{dayTag}
    </select>
</mapper>