<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CustMtdPerformanceMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CustMtdPerformance">
    <!--@mbg.generated-->
    <!--@Table cust_mtd_performance-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="last_bd_name" jdbcType="VARCHAR" property="lastBdName" />
    <result column="last_bd_id" jdbcType="BIGINT" property="lastBdId" />
    <result column="bd_region" jdbcType="VARCHAR" property="bdRegion" />
    <result column="bd_work_zone" jdbcType="VARCHAR" property="bdWorkZone" />
    <result column="cust_id" jdbcType="BIGINT" property="custId" />
    <result column="last_cust_name" jdbcType="VARCHAR" property="lastCustName" />
    <result column="cust_dlv_type" jdbcType="VARCHAR" property="custDlvType" />
    <result column="total_score_num" jdbcType="DOUBLE" property="totalScoreNum" />
    <result column="bd_performance_rate" jdbcType="DOUBLE" property="bdPerformanceRate" />
    <result column="dlv_cust_cnt" jdbcType="BIGINT" property="dlvCustCnt" />
    <result column="cust_comm_amt" jdbcType="DECIMAL" property="custCommAmt" />
    <result column="dlv_ori_amt" jdbcType="DECIMAL" property="dlvOriAmt" />
    <result column="dlv_real_amt" jdbcType="DECIMAL" property="dlvRealAmt" />
    <result column="item_profit_amt" jdbcType="DECIMAL" property="itemProfitAmt" />
    <result column="dlv_real_amt_at" jdbcType="DECIMAL" property="dlvRealAmtAt" />
    <result column="dlv_real_amt_expo" jdbcType="DECIMAL" property="dlvRealAmtExpo" />
    <result column="dlv_real_amt_profit" jdbcType="DECIMAL" property="dlvRealAmtProfit" />
    <result column="dlv_real_amt_normal" jdbcType="DECIMAL" property="dlvRealAmtNormal" />
    <result column="dlv_real_amt_fruit" jdbcType="DECIMAL" property="dlvRealAmtFruit" />
    <result column="cate_group_score_num" jdbcType="DOUBLE" property="cateGroupScoreNum" />
    <result column="dlv_spu_cnt" jdbcType="BIGINT" property="dlvSpuCnt" />
    <result column="more_than_spu_cnt" jdbcType="BIGINT" property="moreThanSpuCnt" />
    <result column="more_than_spu_comm" jdbcType="DECIMAL" property="moreThanSpuComm" />
    <result column="more_than_spu_cust" jdbcType="BIGINT" property="moreThanSpuCust" />
    <result column="total_comm_amt" jdbcType="DECIMAL" property="totalCommAmt" />
    <result column="is_test_bd" jdbcType="VARCHAR" property="isTestBd" />
    <result column="ds" jdbcType="VARCHAR" property="ds" />
    <result column="cust_value_lable" jdbcType="VARCHAR" property="custValueLable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, update_time, create_time, last_bd_name, last_bd_id, bd_region, bd_work_zone, 
    cust_id, last_cust_name, cust_dlv_type, total_score_num, bd_performance_rate, dlv_cust_cnt, 
    cust_comm_amt, dlv_ori_amt, dlv_real_amt, item_profit_amt, dlv_real_amt_at, dlv_real_amt_expo, 
    dlv_real_amt_profit, dlv_real_amt_normal, dlv_real_amt_fruit, cate_group_score_num, 
    dlv_spu_cnt, more_than_spu_cnt, more_than_spu_comm, more_than_spu_cust, total_comm_amt, 
    is_test_bd, ds, cust_value_lable
  </sql>
</mapper>