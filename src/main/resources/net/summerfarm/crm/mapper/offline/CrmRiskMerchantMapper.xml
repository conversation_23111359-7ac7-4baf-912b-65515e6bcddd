<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmRiskMerchantMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmRiskMerchant">
    <id column="id" property="id" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="m_id" property="mId" />
    <result column="trigger_occasions" property="triggerOccasions" />
    <result column="trigger_condition" property="triggerCondition" />
    <result column="trigger_classification" property="triggerClassification" />
    <result column="similar_m_id" property="similarMId" />
    <result column="day_tag" property="dayTag" />
    <result column="merchant_size" property="merchantSize" />
    <result column="similar_size" property="similarSize" />
    <result column="similar_name" property="similarName" />
    <result column="similar_phone" property="similarPhone" />
    <result column="source_type" property="sourceType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, m_id, trigger_occasions, trigger_condition, trigger_classification, 
    similar_m_id, day_tag, merchant_size, similar_size, similar_name, similar_phone, 
    source_type
  </sql>

  <select id="listByDayTag" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from crm_risk_merchant
    where day_tag = #{dayTag}
  </select>
</mapper>