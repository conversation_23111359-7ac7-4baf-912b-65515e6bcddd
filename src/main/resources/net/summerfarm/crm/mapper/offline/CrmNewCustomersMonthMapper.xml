<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmNewCustomersMonthMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmNewCustomersMonth">
    <!--@mbg.generated-->
    <!--@Table crm_new_customers_month-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="bd_id" jdbcType="BIGINT" property="bdId" />
    <result column="day_tag" jdbcType="INTEGER" property="dayTag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, m_id, bd_id, day_tag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from crm_new_customers_month
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from crm_new_customers_month
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmNewCustomersMonth" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into crm_new_customers_month (create_time, update_time, m_id, 
      bd_id, day_tag)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{mId,jdbcType=BIGINT}, 
      #{bdId,jdbcType=BIGINT}, #{dayTag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.crm.model.domain.CrmNewCustomersMonth" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into crm_new_customers_month
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mId != null">
        m_id,
      </if>
      <if test="bdId != null">
        bd_id,
      </if>
      <if test="dayTag != null">
        day_tag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        #{bdId,jdbcType=BIGINT},
      </if>
      <if test="dayTag != null">
        #{dayTag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.crm.model.domain.CrmNewCustomersMonth">
    <!--@mbg.generated-->
    update crm_new_customers_month
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="bdId != null">
        bd_id = #{bdId,jdbcType=BIGINT},
      </if>
      <if test="dayTag != null">
        day_tag = #{dayTag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.crm.model.domain.CrmNewCustomersMonth">
    <!--@mbg.generated-->
    update crm_new_customers_month
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      m_id = #{mId,jdbcType=BIGINT},
      bd_id = #{bdId,jdbcType=BIGINT},
      day_tag = #{dayTag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByBdId" resultType="Long">
    select m_id
    from crm_new_customers_month
    where bd_id = #{bdId}
  </select>
</mapper>