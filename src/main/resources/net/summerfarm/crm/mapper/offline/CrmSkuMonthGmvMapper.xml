<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmSkuMonthGmvMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmSkuMonthGmv">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="gmv" column="gmv" jdbcType="DECIMAL"/>
            <result property="salesVolume" column="sales_volume" jdbcType="OTHER"/>
            <result property="merchantNum" column="merchant_num" jdbcType="OTHER"/>
            <result property="selfSupport" column="self_support" jdbcType="TINYINT"/>
            <result property="areaNo" column="area_no" jdbcType="OTHER"/>
            <result property="monthTag" column="month_tag" jdbcType="OTHER"/>
    </resultMap>
    <resultMap id="GmvResultMap" type="net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="gmv" column="gmv" jdbcType="DECIMAL"/>
        <result property="salesVolume" column="sales_volume" jdbcType="OTHER"/>
        <result property="merchantNum" column="merchant_num" jdbcType="OTHER"/>
        <result property="selfSupport" column="self_support" jdbcType="TINYINT"/>
        <result property="areaNo" column="area_no" jdbcType="OTHER"/>
        <result property="monthTag" column="month_tag" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        sku,gmv,sales_volume,
        merchant_num,self_support,area_no,
        month_tag
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_sku_month_gmv
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <select id="selectByQuery" resultMap="GmvResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_sku_month_gmv
        where month_tag = #{monthTag}
          and area_no = #{areaNo}
        <if test="sku != null">
            and sku = #{sku}
        </if>
        <if test="province != null and province != ''">
            and province = #{province,jdbcType=VARCHAR}
        </if>
        <if test="city != null and city != ''">
            and city = #{city,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area != ''">
            and area = #{area,jdbcType=VARCHAR}
        </if>
        <if test="skuList != null and skuList.size() > 0">
            and sku in
            <foreach collection="skuList" open="(" close=") " separator="," item="item">
                #{item}
            </foreach>
        </if>
        ORDER BY id
    </select>

</mapper>
