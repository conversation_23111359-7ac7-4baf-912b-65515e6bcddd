<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.SaasOrderCustMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.SaasOrderCust">
        <!--@mbg.generated-->
        <!--@Table saas_order_cust-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="order_time" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="payable_price" jdbcType="DECIMAL" property="payablePrice"/>
        <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee"/>
        <result column="order_address_contact_name" jdbcType="VARCHAR" property="orderAddressContactName"/>
        <result column="order_address_contact_phone" jdbcType="VARCHAR" property="orderAddressContactPhone"/>
        <result column="order_address_province" jdbcType="VARCHAR" property="orderAddressProvince"/>
        <result column="order_address_city" jdbcType="VARCHAR" property="orderAddressCity"/>
        <result column="order_address_area" jdbcType="VARCHAR" property="orderAddressArea"/>
        <result column="order_address" jdbcType="VARCHAR" property="orderAddress"/>
        <result column="status" jdbcType="BIGINT" property="status"/>
        <result column="status_text" jdbcType="VARCHAR" property="statusText"/>
        <result column="m_size" jdbcType="VARCHAR" property="mSize"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="mname" jdbcType="VARCHAR" property="mname"/>
        <result column="mcontact" jdbcType="VARCHAR" property="mcontact"/>
        <result column="mphone" jdbcType="VARCHAR" property="mphone"/>
        <result column="sub_account_contact" jdbcType="VARCHAR" property="subAccountContact"/>
        <result column="sub_account_phone" jdbcType="VARCHAR" property="subAccountPhone"/>
        <result column="bd_id" jdbcType="BIGINT" property="bdId"/>
        <result column="bd_name" jdbcType="VARCHAR" property="bdName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, update_time, create_time, order_time, pay_time, delivery_time, order_no, payable_price,
        delivery_fee, order_address_contact_name, order_address_contact_phone, order_address_province,
        order_address_city, order_address_area, order_address, `status`, status_text, m_size,
        province, city, district, mname, mcontact, mphone, sub_account_contact, sub_account_phone,
        bd_id, bd_name
    </sql>

    <!--auto generated by MybatisCodeHelper on 2024-10-23-->
    <select id="selectOneByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from saas_order_cust
        where order_no=#{orderNo,jdbcType=VARCHAR}
    </select>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from saas_order_cust
        <where>
            <if test="query.status != null">
                and status=#{query.status,jdbcType=BIGINT}
            </if>
            <if test="query.startTime != null">
                and order_time &gt;= #{query.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.endTime != null">
                and order_time &lt;= #{query.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="query.deliveryTime != null">
                and date(delivery_time) = date(#{query.deliveryTime,jdbcType=TIMESTAMP})
            </if>
            <if test="query.province != null">
                and province like concat(#{query.province,jdbcType=VARCHAR},'%')
            </if>
            <if test="query.city != null">
                and city=#{query.city,jdbcType=VARCHAR}
            </if>
            <if test="query.area != null">
                and district=#{query.area,jdbcType=VARCHAR}
            </if>
            <if test="query.mname != null">
                and mname like concat('%',#{query.mname,jdbcType=VARCHAR},'%')
            </if>
            <if test="bdIdCollection != null and bdIdCollection.size() > 0">
                and bd_id in
                <foreach item="item" index="index" collection="bdIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        order by order_time desc
    </select>
</mapper>