<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.SaasOrderItemMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.SaasOrderItem">
        <!--@mbg.generated-->
        <!--@Table saas_order_item-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
        <result column="spu_id" jdbcType="BIGINT" property="spuId"/>
        <result column="spu_name" jdbcType="VARCHAR" property="spuName"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="sku_cnt" jdbcType="BIGINT" property="skuCnt"/>
        <result column="payable_price" jdbcType="DECIMAL" property="payablePrice"/>
        <result column="order_item_status" jdbcType="BIGINT" property="orderItemStatus"/>
        <result column="picture_path" jdbcType="VARCHAR" property="picturePath"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, update_time, create_time, order_no, sku_id, spu_id, spu_name, weight, sku_cnt,
        payable_price, order_item_status, picture_path
    </sql>

    <!--auto generated by MybatisCodeHelper on 2024-10-23-->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from saas_order_item
        where order_no=#{orderNo,jdbcType=VARCHAR}
    </select>
</mapper>