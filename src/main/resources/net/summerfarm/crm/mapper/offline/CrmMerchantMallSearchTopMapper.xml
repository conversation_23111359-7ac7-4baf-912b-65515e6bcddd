<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantMallSearchTopMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantMallSearchTop">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="merchantId" column="merchant_id" jdbcType="BIGINT"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="searchNum" column="search_num" jdbcType="OTHER"/>
            <result property="dayTag" column="day_tag" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        merchant_id,product_name,search_num,
        day_tag
    </sql>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_merchant_mall_search_top
        where  day_tag = #{dayTag} and merchant_id = #{merchantId}
        order by search_num desc
    </select>
</mapper>
