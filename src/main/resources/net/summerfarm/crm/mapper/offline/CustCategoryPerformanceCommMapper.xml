<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CustCategoryPerformanceCommMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CustCategoryPerformanceComm">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="bdId" column="bd_id" jdbcType="BIGINT"/>
            <result property="bdName" column="bd_name" jdbcType="VARCHAR"/>
            <result property="custId" column="cust_id" jdbcType="BIGINT"/>
            <result property="custName" column="cust_name" jdbcType="VARCHAR"/>
            <result property="custType" column="cust_type" jdbcType="VARCHAR"/>
            <result property="custTypeDetail" column="cust_type_detail" jdbcType="VARCHAR"/>
            <result property="spuGroup" column="spu_group" jdbcType="VARCHAR"/>
            <result property="bigSkuCnt" column="big_sku_cnt" jdbcType="DOUBLE"/>
            <result property="dlvRealCntToday" column="dlv_real_cnt_today" jdbcType="DOUBLE"/>
            <result property="dlvOrderCntToday" column="dlv_order_cnt_today" jdbcType="DOUBLE"/>
            <result property="dlvOtherCntToday" column="dlv_other_cnt_today" jdbcType="DOUBLE"/>
            <result property="monthDlvRealCntToday" column="month_dlv_real_cnt_today" jdbcType="DOUBLE"/>
            <result property="categoryCommAmt" column="category_comm_amt" jdbcType="DECIMAL"/>
            <result property="oldCustComm" column="old_cust_comm" jdbcType="DECIMAL"/>
            <result property="newCustComm" column="new_cust_comm" jdbcType="DECIMAL"/>
            <result property="monthCategoryCustComm" column="month_category_cust_comm" jdbcType="DECIMAL"/>
            <result property="isDlvPayment" column="is_dlv_payment" jdbcType="INTEGER"/>
            <result property="isComplete" column="is_complete" jdbcType="INTEGER"/>
            <result property="custCnt" column="cust_cnt" jdbcType="INTEGER"/>
            <result property="mtdTxnSkuCnt" column="mtd_txn_sku_cnt" jdbcType="DOUBLE"/>
            <result property="todayTxnSkuCnt" column="today_txn_sku_cnt" jdbcType="DOUBLE"/>
            <result property="rewardPerItem" column="reward_per_item" jdbcType="DECIMAL"/>
            <result property="ds" column="ds" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        bd_id,bd_name,cust_id,
        cust_name,cust_type,cust_type_detail,
        spu_group,mtd_dlv_sku_cnt,today_dlv_sku_cnt,
        today_trd_sku_cnt,other_dlv_sku_cnt,total_dlv_sku_cnt,
        category_comm_amt,old_cust_comm,new_cust_comm,
        total_category_comm_amt,is_dlv_payment,is_completed,ds
    </sql>

    <select id="summaryByBdIds" resultMap="BaseResultMap">
        SELECT
            `bd_id`,
            MAX(`bd_name`) AS bd_name,
            COUNT(DISTINCT `cust_id`) AS cust_cnt,
            SUM(category_comm_amt) AS category_comm_amt,
            SUM(month_category_cust_comm) AS month_category_cust_comm,
            ROUND(SUM(IF(is_dlv_payment = 1, big_sku_cnt, 0)), 2) AS big_sku_cnt,
            ROUND(SUM(IF(is_dlv_payment = 1, month_dlv_real_cnt_today, 0)), 2) AS month_dlv_real_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 1, dlv_real_cnt_today, 0)), 2) AS dlv_real_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 1, dlv_order_cnt_today, 0)), 2) AS dlv_order_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 1, dlv_other_cnt_today, 0)), 2) AS dlv_other_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 0, big_sku_cnt, 0)), 2) AS mtd_txn_sku_cnt,
            ROUND(SUM(IF(is_dlv_payment = 0, dlv_order_cnt_today, 0)), 2) AS today_txn_sku_cnt
        FROM `cust_category_performance_comm`
        WHERE `bd_id` IN
        <foreach collection="bdIds" item="bdId" open="(" separator="," close=")">
            #{bdId}
        </foreach>
        <if test="custType != null and custType != ''">
            AND `cust_type` = #{custType}
        </if>
        <if test="spuGroups != null and spuGroups.size() > 0">
            AND `spu_group` IN
            <foreach collection="spuGroups" item="spuGroup" open="(" separator="," close=")">
                #{spuGroup}
            </foreach>
        </if>
        GROUP BY `bd_id`
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'customerCount'">
                    cust_cnt
                </when>
                <when test="sortField == 'rewardAmount'">
                    category_comm_amt
                </when>
                <when test="sortField == 'fulfillmentCount'">
                    big_sku_cnt
                </when>
                <when test="sortField == 'transactionCount'">
                    mtd_txn_sku_cnt
                </when>
                <otherwise>
                    bd_id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

    <select id="summaryByCustomer" resultMap="BaseResultMap">
        SELECT
            cust_id,
            MAX(`cust_name`) AS cust_name,
            SUM(category_comm_amt) AS category_comm_amt,
            SUM(month_category_cust_comm) AS month_category_cust_comm,
            ROUND(SUM(IF(is_dlv_payment = 1, big_sku_cnt, 0)), 2) AS big_sku_cnt,
            ROUND(SUM(IF(is_dlv_payment = 1, month_dlv_real_cnt_today, 0)), 2) AS month_dlv_real_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 1, dlv_real_cnt_today, 0)), 2) AS dlv_real_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 1, dlv_order_cnt_today, 0)), 2) AS dlv_order_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 1, dlv_other_cnt_today, 0)), 2) AS dlv_other_cnt_today,
            ROUND(SUM(IF(is_dlv_payment = 0, big_sku_cnt, 0)), 2) AS mtd_txn_sku_cnt,
            ROUND(SUM(IF(is_dlv_payment = 0, dlv_order_cnt_today, 0)), 2) AS today_txn_sku_cnt
        FROM `cust_category_performance_comm`
        WHERE `bd_id` = #{bdId,jdbcType=BIGINT}
        <if test="custType != null and custType != ''">
            AND `cust_type` = #{custType}
        </if>
        <if test="mname != null and mname != ''">
            AND `cust_name` LIKE CONCAT('%', #{mname,jdbcType=VARCHAR}, '%')
        </if>
        <if test="spuGroups != null and spuGroups.size() > 0">
            AND `spu_group` IN
            <foreach collection="spuGroups" item="spuGroup" open="(" separator="," close=")">
                #{spuGroup}
            </foreach>
        </if>
        GROUP BY `cust_id`
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'rewardAmount'">
                    category_comm_amt
                </when>
                <when test="sortField == 'fulfillmentCount'">
                    big_sku_cnt
                </when>
                <when test="sortField == 'transactionCount'">
                    mtd_txn_sku_cnt
                </when>
                <otherwise>
                    cust_id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

    <select id="listByCustomer" resultMap="BaseResultMap">
        SELECT
        cust_id,
        cust_name,
        cust_type,
        spu_group,
        category_comm_amt,
        month_category_cust_comm,
        ROUND(IF(big_sku_cnt > 0, category_comm_amt / big_sku_cnt, 0), 2) AS reward_per_item,
        is_dlv_payment,
        ROUND(IF(is_dlv_payment = 1, big_sku_cnt, 0), 2) AS big_sku_cnt,
        ROUND(IF(is_dlv_payment = 1, month_dlv_real_cnt_today, 0), 2) AS month_dlv_real_cnt_today,
        ROUND(IF(is_dlv_payment = 1, dlv_real_cnt_today, 0), 2) AS dlv_real_cnt_today,
        ROUND(IF(is_dlv_payment = 1, dlv_order_cnt_today, 0), 2) AS dlv_order_cnt_today,
        ROUND(IF(is_dlv_payment = 1, dlv_other_cnt_today, 0), 2) AS dlv_other_cnt_today,
        ROUND(IF(is_dlv_payment = 0, big_sku_cnt, 0), 2) AS mtd_txn_sku_cnt,
        ROUND(IF(is_dlv_payment = 0, dlv_order_cnt_today, 0), 2) AS today_txn_sku_cnt
        FROM cust_category_performance_comm
        WHERE cust_id = #{custId}
        AND bd_id = #{bdId}
        <if test="spuGroups != null and spuGroups.size() > 0">
            AND spu_group IN
            <foreach collection="spuGroups" item="spuGroup" open="(" separator="," close=")">
                #{spuGroup}
            </foreach>
        </if>
        <if test="custType != null and custType != ''">
            AND cust_type = #{custType}
        </if>
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'rewardAmount'">
                    category_comm_amt
                </when>
                <when test="sortField == 'fulfillmentCount'">
                    big_sku_cnt
                </when>
                <when test="sortField == 'transactionCount'">
                    mtd_txn_sku_cnt
                </when>
                <otherwise>
                    id
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

    <select id="summaryBySpuGroup" resultMap="BaseResultMap">
        SELECT
        spu_group,
        COUNT(DISTINCT `cust_id`) AS cust_cnt,
        SUM(category_comm_amt) AS category_comm_amt,
        SUM(month_category_cust_comm) AS month_category_cust_comm,
        MAX(is_dlv_payment) AS is_dlv_payment,
        ROUND(SUM(IF(is_dlv_payment = 1, big_sku_cnt, 0)), 2) AS big_sku_cnt,
        ROUND(SUM(IF(is_dlv_payment = 1, month_dlv_real_cnt_today, 0)), 2) AS month_dlv_real_cnt_today,
        ROUND(SUM(IF(is_dlv_payment = 1, dlv_real_cnt_today, 0)), 2) AS dlv_real_cnt_today,
        ROUND(SUM(IF(is_dlv_payment = 1, dlv_order_cnt_today, 0)), 2) AS dlv_order_cnt_today,
        ROUND(SUM(IF(is_dlv_payment = 1, dlv_other_cnt_today, 0)), 2) AS dlv_other_cnt_today,
        ROUND(SUM(IF(is_dlv_payment = 0, big_sku_cnt, 0)), 2) AS mtd_txn_sku_cnt,
        ROUND(SUM(IF(is_dlv_payment = 0, dlv_order_cnt_today, 0)), 2) AS today_txn_sku_cnt
        FROM cust_category_performance_comm
        WHERE `bd_id` IN
        <foreach collection="bdIds" item="bdId" open="(" separator="," close=")">
            #{bdId}
        </foreach>
        <if test="custType != null and custType != ''">
            AND cust_type = #{custType}
        </if>
        GROUP BY spu_group
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
               <when test="sortField == 'customerCount'">
                    cust_cnt
                </when>
                <when test="sortField == 'rewardAmount'">
                    category_comm_amt
                </when>
                <when test="sortField == 'fulfillmentCount'">
                    big_sku_cnt
                </when>
                <when test="sortField == 'transactionCount'">
                    mtd_txn_sku_cnt
                </when>
                <otherwise>
                    spu_group
                </otherwise>
            </choose>
            <if test="sortDirection != null">
                ${sortDirection.name()}
            </if>
        </if>
    </select>

    <select id="listSpuGroup" resultType="java.lang.String">
        SELECT DISTINCT spu_group
        FROM cust_category_performance_comm
        WHERE bd_id IN
        <foreach collection="bdIds" item="bdId" open="(" separator="," close=")">
            #{bdId}
        </foreach>
    </select>

    <select id="listCustomerByBd" resultType="java.lang.Long">
        SELECT cust_id
        FROM cust_category_performance_comm
        WHERE bd_id = #{bdId}
        <if test="spuGroups != null and spuGroups.size() > 0">
            AND spu_group IN
            <foreach collection="spuGroups" item="spuGroup" open="(" separator="," close=")">
                #{spuGroup}
            </foreach>
        </if>
        <if test="custType != null and custType != ''">
            AND cust_type = #{custType}
        </if>
        GROUP BY cust_id
        <if test="completionRewardStatus != null">
            HAVING MIN(is_complete) = #{completionRewardStatus}
        </if>
    </select>

</mapper>