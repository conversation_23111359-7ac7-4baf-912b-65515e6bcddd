<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantTodayGmvMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantTodayGmv">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="mId" column="m_id" jdbcType="BIGINT"/>
            <result property="merchantName" column="merchant_name" jdbcType="VARCHAR"/>
            <result property="merchantTotalGmv" column="merchant_total_gmv" jdbcType="DECIMAL"/>
            <result property="distributionGmv" column="distribution_gmv" jdbcType="DECIMAL"/>
            <result property="fruitGmv" column="fruit_gmv" jdbcType="DECIMAL"/>
            <result property="dairyGmv" column="dairy_gmv" jdbcType="DECIMAL"/>
            <result property="nonDairyGmv" column="non_dairy_gmv" jdbcType="DECIMAL"/>
            <result property="brandGmv" column="brand_gmv" jdbcType="DECIMAL"/>
            <result property="rewardGmv" column="reward_gmv" jdbcType="DECIMAL"/>
            <result property="distributionSpu" column="distribution_spu" jdbcType="INTEGER"/>
            <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,m_id,merchant_name,
        merchant_total_gmv,distribution_gmv,fruit_gmv,
        dairy_gmv,non_dairy_gmv,brand_gmv,
        reward_gmv,distribution_spu,day_tag,
        update_time,create_time
    </sql>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_merchant_today_gmv
        where  day_tag = #{dayTag} and  m_id = #{merchantId}
    </select>
</mapper>
