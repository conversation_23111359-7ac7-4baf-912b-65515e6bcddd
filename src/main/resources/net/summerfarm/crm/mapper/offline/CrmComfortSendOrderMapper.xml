<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmComfortSendOrderMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.vo.ComfortSendOrderVO">
        <result column="m_id" jdbcType="BIGINT" property="mId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_time" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="pd_name" jdbcType="VARCHAR" property="pdName"/>
        <result column="pd_amount" jdbcType="INTEGER" property="pdAmount"/>
        <result column="bd_id" jdbcType="INTEGER" property="bdId"/>
        <result column="pd_weight" jdbcType="VARCHAR" property="pdWeight"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, m_id, order_no, order_time, pd_name, pd_amount, pd_weight, pay_amount, area_no, bd_id, m_name as mName
  </sql>

    <sql id="LIST">
    id, m_id, order_no, order_time, pd_name, pd_amount, pd_weight, pay_amount, area_no, bd_id, mname as mName
   </sql>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where day_tag = #{dayTag}
        <if test="bdId != null">
            and bd_id = #{bdId}
        </if>
        <if test="areaNo != null">
            and area_no = #{areaNo}
        </if>
        <if test="areaNos!=null and areaNos.size()>0">
            and  area_no in
            <foreach open="(" close=")" separator="," collection="areaNos" item="item" index="index">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="province != null and province != ''">
            and province=#{province,jdbcType=VARCHAR}
        </if>
        <if test="city != null and city != ''">
            and city=#{city,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area != ''">
            and area=#{area,jdbcType=VARCHAR}
        </if>
        order by order_time desc
    </select>

    <select id="countByTableBdIdAreaNo" resultType="integer">
        select count(1)
        from ${tableName}
        where day_tag = #{dayTag}
        <if test="bdId != null">
            and bd_id = #{bdId}
        </if>
        <if test="province != null and province != ''">
            and province=#{province,jdbcType=VARCHAR}
        </if>
        <if test="city != null and city != ''">
            and city=#{city,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area != ''">
            and area=#{area,jdbcType=VARCHAR}
        </if>
    </select>



    <select id="selectByQueryList" resultMap="BaseResultMap">
        select
        <include refid="LIST"/>
        from ${tableName}
        where day_tag = #{dayTag}
        <if test="bdId != null">
            and bd_id = #{bdId}
        </if>
        <if test="areaNo != null">
            and area_no = #{areaNo}
        </if>
        <if test="province != null and province != ''">
            and province=#{province,jdbcType=VARCHAR}
        </if>
        <if test="city != null and city != ''">
            and city=#{city,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area != ''">
            and area=#{area,jdbcType=VARCHAR}
        </if>
        <if test="areaNos != null and areaNos.size() > 0">
            and area_no in
            <foreach open="(" close=")" separator="," collection="areaNos" item="item" index="index">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        order by order_time desc
    </select>


</mapper>