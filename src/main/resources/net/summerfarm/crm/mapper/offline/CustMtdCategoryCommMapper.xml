<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CustMtdCategoryCommMapper">

    <!-- 销售维度品类推广数据查询 -->
    <select id="getCategoryPromotionByCustomer"
            resultType="net.summerfarm.crm.model.dto.salesperformance.CategoryPromotionDTO">
        SELECT
        c.cust_id AS custId,
        c.cust_name AS custName,
        SUM(c.category_comm_amt) AS rewardAmount,
        ROUND(SUM(IF(c.is_dlv_payment = 1, c.big_sku_cnt, 0)), 2) AS fulfillmentCount,
        ROUND(SUM(IF(c.is_dlv_payment = 0, c.big_sku_cnt, 0)), 2) AS transactionCount
        FROM
        cust_mtd_category_comm c
        WHERE
        c.bd_id = #{bdId}
        and c.category_comm_amt > 0
        <if test="custName != null and custName != ''">
            AND c.cust_name LIKE CONCAT(#{custName}, '%')
        </if>
        <if test="custType != null and custType != ''">
            AND c.cust_type = #{custType}
        </if>
        <if test="spuGroupList != null and spuGroupList.size() > 0">
            AND c.spu_group IN
            <foreach collection="spuGroupList" item="spuGroup" open="(" separator="," close=")">
                #{spuGroup}
            </foreach>
        </if>
        GROUP BY
        c.cust_id, c.cust_name
        HAVING SUM(c.category_comm_amt) > 0
        <if test="sortField != null">
            ORDER BY ${sortField}
            <bind name="finalSortDirection" value="(sortDirection.equalsIgnoreCase('ASC')) ? 'ASC' : 'DESC'"/>
            ${finalSortDirection}
        </if>
    </select>

    <!-- 门店维度品类推广数据查询 -->
    <select id="getCategoryPromotionByMerchant"
            resultType="net.summerfarm.crm.model.dto.salesperformance.CategoryPromotionDTO">
        SELECT
        c.cust_id AS custId,
        c.cust_name AS custName,
        c.spu_group AS spuGroup,
        c.category_comm_amt AS rewardAmount,
        ROUND(IF(c.is_dlv_payment = 1, c.big_sku_cnt, 0), 2) AS fulfillmentCount,
        ROUND(IF(c.is_dlv_payment = 0, c.big_sku_cnt, 0), 2) AS transactionCount,
        c.category_comm_amt / c.big_sku_cnt AS rewardPerItem
        FROM
        cust_mtd_category_comm c
        WHERE
        c.cust_id = #{custId}
        AND c.bd_id = #{bdId}
        AND c.category_comm_amt > 0
        <if test="spuGroupList != null and spuGroupList.size() > 0">
            AND c.spu_group IN
            <foreach collection="spuGroupList" item="spuGroup" open="(" separator="," close=")">
                #{spuGroup}
            </foreach>
        </if>
        <if test="custType != null and custType != ''">
            AND c.cust_type = #{custType}
        </if>
        GROUP BY
        c.spu_group
        <if test="sortField != null">
            ORDER BY ${sortField}
            <bind name="finalSortDirection" value="(sortDirection.equalsIgnoreCase('ASC')) ? 'ASC' : 'DESC'"/>
            ${finalSortDirection}
        </if>
    </select>

</mapper>
