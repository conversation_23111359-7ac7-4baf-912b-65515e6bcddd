<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.SkuSalesRankingListMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.dto.SkuSalesRankingListDTO">
        <result column="day_tag" jdbcType="VARCHAR" property="dayTag" />
        <result column="merchant_type" jdbcType="VARCHAR" property="merchantType" />
        <result column="sku_list" jdbcType="VARCHAR" property="skuList" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        day_tag, merchant_type, sku_list
    </sql>

    <!-- 根据日期标签和门店类型查询SKU销售排行榜 -->
    <select id="selectByDayTagAndMerchantType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sku_sales_ranking_list
        WHERE day_tag = #{dayTag,jdbcType=VARCHAR}
        <if test="merchantType != null and merchantType != ''">
            AND merchant_type = #{merchantType,jdbcType=VARCHAR}
        </if>
        LIMIT 1
    </select>

    <!-- 根据日期标签查询SKU销售排行榜（不限门店类型） -->
    <select id="selectByDayTag" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sku_sales_ranking_list
        WHERE day_tag = #{dayTag,jdbcType=VARCHAR}
        LIMIT 1
    </select>

</mapper>