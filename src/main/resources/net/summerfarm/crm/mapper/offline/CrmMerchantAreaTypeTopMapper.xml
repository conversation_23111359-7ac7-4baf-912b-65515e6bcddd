<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmMerchantAreaTypeTopMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmMerchantAreaTypeTop">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="areaNo" column="area_no" jdbcType="OTHER"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="categoryIdList" column="category_id_list" jdbcType="VARCHAR"/>
            <result property="pdIdList" column="pd_id_list" jdbcType="VARCHAR"/>
            <result property="monthType" column="month_type" jdbcType="TINYINT"/>
            <result property="monthTag" column="month_tag" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        area_no,type,category_id_list,
        pd_id_list,month_type,month_tag
    </sql>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_merchant_area_type_top
        where  month_tag = #{monthTag} and type = #{type} and area_no = #{areaNo}
    </select>


</mapper>
