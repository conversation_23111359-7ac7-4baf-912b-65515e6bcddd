<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.FruitPopCustValueLableMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.FruitPopCustValueLable">
    <!--@mbg.generated-->
    <!--@Table fruit_pop_cust_value_lable-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="cust_id" jdbcType="BIGINT" property="custId" />
    <result column="cust_value_lable" jdbcType="VARCHAR" property="custValueLable" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, cust_id, cust_value_lable
  </sql>
</mapper>