<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CategoryCouponQuotaRewardMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CategoryCouponQuotaReward">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="adminId" column="admin_id" jdbcType="BIGINT"/>
            <result property="adminName" column="admin_name" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="bdId" column="bd_id" jdbcType="BIGINT"/>
            <result property="bdName" column="bd_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="dayTag" column="day_tag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,admin_id,admin_name,
        amount,bd_id,bd_name,
        create_time,day_tag
    </sql>

    <select id="selectByDayTag" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from category_coupon_quota_reward
        where  day_tag=#{dayTag}
    </select>
</mapper>
