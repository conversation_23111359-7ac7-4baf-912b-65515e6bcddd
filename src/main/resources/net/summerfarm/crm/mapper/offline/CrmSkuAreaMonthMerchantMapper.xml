<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmSkuAreaMonthMerchantMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmSkuAreaMonthMerchant">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="areaNo" column="area_no" jdbcType="OTHER"/>
            <result property="merchantIdText" column="merchant_id_text" jdbcType="VARCHAR"/>
            <result property="monthTag" column="month_tag" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,
        sku,area_no,merchant_id_text,
        month_tag
    </sql>

    <select id="selectByQuery" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from crm_sku_area_month_merchant
        where  month_tag = #{monthTag} and area_no = #{areaNo} and sku = #{sku}
    </select>
</mapper>
