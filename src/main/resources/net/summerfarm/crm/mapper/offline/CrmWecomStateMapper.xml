<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmWecomStateMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmWecomState">
        <!--@Table crm_wecom_state-->
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bd_id" property="bdId"/>
        <result column="bd_name" property="bdName"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_name" property="parentName"/>
        <result column="job_state" property="jobState"/>
        <result column="wecom_state" property="wecomState"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        bd_id,
        bd_name,
        parent_id,
        parent_name,
        job_state,
        wecom_state
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_wecom_state
        where id = #{id}
    </select>

    <select id="selectActivateByBdId" resultType="net.summerfarm.crm.model.vo.weCom.WeComActivateVo">
        select parent_id                                                   bdId,
               parent_name                                                 bdName,
               ifnull(sum(if(job_state = 1, 1, 0)), 0)                     onJobCount,
               ifnull(sum(if(job_state = 0, 1, 0)), 0)                     offJobCount,
               ifnull(sum(if(job_state = 1 and wecom_state = 1, 1, 0)), 0) onJobActivateCount,
               ifnull(sum(if(job_state = 0 and wecom_state = 1, 1, 0)), 0) offJobActivateCount,
               ifnull(sum(if(job_state = 1 and wecom_state = 1, 1, 0)) / sum(if(job_state = 1, 1, 0)), 0) *
               100                                                         onJobActivateProportion
        from crm_wecom_state
        where parent_id in
        <foreach collection="bdId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectStateByParentId" resultType="net.summerfarm.crm.model.vo.weCom.WeComActivateStatusVo">
        select bd_id       bdId,
               bd_name     bdName,
               wecom_state activateState,
               job_state   onJobState
        from crm_wecom_state
        where parent_id = #{bdId} and job_state = 1
    </select>

    <select id="selectStateByBdId" resultType="net.summerfarm.crm.model.vo.weCom.WeComActivateStatusVo">
        select bd_id       bdId,
               bd_name     bdName,
               wecom_state activateState,
               job_state   onJobState
        from crm_wecom_state
        where bd_id = #{bdId}
    </select>
</mapper>