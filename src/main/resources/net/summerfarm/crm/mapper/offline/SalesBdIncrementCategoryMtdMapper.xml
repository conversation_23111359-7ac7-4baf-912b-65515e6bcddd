<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.SalesBdIncrementCategoryMtdMapper">

    <!-- 销售维度品类推广数据查询 -->
    <select id="getCategoryPromotionBySales"
            resultType="net.summerfarm.crm.model.dto.salesperformance.CategoryPromotionDTO">
        SELECT
        s.bd_id AS bdId,
        s.bd_name AS bdName,
        SUM(s.contributing_cust_count) AS customerCount,
        SUM(s.category_comm_amt) AS rewardAmount,
        ROUND(SUM(CASE WHEN s.is_dlv_payment = 1 THEN s.big_sku_cnt ELSE 0 END), 2) AS fulfillmentCount,
        ROUND(SUM(CASE WHEN s.is_dlv_payment = 0 THEN s.big_sku_cnt ELSE 0 END), 2) AS transactionCount
        FROM
        sales_bd_increment_category_mtd s
        WHERE
        s.bd_id IN
        <foreach collection="bdIds" item="bdId" open="(" separator="," close=")">
            #{bdId}
        </foreach>
        <if test="custIncrementType != null and custIncrementType != ''">
            AND s.cust_increment_type = #{custIncrementType}
        </if>
        <if test="spuGroupList != null and spuGroupList.size() > 0">
            AND s.spu_group IN
            <foreach collection="spuGroupList" item="spuGroup" open="(" separator="," close=")">
                #{spuGroup}
            </foreach>
        </if>
        GROUP BY
        s.bd_id, s.bd_name
        <if test="sortField != null">
            ORDER BY ${sortField}
            <bind name="finalSortDirection" value="(sortDirection.equalsIgnoreCase('ASC')) ? 'ASC' : 'DESC'" />
            ${finalSortDirection}
        </if>
    </select>

</mapper>
