<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.crm.mapper.offline.CrmCityTodayHourGmvMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.crm.model.domain.CrmCityTodayHourGmv">
        <!--@mbg.generated-->
        <!--@Table crm_city_today_hour_gmv-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="date_tag" jdbcType="INTEGER" property="dateTag"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="origin_total_gmv" jdbcType="DECIMAL" property="originTotalGmv"/>
        <result column="real_total_gmv" jdbcType="DECIMAL" property="realTotalGmv"/>
        <result column="cust_cnt" jdbcType="BIGINT" property="custCnt"/>
        <result column="categories_origin_total_gmv" jdbcType="DECIMAL" property="categoriesOriginTotalGmv"/>
        <result column="categories_real_total_gmv" jdbcType="DECIMAL" property="categoriesRealTotalGmv"/>
        <result column="categories_cust_cnt" jdbcType="BIGINT" property="categoriesCustCnt"/>
        <result column="fruit_origin_total_gmv" jdbcType="DECIMAL" property="fruitOriginTotalGmv"/>
        <result column="fruit_real_total_gmv" jdbcType="DECIMAL" property="fruitRealTotalGmv"/>
        <result column="fruit_cust_cnt" jdbcType="BIGINT" property="fruitCustCnt"/>
        <result column="anchor_origin_total_gmv" jdbcType="DECIMAL" property="anchorOriginTotalGmv"/>
        <result column="anchor_real_total_gmv" jdbcType="DECIMAL" property="anchorRealTotalGmv"/>
        <result column="anchor_cust_cnt" jdbcType="BIGINT" property="anchorCustCnt"/>
        <result column="dairy_origin_total_gmv" jdbcType="DECIMAL" property="dairyOriginTotalGmv"/>
        <result column="dairy_real_total_gmv" jdbcType="DECIMAL" property="dairyRealTotalGmv"/>
        <result column="dairy_cust_cnt" jdbcType="BIGINT" property="dairyCustCnt"/>
        <result column="other_origin_total_gmv" jdbcType="DECIMAL" property="otherOriginTotalGmv"/>
        <result column="other_real_total_gmv" jdbcType="DECIMAL" property="otherRealTotalGmv"/>
        <result column="other_cust_cnt" jdbcType="BIGINT" property="otherCustCnt"/>
        <result column="no_anchor_origin_total_gmv" jdbcType="DECIMAL" property="noAnchorOriginTotalGmv"/>
        <result column="no_anchor_real_total_gmv" jdbcType="DECIMAL" property="noAnchorRealTotalGmv"/>
        <result column="no_anchor_cust_cnt" jdbcType="BIGINT" property="noAnchorCustCnt"/>
        <result column="dlv_real_total_gmv" jdbcType="DECIMAL" property="dlvRealTotalGmv"/>
        <result column="dlv_cust_cnt" jdbcType="BIGINT" property="dlvCustCnt"/>
        <result column="dlv_categories_origin_total_gmv" jdbcType="DECIMAL" property="dlvCategoriesOriginTotalGmv"/>
        <result column="dlv_categories_real_total_gmv" jdbcType="DECIMAL" property="dlvCategoriesRealTotalGmv"/>
        <result column="dlv_categories_cust_cnt" jdbcType="BIGINT" property="dlvCategoriesCustCnt"/>
        <result column="dlv_fruit_real_total_gmv" jdbcType="DECIMAL" property="dlvFruitRealTotalGmv"/>
        <result column="dlv_fruit_cust_cnt" jdbcType="BIGINT" property="dlvFruitCustCnt"/>
        <result column="dlv_anchor_real_total_gmv" jdbcType="DECIMAL" property="dlvAnchorRealTotalGmv"/>
        <result column="dlv_anchor_cust_cnt" jdbcType="BIGINT" property="dlvAnchorCustCnt"/>
        <result column="dlv_dairy_real_total_gmv" jdbcType="DECIMAL" property="dlvDairyRealTotalGmv"/>
        <result column="dlv_dairy_cust_cnt" jdbcType="BIGINT" property="dlvDairyCustCnt"/>
        <result column="dlv_other_real_total_gmv" jdbcType="DECIMAL" property="dlvOtherRealTotalGmv"/>
        <result column="dlv_other_cust_cnt" jdbcType="BIGINT" property="dlvOtherCustCnt"/>
        <result column="dlv_no_anchor_real_total_gmv" jdbcType="DECIMAL" property="dlvNoAnchorRealTotalGmv"/>
        <result column="dlv_no_anchor_cust_cnt" jdbcType="BIGINT" property="dlvNoAnchorCustCnt"/>
        <result column="dlv_timing_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingRealTotalGmv"/>
        <result column="dlv_timing_anchor_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingAnchorRealTotalGmv"/>
        <result column="dlv_timing_dairy_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingDairyRealTotalGmv"/>
        <result column="dlv_timing_no_anchor_real_total_gmv" jdbcType="DECIMAL"
                property="dlvTimingNoAnchorRealTotalGmv"/>
        <result column="dlv_timing_other_real_total_gmv" jdbcType="DECIMAL" property="dlvTimingOtherRealTotalGmv"/>
        <result column="dlv_tomorrow_origin_total_gmv" jdbcType="DECIMAL" property="dlvTomorrowOriginTotalGmv"/>
        <result column="dlv_tomorrow_real_total_gmv" jdbcType="DECIMAL" property="dlvTomorrowRealTotalGmv"/>
        <result column="dlv_tomorrow_cust_cnt" jdbcType="BIGINT" property="dlvTomorrowCustCnt"/>
        <result column="no_anchor_categories_kpi_gmv" jdbcType="DECIMAL" property="noAnchorCategoriesKpiGmv"/>
        <result column="accompanying_visits_m1" jdbcType="BIGINT" property="accompanyingVisitsM1"/>
        <result column="accompanying_visits_m2" jdbcType="BIGINT" property="accompanyingVisitsM2"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, date_tag, city, area, origin_total_gmv, real_total_gmv, cust_cnt, categories_origin_total_gmv,
        categories_real_total_gmv, categories_cust_cnt, fruit_origin_total_gmv, fruit_real_total_gmv,
        fruit_cust_cnt, anchor_origin_total_gmv, anchor_real_total_gmv, anchor_cust_cnt,
        dairy_origin_total_gmv, dairy_real_total_gmv, dairy_cust_cnt, other_origin_total_gmv,
        other_real_total_gmv, other_cust_cnt, no_anchor_origin_total_gmv, no_anchor_real_total_gmv,
        no_anchor_cust_cnt, dlv_real_total_gmv, dlv_cust_cnt, dlv_categories_origin_total_gmv,
        dlv_categories_real_total_gmv, dlv_categories_cust_cnt, dlv_fruit_real_total_gmv,
        dlv_fruit_cust_cnt, dlv_anchor_real_total_gmv, dlv_anchor_cust_cnt, dlv_dairy_real_total_gmv,
        dlv_dairy_cust_cnt, dlv_other_real_total_gmv, dlv_other_cust_cnt, dlv_no_anchor_real_total_gmv,
        dlv_no_anchor_cust_cnt, dlv_timing_real_total_gmv, dlv_timing_anchor_real_total_gmv,
        dlv_timing_dairy_real_total_gmv, dlv_timing_no_anchor_real_total_gmv, dlv_timing_other_real_total_gmv,
        dlv_tomorrow_origin_total_gmv, dlv_tomorrow_real_total_gmv, dlv_tomorrow_cust_cnt,
        no_anchor_categories_kpi_gmv, accompanying_visits_m1, accompanying_visits_m2
    </sql>

    <sql id="Sum_City_Data">
            ifnull(sum(real_total_gmv), 0)                as real_total_gmv,
            ifnull(sum(dlv_real_total_gmv), 0)            as dlv_real_total_gmv,
            ifnull(sum(cust_cnt), 0)                      as cust_cnt,
            ifnull(sum(categories_real_total_gmv), 0)     as categories_real_total_gmv,
            ifnull(sum(fruit_real_total_gmv), 0)          as fruit_real_total_gmv,
            ifnull(sum(dairy_real_total_gmv), 0)          as dairy_real_total_gmv,
            ifnull(sum(other_real_total_gmv), 0)          as other_real_total_gmv,
            ifnull(sum(anchor_real_total_gmv), 0)         as anchor_real_total_gmv,
            ifnull(sum(accompanying_visits_m1), 0)        as accompanying_visits_m1,
            ifnull(sum(dlv_timing_real_total_gmv), 0)     as dlv_timing_real_total_gmv,
            ifnull(sum(dlv_cust_cnt), 0)                  as dlv_cust_cnt,
            ifnull(sum(dlv_categories_real_total_gmv), 0) as dlv_categories_real_total_gmv,
            ifnull(sum(dlv_fruit_real_total_gmv), 0)      as dlv_fruit_real_total_gmv,
            ifnull(sum(dlv_anchor_real_total_gmv), 0)     as dlv_anchor_real_total_gmv,
            ifnull(sum(dlv_dairy_real_total_gmv), 0)      as dlv_dairy_real_total_gmv,
            ifnull(sum(dlv_other_real_total_gmv), 0)      as dlv_other_real_total_gmv,
            ifnull(sum(dlv_no_anchor_real_total_gmv), 0)  as dlv_no_anchor_real_total_gmv,
            ifnull(sum(no_anchor_categories_kpi_gmv), 0)  as no_anchor_categories_kpi_gmv
    </sql>


    <select id="sumTodayDataByCitiesAndDateTag" resultMap="BaseResultMap">
        select
        <include refid="Sum_City_Data"/>
        from crm_city_today_hour_gmv
        where (city, area) in
        <foreach close=")" collection="cities" item="sales_city" open="(" separator=",">
            (#{sales_city.city}, #{sales_city.area})
        </foreach>
        and date_tag = #{dateTag}
    </select>

</mapper>