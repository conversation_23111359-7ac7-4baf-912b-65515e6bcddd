server:
  port: 80
  servlet:
    session:
      timeout: 3600

spring:
  application:
    name: crm
  http:
    encoding:
      charset: UTF-8
  datasource:
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000
  freemarker:
    template-loader-path: classpath:static/template/
    suffix: .ftl
  servlet:
    multipart:
      max-file-size: 5MB
  schedulerx2:
    appKey: NczB1mQsBFT7tW6KswhNJw==
    endpoint: acm.aliyun.com
    groupId: local_dev_crm_service_task
    namespace: 39ca7826-3009-4217-a25c-d15c6f1e7282
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 4
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 4
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
log-path:  ${APP_LOG_DIR:../log}
logging:
  level:
    root:  info
    org.springframework:  INFO
    org.mybatis:  INFO
    net.summerfarm.crm.mapper.manage: INFO
  #    net.summerfarm.crm.mapper.manage.ProductsMapper: debug
  pattern:
    console: "%d - %msg%n"
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: cfccb911-1306-4ea7-8a9f-18436f9dd245
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

# feign
feign:
  client:
    config:
      feignName:
        loggerLevel: full
        connectTimeout: 5000
        readTimeout: 5000
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/json,application/xml
      min-request-size: 1024
    response:
      enabled: true

mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: ******************************************************************************************
  username: dev4

redis:
  host: test-redis.summerfarm.net
  password: xianmu619
  port: 6379
  timeout: 6000
  database: 4

rocketmq:
  consumer:
    access-key: 'Rocketmq'
    secret-key: 'Rocketmq'
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: 'Rocketmq'
    group: GID_crm
    secret-key: 'Rocketmq'
    sendMsgTimeout: 10000

stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.feishu.cn
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory

xianmu:
  mall:
    domain: https://devh5.summerfarm.net
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C
  accessKeySecret: ******************************
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619

# feign远程调用url
remote:
  manage:
    url: https://dev4admin.summerfarm.net/
xm:
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devossperm.summerfarm.net
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      innerEndpoint: oss-cn-hangzhou-internal.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
      domain: devosstemp.summerfarm.net
  log:
    enable: true
    resp: true
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: b4bafec8-bee6-4ec0-a276-1cda3ad832a8