# mybatis sql 打印
#logging:
#   level:
#     net.summerfarm.mapper: debug
#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: ****************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: ********************************************************************************
  username: test
#redis:
#  host: **************
#  password: xianmu619
#  port: 6379
#  timeout: 6000
#  database: 2
rocketmq:
  consumer:
    access-key: 'Rocketmq'
    secret-key: 'Rocketmq'
  name-server: **************:9900
  producer:
    access-key: 'Rocketmq'
    group: GID_manage
    secret-key: 'Rocketmq'
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.summerfarm.net
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://qah5.summerfarm.net
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C00
  accessKeySecret: zNwstNpZuurzzPHike39bEl7uUat0y00
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
# easy-es:
easy-es:
  enable: true #默认为true,若为false则认为不启用本框架
  address: dev.es.summerfarm.net:80 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
  username: elastic #若无 则可省略此行配置
  password: Xianmu619 #若无 则可省略此行配置
log-path: ../log
spring:
  application:
    id: crm
    name: crm
  servlet:
    multipart:
      max-file-size: 5MB
  schedulerx2:
    appKey: NczB1mQsBFT7tW6KswhNJw==
    endpoint: acm.aliyun.com
    groupId: local_dev_crm_service_task
    namespace: 39ca7826-3009-4217-a25c-d15c6f1e7282
  redis:
    host: **************
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 2
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: **************
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 5
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
# feign远程调用url
remote:
  manage:
    url: http://manage-svc
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://********:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: 34792f7a-aaa2-41ee-8a7f-53be483c2533
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

xm:
  log:
    enable: true
    resp: true