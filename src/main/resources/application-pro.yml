server:
  tomcat:
    accept-count: 1000
    max-connections: 1000
    max-threads: 400
    min-spare-threads: 40
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 8
  maxActive: 300
  maxWait: 6000
  minIdle: 8
  offline:
    password: <PERSON><PERSON><PERSON>@odps_db
    url: ***********************************************************************************************************************
    username: odps_db_sa
  password: Summerfarm$180321
  testWhileIdle: true
  url: ****************************************************************************************************************
  username: xianmu_admin
redis:
  host: r-bp1jbnfh3sc5wfrtrq.tairpena.rds.aliyuncs.com
  password: summerfarm0619#
  port: 6379
  timeout: 5000
  database: 0
rocketmq:
  consumer:
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    secret-key: ******************************
  name-server: http://MQ_INST_1788664839736465_BXz3eVFS.cn-hangzhou.mq-internal.aliyuncs.com:8080
  producer:
    access-key: LTAI5t6azTEqB3a2GyJ2KkCG
    enableMsgTrace: false
    group: GID_manage
    secret-key: ******************************
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.summerfarm.net
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C
  accessKeySecret: ******************************
es:
  port: 9200
  url: es-cn-i7m2pv3ht000o90dy.elasticsearch.aliyuncs.com
  user-name: elastic
  user-pwd: elastic@Xianmu0619
# easy-es:
easy-es:
  enable: true #默认为true,若为false则认为不启用本框架
  address: es-cn-i7m2pv3ht000o90dy.elasticsearch.aliyuncs.com:9200 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200
  username: elastic #若无 则可省略此行配置
  password: elastic@Xianmu0619 #若无 则可省略此行配置

xianmu:
  mall:
    domain: https://h5.summerfarm.net
spring:
  application:
    id: crm
    name: crm
  servlet:
    multipart:
      max-file-size: 5MB
  schedulerx2:
    appKey: c7lIyfYAvk2We6W1D5uyJA==
    endpoint: addr-hz-internal.edas.aliyun.com
    groupId: crm_service_task
    namespace: 40bd5118-977f-435a-b376-834be9588fd9
  redis:
    host: r-bp1jbnfh3sc5wfrtrq.tairpena.rds.aliyuncs.com
    password: summerfarm0619#
    port: 6379
    timeout: 5000
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
# feign远程调用url
remote:
  manage:
    url: manage-svc

xm:
  log:
    enable: true
    resp: true