package net.summerfarm.crm.controller;

import com.google.common.collect.Lists;
import net.summerfarm.crm.common.config.CrmConfig;
import net.summerfarm.crm.model.domain.CrmBdOrg;
import net.summerfarm.crm.model.dto.config.BdGrayConfigDTO;
import net.summerfarm.crm.model.query.tool.BdInGrayRangeQuery;
import net.summerfarm.crm.model.vo.tool.BdInGrayRangeVO;
import net.summerfarm.crm.service.BdAreaConfigService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.CollectionUtils;

import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * ToolController单元测试
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ToolControllerTest {

    @Mock
    private BdAreaConfigService bdAreaConfigService;

    @Mock
    private CrmConfig crmConfig;

    @InjectMocks
    private ToolController toolController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryBdInGrayRange() {
        // 测试灰度项目不存在
        CrmBdOrg bdOrg = new CrmBdOrg();
        bdOrg.setBdId(1);
        when(bdAreaConfigService.getTopRankOrg()).thenReturn(bdOrg);
        when(crmConfig.getBdGrayConfig()).thenReturn(Collections.emptyList());
        BdInGrayRangeQuery query = new BdInGrayRangeQuery();
        query.setGrayProjects(Lists.newArrayList("project-none"));
        CommonResult<BdInGrayRangeVO> result = toolController.queryBdInGrayRange(query);
        assertEquals(ResultStatusEnum.OK.getStatus(), result.getStatus());
        assertTrue(CollectionUtils.isEmpty(result.getData().getDetails()));

        // 测试灰度项目不开放
        BdGrayConfigDTO grayConfig = new BdGrayConfigDTO();
        grayConfig.setProject("project1");
        when(crmConfig.getBdGrayConfig()).thenReturn(Lists.newArrayList(grayConfig));
        query.setGrayProjects(Lists.newArrayList("project1"));
        result = toolController.queryBdInGrayRange(query);
        assertEquals(ResultStatusEnum.OK.getStatus(), result.getStatus());
        assertFalse(result.getData().getDetails().get(0).getInGrayRange());

        // 测试灰度白名单
        grayConfig = new BdGrayConfigDTO();
        grayConfig.setProject("project1");
        grayConfig.setIncludeBds(Lists.newArrayList(1, 2, 3));
        when(crmConfig.getBdGrayConfig()).thenReturn(Lists.newArrayList(grayConfig));
        query.setGrayProjects(Lists.newArrayList("project1"));
        result = toolController.queryBdInGrayRange(query);
        assertEquals(ResultStatusEnum.OK.getStatus(), result.getStatus());
        assertTrue(result.getData().getDetails().get(0).getInGrayRange());

        // 测试灰度黑名单
        grayConfig = new BdGrayConfigDTO();
        grayConfig.setProject("project1");
        grayConfig.setExcludeBds(Lists.newArrayList(1, 2, 3));
        when(crmConfig.getBdGrayConfig()).thenReturn(Lists.newArrayList(grayConfig));
        query.setGrayProjects(Lists.newArrayList("project1"));
        result = toolController.queryBdInGrayRange(query);
        assertEquals(ResultStatusEnum.OK.getStatus(), result.getStatus());
        assertFalse(result.getData().getDetails().get(0).getInGrayRange());

        // 测试灰度项目全部开放
        grayConfig = new BdGrayConfigDTO();
        grayConfig.setProject("project1");
        grayConfig.setExcludeBds(Lists.newArrayList(-1));
        when(crmConfig.getBdGrayConfig()).thenReturn(Lists.newArrayList(grayConfig));
        query.setGrayProjects(Lists.newArrayList("project1"));
        result = toolController.queryBdInGrayRange(query);
        assertEquals(ResultStatusEnum.OK.getStatus(), result.getStatus());
        assertTrue(result.getData().getDetails().get(0).getInGrayRange());

        CrmBdOrg bdOrg2 = new CrmBdOrg();
        bdOrg2.setBdId(2);
        when(bdAreaConfigService.getTopRankOrg()).thenReturn(bdOrg2);
        result = toolController.queryBdInGrayRange(query);
        assertEquals(ResultStatusEnum.OK.getStatus(), result.getStatus());
        assertTrue(result.getData().getDetails().get(0).getInGrayRange());

        // 测试不带项目名称查询
        grayConfig = new BdGrayConfigDTO();
        grayConfig.setProject("project1");
        grayConfig.setExcludeBds(Lists.newArrayList(-1));
        BdGrayConfigDTO grayConfig2 = new BdGrayConfigDTO();
        grayConfig2.setProject("project2");
        grayConfig2.setIncludeBds(Lists.newArrayList(2));
        when(crmConfig.getBdGrayConfig()).thenReturn(Lists.newArrayList(grayConfig, grayConfig2));

        when(bdAreaConfigService.getTopRankOrg()).thenReturn(bdOrg);
        result = toolController.queryBdInGrayRange(new BdInGrayRangeQuery());
        assertEquals(ResultStatusEnum.OK.getStatus(), result.getStatus());
        assertEquals(2, result.getData().getDetails().size());
        BdInGrayRangeVO.BdInGrayRangeDetailVO detailVO1 = result.getData().getDetails().stream().filter(x -> "project1".equals(x.getGrayProject())).findFirst().orElse(null);
        assertNotNull(detailVO1);
        assertTrue(detailVO1.getInGrayRange());
        BdInGrayRangeVO.BdInGrayRangeDetailVO detailVO2 = result.getData().getDetails().stream().filter(x -> "project2".equals(x.getGrayProject())).findFirst().orElse(null);
        assertNotNull(detailVO2);
        assertFalse(detailVO2.getInGrayRange());
    }

}