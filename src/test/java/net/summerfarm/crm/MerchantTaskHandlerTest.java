package net.summerfarm.crm;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import cn.hutool.extra.spring.SpringUtil;
import net.summerfarm.crm.client.input.AdminTurningConfigInput;
import net.summerfarm.crm.client.input.BdInfoQueryInput;
import net.summerfarm.crm.mapper.manage.MerchantMapper;
import net.summerfarm.crm.model.domain.Merchant;
import net.summerfarm.crm.model.domain.MerchantSubAccount;
import net.summerfarm.crm.model.domain.SampleApply;
import net.summerfarm.crm.model.query.SampleApplyQuery;
import net.summerfarm.crm.model.vo.SampleApplyVO;
import net.summerfarm.crm.model.vo.sample.CheckSampleDto;
import net.summerfarm.crm.provider.impl.AdminTurningOperationProviderImpl;
import net.summerfarm.crm.provider.impl.BdInfoQueryProviderImpl;
import net.summerfarm.crm.service.SampleApplyReviewService;
import net.summerfarm.crm.service.SampleApplyService;
import net.summerfarm.crm.service.FollowUpRecordService;
import net.summerfarm.crm.service.SynEsPoolService;
import net.summerfarm.crm.service.WechatService;
import net.summerfarm.crm.task.handler.CheckOfflineHandler;
import net.summerfarm.crm.task.handler.CrmTaskUpdateStatusHandler;
import net.summerfarm.crm.task.handler.WechatTagSyncHandle;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe
 * @create 2022-08-22 15:05
 *
 */
@Slf4j
public class MerchantTaskHandlerTest extends BaseTest{

    @Resource
    private SynEsPoolService synEsPoolService;
    @Resource
    WechatTagSyncHandle wechatTagSyncHandle;

    @Resource
    MerchantMapper merchantMapper;
    @Resource
    AdminTurningOperationProviderImpl adminTurningOperationProvider;
    @Resource
    WechatService wechatService;
    @Resource
    SampleApplyReviewService sampleApplyReviewService;
    @Resource
    SampleApplyService sampleApplyService;

    @Resource
    CheckOfflineHandler checkOfflineHandler;
    @Resource
    CrmTaskUpdateStatusHandler crmTaskUpdateStatusHandler;
    @Resource
    BdInfoQueryProviderImpl bdInfoQueryProvider;
    @Test
    public void fixMerchantCity() throws Exception {
//        XmJobInput context =  new XmJobInput();
//        wechatTagSyncHandle.processResult(context);
        MerchantSubAccount account = new MerchantSubAccount();
        account.setPhone("***********");
        account.setMId(1211L);
        String userId = "YeFeng";
        String extendUserId = "wmndqQCQAAegEE6a80V8GMMfkj8rItvg";
        wechatService.updateRemark(account, userId, extendUserId);
    }
    //1	2023-11-15 16:02:31	2023-11-15 16:07:56	ADMIN_TURNING_CONFIG	4621	880
    @Test
    public void test2() throws Exception {
        XmJobInput context =  new XmJobInput();
        wechatTagSyncHandle.processResult(context);
    }

    @Test
    public void test3() throws Exception {
        XmJobInput xmJobInput  = new XmJobInput();
        checkOfflineHandler.processResult(xmJobInput);
    }


    @Test
    public void test4() throws Exception {
//        SampleApplyVO sampleApplyVO = new SampleApplyVO();
//        sampleApplyVO.setSampleSkuList();
//        sampleApplyVO.geta
        SampleApply sampleApply = new SampleApply();
        sampleApply.setBdId(1);

        AjaxResult xxx = sampleApplyService.selectSampleApplyVO(0, 1, sampleApply, "xxx");

  /*      String json = "{\"mId\":1417,\"mname\":\"薄荷勇敢小屋\",\"mphone\":\"***********\",\"mcontact\":\"吴13\",\"contactId\":1630,\"remark\":\"\",\"areaNo\":1001,\"sampleSkuList\":[{\"amount\":1,\"pdName\":\"演练测试\",\"sku\":\"877804460607\",\"weight\":\"1个*1箱/1/一级\",\"categoryId\":877,\"afterSaleQuantity\":1,\"categoryName\":\"测试3级\",\"extType\":0,\"invId\":21741,\"maturity\":\"\",\"origin\":\"\",\"pdId\":20629,\"picturePath\":\"\",\"quantity\":1761,\"unit\":\"包\",\"volume\":\"0.01*0.01*0.01\",\"weightNum\":1,\"checked\":true},{\"amount\":1,\"pdName\":\"演练测试\",\"sku\":\"877804460864\",\"weight\":\"1个*1箱/1/一级\",\"categoryId\":877,\"afterSaleQuantity\":1,\"categoryName\":\"测试3级\",\"extType\":0,\"invId\":21742,\"maturity\":\"\",\"origin\":\"\",\"pdId\":20629,\"picturePath\":\"\",\"quantity\":1663,\"unit\":\"包\",\"volume\":\"0.01*0.01*0.01\",\"weightNum\":1,\"checked\":true},{\"amount\":1,\"pdName\":\"测试1\",\"sku\":\"22835438855\",\"weight\":\"0.25L*12盒/精品\",\"categoryId\":250,\"afterSaleQuantity\":1000" +
                ",\"categoryName\":\"778\",\"extType\":0,\"invId\":87,\"maturity\":\"\",\"origin\":\"杭州\",\"pdId\":30,\"picturePath\":\"\",\"quantity\":1000,\"skuPic\":\"test/l0od11qp93exzsyq9.png\",\"unit\":\"箱\",\"volume\":\"1*0.10*0.10\",\"weightNum\":2,\"checked\":true}]}";
        SampleApplyVO sampleApplyVO = JsonUtil.fromJson(json, SampleApplyVO.class);
        sampleApplyReviewService.checkMerchantSample(sampleApplyVO);*/
    }

    @Test
    public void  testCrmTaskUpdateStatusHandler() throws Exception {
        XmJobInput context =  new XmJobInput();
        context.setInstanceParameters("19015;19016");
        crmTaskUpdateStatusHandler.processResult(context);
    }
    @Test
    public void test5() throws Exception {
       String str  =
               "summer_003,BaiJinYuan,wmndqQCQAAoA3R44aTuPKK4y0nd3v8Gg";
        String[] split = str.split(";");
        for (String s : split) {
            String[] split1 = s.split(",");
            String from =  split1[0];
            String to =  split1[1];
            String userId =  split1[2];
            wechatService.transferCustomerList(from, to, userId ,null);

        }

    }

    @Test
    public void test6() throws Exception {
        List<BdInfoQueryInput> list = new ArrayList<>();
        BdInfoQueryInput input1 = new BdInfoQueryInput();
        input1.setMid(6L);
        BdInfoQueryInput input2 = new BdInfoQueryInput();
        input2.setMid(7L);
        list.add(input1);
        list.add(input2);

        log.info(JSON.toJSONString(bdInfoQueryProvider.selectBdInfoByMidAndAreaNos(list)));



    }



}
