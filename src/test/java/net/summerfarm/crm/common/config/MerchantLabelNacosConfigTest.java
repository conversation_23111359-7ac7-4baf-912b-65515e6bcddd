package net.summerfarm.crm.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import net.summerfarm.crm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Map;

import static org.junit.Assert.assertEquals;

public class MerchantLabelNacosConfigTest extends BaseTest {

    @Resource
    private MerchantLabelNacosConfig merchantLabelNacosConfig;
    @NacosValue(value = "${merchant.label.school}", autoRefreshed = true)
    private Integer school;

    @Test
    public void testLabelValues() {
        Map<String, Integer> openingTimeMap = merchantLabelNacosConfig.getOpeningTimeMap();
        System.out.println(openingTimeMap);
        assertEquals(15, openingTimeMap.size());
    }
}