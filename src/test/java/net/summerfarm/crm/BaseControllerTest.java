package net.summerfarm.crm;

import com.alibaba.fastjson.JSON;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.DefaultMockMvcBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;

/**
 * @Package: net.summerfarm
 * @Description: controller单元测试基类
 * @author: <EMAIL>
 * @Date: 2016/8/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BaseControllerTest {


    protected static final Logger logger = LoggerFactory.getLogger(BaseControllerTest.class);

    public static final MediaType APPLICATION_JSON_UTF8 = new MediaType(MediaType.APPLICATION_JSON.getType(), MediaType.APPLICATION_JSON.getSubtype(), StandardCharsets.UTF_8);

    @Autowired
    protected WebApplicationContext webApplicationContext;

    protected MockMvc mvc;
    protected MockHttpSession session;

    @Before
    public void setUp() throws Exception {
        DefaultMockMvcBuilder builder = MockMvcBuilders.webAppContextSetup(webApplicationContext);
        builder.addFilters((javax.servlet.Filter) webApplicationContext.getBean("shiroFilter"));
        this.mvc = builder.build();
        //模拟登录
        this.session = getLoginSession();
    }

    @Test
    public void area() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/area")
                .accept(MediaType.APPLICATION_JSON)
                .session(session))        //需要时加入session
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    @Test
    public void ok() throws Exception {
        mvc.perform(
                MockMvcRequestBuilders.get("/ok")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 不建议使用，构造参数过于复杂。
     * 建议使用@RequestBody方式传参，然后使用本方法的重载方法
     * @param url
     * @param params
     * @return
     * @throws Exception
     */
    @Deprecated
    protected MvcResult postTest(String url, MultiValueMap<String, String> params) throws Exception {
        return doTest(url, RequestMethod.POST, params, "",MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult postTest(String url, String jsonData) throws Exception {
        return doTest(url, RequestMethod.POST, null, jsonData,APPLICATION_JSON_UTF8);
    }

    protected MvcResult getTest(String url) throws Exception {
        return doTest(url, RequestMethod.GET, null, "",MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult getTest(String url, MultiValueMap<String, String> params ) throws Exception {
        return doTest(url, RequestMethod.GET, params, "",MediaType.APPLICATION_FORM_URLENCODED);
    }

    protected MvcResult doTest(String url, RequestMethod requestMethod, MultiValueMap<String, String> params, String jsonData, MediaType mediaType) throws Exception {
        logger.info("请求参数为：{},msgbody内容：{}", JSON.toJSONString(params), jsonData);
        if (CollectionUtils.isEmpty(params)) {
            params = new LinkedMultiValueMap<>();
        }
        MockHttpServletRequestBuilder builder = null;
        switch (requestMethod){
            case POST: builder =  MockMvcRequestBuilders.post(url); break;
            case PUT: builder =  MockMvcRequestBuilders.put(url); break;
            case DELETE: builder =  MockMvcRequestBuilders.delete(url); break;
            default: builder = MockMvcRequestBuilders.get(url);break;
        }
        return mvc.perform(
                builder.session(session)
                        .accept(MediaType.APPLICATION_JSON)
                        .params(params)
                        .contentType(mediaType)
                        .content(jsonData)
        ).andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.forwardedUrl(null))        //判断是否发生了跳转
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
    }

    /**
     * 获取登入信息session
     * @return
     * @throws Exception
     */
    private MockHttpSession getLoginSession() throws Exception {
        MvcResult result = this.mvc
                .perform(
                        MockMvcRequestBuilders.post("/login")
                                .param("username", "<EMAIL>")
                                .param("password", "123456"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        return (MockHttpSession) result.getRequest().getSession();
    }
}
