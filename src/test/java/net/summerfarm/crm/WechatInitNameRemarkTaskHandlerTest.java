package net.summerfarm.crm;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.task.handler.WechatInitNameRemarkTaskHandler;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

@Slf4j

public class WechatInitNameRemarkTaskHandlerTest extends BaseTest{
    @Resource
    WechatInitNameRemarkTaskHandler wechatInitNameRemarkTaskHandler;
    @Test
    public void test1(){
        XmJobInput xmJobInput = new XmJobInput();
        try {
            wechatInitNameRemarkTaskHandler.processResult(xmJobInput);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
