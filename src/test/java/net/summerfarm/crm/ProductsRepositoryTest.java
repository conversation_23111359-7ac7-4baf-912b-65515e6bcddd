package net.summerfarm.crm;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.common.util.DateUtils;
import net.summerfarm.crm.mapper.repository.ProductsRepository;
import net.summerfarm.crm.model.dto.CategoryProductDTO;
import net.summerfarm.crm.model.dto.CustomerAnalysisDTO;
import net.summerfarm.crm.model.query.MerchantDetailQuery;
import net.summerfarm.crm.model.query.SkuMerchantQuery;
import net.summerfarm.crm.model.vo.CrmSkuMonthGmvVO;
import net.summerfarm.goods.client.resp.CategoryResp;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;


@Slf4j
public class ProductsRepositoryTest extends BaseTest {

    @Resource
    ProductsRepository productsRepository;


    @Test
    public void selectByQuery() {
        SkuMerchantQuery skuMerchantQuery = new SkuMerchantQuery();
        skuMerchantQuery.setSkuList(Collections.singletonList("5483773380"));
        List<CrmSkuMonthGmvVO> query = productsRepository.selectByQuery(skuMerchantQuery);
        log.info(JSONUtil.toJsonStr(query));


        //无成本价商品啊顶顶顶顶哒哒哒哒哒哒大多数订单的
        SkuMerchantQuery nameQuery = new SkuMerchantQuery();
        nameQuery.setPdName("无成本价商品啊顶顶顶顶哒哒哒哒哒哒大多数订单的");
        List<CrmSkuMonthGmvVO> query1 = productsRepository.selectByQuery(nameQuery);
        log.info(JSONUtil.toJsonStr(query1));

        assertNotNull(query1);

    }



    @Test
    public void selectByCategoryQuery() {
        List<CategoryResp> query = productsRepository.selectByCategoryQuery(Collections.singletonList(244L));
        log.info(JSONUtil.toJsonStr(query));
    }


    @Test
    public void getCategoryTypeBySku() {
        String sku = "5483773380";
        CategoryProductDTO categoryTypeBySku = productsRepository.getCategoryTypeBySku(sku);
        log.info(JSONUtil.toJsonStr(categoryTypeBySku));
    }


    @Test
    public void selectCategoryTop() {
        MerchantDetailQuery merchantDetailQuery = new MerchantDetailQuery();
        merchantDetailQuery.setMerchantId(349237L);
        merchantDetailQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        merchantDetailQuery.setEndTime(LocalDateTime.now());
        List<CustomerAnalysisDTO> query = productsRepository.selectCategoryTop(merchantDetailQuery);
        log.info(JSONUtil.toJsonStr(query));
    }

    @Test
    public void selectProductByQuery() {
        MerchantDetailQuery merchantDetailQuery = new MerchantDetailQuery();
        merchantDetailQuery.setMerchantId(344509L);
        merchantDetailQuery.setStartTime(DateUtils.getAtBeginningOfMonth());
        merchantDetailQuery.setEndTime(LocalDateTime.now());
        List<CrmSkuMonthGmvVO> query = productsRepository.selectProductByQuery(merchantDetailQuery);
        log.info(JSONUtil.toJsonStr(query));
    }

    @Test
    public void getSkuBySkus() {
        List<String> skus = Arrays.asList("不存在的", "5483773381");
        Map<String, CrmSkuMonthGmvVO> skuBySkus = productsRepository.getSkuBySkus(skus);
        log.info(JSONUtil.toJsonStr(skuBySkus));
    }

}
