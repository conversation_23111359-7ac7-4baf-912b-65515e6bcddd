package net.summerfarm.crm.service;

import com.alibaba.fastjson.JSON;
import net.summerfarm.crm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.GET_USER;

public class MerchantLeadsServiceTest extends BaseTest {

    @Resource
    private MerchantLeadsService merchantLeadsService;

    @Test
    public void testGet() {
        System.out.println(JSON.toJSONString(merchantLeadsService.queryPopAreaNo()));
    }

}