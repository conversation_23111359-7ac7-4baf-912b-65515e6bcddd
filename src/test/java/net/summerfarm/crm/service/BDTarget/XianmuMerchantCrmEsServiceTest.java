package net.summerfarm.crm.service.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.model.query.objectiveManagement.NearbyPoiPotentialMerchantQuery;
import net.summerfarm.crm.model.query.objectiveManagement.RecommendPotentialMerchantQuery;
import net.summerfarm.crm.model.vo.PoiVO;
import net.summerfarm.crm.model.vo.objectiveManagement.MerchantVisitPotentialVO;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;

/**
 * 仙木商户CRM ES服务测试类
 * 验证ES查询潜力值客户功能
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class XianmuMerchantCrmEsServiceTest {

    @Resource
    private XianmuMerchantCrmEsService xianmuMerchantCrmEsService;

    /**
     * 测试查询附近POI潜力值客户
     * 验证地理位置查询和潜力值排序功能
     */
    @Test
    public void testQueryNearbyPoiPotentialMerchants() {
        try {
            // 准备测试数据
            NearbyPoiPotentialMerchantQuery query = new NearbyPoiPotentialMerchantQuery();
            
            // 设置地理位置信息
//            query.setProvince("浙江");
//            query.setCity("杭州市");
//            query.setArea("西湖区");
            
            // 设置POI位置（杭州西湖区某个位置）
            PoiVO poi = new PoiVO();
            poi.setLon(115.39154f);   // 经度
            poi.setLat(25.141878f);  // 纬度
            query.setPoi(poi);
            
            // 设置查询距离（5公里）
//            query.setQueryDistance(5000000);
            
            // 设置分页参数
            query.setPageNum(1);
            query.setPageSize(200);
            
            // 设置排序类型（1：潜力值排序，2：距离排序）
            query.setSortType(2);
            
            // 设置生命周期过滤（可选）
//            query.setLifecycleList(Arrays.asList(1, 2, 3));
            
            // 设置高价值标签过滤（可选）
//            query.setHighValueLabelList(Arrays.asList(1, 2));


            query.setBdId(6017784L);

            log.info("开始测试查询附近POI潜力值客户");
            log.info("查询位置: 经度={}, 纬度={}", poi.getLon(), poi.getLat());
            log.info("查询范围: {}米", query.getQueryDistance());
            log.info("查询区域: {}-{}-{}", query.getProvince(), query.getCity(), query.getArea());
            
            // 执行查询
            PageInfo<MerchantVisitPotentialVO> result = xianmuMerchantCrmEsService.queryNearbyPoiPotentialMerchants(query);
            
            log.info("附近POI潜力值客户查询完成: {}", JSONObject.toJSONString(result));
            
            // 验证结果
            if (result != null) {
                log.info("查询成功，总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getList().size());
                
                // 打印前几条记录的详细信息
                if (result.getList() != null && !result.getList().isEmpty()) {
                    log.info("前{}条记录详情:", Math.min(3, result.getList().size()));
                    for (int i = 0; i < Math.min(3, result.getList().size()); i++) {
                        MerchantVisitPotentialVO merchant = result.getList().get(i);
                        log.info("第{}条: 门店ID={}, 门店名称={}, 潜力值={}, 联系人={}, 地址={}", 
                                i + 1, merchant.getMId(), merchant.getMname(), 
                                merchant.getMerchantPotentialValue(), merchant.getContact(), merchant.getAddress());
                    }
                }
            } else {
                log.warn("查询结果为空");
            }
            
        } catch (Exception e) {
            log.error("测试查询附近POI潜力值客户失败", e);
            throw e;
        }
    }
    
    /**
     * 测试查询推荐潜力值客户
     * 验证推荐算法和去重功能
     */
    @Test
    public void testQueryRecommendPotentialMerchants() {
        try {
            // 准备测试数据
            RecommendPotentialMerchantQuery query = new RecommendPotentialMerchantQuery();

            query.setBdId(6017784L);

            // 设置地理位置信息
//            query.setProvince("浙江");
            query.setCity("杭州市");
//            query.setArea("西湖区");

            // 设置POI位置（杭州西湖区某个位置）
            PoiVO poi = new PoiVO();
            poi.setLon(115.39154f);   // 经度
            poi.setLat(25.141878f);  // 纬度
            query.setPoi(poi);
            
            // 设置推荐数量
            query.setRecommendCount(5);
            
            // 设置交通方式（必需字段：1-步行，2-骑行，3-驾车，4-公交）
            query.setTrafficType(1);
            
            // 设置分页参数
            query.setPageNum(1);
            query.setPageSize(10);
            
            // 设置生命周期过滤（可选）
//            query.setLifecycleList(Arrays.asList(1, 2, 3));
            
            // 设置高价值标签过滤（可选）
//            query.setHighValueLabelList(Arrays.asList(1, 2));
            
            // 设置BD ID（必需字段）
            query.setBdId(6017784L);
            
            log.info("开始测试查询推荐潜力值客户");
            log.info("推荐位置: 经度={}, 纬度={}", poi.getLon(), poi.getLat());
            log.info("推荐数量: {}", query.getRecommendCount());
            log.info("推荐区域: {}-{}-{}", query.getProvince(), query.getCity(), query.getArea());
            
            // 执行查询
            PageInfo<MerchantVisitPotentialVO> result = xianmuMerchantCrmEsService.queryRecommendPotentialMerchants(query);
            
            log.info("推荐潜力值客户查询完成: {}", JSONObject.toJSONString(result));
            
            // 验证结果
            if (result != null) {
                log.info("推荐成功，总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getList().size());
                
                // 打印推荐记录的详细信息
                if (result.getList() != null && !result.getList().isEmpty()) {
                    log.info("推荐记录详情:");
                    for (int i = 0; i < result.getList().size(); i++) {
                        MerchantVisitPotentialVO merchant = result.getList().get(i);
                        log.info("推荐{}：门店ID={}, 门店名称={}, 潜力值={}, 联系人={}, 地址={}", 
                                i + 1, merchant.getMId(), merchant.getMname(), 
                                merchant.getMerchantPotentialValue(), merchant.getContact(), merchant.getAddress());
                    }
                }
            } else {
                log.warn("推荐结果为空");
            }
            
        } catch (Exception e) {
            log.error("测试查询推荐潜力值客户失败", e);
            throw e;
        }
    }
}