package net.summerfarm.crm.service.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.enums.BDTarget.BdVisitPlanIndicatorEnum;
import net.summerfarm.crm.model.domain.BDTarget.BdDailyTargetDetail;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 多目标推荐服务测试类
 * 
 * <AUTHOR>
 * @date 2024-09-22
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class MultiTargetRecommendServiceTest {

    @Resource
    private MultiTargetRecommendService multiTargetRecommendService;

    /**
     * 测试新客户推荐脚本生成
     */
    @Test
    public void testGenerateNewCustomerRecommendScript() {
        log.info("开始测试新客户推荐脚本生成");
        
        // 准备测试数据
        Long merchantId = 12345L;
        List<BdDailyTargetDetail> targets = createNewCustomerTargets();
        
        // 执行测试
        String script = multiTargetRecommendService.generateRecommendScript(merchantId, targets);
        
        // 验证结果
        log.info("生成的新客户推荐脚本：\n{}", script);
        
        // 断言检查
        assert script != null : "推荐脚本不能为空";
        assert script.contains("多目标销售推荐脚本") : "脚本应包含标题";
        assert script.contains("门店ID") : "脚本应包含门店ID";
        assert script.contains("新客户") : "脚本应包含新客户相关内容";
    }

    /**
     * 测试老客户推荐脚本生成
     */
    @Test
    public void testGenerateOldCustomerRecommendScript() {
        log.info("开始测试老客户推荐脚本生成");
        
        // 准备测试数据
        Long merchantId = 67890L;
        List<BdDailyTargetDetail> targets = createOldCustomerTargets();
        
        // 执行测试
        String script = multiTargetRecommendService.generateRecommendScript(merchantId, targets);
        
        // 验证结果
        log.info("生成的老客户推荐脚本：\n{}", script);
        
        // 断言检查
        assert script != null : "推荐脚本不能为空";
        assert script.contains("多目标销售推荐脚本") : "脚本应包含标题";
        assert script.contains("门店ID") : "脚本应包含门店ID";
        assert script.contains("老客户") : "脚本应包含老客户相关内容";
    }

    /**
     * 测试混合目标推荐脚本生成
     */
    @Test
    public void testGenerateMixedTargetRecommendScript() {
        log.info("开始测试混合目标推荐脚本生成");
        
        // 准备测试数据
        Long merchantId = 11111L;
        List<BdDailyTargetDetail> targets = createMixedTargets();
        
        // 执行测试
        String script = multiTargetRecommendService.generateRecommendScript(merchantId, targets);
        
        // 验证结果
        log.info("生成的混合目标推荐脚本：\n{}", script);
        
        // 断言检查
        assert script != null : "推荐脚本不能为空";
        assert script.contains("多目标销售推荐脚本") : "脚本应包含标题";
        assert script.contains("门店ID") : "脚本应包含门店ID";
    }

    /**
     * 创建新客户目标数据
     */
    private List<BdDailyTargetDetail> createNewCustomerTargets() {
        BdDailyTargetDetail target1 = new BdDailyTargetDetail();
        target1.setId(1L);
        target1.setTargetType(BdVisitPlanIndicatorEnum.IndicatorType.NEW_CUSTOMER_COUNT.getCode());
        target1.setIndicatorExpectedValue(new BigDecimal("10"));
        target1.setIndicatorCurrentValue(new BigDecimal("3"));
        target1.setTargetDate(LocalDate.now());
        target1.setCreateTime(LocalDateTime.now());
        
        return Arrays.asList(target1);
    }

    /**
     * 创建老客户目标数据
     */
    private List<BdDailyTargetDetail> createOldCustomerTargets() {
        BdDailyTargetDetail target1 = new BdDailyTargetDetail();
        target1.setId(2L);
        target1.setTargetType(BdVisitPlanIndicatorEnum.IndicatorType.MONTHLY_ACTIVE_CUSTOMER_COUNT.getCode());
        target1.setIndicatorExpectedValue(new BigDecimal("50"));
        target1.setIndicatorCurrentValue(new BigDecimal("35"));
        target1.setTargetDate(LocalDate.now());
        target1.setCreateTime(LocalDateTime.now());
        
        return Arrays.asList(target1);
    }

    /**
     * 创建混合目标数据
     */
    private List<BdDailyTargetDetail> createMixedTargets() {
        BdDailyTargetDetail newCustomerTarget = new BdDailyTargetDetail();
        newCustomerTarget.setId(3L);
        newCustomerTarget.setTargetType(BdVisitPlanIndicatorEnum.IndicatorType.NEW_CUSTOMER_COUNT.getCode());
        newCustomerTarget.setIndicatorExpectedValue(new BigDecimal("5"));
        newCustomerTarget.setIndicatorCurrentValue(new BigDecimal("2"));
        newCustomerTarget.setTargetDate(LocalDate.now());
        newCustomerTarget.setCreateTime(LocalDateTime.now());
        
        BdDailyTargetDetail oldCustomerTarget = new BdDailyTargetDetail();
        oldCustomerTarget.setId(4L);
        oldCustomerTarget.setTargetType(BdVisitPlanIndicatorEnum.IndicatorType.MONTHLY_ACTIVE_CUSTOMER_COUNT.getCode());
        oldCustomerTarget.setIndicatorExpectedValue(new BigDecimal("30"));
        oldCustomerTarget.setIndicatorCurrentValue(new BigDecimal("20"));
        oldCustomerTarget.setTargetDate(LocalDate.now());
        oldCustomerTarget.setCreateTime(LocalDateTime.now());
        
        BdDailyTargetDetail productTarget = new BdDailyTargetDetail();
        productTarget.setId(5L);
        productTarget.setTargetType(BdVisitPlanIndicatorEnum.IndicatorType.CATEGORY_TARGET.getCode());
        productTarget.setIndicatorExpectedValue(new BigDecimal("100"));
        productTarget.setIndicatorCurrentValue(new BigDecimal("60"));
        productTarget.setTargetDate(LocalDate.now());
        productTarget.setCreateTime(LocalDateTime.now());
        
        return Arrays.asList(newCustomerTarget, oldCustomerTarget, productTarget);
    }
}