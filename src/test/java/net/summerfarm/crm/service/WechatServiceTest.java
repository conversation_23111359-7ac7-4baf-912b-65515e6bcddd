package net.summerfarm.crm.service;

import net.summerfarm.crm.BaseTest;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static net.summerfarm.crm.common.util.WeChatBaseUtil.GET_USER;
import static org.junit.jupiter.api.Assertions.*;

public class WechatServiceTest extends BaseTest {

    @Resource
    private WechatService wechatService;

    @Test
    public void testGet() {
        List<String> userIds = Arrays.asList(
                "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>hang-LvShiYe", "XiYanQing"
        );

        List<String> result = userIds.stream().map(userId -> {
            Map<String, Object> map = new HashMap<>();
            map.put("userid", userId);
            return wechatService.get(GET_USER, map);
        }).collect(Collectors.toList());
        System.out.println(result);

    }

}