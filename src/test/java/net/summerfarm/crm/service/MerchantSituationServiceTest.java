package net.summerfarm.crm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.crm.BaseTest;
import net.summerfarm.crm.model.dto.MerchantSituationDTO;
import org.junit.Test;

import javax.annotation.Resource;

public class MerchantSituationServiceTest extends BaseTest {

    @Resource
    private MerchantSituationService merchantSituationService;

    @Test
    public void testGet() {
        String s = "{\"status\":0,\"createLocation\":1,\"couponAmount\":1,\"threshold\":2,\"merchantId\":349837,\"monthLivingCoupon\":\"0\",\"situationRemake\":\"3\",\"adminId\":895,\"adminName\":\"杨路伟\"}";
        MerchantSituationDTO merchantSituationDTO = JSONObject.parseObject(s, MerchantSituationDTO.class);
        System.out.println(JSON.toJSONString(merchantSituationService.insertMerchantSituation(merchantSituationDTO)));
    }

}
