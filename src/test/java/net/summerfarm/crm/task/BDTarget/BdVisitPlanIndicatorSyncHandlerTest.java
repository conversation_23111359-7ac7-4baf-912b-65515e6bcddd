package net.summerfarm.crm.task.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.task.handler.bdTarget.BdVisitPlanIndicatorSyncHandler;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class BdVisitPlanIndicatorSyncHandlerTest {

    @Resource
    private BdVisitPlanIndicatorSyncHandler bdVisitPlanIndicatorSyncHandler;

    @Test
    public void test1() throws Exception {
        bdVisitPlanIndicatorSyncHandler.processResult(new XmJobInput());
    }

}
