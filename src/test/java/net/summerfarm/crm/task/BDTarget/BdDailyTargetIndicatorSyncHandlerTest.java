package net.summerfarm.crm.task.BDTarget;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.task.handler.bdTarget.BdDailyTargetIndicatorSyncHandler;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class BdDailyTargetIndicatorSyncHandlerTest {

    @Resource
    private BdDailyTargetIndicatorSyncHandler bdDailyTargetIndicatorSyncHandler;


    @Test
    public void test1() throws Exception {
        bdDailyTargetIndicatorSyncHandler.processResult(new XmJobInput());
    }
}
