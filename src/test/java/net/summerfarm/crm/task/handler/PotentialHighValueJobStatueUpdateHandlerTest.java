package net.summerfarm.crm.task.handler;

import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;

import javax.annotation.Resource;

public class PotentialHighValueJobStatueUpdateHandlerTest extends BaseTest {

    @Resource
    private PotentialHighValueJobStatueUpdateHandler potentialHighValueJobStatueUpdateHandler;

    @Test
    public void processResult() throws Exception {
        potentialHighValueJobStatueUpdateHandler.processResult(new XmJobInput());
    }

}