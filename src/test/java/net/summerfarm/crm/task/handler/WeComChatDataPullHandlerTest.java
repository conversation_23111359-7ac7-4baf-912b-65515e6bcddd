package net.summerfarm.crm.task.handler;

import net.summerfarm.crm.BaseTest;
import net.xianmu.oss.common.constants.OssConstants;
import net.xianmu.oss.common.util.OssGetUtil;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import net.xianmu.oss.helper.XianMuOssHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.Assert.*;

class WeComChatDataPullHandlerTest extends BaseTest {

    @Resource
    WeComChatDataPullHandler weComChatDataPullHandler;

    @Test
    void testExistFile() {
        String md5sum = "707ca900e668756e73970ed7a1d8a288";
        List<String> objectOssKeys = OssGetUtil.listObjectsByPrefix(md5sum, OSSExpiredLabelEnum.NO_EXPIRATION);
        assertFalse(CollectionUtils.isEmpty(objectOssKeys));
        String s = XianMuOssHelper.generateUrl(objectOssKeys.get(0));
        System.out.println(s);
    }

}