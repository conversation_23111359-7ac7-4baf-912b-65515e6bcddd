package net.summerfarm.crm.task.handler;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class WeComBdSyncHandlerTest extends BaseTest {

    @Resource
    WeComBdSyncHandler weComBdSyncHandler;


    @Test
    void processResult() throws Exception {
        XmJobInput context = new XmJobInput();
//        context.setInstanceParameters("{\"taskTime\":\"2023-01-01\", \"bdIds\": [1,2,3]}");
//        context.setInstanceParameters("{\"taskTime\":\"2023-01-01\"}");
        weComBdSyncHandler.processResult(context);
    }
}