package net.summerfarm.crm.task.handler;

import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;

import javax.annotation.Resource;

public class WeComResignedBdCustomerTransferHandlerTest extends BaseTest {

    @Resource
    private WeComResignedBdCustomerTransferHandler weComResignedBdCustomerTransferHandler;

    @Test
    public void processResult() throws Exception {
        XmJobInput context = new XmJobInput();
        context.setInstanceParameters("{\"userIds\":\"ShiZhengHao\"}");
        weComResignedBdCustomerTransferHandler.processResult(context);
    }
}