package net.summerfarm.crm.task.handler;

import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;

import javax.annotation.Resource;

public class FollowUpRecordImportHandlerTest extends BaseTest {

    @Resource
    private FollowUpRecordImportHandler followUpRecordImportHandler;

    @Test
    public void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("{\"bdIds\":[895, 6010857],\"dateTag\":\"20241106\"}");
        followUpRecordImportHandler.processResult(xmJobInput);
    }
}