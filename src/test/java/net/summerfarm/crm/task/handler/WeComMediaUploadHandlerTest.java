package net.summerfarm.crm.task.handler;

import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

class WeComMediaUploadHandlerTest  extends BaseTest {

    @Resource
    private WeComMediaUploadHandler handler;

    @Test
    void processResult() throws Exception {
        XmJobInput context = new XmJobInput();
        handler.processResult(context);
    }
}