package net.summerfarm.crm.task.handler;

import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class WeComAddContactWayHandlerTest extends BaseTest {

    @Resource
    private WeComAddContactWayHandler weComAddContactWayHandler;

    @Test
    void processResult() throws Exception {
        XmJobInput context = new XmJobInput();
        context.setInstanceParameters("{\n" +
                "  \"startTime\": \"2024-05-22\",\n" +
                "  \"endTime\": \"2024-05-24\",\n" +
                "  \"contactWayTypes\": [\n" +
                "    \"PERSONAL_CENTER\"\n" +
                "  ]\n" +
                "}");
        weComAddContactWayHandler.processResult(context);
    }

}