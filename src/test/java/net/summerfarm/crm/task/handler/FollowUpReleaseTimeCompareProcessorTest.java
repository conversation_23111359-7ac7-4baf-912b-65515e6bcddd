package net.summerfarm.crm.task.handler;

import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;

import javax.annotation.Resource;

public class FollowUpReleaseTimeCompareProcessorTest extends BaseTest {

    @Resource
    private FollowUpReleaseTimeCompareProcessor followUpReleaseTimeCompareProcessor;

    @Test
    public void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("{\"startTime\":\"2023-01-01 00:00:00\",\"delayReleaseRules\":[{\"ruleStartTime\":\"2025-07-01\",\"ruleEndTime\":\"2025-08-01\",\"delayReleaseTime\":\"2025-08-02\"},{\"labelIds\":[557,123],\"ruleStartTime\":\"2025-07-01\",\"ruleEndTime\":\"2025-09-01\",\"delayReleaseTime\":\"2025-09-02\"}],\"mIds\":[350906,303965]}");
        followUpReleaseTimeCompareProcessor.processResult(xmJobInput);
    }

}