package net.summerfarm.crm.task.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import net.summerfarm.crm.BaseTest;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PotentialHighValueJobCreationHandlerTest extends BaseTest {

    @Resource
    private PotentialHighValueJobCreationHandler potentialHighValueJobCreationHandler;

    @Test
    public void processResult() throws Exception {
        potentialHighValueJobCreationHandler.processResult(new XmJobInput());
    }

    @Test
    public void testDate() {
        DateTime date = DateUtil.date();
        assertEquals(6, date.monthBaseOne());
        assertEquals(5, DateUtil.offsetMonth(date, -1).monthBaseOne());
    }
}