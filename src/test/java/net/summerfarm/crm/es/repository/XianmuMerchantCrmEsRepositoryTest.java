package net.summerfarm.crm.es.repository;

import net.summerfarm.crm.BaseTest;
import net.summerfarm.crm.es.mapper.XianmuMerchantCrmMapper;
import net.summerfarm.crm.es.model.XianmuMerchantCrm;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertNotNull;

public class XianmuMerchantCrmEsRepositoryTest extends BaseTest {

    @Resource
    private XianmuMerchantCrmMapper xianmuMerchantCrmMapper;

    @Test
    public void selectByMIdIn() {
        List<Long> mIds = Arrays.asList(350883L,289570L,103095L,1480L,103096L);
        List<XianmuMerchantCrm> xianmuMerchantCrms = xianmuMerchantCrmMapper.selectByMIdIn(mIds);
        assertNotNull(xianmuMerchantCrms);
    }
}