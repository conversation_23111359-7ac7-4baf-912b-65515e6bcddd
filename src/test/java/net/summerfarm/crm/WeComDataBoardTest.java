package net.summerfarm.crm;

import com.alibaba.fastjson.JSONObject;
import net.summerfarm.crm.mq.domain.MqData;
import net.summerfarm.crm.mq.handler.WeComUserCreateHandle;
import net.summerfarm.crm.mq.listener.CrmListener;
import net.summerfarm.crm.service.BatchApplicationSamplesService;
import net.summerfarm.crm.service.WeComDataBoardService;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/2/27 11:37
 */
public class WeComDataBoardTest extends BaseTest{
    @Resource
    private WeComDataBoardService dataBoardService;
    @Resource
    private CrmListener listener;
    @Resource
    private BatchApplicationSamplesService batchApplicationSamplesService;

    @Test
    public void taskTest(){
        // 同步用户状态
//        dataBoardService.weComStateSync();

        // 同步客户沟通数据
//        dataBoardService.userBehaviorData();

        // 同步营销任务数据
        dataBoardService.groupMsgData();
    }

    @Test
    public void userCreateTest(){
        MqData mqData = new MqData();
        mqData.setType("WECOM_USER_CREATE");
        mqData.setBusiness("WECOM_USER_CREATE");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("adminId", 6010586);
        jsonObject.put("name", "叶峰");
        jsonObject.put("mobile", "***********");
        mqData.setData(jsonObject.toJSONString());
        listener.process(mqData);
    }

    @Test
    public void setBatchSamplesTest(){
        batchApplicationSamplesService.autoCreateSampleTask();
    }
}
