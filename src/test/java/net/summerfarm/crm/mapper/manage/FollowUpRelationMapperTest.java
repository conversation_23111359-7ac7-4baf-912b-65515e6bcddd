package net.summerfarm.crm.mapper.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import net.summerfarm.crm.BaseTest;
import net.summerfarm.crm.model.domain.FollowUpRelation;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class FollowUpRelationMapperTest extends BaseTest {

    @Resource
    private FollowUpRelationMapper followUpRelationMapper;

    @Test
    public void test() {
        LambdaQueryWrapper<FollowUpRelation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(FollowUpRelation::getAdminId, 895);
        List<FollowUpRelation> followUpRelations = followUpRelationMapper.selectList(lambdaQueryWrapper);
        assertNotNull(followUpRelations);
    }

    @Test
    public void testOld() {
        FollowUpRelation followUpRelation = followUpRelationMapper.selectLastFollowOne(895, 350883);
        assertNotNull(followUpRelation);
    }

    @Test
    public void testAA() {
        A a = new A(null);
        Integer x = false ? Integer.valueOf(1) : a.getB();
        assertEquals(null, x);
    }

    @Test
    public void testException() {
        A a = new A(null);
        Integer x = false ? 1 : a.getB();
    }

    @Data
    @AllArgsConstructor
    class A {
        private Integer b;
    }

}