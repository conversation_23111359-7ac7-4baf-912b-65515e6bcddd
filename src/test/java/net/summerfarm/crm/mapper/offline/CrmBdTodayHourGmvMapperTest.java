package net.summerfarm.crm.mapper.offline;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.crm.BaseTest;
import net.summerfarm.crm.model.domain.CrmBdTodayHourGmv;
import org.junit.Test;

import javax.annotation.Resource;

import static org.junit.Assert.assertNotNull;

public class CrmBdTodayHourGmvMapperTest extends BaseTest {

    @Resource
    private CrmBdTodayHourGmvMapper crmBdTodayHourGmvMapper;

    @Test
    public void testOld() {
        CrmBdTodayHourGmv crmBdTodayHourGmv = crmBdTodayHourGmvMapper.selectOneByBdIdAndDateTagAndIsSameCity(6012425L, 0, "是");
        assertNotNull(crmBdTodayHourGmv);
    }

    @Test
    public void testNew() {
        LambdaQueryWrapper<CrmBdTodayHourGmv> query = new LambdaQueryWrapper<>();
        query.eq(CrmBdTodayHourGmv::getBdId, 6012425);
        query.eq(CrmBdTodayHourGmv::getDateTag, 0);
        query.eq(CrmBdTodayHourGmv::getIsSameCity, "是");
        CrmBdTodayHourGmv crmBdTodayHourGmv = crmBdTodayHourGmvMapper.selectOne(query);
        assertNotNull(crmBdTodayHourGmv);
    }
}